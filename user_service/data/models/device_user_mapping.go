package models

import (
	pb "bitbucket.org/jogocoin/go_api/user_service/proto/user"
	"time"
)

type DeviceUserMapping struct {
	Id 						   int32     `gorm:"not null;type:int(11) unsigned auto_increment;PRIMARY_KEY"`
	DeviceId                   string     `gorm:"not null;type:varchar(255)"`
	UserId                     int32     `gorm:"bigint(8)"`
	AppType               		string    `json:"app_type,omitempty" gorm:"type:varchar(255)"`
	AppVersion            		string    `json:"app_version,omitempty" gorm:"type:varchar(255)"`
	CreatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP"`
}

func (u *DeviceUserMapping) Proto() *pb.DeviceUserMapping {
	return &pb.DeviceUserMapping{
		Id: 						u.Id,
		DeviceId:                  	u.DeviceId,
		UserId:                    	u.UserId,
		AppType:                  	u.AppType,
		AppVersion:                 u.AppVersion,
	}
}
