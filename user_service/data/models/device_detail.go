package models

import (
	pb "bitbucket.org/jogocoin/go_api/user_service/proto/user"
	"time"
)

type DeviceDetail struct {
	Id 						   int32     `gorm:"not null;type:int(11) unsigned auto_increment;PRIMARY_KEY"`
	DeviceId                   string     `gorm:"not null;type:varchar(255);unique"`
	UserId                     int32     `gorm:"bigint(8)"`
	AppsflyerId            	   string     `gorm:"not null;type:varchar(255)"`
	AppType               		string    `json:"app_type,omitempty" gorm:"type:varchar(255)"`
	AppVersion            		string    `json:"app_version,omitempty" gorm:"type:varchar(255)"`
	CreatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP"`
	UpdatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	
}

func (u *DeviceDetail) Proto() *pb.DeviceDetail {
	return &pb.DeviceDetail{
		Id: 						u.Id,
		DeviceId:                  u.<PERSON>ceId,
		UserId:                    u.UserId,
		AppsflyerId:            	u.AppsflyerId,
		AppType:                  	u.AppType,
		AppVersion:                 u.AppVersion,
	}
}
