package subscriber

import (
	"fmt"
	"github.com/Shopify/sarama"
)

type SubscriptionConfigKey string

type OffSet string

func (o OffSet) ToString() string {
	return fmt.Sprintf("%s", o)
}

func (s SubscriptionConfigKey) ToString() string {
	return fmt.Sprintf("%s", s)
}

const (
	Earliest = "earliest"
	Latest   = "latest"
)

const (
	KAFKA_VERSION_STAGING_KAFKA_EXPORTER = "2.2.0"
	KAFKA_VERSION_PUBSUB_KAFKA_EXPORTER  = "2.0.0"
	KAFKA_VERSION_OFFLINE_KAFKA          = "2.5.0"
)

const GROUP_ID SubscriptionConfigKey = "GROUP_ID"
const OFFSET SubscriptionConfigKey = "OFFSET"

type KafkaSubscription struct {
	groupId  string
	topic    string
	offset   string
	clientId string
}

func (k *KafkaSubscription) Offset() string {
	return k.offset
}

func (k *KafkaSubscription) Topic() string {
	return k.topic
}

func (k *KafkaSubscription) GroupId() string {
	return k.groupId
}

func NewKafkaSubscriptionWithOffSet(groupId string, topic string, offset string, clientId string) *KafkaSubscription {
	return &KafkaSubscription{groupId: groupId, topic: topic, offset: offset, clientId: clientId}
}

func NewKafkaSubscription(groupId string, topic string) *KafkaSubscription {
	return NewKafkaSubscriptionWithOffSet(groupId, topic, Latest, "default")
}

func (k *KafkaSubscription) ToConsumerConfig(version string) *sarama.Config {
	config := sarama.NewConfig()
	config.Version = sarama.MaxVersion
	if version == KAFKA_VERSION_STAGING_KAFKA_EXPORTER {
		config.Version = sarama.V2_2_0_0
	} else if version == KAFKA_VERSION_PUBSUB_KAFKA_EXPORTER {
		config.Version = sarama.V2_0_0_0
	} else if version == KAFKA_VERSION_OFFLINE_KAFKA {
		config.Version = sarama.V2_0_0_0
	}
	config.Consumer.Return.Errors = true
	config.ClientID = k.clientId
	if k.offset == Earliest {
		config.Consumer.Offsets.Initial = sarama.OffsetOldest
	}
	if k.offset == Latest {
		config.Consumer.Offsets.Initial = sarama.OffsetNewest
	}
	return config
}
