package config

import (
	"github.com/spf13/cast"
	"strings"
)

const TOPIC_KEY = "topic"
const PARTITION_KEY = "partitions"
const REPLICATION_KEY = "replication"
const KAFKA_CONFIG = "kafka"

type KafkaConfigRepository struct {
	config        *KafkaConfig
	offlineConfig *KafkaConfig
}

var instance *KafkaConfigRepository

func InitializeKafkaConfig(brokers string, offlineBrokers, version string, offlineVersion string) {
	kafkaConfig := ValidateAndExtractConfig(brokers, version)
	offlineKafkaConfig := ValidateAndExtractConfig(offlineBrokers, offlineVersion)
	instance = &KafkaConfigRepository{config: kafkaConfig, offlineConfig: offlineKafkaConfig}
}

func ValidateAndExtractConfig(brokers string, version string) *KafkaConfig {
	brokerSlice := strings.Split(cast.ToString(brokers), ",")
	return NewKafkaConfig(brokerSlice, version)
}

func GetKafkaRepositoryInstance() *KafkaConfigRepository {
	return instance
}

func (k *KafkaConfigRepository) GetConfig() *KafkaConfig {
	return k.config
}

func (k *KafkaConfigRepository) GetOfflineConfig() *KafkaConfig {
	return k.offlineConfig
}
