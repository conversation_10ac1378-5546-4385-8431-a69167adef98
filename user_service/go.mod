module bitbucket.org/jogocoin/go_api/user_service

go 1.13

replace github.com/micro/protoc-gen-micro v1.0.0 => /go/src/go_api/user_service/protoc-gen-micro@v0.8.0

replace bitbucket.org/jogocoin/go_api/pkg => /go/src/go_api/user_service/pkg

require (
	bitbucket.org/jogocoin/go_api/pkg v0.0.0-20220524135150-798cf1e2b24d
	firebase.google.com/go v3.13.0+incompatible
	github.com/aws/aws-sdk-go v1.23.0
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/gin-gonic/gin v1.6.3
	github.com/go-redis/redis v6.15.7+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/goamz/goamz v0.0.0-20180131231218-8b901b531db8
	github.com/golang/protobuf v1.4.3
	github.com/hashicorp/go-version v1.2.1
	github.com/jinzhu/gorm v1.9.16
	github.com/jinzhu/now v1.0.1
	github.com/micro/go-micro v1.18.0
	github.com/micro/go-plugins/broker/kafka v0.0.0-20200119172437-4fe21aa238fd
	github.com/newrelic/go-agent v3.10.0+incompatible
	github.com/satori/go.uuid v1.2.0
	github.com/vaughan0/go-ini v0.0.0-20130923145212-a98ad7ee00ec // indirect
	golang.org/x/crypto v0.0.0-20191205180655-e7c4368fe9dd
	golang.org/x/net v0.0.0-20210119194325-5f4716e94777
	golang.org/x/oauth2 v0.0.0-20190604053449-0f29369cfe45
	google.golang.org/api v0.8.0
	google.golang.org/grpc v1.26.0
	google.golang.org/protobuf v1.23.0
)
