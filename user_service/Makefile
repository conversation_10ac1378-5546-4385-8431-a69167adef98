setup:
	go mod init "bitbucket.org/jogocoin/go_api/user_service"

build:
	protoc -I. \
	  -I$(GOPATH)/src/github.com/grpc-ecosystem/grpc-gateway/third_party/googleapis \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/user/user.proto
	protoc --proto_path=$(GOPATH)/src/go_api/booking_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/booking/booking.proto
	protoc --proto_path=$(GOPATH)/src/go_api/notification_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/notification/notification.proto
	protoc --proto_path=$(GOPATH)/src/go_api/facility_sport_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/facility_sport/facility_sport.proto

	protoc --proto_path=$(GOPATH)/src/go_api/product_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/product/product.proto
	protoc --proto_path=$(GOPATH)/src/go_api/auth_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/auth/auth.proto
	protoc --proto_path=$(GOPATH)/src/go_api/purchase_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/user_service \
		proto/purchase/purchase.proto
	echo "machine bitbucket.org login $(BITBUCKET_USER_NAME) password $(BITBUCKET_USER_PASS)" > $(GOPATH)/src/go_api/user_service/.netrc

local:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50051 MICRO_REGISTRY=mdns GOPRIVATE="bitbucket.org" go run main.go

hot-reload:
	reflex -r '.*\.go' -R '^vendor/' -s make local

local2:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50052 MICRO_REGISTRY=mdns go run main.go

aws_dev:
	make build
	MICRO_MODE=qa MICRO_SERVER_ADDRESS=:50051 MICRO_REGISTRY=mdns go run main.go

aws_prod:
	make build
	MICRO_MODE=prod MICRO_SERVER_ADDRESS=:50051 MICRO_REGISTRY=mdns go run main.go
docker_build:
	make build
	docker build -t user_service .

rund:
	docker container stop user_service
	docker run --rm -it -d -p 50051:50051 -e MICRO_SERVER_ADDRESS=:50051 -e MICRO_REGISTRY=consul -e MICRO_MODE=prod user_service

publish:
	docker tag user_service:latest 051603364801.dkr.ecr.ap-south-1.amazonaws.com/user_service:latest
	docker push 051603364801.dkr.ecr.ap-south-1.amazonaws.com/user_service:latest


redis:
	docker run -it --network="host" --rm redis redis-cli -h auth-redis
