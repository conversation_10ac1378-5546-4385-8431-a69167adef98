package main

import (
	"fmt"
	"log"
	"time"

	"bitbucket.org/jogocoin/go_api/user_service/internal/s3"

	interceptor "bitbucket.org/jogocoin/go_api/pkg/interceptor"
	"bitbucket.org/jogocoin/go_api/pkg/newrelic"
	models "bitbucket.org/jogocoin/go_api/user_service/data/models"
	data "bitbucket.org/jogocoin/go_api/user_service/data"
	userHandler "bitbucket.org/jogocoin/go_api/user_service/handler"
	pb "bitbucket.org/jogocoin/go_api/user_service/proto/user"
	tasks "bitbucket.org/jogocoin/go_api/user_service/tasks"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"github.com/newrelic/go-agent/_integrations/nrmicro"

	//"github.com/micro/go-micro/broker"
	"bitbucket.org/jogocoin/go_api/user_service/setupFunc"
)

func main() {
	// initial setup
	setupFunc.SetUp()

	awsSession := setupFunc.AWSConnect()

	kBroker := setupFunc.KafkaConnect()

	db, err := data.MysqlConnect()
	defer db.Close()

	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	ROdb, ROerr := data.MysqlConnectRO()
	defer ROdb.Close()

	if ROerr != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	db.AutoMigrate(&models.User{})

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	userData := &data.UserData{Db: db, Client: redisClient, ROdb: ROdb}
	publishData := &tasks.PublishTask{Broker: kBroker, AWSSession: awsSession}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.user"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(
			userHandler.LogWrapper,
			userHandler.SetContextWrapper,
			nrmicro.HandlerWrapper(app),
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	imagesBucket := config.Get("s3", "imagesBucket").String("")
	imagesAwsS3 := s3.Initialize(imagesBucket)

	serviceHandler := &userHandler.Service{Data: userData, Pub: publishData, ImagesAwsS3: imagesAwsS3}
	pb.RegisterUserServiceHandler(newService.Server(), serviceHandler)

	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}
}
