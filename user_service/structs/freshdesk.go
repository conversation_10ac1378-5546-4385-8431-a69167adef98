package structs

type FreshdeskTicketPayload struct {
	UserId      int32  `json:"userId" form:"userId"`
	Subject     string `json:"subject" form:"subject"`
	Description string `json:"description" form:"description"`
	DeviceId    string `json:"deviceId" form:"deviceId"`
	Email       string `json:"email" form:"email"`
	Name        string `json:"name" form:"name"`
	Phone       string `json:"phone" form:"phone"`
}

type FreshDeskTicketResponse struct {
	TicketId        int32  `json:"ticketId" form:"ticketId"`
	IsTicketCreated bool   `json:"isTicketCreated" form:"isTicketCreated"`
	Message         string `json:"message" formm:"message"`
}
