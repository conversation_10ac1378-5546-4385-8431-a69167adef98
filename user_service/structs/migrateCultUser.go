package structs

type CultUserDataError struct {
	RowNumber     int
	ErrorMessages []string
	Row           string
}

type CreateCultMembershipResponse struct {
	Success bool `json:"success,omitempty"`
}

type CultAppUsageApiResponse struct {
	Message       string `json:"message"`
	BlockFitsoApp bool   `json:"blockFitsoApp"`
}

type CultAppBlacklistApiResponse struct {
	Message           string `json:"message"`
	IsUserBlacklisted bool   `json:"isUserBlacklisted"`
}
