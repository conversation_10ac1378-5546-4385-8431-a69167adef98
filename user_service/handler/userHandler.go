package userHandler

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"math/rand"
	"mime"
	"mime/multipart"
	"os"

	"bitbucket.org/jogocoin/go_api/user_service/internal/s3"
	"bitbucket.org/jogocoin/go_api/user_service/internal/util"

	"bitbucket.org/jogocoin/go_api/user_service/internal/featuresupport"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"

	"net/http"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"crypto/md5"
	"encoding/hex"

	data "bitbucket.org/jogocoin/go_api/user_service/data"
	models "bitbucket.org/jogocoin/go_api/user_service/data/models"
	sheets1 "bitbucket.org/jogocoin/go_api/user_service/internal/sheets"
	authPB "bitbucket.org/jogocoin/go_api/user_service/proto/auth"
	bookingPB "bitbucket.org/jogocoin/go_api/user_service/proto/booking"
	notificationPB "bitbucket.org/jogocoin/go_api/user_service/proto/notification"
	productPB "bitbucket.org/jogocoin/go_api/user_service/proto/product"

	pb "bitbucket.org/jogocoin/go_api/user_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/user_service/structs"
	tasks "bitbucket.org/jogocoin/go_api/user_service/tasks"
	"github.com/jinzhu/now"
	"github.com/micro/go-micro/config"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/net/context"

	// "encoding/json"
	"sync"

	firebase "firebase.google.com/go"
	"github.com/golang/protobuf/ptypes"
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	micro "github.com/micro/go-micro"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

const (
	topic                        = "user.created"
	formatYMDHIS                 = "2006-01-02 15:04:05" //Go only understands this date as ymdhis format.
	sampleDate                   = "1970-01-01"
	formatYMD                    = "2006-01-02"
	widthPercent                 = 0.8
	measureBannerWidth           = 342
	measureBannerHeight          = 153
	client_secret_path           = "./credentials/client_secret.json"
	DefaultLocation              = "Asia/Kolkata"
	loginOTPSMSTemplateID        = 333
	loginOTPSMSTemplateIDAndroid = 371
	verifyPhoneTemplateID        = 386
	AppHashAndroidTemplate       = "T3/GiWcmqGw"
	iosAlpha3India               = "IND"
	apiFitsoHost                 = "https://api.getfitso.com"
	AcademyTrialUsersGoogleSheet = "1vR-3Iq1Lo_Q1yvP7YTAfTxATuptGuwM33OHLnNnsVjU"
	AcademyTrialUsersGsRange     = "Sheet1!A1:I"
)

var tabIdToTabName = map[int32]string{
	1: "atCenter",
	2: "HomeGround",
	3: "liveSessions",
	4: "profile",
}

var (
	pcl                   productPB.ProductService
	bcl                   bookingPB.BookingService
	ncl                   notificationPB.NotificationService
	acl                   authPB.AuthService
	unblockedUsersListMap map[int32]bool
)

type Service struct {
	Data        data.DataInterface
	Pub         tasks.PublishTaskInterface
	ImagesAwsS3 *s3.S3
}

func init() {
	unblockedUsersListMap = make(map[int32]bool)
	for _, userId := range UNBLOCKED_FROM_FITSO_APP_USERS {
		unblockedUsersListMap[userId] = true
	}
	log.Println("unblockedUsersListMap: ", unblockedUsersListMap)
}

func (s *Service) Ping(ctx context.Context, req *pb.Empty, res *pb.Pong) error {
	res.Response = "Pong"
	return nil
}

func (s *Service) ClearCache(ctx context.Context, req *pb.CacheRequest, res *pb.ClearCacheResponse) error {

	if req.Key == "" {
		Status := &pb.Status{
			Status:  failure,
			Message: "Invalid paramaters, Key is required! ",
		}
		res.Status = Status
		return nil
	}

	clearCachePermitted, err := data.ViewCachePermitted(ctx, req.Key)
	if err != nil {
		log.Println("func:ClearCache: Error in checking clear redis permission for Key %v err: ", req.Key, err)
		return err
	}

	if clearCachePermitted == false {
		Status := &pb.Status{
			Status:  failure,
			Message: "You are not permitted to clear cache for this key: " + req.Key,
		}
		res.Status = Status
		return nil
	}

	status, err := s.Data.ClearCache(req.Key)
	if err != nil {
		log.Println("func:ClearCache: Error in Clear Redis Cache Handler for Key %v err: ", req.Key, err)
		Status := &pb.Status{
			Status:  failure,
			Message: "Error in clearing redis cache for Key: " + req.Key,
		}
		res.Status = Status
		return err
	}
	if status == false {
		Status := &pb.Status{
			Status:  failure,
			Message: "No cache found on key: " + req.Key,
		}
		res.Status = Status
		return nil
	}
	Status := &pb.Status{
		Status:  success,
		Message: "Successfully cleared redis cache for Key: " + req.Key,
	}
	res.Status = Status
	return nil
}

func (s *Service) ViewCache(ctx context.Context, req *pb.CacheRequest, res *pb.ViewCacheResponse) error {

	if req.Key == "" {
		Status := &pb.Status{
			Status:  failure,
			Message: "Invalid paramaters, Key is required! ",
		}
		res.Status = Status
		return nil
	}

	viewCachePermitted, err := data.ViewCachePermitted(ctx, req.Key)
	if err != nil {
		log.Println("func:ViewCache: Error in checking view redis permission for Key %v err: ", req.Key, err)
		return err
	}

	if viewCachePermitted == false {
		Status := &pb.Status{
			Status:  failure,
			Message: "You are not permitted to view cache for this key: " + req.Key,
		}
		res.Status = Status
		return nil
	}

	data, err := s.Data.ViewCache(req.Key)
	if err != nil {
		log.Println("func:ViewCache: Error in View Redis Cache Handler for Key ? err: ", req.Key, err)
		Status := &pb.Status{
			Status:  failure,
			Message: "Error in viewing redis cache for key: " + req.Key,
		}
		res.Status = Status
		return err
	}
	if data == "" {
		Status := &pb.Status{
			Status:  failure,
			Message: "No cache found for key: " + req.Key,
		}
		res.Status = Status
		res.Data = data
		return nil
	}

	Status := &pb.Status{
		Status:  success,
		Message: "Successfully retrieved cache for key: " + req.Key,
	}
	res.Status = Status
	res.Data = data
	return nil
}

func (s *Service) HarvestData(ctx context.Context, req *pb.HarvestRequest, res *pb.HarvestResponse) error {
	if req.UserLatitude == 0 || req.UserLongitude == 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid paramaters, Latitude and Longitude are required! ",
		}
		res.Status = status
		return nil
	}

	if req.UserId > 0 {
		//update latest location in users table
		var updateSuccessful bool
		var message string
		if err := s.Data.SetLocationParameter(req, &updateSuccessful, &message); err != nil {
			log.Printf("Error in updating user location for user id: %d, err: ", req.UserId, err)

			status := &pb.Status{
				Status:  "failure",
				Message: message,
			}
			res.Status = status
			return err

		}

		//maintain user launch log
		appLaunchLogModel := &models.UserAppLaunchLog{
			UserId:     req.UserId,
			Latitude:   req.UserLatitude,
			Longitude:  req.UserLongitude,
			AppType:    req.AppType,
			AppVersion: req.AppVersion,
		}
		var createSuccessful bool
		if err := s.Data.CreateUserAppLaunchLog(appLaunchLogModel, &createSuccessful); err != nil {
			log.Printf("Could not create app launch log for user id: %d, err: %v", req.UserId, err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not create app launch log record!",
			}
			res.Status = status
			return err
		}
		if createSuccessful {
			fmt.Printf("App launch log created successfully for user id: %d!", req.UserId)
			// status := &pb.Status{
			// 	Status:  "success",
			// 	Message: "App launch log created successfully!",
			// }
			// res.Status = status
			res.CreatedRecord = appLaunchLogModel.Proto()
		}

		status := &pb.Status{
			Status:  "success",
			Message: message,
		}
		res.Status = status
	}

	return nil
}

func (s *Service) UserGet(ctx context.Context, req *pb.UserRequest, res *pb.UserResponse) error {

	if len(req.UserIdArray) > 0 {

		var userArr []models.User

		if err := s.Data.GetWithIds(req.UserIdArray, &userArr); err != nil {
			return errors.New(fmt.Sprintf("error getting users for user ids:%v, err: %v", req.UserIdArray, err))
		}

		userIdList := make([]int32, 0)
		for _, user := range userArr {
			userIdList = append(userIdList, user.UserID)
		}

		userAgeMap, userAgeDataError := s.Data.BatchGetUserAgeRecord(ctx, userIdList)
		if userAgeDataError != nil {
			log.Printf("UserGet : error in getting user age for user_ids: %d, error: %v", userIdList, userAgeDataError)
			return fmt.Errorf("UserGet : error in getting user age for user_ids: %d, error: %v", userIdList, userAgeDataError)
		}

		for _, userDB := range userArr {
			userDB.Age = userAgeMap[userDB.UserID]
			if userDB.Age <= 0 && !userDB.Birthday.IsZero() {
				userDB.Age = int32(math.Floor(time.Now().Sub(userDB.Birthday).Hours() / 24 / 365))
			}
			res.Users = append(res.Users, userDB.Proto())
		}
	} else {

		var userDB models.User

		userData := &models.User{
			UserID: req.UserId,
			Phone:  req.Phone,
			Email:  req.Email,
			// Token:  req.Token,
		}

		if err := s.Data.Get(userData, &userDB); err != nil {
			return errors.New(fmt.Sprintf("error getting user for user id: %d, err: %v", req.UserId, err))
		}

		if userDB.ProfilePictureHash != "" {
			userDB.DoesProfilePicExist = true
		} else {
			userDB.DoesProfilePicExist = false
		}

		var userAge models.UserAgeRecord
		err := s.Data.GetUserAgeRecord(userDB.UserID, &userAge)
		if err != nil {
			return fmt.Errorf("Unable to get user age for userId: %d, err: %v", req.UserId, err)
		}
		userDB.Age = userAge.Age
		if userDB.Age <= 0 && !userDB.Birthday.IsZero() {
			userDB.Age = int32(math.Floor(time.Now().Sub(userDB.Birthday).Hours() / 24 / 365))
		}
		res.Users = append(res.Users, userDB.Proto())
	}

	if len(res.Users) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "User(s) found successfully",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "No user found with given data",
		}
		res.Status = status
	}

	return nil
}

func GetMD5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func (s *Service) UserCreate(ctx context.Context, req *pb.User, res *pb.UserResponse) error {
	log.Println("Creating user: ", req)

	// Generates a hashed version of our password
	hashedPass, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return errors.New(fmt.Sprintf("error hashing password: %v", err))
	}
	req.Password = string(hashedPass)
	req.ExpertPassword = GetMD5Hash(req.ExpertPassword)

	userDB := models.User{
		Email:                       req.Email,
		Phone:                       req.Phone,
		Name:                        req.Name,
		FirstName:                   req.FirstName,
		Password:                    req.Password,
		ExpertPassword:              req.ExpertPassword,
		Gender:                      req.Gender,
		AppType:                     req.AppType,
		AppVersion:                  req.AppVersion,
		MaxSportsActiveBookingLimit: 3,
		PhoneIsdCode:                "+91",
		DateCreated:                 time.Now(),
		ContactEmail:                req.ContactEmail,
		PhoneVerifiedFlag:           &req.PhoneVerifiedFlag,
	}
	if err := s.Data.Create(&userDB); err != nil {
		return errors.New(fmt.Sprintf("error creating user: %v", err))
	}

	res.Users = append(res.Users, userDB.Proto())

	return nil
}

// UserChildMappingGetOrCreate handler created user child mapping if not already exists
func (s *Service) UserChildMappingGetOrCreate(ctx context.Context, req *pb.UserChildMappingRequest, res *pb.UserChildMappingResponse) error {
	userChildMapData := &models.UserChildAccountMapping{
		UserId:      req.UserId,
		ChildUserId: req.ChildUserId,
	}

	getOrCreateData, err := s.Data.CreateUserChildMapping(ctx, userChildMapData)

	if err != nil {
		log.Printf("Error in creating user child mapping for user id: %d, child user id: %d, err: %v", req.UserId, req.ChildUserId, err)
		return err
	}

	res.UserId = getOrCreateData.UserId
	res.ChildUserId = getOrCreateData.ChildUserId
	res.Status = &pb.Status{
		Status: success,
	}

	return nil
}

func (s *Service) UserUpdate(ctx context.Context, req *pb.User, res *pb.UserResponse) error {
	log.Printf("Updating user for user id: %d, req: %v", req.UserId, req)

	// Generates a hashed version of our password

	// to check to update password only when it is new
	hashedPass, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return errors.New(fmt.Sprintf("error hashing password: %v", err))
	}

	req.Password = string(hashedPass)
	// if err := s.Data.Create(req); err != nil {
	// 	return errors.New(fmt.Sprintf("error creating user: %v", err))
	// }

	res.Users = append(res.Users, req)

	return nil
}

func (s *Service) Authenticate(ctx context.Context, req *pb.LoginRequest, res *pb.TokenResponse) error {
	log.Printf("authenticating user for user id: %d, req: %v", req.UserId, req)

	var userDB models.User

	if err := s.Data.Authenticate(req, &userDB); err != nil {
		return errors.New(fmt.Sprintf("error fetching data about User for user id: %d, err: %v", req.UserId, err))
	}

	if userDB.UserID == req.UserId {
		token := CreateToken()

		if err := s.Data.Tokenize(token, &userDB); err != nil {
			return errors.New(fmt.Sprintf("error creating token and storing it into redis for user id: %d, err: %v", req.UserId, err))
		}

		res.Token = token
	}

	res.UserId = req.UserId
	res.Users = append(res.Users, userDB.Proto())

	fmt.Println(res)
	return nil
}

func (s *Service) ValidateToken(ctx context.Context, req *pb.UserRequest, res *pb.Token) error {
	log.Println("ValidatingToken:", req)

	reqToken := config.Get("codeIndependent", "redisAPrepend").String("") + "_token_" + string(req.Token)

	var tokenData string
	if err := s.Data.Validate(reqToken, &tokenData); err != nil {

		error := &pb.Error{
			Code:        101,
			Description: "error in fetching token details from redis, no data found",
		}

		res.Errors = append(res.Errors, error)

		status := &pb.Status{
			Status:  "failure",
			Message: "No user exists with this access token",
		}

		res.Status = status
		return nil
	}

	fmt.Println("Token Data ---------------- ", tokenData)
	//fmt.Println("Token expiry updation error -- ", errExpiry)
	var user models.User

	if err := user.FromString(tokenData); err != nil {

		error := &pb.Error{
			Code:        102,
			Description: "error in reading token details from redis",
		}

		res.Errors = append(res.Errors, error)

		status := &pb.Status{
			Status:  "failure",
			Message: "No user exists with this access token",
		}

		res.Status = status
		return nil
	}
	go s.UpdateToken(req.Token)
	//removing comment to cater child user validation
	// if req.UserId == user.UserID {
	res.Valid = true
	res.Token = req.Token
	res.TokenData = &pb.TokenData{User: user.Proto()}
	// }

	if &req.UserId == nil || req.UserId == 0 {
		res.Valid = true
		res.Token = req.Token
		res.TokenData = &pb.TokenData{User: user.Proto()}
	}

	//initialize firebase app and generate custom firebase token
	var firebaseIdToken string
	if errTok := s.GenerateAndGetFirebaseIdToken(strconv.Itoa(int(user.UserID)), &firebaseIdToken); errTok != nil {
		log.Println("Error in generating firebase Id token for -->"+strconv.Itoa(int(user.UserID))+" err: ", errTok)
		log.Println("Going ahead without throwing error!")
	}
	fmt.Println("Generated firebase id token -->", firebaseIdToken)

	res.FirebaseIdToken = firebaseIdToken //firebase_id_token

	status := &pb.Status{
		Status:  "success",
		Message: "Access Token Verification Successful!",
	}

	res.Status = status

	return nil
}

func (s *Service) UpdateToken(token string) error {
	reqToken := config.Get("codeIndependent", "redisAPrepend").String("") + "_token_" + string(token)

	var tokenDB models.UserAccessTokens
	if err := s.Data.GetTokenCreationTime(&tokenDB, token); err != nil {
		return errors.New(fmt.Sprintf("error getting token details for token '%s': %v", reqToken, err))
	}

	var sessionTime time.Duration
	if tokenDB.CreatedAt.Sub(time.Now().AddDate(0, 0, -150)).Seconds() > 0 { //150days
		if (tokenDB.CreatedAt.AddDate(0, 0, 136)).Sub(time.Now()).Seconds() < 0 { //less than
			sessionTime = time.Duration((tokenDB.CreatedAt.AddDate(0, 0, 150)).Sub(time.Now()).Nanoseconds())
		} else {
			sessionTime = time.Duration(1209600 * 1000 * 1000 * 1000) //14days
		}
		if err := s.Data.SetExpiryOfValidationToken(reqToken, sessionTime); err != nil {
			log.Printf("Error in setting expiry of validation token '%s': %v", reqToken, err)
		} //Last_Update time
		if err := s.Data.TokenUpdateTime(&tokenDB); err != nil {
			return errors.New(fmt.Sprintf("error in updating token details for token: %s, err: %v", reqToken, err))
		}
	} else {
		sessionTime = time.Duration(-1)
		if err := s.Data.SetExpiryOfValidationToken(reqToken, sessionTime); err != nil {
			fmt.Println(err)
		}
		if err := s.Data.TokenUpdateIsExpired(&tokenDB); err != nil {
			return errors.New(fmt.Sprintf("error in updating is_expired for token: %s, err: %v", reqToken, err))
		}
	}
	return nil
}

func (s *Service) PhoneLogin(ctx context.Context, req *pb.PhoneLoginRequest, res *pb.PhoneLoginResponse) error {
	fmt.Println("Phone Login:", req)

	if len(req.Phone) != 10 {

		error := &pb.Error{
			Code:        103,
			Description: "Incorrect Phone number",
		}

		res.Errors = append(res.Errors, error)
		return nil
	}

	if len(req.Otp) <= 0 {

		error := &pb.Error{
			Code:        104,
			Description: "Invalid Otp",
		}

		res.Errors = append(res.Errors, error)
		return nil
	}

	userData := &models.User{
		Phone: req.Phone,
	}

	var userDB models.User
	goAhead := 0 //this is needed as gorm return fatal error in case of empty record! when find in not a slice
	if err := s.Data.Get(userData, &userDB); err != nil {
		log.Printf("Error in fetching user data for user with phone: %s, err: %v", req.Phone, err)
		userQueryData := userDB.Proto()
		if userQueryData.UserId == 0 { //empty record
			goAhead = 1
		} else {
			return errors.New(fmt.Sprintf("error getting user by phone %s: %v", req.Phone, err))
		}
	} else {
		goAhead = 1
	}

	if goAhead == 1 {

		if userDB.UserID > 0 { //user exists for phone

			//get user sport skill levels here
			skillReq := &pb.UserSportSkillRequest{
				UserId: userDB.UserID,
			}
			var skillRes pb.SkillResponse //response stored here
			if err := s.GetUserSkillLevel(context.TODO(), skillReq, &skillRes); err != nil {
				log.Println("Could not fetch user skill levels! Unknown error!")
				log.Println("Proceeding without it...")
			}
			//add sports skills in user obj here
			if skillRes.Status.Status == "success" {
				userDB.OverallSkillLevel = skillRes.OverallSkillLevel
			}

			//get all connected users here
			uInfo := &pb.UserInfo{
				UserId: userDB.UserID,
			}
			var uResponse pb.ChildUserResponse //response stored here
			if err := s.GetChildUsers(context.TODO(), uInfo, &uResponse); err != nil {
				log.Printf("Could not fetch all connected users for user id: %d! Unknown error: %v", userDB.UserID, err)
				return err
			}
			//add all connected users in user object here
			if uResponse.Status.Status == "success" {
				userDB.AllConnectedUsers = uResponse.AllConnectedUsers
			}

			userQueryData := userDB.Proto()

			var generatedOtp string
			userOtpToken := data.GetRedisKeyForOTP(ctx, userQueryData.Phone)

			if err := s.Data.Validate(userOtpToken, &generatedOtp); err != nil {
				if len(generatedOtp) > 0 {

					error := &pb.Error{
						Code:        101,
						Description: "error in fetching token details from redis, no data found",
					}

					res.Errors = append(res.Errors, error)
					status := &pb.Status{
						Status:  "failure",
						Message: "Unknown error! Please try again later!",
					}
					res.Status = status
					return nil
				}
			}
			UIDStr := strconv.Itoa(int(userQueryData.UserId))
			if len(UIDStr) < 4 {
				UIDStr = PrependZeros(4-len(UIDStr), UIDStr)
			}
			UIDArr := []rune(UIDStr)
			bypassOTP := string(UIDArr[len(UIDArr)-4:])
			if req.Otp == bypassOTP {
				fmt.Println("here")
			} else {
				fmt.Println("-", req.Otp, "there", bypassOTP)
			}

			if req.Otp == generatedOtp {

				status := &pb.Status{
					Status:  "success",
					Message: "OTP Verification Successful!",
				}
				res.Status = status
				res.User = append(res.User, userQueryData)

				//save user data in redis - generate token
				token := CreateToken()
				if err := s.Data.Tokenize(token, &userDB); err != nil {
					return errors.New(fmt.Sprintf("error creating token and storing it into redis for user with phone: %s, err: %v", req.Phone, err))
				}
				var tokenData pb.Token
				tokenData.Token = token //access_token

				//initialize firebase app and generate custom firebase token
				var firebaseIdToken string
				if errTok := s.GenerateAndGetFirebaseIdToken(UIDStr, &firebaseIdToken); errTok != nil {
					log.Println("Error in generating firebase Id token for -->"+UIDStr+" err: ", errTok)
					log.Println("Going ahead without throwing error!")
				}
				fmt.Println("Generated firebase id token -->", firebaseIdToken)

				tokenData.FirebaseIdToken = firebaseIdToken //firebase_id_token

				res.Token = &tokenData

			} else {
				log.Printf("Incorrect OTP Entered for phone: %s!", req.Phone)
				error := &pb.Error{
					Code:        105,
					Description: "Incorrect OTP",
				}
				res.Errors = append(res.Errors, error)

				status := &pb.Status{
					Status:  "failure",
					Message: "Incorrect OTP Entered",
				}
				res.Status = status
			}
		} else {
			log.Printf("User does not exits with phone number: %s", req.Phone)
			error := &pb.Error{
				Code:        105,
				Description: "No user exists with this Phone Number!",
			}
			res.Errors = append(res.Errors, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "No user exists with this phone number!",
			}
			res.Status = status
			return nil
		}
	}

	return nil
}

func (s *Service) GenerateAndGetFirebaseIdToken(userId string, firebaseIdToken *string) error {
	fmt.Println("req -- ", userId)
	credFilePath := "firebase_config/fitso-sports-firebase-adminsdk-o6j3e-a9335a3a1d.json"
	sa := option.WithCredentialsFile(credFilePath)
	app, err := firebase.NewApp(context.Background(), nil, sa)
	if err != nil {
		//log.Fatalf("error initializing Firebase app: %v\n", err)
		fmt.Println("Error in initializing Firebase app --> ", err)
	}

	client, err := app.Auth(context.Background())
	if err != nil {
		//log.Fatalf("error getting Auth client -- Firebase: %v\n", err)
		fmt.Println("Error in getting Auth client --> ", err)
	}

	//--> test claim
	claims := map[string]interface{}{
		"premiumAccount": true,
	}
	token, errGen := client.CustomTokenWithClaims(context.TODO(), userId, claims)
	if errGen != nil {
		fmt.Println("Error in getting custom token --> ", errGen)
	}
	//fmt.Println("Generated Firebase id Token --->", token)

	*firebaseIdToken = token

	return nil
}

func (s *Service) PhoneLoginV2(ctx context.Context, req *pb.PhoneLoginRequest, res *pb.PhoneLoginResponse) error {
	fmt.Println("Phone Login:", req)

	if len(req.Phone) != 10 {

		error := &pb.Error{
			Code:        103,
			Description: "Incorrect Phone number",
		}

		res.Errors = append(res.Errors, error)
		return nil
	}

	if len(req.Otp) <= 0 {

		error := &pb.Error{
			Code:        104,
			Description: "Invalid Otp",
		}

		res.Errors = append(res.Errors, error)
		return nil
	}

	userData := &models.User{
		Phone: req.Phone,
	}

	var userDB models.User
	goAhead := 0 //this is needed as gorm return fatal error in case of empty record! when find in not a slice
	if err := s.Data.Get(userData, &userDB); err != nil {
		fmt.Println(userDB)
		userQueryData := userDB.Proto()
		if userQueryData.UserId == 0 { //empty record
			goAhead = 1
		} else {
			return errors.New(fmt.Sprintf("error getting user by phone: %v", err))
		}
	} else {
		goAhead = 1
	}

	if goAhead == 1 {

		if userDB.UserID > 0 {

			//get user sport skill levels here
			skillReq := &pb.UserSportSkillRequest{
				UserId: userDB.UserID,
			}
			var skillRes pb.SkillResponse //response stored here
			if err := s.GetUserSkillLevel(context.TODO(), skillReq, &skillRes); err != nil {
				fmt.Println("Could not fetch user skill levels! Unknown error!")
				fmt.Println("Proceeding without it...")
			}
			//add sports skills in user obj here
			if skillRes.Status.Status == "success" {
				userDB.OverallSkillLevel = skillRes.OverallSkillLevel
			}

			//get all connected users here
			uInfo := &pb.UserInfo{
				UserId: userDB.UserID,
			}
			var uResponse pb.ChildUserResponse //response stored here
			if err := s.GetChildUsers(context.TODO(), uInfo, &uResponse); err != nil {
				fmt.Println("Could not fetch all connected users! Unknown error!")
				return err
			}
			if uResponse.Status.Status == "success" {
				userDB.AllConnectedUsers = uResponse.AllConnectedUsers
			}

			//get initial city for user based on plan
			var initialCityId int32
			if err := s.GetInitialCityForUserBasedOnLatestPlan(userDB.UserID, &initialCityId); err != nil {
				fmt.Println("Could not determine initial city id for user: ", err)
				fmt.Println("Proceeding without throwing error!")
			}
			userDB.PlanCityId = initialCityId
		}
		userQueryData := userDB.Proto()

		userOtpToken := data.GetRedisKeyForOTP(ctx, req.Phone)
		var generatedOtp string
		if err := s.Data.Validate(userOtpToken, &generatedOtp); err != nil {
			if len(generatedOtp) > 0 {

				status := &pb.Status{
					Status:  "failure",
					Message: "Unknown error! Please try again later!",
				}
				res.Status = status
				return nil
			}
		}
		var otpDetails structs.UserOtpCount
		if len(generatedOtp) > 0 {

			if err := json.Unmarshal([]byte(generatedOtp), &otpDetails); err != nil {
				return err
			}

			otpDetails.OtpValidateCount = otpDetails.OtpValidateCount - 1
			if err := s.Data.OtpVerificationCount(userOtpToken, otpDetails); err != nil {

				status := &pb.Status{
					Status:  "failure",
					Message: "Unknown error! Please try again later!",
				}
				res.Status = status
				return nil
			}
		}
		UIDStr := strconv.Itoa(int(userQueryData.UserId))
		if len(UIDStr) < 4 {
			UIDStr = PrependZeros(4-len(UIDStr), UIDStr)
		}
		UIDArr := []rune(UIDStr)
		bypassOTP := string(UIDArr[len(UIDArr)-4:])
		if req.Otp == bypassOTP {
			fmt.Println("here")
		} else {
			fmt.Println("-", req.Otp, "there", bypassOTP)
		}
		log.Println(otpDetails.OTP)
		if !(otpDetails.OtpValidateCount > 0) && otpDetails.OTP != "" {
			status := &pb.Status{
				Status:  "failure",
				Message: "Try logging in after 10 minutes",
			}
			res.Status = status
		} else if req.Otp == otpDetails.OTP {

			if userDB.UserID > 0 && (userDB.PhoneVerifiedFlag == nil || *userDB.PhoneVerifiedFlag == 0) {
				var phoneVerifiedFlag = int32(1)
				upData := &models.User{
					UserID:            userDB.UserID,
					PhoneVerifiedFlag: &phoneVerifiedFlag,
				}
				var updateSuccessful bool
				if err := s.Data.UpdateUserDetails(upData, &updateSuccessful); err != nil {
					fmt.Println("Error in updating user details.. PhoneVerifiedFlag")
					return err
				}
			}

			status := &pb.Status{
				Status:  "success",
				Message: "OTP Verification Successful!",
			}
			res.Status = status
			if userQueryData.UserId > 0 {
				res.User = append(res.User, userQueryData)
				res.UserExists = true
				//save user data in redis - generate token
				token := CreateToken()
				if err := s.Data.Tokenize(token, &userDB); err != nil {
					return errors.New(fmt.Sprintf("error creating token and storing it into redis: %v", err))
				}
				appVersion, _ := util.GetAppVersionFromContext(ctx)
				appType, _ := util.GetClientIDFromContext(ctx)
				tokenDB := &models.UserAccessTokens{
					Token:       token,
					UserId:      userDB.UserID,
					LastUpdated: time.Now(),
					IsExpired:   false,
					AppVersion:  appVersion,
					AppType:     appType,
				}
				go s.Data.StoreToken(tokenDB)

				var tokenData pb.Token
				tokenData.Token = token
				//initialize firebase app and generate custom firebase token
				var firebaseIdToken string
				if errTok := s.GenerateAndGetFirebaseIdToken(UIDStr, &firebaseIdToken); errTok != nil {
					fmt.Println("Error in generating firebase Id token for -->"+UIDStr+" err: ", errTok)
					fmt.Println("Going ahead without throwing error!")
				}
				fmt.Println("Generated firebase id token -->", firebaseIdToken)
				tokenData.FirebaseIdToken = firebaseIdToken //firebase_id_token

				res.Token = &tokenData //access token
			} else { //new user
				fmt.Println("New User --- ")
				res.UserExists = false
			}
			go s.StoreOtpCount("used")
			go s.publishOTPVerificationToLeadSquaredTopic(ctx, req.Phone)
		} else {
			fmt.Println("Incorrect OTP Entered!")
			error := &pb.Error{
				Code:        105,
				Description: "Incorrect OTP",
			}
			res.Errors = append(res.Errors, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Incorrect OTP Entered",
			}
			res.Status = status
		}
	}

	return nil
}

func (s *Service) PhoneLoginV2New(ctx context.Context, req *pb.PhoneLoginRequest, res *pb.PhoneLoginResponseV2) error {

	if len(req.Phone) != 10 {
		status := &pb.Status{
			Status:  badRequest,
			Message: "Incorrect Phone Number",
		}
		res.Status = status
		return nil
	}

	if len(req.Otp) <= 4 {
		status := &pb.Status{
			Status:  badRequest,
			Message: "Invalid OTP",
		}
		res.Status = status
		return nil
	}
	var userOtpToken string
	if req.ValidateFor == pb.GenerateOTP_VERIFY_PHONE {
		userOtpToken = data.GetRedisKeyVerifyPhone(ctx, req.Phone)
	} else {
		userOtpToken = data.GetRedisKeyForOTP(ctx, req.Phone)
	}
	var generatedOtp string
	if err := s.Data.Validate(userOtpToken, &generatedOtp); err != nil {
		if len(generatedOtp) > 0 {
			log.Println("Error in getting otp from redis key")
			return err
		}
	}
	var otpDetails structs.UserOtpCount
	if len(generatedOtp) > 0 {

		if err := json.Unmarshal([]byte(generatedOtp), &otpDetails); err != nil {
			log.Println("error Unmarshalling data: %v", err)
			return err
		}

		otpDetails.OtpValidateCount = otpDetails.OtpValidateCount - 1
		if err := s.Data.OtpVerificationCount(userOtpToken, otpDetails); err != nil {
			log.Println("Error in setting otp Verification count (rate limiting)")
			return err
		}
	}

	log.Println(otpDetails.OTP)
	if !(otpDetails.OtpValidateCount > 0) && otpDetails.OTP != "" {
		status := &pb.Status{
			Status:  badRequest,
			Message: "Try logging in after 10 minutes",
		}
		res.Status = status
	} else if req.Otp == otpDetails.OTP && req.ValidateFor == pb.GenerateOTP_VERIFY_PHONE {
		status := &pb.Status{
			Status:  success,
			Message: "OTP Verification Successful!",
		}
		res.Status = status
		go s.publishOTPVerificationToLeadSquaredTopic(ctx, req.Phone)
	} else if req.Otp == otpDetails.OTP {

		status := &pb.Status{
			Status:  success,
			Message: "OTP Verification Successful!",
		}
		res.Status = status

		userData := &models.User{
			Phone: req.Phone,
		}

		var userDB models.User
		if err := s.Data.Get(userData, &userDB); err != nil && gorm.IsRecordNotFoundError(err) == false {
			log.Println("error getting user by phone: %v", err)
			return err
		}
		if userDB.UserID > 0 && (userDB.PhoneVerifiedFlag == nil || *userDB.PhoneVerifiedFlag == 0) {
			var phoneVerifiedFlag = int32(1)
			upData := &models.User{
				UserID:            userDB.UserID,
				PhoneVerifiedFlag: &phoneVerifiedFlag,
			}
			var updateSuccessful bool
			if err := s.Data.UpdateUserDetails(upData, &updateSuccessful); err != nil {
				fmt.Println("Error in updating user details.. PhoneVerifiedFlag")
				return err
			}

		}

		userQueryData := userDB.Proto()
		userDetails := &pb.User{
			UserId:             userQueryData.UserId,
			Name:               userQueryData.Name,
			Email:              userQueryData.Email,
			Phone:              userQueryData.Phone,
			ProfilePictureHash: userQueryData.ProfilePictureHash,
			Gender:             userQueryData.Gender,
		}

		if userQueryData.Birthday != nil && userQueryData.Birthday.Seconds > 0 {
			userDetails.Birthday = userQueryData.Birthday
		}

		if userDB.ProfilePictureHash != "" {
			userDetails.DoesProfilePicExist = true
		} else {
			userDetails.DoesProfilePicExist = false
		}

		if userQueryData.UserId > 0 {
			res.User = userDetails
			res.UserExists = true
			//save user data in redis - generate token
			appVersion, _ := util.GetAppVersionFromContext(ctx)
			appType, _ := util.GetClientIDFromContext(ctx)
			token := CreateToken()
			if err := s.Data.Tokenize(token, &userDB); err != nil {
				log.Println("error creating token and storing it into redis: %v", err)
				return err
			}
			tokenDB := &models.UserAccessTokens{
				Token:       token,
				UserId:      userDB.UserID,
				LastUpdated: time.Now(),
				IsExpired:   false,
				AppVersion:  appVersion,
				AppType:     appType,
			}
			go s.Data.StoreToken(tokenDB)

			UIDStr := strconv.Itoa(int(userQueryData.UserId))
			if len(UIDStr) < 4 {
				UIDStr = PrependZeros(4-len(UIDStr), UIDStr)
			}
			var tokenData pb.Token
			tokenData.Token = token
			//initialize firebase app and generate custom firebase token
			var firebaseIdToken string
			if errTok := s.GenerateAndGetFirebaseIdToken(UIDStr, &firebaseIdToken); errTok != nil {
				log.Println("Error in generating firebase Id token for -->"+UIDStr+" err: ", errTok)
			}
			log.Println("Generated firebase id token -->", firebaseIdToken)
			tokenData.FirebaseIdToken = firebaseIdToken //firebase_id_token
			if req.ValidateFor == pb.GenerateOTP_LOGIN_OTP && len(req.AppsflyerId) > 0 && len(req.DeviceId) > 0 {
				userDeviceData := &pb.DeviceDetail {
					UserId:			userQueryData.UserId,
					DeviceId:		req.DeviceId,
					AppsflyerId: 	req.AppsflyerId,
					AppType:        appType,
					AppVersion:     appVersion,
				}
				go s.CreateUserDeviceDetailsAndMapping(ctx, userDeviceData)
			}
			res.Token = &tokenData
		} else { //new user
			log.Println("New User --- ")
			res.UserExists = false

			token := CreateToken()
			if err := s.Data.SaveNewUserAuthToken(token, req); err != nil {
				log.Println("error storing token in redis %v", err)
				return err
			}
			var tokenData pb.Token
			tokenData.LoginHash = token
			res.Token = &tokenData
		}
		go s.StoreOtpCount("used")
		go s.publishOTPVerificationToLeadSquaredTopic(ctx, req.Phone)

	} else {
		status := &pb.Status{
			Status:  failed,
			Message: "Incorrect OTP Entered",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) publishOTPVerificationToLeadSquaredTopic(ctx context.Context, mobile string) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from publishOTPVerificationToLeadSquaredTopic panic- ", r)
		}
	}()

	userData := &models.User{
		Phone: mobile,
	}

	var userDB models.User
	if err := s.Data.Get(userData, &userDB); err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("publishOTPVerificationToLeadSquaredTopic: error getting user for phone:%s, err: %v", mobile, err)
		return
	}

	if userDB.UserID == 0 {
		productClient := util.GetProductClient()
		leadsData := &productPB.LeadSquaredLead{
			Mobile:     mobile,
			LeadSource: OTP_VERIFICATION_SUCCESS,
		}
		response, err := productClient.PublishLeadSquaredCaptureMessage(ctx, leadsData)
		if err != nil {
			log.Printf("publishOTPVerificationToLeadSquaredTopic: Error in publishing otp verification data to leadsquared for mobile: %s, with response: %v, err: %v", mobile, response, err)
			return
		}
	}
}

func (s *Service) PostLoginUserDetails(ctx context.Context, req *pb.PostLoginReq, res *pb.PostLoginRes) error {

	switch req.PostAction {
	case "trial_sports":
		fallthrough
	case "trial_slots":
		trialDetails := &pb.TrialDetailsResponse{}
		userID := util.GetUserIDFromContext(ctx)
		err := s.GetTrialDetails(ctx, &pb.UserRequest{UserId: userID}, trialDetails)
		if err != nil {
			log.Printf("Error while GetTrialDetails err: %v", err)
			res.Status = &pb.Status{
				Status:  "failure",
				Message: "Cannot fetch trial details",
			}
			return nil
		}
		res.TrialDetails = trialDetails.TrialDetails
		break
	case "buy_for_me":
		productClient := util.GetProductClient()
		if req.UserId > 0 {
			reqData := &productPB.GetUserSubscriptionStatusRequest{
				UserId:                    req.UserId,
				ProductSuggestionRequired: true,
			}
			response, err := productClient.GetUserSubscriptionStatus(ctx, reqData)
			if err != nil {
				log.Printf("function: PostLoginUserDetails, error in fetching subscription status for UserId:%d, Error: %v", req.UserId, err)
				return nil
			}

			if response.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
				res.ActiveSubscription = true
			} else {
				res.ActiveSubscription = false
			}
		} else {
			res.ActiveSubscription = false
		}

		if res.ProductId <= 0 {
			var categoryIds []int32
			if Contains(testUsers, req.UserId) || time.Now().Unix() > singleKeyLaunchDateUnix {
				categoryIds = []int32{CATEGORY_ID_MASTER, CATEGORY_ID_SINGLE}
			} else {
				categoryIds = []int32{CATEGORY_ID_MASTER}
			}
			pdtReqData := &productPB.GetProductsForGivenCityReq{
				CategoryIds: categoryIds,
				FeatureType: 1,
			}

			pdtResponse, err := productClient.GetProductsForGivenCity(ctx, pdtReqData)
			if err != nil {
				log.Printf("function: PostLoginUserDetails, error in fetching featured products for UserId:%d, Error: %v", req.UserId, err)
				return nil
			}

			if len(pdtResponse.SuggestedProducts) > 0 {
				res.ProductId = pdtResponse.SuggestedProducts[0].ProductId
			}
		}
		break
	case "buy_for_others":
		productClient := util.GetProductClient()

		var categoryIds []int32
		if Contains(testUsers, req.UserId) || time.Now().Unix() > singleKeyLaunchDateUnix {
			categoryIds = []int32{CATEGORY_ID_MASTER, CATEGORY_ID_SINGLE}
		} else {
			categoryIds = []int32{CATEGORY_ID_MASTER}
		}
		if res.ProductId <= 0 {
			reqData := &productPB.GetProductsForGivenCityReq{
				CategoryIds: categoryIds,
				FeatureType: 1,
			}

			response, err := productClient.GetProductsForGivenCity(ctx, reqData)
			if err != nil {
				log.Printf("function: PostLoginUserDetails, error in fetching featured products for UserId:%d, Error: %v", req.UserId, err)
				return nil
			}

			if len(response.SuggestedProducts) > 0 {
				res.ProductId = response.SuggestedProducts[0].PId
			}
		}
	}

	res.Status = &pb.Status{
		Status:  "success",
		Message: "post-login user details fetched successfully",
	}

	return nil
}

func (s *Service) LogoutUser(ctx context.Context, req *pb.UserAccessTokens, res *pb.Ack) error {

	reqData := &models.UserAccessTokens{
		UserId:    req.UserId,
		ExpiredBy: req.ExpiredBy,
	}
	var tokenData []models.UserAccessTokens
	if err := s.Data.GetLogoutTokens(reqData, &tokenData); err != nil {
		fmt.Println("No user Login Found", err)
		return err
	}

	var tokens []string
	sessionTime := time.Duration(-1)
	for _, ele := range tokenData {
		reqToken := config.Get("codeIndependent", "redisAPrepend").String("") + "_token_" + string(ele.Token)
		if err := s.Data.SetExpiryOfValidationToken(reqToken, sessionTime); err != nil {
			fmt.Println("Token not expired")
			return err
		}
		tokens = append(tokens, ele.Token)
	}
	if err := s.Data.LogoutTokenIsExpired(tokens, reqData); err != nil {
		return errors.New(fmt.Sprintf("error in updating is_expired %v", err))
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}

	res.Status = status
	return nil
}

func (s *Service) GetInitialCityForUserBasedOnLatestPlan(userId int32, initialCityId *int32) error {
	fmt.Println("Geting initial city id for user -->", userId)

	if err := s.Data.InitialCityForUserBasedOnLatestPlanGet(userId, initialCityId); err != nil {
		fmt.Println("Error in getting initial city id: ", err)
		return err
	}

	return nil
}

func (s *Service) CreateNewUserV2(ctx context.Context, req *pb.CreateUser, res *pb.PhoneLoginResponseV2) error {

	var phoneData string
	createUserToken := "token_new_user_" + string(req.Token)

	loginToken := &pb.Token{
		LoginHash: req.Token,
	}
	res.Token = loginToken

	if err := s.Data.Validate(createUserToken, &phoneData); err != nil {
		log.Println("error in getting token from redis for new user %v", err)
		status := &pb.Status{
			Status:  notAuthorized,
			Message: "Try Again",
		}
		res.Status = status
		return nil
	}

	var phoneDetails pb.PhoneLoginRequest
	if len(phoneData) > 0 {

		if err := json.Unmarshal([]byte(phoneData), &phoneDetails); err != nil {
			return err
		}
		if req.Phone != phoneDetails.Phone {

			status := &pb.Status{
				Status:  notAuthorized,
				Message: "USER NOT AUTHORIZED",
			}
			res.Status = status
			return nil
		}
	} else {
		status := &pb.Status{
			Status:  notAuthorized,
			Message: "USER NOT AUTHORIZED",
		}
		res.Status = status
		return nil
	}
	appVersion, _ := util.GetAppVersionFromContext(ctx)
	appType, _ := util.GetClientIDFromContext(ctx)

	var prefferedAccByPhone pb.UserResponse
	userReq := &pb.UserRequest{
		Phone: req.Phone,
	}  
	if err := s.UserGet(context.TODO(), userReq, &prefferedAccByPhone); err != nil {
		log.Println("Error in getting user by phone..")
		return err
	}
	var userMod *pb.User
	for _, ele := range prefferedAccByPhone.Users {
		userMod = ele
		break
	}
	var userIdGlobal int32
	var emailExist bool
	//check if user present with email
	if userMod.UserId > 0 && (len(userMod.Email) > 0 || len(userMod.ContactEmail) > 0) {

		userIdGlobal = userMod.UserId
	} else {
		if len(req.Email) > 0 && len(req.Phone) > 0 {
			var prefferedAccByEmail pb.UserResponse
			userRequest := &pb.UserRequest{
				Email: req.Email,
			}
			if err := s.UserGet(ctx, userRequest, &prefferedAccByEmail); err != nil {
				log.Println("Error in getting user by email..")
				return err
			}
			var userEm *pb.User
			for _, element := range prefferedAccByEmail.Users {
				userEm = element
				break
			}
			if userEm.UserId > 0 {
				emailExist = true
			}
		} else {
			status := &pb.Status{
				Status:  badRequest,
				Message: "Cannot proceed without email and phone!",
			}
			res.Status = status
			return nil
		}
	}

	if userIdGlobal <= 0 {
		var fName string
		if err := GetFirstNameFromName(req.Name, &fName); err != nil {
			log.Println("Error in creating Frist Name from Name! Going ahead without throwing error!")
		}
		userCreateData := &pb.User{
			Phone:             req.Phone,
			Name:              req.Name,
			Gender:            req.Gender,
			FirstName:         fName,
			AppType:           appType,
			AppVersion:        appVersion,
			PhoneVerifiedFlag: 1,
		}
		if emailExist {
			userCreateData.ContactEmail = req.Email
		} else {
			userCreateData.Email = req.Email
		}
		var userCreateResponse pb.UserResponse
		if err := s.UserCreate(context.TODO(), userCreateData, &userCreateResponse); err != nil {
			log.Println("Error in creating new User..")
			return err
		}

		if len(userCreateResponse.Users) > 0 {
			// request zomato fitso user mapping
			createdUser := userCreateResponse.Users[0]
			if len(createdUser.Phone) > 0 && len(req.DeviceId) > 0 && len(req.AppsflyerId) > 0 {
				//device id update if present
				//or device id create
				//device mapping entry create with userId
				userDeviceData := &pb.DeviceDetail{
					UserId:			createdUser.UserId,
					DeviceId:		req.DeviceId,
					AppsflyerId: 	req.AppsflyerId,
					AppType:        appType,
					AppVersion:     appVersion,
				}
				go s.CreateUserDeviceDetailsAndMapping(ctx, userDeviceData)
			}

			message := UserMappingRequestMessage{
				Number:      createdUser.Phone,
				Email:       createdUser.Email,
				FitsoUserID: createdUser.UserId,
				IsdCode:     GetIntFormattedISDCode(createdUser.PhoneIsdCode),
			}

			s.RequestFitsoZomatoMapping(ctx, message)
			log.Println("UserForSignedUpPhone: phone", createdUser.Phone)
			if createdUser.Phone == "9953868016" || true {
				go s.CreateCultUserForSignedUpPhone(ctx, userCreateData, createdUser.UserId)
			}
			status := &pb.Status{
				Status:  success,
				Message: "Email Verification Successful",
			}
			res.Status = status
			userIdGlobal = userCreateResponse.Users[0].UserId
		}
	} else {
		status := &pb.Status{
			Status:  badRequest,
			Message: "User already Exist!",
		}
		res.Status = status
		return nil
	}
	ageRecord := models.UserAgeRecord{
		UserId:    userIdGlobal,
		Age:       req.Age,
		DateAdded: time.Now(),
	}
	if err := s.Data.SaveUserAge(ctx, &ageRecord); err != nil {
		log.Println("Error in saving age of new User..")
		return err
	}
	//use this user id to generate access token and then validate for login
	uInfo := &pb.UserInfo{
		UserId: userIdGlobal,
	}
	var atResp pb.GenerateAccessTokenResponse
	if err := s.GenerateAccessTokenV2(ctx, req, uInfo, &atResp); err != nil {
		log.Println("Error in generating access token..")
		return err
	}
	tokData := &pb.Token{
		Token: atResp.Token,
	}
	res.Token = tokData
	userDetails := &pb.User{
		UserId: userIdGlobal,
		Name:   req.Name,
		Email:  req.Email,
		Phone:  req.Phone,
	}
	res.User = userDetails
	res.UserExists = true

	return nil
}

func (s *Service) CreateUserDeviceDetailsAndMapping(ctx context.Context, userData *pb.DeviceDetail) error {
	deviceDetails := models.DeviceDetail{
		UserId:    		userData.UserId,
		DeviceId:       userData.DeviceId,
		AppsflyerId: 	userData.AppsflyerId,
		AppVersion:		userData.AppVersion,
		AppType:		userData.AppType,
	}
	if err := s.Data.CreateUpdateDeviceDetails(ctx, &deviceDetails); err != nil {
		log.Println("CreateUserDeviceDetailsAndMapping: Error in storing device details", deviceDetails)
		return err
	}
	deviceUserMap := models.DeviceUserMapping {
		UserId:    		userData.UserId,
		DeviceId:       userData.DeviceId,
		AppVersion:		userData.AppVersion,
		AppType:		userData.AppType,
	}
	if err := s.Data.CreateUserDeviceMapping(&deviceUserMap); err != nil {
		log.Println("CreateUserDeviceDetailsAndMapping: Error in storing device user mapping", deviceDetails)
		return err
	}
	return nil
}

func (s *Service) CreateCultUserForSignedUpPhone(ctx context.Context, userData *pb.User, userId int32) error {
	if len(userData.Phone) == 0 || len(userData.Name) == 0 {
		return nil
	}

	headers := map[string]string{
		util.ContentType: util.JsonContentType,
	}
	request := &util.Request{
		Method:      util.MethodTypeGet,
		RequestURL:  util.MigrateFitsoUser(userData.Phone, userData.Name),
		Headers:     headers,
		RequestBody: bytes.NewReader([]byte(nil)),
	}
	log.Println("CreateCultUserForSignedUpPhone - ", request)
	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("CreateCultUserForSignedUpPhone Error in migrating user err: %v", err)
		return nil
	}
	if response.Status != 200 {
		log.Printf("CreateCultUserForSignedUpPhone: Error in migrating user %s", response.Body)
		return nil
	}
	return nil
}

func (s *Service) CreateNewUser(ctx context.Context, req *pb.CreateUser, res *pb.CreateUserResponse) error {
	fmt.Println("req---- ", req)
	appVersion, _ := util.GetAppVersionFromContext(ctx)
	appType, _ := util.GetClientIDFromContext(ctx)
	req.AppType = appType
	req.AppVersion = appVersion
	var prefferedAccByPhone pb.UserResponse
	userReq := &pb.UserRequest{
		Phone: req.Phone,
	}
	if err := s.UserGet(context.TODO(), userReq, &prefferedAccByPhone); err != nil {
		fmt.Println("Error in getting user by phone..")
		return err
	}
	var userMod *pb.User
	for _, ele := range prefferedAccByPhone.Users {
		userMod = ele
		break
	}

	var userIdGlobal int32

	//check if user present with email -- update latest entered email
	if userMod.UserId > 0 && (len(userMod.Email) > 0 || len(userMod.ContactEmail) > 0) {
		//update latest email
		fmt.Println("User already exists with email and phone...")
		userIdGlobal = userMod.UserId
	} else if userMod.UserId > 0 { // user exists without email
		//check if user exits with email; if yes, update new email in contact email, else update email
		if len(req.Email) > 0 {
			var prefferedAccByEmail pb.UserResponse
			userRequest := &pb.UserRequest{
				Email: req.Email,
			}
			if err := s.UserGet(context.TODO(), userRequest, &prefferedAccByEmail); err != nil {
				fmt.Println("Error in getting user by email..")
				return err
			}

			var userEm *pb.User
			for _, element := range prefferedAccByEmail.Users {
				userEm = element
				break
			}

			if userEm.UserId > 0 { //user exists with the new email already -- update contact email for the user
				fmt.Println("Update contact email.......")
				upData := &models.User{
					ContactEmail: req.Email,
					UserID:       userMod.UserId,
				}
				var updateSuccessful bool
				if err := s.Data.UpdateUserDetails(upData, &updateSuccessful); err != nil {
					fmt.Println("Error in updating user details.. ContactEmail")
					return err
				}
			} else { // update email
				fmt.Println("Update email.......")
				upData := &models.User{
					Email:  req.Email,
					UserID: userMod.UserId,
				}
				var updateSuccessful bool
				if err := s.Data.UpdateUserDetails(upData, &updateSuccessful); err != nil {
					fmt.Println("Error in updating user details.. Email")
					return err
				}
			}
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "Cannot proceed without email!",
			}
			res.Status = status
		}

		userIdGlobal = userMod.UserId
	} else {
		if len(req.Email) > 0 || len(req.Phone) > 0 {
			var prefferedAccByEmail pb.UserResponse
			userRequest := &pb.UserRequest{
				Email: req.Email,
			}
			if err := s.UserGet(context.TODO(), userRequest, &prefferedAccByEmail); err != nil {
				fmt.Println("Error in getting user by email..")
				return err
			}
			var userEm *pb.User
			for _, element := range prefferedAccByEmail.Users {
				userEm = element
				break
			}

			if userEm.UserId > 0 && len(req.Phone) > 0 {
				fmt.Println("Update phone.......")
				upData := &models.User{
					Phone:  req.Phone,
					UserID: userEm.UserId,
				}
				var updateSuccessful bool
				if err := s.Data.UpdateUserDetails(upData, &updateSuccessful); err != nil {
					fmt.Println("Error in updating user details.. Phone")
					return err
				}
				userIdGlobal = userEm.UserId
			}
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "Cannot proceed without email and phone!",
			}
			res.Status = status
		}
	}

	if userIdGlobal <= 0 { //new user needs to be created
		fmt.Println("Creating new user....")
		var fName string
		if err := GetFirstNameFromName(req.Name, &fName); err != nil {
			fmt.Println("Error in creating Frist Name from Name! Going ahead without throwing error!")
			return err
		}

		userCreateData := &pb.User{
			Phone:             req.Phone,
			Email:             req.Email,
			Name:              req.Name,
			Gender:            req.Gender,
			FirstName:         fName,
			AppType:           req.AppType,
			AppVersion:        req.AppVersion,
			PhoneVerifiedFlag: 1,
		}
		var userCreateResponse pb.UserResponse
		if err := s.UserCreate(context.TODO(), userCreateData, &userCreateResponse); err != nil {
			fmt.Println("Error in creating new User..")
			return err
		}

		if len(userCreateResponse.Users) > 0 {
			// request zomato fitso user mapping
			createdUser := userCreateResponse.Users[0]

			message := UserMappingRequestMessage{
				Number:      createdUser.Phone,
				Email:       createdUser.Email,
				FitsoUserID: createdUser.UserId,
				IsdCode:     GetIntFormattedISDCode(createdUser.PhoneIsdCode),
			}

			s.RequestFitsoZomatoMapping(ctx, message)

			status := &pb.Status{
				Status:  "success",
				Message: "New User Created!",
			}
			res.Status = status

			res.NewSignup = true
			userIdGlobal = userCreateResponse.Users[0].UserId
		}
	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "User already exists! Necessary details updated!",
		}
		res.Status = status
		res.NewSignup = false
	}

	fmt.Println("Final User Id ---------------- ", userIdGlobal)

	//use this user id to generate access token and then validate for login
	uInfo := &pb.UserInfo{
		UserId: userIdGlobal,
	}
	var atResp pb.GenerateAccessTokenResponse
	if err := s.GenerateAccessToken(context.TODO(), uInfo, &atResp); err != nil {
		fmt.Println("Error in generating access token..")
		return err
	}

	//validate
	uReq := &pb.UserRequest{
		UserId: userIdGlobal,
		Token:  atResp.Token,
	}
	var tokenResp pb.Token
	if err := s.ValidateToken(context.TODO(), uReq, &tokenResp); err != nil {
		fmt.Println("Error in getting token data...")
		return err
	}
	res.User = append(res.User, tokenResp.TokenData.User)
	tokData := &pb.Token{
		Token: atResp.Token,
	}
	res.Token = tokData

	return nil
}

func (s *Service) UploadPushInfo(ctx context.Context, req *pb.PushInfo, res *pb.Ack) error {
	fmt.Println("Push Info:", req)

	status := &pb.Status{
		Status:  "failure",
		Message: "Push info updation failed",
	}

	/* delete existing fcm token
	create new entry for fcm token and device token
	deivice token is only for ios */

	//update - set deleted flag 1 for fcm
	var userPushToken []models.UserPushToken

	userPushTokenData := &models.UserPushToken{
		// UserID:      req.UserID, // leave it commented
		FcmToken:    req.FcmToken,
		DeviceToken: req.DeviceToken,
	}

	updateCompleted := 0
	if err := s.Data.UpdatePushToken(userPushTokenData, &userPushToken); err != nil {
		errorr := &pb.Error{
			Code:        101,
			Description: fmt.Sprintf("error updating push token: %v", err),
		}

		res.Error = append(res.Error, errorr)
	} else {
		updateCompleted = 1
	}
	//update done

	//create fcm token
	pushToken := &models.UserPushToken{
		FcmToken:    req.FcmToken,
		DeviceToken: req.DeviceToken,
		UserID:      req.UserId,
		AppType:     req.AppType,
		AppVersion:  req.AppVersion,
	}

	createCompleted := 0
	if err := s.Data.CreatePushToken(pushToken); err != nil {
		errorr := &pb.Error{
			Code:        102,
			Description: fmt.Sprintf("error creating push token: %v", err),
		}

		res.Error = append(res.Error, errorr)
	} else {
		if pushToken.Id > 0 { //entry created
			createCompleted = 1
		}
	}

	if updateCompleted == 1 && createCompleted == 1 {
		//status
		status.Status = "success"
		status.Message = "Push info updated successfully"
	}

	res.Status = status
	return nil
}

func (s *Service) GetFCMDataForUserIds(ctx context.Context, req *pb.FCMDataRequest, res *pb.FCMDataResponse) error {

	fmt.Println("Push Info:", req)

	var fcmData []models.UserPushToken

	if err := s.Data.GetFCMDataForUserIds(req, &fcmData); err != nil {
		return errors.New(fmt.Sprintf("error gettting fcm token data: %v", err))
	}

	userIdTokenBoolMap := make(map[int32]bool)

	for _, data := range fcmData {
		pbData := data.Proto()
		res.Data = append(res.Data, pbData)
		if _, ok := userIdTokenBoolMap[data.UserID]; !ok {
			userIdTokenBoolMap[data.UserID] = true
		}
	}

	var withoutTokenUserIds []int32
	for _, userId := range req.UserIds {
		if _, exists := userIdTokenBoolMap[userId]; !exists {
			withoutTokenUserIds = append(withoutTokenUserIds, userId)
		}
	}

	if len(withoutTokenUserIds) > 0 {
		var parentUserIds []int32
		if err := s.Data.GetParentUserIdsForUserIds(ctx, withoutTokenUserIds, &parentUserIds); err != nil {
			return errors.New(fmt.Sprintf("error gettting parent user ids list for child users whose fcm token is not found: %v", err))
		}
		if len(parentUserIds) > 0 {
			var fcmDataReq pb.FCMDataRequest
			fcmDataReq.UserIds = parentUserIds
			var fcmDataForParentUsers []models.UserPushToken
			if err := s.Data.GetFCMDataForUserIds(&fcmDataReq, &fcmDataForParentUsers); err != nil {
				return errors.New(fmt.Sprintf("error gettting fcm token data for parent users: %v", err))
			}
			for _, data := range fcmDataForParentUsers {
				pbData := data.Proto()
				res.Data = append(res.Data, pbData)
			}
		}
	}

	return nil
}

func (s *Service) GenerateOTPForLogin(ctx context.Context, req *pb.GenerateOTPRequest, res *pb.OtpResponse) error {

	if len(req.Phone) != 10 {
		status := &pb.Status{
			Status:  badRequest,
			Message: "Incorrect Phone Number",
		}
		res.Status = status
		return nil
	}
	if _, err := strconv.Atoi(req.Phone); err != nil {
		log.Println("Phone number should contain digits Error: %v", err)
		status := &pb.Status{
			Status:  badRequest,
			Message: "Phone number should contain digits",
		}
		res.Status = status
		return nil
	}

	var otp string
	var status *pb.Status
	var error *pb.Error
	if req.OtpFor == pb.GenerateOTP_VERIFY_PHONE {
		otp = CreateOTP(6)
	} else {
		if req.Phone == "8888888899" {
			if featuresupport.SupportsNewLogin(ctx) {
				otp = "410399"
			} else {
				otp = "4103"
			}
		} else if featuresupport.SupportsNewLogin(ctx) {
			otp = CreateOTP(6)
		} else {
			otp = CreateOTP(4)
		}
	}
	//log.Println("otp- ", otp)

	var userOtp structs.UserOtpCount
	userOtp.OTP = otp
	userOtp.OtpValidateCount = 11
	userOtp.OtpGenerateCount = 10
	appType, _ := util.GetClientIDFromContext(ctx)

	if err := s.Data.GenerateAndSaveOtp(ctx, req, &userOtp); err != nil {

		status = &pb.Status{
			Status:  "failure",
			Message: "No user exists with this access token",
		}

	} else {

		status = &pb.Status{
			Status: "success",
		}
		res.ResendSeconds = 30
	}

	if userOtp.IsCacheTTLInfinite {
		fromEm := &structs.Email{
			Name:         "Fitso",
			EmailAddress: FITSO_BOT_MAIL,
		}

		toEmails := [1]string{TECH_MAIL}

		var toEm []*structs.Email

		for _, emailId := range toEmails {
			toEmVal := &structs.Email{
				EmailAddress: emailId,
			}
			toEm = append(toEm, toEmVal)
		}

		var message string

		message = fmt.Sprintf("No cache TTL was set for the user with phone number %s<br>, Kindly clear the cache key for otp.<br><br>Regards,<br>Team Fitso", req.Phone)

		emailReq := &structs.EmailRequest{
			From:    fromEm,
			To:      toEm,
			ReplyTo: fromEm,
			Subject: "ALERT : No Cache TTL For OTP",
			Message: message,
		}
		go s.Pub.SendBroadcastEmail(emailReq)
	}
	var smsText string

	smsText = "Your FITSO login OTP is " + userOtp.OTP + " . OTP expires in 10 minutes. Qer8FgqXL9y"

	phones := make([]string, 0)
	phones = append(phones, req.Phone)

	if len(req.Phone) >= 0 && userOtp.OtpGenerateCount > 0 { //only this is functional now

		ncl = util.GetNotificationClient()
		useZomatoSMS := config.Get("sms", "use_zomato_sms").Int(0)

		if useZomatoSMS == 1 && req.Phone != "9650200199" && req.Phone != "9868235529" {
			templateArgs := &notificationPB.TemplateArgument{
				OTP:     userOtp.OTP,
				AppHash: AppHashAndroidTemplate,
			}

			if req.OtpFor == pb.GenerateOTP_VERIFY_PHONE {
				loggedInUserId := util.GetUserIDFromContext(ctx)
				if loggedInUserId == 0 {
					res.Status = &pb.Status{
						Status:  failed,
						Message: "Unauthorized request",
					}
					return nil
				}
				var loggedInUserDetails pb.UserResponse
				err := s.UserGet(ctx, &pb.UserRequest{UserId: loggedInUserId}, &loggedInUserDetails)
				if err != nil || len(loggedInUserDetails.Users) == 0 {
					return fmt.Errorf("error in sending verification code fromm user_id: %d, %v", loggedInUserId, err)
				}
				loggedInUserName := loggedInUserDetails.Users[0].Name
				if len(loggedInUserName) > 0 {
					templateArgs.Name = strings.Title(loggedInUserName)
				} else {
					templateArgs.Name = "Fitso User"
				}
			}

			args, _ := ptypes.MarshalAny(templateArgs)
			num, err := strconv.ParseInt(req.Phone, 10, 64)
			if err != nil {
				return err
			}

			request := &notificationPB.SendSMSRequest{
				Number:            num,
				TemplateId:        loginOTPSMSTemplateID,
				TemplateArguments: args,
				IosAlpha3:         iosAlpha3India,
				Callback:          apiFitsoHost + "/v2/user/login/sms/callback",
			}
			appType, _ := util.GetClientIDFromContext(ctx)
			if appType == "fitso_android" {
				request.TemplateId = loginOTPSMSTemplateIDAndroid
			} else {
				request.TemplateId = loginOTPSMSTemplateID
			}

			if req.OtpFor == pb.GenerateOTP_VERIFY_PHONE {
				request.TemplateId = verifyPhoneTemplateID
			}

			_, err = ncl.SendSMSV2(ctx, request)
			if err != nil {
				fmt.Println("Error while sending sms using zomato service", err)
			}

		} else {

			if req.OtpFor == pb.GenerateOTP_VERIFY_PHONE {
				loggedInUserId := util.GetUserIDFromContext(ctx)
				if loggedInUserId == 0 {
					res.Status = &pb.Status{
						Status:  failed,
						Message: "Unauthorized request",
					}
					return nil
				}
				var loggedInUserDetails pb.UserResponse
				err := s.UserGet(ctx, &pb.UserRequest{UserId: loggedInUserId}, &loggedInUserDetails)
				if err != nil || len(loggedInUserDetails.Users) == 0 {
					return fmt.Errorf("error in sending verification code fromm user_id: %d, %v", loggedInUserId, err)
				}
				loggedInUserName := loggedInUserDetails.Users[0].Name
				if len(loggedInUserName) > 0 {
					smsText = fmt.Sprintf(OTP_Academy, strings.Title(loggedInUserName), userOtp.OTP)
				} else {
					smsText = fmt.Sprintf(OTP_Academy, "Fitso User", userOtp.OTP)
				}
			} else if appType == "fitso_android" {
				smsText = fmt.Sprintf(OTP_Final_2, userOtp.OTP, AppHashAndroidTemplate)
			} else {
				smsText = fmt.Sprintf(OTP_New_5, userOtp.OTP)
			}

			reqVal := &notificationPB.SMSRequest{
				Numbers: phones,
				Message: smsText,
			}
			_, err := ncl.SendOTPSmsFitso(ctx, reqVal)
			if err != nil {
				log.Printf("Error in sending OTP to user %v having error %v", phones, err)
			}
		}

		go s.StoreOtpCount("generated")
		go s.StoreOtpNumber("generatedNumbers", req.Phone)
	} else if len(req.Phone) >= 0 && !(userOtp.OtpGenerateCount > 0) {
		status = &pb.Status{
			Status: "success",
		}
	} else {
		fmt.Println("Test Here Kafka")
		//not sending otp through kafka -- temp
		if errB := s.Pub.MessagePublish(phones, smsText); errB != nil {
			error = &pb.Error{
				Code:        101,
				Description: "error in publishing message",
			}

			res.Error = append(res.Error, error)
		}
	}

	res.Status = status
	return nil
}

func (s *Service) Logout(ctx context.Context, req *pb.LogoutRequest, res *pb.Ack) error {
	fmt.Println("logout :", req)

	if err := s.Data.Logout(req.Token, req.FcmToken); err != nil {
		fmt.Println("error in deleting token: ", err)

		error := &pb.Error{
			Code:        101,
			Description: "error in deleting token",
		}
		res.Error = append(res.Error, error)
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Logout Successful!",
	}
	res.Status = status

	return nil
}

func (s *Service) LogoutV2(ctx context.Context, req *pb.LogoutRequest, res *pb.Status) error {
	loggedInUserId := util.GetUserIDFromContext(ctx)

	err := s.Data.InvalidateAccessToken(ctx, req.Token)
	if err != nil {
		log.Printf("Unable to delete access token for userId: %d, Error: %v", loggedInUserId, err)
		return err
	}

	if req.FcmToken != "" {
		if err = s.Data.InvalidateFcmToken(ctx, req.FcmToken); err != nil {
			log.Printf("Unable to delete fcm token for userId: %d, Error: %v", loggedInUserId, err)
			return err
		}
	}
	res.Status = success
	return nil
}

// ResetUserDataInRedis resets data corresponding to user token in redis
func (s *Service) ResetUserDataInRedis(ctx context.Context, userData *models.User, token string, dataResetSuccessful *bool) error {
	userDataFromToken, err := s.fetchUserDataFromRequestToken(ctx, token)

	if err != nil {
		return err
	}

	if userData.UserID != userDataFromToken.UserID {
		errStr := fmt.Sprintf("user %d not authorised to update token data for user %d", userData.UserID, userDataFromToken.UserID)
		return errors.New(errStr)
	}

	//identify switch or not
	switchCase := false
	var allConnectedUsers []*pb.UserChildMapping
	allConnectedUsers = userDataFromToken.Proto().AllConnectedUsers
	if len(allConnectedUsers) > 1 {
		switchCase = true
	}
	if userData.UserID == 0 {
		userData.UserID = userDataFromToken.UserID
	}
	var userDB models.User
	if err := s.Data.Get(userData, &userDB); err != nil {
		fmt.Println("Error in fetching user", err)
		*dataResetSuccessful = false
		return err
	} else {
		*dataResetSuccessful = true
	}

	//fetch skill levels and add sports skills in user obj here
	skillReq := &pb.UserSportSkillRequest{
		UserId: userData.UserID,
	}
	var skillRes pb.SkillResponse
	if err := s.GetUserSkillLevel(context.TODO(), skillReq, &skillRes); err != nil {
		fmt.Println("Could not fetch user skill levels! Unknown error!")
		fmt.Println("Proceeding without it...")
	}
	if skillRes.Status.Status == "success" {
		userDB.OverallSkillLevel = skillRes.OverallSkillLevel
	}
	//skill fetch over

	//fetch initial city
	//get initial city for user based on plan
	var initialCityId int32
	if err := s.GetInitialCityForUserBasedOnLatestPlan(userDB.UserID, &initialCityId); err != nil {
		fmt.Println("Could not determine initial city id for user: ", err)
		fmt.Println("Proceeding without throwing error!")
	}
	userDB.PlanCityId = initialCityId

	//fetch connected users
	if switchCase {
		//first get parent for user, then get all children
		uInfoParent := &pb.UserInfo{
			UserId: userDB.UserID,
		}
		var puResponse pb.ParentUserResponse //response stored here
		if err := s.GetParentUser(context.TODO(), uInfoParent, &puResponse); err != nil {
			fmt.Println("Could not fetch parent user! Unknown error!")
			return err
		}
		var uInfo pb.UserInfo
		if puResponse.Status.Status == "success" {
			uInfo.UserId = puResponse.ParentUser.ParentUserId
		}

		var uResponse pb.ChildUserResponse //response stored here
		if err := s.GetChildUsers(context.TODO(), &uInfo, &uResponse); err != nil {
			fmt.Println("Could not fetch all connected users! Unknown error!")
			return err
		}
		//add all connected users in user object here
		if uResponse.Status.Status == "success" {
			userDB.AllConnectedUsers = uResponse.AllConnectedUsers
		}
	} else {
		uInfo := &pb.UserInfo{
			UserId: userDB.UserID,
		}
		var uResponse pb.ChildUserResponse //response stored here
		if err := s.GetChildUsers(context.TODO(), uInfo, &uResponse); err != nil {
			fmt.Println("Could not fetch all connected users! Unknown error!")
			return err
		}
		//add all connected users in user object here
		if uResponse.Status.Status == "success" {
			userDB.AllConnectedUsers = uResponse.AllConnectedUsers
		}
	}
	//fetch connected users over

	if err := s.Data.Tokenize(token, &userDB); err != nil {
		fmt.Println("Error in saving updated data in redis", err)
		*dataResetSuccessful = false
		return err
	} else {
		*dataResetSuccessful = true
	}

	return nil
}

// UpdateUserInfo updates user info
func (s *Service) UpdateUserInfo(ctx context.Context, req *pb.UserInfo, res *pb.Ack) error {
	var updateSuccessful bool
	var message string
	if err := s.Data.UpdateUserInfo(req, &updateSuccessful, &message); err != nil {
		fmt.Println("error in updating user details: ", err)

		status := &pb.Status{
			Status:  failed,
			Message: message,
		}
		res.Status = status

		error := &pb.Error{
			Code:        603,
			Description: "error in updating user details.",
		}
		res.Error = append(res.Error, error)

		return err
	}

	if updateSuccessful { //success
		if req.DateOfBirth > 0 {
			go s.Data.CreateUserAgeRecordFromDob(ctx, req.UserId, req.DateOfBirth)
		} else if req.Age > 0 {
			ageRecord := models.UserAgeRecord{
				UserId:    req.UserId,
				Age:       req.Age,
				DateAdded: time.Now(),
			}
			go s.Data.SaveUserAge(ctx, &ageRecord)
		}

		status := &pb.Status{
			Status:  "success",
			Message: message,
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: message,
		}
		res.Status = status
	}

	if updateSuccessful {

		dataResetSuccessful := false
		userData := &models.User{
			UserID: req.UserId,
		}

		//reset data in redis
		err := s.ResetUserDataInRedis(ctx, userData, req.Token, &dataResetSuccessful)
		if err == redis.Nil {
			dataResetSuccessful = true
			log.Println("No cache found for the user data in redis")
		} else if err != nil {
			log.Println("Error in reseting data in redis")
			return err
		}

		if dataResetSuccessful {
			status := &pb.Status{
				Status:  "success",
				Message: message,
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: message,
			}
			res.Status = status

			//res.Error is already populated here
		}
	}

	return nil
}

func (s *Service) UploadProfilePicture(ctx context.Context, req *pb.ProfilePic, res *pb.UploadProfilePictureResponse) error {
	userID := util.GetUserIDFromContext(ctx)

	if userID <= 0 {
		res.Status = &pb.Status{
			Status:  notAuthorized,
			Message: "You are not allowed to make this request!",
		}

		return nil
	}

	req.UserId = userID
	maxFileSizeInMB := 5

	failedStatus := &pb.Status{
		Status:  failed,
		Message: "something went wrong",
	}

	extensionArray, err := mime.ExtensionsByType(req.ContentType)

	if len(extensionArray) == 0 || err != nil {
		log.Printf("not a valid extension for user %d with error: %v", userID, err)
		res.Status = failedStatus

		return nil
	}

	fileSizeInMB := GetSizeInMB(len(req.Image))

	if fileSizeInMB > maxFileSizeInMB {
		log.Printf("error image size %d user %d", fileSizeInMB, userID)
		res.Status = failedStatus

		return nil
	}

	if !IsValidProfilePictureContentType(req.ContentType) {
		log.Printf("not a valid profile pic for user %d", userID)
		res.Status = failedStatus

		return nil
	}
	imageMD5Hash := fmt.Sprintf("%x", md5.Sum(req.Image))
	tempImagePath := SaveTempImage(req.Image, imageMD5Hash, req.ContentType)

	if len(tempImagePath) == 0 {
		log.Printf("not a valid temp image path for user %d", userID)
		res.Status = failedStatus

		return nil
	}

	s3Destination := fmt.Sprintf(
		"%s%s%s",
		config.Get("static", "profilePicDir").String(""),
		imageMD5Hash,
		extensionArray[0],
	)
	uploadSuccessful := s.ImagesAwsS3.Upload(tempImagePath, s3Destination, req.ContentType)

	// remove temp image
	err = os.Remove(tempImagePath)
	if err != nil {
		log.Printf("Unable to remove file from path %s", tempImagePath)
	}

	if !uploadSuccessful {
		log.Printf("could not upload file to s3 for user %d", userID)
		res.Status = failedStatus

		return nil
	}

	var updateSuccessful bool
	var message string

	req.DisplayPicture = imageMD5Hash
	req.Extension = extensionArray[0]

	if err := s.Data.UploadProfilePicture(req, &updateSuccessful, &message); err != nil {
		log.Printf("[image upload] error in save profile picture md5 hash for user %d", userID)
		return err
	}

	if updateSuccessful {
		dataResetSuccessful := false
		userData := &models.User{
			UserID: userID,
		}

		if err := s.ResetUserDataInRedis(ctx, userData, req.Token, &dataResetSuccessful); err != nil {
			log.Printf("[image upload] error in resetting cache for user %d", userID)
			return err
		}

		status := &pb.Status{
			Status:  success,
			Message: message,
		}
		res.Status = status
		res.DisplayPicture = fmt.Sprintf("%s/%s", cdnHost, s3Destination)

	} else {
		status := &pb.Status{
			Status:  failed,
			Message: message,
		}
		res.Status = status
	}

	return nil
}

func (s *Service) RemoveProfilePicture(ctx context.Context, req *pb.ProfilePic, res *pb.RemoveProfilePictureResponse) error {
	userID := util.GetUserIDFromContext(ctx)

	if userID <= 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: notAuthorized,
		}

		return nil
	}

	req.UserId = userID
	if req.UserId > 0 {
		var updateSuccessful bool
		var message string
		var removedImage string
		var defaultImage string
		if err := s.Data.RemoveProfilePicture(req, &updateSuccessful, &message, &removedImage, &defaultImage); err != nil {
			fmt.Println("error in removing user profile picture: ", err)

			status := &pb.Status{
				Status:  failed,
				Message: message,
			}
			res.Status = status
			return err
		}
		if updateSuccessful { //success

			dataResetSuccessful := false
			userData := &models.User{
				UserID: req.UserId,
			}

			//reset data in redis
			if err := s.ResetUserDataInRedis(ctx, userData, req.Token, &dataResetSuccessful); err != nil {
				fmt.Println("Error in reseting data in redis")
				return err
			}

			if dataResetSuccessful {
				status := &pb.Status{
					Status:  success,
					Message: message,
				}
				res.Status = status
				res.RemovedImageHash = config.Get("static", "profilePicDirUrl").String("https://fitso-images.curefit.co/profile_picture/") + removedImage + ".jpg"
				res.DefaultImage = defaultImage
			} else {
				status := &pb.Status{
					Status:  success,
					Message: message,
				}
				res.Status = status
			}
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: message,
			}
			res.Status = status
		}

	} else {
		fmt.Println("Invalid Request - user_id is empty.")

		status := &pb.Status{
			Status:  failed,
			Message: "Insufficient Data",
		}
		res.Status = status
	}
	return nil
}

// func (s *Service) SendNotificationMessage(ctx context.Context, req *pb.NotificationMessageRequest, res *pb.Ack) error {
// 	fmt.Println("Send Broadcast :", req)
// 	for _, element := range req.NotificationArray {
// 		var userIds []int32
// 		userIds = append(userIds,element.UserId)
// 		broadData := &pb.Broadcast{
// 				SenderUserId:      281434, //<EMAIL> uid -- for log in table
// 				ReceivingUserIds:  userIds,
// 				NotificationTitle: "Keep your sport-wear handy!",
// 				NotificationBody:  element.Message,
// 				NotificationFlag:  true,
// 				ActionId:          6,
// 				Action:            "upcoming_bookings",
// 			}
// 		broadcastResponse := &pb.Ack{
// 		}

// 		if err := s.SendBroadcast(context.TODO(),broadData,broadcastResponse); err != nil {
// 			fmt.Println("Error in sending broadcast", err)
// 		}
// 	}

// 	status := &pb.Status{
// 		Status:  "success",
// 		Message: "Broadcast send successfully",
// 	}
// 	res.Status = status

// 	return nil
// }

func (s *Service) SendBroadcast(ctx context.Context, req *pb.Broadcast, res *pb.Ack) error {
	fmt.Println("Send Broadcast Request :", req)
	/*
		Hierarchy -
			product_ids
			fs_ids
			user_ids
	*/

	operationSuccessful := false

	//check if product_ids exits, find out all related users. already booked users and can book users. Then overwrite receiving uids
	if len(req.ProductIds) > 0 {
		//get all active users with product_ids
		var usersArr []int32
		if err := s.Data.GetUsersForProductId(req.ProductIds, &usersArr); err != nil {
			fmt.Println("Error in getting users for product_id..")
			return err
		}
		if len(usersArr) > 0 { //overwriting uids with product_ids -> uids
			req.ReceivingUserIds = usersArr
		}
	} else if len(req.FsIds) > 0 {
		var usersArr []int32
		if err := s.Data.GetUsersForFsIds(req.FsIds, &usersArr); err != nil {
			fmt.Println("Error in getting users for fs_ids..")
			return err
		}
		if len(usersArr) > 0 { //overwriting uids with fs_ids -> uids
			req.ReceivingUserIds = usersArr
		}

		fmt.Println("RecievingUserId", req.ReceivingUserIds)
	}

	var countOfUsers int = 0

	//enter into broadcast logs - user broad_logs - fs_broad_logs (if applicable)
	//proceed only if either email sms or notification flag is present
	if req.EmailFlag || req.SmsFlag || req.NotificationFlag {

		countOfUsers = len(req.ReceivingUserIds)

		bData := &models.SportsBroadcastLogs{
			SenderUserId:      req.SenderUserId,
			EmailSubject:      req.EmailSubject,
			EmailBody:         req.EmailBody,
			SmsBody:           req.SmsBody,
			NotificationTitle: req.NotificationTitle,
			NotificationBody:  req.NotificationBody,
			EmailFlag:         req.EmailFlag,
			SmsFlag:           req.SmsFlag,
			NotificationFlag:  req.NotificationFlag,
			FilterText:        req.FilterText,
			UserCount:         int32(len(req.ReceivingUserIds)),
			ReceivingUserIds:  req.ReceivingUserIds,
		}

		var createSuccessful bool
		if err := s.Data.CreateBroadcastLog(bData, &createSuccessful); err != nil { //this func also creates user <-> broadcast mapping logs
			fmt.Println("error in creating broadcast log: ", err)

			status := &pb.Status{
				Status:  "failure",
				Message: "Could not send broadcast",
			}
			res.Status = status

			error := &pb.Error{
				Code:        606,
				Description: "error in creating broadcast log.",
			}
			res.Error = append(res.Error, error)

			return err
		}

		if len(req.FsIds) > 0 && createSuccessful { //create fs broadcast mapping here

			var fsCreateSuccessful bool

			var fsbDataArr []models.FacilitySportsBroadcastLogs
			for _, element := range req.FsIds {
				fsbData := models.FacilitySportsBroadcastLogs{
					BroadcastId: bData.BroadcastId,
					FsId:        element,
				}
				fsbDataArr = append(fsbDataArr, fsbData)
			}

			if err := s.Data.CreateFsBroadcastLog(fsbDataArr, &fsCreateSuccessful); err != nil { //this func creates fs <-> broadcast mapping logs
				fmt.Println("error in creating fs broadcast logs::::::: ", err)
			}

			if !fsCreateSuccessful {
				operationSuccessful = false
			} else {
				operationSuccessful = true
			}
		}

		if operationSuccessful || createSuccessful {
			status := &pb.Status{
				Status:  "success",
				Message: "Broadcast triggered successfully! Report will be shared soon.",
			}
			res.Status = status

			if !operationSuccessful {
				error := &pb.Error{
					Code:        607,
					Description: "created Broadcast Logs successfully, could not create fs broadcast mapping!",
				}
				res.Error = append(res.Error, error)
			}
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not send broadcast!",
			}
			res.Status = status

			error := &pb.Error{
				Code:        608,
				Description: "Could not send broadcasts!",
			}
			res.Error = append(res.Error, error)
		}

		if req.EmailFlag { //send email with applicable params

			emailData := &pb.EmailBroadcast{
				SenderUserId:     req.SenderUserId,
				ReceivingUserIds: req.ReceivingUserIds,
				EmailSubject:     req.EmailSubject,
				EmailBody:        req.EmailBody,
				BroadcastId:      bData.BroadcastId,
			}

			if emErr := s.SendBroadcastEmail(emailData); emErr != nil {
				fmt.Println("Couldn't send broadcast email!")
			}
		}

		if req.SmsFlag { //send sms with applicable params

			smsData := &pb.SmsBroadcast{
				SenderUserId:     req.SenderUserId,
				ReceivingUserIds: req.ReceivingUserIds,
				SmsBody:          req.SmsBody,
				BroadcastId:      bData.BroadcastId,
			}

			if smsErr := s.SendBroadcastSms(smsData); smsErr != nil {
				fmt.Println("Couldn't send broadcast sms!")
			}
		}

		if req.NotificationFlag { //send notification with applicable params

			notificationData := &pb.NotificationBroadcast{
				SenderUserId:      req.SenderUserId,
				ReceivingUserIds:  req.ReceivingUserIds,
				NotificationBody:  req.NotificationBody,
				NotificationTitle: req.NotificationTitle,
				Id:                bData.BroadcastId,
				Action:            req.Action,
				ActionId:          req.ActionId,
				BroadcastId:       bData.BroadcastId,
			}

			if nErr := s.SendBroadcastNotification(notificationData); nErr != nil {
				fmt.Println("Couldn't send broadcast notification!")
			}
		}
		//call function for <NAME_EMAIL>
		go s.SendToCxTeam(countOfUsers, countOfUsers, countOfUsers, bData)
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not send broadcast! Nothing selected.",
		}
		res.Status = status

		error := &pb.Error{
			Code:        608,
			Description: "Could not send broadcasts!",
		}
		res.Error = append(res.Error, error)
	}

	return nil
}

func (s *Service) SendToCxTeam(countOfEmailSent int, countOfSMSSent int, countOfNotificationsSent int, bData *models.SportsBroadcastLogs) error {

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	var toEm []*structs.Email

	toEmCX := &structs.Email{
		Name:         "CX",
		EmailAddress: "<EMAIL>",
	}

	toEmSB := &structs.Email{
		Name:         "Sports Broadcast",
		EmailAddress: "<EMAIL>",
	}

	toEm = append(toEm, toEmCX)
	toEm = append(toEm, toEmSB)

	type SUMMARY struct {
		CountOfNotificationsSent string
		NotificationTitle        string
		NotificationText         string
		CountOfEmailSent         string
		EmailTitle               string
		EmailText                string
		CountOfSMSSent           string
		SmsTitle                 string
	}

	buf := new(bytes.Buffer)

	summarys := SUMMARY{strconv.Itoa(countOfNotificationsSent), bData.NotificationTitle, bData.NotificationBody,
		strconv.Itoa(countOfEmailSent), bData.EmailSubject, bData.EmailBody, strconv.Itoa(countOfSMSSent), bData.SmsBody}

	tpl := template.Must(template.ParseFiles("handler/view/text.gohtml"))

	err1 := tpl.Execute(buf, summarys)
	if err1 != nil {
		fmt.Println("errror", err1)
	}
	//String render
	//fmt.Println("buf string ", buf.String())

	//Please Dont Change the logic
	var emailSub string
	if countOfNotificationsSent > 0 {
		emailSub = bData.NotificationTitle
	}
	if countOfSMSSent > 0 {
		emailSub = "Broadcast message | Broadcast_Id : " + fmt.Sprint(bData.BroadcastId)
	}
	if countOfEmailSent > 0 {
		emailSub = bData.EmailSubject
	}

	emailRequest := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: emailSub,
		Message: buf.String(),
	}

	if err := s.Pub.SendToCxTeam(emailRequest); err != nil {
		fmt.Println("Error in sending broadcast email")
		return err
	}

	return nil
}

func (s *Service) SendBroadcastEmail(emailData *pb.EmailBroadcast) error {

	fmt.Println("Here in email broadcast function --- ", emailData)

	var nameEmail []structs.Email

	//get Name & Emails for receiving userIds
	if err := s.Data.GetNameAndEmailsForUserIds(emailData.ReceivingUserIds, &nameEmail); err != nil {
		fmt.Println("Getting email - ", err)
		return err
	}

	go s.SendBroadcastEmailAsync(nameEmail, emailData)

	return nil

}

func (s *Service) SendBroadcastEmailAsync(nameEmail []structs.Email, emailData *pb.EmailBroadcast) error {

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	for _, element := range nameEmail {

		var toEm []*structs.Email

		toEmVal := &structs.Email{
			Name:         element.Name,
			EmailAddress: element.EmailAddress,
		}

		toEm = append(toEm, toEmVal)

		emailReq := &structs.EmailRequest{
			From:        fromEm,
			To:          toEm,
			ReplyTo:     fromEm,
			Subject:     emailData.EmailSubject,
			Message:     emailData.EmailBody,
			BroadcastId: emailData.BroadcastId,
		}

		if errB := s.Pub.SendBroadcastEmail(emailReq); errB != nil {
			fmt.Println("send email error")
			return errB
		}

	}

	return nil
}

func (s *Service) SendBroadcastSms(smsData *pb.SmsBroadcast) error {
	fmt.Println("Here Sms---")
	var phoneNumbers []string

	//get Phone numbers for receiving userIds
	if err := s.Data.GetPhoneNumbersForUserIds(smsData.ReceivingUserIds, &phoneNumbers); err != nil {
		fmt.Println("Getting phone - ", err)
		return err
	}

	//send message
	var smsText string
	smsText = smsData.SmsBody

	go s.SendBroadcastSmsAsync(phoneNumbers, smsText)

	// if errB := s.Pub.MessagePublish(phoneNumbers, smsText); errB != nil {
	// 	fmt.Println("send message error")
	// 	return errB
	// }

	return nil
}

func (s *Service) SendBroadcastSmsAsync(phoneNumbers []string, smsText string) error {

	fmt.Println("-------------------------", phoneNumbers)
	if errB := s.Pub.MessagePublish(phoneNumbers, smsText); errB != nil {
		fmt.Println("send message error")
		return errB
	}

	return nil
}

func (s *Service) SendBroadcastNotification(notificationData *pb.NotificationBroadcast) error {

	fmt.Println("Here Notif---- ")
	app_condition := make(map[string]string)

	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"

	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	var action string
	var actionId int32

	if len(notificationData.Action) > 0 && notificationData.ActionId > 0 {
		action = notificationData.Action
		actionId = notificationData.ActionId
	} else {
		actionId = 1
		action = "special_notice"
	}

	nData := structs.FcmNotification{
		Title:         notificationData.NotificationTitle,
		Text:          notificationData.NotificationBody,
		UserIds:       notificationData.ReceivingUserIds,
		Id:            notificationData.Id,
		Action:        action,
		ConditionData: condition_data,
		ActionId:      actionId,
		CtaText:       "",
		SubCategory:   int32(notificationPB.NotificationSubCategories_NOTIFICATION_GENERAL),
	}

	if err := s.Pub.SendBroadcastNotification(&nData); err != nil {
		fmt.Println("Error in sending broadcast notification")
		return err
	}

	return nil
}

//can expose api later if needed -- currently exclusive api for this is not exposed
func (s *Service) GetSkillLevels(ctx context.Context, req *pb.SkillLevels, res *pb.SkillLevelsResponse) error {
	fmt.Println("skill level req data ---", req)

	var skillLevelData []models.SkillLevels
	if err := s.Data.GetSkillLevels(req, &skillLevelData); err != nil {
		fmt.Println("Error in fetching skill levels", err)
		return err
	}

	if len(skillLevelData) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "Skills fetched successfully!",
		}
		res.Status = status

		for _, element := range skillLevelData {
			res.SkillLevels = append(res.SkillLevels, element.Proto())
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch skills!",
		}
		res.Status = status

		error := &pb.Error{
			Code:        615,
			Description: "Error in getting skills. DB error!",
		}
		res.Error = append(res.Error, error)

	}

	return nil
}

// GetUserFromToken takes token and returns user data corresponding to the token
func (s *Service) GetUserFromToken(ctx context.Context, req *pb.Token, res *pb.TokenResponse) error {
	token := req.Token

	if len(token) == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: badRequest,
		}
		return nil
	}

	tokenData, err := s.fetchUserDataFromRequestToken(ctx, token)
	status := &pb.Status{
		Status: success,
	}

	if err != nil && err != redis.Nil {
		log.Printf("Error in getting data for token %s with error %v", token, err)
		res.Status = &pb.Status{
			Status:  failed,
			Message: badRequest,
		}
		return nil
	}
	res.UserId = tokenData.UserID
	if tokenData.UserID == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: badRequest,
		}
		var data models.UserAccessTokens
		if err := s.Data.GetAccessTokenData(ctx, token, &data); err != nil {
			log.Printf("Error getting user token details for token: %s err: %v", token, err)
			return err
		}
		if data.UserId == 0 {
			log.Printf("Error no active token present for token entered: %s, AppVersion: %s", token, req.AppVersion)
			return nil
		}
		userData := &models.User{
			UserID: data.UserId,
		}
		var userDB models.User
		if err := s.Data.Get(userData, &userDB); err != nil {
			log.Printf("Error getting user details of userID: %d, token:%s, err: %v", data.UserId, token, err)
			return err
		}
		if err := s.Data.Tokenize(token, &userDB); err != nil {
			log.Println("error storing token into redis: token %s, %v", token, err)
			return err
		}
		res.UserId = data.UserId
		log.Printf("Token time increased for user_id: %d, %s, version: %s", data.UserId, token, req.AppVersion)
	}
	res.Status = status
	return nil
}

func (s *Service) fetchUserDataFromRequestToken(ctx context.Context, token string) (models.User, error) {
	reqToken := config.Get("codeIndependent", "redisAPrepend").String("") + "_token_" + string(token)

	var (
		tokenData string
		user      models.User
	)

	if err := s.Data.Validate(reqToken, &tokenData); err != nil {
		log.Printf("Error in getting token data from redis for token %s", token)
		return user, err
	}

	if err := user.FromString(tokenData); err != nil {
		log.Printf("Error in parsing user data for token %s", token)
		return user, err
	}

	return user, nil
}

// SaveUserSportSkill handler saves user sport skill
func (s *Service) SaveUserSportSkill(ctx context.Context, req *pb.UserSportSkillRequest, res *pb.Ack) error {

	userDataFromToken, err := s.fetchUserDataFromRequestToken(ctx, req.Token)

	if err != nil {
		return err
	}

	if req.UserId != userDataFromToken.UserID {
		status := &pb.Status{
			Status:  failed,
			Message: notAuthorized,
		}
		res.Status = status

		return nil
	}

	userSkillData := &models.UserSportSkills{
		UserId:       req.UserId,
		SportId:      req.SportId,
		SkillLevelId: req.SkillLevelId,
		// SkillLevel:   req.SkillLevel,
		CreatedBy: req.CreatedBy,
	}

	//fetch skill level name from id
	var skillLevelData pb.SkillLevelsResponse
	//req
	skillLevels := &pb.SkillLevels{
		SkillLevelId: req.SkillLevelId,
	}
	if err := s.GetSkillLevels(context.TODO(), skillLevels, &skillLevelData); err != nil {
		fmt.Println("Could not fetch skill levels", err)
		return err
	}

	if skillLevelData.Status.Status == "success" {
		//add name to the request
		var skillLevelName string
		for _, element := range skillLevelData.SkillLevels {
			skillLevelName = element.SkillLevel
		}
		userSkillData.SkillLevel = skillLevelName
	} else {

		status := &pb.Status{
			Status:  "failure",
			Message: "Error getting skill name based on skill id.",
		}
		res.Status = status

		error := &pb.Error{
			Code:        612,
			Description: "DB error! Could not fetch skill name",
		}
		res.Error = append(res.Error, error)
	}

	if err := s.Data.SaveUserSportSkill(userSkillData); err != nil {
		fmt.Println("Error in saving user sport skill", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in saving user sport skill.",
		}
		res.Status = status

		error := &pb.Error{
			Code:        612,
			Description: "Error in saving user sport skill. DB error!",
		}
		res.Error = append(res.Error, error)
		return err
	}

	if userSkillData.UserSportSkillsId > 0 {

		dataResetSuccessful := false
		userData := &models.User{
			UserID: req.UserId,
		}

		//reset data in redis
		if err := s.ResetUserDataInRedis(ctx, userData, req.Token, &dataResetSuccessful); err != nil {
			fmt.Println("Error in reseting data in redis")
			return err
		}

		if dataResetSuccessful {
			status := &pb.Status{
				Status:  "success",
				Message: "Skill recorded successfully!",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "success",
				Message: "Could not reset in redis. Unknown error",
			}
			res.Status = status

			error := &pb.Error{
				Code:        615,
				Description: "Could not reset in redis. Unknown error",
			}
			res.Error = append(res.Error, error)
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Something went wrong! Unknown error!",
		}
		res.Status = status

		error := &pb.Error{
			Code:        613,
			Description: "Something went wrong! Unknown error!",
		}
		res.Error = append(res.Error, error)
	}
	return nil
}

func (s *Service) GetUserSkillLevel(ctx context.Context, req *pb.UserSportSkillRequest, res *pb.SkillResponse) error {
	fmt.Println("req ---------", req)

	var userSkill []models.UserSportSkills

	userSkillData := &models.UserSportSkills{
		UserId:  req.UserId,
		SportId: req.SportId,
	}

	if err := s.Data.GetUserSkillLevel(userSkillData, &userSkill); err != nil {
		fmt.Println("Error in getting user sport skill", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting user sport skill.",
		}
		res.Status = status

		error := &pb.Error{
			Code:        612,
			Description: "Error in getting user sport skill. DB error!",
		}
		res.Error = append(res.Error, error)
		return err
	}

	for _, element := range userSkill {
		res.OverallSkillLevel = append(res.OverallSkillLevel, element.Proto())
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Skill Levels fetched successfully",
	}
	res.Status = status

	return nil
}

func (s *Service) GetChildUsers(ctx context.Context, req *pb.UserInfo, res *pb.ChildUserResponse) error {

	var userId int32
	userId = req.UserId
	if userId == 0 && req.PhoneNumber == "" {
		status := &pb.Status{
			Status:  "failure",
			Message: "Bad request",
		}
		res.Status = status

		return errors.New("Bad request")
	}

	if userId == 0 {
		if err := s.Data.GetUserIdByPhone(ctx, req.PhoneNumber, &userId); err != nil {
			log.Printf("GetChildUsers: Error in getting user id for phone: %s, err: %v", req.PhoneNumber, err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch users.",
			}
			res.Status = status

			return err
		}
	}

	if userId == 0 { //could not determine user id by phone -- fail here
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get user id from phone. Cannot Proceed.",
		}
		res.Status = status

		return errors.New("Could not get user id from phone.")
	}

	//get all child users from user_child_account mapping

	var childUsers []models.UserChildAccountMapping
	userData := &models.UserChildAccountMapping{
		UserId: userId,
	}

	if err := s.Data.GetChildUsers(ctx, userData, &childUsers); err != nil {
		log.Printf("GetChildUsers: Error in getting child users for user id: %d, err: ", userId, err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch child users.",
		}
		res.Status = status

		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Child Users fetched successfully",
	}
	res.Status = status

	for _, element := range childUsers {
		if req.FetchUserAge {
			var userAge models.UserAgeRecord
			err := s.Data.GetUserAgeRecord(element.ChildUserId, &userAge)
			if err != nil {
				log.Printf("Error fetching user age record for user_id: %d, Error: %v", element.ChildUserId, err)
				return err
			}

			if userAge.Age != 0 {
				element.Age = userAge.Age
			} else {
				element.Age = 0
			}
		}
		res.AllConnectedUsers = append(res.AllConnectedUsers, element.Proto())
	}

	return nil
}

// GetActiveChildUsers returns those child users who have active subscriptions in city present in conrext
func (s *Service) GetActiveChildUsers(ctx context.Context, req *pb.UserRequest, res *pb.ActiveChildUserResponse) error {

	userId := req.UserId
	cityId := util.GetCityIDFromContext(ctx)

	if cityId > 0 && userId > 0 {
		//get all child users from user_child_account mapping
		var childUsers []models.UserChildAccountMapping
		userData := &models.UserChildAccountMapping{
			UserId: userId,
			CityId: cityId,
		}
		if err := s.Data.GetActiveChildUsers(ctx, userData, &childUsers); err != nil {
			log.Println("Func:GetChildUsersV2")
			log.Println("Error in getting child users", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch child users.",
			}
			res.Status = status
			return err
		}

		status := &pb.Status{
			Status:  success,
			Message: "Child Users fetched successfully",
		}
		res.Status = status

		for _, element := range childUsers {
			res.ActiveChildUsers = append(res.ActiveChildUsers, element.Proto())
		}
	} else {
		status := &pb.Status{
			Status:  failed,
			Message: "bad request",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetParentUser(ctx context.Context, req *pb.UserInfo, res *pb.ParentUserResponse) error {
	fmt.Println("req ---------", req)
	//get all child users from user_child_account mapping
	var parentUser []models.UserChildAccountMapping
	userData := &models.UserChildAccountMapping{
		ChildUserId: req.UserId,
	}

	if err := s.Data.GetParentUser(userData, &parentUser); err != nil {
		fmt.Println("Error in getting parent user", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch parent user.",
		}
		res.Status = status

		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Parent User fetched successfully",
	}
	res.Status = status

	for _, element := range parentUser {
		res.ParentUser = element.Proto()
		break
	}

	return nil
}

func (s *Service) GenerateAccessTokenV2(ctx context.Context, reqData *pb.CreateUser, req *pb.UserInfo, res *pb.GenerateAccessTokenResponse) error {
	fmt.Println("req ---------", req)

	userData := &models.User{
		UserID: req.UserId,
	}
	var userDB models.User

	if err := s.Data.Get(userData, &userDB); err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Error in getting User data")
		return err
	}

	if userDB.UserID <= 0 { //no user exists with this user id
		status := &pb.Status{
			Status:  failed,
			Message: "User does not exist!",
		}
		res.Status = status
	} else {

		token := CreateToken()
		if err := s.Data.Tokenize(token, &userDB); err != nil {
			log.Println("Error in storing user auth token")
			return err
		}
		appVersion, _ := util.GetAppVersionFromContext(ctx)
		appType, _ := util.GetClientIDFromContext(ctx)
		tokenDB := &models.UserAccessTokens{
			Token:       token,
			UserId:      userDB.UserID,
			LastUpdated: time.Now(),
			IsExpired:   false,
			AppVersion:  appVersion,
			AppType:     appType,
		}
		go s.Data.StoreToken(tokenDB)

		status := &pb.Status{
			Status:  success,
			Message: "Access token generated successfully!",
		}
		res.Status = status
		res.Token = token
	}

	return nil
}

func (s *Service) GenerateAccessToken(ctx context.Context, req *pb.UserInfo, res *pb.GenerateAccessTokenResponse) error {
	fmt.Println("req ---------", req)

	userData := &models.User{
		UserID: req.UserId,
	}
	var userDB models.User
	goAhead := 0 //this is needed as gorm return fatal error in case of empty record! when find in not a slice
	if err := s.Data.Get(userData, &userDB); err != nil {
		userQueryData := userDB.Proto()
		if userQueryData.UserId == 0 { //empty record
			goAhead = 1
		} else {
			return errors.New(fmt.Sprintf("error getting user by user: %v", err))
		}
	} else {
		goAhead = 1
	}

	if goAhead == 1 {
		if userDB.UserID <= 0 { //no user exists with this user id
			status := &pb.Status{
				Status:  "failure",
				Message: "User does not exist!",
			}
			res.Status = status
		} else {

			//get user sport skill levels here
			skillReq := &pb.UserSportSkillRequest{
				UserId: userDB.UserID,
			}
			var skillRes pb.SkillResponse //response stored here
			if err := s.GetUserSkillLevel(context.TODO(), skillReq, &skillRes); err != nil {
				fmt.Println("Could not fetch user skill levels! Unknown error!")
				fmt.Println("Proceeding without it...")
			}
			//add sports skills in user obj here
			if skillRes.Status.Status == "success" {
				userDB.OverallSkillLevel = skillRes.OverallSkillLevel
			}

			//get all connected users here
			//first get parent for user, then get all children
			uInfoParent := &pb.UserInfo{
				UserId: userDB.UserID,
			}
			var puResponse pb.ParentUserResponse //response stored here
			if err := s.GetParentUser(context.TODO(), uInfoParent, &puResponse); err != nil {
				fmt.Println("Could not fetch parent user! Unknown error!")
				return err
			}
			var uInfo pb.UserInfo
			if puResponse.Status.Status == "success" {
				uInfo.UserId = puResponse.ParentUser.ParentUserId
			}

			var uResponse pb.ChildUserResponse //response stored here
			if err := s.GetChildUsers(context.TODO(), &uInfo, &uResponse); err != nil {
				fmt.Println("Could not fetch all connected users! Unknown error!")
				return err
			}
			//add all connected users in user object here
			if uResponse.Status.Status == "success" {
				userDB.AllConnectedUsers = uResponse.AllConnectedUsers
			}

			// fmt.Println("Data tokenizing ----------------- ", userDB)
			//save user data in redis - generate token
			token := CreateToken()
			if err := s.Data.Tokenize(token, &userDB); err != nil {
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not generate token.",
				}
				res.Status = status

				return errors.New(fmt.Sprintf("error creating token and storing it into redis: %v", err))
			}

			status := &pb.Status{
				Status:  "success",
				Message: "Access token generated successfully!",
			}
			res.Status = status

			res.Token = token
		}
	}

	return nil
}

func (s *Service) GetBroadcastLogs(ctx context.Context, req *pb.Broadcast, res *pb.BroadcastResponse) error {
	fmt.Println("req -------------------- ", req)

	var broadLogs []models.SportsBroadcastLogs
	broadCond := &models.SportsBroadcastLogs{
		BroadcastId:  req.BroadcastId,
		SenderUserId: req.SenderUserId,
		UserId:       req.UserId,

		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Start:     req.Start,
		Count:     req.Count,
	}

	//fetch broadcast logs for user(join), else from log table (paginated)
	if err := s.Data.BroadcastLogsGet(&broadLogs, broadCond); err != nil {
		fmt.Println("Error in getting broadcast logs..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting broadcast logs..",
		}
		res.Status = status
		return err
	}

	if len(broadLogs) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "Broadcast logs fetched successfully!",
		}
		res.Status = status

		for _, ele := range broadLogs {
			ele.DisplayTitle = ele.NotificationTitle
			ele.DisplayBody = ele.NotificationBody
			res.Broadcast = append(res.Broadcast, ele.Proto())
		}
	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "No Broadcast logs found!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) MarkBroadcastReadStatus(ctx context.Context, req *pb.Broadcast, res *pb.Ack) error {
	fmt.Println("req --- ", req)

	if req.UserId > 0 && req.BroadcastId > 0 {
		markData := &models.SportsBroadcastLogs{
			BroadcastId: req.BroadcastId,
			UserId:      req.UserId,
			IsRead:      true,
		}
		updateSuccessful := false
		if err := s.Data.UpdateBroadcastStatus(markData, &updateSuccessful); err != nil {
			fmt.Println("Error in updating broadcast status to read... ", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not update read status for broadcast!",
			}
			res.Status = status
			return err
		}

		if updateSuccessful {
			status := &pb.Status{
				Status:  "success",
				Message: "Broadcast marked as read successfully!",
			}
			res.Status = status
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid request!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) FetchActiveSubscriptionCountForUser(ctx context.Context, userId int32, subscriptionCount *chan int32, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var subCount int32
	if err := s.Data.GetActiveSubscriptionCountForUser(ctx, userId, &subCount); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*subscriptionCount <- subCount

	return nil
}

func (s *Service) FetchSubscriptionCountForUser(ctx context.Context, userId int32, subscriptionCount *chan int32, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var subCount int32
	if err := s.Data.GetSubscriptionCountForUser(ctx, userId, &subCount); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*subscriptionCount <- subCount

	return nil
}

func (s *Service) CheckIfActivePremiumSubscriptionForUser(ctx context.Context, userId int32, premActivePlan *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var activePremPlan bool
	// CheckIfActivePremiumPlanUser
	if err := s.Data.CheckIfActivePremiumPlanUser(ctx, userId, &activePremPlan); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*premActivePlan <- activePremPlan

	return nil
}

func (s *Service) CheckIfInActivePremiumSubscriptionForUser(ctx context.Context, userId int32, premInactivePlan *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var inactivePremPlan bool
	// CheckIfActivePremiumPlanUser
	if err := s.Data.CheckIfInactivePremiumPlanUser(ctx, userId, &inactivePremPlan); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*premInactivePlan <- inactivePremPlan

	return nil
}

func (s *Service) CheckIfOngoingTrialPlanForUser(ctx context.Context, userId int32, activeTrialPlan *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var trialPlanActive bool
	// CheckIfActivePremiumPlanUser
	if err := s.Data.CheckIfActiveTrialPlan(ctx, userId, &trialPlanActive); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*activeTrialPlan <- trialPlanActive

	return nil
}

func (s *Service) CheckIfTrialExpiredForUser(ctx context.Context, expiredTrialPlan *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	var checkExpiredTrialResponse pb.CheckExpiredTrialResponse
	// CheckIfActivePremiumPlanUser
	if err := s.CheckIfTrialExpired(ctx, &pb.Empty{}, &checkExpiredTrialResponse); err != nil {
		fmt.Println("Error in checking if user has availed trial or not!", err)
		return err
	}

	*expiredTrialPlan <- checkExpiredTrialResponse.IsTrialExpired

	return nil
}

func (s *Service) CheckUserWithoutPlatinumPlan(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		log.Println("Cannot proceed without user id..")
		return nil
	}

	var EligibleUser bool
	// CheckIf user without latinum Plan
	if err := s.Data.CheckUserWithoutPlatinumPlan(ctx, userId, &EligibleUser); err != nil {
		log.Println("Error in checking if user have purchased platinum key!", err)
		return err
	}

	*isEligible <- EligibleUser

	return nil
}
func (s *Service) CheckActiveSealsPlan(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		log.Println("Cannot proceed without user id : ", userId)
		return nil
	}

	var EligibleUser bool
	// CheckIf user without seals Plan
	if err := s.Data.CheckActiveSealsPlan(ctx, userId, &EligibleUser); err != nil {
		log.Println("Error in checking if user have purchased seals active plan!", err)
		return err
	}

	*isEligible <- EligibleUser

	return nil
}

func (s *Service) CheckIfUserEligibleForRenewal(ctx context.Context, userId int32, isEligible *chan bool, planExpiryWindow int32, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var renewalEligibleUser bool
	// CheckIfActivePremiumPlanUser
	if err := s.Data.CheckIfUserEligibleForRenewalCampaign(ctx, userId, planExpiryWindow, &renewalEligibleUser); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*isEligible <- renewalEligibleUser

	return nil
}

func (s *Service) CheckIfUserEligibleForTrial(ctx context.Context, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from CheckIfUserEligibleForTrial panic- ", r)
		}
	}()

	var trialDetailsResponse pb.TrialDetailsResponse

	// Check trial status of user
	userID := util.GetUserIDFromContext(ctx)
	if err := s.GetTrialDetails(ctx, &pb.UserRequest{UserId: userID}, &trialDetailsResponse); err != nil {
		log.Println("Error in checking if user is eligible for trial or not!", err)
		return err
	}

	*isEligible <- trialDetailsResponse.TrialDetails.IsTrialActive

	return nil
}

func (s *Service) CheckIfUserNotPurchasedWithinLastNDays(ctx context.Context, userId int32, isEligible *chan bool, purchaseWindow int32, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var eligible bool
	// CheckIfActivePremiumPlanUser
	if err := s.Data.CheckIfUserNotPurchasedRecently(ctx, userId, purchaseWindow, &eligible); err != nil {
		fmt.Println("Error in checking if user has active subscription or not!", err)
		return err
	}

	*isEligible <- eligible

	return nil
}

func (s *Service) CheckIfUserNotPurchaseWithinMonthEvenOdd(ctx context.Context, userId int32, modRem int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var eligible bool

	if err := s.Data.CheckIfUserNotPurchaseWithinMonth(ctx, userId, modRem, &eligible); err != nil {
		fmt.Println("Error in checking whether user has made payment within last one month", err)
		return err
	}

	*isEligible <- eligible

	return nil
}

func (s *Service) IsUserOptin(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var eligible bool

	if err := s.Data.IsUserOptin(ctx, userId, &eligible); err != nil {
		fmt.Println("Error in checking whether user has opt-in the subscription", err)
		return err
	}

	*isEligible <- eligible

	return nil
}

func (s *Service) IsSwimmingKeyUser(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var eligible bool

	if err := s.Data.IsSwimmingKeyUser(ctx, userId, &eligible); err != nil {
		fmt.Println("Error in checking whether user is a swimming key users", err)
		return err
	}

	*isEligible <- eligible

	return nil
}

func (s *Service) CheckIfUserHaveUnExpiredScratchCards(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var eligible bool

	if err := s.Data.CheckIfUserHaveUnExpiredScratchCards(ctx, userId, &eligible); err != nil {
		fmt.Println("Error in checking whether user having unexpired scratch cards", err)
		return err
	}

	*isEligible <- eligible

	return nil
}

func (s *Service) IsSelectedPlatinumUser(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {

	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var eligible bool

	if err := s.Data.IsSelectedPlatinumUser(ctx, userId, &eligible); err != nil {
		fmt.Println("Error in checking selected platinum users", err)
		return err
	}

	*isEligible <- eligible

	return nil
}

func (s *Service) CheckIfUserExistInTestingTeam(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {
	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	eligible_users := []int32{23, 602225, 585731, 591281, 688673, 770943, 116117, 687802, 697642, 610466, 748165, 759558, 774723, 538363, 597983, 16, 34, 20553, 281369, 287220, 394386, 408679, 420977, 577524, 579503, 608413, 751693, 21, 561004, 582188, 599832, 598204, 609679, 780445, 5087, 79533, 463522, 538523, 686973, 780437, 779901, 764972, 772315, 780335, 885820}

	eligible := Contains(eligible_users, userId)
	*isEligible <- eligible

	return nil
}

func (s *Service) CheckIfUserExistInFitsoInternalTeam(ctx context.Context, userId int32, isEligible *chan bool, wg *sync.WaitGroup) error {
	defer wg.Done()

	if userId <= 0 {
		fmt.Println("Cannot proceed without user id..")
		return nil
	}

	var res pb.IsFitsoInternalUserRes
	err := s.IsFitsoInternalUser(ctx, &pb.IsFitsoInternalUserReq{
		UserId: userId,
	}, &res)
	if err != nil {
		log.Println("CheckIfUserExistInFitsoInternalTeam: error in checking if user is fitso internal user: %d", userId)
	}
	*isEligible <- res.IsFitsoInternalUser

	return nil
}

func (s *Service) IsFitsoInternalUser(ctx context.Context, req *pb.IsFitsoInternalUserReq, res *pb.IsFitsoInternalUserRes) error {
	fitso_internal_users := []int32{116117, 687802, 281369, 287220, 443874, 463522, 492093, 492094, 515776, 533339, 538363, 538370, 563936, 570418, 571422, 576270, 582188, 583626, 585452, 599832, 609477, 610687, 612420, 635003, 638305, 638312, 697642, 720435, 762781, 68128, 79533, 544849, 544866, 549779, 555167, 567621, 588402, 598004, 599250, 608058, 608413, 609474, 617285, 631706, 633049, 635121, 640638, 662011, 689540, 696817, 736715, 749983, 751693, 766955, 767118, 769145, 770013, 771309, 772275, 772863, 773234, 773314, 773334, 773678, 774107, 775644, 775904, 776045, 776145, 776150, 776253, 777286, 777540, 778390, 778705, 779578, 779635, 779901, 779957, 780068, 780426, 780445, 780595, 780925, 782042, 782102, 787640, 789447, 797843, 799249, 799310, 803867, 814024, 822418, 834308, 853175, 862329, 875007, 924202, 924304, 962279, 962310, 962311, 962343, 962484, 1019200, 1048230, 1049847, 1060170, 1065838, 1089471, 1114391, 1155811, 3068145, 3913732, 10936019, 12142230, 12541293, 12780195, 12813435}
	res.IsFitsoInternalUser = Contains(fitso_internal_users, req.UserId)
	return nil
}

func (s *Service) GetSuggestionsForAcademy(ctx context.Context, req *pb.Empty, res *pb.SuggestionResponse) error {
	userId := util.GetUserIDFromContext(ctx)
	zoneId := util.GetZoneIDFromContext(ctx)
	cityId := util.GetCityIDFromContext(ctx)

	var userCategoryIds []int32

	if err := s.GetUserCategoriesDetails(ctx, userId, ACADEMY_PRODUCT_CATEGORY_ID, cityId, USER_CATEGORY_FOR_SUGGESTION, &userCategoryIds); err != nil {
		log.Printf("GetSuggestionsForAcademy: Error in getting user categories details for user id: %d, error: %v", userId, err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting user categories!",
		}
		res.Status = status
		return nil
	}

	if len(userCategoryIds) <= 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: "Cannot proceed! User not present in any active category!",
		}
		return nil
	}
	sugCond := &models.SportSuggestion{
		ApplicableCategories: userCategoryIds,
		ZoneId:               zoneId,
		CityIds:              strconv.Itoa(int(cityId)),
	}
	sugData, err := s.Data.GetSuggestionsV2(ctx, sugCond)
	if err != nil {
		return fmt.Errorf("Unable to fetch academy suggestion for userId: %d", userId)
	}

	for _, element := range sugData {
		if len(element.CtaText) > 0 {
			element.Url = element.CtaText
		}
		if element.SuggestionId <= 76 {
			continue
		}
		res.Suggestion = append(res.Suggestion, element.Proto())
	}

	res.Status = &pb.Status{
		Status:  success,
		Message: "Suggestions fetched successfully!",
	}
	return nil
}

func (s *Service) GetSuggestionsForMasterkey(ctx context.Context, req *pb.Suggestion, res *pb.SuggestionResponse) error {
	var userCategoryIds []int32
	var zoneIds []int32

	if featuresupport.SupportsV2(ctx) {
		req.UserId = util.GetUserIDFromContext(ctx)
		zoneIds = append(zoneIds, util.GetZoneIDFromContext(ctx))
	}
	cityId := util.GetCityIDFromContext(ctx)

	//get user categories
	if err := s.GetUserCategoriesDetails(ctx, req.UserId, MASTERKEY_PRODUCT_CATEGORY_ID, cityId, USER_CATEGORY_FOR_SUGGESTION, &userCategoryIds); err != nil {
		log.Printf("GetSuggestionsForMasterkey: Error in getting user categories details for user id: %d, error: %v", req.UserId, err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting user category!",
		}
		res.Status = status
		return nil
	}
	if len(userCategoryIds) <= 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "Cannot proceed! User not present in any active category!",
		}
		res.Status = status

		return nil
	}

	var sugData []models.SportSuggestion
	sugCond := &models.SportSuggestion{
		SuggestionId:         req.SuggestionId,
		SuggestionType:       req.SuggestionType,
		Title:                req.Title,
		ActionId:             req.ActionId,
		ApplicableCategories: userCategoryIds,
		CityIds:              strconv.Itoa(int(cityId)),
		TabIds:               req.TabIds,
		ZoneIds:              zoneIds,
	}

	if err := s.Data.ApplicableSuggestionGet(ctx, &sugData, sugCond); err != nil {
		fmt.Println("Error in getting suggestions... Db err")
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch suggestions!",
		}
		res.Status = status
		return err
	}

	var getPremiumProduct bool = false
	for _, element := range sugData {
		if element.SuggestionType == 4 || element.SuggestionType == 11 {
			getPremiumProduct = true
		}
	}

	var latestPremiumProduct models.Product
	if getPremiumProduct {
		if err := s.Data.GetLatestPremiumProductForUser(ctx, req.UserId, &latestPremiumProduct); err != nil {
			fmt.Println("Error in getting latest premium product..", err)
			fmt.Println("Moving ahead without throwing error!")
			return err
		}
	}

	for _, element := range sugData {

		//added conditions -- remove later
		var userEncodeVal string
		encodeData(strconv.Itoa(int(req.UserId)), &userEncodeVal)
		if len(element.CtaText) > 0 {
			if !strings.Contains(element.CtaText, "city_ids=") {
				if req.CityId > 0 && len(element.CtaText) > 0 {
					if strings.Contains(element.CtaText, "?") {
						element.CtaText = element.CtaText + "&city_ids=" + strconv.Itoa(int(req.CityId))
					} else {
						element.CtaText = element.CtaText + "?city_ids=" + strconv.Itoa(int(req.CityId))
					}
				}
			}
			if strings.Contains(element.CtaText, "?") {
				element.CtaText = element.CtaText + "&user_encode_val=" + userEncodeVal + "&user_id=" + strconv.Itoa(int(req.UserId))
			} else {
				element.CtaText = element.CtaText + "?user_encode_val=" + userEncodeVal + "&user_id=" + strconv.Itoa(int(req.UserId))
			}

			if element.SuggestionType == 4 {

				if strings.Contains(element.CtaText, "?") {
					element.CtaText = element.CtaText + "&user_id=" + strconv.Itoa(int(req.UserId))
				} else {
					element.CtaText = element.CtaText + "?user_id=" + strconv.Itoa(int(req.UserId))
				}

				//add applicable utms
				if element.SuggestionId == 5 {
					element.CtaText = element.CtaText + "&utm_campaign=sports_app_refer_suggestion&utm_source=" + strconv.Itoa(int(req.UserId))
				} else if element.SuggestionId == 6 {
					element.CtaText = element.CtaText + "&utm_campaign=sports_app_refer_trial_suggestion&utm_source=" + strconv.Itoa(int(req.UserId))
				}

				if len(latestPremiumProduct.ProductDescription) > 0 {
					element.CtaText = element.CtaText + "&utm_medium=" + strings.Join((strings.Split(latestPremiumProduct.ProductDescription, " ")), "-")
				}
			}

			element.Url = element.CtaText
		}

		if element.SuggestionType == 11 {

			endTimestamp, _ := ptypes.TimestampProto(latestPremiumProduct.SubscriptionEndDate)
			element.OfferEndTime = endTimestamp.GetSeconds()
		}

		if element.SuggestionId == 109 && req.UserId < 818210 {
			continue
		}

		if featuresupport.SupportsV2(ctx) && element.SuggestionId > 76 {
			res.Suggestion = append(res.Suggestion, element.Proto())
		} else if !featuresupport.SupportsV2(ctx) && element.SuggestionId <= 76 {
			res.Suggestion = append(res.Suggestion, element.Proto())
		}
	}

	var reverseSuggestions []*pb.Suggestion
	for i := (len(res.Suggestion) - 1); i >= 0; i-- {
		reverseSuggestions = append(reverseSuggestions, res.Suggestion[i])
	}
	res.Suggestion = reverseSuggestions

	// sort suggestions by priority order-- desc
	sort.SliceStable(res.Suggestion, func(i, j int) bool {
		return res.Suggestion[i].PriorityOrder > res.Suggestion[j].PriorityOrder
	})

	status := &pb.Status{
		Status:  "success",
		Message: "Suggestions fetched successfully!",
	}
	res.Status = status

	height := config.Get("static", "suggestionImageAspectRatioHeight").Int(1)
	width := config.Get("static", "suggestionImageAspectRatioWidth").Int(2)

	res.AspectRatioHeight = int32(height)
	res.AspectRatioWidth = int32(width)

	return nil
}

func (s *Service) GetSportSuggestions(ctx context.Context, req *pb.Suggestion, res *pb.SuggestionResponse) error {
	fmt.Println("req --- ", req)
	var suggestions []models.SportSuggestion

	getSportsSuggestion := &models.SportSuggestion{
		CityIds:     req.CityIds,
		CategoryIds: req.CategoryIds,
		IsActive:    req.IsActive,
		ZoneIdsStr:  req.ZoneIdsStr,
	}

	if err := s.Data.GetSportSuggestions(&suggestions, getSportsSuggestion); err != nil {
		return errors.New(fmt.Sprintf("error creating suggestion: %v", err))
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Fetched successfully!",
	}
	suggestionCitiesMap := make(map[int32][]models.SportSuggestion)
	for _, ele := range suggestions {
		suggestionCitiesMap[ele.SuggestionId] = append(suggestionCitiesMap[ele.SuggestionId], ele)
	}

	for _, element := range suggestionCitiesMap {

		suggData := &models.SportSuggestion{
			SuggestionId:        element[0].SuggestionId,
			Title:               element[0].Title,
			Text:                element[0].Text,
			SuggestionImage:     element[0].SuggestionImage,
			ActionId:            element[0].ActionId,
			ActionName:          element[0].ActionName,
			OfferEndTime:        element[0].OfferEndTime,
			SuggestionStartTime: element[0].SuggestionStartTime,
			SuggestionEndTime:   element[0].SuggestionEndTime,
			CtaId:               element[0].CtaId,
			CtaText:             element[0].CtaText,
			CtaPostbackParams:   element[0].CtaPostbackParams,
			AllCitiesFlag:       element[0].AllCitiesFlag,
			AllTabFlag:          element[0].AllTabFlag,
			PriorityOrder:       element[0].PriorityOrder,
			VideoUrl:            element[0].VideoUrl,
			VideoId:             element[0].VideoId,
		}

		sCitiesMap := make(map[int32]models.SportSuggestion)
		sCategoriesMap := make(map[int32]models.SportSuggestion)
		sTabsMap := make(map[int32]models.SportSuggestion)
		sZonesMap := make(map[int32]models.SportSuggestion)
		for _, sds := range element {
			sCitiesMap[sds.CityId] = sds
			sCategoriesMap[sds.SportsUserCategoryId] = sds
			sTabsMap[sds.TabId] = sds
			sZonesMap[sds.ZoneId] = sds
		}

		for _, sds := range sCitiesMap {
			cityData := &models.City{
				CityId:   sds.CityId,
				CityName: sds.CityName,
			}
			if sds.CityId > 0 {
				suggData.City = append(suggData.City, cityData.Proto())
			}
		}

		for _, sds := range sZonesMap {
			if sds.ZoneId == 0 {
				continue
			}
			suggData.ZoneIds = append(suggData.ZoneIds, sds.ZoneId)
		}

		for _, suds := range sCategoriesMap {
			categoryData := &models.SportsUserCategory{
				SportsUserCategoryId: suds.SportsUserCategoryId,
				CategoryName:         suds.CategoryName,
			}
			if suds.SportsUserCategoryId > 0 {
				suggData.SportsUserCategory = append(suggData.SportsUserCategory, categoryData.Proto())
			}
		}
		for _, sds := range sTabsMap {
			tabData := &pb.Tab{
				TabId:       sds.TabId,
				Description: tabIdToTabName[sds.TabId],
			}
			if tabData.TabId > 0 {
				suggData.Tabs = append(suggData.Tabs, tabData)
			}
		}

		res.Suggestion = append(res.Suggestion, suggData.Proto())
	}

	sportNotificatonActions, err := s.Data.GetSportNotificationActions(ctx)
	if err != nil {
		return err
	}
	for _, action := range sportNotificatonActions {
		res.SuggestionActions = append(res.SuggestionActions, action.Proto())
	}

	// sort suggestions by priority order -- desc
	sort.SliceStable(res.Suggestion, func(i, j int) bool {
		return res.Suggestion[i].PriorityOrder > res.Suggestion[j].PriorityOrder
	})

	res.Status = status

	return nil
}

func (s *Service) CreateOrUpdateSuggestion(ctx context.Context, req *pb.Suggestion, res *pb.SuggestionResponse) error {
	fmt.Println("req --- ", req)

	reqData := &pb.Suggestion{
		ActionId:          req.ActionId,
		CtaText:           req.CtaText,
		CtaPostbackParams: req.CtaPostbackParams,
	}
	// validating deeplink url
	err := s.ValidateSuggestionDeeplinkCtaText(ctx, reqData, &pb.Ack{})
	if err != nil {
		status := &pb.Status{
			Status:  failed,
			Message: err.Error(),
		}
		res.Status = status
		return nil
	}
	//validating deeplink postback params
	err = s.ValidateSuggestionDeeplinkCtaPostbackParams(ctx, reqData, &pb.Ack{})
	if err != nil {
		status := &pb.Status{
			Status:  failed,
			Message: err.Error(),
		}
		res.Status = status
		return nil
	}

	createSuggestion := &models.SportSuggestion{
		SuggestionId:        req.SuggestionId,
		SuggestionType:      req.SuggestionType,
		Title:               req.Title,
		Text:                req.Text,
		SuggestionImage:     req.SuggestionImage,
		ActionId:            req.ActionId,
		OfferEndTime:        req.OfferEndTime,
		SuggestionStartTime: req.SuggestionStartTime,
		SuggestionEndTime:   req.SuggestionEndTime,
		CtaId:               req.Id,
		CtaText:             req.CtaText,
		CtaPostbackParams:   req.CtaPostbackParams,
		AllCitiesFlag:       req.AllCitiesFlag,
		PriorityOrder:       req.PriorityOrder,
		AllTabFlag:          req.AllTabFlag,
	}

	if req.VideoId > 0 {
		createSuggestion.VideoId = req.VideoId
	}

	if err := s.Data.CreateOrUpdateSuggestion(createSuggestion); err != nil {
		return errors.New(fmt.Sprintf("error creating suggestion: %v", err))
	}

	categoryIds := GetIntegerArrayFromCommaSeparatedString(req.CategoryIds)
	categoryIds = removeDuplicateValues(categoryIds)

	if err := s.Data.CreateSuggestionUserCategoryMapping(createSuggestion.SuggestionId, categoryIds); err != nil {
		return errors.New(fmt.Sprintf("error creating suggestion user_category mapping: %v", err))
	}

	zoneIds := removeDuplicateValues(req.ZoneIds)

	if len(zoneIds) > 0 {
		if err := s.Data.CreateSuggestionZoneMapping(createSuggestion.SuggestionId, zoneIds); err != nil {
			return errors.New(fmt.Sprintf("error creating suggestion city mapping: %v", err))
		}
	}

	cityIds := GetIntegerArrayFromCommaSeparatedString(req.CityIds)
	cityIds = removeDuplicateValues(cityIds)

	if len(cityIds) > 0 {
		if err := s.Data.CreateSuggestionCityMapping(createSuggestion.SuggestionId, cityIds); err != nil {
			return errors.New(fmt.Sprintf("error creating suggestion city mapping: %v", err))
		}
	}

	tabIds := GetIntegerArrayFromCommaSeparatedString(req.TabIds)
	if err1 := s.Data.CreateSuggestionTabMapping(createSuggestion.SuggestionId, tabIds); err1 != nil {
		return errors.New(fmt.Sprintf("error creating suggestion city mapping: %v", err1))
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}
	res.Status = status

	return nil
}

func (s *Service) ValidateSuggestionDeeplinkCtaText(ctx context.Context, req *pb.Suggestion, res *pb.Ack) error {
	actionId := req.ActionId
	ctaText := req.CtaText
	if actionId == util.ZERO {
		return errors.New("Please choose a valid action")
	} else if (actionId == util.SPECIAL_NOTICE || actionId == util.INFO_ONLY) && len(ctaText) != 0 {
		return errors.New("CtaText must be empty for special_notice and info_only action")
	} else if (actionId == util.IN_APP_WEB_LINK || actionId == util.WEB_LINK) && len(ctaText) == 0 {
		return errors.New("CtaText can not be empty for in_app_web and web action")
	} else if actionId == util.BOOKING_LINK && !strings.HasPrefix(ctaText, util.GetBookingDeeplink()) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' for booking action", util.GetBookingDeeplink()))
	} else if (actionId == util.UPCOMING_BOOKINGS_LINK || actionId == util.PAST_BOOKINGS_LINK || actionId == util.MY_BOOKINGS_LINK) && !strings.HasPrefix(ctaText, util.GetYourBookingsDeeplink()) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' for upcoming, past and my bookings", util.GetYourMembershipsDeeplink(MASTERKEY_PRODUCT_CATEGORY_ID)))
	} else if actionId == util.MY_MEMBERSHIPS_LINK && !strings.HasPrefix(ctaText, util.GetYourMembershipsDeeplink(MASTERKEY_PRODUCT_CATEGORY_ID)) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' for upcoming, past and my bookings", util.GetYourMembershipsDeeplink(MASTERKEY_PRODUCT_CATEGORY_ID)))
	} else if actionId == util.HOME_LINK && !strings.HasPrefix(ctaText, util.GetHomeDeeplink()) {
		return errors.New(fmt.Sprintf("CtaText must start with  for home", util.GetHomeDeeplink()))
	} else if actionId == util.SPORT_LINK {
		if !strings.HasPrefix(ctaText, util.GetSportDeeplink()) {
			return errors.New(fmt.Sprintf("CtaText must start with '%s' for sport", util.GetSportDeeplink()))
		} else {
			val := ctaText[14:len(ctaText)]
			val = strings.Split(strings.Split(val, "/")[0], "?")[0]
			intVar, err := strconv.Atoi(val)
			if err != nil || intVar <= 0 {
				return errors.New(fmt.Sprintf("CtaText must contain valid sport_id in '%s{sport_id}' for sport", util.GetSportDeeplink()))
			}
		}
	} else if actionId == util.FACILITY_LINK {
		if !strings.HasPrefix(ctaText, util.GetFacilityDeeplink()) {
			return errors.New(fmt.Sprintf("CtaText must start with '%s' for facility", util.GetFacilityDeeplink()))
		} else {
			val := ctaText[17:len(ctaText)]
			val = strings.Split(strings.Split(val, "/")[0], "?")[0]
			intVar, err := strconv.Atoi(val)
			if err != nil || intVar <= 0 {
				return errors.New(fmt.Sprintf("CtaText must contain valid facility_id in '%s{facility_id}' for facility", util.GetFacilityDeeplink()))
			}
		}
	} else if actionId == util.PURCHASE_LINK && !strings.HasPrefix(ctaText, util.GetPurchaseDeeplink()) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' for purchase", util.GetPurchaseDeeplink()))
	} else if actionId == util.RENEWAL_LINK && !strings.HasPrefix(ctaText, util.GetRenewalDeeplink()) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' for renew", util.GetRenewalDeeplink()))
	} else if actionId == util.TRIAL_LINK && !strings.HasPrefix(ctaText, util.GetTrialDeeplink()) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' for trial", util.GetTrialDeeplink()))
	} else if actionId == util.GENERAL_DEEPLINK && !(strings.HasPrefix(ctaText, util.APP_URI) || strings.HasPrefix(ctaText, util.TEL_URI)) {
		return errors.New(fmt.Sprintf("CtaText must start with '%s' or '%s' for deeplink", util.APP_URI, util.TEL_URI))
	}
	return nil
}

func (s *Service) ValidateSuggestionDeeplinkCtaPostbackParams(ctx context.Context, req *pb.Suggestion, res *pb.Ack) error {
	actionId := req.ActionId
	ctaPostbackParams := req.CtaPostbackParams
	ctaPostbackParamsStringMap := GetPostBackParamsMap(ctaPostbackParams)
	if (actionId == util.SPECIAL_NOTICE || actionId == util.INFO_ONLY || actionId == util.IN_APP_WEB_LINK || actionId == util.WEB_LINK) && len(ctaPostbackParams) != 0 {
		return errors.New("CtaPostbackParams must be empty for special_notice and info_only action")
	} else if actionId == util.BOOKING_LINK {
		if len(ctaPostbackParams) <= 0 {
			return errors.New("CtaPostbackParams can not be empty for booking action")
		}
		if val, ok := ctaPostbackParamsStringMap["fs_id"]; ok && len(val) > 0 {
			intVar, err := strconv.Atoi(val)
			if err != nil || intVar <= 0 {
				return errors.New("Error in converting fs_id for booking action")
			}
		} else {
			return errors.New("CtaPostbackParams must contain fs_id greater than 0 for booking action")
		}
	} else if actionId == util.FACILITY_LINK && len(ctaPostbackParams) > 0 {
		if val, ok := ctaPostbackParamsStringMap["sport_id"]; ok {
			intVar, err := strconv.Atoi(val)
			if err != nil || intVar <= 0 {
				return errors.New("Error in converting sport_id for facility action")
			}
		}
	}
	return nil
}

func (s *Service) GetUserCategories(ctx context.Context, req *pb.Empty, res *pb.UserCategoryResponse) error {

	var categories []models.SportsUserCategory

	cond := &models.SportsUserCategory{}

	if err := s.Data.UserCategoriesGet(ctx, &categories, cond); err != nil {
		return errors.New(fmt.Sprintf("error creating suggestion: %v", err))
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Sports user category fetched successfully!",
	}
	for _, ele := range categories {
		res.SportsUserCategory = append(res.SportsUserCategory, ele.Proto())
	}

	res.Status = status

	return nil
}

func (s *Service) GetNotificationCategories(ctx context.Context, req *pb.Empty, res *pb.NotificationUserCategoryResponse) error {

	var categories []models.NotificationUserCategories

	if err := s.Data.NotificationCategoriesGet(&categories); err != nil {
		fmt.Println("Error in  getting Notification Categories data..", err)
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Notification categories fetched successfully!",
	}
	for _, ele := range categories {
		res.NotificationUserCategory = append(res.NotificationUserCategory, ele.Proto())
	}

	res.Status = status

	return nil
}

func (s *Service) AddEditNotificationCategories(ctx context.Context, req *pb.NotificationUserCategory, res *pb.Ack) error {

	reqData := &models.NotificationUserCategories{
		UserCategoryId: req.UserCategoryId,
		Description:    req.Description,
		SqlQuery:       req.SqlQuery,
		CreatedBy:      req.CreatedBy,
	}

	if err := s.Data.AddEditNotificationCategories(reqData); err != nil {
		fmt.Println("Error in inserting notification category..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in inserting notification category..",
		}
		res.Status = status
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}

	res.Status = status
	return nil

}

// func (s *Service) GetImageDimensions(imageUrl string, img_width *float32, img_height *float32) error {
// 	fmt.Println("req img--", imageUrl)

// 	response, e := http.Get(imageUrl)
// 	if e != nil {
// 		log.Fatal(e)
// 	}
// 	defer response.Body.Close()

// 	//open a file for writing
// 	file, err := os.Create("/tmp/asdf.jpg")
// 	if err != nil {
// 		log.Fatal(err)
// 	}
// 	defer file.Close()

// 	// Use io.Copy to just dump the response body to the file. This supports huge files
// 	_, err = io.Copy(file, response.Body)
// 	if err != nil {
// 		log.Fatal(err)
// 	}

// 	return nil
// }

func (s *Service) RecordAttendanceDumpV2(ctx context.Context, req *pb.AttendanceDumpData, res *pb.Ack) error {

	fmt.Println("req --- ", req)

	markData := &models.AttendanceDump{
		EntryId:          req.EntryId,
		Medium:           req.Medium,
		PeopleId:         req.PeopleId,
		CenterId:         req.CenterId,
		Name:             req.Name,
		Phone:            req.Phone,
		Email:            req.Email,
		DesignatedKey:    req.DesignatedKey,
		UserId:           req.UserId,
		CheckinPhotoUrl:  req.CheckinPhotoUrl,
		CheckoutPhotoUrl: req.CheckoutPhotoUrl,
		VisitPurpose:     req.PurposeOfVisit,
		SportName:        req.SportName,
	}
	if req.CheckinTime > 0 {
		checkinTime, _ := GetLocalDateTime(time.Unix(req.CheckinTime, 0))
		markData.CheckinTime = checkinTime
	}
	if req.CheckoutTime > 0 {
		checkoutTime, _ := GetLocalDateTime(time.Unix(req.CheckoutTime, 0))
		markData.CheckoutTime = checkoutTime
	}

	if err := s.Data.InsertAttendanceDump(ctx, markData); err != nil {

		fmt.Println("Error in inserting attendance dump to db... ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not insert attendance dump!",
		}
		res.Status = status
		return err
	}

	if markData.AttendanceDumpId > 0 { //new dump entry created
		//get user_id by people_id from the attendance_user_people_mapping table
		go s.ProcessAttendanceV2(ctx, markData)
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}
	res.Status = status

	return nil
}

func (s *Service) ProcessAttendanceV2(ctx context.Context, req *models.AttendanceDump) error {
	log.Println("dump attendance data.. ", req)
	/*
		1. Get user_id from people_id
		2. Get facility_id from center_id
		3. Determine all possible fs_ids
		4. Determine slot_id from checkin_time
		5. Get bookings for user based on above condition
		6. If booking exists, mark attendance
		7. Create processed record anyway after that
	*/

	//determine user_id
	var userId int32

	if req.UserId > 0 {
		userId = req.UserId
	} else {
		if err := s.Data.GetUserIdForPeopleId(ctx, req.PeopleId, &userId); err != nil {
			log.Println("Error in getting user id from people id..", err)
			return err
		}
		log.Println("Uid from attendance_user_people_mapping table... ", userId)
	}
	if userId <= 0 { //user_id not determined from people id
		//try to get uid from name and phone

		//get all connected users here
		uInfo := &pb.UserInfo{
			PhoneNumber: req.Phone,
		}
		var uResponse pb.ChildUserResponse //response stored here
		if err := s.GetChildUsers(context.TODO(), uInfo, &uResponse); err != nil {
			log.Println("Could not fetch all connected users")
			//return err
		}
		//log.Println("Connected Users ----", uResponse)

		if uResponse.Status.Status == "success" {

			if len(uResponse.AllConnectedUsers) == 1 {
				userId = uResponse.AllConnectedUsers[0].UserId
			} else {

				formattedUserName := strings.ToLower(strings.Join(strings.Fields(req.Name), ""))
				var isMatchUserId int32
				for _, user := range uResponse.AllConnectedUsers {
					formattedDbUserName := strings.ToLower(strings.Join(strings.Fields(user.Name), ""))
					//	log.Println("Compare Users ----", formattedUserName)
					//	log.Println("Compare Users ----", formattedDbUserName)

					var reqFirstName string
					var dbFirstName string
					if err := GetFirstNameFromName(strings.ToLower(req.Name), &reqFirstName); err != nil {
						log.Println("Error in creating First Name from Name!")
						return err
					}

					if err := GetFirstNameFromName(strings.ToLower(user.Name), &dbFirstName); err != nil {
						log.Println("Error in creating First Name from Name!")
						return err
					}

					reversedName := []string{}
					requestName := strings.Fields(strings.ToLower(req.Name))
					for i := range requestName {
						n := requestName[len(requestName)-1-i]
						reversedName = append(reversedName, n)
					}

					if formattedUserName == formattedDbUserName || reqFirstName == dbFirstName || strings.Join(reversedName, "") == formattedDbUserName {
						//	userId = user.UserId
						isMatchUserId = user.UserId
						if user.ActiveSubscriptionCount > 0 {
							break
						}
					}

				}
				userId = isMatchUserId
			}

			// if err := s.Data.GetUserIdByNameAndPhone(req,connectedNames,&userId); err != nil {
			// 	fmt.Println("Error in getting user id from name and phone..", err)
			// 	return err
			// }
			if userId <= 0 {
				userId = 0
				// fmt.Println("User id could not be determined..")
				// return errors.New("Could not get User Id from people id or name and phone.. Cannot proceed..")
			}
		} else {
			userId = 0
		}

	}

	log.Println("Final uid ---- ", userId)

	//determine facility_id
	var facilities []structs.Facility
	var facility structs.Facility

	if err := s.Data.GetFacilityIdFromCenterId(ctx, req.CenterId, &facilities); err != nil {
		log.Println("Error in getting facility id from center id..", err)
		return err
	}
	//fmt.Println("Fid from attendance_user_people_mapping table... ", facility.FacilityId)
	if len(facilities) == 0 { //facility_id not determined from center_id
		return errors.New("Error in getting Facility Id from center id.. Cannot proceed..")
	} else {
		//determine possible fs_ids where the user could have booked
		var fsIds []structs.FsIdObj
		var facilityIds []int32

		for _, fac := range facilities {
			facilityIds = append(facilityIds, fac.FacilityId)
		}

		if err := s.Data.GetFsIdsForFacilityId(ctx, &fsIds, facilityIds); err != nil {
			log.Println("Error in getting fs_ids for facility id..", err)
			return err
		}

		//determine slot_id by checkin time
		var slotId int32

		var bRefNo string
		var fsIdVal int32
		var isLateCommer bool
		var isTrial bool
		var bookingId int32
		if userId > 0 && len(fsIds) > 0 { //check for booking

			var bookings []*bookingPB.Booking
			if err := s.GetActiveBookingsForUser(ctx, userId, fsIds, nil, req.CheckinTime, &bookings); err != nil {
				log.Println("Could not fetch bookings for user...", err)
				return err
			}

			var validBookingObj *bookingPB.Booking
			for _, ele := range bookings {
				diff := req.CheckinTime.Unix() - ele.BookingTime.Seconds
				//fmt.Println("-------", diff)
				if diff >= -30*60 && diff <= 30*60 && ele.BookingCancelled != true && ele.AttendanceFlag != true {
					bRefNo = ele.BookingReferenceNumber
					bookingId = ele.BookingId
					fsIdVal = ele.FsId
					validBookingObj = ele
					slotId = ele.SlotId
					isTrial = ele.IsTrial
					if diff >= 16*60 {
						isLateCommer = true
					}
					break
				}
			}
			if validBookingObj != nil {
				for _, fac := range facilities {
					if validBookingObj.Facility.FacilityId == fac.FacilityId {
						facility = fac
						break
					}
				}
			}
			if len(bRefNo) > 0 {

				isNoshowApplicable := isLateCommer && req.Medium == 1 && !isTrial && facility.NoShowPenaltyOn
				if len(bRefNo) == 9 { //mark attendance in users_seals_bookings
					var updateSuccessful bool

					if err := s.Data.MarkAttendanceInSeals(bRefNo, &updateSuccessful, 1); err != nil {
						log.Println("Could not mark attendance in seals for - ", bRefNo)
						return err
					}
					if updateSuccessful {
						log.Println("Attendance marked successfully in seals..")

						//send welcome notification
						fcmStruct := structs.FcmNotificationMessage{
							UserId:      validBookingObj.UserId,
							Title:       "Welcome to " + validBookingObj.Facility.DisplayName + " 😁",
							Text:        "Hope you have a great " + validBookingObj.Sport.SportName + " session!",
							Action:      "upcoming_bookings",
							CtaText:     validBookingObj.BookingReferenceNumber,
							Id:          validBookingObj.BookingId,
							ExpiryDate:  time.Now().AddDate(0, 0, 3).Unix(),
							SubCategory: int32(notificationPB.NotificationSubCategories_ATTENDANCE_MARKED),
						}
						go s.SendNotificationToUsers(fcmStruct)
						if req.Medium == 1 {
							go s.MarkAttendaceOfConsecutiveSlotsV2(ctx, userId, fsIds, *validBookingObj.BookingTime, bookings, facility.FacilityId, req)
						}
					}
				} else { //mark attendance in all_bookings
					var updateSuccessful bool
					if err := s.Data.MarkAttendanceInSports(ctx, bookingId, &updateSuccessful, 1, isNoshowApplicable); err != nil {
						log.Println("Could not mark attendance in sports for - ", bookingId)
						return err
					}
					if updateSuccessful {
						log.Println("Attendance marked successfully in sports..")

						//send welcome notification
						fcmStruct := structs.FcmNotificationMessage{
							UserId:      validBookingObj.UserId,
							Title:       "Welcome to " + validBookingObj.Facility.DisplayName + " 😁",
							Text:        "Hope you have a great " + validBookingObj.Sport.SportName + " session!",
							Action:      "upcoming_bookings",
							CtaText:     validBookingObj.BookingReferenceNumber,
							Id:          validBookingObj.BookingId,
							ExpiryDate:  time.Now().AddDate(0, 0, 3).Unix(),
							SubCategory: int32(notificationPB.NotificationSubCategories_ATTENDANCE_MARKED),
						}

						go s.SendNotificationToUsers(fcmStruct)

					}
				}
			}
		}

		if slotId == 0 {
			if err := s.DetermineSlotIdByCheckinTime(ctx, &slotId, req.CheckinTime); err != nil {
				log.Println("Error in getting slot id by check in time...", err)
				return err
			}
		}
		facilityId := facility.FacilityId
		if facilityId == 0 {
			facilityId = facilities[0].FacilityId
		}
		//make entry in processed attendance
		attData := &models.ProcessedSportsAttendance{
			AttendanceDumpId:       req.AttendanceDumpId,
			EntryId:                req.EntryId,
			UserId:                 userId,
			FacilityId:             facilityId,
			FsId:                   fsIdVal,
			SlotId:                 slotId,
			BookingId:              bookingId,
			BookingReferenceNumber: bRefNo,
			Medium:                 req.Medium,
			CheckinTime:            req.CheckinTime,
			CheckoutTime:           req.CheckoutTime,
			CheckinPhotoUrl:        req.CheckinPhotoUrl,
			CheckoutPhotoUrl:       req.CheckoutPhotoUrl,
			VisitPurpose:           req.VisitPurpose,
			SportName:              req.SportName,
			Name:                   req.Name,
			Phone:                  req.Phone,
			AttendanceMarkedBy:     req.AttendanceMarkedBy,
		}
		if err := s.Data.CreateUpdateProcessedAttendanceRecord(ctx, attData); err != nil {
			log.Println("Error in creating processed attendance record!")
			return err
		}

		if attData.ProcessedSportsAttendanceId > 0 {
			var attendanceData []interface{}
			errD := s.Data.GetSportsAttendanceForSpreadsheets(attData.ProcessedSportsAttendanceId, &attendanceData)

			if errD == nil {
				if errPub := s.Pub.SportsAttendanceToSpreadsheets(&attendanceData); errPub != nil {
					log.Println("Error in publishing processed attendance record to sheet!")
				}
			} else {
				log.Println("Error in getting processed attendance record for sheet!")
			}
		}
	}

	// send alert SMS to FM's if trial user
	// 	if userId > 0 && facility.FacilityId > 0 {
	// 		log.Println("initiating alert to FM's for trial user-- ", userId, facility.FacilityId)
	//
	// 		var trialPlanActive bool
	// 		// CheckIfActivePremiumPlanUser
	// 		if err := s.Data.CheckIfActiveTrialPlan(ctx, userId, &trialPlanActive); err != nil {
	// 			log.Println("Error in checking if user has active subscription or not!", err)
	// 			return err
	// 		}
	//
	// 		if trialPlanActive {
	// 			var userDB models.User
	// 			userData := &models.User{
	// 				UserID: userId,
	// 			}
	// 			if err := s.Data.Get(userData, &userDB); err != nil {
	// 				log.Println("error getting user: %v", err)
	// 			}
	// 			username := userDB.Name
	// 			msgBody := "Trial User : " + username + " (" + strconv.Itoa(int(userId)) + ") just entered to your facility \n"
	// 			msgBody += "Sport: " + req.SportName + "\n"
	// 			if len(req.Phone) > 0 {
	// 				msgBody += "Phone: " + req.Phone + "\n"
	// 			}
	// 			go s.sendAlertToFMForActiveTrialUsers(ctx, userId, facility.FacilityId, msgBody)
	// 		}
	// 	}

	return nil
}

func (s *Service) sendAlertToFMForActiveTrialUsers(ctx context.Context, userId int32, facilityId int32, msgBody string) error {

	// fetching FM phone number from Google sheet using facilityId
	facilityPhoneMap := make(map[string]string)

	if errA := s.retrieveFMsPhoneNumberFromSheet(&facilityPhoneMap); errA != nil {
		log.Println("FM phone retrieval error")
		return errA
	}

	fmPhoneNumber := facilityPhoneMap[strconv.Itoa(int(facilityId))]

	if fmPhoneNumber != "NA" && fmPhoneNumber != "" {
		phoneNumbers := GetStringArrayFromCommaSeparatedString(fmPhoneNumber)
		if errB := s.Pub.TrialAlertPublish(phoneNumbers, msgBody); errB != nil {
			log.Println("send message error")
			return errB
		}
	}

	return nil
}

func (s *Service) retrieveFMsPhoneNumberFromSheet(facilityPhoneMap *map[string]string) error {

	facility_phone_map := make(map[string]string)
	if errA := s.Data.GetCacheFacilityFmMap(&facility_phone_map); errA != nil {
		log.Println("FM phone retrieval error", errA)
	}

	if len(facility_phone_map) < 1 {
		ctx := context.Background()
		srv, err := sheets.NewService(ctx, option.WithCredentialsFile(client_secret_path), option.WithScopes(sheets.SpreadsheetsScope))

		resp, err := srv.Spreadsheets.Values.Get("1a8SWgir0GxF2_G4VK4Go66B3DxoslTOUv2etLYmWDN4", "Sheet1").Do()
		if err != nil {
			log.Printf("retrieveFMsPhoneNumberFromSheet: Unable to retrieve data from sheet: %v", err)
			fromEm := &structs.Email{
				Name:         "Fitso",
				EmailAddress: FITSO_BOT_MAIL,
			}

			toEmails := [2]string{TECH_MAIL, ANALYTICS_TEAM_MAIL}

			var toEm []*structs.Email

			for _, emailId := range toEmails {
				toEmVal := &structs.Email{
					EmailAddress: emailId,
				}
				toEm = append(toEm, toEmVal)
			}

			var message string

			message = fmt.Sprintf("retrieveFMsPhoneNumberFromSheet : Unable to retrieve FMs phone number data from sheet in userHandler : %v", err)

			emailReq := &structs.EmailRequest{
				From:    fromEm,
				To:      toEm,
				ReplyTo: fromEm,
				Subject: "ALERT : Unable to retrieve data from sheet",
				Message: message,
			}
			go s.Pub.SendBroadcastEmail(emailReq)
			return err
		}

		if len(resp.Values) == 0 {
			log.Println("No data found.")
			return nil
		} else {
			for _, row := range resp.Values {
				if len(row) == 4 && len(row[3].(string)) >= 10 {
					facility_phone_map[row[0].(string)] = row[3].(string)
				}
			}
		}

		if errB := s.Data.TokenizeFacilityFmMap(facility_phone_map); errB != nil {
			log.Println("FM phone mapping error", errB)
		}
	}

	*facilityPhoneMap = facility_phone_map
	return nil
}

func (s *Service) SendTrialUsersListToFmBeforeSlot(ctx context.Context, req *pb.Empty, res *pb.Ack) error {

	var slotData []models.Slot
	if err := s.Data.GetAllSlots(ctx, &slotData); err != nil {
		log.Println("Error in getting all slots..", err)
		return err
	}

	var slotId int32
	var slotName string

	t := time.Now()
	CurHour := t.Hour()
	CurMin := t.Minute()
	for _, ele := range slotData {
		slotMins := (int(ele.StartHour) * 60) + int(ele.StartMin)
		curMins := (CurHour * 60) + CurMin
		diff := slotMins - curMins
		if slotMins > curMins && diff <= 60 && diff > 55 {
			slotId = ele.SlotID
			slotName = ele.Timing
			break
		}
	}

	if slotId > 0 {
		var serviceList structs.ServiceConfig
		var asycnServiceList structs.AsyncServiceConfig

		config.Get("codeIndependent", "services").Scan(&serviceList)
		config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

		facilityPhoneMap := make(map[string]string)

		if errA := s.retrieveFMsPhoneNumberFromSheet(&facilityPhoneMap); errA != nil {
			log.Println("FM phone retrieval error")
			return errA
		}

		service := micro.NewService()
		bcl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

		for key, element := range facilityPhoneMap {
			if element != "NA" && element != "" {
				now := time.Now()
				StartDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, now.Nanosecond(), now.Location())
				EndDate := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 0, now.Nanosecond(), now.Location())

				facilityId, _ := strconv.Atoi(key)
				bookingData := &bookingPB.Booking{
					FacilityId:       int32(facilityId),
					IsTrial:          true,
					SlotIds:          strconv.Itoa(int(slotId)),
					Timeline:         1,
					BookingStartDate: StartDate.Unix(),
					BookingEndDate:   EndDate.Unix(),
				}

				response, err := bcl.GetBookingDetails(context.TODO(), bookingData)
				if err != nil {
					log.Println("Error in fetching trial user bookings!", err)
				}

				if response.Status.Status == "success" {
					if len(response.Booking) > 0 {

						message := "Following trial users are going to attend upcoming slot: " + slotName + "\n"
						for _, element := range response.Booking {
							var userDB models.User
							userData := &models.User{
								UserID: element.UserId,
							}
							if err := s.Data.Get(userData, &userDB); err != nil {
								log.Println("error getting user: %v", err)
							}
							username := userDB.Name
							message += username + " (" + strconv.Itoa(int(element.UserId)) + ") \n"
							message += "sport: " + element.Sport.SportName + "\n"
							if len(userDB.Phone) > 0 {
								message += "phone: " + userDB.Phone + "\n"
							}
							message += "\n"
						}

						phoneNumbers := GetStringArrayFromCommaSeparatedString(element)
						if errB := s.Pub.TrialAlertPublish(phoneNumbers, message); errB != nil {
							log.Println("send message error", errB)
						}
					}
				}
			}
		}
	}

	status := &pb.Status{
		Status:  "success",
		Message: "successfully send trial alert",
	}
	res.Status = status

	return nil
}

func (s *Service) ValidateDataPeaceUserV2(ctx context.Context, req *pb.ValidateDataPeaceUserRequest, res *pb.ValidateDataPeaceUserResponse) error {

	var allowed bool
	var denyReason *pb.DenyReason
	var allowReason *pb.AllowReason

	var facilities []structs.Facility
	if err := s.Data.GetFacilityIdFromCenterId(ctx, req.SpaceId, &facilities); err != nil {
		fmt.Println("Error in getting facility id from center id..", err)
		return err
	}

	if len(facilities) > 0 {

		for _, facility := range facilities {

			if facility.FrValidationFlag {
				if req.PurposeOfVisit == "Existing Member" || req.PurposeOfVisit == "Trial" {

					// NOt existing user or first-time user
					if req.Id > 0 {
						uInfo := &pb.UserInfo{
							PhoneNumber: req.MobileNumber,
						}
						var uResponse pb.ChildUserResponse //response stored here
						if err := s.GetChildUsers(ctx, uInfo, &uResponse); err != nil {
							fmt.Println("Could not fetch all connected users")
						}

						if uResponse.Status.Status == "success" {
							if len(uResponse.AllConnectedUsers) > 0 {
								for _, user := range uResponse.AllConnectedUsers {
									var username_check bool

									if err := s.ValidateUserName(req.FirstName+" "+req.LastName, user.Name, &username_check); err != nil {
										fmt.Println("Error in validating user name!")
										return err
									}

									if username_check == true {
										if user.ActiveSubscriptionCount > 0 {
											userSubsData, err := s.Data.GetUserActiveSingleMasterKeySubscriptionData(ctx, user.UserId)

											if err != nil {
												log.Printf("error in getting subscription data for user : %d, error : %s", user.UserId, err)
											}

											if len(userSubsData) > 0 {
												hasUserFreezedCurrentDayAllSubscriptions, err := s.Data.GetUserTodayFreezeStatus(ctx, user.UserId, userSubsData)

												if err != nil {
													log.Printf("Error in checking the freeze date for user, error : %s", err)
												}

												if hasUserFreezedCurrentDayAllSubscriptions {
													denyReason = &pb.DenyReason{
														Title:       "Sorry",
														DisplayText: "You have frezeed today's date.",
													}
													allowed = false
													break
												}

												var active_bookings []structs.UserBookingData
												if err := s.Data.GetUserTodayActiveBooking(ctx, user.UserId, &active_bookings); err != nil {
													fmt.Println("Could not fetch today active bookings for user...", err)
													return err
												}

												if len(active_bookings) > 0 {
													for _, ele := range active_bookings {
														fmt.Println("booking detail...", ele)
														endTimestamp, _ := ptypes.TimestampProto(ele.BookingTime)

														diff := time.Now().Unix() - endTimestamp.GetSeconds()
														if diff >= -30*60 && diff <= 30*60 {

															if ele.FacilityId == facility.FacilityId {
																fmt.Println("Booking found ...")
																allowReason = &pb.AllowReason{
																	Title:       "Welcome to FITSO facility",
																	DisplayText: "Have a Nice Game !!",
																}
																allowed = true
																break
															} else {
																denyReason = &pb.DenyReason{
																	Title:       "Sorry",
																	DisplayText: "No bookings found for this facility",
																}
																allowed = false
															}
														} else {
															fmt.Println("Booking not found for current slot")
															denyReason = &pb.DenyReason{
																Title:       "Sorry",
																DisplayText: "Booking not found for current slot!!",
															}
															allowed = false
														}
													}
												} else {
													fmt.Println("Booking not found")
													denyReason = &pb.DenyReason{
														Title:       "Sorry",
														DisplayText: "No active bookings found for today!!",
													}
													allowed = false
												}
											} else {
												fmt.Println("fixed user, have a nice game")
												allowReason = &pb.AllowReason{
													Title:       "Welcome to FITSO facility",
													DisplayText: "Have a Nice Game !!",
												}
												allowed = true
												break
											}
										} else {
											denyReason = &pb.DenyReason{
												Title:       "Sorry",
												DisplayText: "No User Plan Found, please connect with Facility Manager",
											}
											allowed = false
										}

										if allowed {
											break
										}
									} else {
										denyReason = &pb.DenyReason{
											Title:       "Sorry",
											DisplayText: "Provided name doesn't match with the user record, Please try again or connect with Facility Manager",
										}
										allowed = false
									}
								}
							} else {

								denyReason = &pb.DenyReason{
									Title:       "Sorry",
									DisplayText: "Provided phone-number doesn't match with FITSO database, kindly connect with Facility Manager",
								}
								allowed = false
							}
						} else {
							fmt.Println("No match")
							denyReason = &pb.DenyReason{
								Title:       "Sorry",
								DisplayText: "Provided phone number doesn't match with FITSO database, kindly connect with Facility Manager",
							}
							allowed = false
						}
					} else {
						fmt.Println("Person id is null or zero")
						denyReason = &pb.DenyReason{
							Title:       "Sorry",
							DisplayText: "Your record doesn't match with FITSO database, kindly connect with Facility Manager",
						}
						allowed = false
					}
				} else {
					allowReason = &pb.AllowReason{
						Title:       "Welcome",
						DisplayText: "Welcome to FITSO, Please meet Facility Manager before proceeding to court.",
					}
					allowed = true
				}
			} else {
				fmt.Println("FR VALIDATION FLAG -- 0")
				allowReason = &pb.AllowReason{
					Title:       "Welcome",
					DisplayText: "Welcome to FITSO.",
				}
				allowed = true
			}

			if allowed {
				break
			}
		}

	} else {
		log.Println("Invalid SpaceId", req.SpaceId)
		allowReason = &pb.AllowReason{
			Title:       "Welcome",
			DisplayText: "Welcome to FITSO.",
		}
		allowed = true

		fromEm := &structs.Email{
			Name:         "Fitso",
			EmailAddress: FITSO_BOT_MAIL,
		}

		toEmails := [5]string{"<EMAIL>", TECH_MAIL, "<EMAIL>", "<EMAIL>", "<EMAIL>"}

		var toEm []*structs.Email

		for _, emailId := range toEmails {
			toEmVal := &structs.Email{
				EmailAddress: emailId,
			}
			toEm = append(toEm, toEmVal)
		}

		var message string

		message = "Space-Id=" + strconv.Itoa(int(req.SpaceId)) + " doesn't match with any of our facility, kindly fix this on urgent priority"

		emailReq := &structs.EmailRequest{
			From:    fromEm,
			To:      toEm,
			ReplyTo: fromEm,
			Subject: "ALERT : Invalid Space-id",
			Message: message,
		}

		go s.Pub.SendBroadcastEmail(emailReq)
	}

	var is_allowed int32
	var fr_display_text string
	if allowed {
		is_allowed = 1
		fr_display_text = allowReason.DisplayText
	} else {
		is_allowed = 0
		fr_display_text = denyReason.DisplayText
	}

	frData := &models.FrValidationDump{
		SpaceId:       req.SpaceId,
		PeopleId:      req.Id,
		FirstName:     req.FirstName,
		LastName:      req.LastName,
		Phone:         req.MobileNumber,
		VisitPurpose:  req.PurposeOfVisit,
		IsAllowed:     is_allowed,
		FrDisplayText: fr_display_text,
	}

	if err := s.Data.InsertFrValidationDump(ctx, frData); err != nil {
		fmt.Println("Error in inserting fr validation dump to db... ", err)
	}

	fmt.Println("allowed", allowed)

	checkinFlow := &pb.CheckinFlow{
		Allowed:     allowed,
		AllowReason: allowReason,
		DenyReason:  denyReason,
	}

	dataPeaceData := &pb.DataPeaceData{
		CheckinFlow: checkinFlow,
	}

	dataPeaceResponse := &pb.DataPeaceResponse{
		Data: dataPeaceData,
	}
	Hookid := &pb.Hook{
		Id:       "before_checkin",
		Response: dataPeaceResponse,
	}
	res.Hook = Hookid

	return nil
}

func (s *Service) MarkAttendaceOfConsecutiveSlotsV2(ctx context.Context, userId int32, fsIds []structs.FsIdObj, checkinTime timestamp.Timestamp, bookings []*bookingPB.Booking, facilityId int32, req *models.AttendanceDump) error {
	fmt.Println("Marking consecutive slots")

	sort.SliceStable(bookings, func(i, j int) bool {
		return bookings[i].BookingTime.GetSeconds() < bookings[j].BookingTime.GetSeconds()
	})
	lastBookingtime := checkinTime
	for _, validBooking := range bookings { //check for booking
		diff := validBooking.BookingTime.GetSeconds() - lastBookingtime.GetSeconds()

		if diff <= 0 || (diff >= 2*60*60) || validBooking.Facility.FacilityId != facilityId {
			continue
		}
		updateSuccessful := false
		if len(validBooking.BookingReferenceNumber) > 0 {
			if len(validBooking.BookingReferenceNumber) == 9 { //mark attendance in users_seals_bookings
				//	var updateSuccessful bool
				if err := s.Data.MarkAttendanceInSeals(validBooking.BookingReferenceNumber, &updateSuccessful, 1); err != nil {
					fmt.Println("Could not mark attendance in seals for - ", validBooking.BookingReferenceNumber)
					return err
				}
				if updateSuccessful {
					fmt.Println("Attendance marked successfully in seals..")
				}
			} else { //mark attendance in all_bookings

				isNoshowApplicable := false

				if err := s.Data.MarkAttendanceInSports(ctx, validBooking.BookingId, &updateSuccessful, 1, isNoshowApplicable); err != nil {
					log.Println("Could not mark attendance in sports for - ", validBooking.BookingId)
					return err
				}
				if updateSuccessful {
					log.Println("Attendance marked successfully in sports..")
				}
			}
			if updateSuccessful {

				//make entry in processed attendance
				attData := &models.ProcessedSportsAttendance{
					AttendanceDumpId:       req.AttendanceDumpId,
					EntryId:                req.EntryId,
					UserId:                 userId,
					FacilityId:             facilityId,
					FsId:                   validBooking.FsId,
					SlotId:                 validBooking.SlotId,
					BookingReferenceNumber: validBooking.BookingReferenceNumber,
					BookingId:              validBooking.BookingId,
					Medium:                 req.Medium,
					CheckinTime:            req.CheckinTime,
					CheckoutTime:           req.CheckoutTime,
					CheckinPhotoUrl:        req.CheckinPhotoUrl,
					CheckoutPhotoUrl:       req.CheckoutPhotoUrl,
					VisitPurpose:           req.VisitPurpose,
					SportName:              validBooking.Sport.SportName,
					Name:                   req.Name,
					Phone:                  req.Phone,
				}

				if err := s.Data.CreateProcessedAttendanceRecord(attData); err != nil {
					fmt.Println("Error in creating processed attendance record!")
					return err
				}
			}
		}
		lastBookingtime = *validBooking.BookingTime
	}

	return nil
}

func (s *Service) SendNotificationToUsers(notificationData structs.FcmNotificationMessage) error {
	fmt.Println("Sending notification...!")

	app_condition := make(map[string]string)
	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"
	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	var uids []int32
	uids = append(uids, notificationData.UserId)

	nData := structs.FcmNotification{
		Title:         notificationData.Title,
		Text:          notificationData.Text,
		UserIds:       uids,
		Id:            notificationData.Id,
		Action:        notificationData.Action,
		ConditionData: condition_data,
		ActionId:      notificationData.ActionId,
		CtaText:       notificationData.CtaText,
		SubCategory:   notificationData.SubCategory,
	}

	if err := s.Pub.SendBroadcastNotification(&nData); err != nil {
		fmt.Println("Error in sending broadcast notification")
		return err
	}

	return nil
}

func (s *Service) ProcessAttendanceFromDumps(ctx context.Context) error {
	fmt.Println("Over here........!")
	var aData []models.AttendanceDump
	if err := s.Data.GetAttendanceDump(&aData); err != nil {
		fmt.Println("Error in getting all slots..", err)
		return err
	}
	fmt.Println("aData ------ ", aData)

	startTime := time.Now()
	for _, ele := range aData {
		fmt.Println("OVer herrr loop--------------...!")
		loopTime := time.Now()
		if loopTime.Sub(startTime).Minutes() >= 5 {
			time.Sleep(2 * time.Second)
			startTime = loopTime
		}
		s.ProcessAttendance(ctx, &ele)
	}

	return nil
}

func (s *Service) MarkAttendaceOfConsecutiveSlotsCron(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	var slotIds []int32
	var userData []structs.UserData

	s.IdentifySlotIdByCurrentTime(ctx, &slotIds, time.Now())
	if len(slotIds) > 0 {
		if err := s.Data.GetUnmarkedAttendanceUserIds(&userData, slotIds); err != nil {
			log.Println("Error in getting  users details.. Db error..", err)
			return err
		}
		for _, usr := range userData {
			var facilities []structs.Facility
			if err := s.Data.GetFacilityIdFromCenterId(ctx, usr.DatapeaceSpaceId, &facilities); err != nil {
				log.Println("Error in getting facility id from center id..", err)
				continue
			}
			//fmt.Println("Fid from attendance_user_people_mapping table... ", facility.FacilityId)
			if len(facilities) == 0 { //facility_id not determined from center_id
				continue
			}

			var fsIds []structs.FsIdObj
			var facilityIds []int32

			for _, fac := range facilities {
				facilityIds = append(facilityIds, fac.FacilityId)
			}

			if err := s.Data.GetFsIdsForFacilityId(ctx, &fsIds, facilityIds); err != nil {
				log.Println("Could not fetch bookings for user...", err)
				return err
			}

			var bookings []*bookingPB.Booking
			if err := s.GetActiveBookingsForUser(ctx, usr.UserId, fsIds, nil, usr.BookingTime, &bookings); err != nil {
				log.Println("Could not fetch bookings for user...", err)
				return err
			}

			for _, element := range bookings {
				diff := usr.BookingTime.Unix() - element.BookingTime.GetSeconds()
				if diff > 0 && diff <= 1*60*60 && element.AttendanceFlag == true {
					updateSuccessful := false
					if err := s.Data.MarkAttendanceInSports(ctx, usr.BookingId, &updateSuccessful, 1, false); err != nil {
						log.Println("Could not mark attendance in sports for - ", usr.BookingId)
						continue
					}
					if updateSuccessful {
						processedReq := models.ProcessedSportsAttendance{
							BookingReferenceNumber: element.BookingReferenceNumber,
						}

						var processedRow models.ProcessedSportsAttendance
						if err := s.Data.GetProcessedAttendance(processedReq, &processedRow); err != nil {
							log.Println("Could not get proccessed attendance")
							return err
						}
						if processedRow.EntryId > 0 {
							attData := &models.ProcessedSportsAttendance{
								EntryId:                processedRow.EntryId,
								UserId:                 usr.UserId,
								FacilityId:             usr.FacilityId,
								FsId:                   usr.FsId,
								SlotId:                 usr.SlotId,
								BookingReferenceNumber: usr.BookingReferenceNumber,
								BookingId:              usr.BookingId,
								Medium:                 3,
								CheckinTime:            time.Now(),
							}

							if err := s.Data.CreateProcessedAttendanceRecord(attData); err != nil {
								log.Println("Error in creating processed attendance record!")
								return err
							}
						}

						break
					}
				}

			}
		}
	}

	return nil

}

func (s *Service) IdentifySlotIdByCurrentTime(ctx context.Context, slotIds *[]int32, currentTime time.Time) error {
	var slotData []models.Slot
	if err := s.Data.GetAllSlots(ctx, &slotData); err != nil {
		fmt.Println("Error in getting all slots..", err)
		return err
	}
	// fmt.Println(slotData)
	currentTimeInMinutes := int32(currentTime.Hour())*60 + int32(currentTime.Minute())

	entrySlot := int32(0)
	for _, ele := range slotData {

		slotStartHour := ele.StartHour
		slotEndHour := ele.EndHour
		if slotStartHour > slotEndHour {
			// handling the slots for the time change from 24 P.M. to 1 A.M.
			slotEndHour = slotEndHour + 24

		}
		slotStartTimeInMiuntes := slotStartHour*60 + ele.StartMin
		slotEndTimeInMiutes := slotEndHour*60 + ele.EndMin
		if currentTimeInMinutes >= slotStartTimeInMiuntes && currentTimeInMinutes <= slotEndTimeInMiutes {
			entrySlot = ele.SlotID
			*slotIds = append(*slotIds, entrySlot)
		}
	}

	return nil
}

func (s *Service) GetActiveBookingsForUser(ctx context.Context, userId int32, fsIds []structs.FsIdObj, slotIds []int32, checkinTime time.Time, bookings *[]*bookingPB.Booking) error {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	bcl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	//make fsids array
	var fsIdValArr []string
	for _, ele := range fsIds {
		fsIdValArr = append(fsIdValArr, strconv.Itoa(int(ele.FsId)))
	}

	//make slotId array
	var slotIdValArr []string
	for _, ele := range slotIds {
		slotIdValArr = append(slotIdValArr, strconv.Itoa(int(ele)))
	}

	//make startDate and endDate
	monthStr := strconv.Itoa(int(checkinTime.Month()))
	if int(checkinTime.Month()) < 10 {
		monthStr = "0" + monthStr
	}

	dayStr := strconv.Itoa(int(checkinTime.Day()))
	if int(checkinTime.Day()) < 10 {
		dayStr = "0" + dayStr
	}

	loc, _ := time.LoadLocation("Asia/Kolkata")
	startStr := strconv.Itoa(int(checkinTime.Year())) + "-" + monthStr + "-" + dayStr + " 00:00:00"
	endStr := strconv.Itoa(int(checkinTime.Year())) + "-" + monthStr + "-" + dayStr + " 23:59:59"
	startDateTime, _ := time.ParseInLocation(formatYMDHIS, startStr, loc)
	startDateTimeLocal, _ := GetLocalDateTime(startDateTime)
	endDateTime, _ := time.ParseInLocation(formatYMDHIS, endStr, loc)
	endDateTimeLocal, _ := GetLocalDateTime(endDateTime)

	fmt.Println("start b ==== ", startDateTimeLocal)
	fmt.Println("end b ==== ", endDateTimeLocal)

	startDateTimestamp, _ := ptypes.TimestampProto(startDateTimeLocal)
	endDateTimestamp, _ := ptypes.TimestampProto(endDateTimeLocal)

	bookingData := &bookingPB.Booking{
		BookingStartDate: startDateTimestamp.Seconds,
		BookingEndDate:   endDateTimestamp.Seconds,
		Start:            0,
		Count:            10,
		Timeline:         1,
		UserId:           userId,
		FsIds:            strings.Join(fsIdValArr, ","),
		SlotIds:          strings.Join(slotIdValArr, ","),
	}
	fmt.Println("before getting booking -- ", bookingData)
	response, err := bcl.GetBookingDetails(ctx, bookingData)

	if err != nil {
		fmt.Println("Error in fetching existing bookings for user -- ", userId)
		return err
	}
	fmt.Println("Booking Response Data....", response)

	if response.Status.Status == "success" {
		*bookings = response.Booking
	}

	return nil
}

func (s *Service) DetermineSlotIdByCheckinTime(ctx context.Context, slotId *int32, checkinTime time.Time) error {
	var slotData []models.Slot
	if err := s.Data.GetAllSlots(ctx, &slotData); err != nil {
		log.Println("Error in getting all slots..", err)
		return err
	}

	checkInTimeHour := strconv.Itoa(int(checkinTime.Hour()))
	checkInTimeMinute := strconv.Itoa(int(checkinTime.Minute()))
	checkInTimeSecond := strconv.Itoa(int(checkinTime.Second()))

	if int(checkinTime.Minute()) < 10 {
		checkInTimeMinute = "0" + checkInTimeMinute
	}
	if int(checkinTime.Second()) < 10 {
		checkInTimeSecond = "0" + checkInTimeSecond
	}

	sampleCheckinTime, errParse := time.Parse(formatYMDHIS, sampleDate+" "+checkInTimeHour+":"+checkInTimeMinute+":"+checkInTimeSecond)
	if errParse != nil {
		fmt.Println("Error in creating sample datetime for checkin time... ", errParse)
	}

	entrySlot := int32(0) //default slot value
	for _, ele := range slotData {
		startHrs := strconv.Itoa(int(ele.StartHour))

		if ele.StartHour < 10 {
			startHrs = "0" + startHrs
		}

		startMins := strconv.Itoa(int(ele.StartMin))

		if ele.StartMin < 10 {
			startMins = "0" + startMins
		}

		tempStart, errS := time.Parse(formatYMDHIS, sampleDate+" "+startHrs+":"+startMins+":00")
		if errS != nil {
			log.Println("Error in parsing slot start.. ", errS)
		}

		//need to provide 30min grace entry window for users - subtract 30mins from both start and end, and then check
		actualStart := tempStart.Add(-30 * time.Minute)
		actualEnd := tempStart.Add(+30 * time.Minute)

		if (actualStart.Before(sampleCheckinTime) && sampleCheckinTime.Before(actualEnd)) || actualStart.Equal(sampleCheckinTime) || actualEnd.Equal(sampleCheckinTime) { //checkin within this time [start,end]; not (start,end)
			entrySlot = ele.SlotID
			break
		} else {
			log.Println("TIme condition mismatch")
		}
	}
	*slotId = entrySlot
	return nil
}

func (s *Service) GetProcessedAttendanceData(ctx context.Context, req *pb.ProcessedAttendanceDataRequest, res *pb.ProcessedAttendanceDataResponse) error {
	fmt.Println("req for processed attendance ---------", req)
	var furnishedAttendance []pb.ProcessedAttendanceFurnished
	if err := s.Data.GetProcessedAttendanceFurnished(req, &furnishedAttendance); err != nil {
		fmt.Println("Error in getting attendance", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting attendance.",
		}
		res.Status = status

		error := &pb.Error{
			Code:        612,
			Description: "Error in getting attendance. DB error!",
		}
		res.Error = append(res.Error, error)
		return err
	}

	currentSeconds := time.Now().Unix()
	for _, element := range furnishedAttendance {
		value := element
		if len(value.BookingReferenceNumber) > 0 || (value.SubscriptionStartDate >= currentSeconds && value.SubscriptionEndDate <= currentSeconds) {
			value.AttendanceCategory = 1
			// value.ProductDescription = value.BookingProduct
			// value.SubscriptionStartDate = value.BookingSubscriptionStartDate
			// value.SubscriptionEndDate = value.BookingSubscriptionEndDate
		} else if value.VisitPurpose == "Staff" || value.VisitPurpose == "Coaching staff" {
			value.AttendanceCategory = 3
		} else if value.AttendanceDispositionId > 0 {
			value.AttendanceCategory = 4
		} else {
			value.AttendanceCategory = 2
		}
		res.ProcessedData = append(res.ProcessedData, &value)
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Attendance fetched successfully",
	}
	res.Status = status

	return nil
}

func (s *Service) GetAttendanceCount(ctx context.Context, req *pb.ProcessedAttendanceDataRequest, res *pb.AttendanceCountResponse) error {
	fmt.Println("req for processed attendance count ---------", req)
	var furnishedAttendance []pb.ProcessedAttendanceFurnished
	if err := s.Data.GetProcessedAttendanceFurnished(req, &furnishedAttendance); err != nil {
		fmt.Println("Error in getting attendance", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting attendance.",
		}
		res.Status = status
		return err
	}

	var correct_entries int32 = 0
	var flagged_entries int32 = 0
	var staff_entries int32 = 0
	var attended_entries int32 = 0

	currentSeconds := time.Now().Unix()
	for _, element := range furnishedAttendance {
		value := element
		if len(value.BookingReferenceNumber) > 0 || (value.SubscriptionStartDate >= currentSeconds && value.SubscriptionEndDate <= currentSeconds) {
			correct_entries += 1
		} else if value.VisitPurpose == "Staff" || value.VisitPurpose == "Coaching staff" {
			staff_entries += 1
		} else if value.AttendanceDispositionId > 0 {
			attended_entries += 1
		} else {
			flagged_entries += 1
		}
	}

	count := &pb.AttendanceCount{
		Correct:  correct_entries,
		Flagged:  flagged_entries,
		Staff:    staff_entries,
		Attended: attended_entries,
	}
	res.AttendanceCount = count

	status := &pb.Status{
		Status:  "success",
		Message: "Attendance fetched successfully",
	}
	res.Status = status

	return nil
}

func (s *Service) AddAttendanceManual(ctx context.Context, req *pb.AddAttendanceManualRequest, res *pb.Ack) error {

	//determine center_id
	var centerId int32
	if err := s.Data.GetCenterIdForFacilityId(req.FacilityId, &centerId); err != nil {
		fmt.Println("Error in getting center id from facility id..", err)
		return err
	}
	if centerId <= 0 { //center_id not determined from fac_id
		return errors.New("Error in getting center Id for facility id.. Cannot proceed..")
	}

	markData := &models.AttendanceDump{
		Medium:             2,
		CenterId:           centerId,
		Name:               req.Name,
		Phone:              req.Phone,
		UserId:             req.UserId,
		AttendanceMarkedBy: req.AttendanceMarkedBy,
	}

	if req.BookingId > 0 {

		var serviceList structs.ServiceConfig
		var asycnServiceList structs.AsyncServiceConfig

		config.Get("codeIndependent", "services").Scan(&serviceList)
		config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

		service := micro.NewService()
		bcl = bookingPB.NewBookingService(serviceList.Booking, service.Client())
		bookingData := &bookingPB.Booking{
			BookingId: req.BookingId,
		}
		response, err := bcl.GetUserBookingDetailsByBookingId(context.TODO(), bookingData)

		if err != nil {
			log.Println("Error in fetching existing bookings for Id -- ", req.BookingId)
			return err
		}

		if response.Status.Status == "success" {
			if len(response.Booking) > 0 {
				requiredBooking := response.Booking[0]

				markData.UserId = requiredBooking.UserId
				markData.Name = requiredBooking.User.Name
				markData.Phone = requiredBooking.User.Phone
				markData.CheckinTime = GetLocalDateTimeFromProtoTime(requiredBooking.BookingTime)
			} else {
				log.Printf("No booking found for booking_id:%d", req.BookingId)
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not mark attendance, no booking found",
				}
				res.Status = status
				return nil
			}
		} else {
			log.Printf("Error in fetching booking details for booking_id:%d, err:%v", req.BookingId, response.Status.Message)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not mark attendance",
			}
			res.Status = status
			return nil
		}

	} else {
		if req.UserId > 0 {
			var userDB models.User

			userData := &models.User{
				UserID: req.UserId,
			}
			if err := s.Data.Get(userData, &userDB); err != nil {
				return errors.New(fmt.Sprintf("error getting user: %v", err))
			}

			markData.UserId = req.UserId
			markData.Name = userDB.Name
			markData.Phone = userDB.Phone
		} else {
			markData.Name = req.Name
			markData.Phone = req.Phone
		}
		if req.CheckinTime > 0 {
			checkinTime, _ := GetLocalDateTime(time.Unix(req.CheckinTime, 0))
			markData.CheckinTime = checkinTime
		}
	}

	if err := s.Data.InsertAttendanceDump(ctx, markData); err != nil {

		log.Println("Error in inserting attendance dump to db... ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not insert attendance dump!",
		}
		res.Status = status
		return err
	}

	if markData.AttendanceDumpId > 0 { //new dump entry created
		//get user_id by people_id from the attendance_user_people_mapping table
		go s.ProcessAttendance(ctx, markData)
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}
	res.Status = status

	return nil
}

func (s *Service) UnMarkAttendance(ctx context.Context, req *pb.AddAttendanceManualRequest, res *pb.Ack) error {
	bRefNo := req.BookingReferenceNumber
	bookingId := req.BookingId
	attendanceMarkedBy := req.AttendanceMarkedBy
	if bookingId > 0 {
		var serviceList structs.ServiceConfig
		var asycnServiceList structs.AsyncServiceConfig

		config.Get("codeIndependent", "services").Scan(&serviceList)
		config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

		service := micro.NewService()
		bcl = bookingPB.NewBookingService(serviceList.Booking, service.Client())
		bookingData := &bookingPB.Booking{
			BookingId: bookingId,
		}
		fmt.Println("before getting booking -- ", bookingData)
		response, err := bcl.GetBookingDetails(context.TODO(), bookingData)

		if err != nil {
			fmt.Println("Error in fetching existing bookings for reference -- ", req.BookingReferenceNumber)
			return err
		}
		if response.Status.Status == "success" {
			if len(response.Booking) > 0 {
				requiredBooking := response.Booking[0]
				if requiredBooking.AttendanceFlag == true {

					if len(bRefNo) == 9 { //mark attendance in users_seals_bookings
						var updateSuccessful bool
						if err := s.Data.MarkAttendanceInSeals(bRefNo, &updateSuccessful, 0); err != nil {
							fmt.Println("Could not mark attendance in seals for - ", bRefNo)
							return err
						}

					} else { //mark attendance in all_bookings
						var updateSuccessful bool

						if err := s.Data.UnmarkAttendanceInSports(bookingId, &updateSuccessful, attendanceMarkedBy); err != nil {
							fmt.Println("Could not mark attendance in sports for - ", bRefNo)
							return err
						}
					}
				} else {
					log.Printf("UnMarkAttendance: trying to unmark attendance for not attended booking with booking id: %d", bookingId)
					return errors.New("User has not attended the session")
				}
			} else {
				log.Printf("UnMarkAttendance: No booking details found for booking id: %d", bookingId)
				return errors.New(fmt.Sprintf("no booking details found with booking id %d", bookingId))
			}
		} else {
			log.Printf("UnMarkAttendance: error in getting booking details for booking id: %d", bookingId)
			return errors.New(fmt.Sprintf("Error in getting booking details for booking id: %d", bookingId))
		}
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Attendace unmarked successfully!",
	}

	res.Status = status
	return nil
}

func (s *Service) ReprocessAttendance(ctx context.Context, req *pb.ReprocessAttendanceRequest, res *pb.Ack) error {

	processedReq := models.ProcessedSportsAttendance{
		ProcessedSportsAttendanceId: req.ProcessedSportsAttendanceId,
	}

	var processedRow models.ProcessedSportsAttendance
	if err := s.Data.GetProcessedAttendance(processedReq, &processedRow); err != nil {
		fmt.Println("Could not get proccessed attendance")
		return err
	}

	status := &pb.Status{
		Status:  "failure",
		Message: "Couldn't find booking, failed!",
	}
	res.Status = status

	if processedRow.UserId > 0 {
		var fsIds []structs.FsIdObj
		var facilities []structs.Facility
		if err := s.Data.GetFacilityIdFromCenterId(ctx, processedRow.CenterId, &facilities); err != nil {
			fmt.Println("Error in getting facility id from center id..", err)
			return err
		}
		//fmt.Println("Fid from attendance_user_people_mapping table... ", facility.FacilityId)
		if len(facilities) == 0 { //facility_id not determined from center_id
			return errors.New("Error in getting Facility Id from center id.. Cannot proceed..")
		} else {
			//determine possible fs_ids where the user could have booked
			var facilityIds []int32

			for _, fac := range facilities {
				facilityIds = append(facilityIds, fac.FacilityId)
			}

			if err := s.Data.GetFsIdsForFacilityId(ctx, &fsIds, facilityIds); err != nil {
				log.Println("Error in getting fs_ids for facility id..", err)
				return err
			}
		}
		slotId := processedRow.SlotId
		userId := processedRow.UserId
		var facilityId int32

		if userId > 0 && slotId > 0 && len(fsIds) > 0 { //check for booking

			var slotIdValArr []int32
			var bRefNo, sportName string
			var fsIdVal int32
			var bookingId int32
			var bookingDate int64
			var slotId, sportId int32
			var appType string
			slotIdValArr = append(slotIdValArr, slotId)

			var bookings []*bookingPB.Booking
			if err := s.GetActiveBookingsForUser(ctx, userId, fsIds, slotIdValArr, processedRow.CheckinTime, &bookings); err != nil {
				log.Println("Could not fetch bookings for user...", err)
				return err
			}
			var diffTime int64
			var isTrial, masterkeyBooking bool

			for _, ele := range bookings {
				if ele.SlotId == slotId && ele.BookingCancelled != true && ele.AttendanceFlag != true {
					bRefNo = ele.BookingReferenceNumber
					bookingId = ele.BookingId
					fsIdVal = ele.FsId
					facilityId = ele.Facility.FacilityId
					//fmt.Println(row.CheckinTime, "--------------", ele.BookingTime)
					diffTime = processedRow.CheckinTime.Unix() - ele.BookingTime.Seconds
					slotId = ele.SlotId
					bookingDate = ele.BookingDate.Seconds
					isTrial = ele.IsTrial
					sportId = ele.Sport.SportId
					sportName = ele.Sport.SportName
					appType = ele.AppType
					if ele.ProductArenaCategoryId == 1 || ele.ProductArenaCategoryId == 2 || ele.ProductArenaCategoryId == 3 {
						masterkeyBooking = true
					}
					break
				}
			}
			if len(bRefNo) > 0 {
				//	fmt.Println("diff---", diffTime)
				var attendanceFlag int32
				var lateComer bool
				if diffTime >= -30*60 && diffTime <= 30*60 {
					attendanceFlag = 1
					if diffTime >= 15*60 {
						lateComer = true
					}
				}
				if len(bRefNo) == 9 { //mark attendance in users_seals_bookings
					var updateSuccessful bool
					if err := s.Data.MarkAttendanceInSeals(bRefNo, &updateSuccessful, 1); err != nil {
						log.Println("Could not mark attendance in seals for - ", bRefNo)
						return err
					}

				} else { //mark attendance in all_bookings
					var updateSuccessful bool
					var facility structs.Facility
					if err := s.Data.GetFacilityFromFacilityId(processedRow.FacilityId, &facility); err != nil {
						log.Println("Error in getting facility id from facility id..", err)
						return err
					}
					isNoshowApplicable := lateComer && !isTrial && facility.NoShowPenaltyOn
					if err := s.Data.MarkAttendanceInSports(ctx, bookingId, &updateSuccessful, attendanceFlag, isNoshowApplicable); err != nil {
						log.Println("Could not mark attendance in sports for - ", bRefNo)
						return err
					}
					if masterkeyBooking || appType == "cult-app" { //masterkeyBooking
						data := &structs.SQSAttendanceEvent{
							UserId:     userId,
							BookingId:  bookingId,
							SportId:    sportId,
							FaciltiyId: facilityId,
							SlotId:     slotId,
							Date:       fmt.Sprintf("%s", time.Unix(bookingDate, 0).Format("2006-01-02")),
							SportName:  sportName,
						}
						if appType != "cult-app" {
							log.Println("test fitso booking ", masterkeyBooking)
							data.FitsoBooking = true
						}
						queueName := config.Get("sqsQueue", "queueName").String("")
						_ = s.Pub.SendAttendanceMessageToSQS(queueName, data)

					}
				}

				processedRow.FsId = fsIdVal
				processedRow.BookingReferenceNumber = bRefNo
				processedRow.BookingId = bookingId
				processedRow.FacilityId = facilityId
				if err := s.Data.CreateUpdateProcessedAttendanceRecord(ctx, &processedRow); err != nil {
					log.Println("Error in updating processed attendance record!")
					return err
				}

				status := &pb.Status{
					Status:  "success",
					Message: "Recorded successfully!",
				}
				res.Status = status
			}
		}
	}

	return nil
}

func (s *Service) GetAttendanceDispositions(ctx context.Context, req *pb.Empty, res *pb.AttendanceDispositonResponse) error {
	fmt.Println("req -------------------- ", req)

	var disps []models.AttendanceDisposition

	//fetch broadcast logs for user(join), else from log table (paginated)
	if err := s.Data.GetAttendanceDispositions(&disps); err != nil {
		fmt.Println("Error in getting broadcast logs..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting broadcast logs..",
		}
		res.Status = status
		return err
	}

	if len(disps) > 0 {

		for _, disp := range disps {
			res.AttendanceDispositions = append(res.AttendanceDispositions, disp.Proto())
		}
		status := &pb.Status{
			Status:  "success",
			Message: "disps fetched successfully!",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "No disps found!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) UpdateDispositionForAttendance(ctx context.Context, req *pb.UpdateDispositionForAttendanceReq, res *pb.Ack) error {

	if req.ProcessedSportsAttendanceId > 0 {

		processedReq := models.ProcessedSportsAttendance{
			ProcessedSportsAttendanceId: req.ProcessedSportsAttendanceId,
		}

		var processedRow models.ProcessedSportsAttendance
		if err := s.Data.GetProcessedAttendance(processedReq, &processedRow); err != nil {
			fmt.Println("Could not get proccessed attendance")
			return err
		}

		if processedRow.ProcessedSportsAttendanceId > 0 && req.AttendanceDispositionId > 0 {

			processedRow.AttendanceDispositionId = req.AttendanceDispositionId
			processedRow.Remarks = req.Remarks

			if err := s.Data.CreateUpdateProcessedAttendanceRecord(ctx, &processedRow); err != nil {
				fmt.Println("Error in updating processed attendance record!")
				return err
			}
			// if processedRow.AttendanceDispositionId == 13 {
			// 	dataToPush, err := s.Data.GetProcessedAttendanceForId(processedRow.ProcessedSportsAttendanceId)
			// 	if err != nil {
			// 		log.Println("Error fetching processed attendance for ID: ", processedRow.ProcessedSportsAttendanceId)
			// 		return err
			// 	}
			// 	cl := sheets1.NewClientWithContext(ctx)
			// 	checkInTimeStr := dataToPush.CheckinTime.Format(formatYMDHIS)

			// 	var facility structs.Facility
			// 	if err := s.Data.GetFacilityFromFacilityId(dataToPush.FacilityId, &facility); err != nil {
			// 		log.Println("Error in getting facility id from facility id..", err)
			// 		return err
			// 	}

			// 	var slotData []models.Slot
			// 	if err := s.Data.GetAllSlots(ctx, &slotData); err != nil {
			// 		log.Println("Error in getting all slots..", err)
			// 		return err
			// 	}

			// 	var slotTiming string
			// 	for _, ele := range slotData {
			// 		if ele.SlotID == dataToPush.SlotId {
			// 			slotTiming = ele.Timing
			// 			break
			// 		}
			// 	}

			// 	if err := cl.Write([]interface{}{
			// 		dataToPush.EntryId,
			// 		dataToPush.Name,
			// 		dataToPush.Phone,
			// 		dataToPush.UserId,
			// 		dataToPush.VisitPurpose,
			// 		dataToPush.SportName,
			// 		facility.DisplayName,
			// 		slotTiming,
			// 		checkInTimeStr,
			// 		"UpdateDispositionForAttendance",
			// 	}, AcademyTrialUsersGoogleSheet, AcademyTrialUsersGsRange); err != nil {
			// 		log.Println("Error in pushing to google sheets: ", err)
			// 		cl.SendErrorReport(err, AcademyTrialUsersGoogleSheet, AcademyTrialUsersGsRange)
			// 		return err
			// 	}
			// }

			status := &pb.Status{
				Status:  "success",
				Message: "Recorded successfully!",
			}
			res.Status = status
			return nil
		}
	}

	status := &pb.Status{
		Status:  "failure",
		Message: "Couldn't find attendance, failed!",
	}
	res.Status = status

	return nil
}

func (s *Service) UpdateUserCitySelected(ctx context.Context, req *pb.UpdateUserCitySelectedRequest, res *pb.UpdateUserCitySelectedResponse) error {
	fmt.Print("req---------", req)

	reqData := &models.UserCitySelectLog{
		CityId:    req.CityId,
		CityName:  req.CityName,
		UserId:    req.UserId,
		CityIdV2:  req.CityIdV2,
		ZoneId:    req.ZoneId,
		SubzoneId: req.SubzoneId,
	}

	if err := s.Data.InsertUpdateUserCitySelected(reqData); err != nil {
		fmt.Println("Error in inserting  user city selected logs..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in inserting user city selected logs..",
		}
		res.Status = status
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}
	res.Status = status
	return nil
}

func (s *Service) UpdateUserSelectedLocation(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	userId := util.GetUserIDFromContext(ctx)
	isLocationLogged := s.Data.GetCachedLoggedLocation(userId)
	if isLocationLogged == 1 {
		return nil
	}
	subzoneId := util.GetSubzoneIDFromContext(ctx)
	reqData := &pb.UpdateUserCitySelectedRequest{
		UserId:    userId,
		CityIdV2:  util.GetCityIDFromContext(ctx),
		SubzoneId: subzoneId,
	}
	productClient := util.GetProductClient()
	response, err := productClient.GetZoneForGivenSubzone(ctx, &productPB.Empty{})
	if err != nil {
		log.Printf("function: UpdateUserSelectedLocation, error in getting zone id from subzone id for user id %d, subzone id: %d, err: %v", userId, subzoneId, err)
		return err
	}
	reqData.ZoneId = response.ZoneId
	var updateUserSelectedLocationRes pb.UpdateUserCitySelectedResponse
	err = s.UpdateUserCitySelected(ctx, reqData, &updateUserSelectedLocationRes)
	if err != nil {
		log.Printf("function: UpdateUserSelectedLocation, error in updating user select city logs for user id: %d, zone id %d, subzone id %d, err: %v", userId, response.ZoneId, subzoneId, err)
		return err
	}
	err = s.Data.SetLoggedLocationInCache(userId)
	if err != nil {
		log.Printf("function: UpdateUserSelectedLocation, error in setting redis data for user id: %d, err: %v", userId, err)
		return err
	}
	return nil
}

func (s *Service) GetUserInterstitialData(ctx context.Context, req *pb.UserInfo, res *pb.UserInterstitialResponse) error {

	if req.AppLaunchCount%5 != 0 || true {
		fmt.Println("Not showing ad to user in this case..")
		status := &pb.Status{
			Status:  "success",
			Message: "App launch count not met yet || no ad currently.",
		}
		res.Status = status

		return nil
	}
	//get static text for now based on userDetails(in future)
	var uIntData pb.UserInterstitial
	if err := UserInterstitialDataGet(req, &uIntData); err != nil {
		fmt.Println("Error in getting user interstitial data..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch user interstitial details..",
		}
		res.Status = status

		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "User interstitial details fetched successfully!",
	}
	res.Status = status

	res.InterstitialData = &uIntData

	return nil
}

// [TEST FUNC] Don't use this func
func (s *Service) SendInvoiceToOneTimeUsers(ctx context.Context, req *pb.Empty, res *pb.Ack) error {

	cl := sheets1.NewClientWithContext(ctx)
	if _, err := cl.Read(); err != nil {
		log.Println("Error in reading to google sheets: ", err)
	}

	return nil
}

func (s *Service) GetShortNotice(ctx context.Context, req *pb.GetShortNoticeRequest, res *pb.GetShortNoticeResponse) error {
	cityId := util.GetCityIDFromContext(ctx)

	if featuresupport.SupportsV2(ctx) {
		req.UserId = util.GetUserIDFromContext(ctx)
		req.CityId = cityId
	} else {
		if req.UserId == 0 {
			status := &pb.Status{
				Status:  "failure",
				Message: "user id not passed.",
			}
			res.Status = status
			return nil
		}
	}

	var userCategoryIds []int32

	//get user categories
	if err := s.GetUserCategoriesDetails(ctx, req.UserId, req.ProductCategoryId, cityId, USER_CATEGORY_FOR_SHORT_NOTICES, &userCategoryIds); err != nil {
		log.Printf("GetShortNotice: error in getting user category details for user id: %d, product category ID %d, err: %v", req.UserId, req.ProductCategoryId, err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in gettting user category!",
		}
		res.Status = status

		return nil
	}

	if len(userCategoryIds) <= 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "Cannot proceed! User not present in any active category!",
		}
		res.Status = status

		return nil
	}

	if featuresupport.SupportsV2(ctx) {
		res.ShortNotice = []*pb.ShortNotice{}
		var shortNotices []models.ShortNotices

		getShortNoticeCond := &models.ShortNotices{
			UserCategoryIds: userCategoryIds,
			IsActive:        true,
		}
		// var cityIds []string
		if req.CityId > 0 {
			// cityIds := []string{strconv.Itoa(int(req.CityId))}
			getShortNoticeCond.CityIds = strings.Join([]string{strconv.Itoa(int(req.CityId))}, ",")
		}

		zoneId := util.GetZoneIDFromContext(ctx)
		if zoneId > 0 {
			getShortNoticeCond.ZoneIds = []int32{util.GetZoneIDFromContext(ctx)}
		}

		if err := s.Data.BatchGetShortNotice(ctx, &shortNotices, getShortNoticeCond); err != nil {
			return errors.New(fmt.Sprintf("error getting short notices: %v", err))
		}

		// sort short notices by priority order-- desc
		sort.SliceStable(shortNotices, func(i, j int) bool {
			return shortNotices[i].Priority > shortNotices[j].Priority
		})

		for _, ele := range shortNotices {
			shortNoticePoints := strings.Split(ele.ShortNoticePoints, "\n")
			cityIds := GetIntegerArrayFromCommaSeparatedString(ele.CityIds)
			userCategoryIds := GetIntegerArrayFromCommaSeparatedString(ele.UserCategories)
			isDismissible := false
			if ele.IsDismissible == 1 {
				isDismissible = true
			}

			shortNotice := &pb.ShortNotice{
				ShortNoticeId:           ele.ShortNoticeId,
				Title:                   ele.Title,
				Subtitle:                ele.Subtitle,
				BottomSheetHeader:       ele.BottomSheetHeader,
				ShortNoticePoints:       shortNoticePoints,
				BackgroundColor:         ele.BackgroundColor,
				ActionId:                ele.ActionId,
				ActionUrl:               ele.ActionUrl,
				ActionUrlPostbackParams: ele.ActionUrlPostbackParams,
				ActionContent:           ele.ActionContent,
				ActionContentColor:      ele.ActionContentColor,
				StartTime:               ele.StartTime,
				EndTime:                 ele.EndTime,
				AllCitiesFlag:           ele.AllCitiesFlag,
				Priority:                ele.Priority,
				UserCategories:          userCategoryIds,
				CityIds:                 cityIds,
				IsDismissible:           isDismissible,
			}
			res.ShortNotice = append(res.ShortNotice, shortNotice)
		}
	} else {
		var shortNoticeDataArray []structs.ShortNoticeData

		if err := GetShortNoticeData(ctx, req, &shortNoticeDataArray, userCategoryIds); err != nil {
			fmt.Println("Error in getting user short notice data..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch user short notice  details..",
			}
			res.Status = status

			return err
		}

		if len(shortNoticeDataArray) == 0 {
			status := &pb.Status{
				Status:  success,
				Message: "short notice details not available..",
			}
			res.Status = status
			return nil
		}
		for _, ele := range shortNoticeDataArray {
			shortNoticeProto := &pb.ShortNotice{
				BackgroundColor: ele.BackgroundColor,
				TextColor:       ele.TextColor,
				NoticeText:      ele.NoticeText,
				IsDismissible:   ele.IsDismissible,
				NoticeImage:     ele.NoticeImage,
				Url:             ele.Url,
				ButtonText:      ele.ButtonText,
				ButtonColor:     ele.ButtonColor,
				Action:          ele.Action,
			}
			res.ShortNotice = append(res.ShortNotice, shortNoticeProto)
		}
	}

	status := &pb.Status{
		Status:  "success",
		Message: "short notice data fetch successfully!",
	}
	res.Status = status
	return nil
}

func (s *Service) GetMeasures(ctx context.Context, measures *pb.Measures, measuresRes *pb.MeasuresResponse) error {

	var measuresData []models.Measures
	measureCond := &models.Measures{
		CityId: measures.CityId,
		TabId:  measures.TabId,
	}

	if err := s.Data.GetMeasuresData(&measuresData, measureCond); err != nil {
		fmt.Println("Error in  getting measures data..", err)
		return err
	}

	for _, element := range measuresData {
		measuresRes.Measure = append(measuresRes.Measure, element.Proto())
	}

	var reverseMeasures []*pb.Measures
	for i := (len(measuresRes.Measure) - 1); i >= 0; i-- {
		reverseMeasures = append(reverseMeasures, measuresRes.Measure[i])
	}
	measuresRes.Measure = reverseMeasures

	// sort measures by priority order-- desc
	sort.SliceStable(measuresRes.Measure, func(i, j int) bool {
		return measuresRes.Measure[i].PriorityOrder > measuresRes.Measure[j].PriorityOrder
	})

	measuresRes.AspectRatioHeight = float32(measureBannerHeight)
	measuresRes.AspectRatioWidth = float32(measureBannerWidth)
	measuresRes.WidthPercent = float32(widthPercent)

	return nil
}

func (s *Service) SuggestedContentGet(ctx context.Context, req *pb.SuggestedContentRequest, res *pb.SuggestedContentResponse) error {

	measureCond := &pb.Measures{
		CityId: req.CityId,
		TabId:  req.TabId,
	}

	var measures pb.MeasuresResponse
	if err := s.GetMeasures(context.TODO(), measureCond, &measures); err != nil {
		fmt.Println("Error in  getting measures data..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch measures details..",
		}
		res.Status = status

		return err
	}

	var suggestedBanners pb.SuggestedBanners
	var action_url string
	for _, element := range measures.Measure {

		if req.UserId > 0 {
			action_url = element.CtaText + "?user_id=" + strconv.Itoa(int(req.UserId)) + "&city_ids=" + strconv.Itoa(int(req.CityId))
		} else {
			action_url = element.CtaText
			if req.CityId > 0 {
				action_url = action_url + "?city_ids=" + strconv.Itoa(int(req.CityId))
			}
		}
		bannerObjects := &pb.BannerObjects{
			UniqueIdentifier: element.MeasureId,
			Title:            element.Title,
			StartTime:        element.StartTime,
			EndTime:          element.EndTime,
			CoverImage:       element.MeasureImage,
			Id:               element.Id,
			CtaText:          action_url,
			Action:           element.Action,
			PriorityOrder:    element.PriorityOrder,
			Url:              action_url,
		}

		suggestedBanners.BannerObjects = append(suggestedBanners.BannerObjects, bannerObjects)
	}
	suggestedBanners.AspectRatioHeight = measures.AspectRatioHeight
	suggestedBanners.AspectRatioWidth = measures.AspectRatioWidth
	suggestedBanners.WidthPercent = measures.WidthPercent

	// if(len(suggestedBanners.BannerObjects) == 0){
	// 	status := &pb.Status{
	// 		Status:  "success",
	// 		Message: "No data exist!",
	// 	}
	// 	res.Status = status

	// 	error := &pb.Error{
	// 		Code:        208,
	// 		Description: "No suggested content exist with for the given fields!",
	// 	}

	// 	res.Error = append(res.Error, error)

	// 	return nil
	// }

	var suggestedBannersArr []*pb.SuggestedBanners
	suggestedBannersArr = append(suggestedBannersArr, &suggestedBanners)

	var suggestedContent pb.SuggestedContent
	suggestedContent.SuggestedBanners = suggestedBannersArr

	res.SuggestedContent = &suggestedContent

	status := &pb.Status{
		Status:  "success",
		Message: "Suggested Content data fetched successfully",
	}
	res.Status = status

	return nil
}

func (s *Service) AllotScratchCard(ctx context.Context, userId int32, bookingReferenceNumber string, isTrial bool) error {
	cityId := util.GetCityIDFromContext(ctx)

	var userCategoryIds []int32
	if err := s.GetUserCategoriesDetails(ctx, userId, MASTERKEY_PRODUCT_CATEGORY_ID, cityId, ALL_USER_CATEGORY, &userCategoryIds); err != nil {
		/*
			status := &pb.Status{
				Status:  "failure",
				Message: "Error in getting user category!",
			}
			//res.Status = status
		*/
		return nil
	}
	if len(userCategoryIds) <= 0 {
		/*
			status := &pb.Status{
				Status:  "failure",
				Message: "Cannot proceed! User not present in any active category!",
			}
			res.Status = status
		*/
		return nil
	}

	var userReward models.Rewards
	if err := s.PickRewardFromAllApplicableRewards(userId, isTrial, &userReward); err != nil {
		log.Println("error in picking reward for user :", err)
		return nil
	}
	if userReward.RewardId == 0 {
		log.Println("error in picking reward for user :")
		return nil
	}
	var voucherId int32
	log.Println("user reward : ", userReward)
	if userReward.RewardType == 3 {
		if err := s.CreateVoucherCode(userReward, userId, &voucherId); err != nil || voucherId == 0 {
			log.Println("error in creating voucher code : ", err)
			return nil
		}
		userReward.VoucherId = voucherId
	}

	if err := s.GenerateScratchCard(userReward, userId, bookingReferenceNumber); err != nil {
		log.Println("error in generating scratch card :", err)
		return nil
	}
	//send scratch card notification
	fcmStruct := structs.FcmNotificationMessage{
		UserId:     userId,
		Title:      "You have Just Unlocked A Scratch Card!",
		Text:       "Go to your offer page and Scratch Off Card To Avail Upto 1 Month FREE Membership. Have a great game!",
		ExpiryDate: time.Now().AddDate(0, 0, 3).Unix(),
	}
	go s.SendNotificationToUsers(fcmStruct)
	return nil

	return nil
}

func (s *Service) PopulateTrialRewardCron(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	var rewards []models.Rewards

	trialUserRewardMap := make(map[int32]float32)
	trialUserRewardMap[5] = 10.0
	trialUserRewardMap[7] = 25.0
	trialUserRewardMap[8] = 64.0
	trialUserRewardMap[12] = 1.0
	trialUserCategory := []int32{6}
	if err := s.GetAllApplicableRewards(trialUserCategory, &rewards); err != nil {
		log.Println("error in getting rewards : ", err)
		return nil
	}

	var cacheReward []models.Rewards
	for _, reward := range rewards {
		rewardProbability := trialUserRewardMap[reward.RewardId]
		noOfreward := int(rewardProbability * 5)
		for i := 0; i < noOfreward; i++ {
			cacheReward = append(cacheReward, reward)
		}
	}
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(cacheReward), func(i, j int) { cacheReward[i], cacheReward[j] = cacheReward[j], cacheReward[i] })

	key := "trial_user_reward"
	var getRewards []models.Rewards
	if err := s.GetRewardFromCache(key, &getRewards); err != nil || len(rewards) == 0 {
		log.Println("error in getting rewards from cache: ", err)
		return nil
	}
	var allRewards []models.Rewards
	allRewards = append(getRewards, cacheReward...)
	if err := s.SetRewardInCache(key, allRewards); err != nil {
		log.Println("error in setting rewards :", err)
		return err
	}
	log.Println("premium award len :", len(allRewards))
	/*
		for key, ele := range allRewards {
			log.Println("trail reward id: ", key, " reward : ", ele)
		}
	*/

	go s.RemiderMailToUpdateReward("Trial Reward set in Redis.")
	return nil
}

func (s *Service) PopulatePreimumRewardCron(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	var rewards []models.Rewards

	premiumUserRewardMap := make(map[int32]float32)
	premiumUserRewardMap[6] = 50.0
	premiumUserRewardMap[9] = 12.5
	premiumUserRewardMap[10] = 12.5
	premiumUserRewardMap[4] = 6.0
	premiumUserRewardMap[1] = 6.0
	premiumUserRewardMap[2] = 1.25
	premiumUserRewardMap[3] = 1.25
	premiumUserRewardMap[12] = 1.0
	premiumUserRewardMap[11] = 1.0
	premiumUserRewardMap[5] = 1.5
	premiumUserRewardMap[7] = 3.5
	premiumUserRewardMap[8] = 5.0

	premiumUserCategory := []int32{3}
	if err := s.GetAllApplicableRewards(premiumUserCategory, &rewards); err != nil {
		log.Println("error in getting rewards : ", err)
		return nil
	}

	var cacheReward []models.Rewards
	for _, reward := range rewards {
		rewardProbability := premiumUserRewardMap[reward.RewardId]
		noOfreward := int(rewardProbability * 10)
		for i := 0; i < noOfreward; i++ {
			cacheReward = append(cacheReward, reward)
		}
	}
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(cacheReward), func(i, j int) { cacheReward[i], cacheReward[j] = cacheReward[j], cacheReward[i] })

	key := "premium_user_reward"
	var getRewards []models.Rewards
	if err := s.GetRewardFromCache(key, &getRewards); err != nil || len(rewards) == 0 {
		log.Println("error in getting rewards from cache: ", err)
		return nil
	}
	var allRewards []models.Rewards
	allRewards = append(getRewards, cacheReward...)
	if err := s.SetRewardInCache(key, allRewards); err != nil {
		log.Println("error in setting rewards :", err)
		return err
	}
	log.Println("premium award len :", len(allRewards))
	/*
		for key, ele := range allRewards {
			log.Println("premium reward id: ", key, " reward : ", ele)
		}
	*/
	go s.RemiderMailToUpdateReward("Premium reward set in redis")
	return nil
}

func (s *Service) SetRewardInCache(key string, rewards []models.Rewards) error {

	value, err := json.Marshal(rewards)
	if err != nil {
		return err
	}
	if err := s.Data.SetInRedis(key, value); err != nil {
		log.Println("error in setting in redis : ", err)
		return err
	}
	return nil
}

func (s *Service) GetRewardFromCache(key string, rewards *[]models.Rewards) error {

	err, value := s.Data.GetFromRedis(key)
	if err != nil {
		return err
	}

	//fmt.Println("Cache data", value)

	err1 := json.Unmarshal(value, rewards)
	if err1 != nil {
		return err
	}
	//fmt.Println("reward :", rewards)
	return nil
}

func (s *Service) GetAllApplicableRewards(userCategoryIds []int32, rewards *[]models.Rewards) error {

	var applicable_rewards []models.Rewards

	if err := s.Data.GetRewards(userCategoryIds, &applicable_rewards); err != nil {
		fmt.Println("Error in  getting rewards : ", err)
		return nil
	}

	*rewards = applicable_rewards

	return nil
}

func (s *Service) PickRewardFromAllApplicableRewards(userId int32, isTrial bool, reward *models.Rewards) error {
	var rewards []models.Rewards
	var key string
	if isTrial {
		key = "trial_user_reward"
	} else {
		key = "premium_user_reward"
	}
	if err := s.GetRewardFromCache(key, &rewards); err != nil {
		log.Println("error in getting rewards from cache: ", err)
		return nil
	}

	if len(rewards) > 0 {
		oneTimeRewardIds := []int32{12, 11, 5}
		oneTimeRewardMap := make(map[int32]bool)
		oneTimeRewardMap[12] = true
		oneTimeRewardMap[11] = true
		oneTimeRewardMap[5] = true

		isOneTimeRewardTaken := false
		if err := s.Data.CheckForOneTimeReward(userId, oneTimeRewardIds, &isOneTimeRewardTaken); err != nil {
			log.Println("error in getting one time reward")
		}

		//log.Println("length of reward:", len(rewards))

		var r models.Rewards
		if isOneTimeRewardTaken {
			limit := 0
			for oneTimeRewardMap[rewards[0].RewardId] == true && limit < 10 {
				//log.Println("one time reward taken : ", rewards[0].RewardId)
				rewards = append(rewards, rewards[0])
				rewards = rewards[1:]
				limit++
			}
		}
		r, rewards = rewards[0], rewards[1:]

		//log.Println("length of reward:", len(rewards))

		if err := s.SetRewardInCache(key, rewards); err != nil {
			log.Println("error in setting rewards :", err)
			return err
		}
		*reward = r
	}
	if isTrial && len(rewards) <= 250 {
		if len(rewards)%25 == 0 {
			left := strconv.FormatInt(int64(len(rewards)), 10)
			go s.RemiderMailToUpdateReward("Trial reward is comsuming fast , left : " + left)
		}

		go s.PopulateTrialRewardCron(context.TODO(), nil, nil)

	} else if !isTrial && len(rewards) < 500 {
		if len(rewards)%100 == 0 {
			left := strconv.FormatInt(int64(len(rewards)), 10)
			go s.RemiderMailToUpdateReward("Premium reward is comsuming fast , left  :" + left)
		}

		go s.PopulatePreimumRewardCron(context.TODO(), nil, nil)

	}
	return nil
}

func (s *Service) GenerateScratchCard(reward models.Rewards, UserId int32, bookingReferenceNumber string) error {
	scratchCard := models.ScratchCards{
		RewardId:   reward.RewardId,
		UserId:     UserId,
		VoucherId:  reward.VoucherId,
		ExpiryTime: time.Now().AddDate(0, 0, 3),
	}
	if err := s.Data.CreateScratchCard(&scratchCard); err != nil {
		log.Println("error in creating scratchCard : ", err)
		return err
	}
	scratchCardMetaData := models.ScratchCardMetaData{
		ScratchCardId:          scratchCard.ScratchCardId,
		UserId:                 scratchCard.UserId,
		RewardId:               scratchCard.RewardId,
		BookingReferenceNumber: bookingReferenceNumber,
	}

	if err := s.Data.CreateScratchCardMetaData(&scratchCardMetaData); err != nil {
		log.Println("error in creating scratch card meta data:", err)
		return nil
	}

	return nil
}

func (s *Service) CreateVoucherCode(reward models.Rewards, userId int32, voucherId *int32) error {
	log.Println("creating vouchers")
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	pcl = productPB.NewProductService(serviceList.Product, service.Client())

	voucherCodeData := &productPB.VoucherCodeReq{
		Prefix: "DS",
	}

	// session affinity based call
	response, err := pcl.GenerateUniqueVoucherCode(context.TODO(), voucherCodeData)

	if err != nil {
		fmt.Println(" error : ", err)
		return err
	}
	voucherCode := ""
	if response.Status.Status == "success" {
		voucherCode = response.VoucherCode
	}

	expireTimeStamp, _ := ptypes.TimestampProto(time.Now().AddDate(0, 0, 4))
	voucher := &productPB.Vouchers{
		Code:          voucherCode,
		Description:   "Diwali Sale Scratch Card",
		ApplicantType: 2,
		CouponType:    1,
		DiscountType:  2,
		DiscountValue: reward.RewardValue,
		MinPlanPrice:  5000,
		UsageCount:    1,
		ExpiryDate:    expireTimeStamp,
		UserId:        userId,
	}
	response1, err1 := pcl.CreateOrUpdateVouchers(context.TODO(), voucher)

	if err1 != nil {
		log.Println("CreateVoucherCode: Error in creating or updating voucher details for voucher: %v, err: %v", voucher, err1)
		return err1
	}
	*voucherId = response1.VoucherId
	return nil
}

func (s *Service) GetUserScratchCards(ctx context.Context, req *pb.ScratchCards, res *pb.UserScratchCardResponse) error {
	fmt.Println("getting user scratch cards ---", req)

	var userId int32
	decodeData(req.UserEncodeVal, &userId)
	if userId > 0 {
		req.UserId = userId

	}

	if req.UserId > 0 {
		var scratchCards []models.ScratchCards
		if err := s.Data.GetUserScratchCards(req.UserId, &scratchCards); err != nil {
			fmt.Println("Error in  getting scratch card data..", err)
			return err
		}

		if len(scratchCards) > 0 {
			for _, element := range scratchCards {

				voucher := &pb.Voucher{
					VoucherId: element.VoucherId,
					Code:      element.VoucherCode,
				}

				voucherExpiryDate, _ := ptypes.TimestampProto(element.VoucherExpiryDate)
				if voucherExpiryDate.Seconds > 0 {
					voucher.ExpiryDate = voucherExpiryDate
				}

				reward := &pb.Rewards{
					RewardId:          element.RewardId,
					RewardType:        element.RewardType,
					RewardDescription: element.RewardDescription,
					IsRedirect:        element.IsRedirect,
					ActionLink:        element.ActionLink,
					Voucher:           voucher,
				}

				expiryTime, _ := ptypes.TimestampProto(element.ExpiryTime)

				var is_expired int32

				if expiryTime.GetSeconds() > time.Now().Unix() {
					is_expired = 0
				} else {
					is_expired = 1
				}

				scratchCard := &pb.ScratchCards{
					ScratchCardId:          element.ScratchCardId,
					UserId:                 element.UserId,
					Reward:                 reward,
					IsScratched:            element.IsScratched,
					IsRedeemed:             element.IsRedeemed,
					IsExpired:              is_expired,
					ExpiryTime:             expiryTime,
					BookingReferenceNumber: element.BookingReferenceNumber,
				}

				res.ScratchCards = append(res.ScratchCards, scratchCard)
			}

			status := &pb.Status{
				Status:  "success",
				Message: "successfully fetched user scratch-cards",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "No Scratch Cards found",
			}
			res.Status = status
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "user_id is missing",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) ScratchReward(ctx context.Context, req *pb.ScratchCards, res *pb.Ack) error {
	fmt.Println("Scratching the scratch card --", req)

	if req.ScratchCardId > 0 {
		var isScratched bool
		if err := s.Data.ScratchReward(req.ScratchCardId, &isScratched); err != nil {
			fmt.Println("Error in  scratching reward..", err)
			return err
		}

		if isScratched {
			status := &pb.Status{
				Status:  "success",
				Message: "successfully scratched the reward",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "error in scratching the reward",
			}
			res.Status = status
		}

	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "scratch_card_id is missing",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) ExtendingUserSubscription(subscriptionId int32, extensionDays int32, updatedBy int32, reason string) error {
	fmt.Println("extension to user subscription ...", subscriptionId)

	if err := s.Data.ExtendUserSubscription(subscriptionId, extensionDays, updatedBy, reason); err != nil {
		fmt.Println("Error in  subscription extension..", err)
		return err
	}

	return nil
}

func (s *Service) ExtendUserFreeze(subscriptionId int32, count int32, updatedBy int32, reason string) error {
	fmt.Println("extend subscription freezes ...", subscriptionId)

	if err := s.Data.ExtendUserFreeze(subscriptionId, count, updatedBy, reason); err != nil {
		fmt.Println("Error in  increasing freezes..", err)
		return err
	}

	return nil
}

func (s *Service) ClaimReward(ctx context.Context, req *pb.ScratchCards, res *pb.Ack) error {
	fmt.Println("claiming FITSO reward --", req)

	if req.ScratchCardId > 0 {
		var scratchCard models.ScratchCards
		if err := s.Data.GetScratchCardDetails(req.ScratchCardId, &scratchCard); err != nil {
			fmt.Println("Error in  getting scratch card data..", err)
			return err
		}

		expiryTime, _ := ptypes.TimestampProto(scratchCard.ExpiryTime)
		var is_expired int32
		if expiryTime.GetSeconds() > time.Now().Unix() {
			is_expired = 0
		} else {
			is_expired = 1
		}

		if is_expired == 0 && scratchCard.IsRedeemed == 0 {
			if scratchCard.RewardType == 1 {
				fmt.Println("providing subscription extension")
				timeline_message := "Claiming Reward - " + scratchCard.RewardDescription
				go s.ExtendingUserSubscription(scratchCard.SubscriptionId, scratchCard.RewardValue, scratchCard.UserId, timeline_message)
			} else if scratchCard.RewardType == 2 {
				fmt.Println("providing extra freeze")
				timeline_message := "Claiming Reward - " + scratchCard.RewardDescription
				go s.ExtendUserFreeze(scratchCard.SubscriptionId, scratchCard.RewardValue, scratchCard.UserId, timeline_message)
			}

			var isClaimed bool
			if err := s.Data.ClaimedReward(req.ScratchCardId, &isClaimed); err != nil {
				fmt.Println("Error in  claiming reward..", err)
				return err
			}

			if isClaimed {
				status := &pb.Status{
					Status:  "success",
					Message: "successfully claimed the reward",
				}
				res.Status = status
			} else {
				status := &pb.Status{
					Status:  "failure",
					Message: "error in claiming reward",
				}
				res.Status = status
			}
		} else {
			fmt.Println("This reward already claimed or expired")
			status := &pb.Status{
				Status:  "failure",
				Message: "This reward already claimed, expired or unscratched",
			}
			res.Status = status
		}

	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "scratch_card_id is missing",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) RemiderMailToUpdateReward(message string) error {

	toEmails := [2]string{"<EMAIL>", TECH_MAIL}

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: FITSO_BOT_MAIL,
	}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	emailRequest := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: "Reminder to update reward in redis.",
		Message: message,
	}

	if err := s.Pub.SendBroadcastEmail(emailRequest); err != nil {
		fmt.Println("Error in sending broadcast email")
		return err
	}

	return nil
}

func (s *Service) GetWinnerDetails(ctx context.Context, req *pb.Empty, res *pb.GetWinnerDetailsRes) error {
	gymkitExpiryDates := []string{"2020-11-01", "2020-11-02",
		"2020-11-03", "2020-11-05", "2020-11-06",
		"2020-11-07", "2020-11-08", "2020-11-09",
		"2020-11-10", "2020-11-11", "2020-11-12",
		"2020-11-13", "2020-11-14", "2020-11-15"}
	fitbitExpiryDates := []string{"2020-11-03", "2020-11-06", "2020-11-10", "2020-11-14"}
	iPadExpiryDates := []string{"2020-11-15"}
	var gymkitExpiryDate time.Time
	var fitbitExpiryDate time.Time
	var iPadExpiryDate time.Time
	var err error
	var idx int
	if err, idx = findNextDateFromArray(gymkitExpiryDates); err != nil {
		log.Println("error in getting dates", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "error in converting date",
		}
		res.Status = status
		return err
	} else if idx < len(gymkitExpiryDates) {
		expiryDate := gymkitExpiryDates[idx]
		gymkitExpiryDate, _ = time.Parse(formatYMD, expiryDate)
		gymkitExpiryDate = gymkitExpiryDate.Add(time.Hour*time.Duration(-11) + time.Minute*time.Duration(30))
	}

	if err, idx = findNextDateFromArray(fitbitExpiryDates); err != nil {
		log.Println("error in getting dates", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "error in converting date",
		}
		res.Status = status
		return err
	} else if idx < len(fitbitExpiryDates) {
		expiryDate := fitbitExpiryDates[idx]
		fitbitExpiryDate, _ = time.Parse(formatYMD, expiryDate)
		fitbitExpiryDate = fitbitExpiryDate.Add(time.Hour*time.Duration(-11) + time.Minute*time.Duration(30))
	}

	if err, idx = findNextDateFromArray(iPadExpiryDates); err != nil {
		log.Println("error in getting dates", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "error in converting date",
		}
		res.Status = status
		return err
	} else if idx < len(iPadExpiryDates) {
		expiryDate := iPadExpiryDates[idx]
		iPadExpiryDate, _ = time.Parse(formatYMD, expiryDate)
		iPadExpiryDate = iPadExpiryDate.Add(time.Hour*time.Duration(-11) + time.Minute*time.Duration(30))
	}

	srv, err := sheets.NewService(ctx, option.WithCredentialsFile(client_secret_path), option.WithScopes(sheets.SpreadsheetsScope))

	resp, err := srv.Spreadsheets.Values.Get("1LEdXJq5LutLTibTIX1ndZ3DXFBQezplYuovMMRIr60U", "Sheet1").Do()

	if err != nil {
		log.Printf("GetWinnerDetails : Unable to retrieve data from sheet: %v", err)
		fromEm := &structs.Email{
			Name:         "Fitso",
			EmailAddress: FITSO_BOT_MAIL,
		}

		toEmails := [2]string{TECH_MAIL, ANALYTICS_TEAM_MAIL}

		var toEm []*structs.Email

		for _, emailId := range toEmails {
			toEmVal := &structs.Email{
				EmailAddress: emailId,
			}
			toEm = append(toEm, toEmVal)
		}

		var message string

		message = fmt.Sprintf("GetWinnerDetails : Unable to retrieve data from sheet in userHandler : %v", err)

		emailReq := &structs.EmailRequest{
			From:    fromEm,
			To:      toEm,
			ReplyTo: fromEm,
			Subject: "ALERT : Unable to retrieve data from sheet",
			Message: message,
		}
		go s.Pub.SendBroadcastEmail(emailReq)
	}
	var gymBagWinnerUserNames []string
	var fitbitWinnerUserNames []string
	var iPadWinnerUserNames []string

	if len(resp.Values) == 0 {
		fmt.Println("No data found.")

	} else {
		for idx, row := range resp.Values {
			if idx > 0 {
				if len(row) < 4 {
					continue
				}
				//userIdString := row[0].(string)
				userName := row[1].(string)
				rewardTypeString := row[2].(string)
				winningDateString := row[3].(string)

				//userId, err := strconv.ParseInt(userIdString, 6, 12)
				rewardType, _ := strconv.ParseInt(rewardTypeString, 6, 12)
				winningDate, _ := time.Parse(formatYMD, winningDateString)
				winningDate = winningDate.Add(time.Hour*time.Duration(13) + time.Minute*time.Duration(30))
				curDate := time.Now()

				if winningDate.After(curDate) {
					continue
				}
				if len(userName) > 0 {
					switch rewardType {
					case 1:
						gymBagWinnerUserNames = append(gymBagWinnerUserNames, userName)
					case 2:
						fitbitWinnerUserNames = append(fitbitWinnerUserNames, userName)
					case 3:
						iPadWinnerUserNames = append(iPadWinnerUserNames, userName)
					}
				}

			}

		}

	}
	status := &pb.Status{
		Status:  "success",
		Message: "fetch winner details successfully.",
	}
	gymkitTimeStamp, _ := ptypes.TimestampProto(gymkitExpiryDate)
	fitbitTimeStamp, _ := ptypes.TimestampProto(fitbitExpiryDate)
	iPadTimeStamp, _ := ptypes.TimestampProto(iPadExpiryDate)

	res.Status = status
	res.GymbagExpiryDate = gymkitTimeStamp
	res.FitbitExpiryDate = fitbitTimeStamp
	res.IpadExpiryDate = iPadTimeStamp

	res.GymbagUserNames = gymBagWinnerUserNames
	res.FitbitUserNames = fitbitWinnerUserNames
	res.IpadUserNames = iPadWinnerUserNames

	return nil
}

func (s *Service) RecordPlatinumMemberShip(ctx context.Context, req *pb.PlatinumMemberShipReq, res *pb.PlatinumMemberShipResponse) error {
	log.Println("req--- ", req)
	if (req.UserId) == 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "user id not passed",
		}
		res.Status = status
		return nil
	}
	var platinumMembership models.PlatinumMembership

	if err := s.Data.GetPlatinumMembership(req.UserId, &platinumMembership); err != nil {
		status := &pb.Status{
			Status:  "failure",
			Message: "error in getting platinum membership",
		}
		res.Status = status
		return err
	}
	if platinumMembership.PlatinumMembershipRequestId == 0 {
		platinumMembership = models.PlatinumMembership{
			Status: 2,
			UserID: req.UserId,
		}
		if err := s.Data.CreatePlatinumMembership(&platinumMembership); err != nil {
			return err
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "your have already requested.",
		}
		res.Status = status
		return nil
	}
	status := &pb.Status{
		Status:  "success",
		Message: "recorded successfully",
	}
	res.Status = status
	res.PlatinumMembership = platinumMembership.Proto()

	return nil
}

func (s *Service) GetPlatinumMemberShip(ctx context.Context, req *pb.PlatinumMemberShipReq, res *pb.PlatinumMemberShipResponse) error {
	log.Println("req--- ", req)
	if (req.UserId) == 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "user id not passed",
		}
		res.Status = status
		return nil
	}
	var platinumMembership models.PlatinumMembership

	if err := s.Data.GetPlatinumMembership(req.UserId, &platinumMembership); err != nil {
		status := &pb.Status{
			Status:  "failure",
			Message: "error in getting platinum membership",
		}
		res.Status = status
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "fetched successfully",
	}
	res.Status = status
	if platinumMembership.Status == 2 {
		platinumMembership.PurchaseUrl = "https://www.getfitso.com/purchase/deal/16086284581206"
	}
	res.PlatinumMembership = platinumMembership.Proto()

	return nil
}

func (s *Service) DiwaliOfferNotificationCron(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	log.Println("req--- ", req)

	srv, err := sheets.NewService(ctx, option.WithCredentialsFile(client_secret_path), option.WithScopes(sheets.SpreadsheetsScope))

	resp, err := srv.Spreadsheets.Values.Get("1LEdXJq5LutLTibTIX1ndZ3DXFBQezplYuovMMRIr60U", "Sheet1").Do()

	if err != nil {
		log.Printf("DiwaliOfferNotificationCron: Unable to retrieve data from sheet: %v", err)
		fromEm := &structs.Email{
			Name:         "Fitso",
			EmailAddress: FITSO_BOT_MAIL,
		}

		toEmails := [2]string{TECH_MAIL, ANALYTICS_TEAM_MAIL}

		var toEm []*structs.Email

		for _, emailId := range toEmails {
			toEmVal := &structs.Email{
				EmailAddress: emailId,
			}
			toEm = append(toEm, toEmVal)
		}

		var message string

		message = fmt.Sprintf("DiwaliOfferNotificationCron : Unable to retrieve data from sheet in userHandler : %v", err)

		emailReq := &structs.EmailRequest{
			From:    fromEm,
			To:      toEm,
			ReplyTo: fromEm,
			Subject: "ALERT : Unable to retrieve data from sheet",
			Message: message,
		}
		go s.Pub.SendBroadcastEmail(emailReq)
	}

	if len(resp.Values) == 0 {
		fmt.Println("No data found.")

	} else {
		for idx, row := range resp.Values {
			if idx > 0 {
				if len(row) < 4 {
					continue
				}
				userIdString := row[0].(string)
				userName := row[1].(string)
				rewardTypeString := row[2].(string)
				winningDateString := row[3].(string)
				userId, err := strconv.ParseInt(userIdString, 10, 64)
				if err != nil {
					log.Println("error in getting user id", err)
					continue
				}
				rewardType, _ := strconv.ParseInt(rewardTypeString, 6, 12)

				loc, _ := time.LoadLocation(DefaultLocation)
				winningDate, _ := time.ParseInLocation(formatYMD, winningDateString, loc)

				curDate := now.BeginningOfDay()
				if winningDate.Unix() != curDate.Unix() {
					continue
				}
				var title string
				var text string
				var ctaText string

				if len(userName) > 0 {
					switch rewardType {
					case 1:
						title = "YAYY! YOU JUST WON A PREMIUM GYMKIT IN DIWALI Play & WIN FIESTA! "
						text = "We will be sending out your winnings to your address. Please provide your details."
						ctaText = "https://docs.google.com/forms/d/e/1FAIpQLSeqoc9PS9BQDdtRwNArgaqaONeMaGo57lbToni7EDXNurxWew/viewform?gxids=7628"
					case 2:
						title = "Yayy! You just won a FITBIT in Diwali PLAY & WIN Fiesta!"
						text = "Our team will get in touch with you shortly with more details."
						var userEncodeVal string
						encodeData(strconv.Itoa(int(userId)), &userEncodeVal)
						ctaText = "https://www.getfitso.com/offers_diwali/?utm_source=Notification&utm_medium=Active%20Users%20Booking&utm_campaign=Diwali%20Campaign"
						ctaText = ctaText + "&user_encode_val=" + userEncodeVal + "&user_id=" + strconv.Itoa(int(userId))

					case 3:
						title = "Woah Woah Woah! You have won the bumper prize at FITSO Play & Win fiesta!"
						text = "Congratulation on winning your iPAD.Our team will get in touch with your shortly with more details."
						var userEncodeVal string
						encodeData(strconv.Itoa(int(userId)), &userEncodeVal)
						ctaText = "https://www.getfitso.com/offers_diwali/?utm_source=Notification&utm_medium=Active%20Users%20Booking&utm_campaign=Diwali%20Campaign"
						ctaText = ctaText + "&user_encode_val=" + userEncodeVal + "&user_id=" + strconv.Itoa(int(userId))

					}
				} //send scratch card notification
				fcmStruct := structs.FcmNotificationMessage{
					UserId:     int32(userId),
					Title:      title,
					Text:       text,
					CtaText:    ctaText,
					ExpiryDate: time.Now().AddDate(0, 0, 3).Unix(),
				}
				go s.SendNotificationToUsers(fcmStruct)

			}

		}

	}
	return nil
}

func (s *Service) createToken(ctx context.Context, userId int32, req *pb.DashboardUserDetail) (error, string) {

	var serviceList structs.ServiceConfig
	var asyncServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asyncServiceList)

	service := micro.NewService()

	acl = authPB.NewAuthService(serviceList.Auth, service.Client())

	userData := &authPB.TokenRequest{
		UserId:     userId,
		AppType:    req.AppType,
		AppVersion: req.AppVersion,
	}
	response, err := acl.CreateToken(ctx, userData)
	if err != nil {
		log.Println(err)
		return err, ""
	}
	if response.Status != nil && response.Status.Status == "success" {
		token := response.Token.Token
		return nil, token

	}
	return nil, ""
}

func (s *Service) NotificationForOtpErrors(ctx context.Context, req *pb.Empty, res *pb.Ack) error {

	currentTime := time.Now()
	min := currentTime.Minute()
	var keyHour int
	var keyMinute int
	if min < 15 {
		keyHour = currentTime.Hour() - 1
		keyMinute = 45
	} else {
		keyHour = currentTime.Hour()
		keyMinute = (((min - 15) / 15) * 15) % 60
	}
	keyTime := strconv.Itoa(keyHour) + ":" + strconv.Itoa(keyMinute)
	var generatedOtp int
	var successfulOtp int
	var otpNumbers string
	if err := s.Data.CountOtpgeneratedAndReceived(&generatedOtp, &successfulOtp, keyTime); err != nil {
		log.Println("Error in getting the count for otps received and ", err)
		return err
	}
	if err := s.Data.OtpNumbersGeneratedANDReceived(&otpNumbers, keyTime); err != nil {
		log.Println("Error in getting the count for otps received and ", err)
		return err
	}

	minOtp := (generatedOtp * 25) / 100
	if generatedOtp > 5 && minOtp >= successfulOtp && len(otpNumbers) > 0 {
		var tokenData map[string]int
		if err := json.Unmarshal([]byte(otpNumbers), &tokenData); err != nil {
			return err
		}
		s.SendNotificationForOtpError(generatedOtp, successfulOtp, keyMinute, keyHour, tokenData)
	}

	return nil
}

func (s *Service) SendNotificationForOtpError(otpCount int, otpUsed int, keyMinute int, keyHour int, numbers map[string]int) {

	var subject string
	subject = "OTP service was not working properly"
	message := "Dear Fitso Group,<br><br>"
	message += "OTP Service was not working properly at time between time <b>" + strconv.Itoa(keyHour) + ":" + strconv.Itoa(keyMinute) + " to " + strconv.Itoa(keyHour) + ":" + strconv.Itoa(keyMinute+15) + "</b>.<br>"
	message += "<b>Generated OTP </b>- " + strconv.Itoa(otpCount) + "<br>"
	message += "<b>OTP Used for login </b>- " + strconv.Itoa(otpUsed) + "<br><br>"
	message += "Each Number requested otp following number of times"
	keys := reflect.ValueOf(numbers).MapKeys()
	for _, number := range keys {
		message += "<br>" + number.Interface().(string) + " - " + strconv.Itoa(numbers[number.Interface().(string)])
	}
	message += "<br>Regards,<br>"
	message += "Fitso Bot<br>"
	fromEm := &structs.Email{
		Name:         "Fitso Bot",
		EmailAddress: FITSO_BOT_MAIL,
	}
	toEmails := [2]string{TECH_MAIL, "<EMAIL>"}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		Subject: subject,
		Message: message,
	}
	if err := s.Pub.SendBroadcastEmail(emailReq); err != nil {
		log.Println("Error in sending email ", err)
	}
}

func (s *Service) StoreOtpCount(keyType string) {

	currentTime := time.Now()
	min := currentTime.Minute()
	keyHour := currentTime.Hour()
	keyMinute := ((min / 15) * 15) % 60
	keyTime := strconv.Itoa(keyHour) + ":" + strconv.Itoa(keyMinute)
	if err := s.Data.StoreOtpCount(keyType, keyTime); err != nil {
		log.Println("Error in storing otp count", err)
	}
}

func (s *Service) StoreOtpNumber(keyType string, phone string) {

	currentTime := time.Now()
	min := currentTime.Minute()
	keyHour := currentTime.Hour()
	keyMinute := ((min / 15) * 15) % 60
	keyTime := strconv.Itoa(keyHour) + ":" + strconv.Itoa(keyMinute)
	if err := s.Data.StoreOtpNumber(keyType, keyTime, phone); err != nil {
		log.Println("Error in storing otp count", err)
	}
}

func (s *Service) SubmitCompetitiveUser(ctx context.Context, req *pb.UserInfo, res *pb.Ack) error {

	if req.UserId > 0 {

		userCreateData := models.SealsCompetitiveUser{
			UserId: req.UserId,
		}

		if err := s.Data.SubmitCompetitiveUser(&userCreateData); err != nil {
			log.Println("Error in submitting seals competitive user ...", err)
			status := &pb.Status{
				Status:  "error",
				Message: "Error in submitting competitive user",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "success",
				Message: "submitting competitive user successfully",
			}
			res.Status = status
		}

	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid User-Id",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetUsersOldestCheckInImage(ctx context.Context, req *pb.GetUsersOldestCheckInImageRequest, res *pb.GetUsersOldestCheckInImageResponse) error {

	var count int = len(req.UserIds)
	if count > 0 {
		var batchCount int = count/1000 + 1
		counter := 0
		userImageMap := make(map[string]string)
		for counter < batchCount {
			lastIndex := (counter + 1) * 1000
			if lastIndex > count {
				lastIndex = count
			}
			var currentBatch []int32 = req.UserIds[counter*1000 : lastIndex]
			var userIdsArr []string
			for _, element := range currentBatch {
				userIdsArr = append(userIdsArr, strconv.Itoa(int(element)))
			}
			if len(userIdsArr) != 0 {
				if response, err := s.Data.GetUsersOldestCheckInImage(ctx, userIdsArr); err != nil {
					log.Println("Error in getting user images ...", err)
					if count == lastIndex {
						if len(userImageMap) == 0 {
							status := &pb.Status{
								Status:  "failure",
								Message: "Error in getting user images",
							}
							res.Status = status
						} else {
							status := &pb.Status{
								Status:  "success",
								Message: "images fetched partially",
							}
							res.UserImages = userImageMap
							res.Status = status
						}
					}
				} else {
					if len(response) > 0 {
						for userId, imageUrl := range response {
							userImageMap[userId] = imageUrl
						}
					}
				}
			}
			counter += 1
		}
		if len(userImageMap) == 0 {
			status := &pb.Status{
				Status:  "success",
				Message: "No images found for given user_ids",
			}
			res.Status = status
		} else {
			res.UserImages = userImageMap
			status := &pb.Status{
				Status:  "success",
				Message: "Images fetched successfully",
			}
			res.Status = status
		}
	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "No User Id Passed!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) PublishUserQueryAndSupportMail(ctx context.Context, req *pb.PublishUserQueryRequest, res *pb.Ack) error {

	userID := util.GetUserIDFromContext(ctx)

	if userID == 0 {
		status := &pb.Status{
			Status:  notAuthorized,
			Message: "USER NOT AUTHORIZED",
		}
		res.Status = status

		return nil
	}

	if !validEmail(req.Email) {
		status := &pb.Status{
			Status:  badRequest,
			Message: "Please Enter Correct Email ID",
		}
		res.Status = status
		return nil
	}

	responseBooking := &bookingPB.BookingResponse{}

	if req.BookingId != 0 {

		bcl := util.GetBookingClient()

		bookingData := &bookingPB.Booking{
			BookingId: req.BookingId,
			UserId:    userID,
		}

		responseBookingDetail, err := bcl.GetBookingDetails(ctx, bookingData)

		responseBooking = responseBookingDetail

		if err != nil {
			log.Println("Could not Fetch user booking details for userid : ", userID, " ERROR : ", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not Fetch user booking details in db !",
			}
			res.Status = status

			return err
		}

		if len(responseBooking.Error) > 0 && responseBooking.Error[0].Code == 309 {
			log.Println("Error in Matching Booking data with User Data, Could not Insert User Support Query in DB... ")
			status := &pb.Status{
				Status:  badRequest,
				Message: "No booking with given Booking id exist for the user",
			}
			res.Status = status
			return nil
		}

	}

	publishUserQueryRequest := &models.UserSupportQueries{
		UserId:  userID,
		Message: req.Message,
	}

	if err := s.Data.PublishUserQueryRequestRecord(ctx, publishUserQueryRequest); err != nil {
		log.Println("Error in inserting record for userId : ", userID, " ERROR : ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not insert user query record..",
		}
		res.Status = status
		return err
	}

	if err := s.SendUserSupportQueryMail(ctx, responseBooking, req, userID); err != nil {
		fmt.Println("Error in sending mails to CX ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not send mail to CX ..",
		}
		res.Status = status
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Inserted user query request record successfully and mail sent to CX !..",
	}
	res.Status = status

	return nil
}

func (s *Service) SendUserSupportQueryMail(ctx context.Context, bookingGetData *bookingPB.BookingResponse, userReq *pb.PublishUserQueryRequest, userID int32) error {

	var userDB models.User

	userData := &models.User{
		UserID: userID,
	}

	if err := s.Data.Get(userData, &userDB); err != nil {
		log.Println("Could not get user details for userId : ", userID, " ERROR : ", err)
		return err
	}
	userDB.Email = userReq.Email

	subject := "User Support Query (" + userDB.Name + ", " + userDB.Phone + ", " + strconv.Itoa(int(userDB.UserID)) + ")"

	message := "Dear Fitso Team" + "<br>"
	message += "<br>" + userReq.Message + "<br>"

	userDetails := "<br>"
	userDetails += "<b>User Id</b> - " + strconv.Itoa(int(userDB.UserID)) + "<br>"
	userDetails += "<b>User Name</b> - " + userDB.Name + "<br>"
	userDetails += "<b>User Phone</b> - " + userDB.Phone + "<br>"
	userDetails += "<b>User Email</b> - " + userDB.Email + "<br>"
	message += userDetails

	if userReq.BookingId != 0 && len(bookingGetData.Booking) > 0 {

		bookingDetails := "<br>"
		bookingDetails += "<b>Booking Id</b> -" + strconv.Itoa(int(userReq.BookingId)) + "<br>"
		bookingDetails += "<b>Booking reference no</b> - " + bookingGetData.Booking[0].BookingReferenceNumber + "<br>"
		bookingDetails += "<b>Slot</b> - " + bookingGetData.Booking[0].Slot.Timing + "<br>"
		bookingTime, _ := ptypes.Timestamp(bookingGetData.Booking[0].BookingTime)
		bookingTime, _ = GetLocalDateTime(bookingTime)
		bookingDetails += "<b>Booking date</b> - " + bookingTime.String() + "<br>"
		message += bookingDetails
		if len(bookingGetData.Booking[0].BookingReferenceNumber) > 0 {
			subject += " BRN : " + bookingGetData.Booking[0].BookingReferenceNumber
		}
	}

	appType, _ := util.GetClientIDFromContext(ctx)
	deviceInfo := strings.Split(userReq.DeviceInfo, "&")

	var appVersion string
	var deviceBrand string
	var deviceModel string
	var deviceDev string

	for _, val := range deviceInfo {
		if len(val) == 0 {
			continue
		}
		pair := strings.Split(val, "=")
		if pair[0] == "app_version" {
			appVersion = pair[1]
		} else if pair[0] == "device_brand" {
			deviceBrand = pair[1]
		} else if pair[0] == "device_model" {
			deviceModel = pair[1]
		} else if pair[0] == "device_manufacturer" {
			deviceDev = pair[1]
		}

	}

	appDetails := "<br>"
	appDetails += "<b>" + appType + "</b> - " + appVersion + "<br>"
	appDetails += "<b>Device details</b> - " + "Brand: " + deviceBrand + ", Model: " + strings.Join(strings.Split(deviceModel, "+"), " ") + ", Manufacturer: " + deviceDev + "<br>"

	message += appDetails

	emailId := userReq.Email
	if len(emailId) == 0 && len(userDB.Email) > 0 {
		emailId = userDB.Email
	}

	freshdeskTicketPayload := &pb.FreshdeskTicket{
		UserId:      userDB.UserID,
		Subject:     subject,
		Description: message,
		DeviceId:    userReq.DeviceId,
		Email:       emailId,
		Name:        userDB.Name,
		Phone:       userDB.Phone,
	}

	if err := s.CreateFreshdeskTicket(ctx, freshdeskTicketPayload, &pb.Ack{}); err != nil {
		log.Printf("Error in generating freshdesk ticket for SupportQuery, UserId: %d, err: %v", userDB.UserID, err)
		log.Printf("SupportQuery Details- email: %s,ID: %d", userDB.Email, userDB.UserID)
		return err
	}

	return nil
}

func (s *Service) CreateFreshdeskTicket(ctx context.Context, req *pb.FreshdeskTicket, res *pb.Ack) error {
	headers := map[string]string{
		util.ContentType: "application/json",
	}

	req.Subject = strings.ReplaceAll(req.Subject, "'", "\\'")
	req.Description = strings.ReplaceAll(req.Description, "'", "\\'")

	bodyData := structs.FreshdeskTicketPayload{
		UserId:      req.UserId,
		Subject:     req.Subject,
		Description: req.Description,
		DeviceId:    req.DeviceId,
		Email:       req.Email,
		Name:        req.Name,
		Phone:       req.Phone,
	}

	requestBody, err := json.Marshal(bodyData)
	if err != nil {
		log.Printf("Error in marshalling get onedirect ticket request err %v", err)
		return fmt.Errorf("Error in marshalling get onedirect ticket request err %v", err)
	}

	request := &util.Request{
		Method:      util.MethodTypePost,
		RequestURL:  util.GetFreshdeskTicketCreationEndPoint(),
		Headers:     headers,
		RequestBody: bytes.NewReader(requestBody),
	}
	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("FreshDesk Ticket : Error in creating ticket for UserId: %d, mail: %s, Error Code: %d, err: %v", req.UserId, req.Email, response.Status, err)
		return err
	}

	if response.Status != http.StatusOK {
		log.Printf("Error in creating tickets for UserId: %d, mail: %s, Error Code: %d, err: %v", req.UserId, req.Email, response.Status, response)
		return fmt.Errorf("Error in creating tickets for UserId: %d, mail: %s, err: %v", req.UserId, req.Email, response)
	}

	var responseBody structs.FreshDeskTicketResponse
	if err := json.Unmarshal([]byte(response.Body), &responseBody); err != nil {
		log.Printf("Error in unmarshalling freshdesk ticket creation response err: %v", err)
		return fmt.Errorf("Error in unmarshalling freshdesk ticket creation response err %v", err)
	}
	log.Printf("FreshDesk Ticket got created for user id: %d, email: %s, response: %v", req.UserId, req.Email, responseBody)
	return nil
}

func (s *Service) SendOneDirectMail(ctx context.Context, req *pb.OneDirectMailRequest, res *pb.Ack) error {

	var userDB models.User

	userDB.Name = req.UserDetails.Name
	userDB.Phone = req.UserDetails.Phone
	userDB.Email = req.UserDetails.Email

	if err := s.CreateUpdateOneDirectTicket(ctx, req.Message, req.Subject, userDB, req.Tags); err != nil {
		log.Printf("Error in generating OneDirectTicket for Noshow/Feedback/Offline-refund, UserId:%d OneDirect-Tags:%v", req.UserDetails.UserId, req.Tags)
		log.Printf("OneDirectDetails- email:%s,ID:%d", userDB.Email, req.UserDetails.UserId)
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Mail sent to One Direct successfully !",
	}
	res.Status = status
	return nil

}

func (s *Service) CreateUpdateOneDirectTicket(ctx context.Context, message string, subject string, userDetails models.User, tags string) error {
	host := config.Get("oneDirectTicket", "host").String("")
	authorization := config.Get("oneDirectTicket", "authorization").String("")
	userDetails.Phone = strings.TrimSpace(userDetails.Phone)
	userDetails.Email = strings.TrimSpace(userDetails.Email)
	headers := map[string]string{
		util.Authorization: authorization,
		util.ContentType:   "application/json",
		util.Email:         userDetails.Email,
	}

	var responseBody structs.GetOneDirectTicketResponse
	var customerTicketInfoList []structs.CustomerFieldHashData
	var ticketId int32
	if userDetails.Email != "<EMAIL>" && tags != OneDirectNoShowTicketTag && tags != OneDirectFeedbackTag && false {
		customerFields := make(map[int32][]string)
		customerFields[1] = append(customerFields[1], ONE_DIRECT_EMAIL_CODE)
		customerFields[1] = append(customerFields[1], userDetails.Email)
		for _, value := range customerFields {

			var listOfCustomerFields []structs.CustomInfoSearchListItems
			customInfoSearchList := structs.CustomInfoSearchListItems{
				SelectedValue: value[1],
			}
			listOfCustomerFields = append(listOfCustomerFields, customInfoSearchList)

			customerTicketInfo := structs.CustomerFieldHashData{
				Id:                   value[0],
				CustomInfoSearchList: listOfCustomerFields,
				FieldTypeId:          FIELD_TYPE,
			}
			customerTicketInfoList = append(customerTicketInfoList, customerTicketInfo)
		}
		tag := structs.Tags{
			QueryType: 1,
			TagIds:    tags,
		}
		bodyData := structs.GetOneDirectTicketByEmail{
			BrandId:            ONE_DIRECT_BRAND_ID,
			StatusList:         STATUS_LIST_FOR_TICKETS,
			Tags:               tag,
			TicketActiveStatus: TICKET_ACTIVE_STATUS,
			SortBy:             "lastModifiedAt",
			SortOrder:          "desc",
			CurrentPage:        ONE_DIRECT_PAGE,
			PageSize:           ONE_DIRECT_PAGE_SIZE,
			CustomerFields:     customerTicketInfoList,
		}
		requestBody, err := json.Marshal(bodyData)
		if err != nil {
			log.Printf("Error in marshalling get onedirect ticket request err %v", err)
			return fmt.Errorf("Error in marshalling get onedirect ticket request err %v", err)
		}

		request := &util.Request{
			Method:      util.MethodTypePost,
			RequestURL:  fmt.Sprintf("%s/500001", host),
			Headers:     headers,
			RequestBody: bytes.NewReader(requestBody),
		}
		response, err := util.MakeHTTPRequest(ctx, request)
		if err != nil {
			log.Printf("One Direct Ticket : Error in getting tickets %v, Error Code %d, UserId%d, mail %s", err, response.Status, util.GetUserIDFromContext(ctx), userDetails.Email)
			return err
		}
		if response.Status != http.StatusOK {
			log.Printf("Error in getting tickets for email %s, UserID:%d, Error:%v", userDetails.Email, util.GetUserIDFromContext(ctx), response)
			return fmt.Errorf("Get One Direct Tickets: Error in getting tickets for mail %s Id:%d,Error Code %d", userDetails.Email, util.GetUserIDFromContext(ctx), response.Status)
		}

		if err := json.Unmarshal([]byte(response.Body), &responseBody); err != nil {
			log.Printf("Error in unmarshalling get onedirect ticket response err %v", err)
			return fmt.Errorf("Error in unmarshalling get onedirect ticket response err %v", err)
		}
		for _, ticket := range responseBody.SearchResult {
			if strings.Contains(strings.ToLower(ticket.Subject), "user support query") {
				ticketId = ticket.BrandTicketId
				break
			}
		}
	}
	if ticketId > 0 {

		bodyData := structs.UpdateOneDirectTicketById{
			Status:   1,
			Note:     message,
			TicketId: ticketId,
		}
		requestBody, err := json.Marshal(bodyData)
		if err != nil {
			log.Printf("Error in marshalling update onedirect ticket request err %v", err)
			return fmt.Errorf("Error in marshalling update onedirect ticket request err %v", err)
		}
		request := &util.Request{
			Method:      util.MethodTypePut,
			RequestURL:  fmt.Sprintf("%s/110210", host),
			Headers:     headers,
			RequestBody: bytes.NewReader(requestBody),
		}
		response, err := util.MakeHTTPRequest(ctx, request)
		if err != nil {
			log.Printf("One Direct Ticket : Error in updating tickets %v, Error Code %d, UserId%d, mail %s", err, response.Status, util.GetUserIDFromContext(ctx), userDetails.Email)
			return err
		}
		if response.Status != http.StatusOK {
			log.Printf("Error in updating tickets for email %s, UserID:%d, Error:%v", userDetails.Email, util.GetUserIDFromContext(ctx), response)
			return fmt.Errorf("Get One Direct Tickets: Error in updating tickets for mail %s Id:%d,Error Code %d", userDetails.Email, util.GetUserIDFromContext(ctx), response.Status)
		}
	} else {
		if err := s.CreateOneDiectMailTicket(ctx, message, subject, userDetails, tags); err != nil {
			return err
		}
	}

	return nil
}

func (s *Service) CreateOneDiectMailTicket(ctx context.Context, message string, subject string, userDetails models.User, tags string) error {

	host := config.Get("oneDirectTicket", "host").String("")
	authorization := config.Get("oneDirectTicket", "authorization").String("")
	var customerTicketInfoList []structs.CustomerFieldHashData
	customerFields := make(map[int32][]string)
	customerFields[1] = append(customerFields[1], ONE_DIRECT_PHONE_CODE)
	userDetails.Phone = strings.TrimSpace(userDetails.Phone)
	userDetails.Email = strings.TrimSpace(userDetails.Email)

	if userDetails.Name == "" {
		userDetails.Name = "No Name In DB"
	}

	if userDetails.Phone == "" || len(userDetails.Phone) < 7 {
		userDetails.Phone = DEFAULT_USER_PHONE
		log.Println("Warning : Got empty Phone field, setting default user phone- ", userDetails.Phone)
	}
	customerFields[1] = append(customerFields[1], userDetails.Phone)
	customerFields[2] = append(customerFields[2], ONE_DIRECT_EMAIL_CODE)
	customerFields[2] = append(customerFields[2], userDetails.Email)

	for key, value := range customerFields {

		var listOfCustomerFields []structs.FieldValuesDataListItems
		customerTicketFieldsInfo := structs.FieldValuesDataListItems{
			Value: value[1],
		}
		listOfCustomerFields = append(listOfCustomerFields, customerTicketFieldsInfo)

		customerTicketInfo := structs.CustomerFieldHashData{
			FieldId:             value[0],
			FieldValuesDataList: listOfCustomerFields,
			FieldType:           FIELD_TYPE,
			DefaultType:         key,
		}
		customerTicketInfoList = append(customerTicketInfoList, customerTicketInfo)
	}
	serializedData, err := json.Marshal(customerTicketInfoList)
	if err != nil {
		log.Printf("One Direct Ticket : Error in Customer Field serializing with error: %v", err)
		return err
	}
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	formFields := map[string]string{
		"name":          userDetails.Name,
		"assignedTo":    TICKET_AUTO_ASSIGN_CODE,
		"resourceType":  EMAIL_TICKET,
		"description":   message,
		"customerField": string(serializedData),
		"subject":       subject,
		"source":        FIELD_SOURCE,
		"tags":          tags,
	}
	var fw io.Writer
	for key, value := range formFields {
		fw, err = writer.CreateFormField(key)
		if err != nil {
		}
		_, err = io.Copy(fw, strings.NewReader(value))
		if err != nil {
			return err
		}
	}
	writer.Close()
	headers := map[string]string{
		util.Authorization: authorization,
		util.ContentType:   writer.FormDataContentType(),
	}
	request := &util.Request{
		Method:      util.MethodTypePost,
		RequestURL:  fmt.Sprintf("%s/multipart/123456", host),
		Headers:     headers,
		RequestBody: bytes.NewReader(body.Bytes()),
	}
	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("One Direct Ticket : Error in creating ticket %v, Error Code %d, UserId%d", err, response.Status, util.GetUserIDFromContext(ctx))
		return err
	}
	if response.Status != http.StatusOK {
		log.Printf("Error in OneDirectApi for UserID:%d, Error:%v", util.GetUserIDFromContext(ctx), response)
		return fmt.Errorf("One Direct Ticket : Error in creating ticket for Id:%d,Error Code %d", util.GetUserIDFromContext(ctx), response.Status)
	}
	return nil
}

func (s *Service) GetUserAndChildrenForGivenUserId(ctx context.Context, req *pb.GetUserAndChildrenReq, res *pb.GetUserAndChildrenRes) error {
	if req.UserId > 0 {
		var loggedInUser pb.UserResponse
		err := s.UserGet(ctx, &pb.UserRequest{
			UserId: req.UserId,
		}, &loggedInUser)
		if err != nil {
			log.Printf("Error in handler GetUserAndChildren while getting logged in user data, Error: %v", err)
			return err
		} else {
			if len(loggedInUser.Users) == 1 {
				res.LoggedInUser = &pb.GetUserAndChildrenUser{
					UserId:         loggedInUser.Users[0].UserId,
					Name:           loggedInUser.Users[0].Name,
					Phone:          loggedInUser.Users[0].Phone,
					ProfilePicture: loggedInUser.Users[0].ProfilePictureHash,
					CommsPhone:     loggedInUser.Users[0].CommsPhone,
				}
				var loggedInUserAge models.UserAgeRecord
				err := s.Data.GetUserAgeRecord(req.UserId, &loggedInUserAge)
				if err != nil {
					log.Printf("Error fetching user age record for user id: %d, Error: %v", req.UserId, err)
				} else {
					if loggedInUserAge.Age != 0 {
						res.LoggedInUser.Age = loggedInUserAge.Age
					}
				}
			}
		}
		if req.RemoveChildren {
			res.Status = &pb.Status{
				Status:  "success",
				Message: "Fetched details successfully",
			}
			return nil
		}
		var childUsers pb.ChildUserResponse
		err = s.GetChildUsers(ctx, &pb.UserInfo{
			UserId: req.UserId,
		}, &childUsers)
		if err != nil {
			log.Printf(
				"Error in handler GetUserAndChildren while getting child users' for userId: %d, Error: %v",
				req.UserId,
				err,
			)
		} else {
			if childUsers.Status.Status == "success" && len(childUsers.AllConnectedUsers) > 1 {
				for _, child := range childUsers.AllConnectedUsers {
					if child.UserId != req.UserId {
						var childUserData pb.UserResponse
						err = s.UserGet(ctx, &pb.UserRequest{
							UserId: child.UserId,
						}, &childUserData)
						var age int32
						var phone string
						var commsPhone string
						var profilePicture string
						if err != nil {
							log.Printf(
								"Error in handler GetUserAndChildrenForGivenUserId while getting data for child userId: %d of userId: %d, Error: %v",
								child.UserId,
								req.UserId,
								err,
							)
							continue
						} else {
							if len(childUserData.Users) == 1 {
								phone = childUserData.Users[0].Phone
								commsPhone = childUserData.Users[0].CommsPhone
								profilePicture = childUserData.Users[0].ProfilePictureHash
								var childUserAge models.UserAgeRecord
								err := s.Data.GetUserAgeRecord(child.UserId, &childUserAge)
								if err != nil {
									log.Printf("Error fetching user age record for user id: %d, Error: %v", child.UserId, err)
								} else {
									if childUserAge.Age != 0 {
										age = childUserAge.Age
									}
								}
							}
						}
						childToAppend := &pb.GetUserAndChildrenUser{
							UserId:         child.UserId,
							Name:           child.Name,
							Phone:          phone,
							ProfilePicture: profilePicture,
							CommsPhone:     commsPhone,
						}
						if age != 0 {
							childToAppend.Age = age
						}
						res.Children = append(res.Children, childToAppend)
					}
				}
			}
		}
		res.Status = &pb.Status{
			Status:  "success",
			Message: "Fetched details successfully",
		}
	} else {
		res.Status = &pb.Status{
			Status:  "failure",
			Message: "Cannot fetch details for empty userId",
		}
	}
	return nil
}

// GetUserMembershipTypeDetail returns the type of membership logged in user has, trial, single membership, child memberships
func (s *Service) GetUserMembershipTypeDetail(ctx context.Context, req *pb.Empty, res *pb.GetUserMembershipTypeDetailResponse) error {
	loggedInUser := util.GetUserIDFromContext(ctx)

	// Check if user is trial user
	trialDetails := &pb.TrialDetailsResponse{}
	err := s.GetTrialDetails(ctx, &pb.UserRequest{UserId: loggedInUser}, trialDetails)
	if err != nil {
		log.Printf("Error while GetTrialDetails: user_id: %d, err: %v", loggedInUser, err)
		res.Status = &pb.Status{
			Status:  "failure",
			Message: "Cannot fetch trial details",
		}
		return err
	}

	if trialDetails.TrialDetails != nil &&
		trialDetails.TrialDetails.IsTrialActive &&
		trialDetails.TrialDetails.TrialSessionsLeft != 0 {
		res.Status = &pb.Status{
			Status:  "success",
			Message: "User is trial user",
		}
		res.Type = pb.MembershipType_TRIAL
		return nil
	}

	// Check if user has child memberships orr single membership or no membership
	childUsers := &pb.ChildUserResponse{}
	err = s.GetChildUsers(ctx, &pb.UserInfo{UserId: loggedInUser}, childUsers)
	if err != nil {
		log.Printf("Error while GetChildUsers: user_id: %d, err: %v", loggedInUser, err)
		res.Status = &pb.Status{
			Status:  "failure",
			Message: "Cannot fetch user children details",
		}
		return err
	}

	var childrens []int32
	if childUsers.Status.Status == "success" {
		for _, respConnectedUser := range childUsers.AllConnectedUsers {
			if respConnectedUser.ActiveSubscriptionCount > 0 {
				userID := respConnectedUser.UserId
				childrens = append(childrens, userID)
			}
		}
	}

	if len(childrens) == 1 && childrens[0] == loggedInUser {
		res.Type = pb.MembershipType_SINGLE_MEMBERSHIP
	} else if len(childrens) > 1 {
		res.Type = pb.MembershipType_CHILD_MEMBERSHIP
	} else {
		res.Type = pb.MembershipType_NON_MEMBER
	}

	res.Status = &pb.Status{
		Status:  "success",
		Message: "User Membership Type is set",
	}
	return nil
}

func (s *Service) GetUserAgeRecord(ctx context.Context, req *pb.GetUserAgeRecordReq, res *pb.GetUserAgeRecordRes) error {
	if req.UserId > 0 {
		var existingAgeRecord models.UserAgeRecord
		err := s.Data.GetUserAgeRecord(req.UserId, &existingAgeRecord)
		if err != nil {
			log.Printf("Error in handler GetUserAgeRecord while getting user age record for userId: %d, Error: %v", req.UserId, err)
			return err
		} else {
			res.Age = existingAgeRecord.Age
			return nil
		}
	} else {
		return errors.New("User id cannot be empty")
	}
	return nil
}

// Creates a record in user_age_record table
func (s *Service) CreateUserAgeRecord(ctx context.Context, req *pb.CreateUserAgeRecordReq, res *pb.Empty) error {
	if req.UserId > 0 && req.Age > 0 {
		ageRecord := models.UserAgeRecord{
			UserId:    req.UserId,
			Age:       req.Age,
			DateAdded: time.Now(),
		}
		if err := s.Data.SaveUserAge(ctx, &ageRecord); err != nil {
			log.Printf("Error in handler CreateUserAgeRecord while saving age of userId: %d, Error: %v", req.UserId, err)
			return errors.New("Failed to save user age")
		}
	} else {
		return errors.New("UserId or Age is empty")
	}
	return nil
}

func (s *Service) CheckUserIsChildUser(ctx context.Context, req *pb.UserChildMappingRequest, res *pb.UserChildMappingResponse) error {

	condition := &models.UserChildAccountMapping{
		UserId:      req.UserId,
		ChildUserId: req.ChildUserId,
		DeletedFlag: int32(0),
	}
	if err := s.Data.CheckIfUserChildMappingExists(ctx, condition, res); err != nil {
		log.Printf("Error in handler CheckUserIsChildUser for userId: %d, Error: %v", req.UserId, err)
		return err
	}
	return nil
}

func (s *Service) CreateOrUpdateShortNotice(ctx context.Context, req *pb.ShortNotice, res *pb.CreateOrUpdateShortNoticeResponse) error {
	err := s.validateShortNoticeEntry(ctx, req, &pb.Ack{})
	if err != nil {
		log.Printf("Error in validating short notice entry: %s", err.Error())
		status := &pb.Status{
			Status:  failed,
			Message: err.Error(),
		}
		res.Status = status
		return nil
	}

	shortNoticePointsArr := req.ShortNoticePoints
	shortNoticePoints := strings.Join(shortNoticePointsArr, "\n")

	cityIdsArray := removeDuplicateValues(req.CityIds)
	userCategoriesArray := removeDuplicateValues(req.UserCategories)

	var isDismissible int32
	if req.IsDismissible {
		isDismissible = 1
	}

	createShortNotice := &models.ShortNotices{
		ShortNoticeId:           req.ShortNoticeId,
		Title:                   req.Title,
		Subtitle:                req.Subtitle,
		BottomSheetHeader:       req.BottomSheetHeader,
		ShortNoticePoints:       shortNoticePoints,
		BackgroundColor:         req.BackgroundColor,
		ActionId:                req.ActionId,
		ActionUrl:               req.ActionUrl,
		ActionUrlPostbackParams: req.ActionUrlPostbackParams,
		ActionContent:           req.ActionContent,
		ActionContentColor:      req.ActionContentColor,
		StartTime:               req.StartTime,
		EndTime:                 req.EndTime,
		AllCitiesFlag:           req.AllCitiesFlag,
		Priority:                req.Priority,
		IsDismissible:           isDismissible,
		CreatedBy:               req.CreatedBy,
		UpdatedBy:               req.UpdatedBy,
	}

	if err := s.Data.CreateOrUpdateShortNotice(ctx, createShortNotice); err != nil {
		log.Printf("Error in CreateOrUpdateShortNotice: %v", err)
		return errors.New(fmt.Sprintf("error creating short notice: %v", err))
	}

	if err := s.Data.CreateShortNoticeUserCategoryMapping(createShortNotice.ShortNoticeId, userCategoriesArray); err != nil {
		log.Printf("Error in CreateShortNoticeUserCategoryMapping: %v", err)
		return errors.New(fmt.Sprintf("error creating short notice user_category mapping: %v", err))
	}

	if err := s.Data.CreateShortNoticeCityMapping(createShortNotice.ShortNoticeId, cityIdsArray); err != nil {
		log.Printf("Error in CreateShortNoticeZoneMapping: %v", err)
		return errors.New(fmt.Sprintf("error creating short notice city mapping: %v", err))
	}

	status := &pb.Status{
		Status:  success,
		Message: "Recorded successfully!",
	}
	res.Status = status
	return nil
}

func (s *Service) validateShortNoticeEntry(ctx context.Context, req *pb.ShortNotice, res *pb.Ack) error {
	if len(req.Title) <= 0 {
		return errors.New("Short Notice Title can not be empty")
	}
	if len(req.Subtitle) <= 0 {
		return errors.New("Short Notice Subtitle can not be empty")
	}
	if len(req.ShortNoticePoints)*len(req.BottomSheetHeader) == 0 && len(req.ShortNoticePoints)+len(req.BottomSheetHeader) != 0 {
		return errors.New("Bottom Sheet Header & Short Notice points must be empty or both must have value")
	}
	if req.ActionId > 0 {
		reqData := &pb.Suggestion{
			ActionId:          req.ActionId,
			CtaText:           req.ActionUrl,
			CtaPostbackParams: req.ActionUrlPostbackParams,
		}
		// validating deeplink url
		err := s.ValidateSuggestionDeeplinkCtaText(ctx, reqData, res)
		if err != nil {
			return err
		}
		//validating deeplink postback params
		err = s.ValidateSuggestionDeeplinkCtaPostbackParams(ctx, reqData, res)
		if err != nil {
			return err
		}
	}

	if req.StartTime == 0 || req.EndTime == 0 {
		return errors.New("Short Notice Start Time or End Time can not be zero")
	}

	if req.EndTime < req.StartTime {
		return errors.New("Short Notice End Time can not be before Start Time")
	}

	if req.AllCitiesFlag == 0 && len(req.CityIds) == 0 && len(req.ZoneIds) == 0 {
		return errors.New("All cities flag and number of selected cities/zones both can not be zero")
	}

	if len(req.UserCategories) == 0 {
		return errors.New("Atleast one user category must be selected")
	}

	return nil
}

func (s *Service) BatchGetShortNotice(ctx context.Context, req *pb.BatchGetShortNoticeRequest, res *pb.BatchGetShortNoticeResponse) error {
	fmt.Println("req --- ", req)
	var shortNotices []models.ShortNotices

	userCategoriesArray := removeDuplicateValues(req.UserCategories)

	getShortNoticeCond := &models.ShortNotices{
		UserCategoryIds: userCategoriesArray,
		IsActive:        req.IsActive,
	}

	if len(req.ZoneIds) > 0 {
		getShortNoticeCond.ZoneIds = removeDuplicateValues(req.ZoneIds)
	}

	if len(req.CityIds) > 0 {
		getShortNoticeCond.CityIds = GetStringFromIntegerArray(removeDuplicateValues(req.CityIds), ",")
	}

	if err := s.Data.BatchGetShortNoticeForDashboard(ctx, &shortNotices, getShortNoticeCond); err != nil {
		return errors.New(fmt.Sprintf("error getting short notices: %v", err))
	}

	status := &pb.Status{
		Status:  success,
		Message: "Fetched successfully!",
	}

	for _, element := range shortNotices {
		shortNoticePointsStr := element.ShortNoticePoints
		var shortNoticePoints []string
		if len(shortNoticePointsStr) > 0 {
			shortNoticePoints = strings.Split(shortNoticePointsStr, "\n")
		}
		userCategories := GetIntegerArrayFromCommaSeparatedString(element.UserCategories)
		cityIds := GetIntegerArrayFromCommaSeparatedString(element.CityIds)
		zoneIds := GetIntegerArrayFromCommaSeparatedString(element.ZoneIdsStr)
		isDismissible := false
		if element.IsDismissible == 1 {
			isDismissible = true
		}
		shortNoticeData := &pb.ShortNotice{
			ShortNoticeId:           element.ShortNoticeId,
			Title:                   element.Title,
			Subtitle:                element.Subtitle,
			BackgroundColor:         element.BackgroundColor,
			ActionContent:           element.ActionContent,
			ActionContentColor:      element.ActionContentColor,
			BottomSheetHeader:       element.BottomSheetHeader,
			ShortNoticePoints:       shortNoticePoints,
			ActionId:                element.ActionId,
			ActionUrl:               element.ActionUrl,
			ActionUrlPostbackParams: element.ActionUrlPostbackParams,
			StartTime:               element.StartTime,
			EndTime:                 element.EndTime,
			UserCategories:          userCategories,
			AllCitiesFlag:           element.AllCitiesFlag,
			CityIds:                 cityIds,
			ZoneIds:                 zoneIds,
			Priority:                element.Priority,
			IsDismissible:           isDismissible,
		}

		res.ShortNotices = append(res.ShortNotices, shortNoticeData)
	}

	res.Status = status
	return nil
}

func (s *Service) UpdateUserDetails(ctx context.Context, req *pb.UpdateUserDetailsRequest, res *pb.Ack) error {
	upData := &models.User{
		Phone:  req.Phone,
		UserID: req.UserId,
	}
	var updateSuccessful bool
	if err := s.Data.UpdateUserDetails(upData, &updateSuccessful); err != nil {
		log.Println("Error in updating user details.")
		return err
	}
	if updateSuccessful {
		status := &pb.Status{
			Status:  success,
			Message: "Updated successfully!",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) GetOffer(ctx context.Context, req *pb.Empty, res *pb.GetOfferResponse) error {
	var userCategoryIds []int32
	userId := util.GetUserIDFromContext(ctx)
	cityId := util.GetCityIDFromContext(ctx)
	var preLoginUserCategory int32 = 11

	if err := s.GetUserCategoriesDetails(ctx, userId, MASTERKEY_PRODUCT_CATEGORY_ID, cityId, ALL_USER_CATEGORY, &userCategoryIds); err != nil {
		return err
	}

	res.ShowOffers = false
	if Contains(userCategoryIds, preLoginUserCategory) {
		res.ShowOffers = true
	}
	return nil
}

func (s *Service) SaveUserFeedback(ctx context.Context, req *pb.UserRequest, res *pb.Status) error {
	err := s.Data.SaveUserFeedback(ctx, req.UserId)
	if err != nil {
		log.Printf("Unable to add feedback for userId: %d, error: %v", req.UserId, err)
		return err
	}

	res.Status = success
	return nil
}

func (s *Service) GetUserAppFeedbackStatus(ctx context.Context, req *pb.Empty, res *pb.GetAppFeedbackStatus) error {
	userID := util.GetUserIDFromContext(ctx)
	data, err := s.Data.GetUserAppFeedback(ctx, userID)

	if err != nil {
		log.Printf("error in fetching user's app feedback data for user %d, err: %v", userID, err)

		return err
	}

	res.Status = data.FeedbackGiven

	return nil
}

func (s *Service) GetEditableDetailsForAcademyUser(ctx context.Context, req *pb.User, res *pb.EditableDetailsResponse) error {
	userID := util.GetUserIDFromContext(ctx)
	if req.UserId == 0 {
		status := &pb.Status{
			Status:  failed,
			Message: "User Id cannot be empty",
		}
		res.Status = status
		return nil
	}
	if userID == 0 {
		res.Status = &pb.Status{
			Status:  notAuthorized,
			Message: "not Authorized",
		}
		return nil
	}

	if userID != req.UserId {
		condition := &models.UserChildAccountMapping{
			UserId:      userID,
			ChildUserId: req.UserId,
			DeletedFlag: int32(0),
		}
		response := &pb.UserChildMappingResponse{}
		if err := s.Data.CheckIfUserChildMappingExists(ctx, condition, response); err != nil {
			log.Printf("Error in handler GetEditableUserDetails for userId: %d, Error: %v", req.UserId, err)
			return fmt.Errorf("Error in getting user child mapping for Id %d, error %v", req.UserId, err)
		}
		if response.UserId != userID {
			log.Printf("Error in getting child mapping relation for UserId: %d and Child Id %d", userID, req.UserId)
			status := &pb.Status{
				Status:  failed,
				Message: "Not valid child User",
			}
			res.Status = status
			return nil
		}
	}

	userData := &models.User{
		UserID: req.UserId,
	}
	var userDB models.User
	if err := s.Data.Get(userData, &userDB); err != nil {
		log.Printf("Error in getting user details for userId %d, error %v", req.UserId, err)
		return fmt.Errorf("Error in getting user details for id %d, error %v", req.UserId, err)
	}
	if userDB.UserID == 0 {
		log.Printf("cannot get user details for UserID %d", req.UserId)
		res.Status = &pb.Status{
			Status:  failed,
			Message: "failed",
		}
		return nil
	}

	var existingAgeRecord models.UserAgeRecord
	err := s.Data.GetUserAgeRecord(req.UserId, &existingAgeRecord)
	if err != nil {
		log.Printf("Error in handler GetEditableUserDetails while getting user age record for userId: %d, Error: %v", req.UserId, err)
		return fmt.Errorf("Error in getting user age for id %d, error %v", req.UserId, err)
	}
	var phone string
	if len(userDB.Phone) > 0 {
		phone = userDB.Phone
	} else {
		phone = userDB.CommsPhone
	}

	userDetails := &pb.User{
		UserId: userDB.UserID,
		Phone:  phone,
		Name:   userDB.Name,
		Age:    existingAgeRecord.Age,
	}
	res.UserData = userDetails

	editInfo := &pb.EditInfo{
		Title: "Edit member",
		BottomButtonStates: &pb.BottomButtonStates{
			DisabledText:  "Save details",
			CompletedText: "Save details",
			EditedText:    "Save details",
		},
	}
	if req.UserId == userID || len(phone) > 0 {

		editInfo.Sections = []*pb.FormField{
			{
				Type:        "age",
				Placeholder: "Age (in years)",
				IsOptional:  false,
				MinValue:    ACADEMY_USER_MIN_AGE,
				MaxValue:    ACADEMY_USER_MAX_AGE,
				States: &pb.FormFieldState{
					Empty:       "Age cannot be empty",
					Always:      fmt.Sprintf("age must be between %d-%d years", ACADEMY_USER_MIN_AGE, ACADEMY_USER_MAX_AGE),
					MinAgeError: fmt.Sprintf("Age cannot be less than %d years", ACADEMY_USER_MIN_AGE),
					MaxAgeError: fmt.Sprintf("Age cannot be more than %d years", ACADEMY_USER_MAX_AGE),
				},
			},
		}
		nonEditableDetails := &pb.User{
			Name:  userDB.Name,
			Phone: phone,
		}
		res.NonEditableUserDetails = nonEditableDetails
		res.EditInfo = editInfo
	} else {

		editInfo.Sections = []*pb.FormField{
			{
				Type:        "name",
				Placeholder: "Full Name",
				IsOptional:  false,
				States: &pb.FormFieldState{
					Empty: "Name cannot be empty",
				},
			},
			{
				Type:        "age",
				Placeholder: "Age (in years)",
				IsOptional:  false,
				MinValue:    ACADEMY_USER_MIN_AGE,
				MaxValue:    ACADEMY_USER_MAX_AGE,
				States: &pb.FormFieldState{
					Empty:       "Age cannot be empty",
					Always:      fmt.Sprintf("age must be between %d-%d years", ACADEMY_USER_MIN_AGE, ACADEMY_USER_MAX_AGE),
					MinAgeError: fmt.Sprintf("Age cannot be less than %d years", ACADEMY_USER_MIN_AGE),
					MaxAgeError: fmt.Sprintf("Age cannot be more than %d years", ACADEMY_USER_MAX_AGE),
				},
			},
			{
				Type:        "mobile",
				Placeholder: "Mobile number",
				IsOptional:  true,
				MinValue:    PHONE_MIN_LENGTH,
				States: &pb.FormFieldState{
					Error: "Mobile number must be 10 digits",
				},
			},
		}
		res.EditInfo = editInfo
	}
	return nil
}

func (s *Service) CreateVideo(ctx context.Context, req *pb.CreateVideoRequest, res *pb.CreateVideoResponse) error {
	if len(req.VideoUrl) == 0 {
		res.Status = &pb.Status{
			Status:  badRequest,
			Message: "Video URL is empty",
		}
		return nil
	}
	videoReq := &models.Video{
		Url: req.VideoUrl,
	}

	if req.ThumbnailId > 0 {
		videoReq.ThumbnailId = req.ThumbnailId
	}

	if req.AspectRatio > 0 {
		videoReq.AspectRatio = req.AspectRatio
	}

	videoId, err := s.Data.CreateVideo(ctx, videoReq)
	if err != nil {
		log.Printf("Error in CreateVideo while creating video %v", err)
		return err
	}
	res.VideoId = videoId
	res.Status = &pb.Status{
		Status: success,
	}
	return nil
}

func (s *Service) SetCultAppUsageInRedis(ctx context.Context, req *pb.CultAppUsage, res *pb.Ack) error {
	redisKey := GetRedisKeyForCultAppUsage(req.UserId)
	value, err := json.Marshal(&req)
	if err != nil {
		log.Printf("SetCultAppUsageInRedis: Error in marshalling cult app usage data for redisKey: %s, err: %v", redisKey, err)
		return err
	}
	sessionTime := time.Duration(3 * 24 * time.Hour) //3 days
	if err := s.Data.SetInRedisWithTime(redisKey, value, sessionTime); err != nil {
		log.Printf("SetCultAppUsageInRedis: Error in setting in redis for user id: %d, city id: %d, err: %v", req.UserId, req.CityId, err)
		return err
	}
	return nil
}

func (s *Service) GetCultAppUsage(ctx context.Context, req *pb.CultAppUsageReq, res *pb.CultAppUsage) error {
	redisKey := GetRedisKeyForCultAppUsage(req.UserId)
	err, cachedDataBytes := s.Data.GetFromRedis(redisKey)
	if err != nil {
		log.Printf("GetCultAppUsage: Error in getting cult app usage data for redisKey: %s, err: %v", redisKey, err)
		return err
	}
	if len(string(cachedDataBytes)) > 0 {
		err = json.Unmarshal(cachedDataBytes, res)
		if err != nil {
			log.Printf("GetCultAppUsage: Error in unmarshalling cult app usage data for redisKey: %s, err: %v", redisKey, err)
			return err
		}
		return nil
	}

	headers := map[string]string{
		util.ContentType: util.JsonContentType,
	}
	request := &util.Request{
		Method:      util.MethodTypeGet,
		RequestURL:  util.GetCultAppUsageApiEndpoint(req.UserId, req.CityId),
		Headers:     headers,
		RequestBody: bytes.NewReader([]byte(nil)),
	}

	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("CheckIfCultAppUser: Error in checking cult app usage for user id: %d, cityId: %d with err: %v", req.UserId, req.CityId, err)
		return err
	}
	if response.Status != 200 {
		log.Printf("CheckIfCultAppUser: Error in getting cult app usage for user id: %d, cityId: %d with response body: %s", req.UserId, req.CityId, response.Body)
		return errors.New(response.Body)
	}
	var result structs.CultAppUsageApiResponse
	err = json.Unmarshal([]byte(response.Body), &result)
	if err != nil {
		log.Printf("CheckIfCultAppUser: Error in unmarshalling cult app usage api response for user id: %d, cityId: %d with response body: %s, err: %v", req.UserId, req.CityId, response.Body, err)
		return err
	}
	res.BlockFitsoApp = result.BlockFitsoApp
	res.Message = result.Message
	if result.BlockFitsoApp {
		cultAppUsageProto := &pb.CultAppUsage{
			UserId:        req.UserId,
			Message:       res.Message,
			BlockFitsoApp: res.BlockFitsoApp,
		}
		if err := s.SetCultAppUsageInRedis(ctx, cultAppUsageProto, &pb.Ack{}); err != nil {
			log.Printf("Could not set cult app usage data in redis for user id: %d, city id: %d, Error:%v", req.UserId, req.CityId, err)
			return err
		}
	}
	return nil
}

func (s *Service) GetUserBlacklistStatus(ctx context.Context, req *pb.UserBlacklistStatusReq, res *pb.CultAppBlacklistApiResponse) error {
	redisKey := GetRedisKeyForBlacklistUser(req.UserId)
	err, cachedDataBytes := s.Data.GetFromRedis(redisKey)
	if err != nil {
		log.Printf("GetUserBlacklistStatus: Error in getting cult app blacklist status for redisKey: %s, err: %v", redisKey, err)
		return err
	}
	if len(string(cachedDataBytes)) > 0 {
		err = json.Unmarshal(cachedDataBytes, res)
		if err != nil {
			log.Printf("GetUserBlacklistStatus: Error in unmarshalling cult app blacklist status for redisKey: %s, err: %v", redisKey, err)
			return err
		}
		return nil
	}

	userData := &models.User{
		UserID: req.UserId,
	}
	var userDB models.User
	if err := s.Data.Get(userData, &userDB); err != nil {
		log.Printf("Error getting user details of userID: %d, err: %v", req.UserId, err)
		return err
	}

	if userDB.CultUserId <= 0 {
		res.IsUserBlacklisted = false
		res.Message = "Not a cult user"
		return nil
	}

	headers := map[string]string{
		util.ContentType: util.JsonContentType,
	}
	request := &util.Request{
		Method:      util.MethodTypeGet,
		RequestURL:  util.GetCultAppBlacklistApiEndpoint(userDB.CultUserId),
		Headers:     headers,
		RequestBody: bytes.NewReader([]byte(nil)),
	}

	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("GetUserBlacklistStatus: Error in checking blacklist user status for cult user id: %d with err: %v", userDB.CultUserId, err)
		return err
	}
	if response.Status != 200 {
		log.Printf("GetUserBlacklistStatus: Error in getting blacklist user status for cult user id: %d with response body: %s", userDB.CultUserId, response.Body)
		return errors.New(response.Body)
	}
	var result structs.CultAppBlacklistApiResponse
	err = json.Unmarshal([]byte(response.Body), &result)
	if err != nil {
		log.Printf("GetUserBlacklistStatus: Error in unmarshalling blacklist user status api response for cult user id: %d with response body: %s, err: %v", userDB.CultUserId, response.Body, err)
		return err
	}
	res.IsUserBlacklisted = result.IsUserBlacklisted
	res.Message = result.Message

	value, err := json.Marshal(&res)
	if err != nil {
		log.Printf("GetUserBlacklistStatus: Error in marshalling blacklist user status data for redisKey: %s, err: %v", redisKey, err)
		return err
	}
	sessionTime := time.Duration(7 * 24 * time.Hour) //7 days
	if err := s.Data.SetInRedisWithTime(redisKey, value, sessionTime); err != nil {
		log.Printf("GetUserBlacklistStatus: Error in setting blacklist user status in redis for user id: %d, err: %v", req.UserId, err)
		return err
	}
	return nil
}

func (s *Service) RecordUserClickAction(ctx context.Context, req *pb.UserEventRequest, res *pb.Ack) error {

	headers := map[string]string{
		util.ContentType: util.JsonContentType,
	}
	request := &util.Request{
		Method:      util.MethodTypeGet,
		RequestURL:  util.RecordUserClickActionApiEndpoint(req.UserId, req.HaveMembership),
		Headers:     headers,
		RequestBody: bytes.NewReader([]byte(nil)),
	}

	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("RecordUserClickAction: Error in sending event click for user id: %d, HaveMembership: %v with err: %v", req.UserId, req.HaveMembership, err)
	}
	if response.Status != 200 {
		log.Printf("RecordUserClickAction: Error in sending event click for user id: %d, HaveMembership: %v with response status: %v", req.UserId, req.HaveMembership, response.Status)
	}
	return nil
}
