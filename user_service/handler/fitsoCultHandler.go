package userHandler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	models "bitbucket.org/jogocoin/go_api/user_service/data/models"
	"bitbucket.org/jogocoin/go_api/user_service/internal/util"
	//productPB "bitbucket.org/jogocoin/go_api/user_service/proto/product"
	pb "bitbucket.org/jogocoin/go_api/user_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/user_service/structs"
	"golang.org/x/net/context"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

func (s *Service) CheckOrCreateCultSignUpUser(ctx context.Context, req *pb.CultSignUpUserReq, res *pb.CultSignUpUserResponse) error {

	var userDetails models.User
	userData := &models.User{
		Phone: req.Phone,
	}

	if err := s.Data.Get(userData, &userDetails); err != nil {
		return fmt.Errorf("Error in checking user for phone %v, err %v", req.Phone, err)
	}
	//productClient := util.GetProductClient()

	status := &pb.Status{
		Status:  "success",
		Message: "Request Successful",
	}
	if userDetails.UserID > 0 {
		//check membership active or not
		/*latestSubDetails, err := productClient.GetMKActiveSubscriptionsForUser(ctx, &productPB.Subscription{
			UserId: userDetails.UserID,
		})
		if err != nil {
			log.Println("Cannot find latest sub end date for user: ", userDetails.UserID, err)
			return fmt.Errorf("Cannot find latest sub end date for user %v, err %v ", userDetails.UserID, err)
		}*/
		if userDetails.CultUserId <= 0 {
			var updateSuccessful bool
			var message string
			data := &pb.UserInfo{
				UserId:     userDetails.UserID,
				CultUserId: req.CultUserId,
			}
			if err := s.Data.UpdateUserInfo(data, &updateSuccessful, &message); err != nil {
				log.Printf("error in updating user details: %v , %v", err, message)
				return fmt.Errorf("error in updating user details: %v , %v", err, message)
			}
		}
		/*if latestSubDetails != nil && latestSubDetails.Subscriptions != nil {
			for _, subscription := range latestSubDetails.Subscriptions {
				if subscription.DurationInDays != 90 && subscription.DurationInDays != 180 && subscription.DurationInDays != 365 {
					continue
				}
				cityId := FITSO_CULT_CITY_ID_MAPPING[subscription.LocationIdV2]
				res.Memberships = append(res.Memberships, &pb.MembershipDetails{
					CultUserId:         req.CultUserId,
					Phone:              req.Phone,
					CountryCallingCode: "+91",
					StartDate:          subscription.StartDate,
					CultCityId:         cityId,
					Duration:           subscription.DurationInDays,
				})
			}
			res.Status = status
		}*/
		//send response
	} else {
		//create user
		date, _ := time.Parse("2006-01-02", req.Dob)
		newUser := &models.User{
			Name:       req.Name,
			Phone:      req.Phone,
			CommsPhone: req.Phone,
			CultUserId: req.CultUserId,
			Birthday:	date,
		}
		if err := s.Data.Create(newUser); err != nil {
			log.Printf("error in creating user for phone %v, error: %v", req.Phone, err)
			return fmt.Errorf("error in creating user for phone %v, error: %v", req.Phone, err)
		}
	}
	res.Status = status

	return nil
}

func (s *Service) AddCultUsersFromSheet(ctx context.Context, req *pb.Empty, res *pb.Ack) error {

	srv, err := sheets.NewService(ctx, option.WithCredentialsFile(client_secret_path), option.WithScopes(sheets.SpreadsheetsScope))
	if err != nil {
		log.Printf("AddCultUsersFromSheet, Unable to get google sheet client: %v", err)
		return err
	}
	rangePath := fmt.Sprintf("Sheet3!A%d:E", 2)
	resp, err := srv.Spreadsheets.Values.Get(CultUserDataSheetPath, rangePath).Do()
	if err != nil {
		log.Printf("AddCultUsersFromSheet, Error in retrieving data from sheet: %v", err)
		return err
	}

	if resp == nil || len(resp.Values) == 0 {
		log.Println("AddCultUsersFromSheet, No entry found!")
		return fmt.Errorf("AddCultUsersFromSheet: No entry found")
	}
	var invalidEntries []structs.CultUserDataError
	for i := 0; i < len(resp.Values); i++ {
		row := resp.Values[i]
		if len(row) >= 5 {
			idString := row[0].(string)
			cultUserIdString := row[1].(string)
			phoneString := row[2].(string)
			nameString := row[3].(string)
			dobString := row[4].(string)
			var errorMessages []string

			cultUserId, err := strconv.ParseInt(cultUserIdString, 10, 64)
			if err != nil || cultUserId <= 0 {
				errMsg := fmt.Sprintf("Invalid value %s for cult User Id ", idString)
				if err != nil {
					errMsg = fmt.Sprintf("Error in parsing %s for cult User Id for %s, err: %v", cultUserIdString, idString, err)
				}
				errorMessages = append(errorMessages, errMsg)
			}
			if len(phoneString) <= 7 || len(phoneString) >= 11 {
				errMsg := fmt.Sprintf("Invalid value %s for phone %s", idString, phoneString)
				errorMessages = append(errorMessages, errMsg)
			}

			userInfo := fmt.Sprintf("cult User Id: %s, dob: %s, name: %s, phone: %s, id %s", cultUserIdString, dobString, nameString, phoneString, idString)
			log.Println("Test user info ", userInfo)	
			var userDataResponse pb.CultSignUpUserResponse
			if len(errorMessages) == 0 {
				request := &pb.CultSignUpUserReq{
					Phone:      phoneString,
					CultUserId: int32(cultUserId),
					Dob:        dobString,
					Name:       nameString,
				}
				err = s.CheckOrCreateCultSignUpUser(ctx, request, &userDataResponse)
				if err != nil {
					log.Printf("Error in storing user %s, err %s", userInfo, err)
					errorMessages = append(errorMessages, fmt.Sprintf("Error in storing user %s, err %s", userInfo, err))
				}
			}
			if len(errorMessages) == 0 {
				/*if userDataResponse.Status != nil && userDataResponse.Status.Status == success && userDataResponse.Memberships != nil && len(userDataResponse.Memberships) > 0 {
					//send request
					for _, membership := range userDataResponse.Memberships {
						err := s.CreateCultMembershipForUser(ctx, membership)
						if err != nil {
							log.Printf("Error in creating cult membership for user %s, membership %s, err %s", userInfo, membership, err)
							errorMessages = append(errorMessages, fmt.Sprintf("Error in creating cult membership for user %s, membership %s, err %s", userInfo, membership, err))
						}
					}
				}*/
			}

			if len(errorMessages) > 0 {
				var entryError structs.CultUserDataError
				entryError.RowNumber = i + 1
				entryError.ErrorMessages = errorMessages
				entryError.Row = userInfo
				invalidEntries = append(invalidEntries, entryError)
				continue
			}

			log.Println("test response ", userDataResponse)
		}
	}
	if len(invalidEntries) > 0 {
		s.ErrorMigrateCultUsersAlertMail(ctx, invalidEntries)
	}
	return nil
}

func (s *Service) CreateCultMembershipForUser(ctx context.Context, bodyData *pb.MembershipDetails) error {

	var responseBody structs.CreateCultMembershipResponse
	requestBody, err := json.Marshal(bodyData)
	if err != nil {
		log.Printf("Error in marshalling cult create membership request err %v", err)
		return fmt.Errorf("Error in marshalling cult create membership request err %v", err)
	}

	request := &util.Request{
		Method:      util.MethodTypePost,
		RequestURL:  fmt.Sprintf("%s/userMembership/createCultPack", "cult"),
		RequestBody: bytes.NewReader(requestBody),
	}
	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("Create Cult Membership : Error in creating membership %v, Error Code %d", err, response.Status)
		return err
	}
	if response.Status != http.StatusOK {
		log.Printf("Error in create cult membership Error:%v", response)
		return fmt.Errorf("Error in create cult membership status %s, Error:%v", response, response.Status)
	}

	if err := json.Unmarshal([]byte(response.Body), &responseBody); err != nil {
		log.Printf("Error in unmarshalling create cult membership response err %v", err)
		return fmt.Errorf("Error in unmarshalling create cult membership response err %v", err)
	}
	if !responseBody.Success {
		return fmt.Errorf("failure in creating cult membership, status %s, %s", response.Status, responseBody)
	}
	return nil
}

func (s *Service) ErrorMigrateCultUsersAlertMail(ctx context.Context, invalidEntries []structs.CultUserDataError) {
	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: FITSO_BOT_MAIL,
	}

	toEmails := [2]string{
		TECH_MAIL,
		"<EMAIL>",
	}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	message := "Following is the report for cron to migrate cult users to fitso DB <br><br><br>"
	if len(invalidEntries) > 0 {
		message += "Invalid Entries: <br><br>"
		for _, invalidEntry := range invalidEntries {
			message += "Row Number: " + strconv.Itoa(invalidEntry.RowNumber) + "<br>"
			message += "Entry: " + invalidEntry.Row + "<br>"
			message += "Errors: " + strings.Join(invalidEntry.ErrorMessages, ", ") + "<br><br>"
		}
		message += "Please check the rows where data is wrong<br><br><br>"
	}

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: "ALERT : Error storing cult user and creating subscription",
		Message: message,
	}

	go s.Pub.SendBroadcastEmail(emailReq)
}
