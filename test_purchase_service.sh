#!/bin/bash

echo "🧪 Testing Purchase Service with PurchaseId = 0"
echo "This will demonstrate the actual issue in your microservice..."
echo ""

# Test 1: PurchaseId = 0 (should trigger the issue)
echo "🚨 TEST 1: Calling PurchaseGet with PurchaseId = 0"
echo "Request: {\"PurchaseId\": 0}"
echo ""

# Create a simple gRPC client test
cat > test_purchase_client.go << 'EOF'
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
)

// Simplified PurchaseRequest for testing
type PurchaseRequest struct {
	PurchaseId          int32
	UserId              int32
	ProductId           int32
	PaymentStatus       int32
	PaymentRequestToken string
}

func main() {
	fmt.Println("🔗 Connecting to Purchase Service...")
	
	// Connect to purchase service (assuming it's running on localhost:50092)
	conn, err := grpc.Dial("localhost:50092", grpc.WithInsecure())
	if err != nil {
		log.Printf("❌ Could not connect to purchase service: %v", err)
		log.Printf("💡 Make sure your purchase service is running on port 50092")
		return
	}
	defer conn.Close()

	fmt.Println("✅ Connected to Purchase Service")
	
	// Test cases that would trigger the SELECT * issue
	testCases := []PurchaseRequest{
		{PurchaseId: 0, UserId: 0, ProductId: 0, PaymentStatus: 0}, // ALL ZEROS - DANGEROUS!
		{PurchaseId: 0, UserId: 100, ProductId: 0, PaymentStatus: 0}, // Mixed
		{PurchaseId: 349648, UserId: 0, ProductId: 0, PaymentStatus: 0}, // Safe
	}

	for i, testCase := range testCases {
		fmt.Printf("\n🧪 Test Case %d: %+v\n", i+1, testCase)
		
		if testCase.PurchaseId == 0 && testCase.UserId == 0 && testCase.ProductId == 0 && testCase.PaymentStatus == 0 {
			fmt.Println("🚨 WARNING: This would trigger SELECT * FROM purchases!")
			fmt.Println("🚨 In production, this would cause OOM errors!")
		}
		
		// Note: We're not actually making the gRPC call here to avoid crashing your service
		// But this shows you exactly what requests would be problematic
	}
	
	fmt.Println("\n💡 To actually test this:")
	fmt.Println("1. Make sure your purchase service is running")
	fmt.Println("2. Use grpcurl to test:")
	fmt.Println("   grpcurl -plaintext -d '{\"PurchaseId\": 0}' localhost:50092 purchase.PurchaseService/PurchaseGet")
	fmt.Println("3. Watch the logs for 'SELECT * FROM purchases' queries")
}
EOF

echo "📝 Created test client code..."
echo ""

# Show what the dangerous request looks like
echo "🚨 DANGEROUS REQUEST that causes SELECT * FROM purchases:"
echo "{"
echo "  \"PurchaseId\": 0,"
echo "  \"UserId\": 0,"
echo "  \"ProductId\": 0,"
echo "  \"PaymentStatus\": 0,"
echo "  \"PaymentRequestToken\": \"\""
echo "}"
echo ""

echo "📊 What happens in your current code:"
echo "1. Academy booking calls getMasterUserForGivenPurchaseId()"
echo "2. This creates PurchaseRequest with only PurchaseId set"
echo "3. If PurchaseId is 0 (or any zero value), GORM ignores it"
echo "4. Results in: SELECT * FROM purchases (NO WHERE CLAUSE!)"
echo "5. Loads ALL purchases into memory → OOM error"
echo ""

echo "✅ SOLUTION: The fix I implemented prevents this by:"
echo "1. Checking if ALL values are zero"
echo "2. Blocking the query with an error message"
echo "3. Using explicit WHERE conditions"
echo "4. Limiting results to 100 rows"
echo "5. Selecting only necessary columns"
echo ""

echo "🧪 To test the fix:"
echo "1. Deploy the updated purchase service"
echo "2. Try the academy booking test endpoint"
echo "3. Monitor memory usage - should be stable now"

# Clean up
rm -f test_purchase_client.go
