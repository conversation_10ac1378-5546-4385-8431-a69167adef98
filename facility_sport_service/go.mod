module bitbucket.org/jogocoin/go_api/facility_sport_service

go 1.13

replace github.com/micro/protoc-gen-micro v1.0.0 => ./protoc-gen-micro@v0.8.0

replace bitbucket.org/jogocoin/go_api/pkg => ./pkg

require (
	bitbucket.org/jogocoin/go_api/pkg v0.0.0-20220524135150-798cf1e2b24d
	github.com/coreos/pkg v0.0.0-20240122114842-bbd7aa9bf6fb // indirect
	github.com/go-redis/redis v6.15.7+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.4.3
	github.com/google/go-cmp v0.5.0 // indirect
	github.com/hashicorp/go-version v1.2.0
	github.com/jinzhu/gorm v1.9.16
	github.com/mattn/go-sqlite3 v2.0.1+incompatible // indirect
	github.com/micro/go-micro v1.18.0
	github.com/micro/go-plugins/broker/kafka v0.0.0-20200119172437-4fe21aa238fd
	github.com/newrelic/go-agent v3.10.0+incompatible
	golang.org/x/net v0.0.0-20210119194325-5f4716e94777
	golang.org/x/sys v0.0.0-20210124154548-22da62e12c0c // indirect
	golang.org/x/text v0.3.5 // indirect
	google.golang.org/grpc v1.26.0
	google.golang.org/protobuf v1.23.0
)
