package main

import (
	"fmt"
	"log"
	"time"

	pb "bitbucket.org/jogocoin/go_api/facility_sport_service/proto/facility_sport"

	interceptor "bitbucket.org/jogocoin/go_api/pkg/interceptor"
	"bitbucket.org/jogocoin/go_api/pkg/newrelic"

	data "bitbucket.org/jogocoin/go_api/facility_sport_service/data"
	facilitySportHandler "bitbucket.org/jogocoin/go_api/facility_sport_service/handler"
	facilitySportSubscriber "bitbucket.org/jogocoin/go_api/facility_sport_service/subscriber"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/server"

	// "github.com/micro/cli"
	"bitbucket.org/jogocoin/go_api/facility_sport_service/setupFunc"
	"github.com/micro/go-micro"
	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	// initial setup
	setupFunc.SetUp()

	db, err := data.MysqlConnect()
	defer db.Close()

	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}
	kBroker := setupFunc.KafkaConnect()

	ROdb, ROerr := data.MysqlConnectRO()
	defer ROdb.Close()

	if ROerr != nil {
		log.Printf("Could not connect to Read Only DB: %v", ROerr)
	}

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	facilitySportData := &data.FacilitySportData{Db: db, Client: redisClient, Broker: kBroker, ROdb: ROdb}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.facility_sport"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(facilitySportHandler.LogWrapper,
			facilitySportHandler.SetContextWrapper,
			nrmicro.HandlerWrapper(app),
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	// register a new subscriber function to the service
	micro.RegisterSubscriber("product.subscriber.facility_sport.test", newService.Server(), facilitySportSubscriber.SubTestEvent, server.SubscriberQueue("queue.pubsub"))

	pb.RegisterFacilitySportServiceHandler(newService.Server(), &facilitySportHandler.Service{facilitySportData})

	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}

}
