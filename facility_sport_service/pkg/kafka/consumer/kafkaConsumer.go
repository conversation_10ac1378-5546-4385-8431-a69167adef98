package consumer

import (
	"bitbucket.org/jogocoin/go_api/pkg/kafka/config"
	subscriber2 "bitbucket.org/jogocoin/go_api/pkg/kafka/subscriber"
	"context"
	"github.com/Shopify/sarama"
	"log"
)

type KafkaConsumer struct {
	config       *config.KafkaConfig
	subscription *subscriber2.KafkaSubscription
	handler      MessageHandler
	consumer     sarama.ConsumerGroup
}

func NewKafkaConsumerWithConfig(config *config.KafkaConfig) *KafkaConsumer {
	return &KafkaConsumer{config: config}
}

func NewKafkaConsumer() *KafkaConsumer {
	repo := config.GetKafkaRepositoryInstance()
	kafkaConfig := repo.GetConfig()
	return &KafkaConsumer{config: kafkaConfig}
}

func NewKafkaConsumerWithOfflineBrokers() *KafkaConsumer {
	repo := config.GetKafkaRepositoryInstance()
	kafkaConfig := repo.GetOfflineConfig()
	return &KafkaConsumer{config: kafkaConfig}
}

func (k *KafkaConsumer) Subscribe(ctx context.Context, handler MessageHandler, subscription *subscriber2.KafkaSubscription) error {
	k.subscription = subscription
	kafkaConfig := k.subscription.ToConsumerConfig(k.config.Version())
	c, consumerErr := sarama.NewConsumerGroup(k.config.Servers(), k.subscription.GroupId(), kafkaConfig)
	if consumerErr != nil {
		log.Println("Error in initializaing consumer", consumerErr)
		return consumerErr
	}
	k.consumer = c
	for {
		topics := []string{k.subscription.Topic()}
		handler := NewConsumerMessageHandler(handler)
		consumerErr = c.Consume(ctx, topics, handler)
		if consumerErr != nil {
			log.Printf("Consumer failed with err : %s", consumerErr)
			return consumerErr
		}
	}
}

func (k *KafkaConsumer) Close(ctx context.Context) error {
	closeErr := k.consumer.Close()
	if closeErr != nil {
		log.Println("Error in closing consumer", closeErr)
		return closeErr
	}
	log.Println("Consumer successfully closed")
	return nil
}
