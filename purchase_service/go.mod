module bitbucket.org/jogocoin/go_api/purchase_service

go 1.13

replace github.com/micro/protoc-gen-micro v1.0.0 => /go/src/go_api/purchase_service/protoc-gen-micro@v0.8.0

replace bitbucket.org/jogocoin/go_api/pkg => /go/src/go_api/purchase_service/pkg

require (
	bitbucket.org/jogocoin/go_api/pkg v0.0.0-20220524135150-798cf1e2b24d
	github.com/Shopify/sarama v1.25.0
	github.com/aws/aws-sdk-go v1.23.0
	github.com/go-redis/redis v6.15.7+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.5.0
	github.com/hashicorp/go-version v1.3.0
	github.com/jinzhu/gorm v1.9.16
	github.com/mattn/go-sqlite3 v2.0.1+incompatible // indirect
	github.com/micro/go-micro v1.18.0
	github.com/micro/go-plugins/broker/kafka v0.0.0-20200119172437-4fe21aa238fd
	github.com/newrelic/go-agent v3.10.0+incompatible
	github.com/segmentio/kafka-go v0.4.13
	github.com/spf13/cast v1.3.1
	golang.org/x/net v0.0.0-20200324143707-d3edc9973b7e
	google.golang.org/grpc v1.26.0
	google.golang.org/protobuf v1.26.0
)
