package data

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	gormNewrelicConfig "bitbucket.org/jogocoin/go_api/pkg/newrelic"
	models "bitbucket.org/jogocoin/go_api/purchase_service/data/model"
	pb "bitbucket.org/jogocoin/go_api/purchase_service/proto/purchase"
	"bitbucket.org/jogocoin/go_api/purchase_service/structs"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"github.com/micro/go-micro/broker"
)

type DataInterface interface {
	MakePurchase
	RefundDataInterface
	PurchaseGet(purchase *models.Purchase, purchases *[]models.Purchase) error
	PurchaseCreate(purchase *models.Purchase) (error, int32)
	UpdateActualAmount(purchaseId int32, actualAmount float32, discountAmount float32) error
	PushPurchaseEmailData(emData *structs.EmailRequest) error
	GetSportWiseProductsForProductCategories(sportData *[]models.Sport, productCategories structs.ProductCategoryCity) error
	GetProductsForProductCategories(productData *[]models.Product, pcFt *pb.ProductCategoryFeatureType) error
	UpdatePurchaseIdToSubscription(subscriptionId, memberPurchaseId int32, oldPurchaseId *int32) error
	UpdatePreviousPurchaseIdToNewPurchase(newPurchaseId, oldPurchaseId int32) error
	FacilityGet(facillityId int32, facilityName *string) error
	UpdatePurchase(purchaseId int32, purchaseData models.Purchase) error
	StoreAmazonGiftCards(giftCodes *models.AmazonGiftCards) error
	GetUnusedGiftCardCount(res *pb.UnusedAmazonGiftCardsCount) error
	UpdateEffectiveRevenueForPurchase(purchase_id int32, effective_revenue float32) error
	GetSubscriptionDetails(subscription_id int32, subscription *structs.Subscription) error
	CreatePurchasePaymentMapping(ctx context.Context, user *models.PurchasePaymentMapping) error
	GetPurchaseCancellationRecord(ctx context.Context, condition models.RefundRecord, res *models.RefundRecord) error
	CreatePurchaseModificationLogs(ctx context.Context, purchaseData *models.PurchaseModificationLogs) error
	GetPurchaseModificationLogs(ctx context.Context, condition *models.PurchaseModificationLogs, purchases *[]models.PurchaseModificationLogs) error
	GetPurchaseIDPaymentDetails(ctx context.Context, condition models.PurchasePaymentMapping, paymentDetails *models.PurchasePaymentMapping) error
	SendNotification(ndata *structs.FcmNotification) error
	GetUserPurchasesForProduct(ctx context.Context, userID int32, productID int32, purchases *[]models.Purchase) error
	GetPurchasesForLastTwoDays(ctx context.Context, purchase *models.Purchase, purchases *[]models.Purchase) error
	GetLinkedPurchases(ctx context.Context, purchaseID int32) ([]*models.Purchase, error)
	GetUserIdsByPurchaseId(ctx context.Context, purchaseId int32) ([]int32, error)
	GetActivePurchaseByUser(ctx context.Context, userId int32) ([]*models.Purchase, error)
	GetPurchasesofLastNthDay(ctx context.Context, nthDay int32) ([]*models.Purchase, error)
	GetPurchaseForOneTimeNPSSurvey(ctx context.Context) ([]*models.Purchase, error)
	GetPurchaseDetailsFromSubscriptionId(ctx context.Context, subscriptionId int32, res *models.Purchase) error
	GetPurchasesWithExpiryInWeekForProductCategoryId(ctx context.Context, productCategoryId int32) ([]*models.Purchase, error)
	GetMaxDiscountAllowed(ctx context.Context) (int32, error)
	GetDiscountAmountMap(ctx context.Context, res *pb.DiscountMap) error
	SetDiscountAmountMap(ctx context.Context, res *pb.DiscountMap) error
}

type PurchaseData struct {
	Db     *gorm.DB
	Client *redis.Client
	Broker broker.Broker
}

var (
	notificationTopic = "notificationTopic"
	emailTopic        = "emailTopic"
)

func (a *PurchaseData) GetPurchaseDetailsFromSubscriptionId(ctx context.Context, subscriptionId int32, res *models.Purchase) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("subscriptions s").
		Select("p.*").
		Joins("INNER JOIN purchases p on s.`purchase_id` = p.`purchase_id`").
		Where("s.`subscription_id` = ?", subscriptionId).
		Find(&res)
	if err := response.Error; err != nil {
		log.Println("Error in getting purchaseId from Db for subscriptionId: ", subscriptionId)
		return err
	}
	return nil
}

func (a *PurchaseData) CreatePurchasePaymentMapping(ctx context.Context, mappingData *models.PurchasePaymentMapping) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	response := DB.Create(mappingData)

	if response.Error != nil {
		log.Printf("error in creating purchase payment mapping data for purchase id %d, error: %v", mappingData.PurchaseID, response.Error)

		return response.Error
	}

	return nil
}

func (a *PurchaseData) PurchaseGet(purchase *models.Purchase, purchases *[]models.Purchase) error {

	// Optimize query to only select necessary columns and add limit
	response := a.Db.Select("purchase_id, user_id, linked_purchase_id, pp_id").
		Where(&purchase).
		Limit(100). // Prevent massive result sets
		Find(&purchases)

	if response.Error != nil && gorm.IsRecordNotFoundError(response.Error) == false {
		log.Printf("error in fetching purchase details for purchase_id:%d, err:%v", purchase.PurchaseId, response.Error)
		return response.Error
	}

	return nil
}

func (a *PurchaseData) GetPurchasesForLastTwoDays(ctx context.Context, purchase *models.Purchase, purchases *[]models.Purchase) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("purchases").
		Select("*").
		Where("purchases.created_at > date_sub(curdate(),interval 2 day)").
		Where(&purchase).
		Find(&purchases)

	if response.Error != nil && gorm.IsRecordNotFoundError(response.Error) == false {
		log.Println("error in getting purchases in func: GetPurchaseInInterval", response.Error)
		return response.Error
	}

	return nil
}

func (a *PurchaseData) PurchaseCreate(purchase *models.Purchase) (error, int32) {

	if err := a.Db.Create(&purchase).Error; err != nil {
		log.Printf("error in creating purchase: %v for user %d", err, purchase.UserId)
		return err, 0
	}

	return nil, purchase.PurchaseId
}

func (a *PurchaseData) UpdateActualAmount(purchaseId int32, actualAmount float32, discountAmount float32) error {

	purchase := &models.Purchase{}

	response := a.Db.Model(&purchase).Where("purchase_id = ?", purchaseId).Updates(models.Purchase{ActualAmount: actualAmount, DiscountAmount: discountAmount, EffectiveRevenue: actualAmount})

	if response.Error != nil {
		fmt.Println(response.Error)
		return response.Error
	}
	return nil
}

func (a *PurchaseData) GetPurchaseModificationLogs(ctx context.Context, condition *models.PurchaseModificationLogs, purchases *[]models.PurchaseModificationLogs) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Where(&condition).Where("deleted_flag = 0").Find(&purchases)

	if response.Error != nil && gorm.IsRecordNotFoundError(response.Error) == false {
		log.Println(response.Error)
		return response.Error
	}

	return nil
}

func (a *PurchaseData) PushPurchaseEmailData(emData *structs.EmailRequest) error {

	bodyBytes, _ := json.Marshal(emData)

	msg := &broker.Message{
		Header: map[string]string{
			"id": "1",
		},
		Body: bodyBytes,
	}

	if err := a.Broker.Publish(emailTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *PurchaseData) GetProductsForProductCategories(productData *[]models.Product, pcFt *pb.ProductCategoryFeatureType) error {
	response := a.Db.Table("products").
		Select("products.*, pc.`category_name`, pc.`category_description`").
		Joins("INNER JOIN product_categories pc ON pc.`product_category_id` = products.`product_category_id` AND pc.`deleted_flag` = ?", 0).
		Where("products.`feature_type` IN (?) AND products.`product_category_id` IN (?)", pcFt.FeatureType, pcFt.ProductCategoryIds).
		Order("products.`price` DESC")

	if pcFt.CityId > 0 {
		response = response.Joins("INNER JOIN `product_facility_sport_mappings` pfsm ON pfsm.`product_id` = products.`product_id` AND pfsm.`is_active` = 1")
		response = response.Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = pfsm.`fs_id` AND fsm.`operation_status` = 1")
		response = response.Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`")

		response = response.Where("products.location_id = ?", pcFt.CityId)

	}
	response = response.Group("products.`product_id`").Find(&productData)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting featured products for product categories... Db error")
		return err
	}

	fmt.Println(productData)

	return nil
}

func (a *PurchaseData) GetSportWiseProductsForProductCategories(sportData *[]models.Sport, productCategories structs.ProductCategoryCity) error {
	//feature type arr
	var ftArr []int32
	ftArr = append(ftArr, 1)
	ftArr = append(ftArr, 2)

	response := a.Db.Table("sports").
		Select("sports.`sport_id`, sports.`sport_name`, sports.`icon`, sports.`icon_sports_app`, sports.`popularity`, p.*, pc.`category_name`, pc.`category_description`").
		Joins("INNER JOIN facility_sport_mappings fsm ON fsm.`sport_id` = sports.`sport_id`").
		Joins("INNER JOIN product_facility_sport_mappings pfsm ON pfsm.`fs_id` = fsm.`fs_id`").
		Joins("INNER JOIN products p ON p.`product_id` = pfsm.`product_id` AND p.`product_category_id` IN (?) AND p.`feature_type` IN (?)", productCategories.ProductCategoryIds, ftArr).
		Joins("INNER JOIN product_categories pc ON pc.`product_category_id` = p.`product_category_id` AND pc.`deleted_flag` = ?", 0).
		Order("p.`price` DESC")

	if productCategories.CityId > 0 {
		response = response.Joins("INNER JOIN facilities f ON f.`facility_id` = fsm.`facility_id` AND p.`location_id`=?", productCategories.CityId)
	}

	response = response.Where("sports.`is_offered` = ?", 1).Group("sports.`sport_id`, p.`product_id`").Find(&sportData)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting featured products sport wise for product categories... Db error")
		return err
	}

	fmt.Println(sportData)

	return nil
}

func (a *PurchaseData) UpdatePurchaseIdToSubscription(subscriptionId, memberPurchaseId int32, oldPurchaseId *int32) error {

	type OldPurchase struct {
		PurchaseId int32
	}

	var oldPurchase OldPurchase

	responseSl := a.Db.Table("subscriptions").
		Select("subscriptions.purchase_id").
		Where("subscriptions.subscription_id = ?", subscriptionId).
		Scan(&oldPurchase)

	if err := responseSl.Error; err != nil {
		fmt.Println("Error in selecting purchase id")
		return err
	} else {
		*oldPurchaseId = oldPurchase.PurchaseId
	}

	responseDp := a.Db.Table("subscriptions").
		Where("subscriptions.subscription_id = ?", subscriptionId).
		UpdateColumn("subscriptions.purchase_id", memberPurchaseId)

	if err := responseDp.Error; err != nil {
		fmt.Println("Error in updating purchase id")
		return err
	}

	return nil
}

func (a *PurchaseData) UpdatePreviousPurchaseIdToNewPurchase(newPurchaseId, oldPurchaseId int32) error {

	purchase := &models.Purchase{}

	response := a.Db.Model(&purchase).Where("purchase_id = ?", newPurchaseId).Update("pp_id", oldPurchaseId)

	if response.Error != nil {
		fmt.Println(response.Error)
		return response.Error
	}
	return nil
}

func (a *PurchaseData) FacilityGet(facillityId int32, facilityName *string) error {
	type FacilityNameObj struct {
		DisplayName string `json:"display_name"`
	}
	var facilityNameVal FacilityNameObj

	response := a.Db.Table("facilities").
		Select("display_name").
		Where("facilities.facility_id = ?", facillityId).
		Find(&facilityNameVal)

	*facilityName = facilityNameVal.DisplayName
	if response.Error != nil {
		fmt.Println(response.Error)
		return response.Error
	}

	return nil
}

func (a *PurchaseData) UpdatePurchase(purchaseId int32, purchaseData models.Purchase) error {

	purchase := &models.Purchase{
		PurchaseId: purchaseId,
	}
	response := a.Db.Model(&purchase).Where("purchase_id = ?", purchaseId).Updates(&purchaseData)

	if response.Error != nil {
		fmt.Println(response.Error)
		return response.Error
	}
	return nil
}

func (a *PurchaseData) CreatePurchaseModificationLogs(ctx context.Context, purchaseData *models.PurchaseModificationLogs) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	if err := DB.Create(&purchaseData).Error; err != nil {
		log.Printf("error in creating Purchase Modification Logs: %v for purchase ID %d", err, purchaseData.PurchaseID)
		return err
	}

	return nil
}

func (a *PurchaseData) StoreAmazonGiftCards(giftCodes *models.AmazonGiftCards) error {
	valuesStringArray := strings.Split(giftCodes.GiftCode, ",")

	type LastId struct {
		AmazonGiftCardId int `json:"amazon_gift_card_id"`
	}
	var cardId LastId
	res := a.Db.Table("amazon_gift_cards").
		Select("amazon_gift_card_id").
		Order("amazon_gift_card_id desc").
		Limit(1).
		Find(&cardId)
	if err := res.Error; err != nil {
		fmt.Println("Error in getting last gift card ID...")
		return err
	}
	/*
		type AmazonGiftCards1 struct {
			AmazonGiftCardId int       `json:"amazon_gift_card_id"`
			Amount           int32     `json:"amount"`
			GiftCode         string    `json:"gift_code"`
			IsUsed           int       `json:"is_used"`
			GiftCodeDescription string `json:"gift_code_description"`
		}
		giftCards := []AmazonGiftCards1{{AmazonGiftCardId: cardId.AmazonGiftCardId,Amount:giftCodes.Amount,GiftCode: valuesStringArray[0],IsUsed:0,GiftCodeDescription:"GC"}}
		for index, element := range valuesStringArray {
			giftCard := AmazonGiftCards1{AmazonGiftCardId: cardId.AmazonGiftCardId+index+2, Amount:giftCodes.Amount, GiftCode: element,IsUsed:0,GiftCodeDescription:"GC"}
			giftCards = append(giftCards,giftCard)
		}
		fmt.Println(giftCards)
		response := a.Db.Select("amazon_gift_card_id", "amount", "gift_code","is_used","gift_code_description").Create(&giftCards)
		if err := response.Error; err != nil {
			fmt.Println(err)
			return err
		}
		if err := a.Db.Create(&giftCodes).Error; err != nil {
			fmt.Println(err)
			return err
		}*/
	stmt := "INSERT IGNORE INTO `amazon_gift_cards` (amazon_gift_card_id, amount, gift_code, is_used, created_at, gift_code_description) values"
	var valStrArr []string
	for index, element := range valuesStringArray {
		valStr := "(" + fmt.Sprint(cardId.AmazonGiftCardId+index+1) + "," + fmt.Sprint(giftCodes.Amount) + "," + "'" + element + "'" + "," + "0" + "," + "NOW()" + "," + "'GC'" + ")"
		valStrArr = append(valStrArr, valStr)
	}
	valuesStr := strings.Join(valStrArr, ",")
	stmt += valuesStr
	response := a.Db.Exec(stmt)
	if err := response.Error; err != nil {
		fmt.Println("unable to insert Amazon cards")
		return err
	}
	return nil
}

func (a *PurchaseData) GetUnusedGiftCardCount(res *pb.UnusedAmazonGiftCardsCount) error {
	response := a.Db.Table("amazon_gift_cards").
		Select("Count(*) as gift_card_unused").
		Where("is_used = ?", false).
		Find(&res)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Error in getting Unused Gift cards count.. DB error")
		return err
	}
	return nil
}

func (a *PurchaseData) UpdateEffectiveRevenueForPurchase(purchase_id int32, effective_revenue float32) error {
	log.Println("updating effective_revenue for purchase_id ---- ", purchase_id, effective_revenue)

	response := a.Db.Table("purchases p").
		Where("p.`purchase_id` = ?", purchase_id).
		Updates(map[string]interface{}{"effective_revenue": effective_revenue})

	if err := response.Error; err != nil {
		log.Println("Error in updating effective_revenue. Db error -- ", err)
	}
	return nil
}

func (a *PurchaseData) GetSubscriptionDetails(subscription_id int32, subscription *structs.Subscription) error {

	response := a.Db.Table("subscriptions sub").
		Select("sub.*, pu.`effective_revenue`").
		Joins("INNER JOIN `purchases` pu ON pu.`purchase_id` = sub.`purchase_id`").
		Where("sub.`subscription_id` = ?", subscription_id).
		Find(&subscription)

	if err := response.Error; err != nil {
		log.Println(response.Error)
		return err
	}

	return nil
}

func (a *PurchaseData) GetPurchaseCancellationRecord(ctx context.Context, condition models.RefundRecord, res *models.RefundRecord) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("refund_records").
		Select("*").
		Where(condition).
		Find(res)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch cancel record ratings")
		return err
	}
	return nil
}

func (a *PurchaseData) GetPurchaseIDPaymentDetails(ctx context.Context, condition models.PurchasePaymentMapping, paymentDetails *models.PurchasePaymentMapping) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("purchase_payment_mappings").
		Select("*").
		Where(condition).
		Find(paymentDetails)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not get purchased payment details")
		return err
	}
	return nil
}

func (a *PurchaseData) SendNotification(ndata *structs.FcmNotification) error {

	bodyBytes, err := json.Marshal(ndata)
	if err != nil {
		log.Println("error in marshalling")
		return err
	}

	msg := &broker.Message{
		Header: map[string]string{
			"id": "0",
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(notificationTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *PurchaseData) GetUserPurchasesForProduct(ctx context.Context, userID int32, productID int32, purchases *[]models.Purchase) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Where("`product_id` = ? and user_id = ? ", productID, userID).
		Order("`purchase_date` DESC").
		Find(&purchases)

	if response.Error != nil {
		log.Printf("Could not get user purchases for product: %s", response.Error)
		return response.Error
	}

	return nil
}

// GetLinkedPurchases returns all the linked purchases corresponding to a purchase id
func (a *PurchaseData) GetLinkedPurchases(ctx context.Context, purchaseID int32) ([]*models.Purchase, error) {
	log.Println("GetLinkedPurchases for purchase id: ", purchaseID)
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	var purchases []*models.Purchase

	response := DB.Where("`linked_purchase_id` = ? and payment_status = ?", purchaseID, completePaymentStatus).
		Find(&purchases)

	if response.Error != nil {
		log.Printf("[GetLinkedPurchases] Could not get linked purchases for purchase id: %s with err %v", purchaseID, response.Error)

		return nil, response.Error
	}

	return purchases, nil
}

func (a *PurchaseData) GetUserIdsByPurchaseId(ctx context.Context, purchaseId int32) ([]int32, error) {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	userIds := make([]int32, 0)
	purchases := make([]*models.Purchase, 0)

	response := DB.Table("purchases").
		Select("user_id, effective_revenue").
		Where("(purchase_id = ? OR linked_purchase_id = ?) AND payment_status = ? AND effective_revenue > ?", purchaseId, purchaseId, completePaymentStatus, 0).
		Find(&purchases)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("unable to fetch user for given purchase id: %d, error: %v", purchaseId, err)
		return userIds, err
	}

	for _, val := range purchases {
		userIds = append(userIds, val.UserId)
	}
	return userIds, nil
}

func (a *PurchaseData) GetActivePurchaseByUser(ctx context.Context, userId int32) ([]*models.Purchase, error) {
	purchases := make([]*models.Purchase, 0)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("purchases p").
		Select("p.*").
		Joins("INNER JOIN subscriptions s ON s.purchase_id = p.purchase_id AND s.user_id = ?", userId).
		Where("s.subscription_end_date > CURRENT_TIMESTAMP AND s.is_active = ?", 1).
		Find(&purchases)

	if err := response.Error; err != nil {
		return purchases, err
	}
	return purchases, nil
}

func (a *PurchaseData) GetPurchasesofLastNthDay(ctx context.Context, nthDay int32) ([]*models.Purchase, error) {
	purchases := make([]*models.Purchase, 0)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("purchases p").
		Select("p.*").
		Joins("INNER JOIN subscriptions s ON s.purchase_id = p.purchase_id").
		Where("DATE(p.`purchase_date`)=DATE_ADD(CURDATE(), INTERVAL -? DAY) AND s.is_active = ? AND p.payment_status = ?", nthDay, 1, 2).
		Group("p.user_id").
		Find(&purchases)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("func:GetPurchasesofLastNthDay, unable to fetch purchases of last %d th day error: %v", nthDay, err)
		return purchases, err
	}
	return purchases, nil
}

func (a *PurchaseData) GetPurchaseForOneTimeNPSSurvey(ctx context.Context) ([]*models.Purchase, error) {
	purchases := make([]*models.Purchase, 0)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("purchases p").
		Select("p.*").
		Joins("INNER JOIN subscriptions s ON s.purchase_id = p.purchase_id").
		Where("DATE(p.`purchase_date`) < '2021-07-23' AND s.subscription_end_date >= CURDATE() AND s.is_active = ? AND p.payment_status = ?", 1, 2).
		Group("p.user_id").
		Find(&purchases)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("func:GetPurchaseForOneTimeNPSSurvey, unable to fetch purchases for one time nps survey, error: %v", err)
		return purchases, err
	}
	return purchases, nil
}

func (a *PurchaseData) GetPurchasesWithExpiryInWeekForProductCategoryId(ctx context.Context, productCategoryId int32) ([]*models.Purchase, error) {
	purchases := make([]*models.Purchase, 0)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("`purchases` p").
		Select("p.*").
		Joins("INNER JOIN `subscriptions` s ON p.`purchase_id` = s.`purchase_id`").
		Joins("INNER JOIN `products` prod ON p.`product_id` = prod.`product_id`").
		Where("prod.`product_category_id` = ? and s.`is_active`= ? and DATE_ADD(CURRENT_DATE,INTERVAL 7 DAY) = DATE(s.`subscription_end_date`)", productCategoryId, 1).
		Find(&purchases)
	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("func:GetPurchasesWithExpiryInWeekForProductCategoryId, unable to fetch purchases for one time nps survey, error: %v", err)
		return purchases, err
	}
	return purchases, nil
}

func getMaxDiscountCacheKey() string {
	return fmt.Sprintf("max_discount_allowed")
}
func (a *PurchaseData) GetMaxDiscountAllowed(ctx context.Context) (int32, error) {
	var maxDiscount int32
	redisKey := getMaxDiscountCacheKey()
	redisValue, err := a.Client.Get(redisKey).Result()
	if err == redis.Nil {
		log.Printf("func:GetMaxDiscountAllowed, max discount allowed not set in redis, error: %v", err)
		maxDiscount = 500
		return maxDiscount, nil
	}
	if err != nil {
		log.Printf("func:GetMaxDiscountAllowed, unable to fetch max discount allowed from redis, error: %v", err)
		return maxDiscount, err
	}
	err = json.Unmarshal([]byte(redisValue), &maxDiscount)
	if err != nil {
		log.Printf("func:GetMaxDiscountAllowed, unable to unmarshal max discount allowed from redis, error: %v", err)
		return maxDiscount, err
	}
	return maxDiscount, nil
}

func getDiscountMapCacheKey() string {
	return fmt.Sprintf("discount_amount_map")
}

func (a *PurchaseData) GetDiscountAmountMap(ctx context.Context, discountMap *pb.DiscountMap) error {
	redisKey := getDiscountMapCacheKey()
	redisValue, err := a.Client.Get(redisKey).Result()
	if err == redis.Nil {
		log.Printf("func:GetDiscountAmountMap, discount map not set in redis, error: %v", err)
		return nil
	}
	if err != nil {
		log.Printf("func:GetDiscountAmountMap, unable to fetch discount map allowed from redis, error: %v", err)
		return err
	}
	err = json.Unmarshal([]byte(redisValue), &discountMap)
	if err != nil {
		log.Printf("func:GetDiscountAmountMap, unable to unmarshal max discount allowed from redis, error: %v", err)
		return err
	}
	return nil
}

func (a *PurchaseData) SetDiscountAmountMap(ctx context.Context, discountMap *pb.DiscountMap) error {
	redisKey := getDiscountMapCacheKey()
	serializedData, err := json.Marshal(discountMap)
	if err != nil {
		log.Printf("error marshalling the data for discount amount map: %v, err: ", discountMap, err)
		return err
	}

	if err := a.Client.Set(redisKey, serializedData, 0).Err(); err != nil {
		log.Println("error in caching discount amount: %v", err)
		return err
	}
	return nil
}
