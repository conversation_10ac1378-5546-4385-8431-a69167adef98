package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"bitbucket.org/jogocoin/go_api/pkg/interceptor"
	"bitbucket.org/jogocoin/go_api/pkg/newrelic"
	data "bitbucket.org/jogocoin/go_api/purchase_service/data"
	purchaseHandler "bitbucket.org/jogocoin/go_api/purchase_service/handler"
	pb "bitbucket.org/jogocoin/go_api/purchase_service/proto/purchase"
	"bitbucket.org/jogocoin/go_api/purchase_service/setupFunc"
	purchaseSubscriber "bitbucket.org/jogocoin/go_api/purchase_service/subscriber"
	workerExecutor "bitbucket.org/jogocoin/go_api/purchase_service/worker"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/server"
	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	// initial setup
	setupFunc.SetUp()
	kBroker := setupFunc.KafkaConnect()

	db, err := data.MysqlConnect()
	defer db.Close()

	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	purchaseData := &data.PurchaseData{Db: db, Client: redisClient, Broker: kBroker}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.purchase"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(purchaseHandler.LogWrapper,
			nrmicro.HandlerWrapper(app),
			purchaseHandler.SetContextWrapper,
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	// register a new subscriber function to the service
	micro.RegisterSubscriber("product.subscriber.purchase", newService.Server(), purchaseSubscriber.SubTestEvent, server.SubscriberQueue("queue.pubsub"))
	serviceHandler := purchaseHandler.Service{purchaseData}
	pb.RegisterPurchaseServiceHandler(newService.Server(), &serviceHandler)
	runWorkers(context.Background(), &serviceHandler)

	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}

}

func runWorkers(ctx context.Context, serviceHandler *purchaseHandler.Service) {
	log.Println("Starting purchase_service workers...")
	workerExecutor.StartJobs(context.Background(), serviceHandler)
}
