package interceptor

import (
	"context"
	"errors"
	"fmt"
	"runtime/debug"

	"github.com/micro/go-micro/server"
	"github.com/micro/go-micro/util/log"
	newrelic "github.com/newrelic/go-agent"
)

// PanicRecoveryInterceptor handles panics within gRPC calls and logs them as errors
// This prevents the complete application from failing in case of panic in a single RPC call
func PanicRecoveryInterceptor(fn server.HandlerFunc) server.HandlerFunc {
	return func(ctx context.Context, req server.Request, res interface{}) error {
		defer handleCrash(func(r interface{}) {
			txn := newrelic.FromContext(ctx)
			errMsg := fmt.Sprintf("[PANIC] %v\nstacktrace: %s", r, string(debug.Stack()))
			if txn != nil {
				txn.NoticeError(errors.New(errMsg))
			}

			log.Errorf(errMsg)
		})

		err := fn(ctx, req, res)
		return err
	}
}

func handleCrash(handler func(interface{})) {
	if r := recover(); r != nil {
		handler(r)
	}
}

// ErrorInterceptor logs server errors other than panic to newrelic
func ErrorInterceptor(fn server.HandlerFunc) server.HandlerFunc {
	return func(ctx context.Context, req server.Request, res interface{}) error {
		err := fn(ctx, req, res)
		if err != nil {
			txn := newrelic.FromContext(ctx)
			errLog := fmt.Errorf("[ERROR] %s | stacktrace: %s", err.Error(), string(debug.Stack()))
			if txn != nil {
				txn.NoticeError(errLog)
			}
		}
		return err
	}
}
