setup:
	go mod init "bitbucket.org/jogocoin/go_api/purchase_service"

build:
	protoc -I. \
	  -I$(GOPATH)/src/github.com/grpc-ecosystem/grpc-gateway/third_party/googleapis \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/purchase_service \
		proto/purchase/purchase.proto
	protoc --proto_path=$(GOPATH)/src/go_api/product_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/purchase_service \
		proto/product/product.proto
	protoc --proto_path=$(GOPATH)/src/go_api/notification_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/purchase_service \
		proto/notification/notification.proto
	protoc --proto_path=$(GOPATH)/src/go_api/booking_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/purchase_service \
		proto/booking/booking.proto
	protoc --proto_path=$(GOPATH)/src/go_api/user_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/purchase_service \
		proto/user/user.proto
	protoc --proto_path=$(GOPATH)/src/go_api/facility_sport_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/purchase_service \
		proto/facility_sport/facility_sport.proto
	echo "machine bitbucket.org login $(BITBUCKET_USER_NAME) password $(BITBUCKET_USER_PASS)" > $(GOPATH)/src/go_api/purchase_service/.netrc

local:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50157 GOPRIVATE="bitbucket.org" go run main.go

hot-reload:
	reflex -r '.*\.go' -R '^vendor/' -s make local

local2:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50059 MICRO_REGISTRY=mdns go run main.go

aws_dev:
	make build
	MICRO_MODE=qa MICRO_SERVER_ADDRESS=:50059 MICRO_REGISTRY=mdns go run main.go

aws_prod:
	make build
	MICRO_MODE=prod MICRO_SERVER_ADDRESS=:50059 MICRO_REGISTRY=mdns go run main.go

docker_build:
	make build
	docker build -t purchase_service .

rund:
	docker run --rm -it -p 50051:50051 --network="host" -e MICRO_SERVER_ADDRESS=:50051 -e MICRO_REGISTRY=mdns -e DB_USER=root -e DB_NAME=newSchema -e DB_PASSWORD=newPassword purchase_service

redis:
	docker run -it --network="host" --rm redis redis-cli -h purchase-redis
