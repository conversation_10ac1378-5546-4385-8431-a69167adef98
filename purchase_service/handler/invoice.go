package purchaseHandler

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	message "bitbucket.org/jogocoin/go_api/pkg/pubsub/message"
	models "bitbucket.org/jogocoin/go_api/purchase_service/data/model"
	util "bitbucket.org/jogocoin/go_api/purchase_service/internal/util"
	facilitySportPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/product"
	pb "bitbucket.org/jogocoin/go_api/purchase_service/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/purchase_service/structs"
	queues "bitbucket.org/jogocoin/go_api/purchase_service/worker/queues"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	config "github.com/micro/go-micro/config"
	"golang.org/x/net/context"
)

func (s *Service) SendTaxInvoice(ctx context.Context, req *pb.PurchaseRequest, res *pb.Ack) error {

	if req.PurchaseId <= 0 {
		log.Println("func:SendTaxInvoice, purchase_id couldn't be zero")
		res.Status = &pb.Status{
			Status:  failed,
			Message: "purchase_id couldn't be zero",
		}
		return nil
	}

	purchaseIdForInvoice := req.PurchaseId

	var purchasesData []models.Purchase
	if err := s.Data.Db.Select("purchase_id, user_id, product_id, payment_status").
		Where("purchase_id = ?", req.PurchaseId).
		Limit(1).
		Find(&purchasesData).Error; err != nil {
		log.Printf("func:SendTaxInvoice, error in getting purchase data: %v for purchaseID %d", err, req.PurchaseId)
		return err
	}

	if len(purchasesData) == 0 {
		log.Printf("func:SendTaxInvoice, purchase with purchaseID %d not found", req.PurchaseId)
		res.Status = &pb.Status{
			Status:  failed,
			Message: "purchase_id not found",
		}
		return nil
	}

	purchaseData := purchasesData[0]
	if purchaseData.PaymentStatus != completePaymentStatus {
		log.Printf("func:SendTaxInvoice, payment satus is not valid for purchaseID: %d", req.PurchaseId)
		res.Status = &pb.Status{
			Status:  failed,
			Message: "invalid payment status",
		}
		return nil
	}

	if purchaseData.LinkedPurchaseId != 0 {
		purchaseIdForInvoice = purchaseData.LinkedPurchaseId
		purchase = &models.Purchase{
			PurchaseId: purchaseData.LinkedPurchaseId,
		}
		var linkedPurchasesData []models.Purchase
		if err := s.Data.PurchaseGet(purchase, &linkedPurchasesData); err != nil {
			log.Printf("func:SendTaxInvoice, error in getting purchase data: %v for linkedPurchaseID %d", err, purchaseData.LinkedPurchaseId)
			return err
		}
		if len(linkedPurchasesData) == 0 {
			log.Printf("func:SendTaxInvoice, purchase with linked purchaseID: %d not found", purchaseData.LinkedPurchaseId)
			res.Status = &pb.Status{
				Status:  failed,
				Message: "linked_purchase_id not found",
			}
			return nil
		}
		purchaseData = linkedPurchasesData[0]
	}

	if purchaseData.Amount == 0 {
		log.Printf("func:SendTaxInvoice, invalid payment amount purchaseID: %d", purchaseIdForInvoice)
		res.Status = &pb.Status{
			Status:  failed,
			Message: "tax invoice couldn't be prcocessed for zero amount",
		}
		return nil
	}

	invoiceReqData := structs.InvoicePDFRequest{
		PurchaseId:  purchaseIdForInvoice,
		InvoiceType: TAX_INVOICE,
	}
	pubSubMsg, err := message.NewMessageForJson(&invoiceReqData)
	if err != nil {
		log.Printf("func:SendTaxInvoice, error in getting message from json for purchaseId: %d, err:%v", purchaseIdForInvoice, err)
		return err
	}
	log.Printf("func: SendTaxInvoice, calling HandleInvoiceRequest for purchase id : %d, with req: %v", invoiceReqData.PurchaseId, invoiceReqData)
	queue_obj := queues.InvoiceEventsQueue()
	err = queue_obj.Publish(ctx, pubSubMsg)
	if err != nil {
		log.Printf("func:SendTaxInvoice, error in publishing invoiceReqData for purchaseId: %d, err:%v", purchaseIdForInvoice, err)
		return err
	}

	res.Status = &pb.Status{
		Status:  success,
		Message: "tax invoice sent successfully",
	}

	return nil
}

func (s *Service) HandleInvoiceRequest(ctx context.Context, req *structs.InvoicePDFRequest) (err error) {
	log.Printf("HandleInvoiceRequest for purchase id: %d, with req: %v", req.PurchaseId, req)
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from HandleInvoiceRequest panic- ", r)
		}
	}()
	if req.PurchaseId == 0 {
		log.Println("func:HandleInvoiceRequest, invalid purchase_id")
		return errors.New("invalid purchase_id")
	}

	var pdfBytes []byte
	var tmpFileName string
	var uploadPdfPath string

	pcl := util.GetProductServiceClient()

	if req.InvoiceType == TAX_INVOICE {

		reqData := &models.Purchase{
			PurchaseId: req.PurchaseId,
		}
		purchaseEmailData, err := s.PrepareReceiptEmailData(ctx, reqData)
		if err != nil {
			log.Printf("func:HandleInvoiceRequest, error in getting purchase data: %v for purchaseID %d", err, req.PurchaseId)
			return err
		}

		if purchaseEmailData.Amount == 0 {
			log.Println("func:HandleInvoiceRequest, invalid request amount is zero")
			return errors.New("invalid invoice request: zero amount")
		}

		req.InvoiceNumber = purchaseEmailData.InvoiceNumber
		year, month, day := purchaseEmailData.PurchaseDate.Date()
		req.InvoiceDate = fmt.Sprintf("%02d-%02d-%d", day, month, year)
		req.CustomerName = purchaseEmailData.CartUserName

		preferredFacilityId := purchaseEmailData.FinalCartUsers[0].PreferredFacilityId
		preferredFacilityStateId := getPrefferedFacilityStateId(ctx, preferredFacilityId)
		if preferredFacilityStateId == 0 {
			log.Printf("func:HandleInvoiceRequest, error in getting stateId for for preferredFacilityId %d", preferredFacilityId)
			return err
		}
		req.GstinDetails = getGstinDetailsForState(preferredFacilityStateId)

		subtotal := ((purchaseEmailData.Amount) * float32(100)) / float32(118)
		req.TaxableAmt = subtotal
		req.Cgst = subtotal * float32(0.09)
		req.Sgst = subtotal * float32(0.09)
		req.TotalAmt = purchaseEmailData.Amount
		var totalDiscountAmount float32

		var invoiceItems []structs.InvoiceItem
		for idx, cartUser := range purchaseEmailData.FinalCartUsers {
			if idx == 0 {
				preferredFacilityId = cartUser.PreferredFacilityId
			}
			invoiceItem := structs.InvoiceItem{
				Description: purchaseEmailData.Description,
			}

			invoiceItem.Amount = (cartUser.Amount * float32(100)) / float32(118)
			if cartUser.DiscountAmount > 0 {
				invoiceItem.Amount = invoiceItem.Amount + cartUser.DiscountAmount
				totalDiscountAmount += cartUser.DiscountAmount
			}
			invoiceItems = append(invoiceItems, invoiceItem)
		}

		if totalDiscountAmount > 0 {
			invoiceDiscountItem := structs.InvoiceItem{
				Description: "Discount",
				Amount:      totalDiscountAmount,
			}
			invoiceItems = append(invoiceItems, invoiceDiscountItem)
		}
		req.Items = invoiceItems

		pdfBytes, err = generateTaxInvoiceV2(ctx, req)
		if err != nil {
			log.Printf("func:HandleInvoiceRequest, error in generating tax invoice, err:%v", err)
			return err
		}

		tmpFileName = "tax_invoice_" + fmt.Sprintf("%d", req.PurchaseId) + ".pdf"
		uploadPdfPath = "tax_invoices/" + tmpFileName
		filePath := "invoices/" + uploadPdfPath
		if err := uploadInvoiceToS3(ctx, filePath, pdfBytes); err != nil {
			log.Printf("func:HandleInvoiceRequest, error in uploadingg pdf to s3 bucket for filepath:%s, err:%v", filePath, err)
			return err
		}

		attachedFile := &structs.AttachmentBody{
			FileName:  tmpFileName,
			FileBytes: pdfBytes,
		}
		purchaseEmailData.InvoiceBody = attachedFile
		go s.SendUserReceiptMailForPurchaseV3(ctx, purchaseEmailData)
	} else if req.InvoiceType == CREDIT_NOTE_INVOICE {
		if req.RefundAmt == 0 {
			log.Println("No need to create credit note for zero refund amt")
			return nil
		}

		purchaseID := req.PurchaseId
		reqData := &productPB.BatchGetSubscriptionsByPurchaseIDRequest{
			PurchaseId: []int32{purchaseID},
		}
		response, err := pcl.BatchGetSubscriptionsByPurchaseID(ctx, reqData)
		if err != nil {
			log.Printf("func:HandleInvoiceRequest, error in fetching subscription details for purchaseID : %d, ERROR: %v", purchaseID, err)
			return err
		}

		if _, ok := response.Subscriptions[purchaseID]; !ok {
			log.Printf("subscription doesn't exist for purchaseId:%d", purchaseID)
			return errors.New("subscription data doesn't exist")
		}

		ucl := util.GetUserServiceClient()
		userRequest := &userPB.UserRequest{
			UserId: response.Subscriptions[purchaseID].UserId,
		}
		userData, err := ucl.UserGet(ctx, userRequest)
		if err != nil {
			log.Printf("Error in getting user data for userID: %d, ERROR: %v", response.Subscriptions[purchaseID].UserId, err)
			return err
		}

		req.CustomerName = userData.Users[0].Name

		// amount
		subtotal := ((req.RefundAmt) * float32(100)) / float32(118)
		req.TaxableAmt = subtotal
		req.Cgst = subtotal * float32(0.09)
		req.Sgst = subtotal * float32(0.09)
		req.TotalAmt = req.RefundAmt

		// gst details
		preferredFacilityStateId := getPrefferedFacilityStateId(ctx, response.Subscriptions[purchaseID].PreferredFacilityId)
		if preferredFacilityStateId == 0 {
			log.Printf("func:HandleInvoiceRequest, error in getting stateId for for preferredFacilityId %d", response.Subscriptions[purchaseID].PreferredFacilityId)
			return err
		}
		req.GstinDetails = getGstinDetailsForState(preferredFacilityStateId)

		// items
		productRequest := &productPB.ProductRequest{
			PId: response.Subscriptions[purchaseID].ProductId,
		}

		productData, err := pcl.ProductGet(ctx, productRequest)
		if err != nil {
			log.Printf("func:HandleInvoiceRequest, error in getting product with productID %d", response.Subscriptions[purchaseID].ProductId)
			return err
		}
		if len(productData.Products) == 0 {
			log.Printf("func:HandleInvoiceRequest, product with productID %d not found", response.Subscriptions[purchaseID].ProductId)
			return errors.New("product not found")
		}

		var invoiceItems []structs.InvoiceItem
		invoiceItem := structs.InvoiceItem{
			Description: productData.Products[0].ProductDescription,
			Amount:      subtotal,
		}
		invoiceItems = append(invoiceItems, invoiceItem)
		req.Items = invoiceItems

		pdfBytes, err = generateTaxInvoiceV2(ctx, req)
		if err != nil {
			return err
		}

		tmpFileName = "credit_note_" + fmt.Sprintf("%d", req.RefundId) + ".pdf"
		uploadPdfPath = "credit_notes/" + tmpFileName
		filePath := "invoices/" + uploadPdfPath
		if err := uploadInvoiceToS3(ctx, filePath, pdfBytes); err != nil {
			log.Printf("func:HandleInvoiceRequest, error in uploadingg pdf to s3 bucket for filepath:%s, err:%v", filePath, err)
			return err
		}

		attachedFile := &structs.AttachmentBody{
			FileName:  tmpFileName,
			FileBytes: pdfBytes,
		}

		emailData := &structs.PurchaseEmailData{
			InvoiceBody: attachedFile,
			UserEmail:   userData.Users[0].Email,
		}

		go s.SendUserCreditNoteMailForPurchaseV3(ctx, emailData)
	}

	return nil
}

// [TEST FUNC] Don't use this func
func (s *Service) SendOneTimeUserReceiptMailForPurchaseV3(ctx context.Context, emailData *structs.PurchaseEmailData) {

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: FITSO_MAIL_BOT,
	}

	var toEmails []*structs.Email
	toEmailObject := &structs.Email{
		EmailAddress: "<EMAIL>",
	}
	toEmails = append(toEmails, toEmailObject)
	bccEmails := []string{"<EMAIL>", "<EMAIL>"}
	var bccObj []*structs.Email
	for _, bccEmail := range bccEmails {
		bccObj = append(bccObj, &structs.Email{
			EmailAddress: bccEmail,
		})
	}

	if emailData.InvoiceBody == nil {
		return
	}

	var attachments []*structs.AttachmentBody
	attachments = append(attachments, emailData.InvoiceBody)

	emailReq := &structs.EmailRequest{
		From:        fromEm,
		To:          toEmails,
		BCC:         bccObj,
		Subject:     "Daily User Invoices - 02nd October",
		Message:     "Please find the attached invoice",
		Attachments: attachments,
	}

	s.Data.PushPurchaseEmailData(emailReq)

	return
}

// [TEST FUNC] Don't use this func
func (s *Service) GenerateInvoiceForOneTimeUser(ctx context.Context, req *pb.InvoiceForOneTimeUserReq, res *pb.Ack) error {

	reqData := &structs.InvoicePDFRequest{
		InvoiceType:   TAX_INVOICE,
		GstinDetails:  getGstinDetailsForState(9),
		CustomerName:  req.CustomerName,
		InvoiceNumber: fmt.Sprintf("J%s-%s-%s", "21", "DL", fmt.Sprintf("%06d", req.InvoiceCount)),
		InvoiceDate:   req.InvoiceDate,
	}

	subtotal := ((req.PaidAmount) * float32(100)) / float32(118)
	reqData.TaxableAmt = subtotal
	reqData.Cgst = subtotal * float32(0.09)
	reqData.Sgst = subtotal * float32(0.09)
	reqData.TotalAmt = req.PaidAmount

	var invoiceItems []structs.InvoiceItem
	invoiceItem := structs.InvoiceItem{
		Description: req.ProductName,
		Amount:      subtotal,
	}
	invoiceItems = append(invoiceItems, invoiceItem)
	reqData.Items = invoiceItems

	log.Println("req....", reqData)
	pdfBytes, err := generateTaxInvoiceV2(ctx, reqData)
	if err != nil {
		log.Println("func:GenerateInvoiceForOneTimeUser, error in making tax invoices", err)
		return err
	}

	tmpFileName := "tax_invoice.pdf"
	// uploadPdfPath := "tax_invoices/" + tmpFileName
	// filePath := "invoices/" + uploadPdfPath
	// if err := uploadInvoiceToS3(ctx, filePath, pdfBytes); err != nil {
	// 	log.Printf("func:GenerateInvoiceForOneTimeUser, error in uploadingg pdf to s3 bucket for filepath:%s, err:%v", filePath, err)
	// 	return err
	// }
	attachedFile := &structs.AttachmentBody{
		FileName:  tmpFileName,
		FileBytes: pdfBytes,
	}

	emailData := &structs.PurchaseEmailData{
		InvoiceBody: attachedFile,
	}
	log.Println("emailData", emailData)
	s.SendOneTimeUserReceiptMailForPurchaseV3(ctx, emailData)

	return nil
}

func generateTaxInvoice(ctx context.Context, req *structs.InvoicePDFRequest) (pdfBytes []byte, err error) {
	invoiceParams, err := buildInvoiceParams(ctx, req)
	if err != nil {
		log.Println("func:generateTaxInvoice, error in building invoice params", err)
		return
	}

	log.Println("Invoice Params", invoiceParams)

	pdfBytes, err = generateSignedInvoicePdf(ctx, invoiceParams)

	return
}

func buildInvoiceParams(ctx context.Context, req *structs.InvoicePDFRequest) (paramStr string, err error) {
	data := url.Values{}

	if req.InvoiceType == TAX_INVOICE {
		data.Add("type", "fitso/fitso_invoice")
	} else if req.InvoiceType == CREDIT_NOTE_INVOICE {
		data.Add("type", "fitso/fitso_credit_note")
	}

	data.Set("details[entity_name]", getCorpName())
	data.Set("details[pan_number]", getCorpPanNumber())
	data.Set("details[cin_number]", getCorpCinNumber())

	data.Set("details[state]", req.GstinDetails.State)
	data.Set("details[display_address]", req.GstinDetails.RegisteredAddress)
	data.Set("details[state_code]", req.GstinDetails.StateCode)
	data.Set("details[gstin_number]", req.GstinDetails.GstinNumber)

	data.Set("details[from_email]", getFitsoFinanceTeamEmailId())

	// service
	data.Set("details[hsn_code]", getCorpHsnCode())
	if req.InvoiceType == TAX_INVOICE {
		data.Set("details[service_description]", "Sports and recreational sports facility operation services")
	}

	// customer
	data.Set("details[customer][name]", req.CustomerName)

	// invoice
	if req.InvoiceType == CREDIT_NOTE_INVOICE {
		data.Set("details[invoice_number]", req.CNInvoiceNumber)
		data.Set("details[invoice_date]", req.CNInvoiceDate)
		data.Set("details[ref_invoice_number]", req.InvoiceNumber)
		data.Set("details[ref_invoice_date]", req.InvoiceDate)
	} else if req.InvoiceType == TAX_INVOICE {
		data.Set("details[invoice_number]", req.InvoiceNumber)
		data.Set("details[invoice_date]", req.InvoiceDate)
	}

	// items
	for index, item := range req.Items {
		data.Set("details[items]["+fmt.Sprintf("%v", index)+"][index]", fmt.Sprintf("%d", index+1))
		data.Set("details[items]["+fmt.Sprintf("%v", index)+"][name]", item.Description)
		if item.Amount > 0 {
			var displayAmount string
			if item.Description == "Discount" {
				displayAmount = "-" + fmt.Sprintf("%.2f", item.Amount)
			} else {
				displayAmount = fmt.Sprintf("%.2f", item.Amount)
			}
			data.Set("details[items]["+fmt.Sprintf("%v", index)+"][amount]", displayAmount)
		}
	}
	// amount and taxes
	data.Set("details[taxable_amount]", fmt.Sprintf("%.2f", req.TaxableAmt))

	data.Set("details[taxes][0][label]", "CGST @ 9%")
	data.Set("details[taxes][0][cost]", fmt.Sprintf("%.2f", req.Cgst))
	data.Set("details[taxes][1][label]", "SGST @ 9%")
	data.Set("details[taxes][1][cost]", fmt.Sprintf("%.2f", req.Sgst))

	data.Set("details[total_amount]", fmt.Sprintf("%.2f", req.TotalAmt))
	data.Set("details[fitso_registered_address]", "T-19, Basement, Green Park Main, New Delhi - 110016, India")

	log.Println("data", data)

	paramStr = data.Encode()

	return
}

func generateTaxInvoiceV2(ctx context.Context, req *structs.InvoicePDFRequest) (pdfBytes []byte, err error) {
	invoiceParams, err := buildInvoiceParamsV2(ctx, req)
	if err != nil {
		log.Println("func:generateTaxInvoiceV2, error in building invoice params", err)
		return
	}

	ctx, cancel := context.WithTimeout(ctx, time.Minute*30)
	defer cancel()

	pdfBytes, err = generateSignedInvoicePdfV2(ctx, invoiceParams)
	return
}
func buildInvoiceParamsV2(ctx context.Context, req *structs.InvoicePDFRequest) (paramStr string, err error) {
	data := url.Values{}

	cityID := util.GetCityIDFromContext(ctx)

	data.Add("city_id", string(cityID))

	if req.InvoiceType == TAX_INVOICE {
		data.Add("type", "fitso_invoice4")
	} else if req.InvoiceType == CREDIT_NOTE_INVOICE {
		data.Add("type", "fitso_credit_note4")
	}

	data.Set("details[entity_name]", getCorpName())
	data.Set("details[pan_number]", getCorpPanNumber())
	data.Set("details[cin_number]", getCorpCinNumber())

	data.Set("details[state]", req.GstinDetails.State)
	data.Set("details[display_address]", req.GstinDetails.RegisteredAddress)
	data.Set("details[state_code]", req.GstinDetails.StateCode)
	data.Set("details[gstin_number]", req.GstinDetails.GstinNumber)

	data.Set("details[from_email]", getFitsoFinanceTeamEmailId())

	// service
	data.Set("details[hsn_code]", getCorpHsnCode())
	if req.InvoiceType == TAX_INVOICE {
		data.Set("details[service_description]", "Sports and recreational sports facility operation services")
	}

	// customer
	data.Set("details[customer][name]", req.CustomerName)

	// invoice
	if req.InvoiceType == CREDIT_NOTE_INVOICE {
		data.Set("details[invoice_number]", req.CNInvoiceNumber)
		data.Set("details[invoice_date]", req.CNInvoiceDate)
		data.Set("details[ref_invoice_number]", req.InvoiceNumber)
		data.Set("details[ref_invoice_date]", req.InvoiceDate)
	} else if req.InvoiceType == TAX_INVOICE {
		data.Set("details[invoice_number]", req.InvoiceNumber)
		data.Set("details[invoice_date]", req.InvoiceDate)
	}

	// items
	for index, item := range req.Items {
		data.Set("details[items]["+fmt.Sprintf("%v", index)+"][index]", fmt.Sprintf("%d", index+1))
		data.Set("details[items]["+fmt.Sprintf("%v", index)+"][name]", item.Description)
		if item.Amount > 0 {
			var displayAmount string
			if item.Description == "Discount" {
				displayAmount = "-" + fmt.Sprintf("%.2f", item.Amount)
			} else {
				displayAmount = fmt.Sprintf("%.2f", item.Amount)
			}
			data.Set("details[items]["+fmt.Sprintf("%v", index)+"][amount]", displayAmount)
		}
	}
	// amount and taxes
	data.Set("details[taxable_amount]", fmt.Sprintf("%.2f", req.TaxableAmt))

	data.Set("details[taxes][0][label]", "CGST @ 9%")
	data.Set("details[taxes][0][cost]", fmt.Sprintf("%.2f", req.Cgst))
	data.Set("details[taxes][1][label]", "SGST @ 9%")
	data.Set("details[taxes][1][cost]", fmt.Sprintf("%.2f", req.Sgst))

	data.Set("details[total_amount]", fmt.Sprintf("%.2f", req.TotalAmt))
	data.Set("details[fitso_registered_address]", "T-19, Basement, Green Park Main, New Delhi - 110016, India")

	log.Println("data", data)

	paramStr = data.Encode()

	return
}

func generateSignedInvoicePdfV2(ctx context.Context, postParams string) (pdfBytes []byte, err error) {

	headers := map[string]string{
		util.ContentType: util.FormContentType,
	}
	request := &util.Request{
		Method:      util.MethodTypePost,
		Headers:     headers,
		RequestURL:  fmt.Sprintf("%s/api/v1/generateSignedPDF.json", PHP_API_URL_HOST),
		RequestBody: strings.NewReader(postParams),
	}

	resp, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("func:GenerateSignedInvoicePdfV2, error in making http request with error: %v", err)
		return nil, err
	}
	res := structs.SignedPDFGeneratorResponse{}
	unmarshalErr := json.Unmarshal([]byte(resp.Body), &res)
	if unmarshalErr != nil {
		log.Println("func:GenerateSignedInvoicePdfV2, Unmarshall error::", unmarshalErr)
		return nil, unmarshalErr
	}
	return res.Pdf, nil
}

func generateSignedInvoicePdf(ctx context.Context, postParams string) (pdfBytes []byte, err error) {
	host := config.Get("signedPDFGenerator", "host").String("")
	headers := map[string]string{
		util.ContentType:        util.FormContentType,
		"X-Zomato-PDF-Security": config.Get("signedPDFGenerator", "pdfSecurityKey").String(""),
	}

	request := &util.Request{
		Method:      util.MethodTypePost,
		RequestURL:  fmt.Sprintf("%s", host),
		Headers:     headers,
		RequestBody: strings.NewReader(postParams),
	}

	resp, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("func:GenerateSignedInvoicePdf, error in making http request with error: %v", err)
		return nil, err
	}

	res := structs.SignedPDFGeneratorResponse{}
	unmarshalErr := json.Unmarshal([]byte(resp.Body), &res)
	if unmarshalErr != nil {
		return nil, unmarshalErr
	}

	return res.Pdf, nil
}

func uploadInvoiceToS3(ctx context.Context, uploadPdfPath string, pdfBytes []byte) error {

	pdfReader := bytes.NewReader(pdfBytes)
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(getInvoiceS3BucketRegion()),
	})

	if err != nil {
		log.Printf("func:UploadInvoiceToS3, failed to load config for s3 session, err:%v", err)
		return err
	}

	// Setup the S3 Upload Manager.
	uploader := s3manager.NewUploader(sess)
	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(getInvoiceS3BucketName()),
		Key:         aws.String(uploadPdfPath),
		Body:        pdfReader,
		ContentType: aws.String(http.DetectContentType(pdfBytes)),
		//ACL:       aws.String("public-read"),
	})

	if err != nil {
		log.Printf("func:UploadInvoiceToS3, Unable to upload %q to %q, err:%v", uploadPdfPath, getInvoiceS3BucketName(), err)
		return err
	}

	return nil
}

func (s *Service) GenerateInvoiceNumber(ctx context.Context, purchase models.Purchase, preferredFacilityId int32) string {
	purchaseID := purchase.PurchaseId

	if purchaseID == 0 {
		log.Printf("func:GenerateInvoiceNumber, invalid purchase id to generate invoice number")
		return ""
	}

	preferredFacilityStateId := getPrefferedFacilityStateId(ctx, preferredFacilityId)
	if preferredFacilityStateId == 0 {
		log.Printf("func:GenerateInvoiceNumber, error in getting stateId for for preferredFacilityId %d", preferredFacilityId)
		return ""
	}

	gstinDetails := getGstinDetailsForState(preferredFacilityStateId)
	stateAbbv := gstinDetails.ShortName

	finStartYear := purchase.CreatedAt.AddDate(0, -3, 0).Format("06")

	var successPurchaseCount int32
	err := s.Data.GetFinancialYearPurchaseSuccessCountForStateFromCache(ctx, preferredFacilityStateId, finStartYear, stateAbbv, &successPurchaseCount)
	if err != nil {
		log.Printf("func:GenerateInvoiceNumber, error in fetching financial year success purchase count, error: %v", err)
		return ""
	}

	invoiceNumber := fmt.Sprintf("J%s-%s-%s", finStartYear, stateAbbv, fmt.Sprintf("%06d", successPurchaseCount+1))

	return invoiceNumber
}

// UpdateInvoiceNumber updates the invoice number for a particular purchase
func (s *Service) UpdateInvoiceNumber(ctx context.Context, purchaseID int32, invoiceNumber string) error {
	updatedPurchaseData := models.Purchase{
		InvoiceNumber: invoiceNumber,
	}

	return s.Data.UpdatePurchase(purchaseID, updatedPurchaseData)
}

func (s *Service) GenerateCNInvoiceNumber(ctx context.Context, refundRecord *pb.RefundRecord) string {
	purchaseID := refundRecord.PurchaseId

	pcl := util.GetProductServiceClient()
	if purchaseID == 0 {
		log.Printf("func:GenerateCNInvoiceNumber, invalid purchase id to generate invoice number")
		return ""
	}

	subscriptionData := &productPB.BatchGetSubscriptionsByPurchaseIDRequest{
		PurchaseId: []int32{purchaseID},
	}
	response, err := pcl.BatchGetSubscriptionsByPurchaseID(ctx, subscriptionData)
	if err != nil {
		log.Printf("func:GenerateCNInvoiceNumber, error in fetching subscription details for purchaseID : %d, ERROR: %v", purchaseID, err)
		return ""
	}

	if _, ok := response.Subscriptions[purchaseID]; !ok {
		log.Printf("func:GenerateCNInvoiceNumber, subscription data doesn't exist")
		return ""
	}

	preferredFacilityId := response.Subscriptions[purchaseID].PreferredFacilityId
	preferredFacilityStateId := getPrefferedFacilityStateId(ctx, preferredFacilityId)

	gstinDetails := getGstinDetailsForState(preferredFacilityStateId)
	stateAbbv := gstinDetails.ShortName

	now := time.Now()
	finStartYear := now.AddDate(0, -3, 0).Format("06")

	successRefundCount, err := s.Data.GetFinancialYearSuccessRefundCount(ctx, refundRecord, preferredFacilityStateId)
	if err != nil {
		log.Printf("func:GenerateCNInvoiceNumber, error in fetching financial year success refund count, error: %v", err)
		return ""
	}
	cnInvoiceNumber := fmt.Sprintf("J%s-%sCN-%s", finStartYear, stateAbbv, fmt.Sprintf("%06d", successRefundCount+1))

	return cnInvoiceNumber
}

func getPrefferedFacilityStateId(ctx context.Context, preferredFacilityId int32) int32 {
	fcl := util.GetFacilitySportServiceClient()
	facilityDetailRequest := &facilitySportPB.GetFacilityDetailsRequest{
		FacilityId: preferredFacilityId,
	}
	facilityDetailsResponse, err := fcl.GetFacilityDetails(ctx, facilityDetailRequest)
	if err != nil {
		log.Printf("func:getPrefferedFacilityStateId, error in fetching facility details, error: %v", err)
		return 0
	}

	pcl := GetProductServiceClient()
	cityID := facilityDetailsResponse.CityId
	ProductcityRequest := &productPB.GetFitsoCityDataFromCityIdRequest{
		FitsoCityId: cityID,
	}
	cityData, err := pcl.GetFitsoCityDataFromCityId(ctx, ProductcityRequest)
	if err != nil {
		log.Printf("invalid getPrefferedFacilityStateId: %d with error: %v", cityID, err)
		return 0
	}

	return cityData.StateId
}

func getCorpName() string {
	corp_name := config.Get("corp_details", "corp_name").String("")
	return corp_name
}

func getCorpPanNumber() string {
	pan_number := config.Get("corp_details", "pan_number").String("")
	return pan_number
}

func getCorpCinNumber() string {
	cin_number := config.Get("corp_details", "cin_number").String("")
	return cin_number
}

func getCorpHsnCode() string {
	hsn_code := config.Get("corp_details", "hsn_code").String("")
	return hsn_code
}

func getFitsoFinanceTeamEmailId() string {
	return FITSO_FINANCE_EMAIL
}

func getInvoiceS3BucketName() string {
	bucket_name := config.Get("s3", "invoice", "bucketName").String("")
	return bucket_name
}

func getInvoiceS3BucketRegion() string {
	bucket_region := config.Get("s3", "invoice", "bucketRegion").String("")
	return bucket_region
}

func getGstinDetailsForState(stateId int32) structs.GstinDetails {
	stateGstinInfo := structs.GstinDetails{}
	if stateId == 9 {
		// DELHI
		stateGstinInfo.State = "Delhi"
		stateGstinInfo.ShortName = "DL"
		stateGstinInfo.StateCode = "07"
		stateGstinInfo.RegisteredAddress = "T-19, Basement, Green Park Main, New Delhi - 110016, India"
		stateGstinInfo.GstinNumber = "07AADCJ5540N1ZA"
	} else if stateId == 12 {
		// HARYANA
		stateGstinInfo.State = "Haryana"
		stateGstinInfo.ShortName = "HR"
		stateGstinInfo.StateCode = "06"
		stateGstinInfo.RegisteredAddress = "1st floor, Cyber Approach Workspaces, plot no 52, Sector 44, Gurugram, Gurugram, Haryana, 122003"
		stateGstinInfo.GstinNumber = "06AADCJ5540N1ZC"
	} else if stateId == 16 {
		// KARNATAKA
		stateGstinInfo.State = "Karnataka"
		stateGstinInfo.ShortName = "KA"
		stateGstinInfo.StateCode = "29"
		stateGstinInfo.RegisteredAddress = "No.17, 3rd Sector, HSR Layout, Bengaluru, Bengaluru Urban, Karnataka, 560102"
		stateGstinInfo.GstinNumber = "29AADCJ5540N1Z4"
	} else if stateId == 31 {
		// UTTAR PRADESH
		stateGstinInfo.State = "Uttar Pradesh"
		stateGstinInfo.ShortName = "UP"
		stateGstinInfo.StateCode = "09"
		stateGstinInfo.RegisteredAddress = "First Floorl, Taj Plaza Complex, 3 Way Road opposite Krishi Bhawan Madan Mohan Malviya Marg, Lucknow, Lucknow, Uttar Pradesh, 226001"
		stateGstinInfo.GstinNumber = "09AADCJ5540N1Z6"
	} else if stateId == 36 {
		// TELANGANA
		stateGstinInfo.State = "Telangana"
		stateGstinInfo.ShortName = "TL"
		stateGstinInfo.StateCode = "36"
		stateGstinInfo.RegisteredAddress = "PLOT NO 71, 91SPRINGBOARD, LVS ARCADE, JUBILEE ENCLAVE, HITEC CITY, MADHAPUR, HYDERABAD, Hyderabad, Telangana, 500081"
		stateGstinInfo.GstinNumber = "36AADCJ5540N1Z9"
	}

	return stateGstinInfo
}
