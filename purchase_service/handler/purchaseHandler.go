package purchaseHandler

import (
	"errors"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"

	data "bitbucket.org/jogocoin/go_api/purchase_service/data"
	models "bitbucket.org/jogocoin/go_api/purchase_service/data/model"
	"bitbucket.org/jogocoin/go_api/purchase_service/internal/util"
	bookingPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/product"
	pb "bitbucket.org/jogocoin/go_api/purchase_service/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/purchase_service/structs"
	"github.com/golang/protobuf/ptypes"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"golang.org/x/net/context"
)

const (
	formatYMD    = "2006-01-02"
	formatYMDHIS = "2006-01-02 15:04:05"
)

var (
	purchaseTypes = map[int32]string{
		1: "Full Payment",
		2: "PP 1st installment",
		3: "PP nth installment",
		4: "PP last installment",
		5: "Upgrade",
	}
)

const (
	fullPaymentPurchase        int32 = 1
	PPFirstInstallmentPurchase int32 = 2
)

type Service struct {
	Data data.DataInterface
}

func (s *Service) Ping(ctx context.Context, req *pb.Empty, res *pb.Pong) error {
	res.Response = "Pong"
	return nil
}

func (s *Service) GetPurchaseDetailsFromSubscriptionId(ctx context.Context, req *pb.GetPurchaseDetailsFromSubscriptionIdRequest, res *pb.PurchaseResponse) error {
	var purchase models.Purchase
	if err := s.Data.GetPurchaseDetailsFromSubscriptionId(ctx, req.SubscriptionId, &purchase); err != nil {
		log.Println("Error getting purchaseID for subacriptionId: ", req.SubscriptionId)
		return err
	}
	res.Purchase = purchase.Proto()
	return nil
}

func (s *Service) PurchaseGet(ctx context.Context, req *pb.PurchaseRequest, res *pb.PurchaseResponse) error {

	// Build WHERE conditions explicitly to prevent SELECT * queries
	var purchases []models.Purchase
	query := s.Data.Db.Select("purchase_id, user_id, linked_purchase_id, pp_id, product_id, payment_status, payment_request_token")

	// Add WHERE conditions - handle zero values explicitly when needed
	hasConditions := false

	if req.PurchaseId > 0 {
		query = query.Where("purchase_id = ?", req.PurchaseId)
		hasConditions = true
	}

	// Special handling for UserId: distinguish between "not provided" vs "find UserId = 0"
	// In protobuf, we can't distinguish between unset and 0, so we assume 0 means "find UserId = 0"
	if req.UserId >= 0 { // Allow UserId = 0 searches
		query = query.Where("user_id = ?", req.UserId)
		hasConditions = true
	}

	if req.ProductId > 0 {
		query = query.Where("product_id = ?", req.ProductId)
		hasConditions = true
	}

	// PaymentStatus = 0 is a valid status, so include it
	if req.PaymentStatus >= 0 {
		query = query.Where("payment_status = ?", req.PaymentStatus)
		hasConditions = true
	}

	if req.PaymentRequestToken != "" {
		query = query.Where("payment_request_token = ?", req.PaymentRequestToken)
		hasConditions = true
	}

	// CRITICAL: Prevent queries without any WHERE conditions
	if !hasConditions {
		log.Printf("🚨 PurchaseGet called with NO valid WHERE conditions - would cause SELECT * FROM purchases! req: %+v", req)
		return errors.New("at least one search parameter must be provided to prevent SELECT * queries")
	}

	if err := query.Limit(100).Find(&purchases).Error; err != nil {
		return errors.New(fmt.Sprintf("error getting purchase: %v", err))
	}

	log.Printf("PurchaseGet executed query with %d results for req: %+v", len(purchases), req)

	for _, purchase := range purchases {
		castedPurchase := purchase.Proto()
		res.Purchases = append(res.Purchases, castedPurchase)
	}

	return nil
}

func (s *Service) PurchaseInitiate(ctx context.Context, req *pb.Purchase, res *pb.PurchaseInitiationResponse) error {
	return nil
}

func (s *Service) PurchaseComplete(ctx context.Context, req *pb.CompletionRequest, res *pb.CompletionResponse) error {

	return nil
}

func (s *Service) CalculateEffectiveRevenueSports(subData structs.Subscription, carryFwdAmt *float32, leftOvrAmt *float32) error {
	log.Println("Calculating carryFwdAmt and leftOvrAmt for ", subData)

	plan_duration := (subData.SubscriptionEndDate.Sub(subData.StartDate).Hours()) / 24
	log.Println("Plan duration- ", plan_duration)
	today := time.Now()

	days_left := (subData.SubscriptionEndDate.Sub(today).Hours()) / 24
	log.Println("Days Left:- ", days_left)
	days_consumed := (today.Sub(subData.StartDate).Hours()) / 24
	log.Println("Days Consumed:- ", days_consumed)

	log.Println("EffectiveRevenue:-", subData.EffectiveRevenue)

	var carry_fwd_amt, left_ovr_amt float32
	if days_consumed >= 0 {
		carry_fwd_amt = (float32(days_left) / float32(plan_duration)) * (float32(subData.EffectiveRevenue))
		left_ovr_amt = (float32(days_consumed) / float32(plan_duration)) * (float32(subData.EffectiveRevenue))
	} else {
		// for future plans
		carry_fwd_amt = float32(subData.EffectiveRevenue)
		left_ovr_amt = 0
	}

	log.Println("Carry Fwd Amt:- ", carry_fwd_amt)
	log.Println("Left Ovr Amt:- ", left_ovr_amt)

	*carryFwdAmt = carry_fwd_amt
	*leftOvrAmt = left_ovr_amt

	return nil
}

func (s *Service) GetMaxDiscountAllowed(ctx context.Context, req *pb.Empty, res *pb.MaxDiscountRes) error {

	maxDiscount, err := s.Data.GetMaxDiscountAllowed(ctx)
	if err != nil {
		log.Println("func:GetMaxDiscountAllowed Error getting max discount allowed: ", err)
		return err
	}
	res.MaxDiscount = maxDiscount
	return nil
}

func (s *Service) GetDiscountAmountMap(ctx context.Context, req *pb.Empty, res *pb.DiscountMap) error {

	err := s.Data.GetDiscountAmountMap(ctx, res)
	if err != nil {
		log.Println("func:GetDiscountAmountMap Error getting max discount allowed: ", err)
		return err
	}
	return nil
}

func (s *Service) SetDiscountAmountMap(ctx context.Context, req *pb.SetDiscountRequest, res *pb.SetDiscountResponse) error {
	if req.ProductCategoryId == 0 || req.ProductDurationInDays == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: "Bad Request",
		}
		return nil
	}
	var existingDiscountMap pb.DiscountMap
	err := s.Data.GetDiscountAmountMap(ctx, &existingDiscountMap)
	if err != nil {
		log.Println("func:GetDiscountAmountMap Error getting max discount allowed: ", err)
		return err
	}
	if val, ok := existingDiscountMap.DiscountMap[req.ProductCategoryId]; ok {
		val.Items[req.ProductDurationInDays] = req.DiscountAmount
	} else {
		durationDiscountMap := map[int32]int32{
			req.ProductDurationInDays: req.DiscountAmount,
		}
		discountMapItems := &pb.DiscountMapItems{
			Items: durationDiscountMap,
		}
		existingDiscountMap.DiscountMap[req.ProductCategoryId] = discountMapItems
	}
	err = s.Data.SetDiscountAmountMap(ctx, &existingDiscountMap)
	if err != nil {
		log.Println("func:GetDiscountAmountMap Error getting max discount allowed: ", err)
		return err
	}
	res.DiscountMap = existingDiscountMap.DiscountMap
	res.Status = &pb.Status{
		Status:  success,
		Message: "Discount amount updated successfully",
	}
	return nil
}

func (s *Service) GenerateReceipt(ctx context.Context, req *pb.ReceiptGenerationRequest, res *pb.GenerateReceiptResponse) error {

	log.Println("Generating receipt for reqData: ", req)

	// maxDiscount, err := s.Data.GetMaxDiscountAllowed(ctx)
	// if err != nil {
	// 	log.Println("func:GetMaxDiscountAllowed Error getting max discount allowed: ", err)
	// 	return err
	// }

	// if maxDiscount < req.DiscountPerPerson {
	// 	log.Println("func:GenerateReceipt Error: Discount is greater than max discount allowed: ", err)
	// 	res.Status = &pb.Status{
	// 		Status:  "failure",
	// 		Message: "Discount is greater than max discount allowed",
	// 	}
	// 	return nil
	// }

	totalMembers := len(req.MembersData)
	perPersonActualAmount := req.Amount / int32(totalMembers)

	defaultPaymentStatus := int32(2)
	defaultOfflinePaymentFlag := int32(1)

	_purchase_date := req.PurchaseDate
	purchaseDate := time.Unix(_purchase_date.Seconds, int64(_purchase_date.Nanos)).UTC()

	productClient := util.GetProductServiceClient()
	requiredProduct, err := productClient.GetProductDetails(ctx, &productPB.Product{ProductId: req.ProductId})
	if err != nil {
		return err
	}
	if requiredProduct.PId == 0 {
		res.Status = &pb.Status{
			Status:  "failure",
			Message: "Could not fetch product details",
		}
		return nil
	}

	var purchasingUserIds []int32

	// Payload validation
	for _, v := range req.MembersData {
		switch requiredProduct.ProductCategoryId {
		case ACADEMY_PRODUCT_CATEGORY_ID:
			if len(v.FacilitySlots) == 0 {
				res.Status = &pb.Status{
					Status:  "failure",
					Message: "no facility slots found in request",
				}
				return nil
			}
			if req.PurchaseType == PURCHASE_TYPE_UPGRADE && v.SubscriptionId == 0 {
				res.Status = &pb.Status{
					Status:  "failure",
					Message: "subscription missing for user in request",
				}
				return nil
			}

		case SUMMERCAMP_PRODUCT_CATEGORY_ID:
			if len(v.FacilitySlots) == 0 {
				res.Status = &pb.Status{
					Status:  "failure",
					Message: "no facility slots found in request",
				}
				return nil
			}
		case MASTERKEY_PRODUCT_CATEGORY_ID:
			purchasingUserIds = append(purchasingUserIds, v.UserId)
			fallthrough
		case SINGLEKEY_PRODUCT_CATEGORY_ID:
			purchasingUserIds = append(purchasingUserIds, v.UserId)
			fallthrough
		case FIXED_PRODUCT_CATEGORY_ID:
			if req.PurchaseType == PURCHASE_TYPE_FULL_PAYMENT && v.PreferredFacilityId == 0 {
				res.Status = &pb.Status{
					Status:  "failure",
					Message: "preffered facility missing for user in request",
				}
				return nil
			}
			if req.PurchaseType == PURCHASE_TYPE_UPGRADE && v.SubscriptionId == 0 {
				res.Status = &pb.Status{
					Status:  "failure",
					Message: "subscription missing for user in request",
				}
				return nil
			}

		default:
			res.Status = &pb.Status{
				Status:  "failure",
				Message: "invalid product category",
			}
			return nil
		}
	}

	// purchase validation for categories
	if requiredProduct.ProductCategoryId == MASTERKEY_PRODUCT_CATEGORY_ID || requiredProduct.ProductCategoryId == SINGLEKEY_PRODUCT_CATEGORY_ID {
		validityRes, err := productClient.IsMasterkeyPurchaseValid(ctx, &productPB.IsMasterkeyPurchaseValidReq{PurchasingUserIds: purchasingUserIds, UserId: req.UserId})
		if err != nil {
			log.Printf("GenerateReceipt: error in masterkey purchase validation: %v", err)
			return err
		}
		if !validityRes.IsValid {
			res.Status = &pb.Status{
				Status:  "failure",
				Message: validityRes.Message,
			}
			return nil
		}
	} else if requiredProduct.ProductCategoryId == ACADEMY_PRODUCT_CATEGORY_ID {
		var cartUsers []*productPB.CartUser
		for _, member := range req.MembersData {
			var fsData []*productPB.FacilitySlot
			for _, v := range member.FacilitySlots {
				fsData = append(fsData, &productPB.FacilitySlot{
					AcademySlotId: v.AcademySlotId,
				})
			}
			cartUsers = append(cartUsers, &productPB.CartUser{
				ProductId:     req.ProductId,
				FacilitySlots: fsData,
				UserId:        member.UserId,
			})
		}
		validityRes, err := productClient.IsAcademyPurchaseValid(ctx, &productPB.IsAcademyPurchaseValidRequest{CartUsers: cartUsers})
		if err != nil {
			return err
		}
		if !validityRes.IsValid {
			res.Status = &pb.Status{
				Status:  "failure",
				Message: "required purchase capacity not available",
			}
			return nil
		}
	} else if requiredProduct.ProductCategoryId == SUMMERCAMP_PRODUCT_CATEGORY_ID {
		var cartUsers []*productPB.CartUser
		for _, member := range req.MembersData {
			var fsData []*productPB.FacilitySlot
			for _, v := range member.FacilitySlots {
				fsData = append(fsData, &productPB.FacilitySlot{
					SummercampSlotId: v.SummercampSlotId,
				})
			}
			cartUsers = append(cartUsers, &productPB.CartUser{
				ProductId:     req.ProductId,
				FacilitySlots: fsData,
				UserId:        member.UserId,
			})
		}
		validityRes, err := productClient.IsSummercampPurchaseValid(ctx, &productPB.IsAcademyPurchaseValidRequest{CartUsers: cartUsers})
		if err != nil {
			log.Println("GenerateReceipt: error in checking whether summer camp purchase is valid for cartUsers: %v", cartUsers)
			return err
		}
		log.Println("GenerateReceipt: validityRes", validityRes)
		if !validityRes.IsValid {
			res.Status = &pb.Status{
				Status:  "failure",
				Message: "required purchase capacity not available",
			}
			return nil
		}
	}

	primaryPurchaseObj := &models.Purchase{
		UserId:              req.UserId,
		ProductId:           req.ProductId,
		Provider:            int32(req.Provider),
		PaymentStatus:       defaultPaymentStatus,
		PurchaseType:        req.PurchaseType,
		OfflinePayment:      defaultOfflinePaymentFlag,
		PaymentRequestToken: req.TransactionNumber,
		Amount:              float32(req.Amount),
		Purpose:             req.Purpose,
		AppType:             req.AppType,
		AppVersion:          req.AppVersion,
		PurchaseDate:        purchaseDate,
		PSPurchaseId:        req.PSPurchaseId,
		CollectedBy:         req.CollectedBy,
		CreatedBy:           req.GeneratedBy,
		AmountCollected:     float32(req.Amount),
		VoucherId:           req.VoucherId,
	}
	// Primary Purchase
	err, purchaseId := s.PurchaseCreate(ctx, primaryPurchaseObj)
	if err != nil {
		return fmt.Errorf("Could not create purchase!, Error: %v", err)
	}

	for _, memberData := range req.MembersData {
		var memberPurchaseId int32

		switch req.PurchaseType {
		case 1:
			fallthrough
		case 2:

			if memberData.UserId == req.UserId {
				// purchase already created -- just update actual amount
				memberPurchaseId = purchaseId
				s.Data.UpdateActualAmount(memberPurchaseId, float32(perPersonActualAmount), float32(req.DiscountPerPerson))

			} else {
				// create purchase
				memberPurchaseObj := primaryPurchaseObj
				memberPurchaseObj.PurchaseId = int32(0)
				memberPurchaseObj.UserId = memberData.UserId
				memberPurchaseObj.Amount = float32(0)
				memberPurchaseObj.ActualAmount = float32(perPersonActualAmount)
				memberPurchaseObj.EffectiveRevenue = float32(perPersonActualAmount)
				memberPurchaseObj.DiscountAmount = float32(req.DiscountPerPerson)
				memberPurchaseObj.LinkedPurchaseId = purchaseId

				err, memberPurchaseId = s.PurchaseCreate(ctx, memberPurchaseObj)
				if err != nil {
					return fmt.Errorf("Could not create child purchase!, error: %v", err)
				}
			}

			planData := &productPB.PlanData{
				PlanStartDate:       memberData.PlanStartDate.Seconds,
				PreferredFacilityId: memberData.PreferredFacilityId,
				PreferredSportId:    memberData.PreferredSportId,
			}
			academySlotId := int32(0)
			if requiredProduct.ProductCategoryId == ACADEMY_PRODUCT_CATEGORY_ID {
				if len(memberData.FacilitySlots) > 0 {
					academySlotId = memberData.FacilitySlots[0].AcademySlotId
				}
			}
			subcriptionRequest := &productPB.CreateSubscriptionRequest{
				UserId:             memberData.UserId,
				ProductId:          req.ProductId,
				PurchaseId:         memberPurchaseId,
				PlanData:           planData,
				IsDashboardRequest: true,
				AcademySlotId:      academySlotId,
				ParentVoucherId:    req.VoucherId,
			}
			subscriptionResponse, err := productClient.CreateSubscriptionV2(ctx, subcriptionRequest)
			if err != nil || subscriptionResponse.SubscriptionId == 0 {
				log.Printf("error in creating academy subscription for user %d with purchase %d, error: %v", memberData.UserId, memberPurchaseId, err)
				return fmt.Errorf("Unable to create subscription for userId %d and purchaseId %d, Error: %v", memberData.UserId, memberPurchaseId, err)
			}
			memberData.SubscriptionId = subscriptionResponse.SubscriptionId

			if requiredProduct.ProductCategoryId == ACADEMY_PRODUCT_CATEGORY_ID {
				for _, data := range memberData.FacilitySlots {
					slotMappingRequest := &productPB.CreateSlotMappingRequest{
						SubscriptionId: subscriptionResponse.SubscriptionId,
						AcademySlotId:  data.AcademySlotId,
						ProductId:      req.ProductId,
						UserId:         memberData.UserId,
					}

					academyResponse, err := productClient.CreateSlotMappingsForAcademy(ctx, slotMappingRequest)
					if err != nil {
						log.Printf("error in creating academy subscription mapping academy slot id %d, user %d with subscription %d, error: %v", data.AcademySlotId, memberData.UserId, subscriptionResponse.SubscriptionId, err)
						return err
					}
					if academyResponse.Status != nil && academyResponse.Status.Status != success {
						log.Printf("error in creating academy subscription mapping academy slot id %d, user %d with subscription %d, message %s", data.AcademySlotId, memberData.UserId, subscriptionResponse.SubscriptionId, academyResponse.Status.Message)
						res.Status = &pb.Status{
							Status:  academyResponse.Status.Status,
							Message: academyResponse.Status.Message,
						}
						return nil
					}
					go CreateAcademySessionBookingsForUser(ctx, memberData.UserId)
				}
			} else if requiredProduct.ProductCategoryId == SUMMERCAMP_PRODUCT_CATEGORY_ID {
				for _, data := range memberData.FacilitySlots {
					slotMappingRequest := &productPB.CreateSlotMappingRequest{
						SubscriptionId:   subscriptionResponse.SubscriptionId,
						SummercampSlotId: data.SummercampSlotId,
						ProductId:        req.ProductId,
						UserId:           memberData.UserId,
						PlanStartDate:    memberData.PlanStartDate.Seconds,
					}

					summercampResponse, err := productClient.CreateSlotMappingsForSummercamp(ctx, slotMappingRequest)
					if err != nil {
						log.Printf("error in creating summercamp subscription mapping summercamp slot id %d, user %d with subscription %d, error: %v", data.SummercampSlotId, memberData.UserId, subscriptionResponse.SubscriptionId, err)
						return err
					}
					if summercampResponse.Status != nil && summercampResponse.Status.Status != success {
						log.Printf("error in creating summercamp subscription mapping summercamp slot id %d, user %d with subscription %d, message %s", data.SummercampSlotId, memberData.UserId, subscriptionResponse.SubscriptionId, summercampResponse.Status.Message)
						res.Status = &pb.Status{
							Status:  summercampResponse.Status.Status,
							Message: summercampResponse.Status.Message,
						}
						return nil
					}
					go CreateSummercampSessionBookingsForUser(ctx, memberData.UserId)
				}
			}
			break

		case 3:
			fallthrough
		case 4:

			if memberData.SubscriptionId > 0 {

				if memberData.UserId == req.UserId {
					// purchase already created -- just update actual amount
					memberPurchaseId = purchaseId
					s.Data.UpdateActualAmount(memberPurchaseId, float32(perPersonActualAmount), float32(req.DiscountPerPerson))

				} else {
					// create purchase
					memberPurchaseObj := primaryPurchaseObj
					memberPurchaseObj.PurchaseId = int32(0)
					memberPurchaseObj.UserId = memberData.UserId
					memberPurchaseObj.Amount = float32(0)
					memberPurchaseObj.ActualAmount = float32(perPersonActualAmount)
					memberPurchaseObj.EffectiveRevenue = float32(perPersonActualAmount)
					memberPurchaseObj.DiscountAmount = float32(req.DiscountPerPerson)
					memberPurchaseObj.LinkedPurchaseId = purchaseId

					err, memberPurchaseId = s.PurchaseCreate(ctx, memberPurchaseObj)
					if err != nil {
						status := &pb.Status{
							Status:  "failure",
							Message: "Could not create child purchase!",
						}
						res.Status = status

						error := &pb.Error{
							Code:        412,
							Description: "Error in creating child purchase!",
						}
						res.Error = append(res.Error, error)
						return nil
					}
				}

				var oldPurchaseId int32

				s.Data.UpdatePurchaseIdToSubscription(memberData.SubscriptionId, memberPurchaseId, &oldPurchaseId)

				if oldPurchaseId > 0 && oldPurchaseId != memberPurchaseId {
					s.Data.UpdatePreviousPurchaseIdToNewPurchase(memberPurchaseId, oldPurchaseId)
				}

			} else {
				status := &pb.Status{
					Status:  "failure",
					Message: "No subscription id found!",
				}
				res.Status = status

				error := &pb.Error{
					Code:        416,
					Description: "No subscription id found!",
				}
				res.Error = append(res.Error, error)
				return nil
			}

			break

		case 5:

			if memberData.SubscriptionId <= 0 {
				res.Status = &pb.Status{
					Status:  "failure",
					Message: "No subscription id found!",
				}
				return nil
			}

			if memberData.UserId == req.UserId {
				// purchase already created -- just update actual amount
				memberPurchaseId = purchaseId
				s.Data.UpdateActualAmount(memberPurchaseId, float32(perPersonActualAmount), float32(req.DiscountPerPerson))
			} else {
				// create purchase
				memberPurchaseObj := primaryPurchaseObj
				memberPurchaseObj.PurchaseId = int32(0)
				memberPurchaseObj.UserId = memberData.UserId
				memberPurchaseObj.Amount = float32(0)
				memberPurchaseObj.ActualAmount = float32(perPersonActualAmount)
				memberPurchaseObj.EffectiveRevenue = float32(perPersonActualAmount)
				memberPurchaseObj.DiscountAmount = float32(req.DiscountPerPerson)
				memberPurchaseObj.LinkedPurchaseId = purchaseId

				err, memberPurchaseId = s.PurchaseCreate(ctx, memberPurchaseObj)
				if err != nil {
					return fmt.Errorf("Could not create child purchase!, error: %v", err)
				}
			}

			// getting previous subscription details
			var subData structs.Subscription
			if err := s.Data.GetSubscriptionDetails(memberData.SubscriptionId, &subData); err != nil {
				log.Println("Error in fetching subscription details", err)
				return fmt.Errorf("Error in fetching subscription details for subsId:%d, Error: %v", memberData.SubscriptionId, err)
			}

			var carryFwdAmt, leftOvrAmt float32
			if err := s.CalculateEffectiveRevenueSports(subData, &carryFwdAmt, &leftOvrAmt); err != nil {
				log.Println("Error in getting effective revenue:", err)
			}

			// updating leftover amt in previous purchase
			if leftOvrAmt >= 0 {
				log.Println("Prev Purchase-id:- ", subData.PurchaseId)
				if err := s.Data.UpdateEffectiveRevenueForPurchase(subData.PurchaseId, leftOvrAmt); err != nil {
					log.Println("Error in updating leftover amt", err)
				}
			}

			// updating carryFwd amt in new purchase
			log.Println("New Purchase-id:- ", memberPurchaseId)
			if carryFwdAmt >= 0 {
				if err := s.Data.UpdateEffectiveRevenueForPurchase(memberPurchaseId, float32(perPersonActualAmount)+carryFwdAmt); err != nil {
					log.Println("Error in updating carryfwd amt", err)
				}
			}

			var newFacilitySlots []*structs.FacilitySlot
			for _, v := range memberData.FacilitySlots {
				fs := &structs.FacilitySlot{AcademySlotId: v.AcademySlotId}
				newFacilitySlots = append(newFacilitySlots, fs)
			}
			upgradeReq := structs.UpgradeSubscriptionRequest{
				UserId:           memberData.UserId,
				SubscriptionId:   memberData.SubscriptionId,
				ProductId:        req.ProductId,
				NewPurchaseId:    memberPurchaseId,
				NewFacilitySlots: newFacilitySlots,
			}
			errS := s.UpgradeSubscription(ctx, &upgradeReq)
			if errS != nil {
				return fmt.Errorf("Could not upgrade child purchaseId for userId: %d, oldPurchase:%d, newPurchase: %d, Error: %v", memberData.UserId, upgradeReq.OldPurchaseId, memberPurchaseId, err)
			}

			if upgradeReq.OldPurchaseId > 0 && upgradeReq.OldPurchaseId != memberPurchaseId {
				s.Data.UpdatePreviousPurchaseIdToNewPurchase(memberPurchaseId, upgradeReq.OldPurchaseId)
			}
			break

		case 8:
			//seals upgrade
			if memberData.UserId == req.UserId {
				// purchase already created -- just update actual amount
				memberPurchaseId = purchaseId
				s.Data.UpdateActualAmount(memberPurchaseId, float32(perPersonActualAmount), float32(req.DiscountPerPerson))

			} else {
				// create purchase
				memberPurchaseObj := primaryPurchaseObj
				memberPurchaseObj.PurchaseId = int32(0)
				memberPurchaseObj.UserId = memberData.UserId
				memberPurchaseObj.Amount = float32(0)
				memberPurchaseObj.ActualAmount = float32(perPersonActualAmount)
				memberPurchaseObj.EffectiveRevenue = float32(perPersonActualAmount)
				memberPurchaseObj.DiscountAmount = float32(req.DiscountPerPerson)
				memberPurchaseObj.LinkedPurchaseId = purchaseId

				err, memberPurchaseId = s.PurchaseCreate(ctx, memberPurchaseObj)
				if err != nil {
					status := &pb.Status{
						Status:  "failure",
						Message: "Could not create child purchase!",
					}
					res.Status = status

					error := &pb.Error{
						Code:        412,
						Description: "Error in creating child purchase!",
					}
					res.Error = append(res.Error, error)
					return nil
				}
			}

			var oldPurchaseId int32

			newSubscriptionId, errS := s.CreateSubscription(ctx, memberPurchaseId, memberData.UserId, req.ProductId, memberData.PlanStartDate.Seconds, memberData.PlanEndDate.Seconds, memberData.PreferredFacilityId)
			memberData.SubscriptionId = newSubscriptionId
			if errS != nil {
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not upgrade child purchase!",
				}
				res.Status = status
				fmt.Println(errS)
				return errS
			}
			if oldPurchaseId > 0 && oldPurchaseId != memberPurchaseId {
				s.Data.UpdatePreviousPurchaseIdToNewPurchase(memberPurchaseId, oldPurchaseId)
			}

			break
		}
		res.SubscriptionId = memberData.SubscriptionId
	}

	if req.PurchaseType != 8 {
		if requiredProduct.ProductCategoryId == MASTERKEY_PRODUCT_CATEGORY_ID || requiredProduct.ProductCategoryId == SINGLEKEY_PRODUCT_CATEGORY_ID || requiredProduct.ProductCategoryId == SUMMERCAMP_PRODUCT_CATEGORY_ID {
			s.GenerateInvoice(ctx, purchaseId, req.UserId)
		}
		if requiredProduct.ProductCategoryId == ACADEMY_PRODUCT_CATEGORY_ID {
			go s.sendWelcomeMailOnPurchaseV2(ctx, purchaseId)
			go s.sendPurchaseReceipt(ctx, purchaseId)
		}

		if requiredProduct.ProductCategoryId == FIXED_PRODUCT_CATEGORY_ID {
			go s.sendPurchaseReceipt(ctx, purchaseId)
		}
		s.SendPurchaseMail(ctx, req)
	}
	go s.publishReceiptGenerationDataToLeadSquared(ctx, req)

	status := &pb.Status{
		Status:  "success",
		Message: "Receipt created successfully!",
	}
	res.Status = status

	return nil
}

func CreateAcademySessionBookingsForUser(ctx context.Context, userId int32) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[PANIC] func: CreateAcademySessionBookingsForUser, UserId: %d, Error: %v", userId, r)
		}
	}()

	bookingClient := util.GetBookingServiceClient()
	currentTime := time.Now().Unix()

	timeToProcess := []int64{currentTime + 3600, currentTime + 86400}
	req := &bookingPB.CreateAcademySessionBookingsRequest{
		TimeToProcess: timeToProcess,
		UserId:        userId,
	}
	_, err := bookingClient.CreateAcademySubscriptionBookings(ctx, req)
	if err != nil {
		log.Printf("Error in populating academy subscription bookings, err: %v", err)
		return
	}
}

func CreateSummercampSessionBookingsForUser(ctx context.Context, userId int32) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[PANIC] func: CreateSummercampSessionBookingsForUser, UserId: %d, Error: %v", userId, r)
		}
	}()

	bookingClient := util.GetBookingServiceClient()
	currentTime := time.Now().Unix()

	timeToProcess := []int64{currentTime + 3600, currentTime + 86400}
	req := &bookingPB.CreateAcademySessionBookingsRequest{
		TimeToProcess: timeToProcess,
		UserId:        userId,
	}
	_, err := bookingClient.CreateSummerCampSubscriptionBookings(ctx, req)
	if err != nil {
		log.Printf("Error in populating summercamp subscription bookings, err: %v", err)
		return
	}
}

func (s *Service) PurchaseCreate(ctx context.Context, purchaseModel *models.Purchase) (error, int32) {

	err, purchaseId := s.Data.PurchaseCreate(purchaseModel)

	if err != nil {
		return errors.New(fmt.Sprintf("error creating purchase: %v", err)), 0
	}

	return nil, purchaseId
}

func (s *Service) CreateSubscription(ctx context.Context, purchaseId int32, userId int32, productId int32, planStartDateTs int64, planEndDateTs int64, preferredFacilityId int32) (int32, error) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	pcl := productPB.NewProductService(serviceList.Product, service.Client())

	subsPlanData := &productPB.PlanData{
		PlanStartDate:       planStartDateTs,
		PlanEndDate:         planEndDateTs,
		PreferredFacilityId: preferredFacilityId,
	}

	subscriptionData := &productPB.CreateSubscriptionRequest{
		UserId:     userId,
		ProductId:  productId,
		PurchaseId: purchaseId,
		PlanData:   subsPlanData,
	}

	response, err := pcl.CreateSubscription(context.TODO(), subscriptionData)
	if err != nil {
		fmt.Println(err)
		return 0, err
	}

	fmt.Println("create subscription response -- ", response)

	return response.SubscriptionId, nil
}

func (s *Service) OptInSubscription(userId int32, subscriptionId int32) error {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	pcl := productPB.NewProductService(serviceList.Product, service.Client())

	reqData := &productPB.OptInSubscriptionRequest{
		SubscriptionId: subscriptionId,
		UserId:         userId,
	}

	_, err := pcl.OptInSubscription(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		return err
	}

	return nil
}

func (s *Service) UpgradeSubscription(ctx context.Context, req *structs.UpgradeSubscriptionRequest) error {

	pcl := util.GetProductServiceClient()
	subscriptionReq := &productPB.UpgradeSubscriptionRequest{
		UserId:         req.UserId,
		SubscriptionId: req.SubscriptionId,
		ProductId:      req.ProductId,
		PurchaseId:     req.NewPurchaseId,
	}

	if len(req.NewFacilitySlots) > 0 {
		for _, v := range req.NewFacilitySlots {
			fs := &productPB.FacilitySlot{
				AcademySlotId: v.AcademySlotId,
			}
			subscriptionReq.FacilitySlots = append(subscriptionReq.FacilitySlots, fs)
		}
	}

	response, err := pcl.UpgradeSubscription(ctx, subscriptionReq)
	if err != nil {
		return err
	}

	log.Println("create subscription response -- ", response)
	req.OldPurchaseId = response.OldPurchaseId
	return nil
}

func (s *Service) getUserData(ctx context.Context, userIds []int32, userDataMap map[int32]models.User) error {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	ucl := userPB.NewUserService(serviceList.User, service.Client())

	userIdsData := &userPB.UserRequest{
		UserIdArray: userIds,
	}

	response, err := ucl.UserGet(context.TODO(), userIdsData)

	if err != nil {
		fmt.Println(err)
		return err
	}

	var userData models.User

	for _, element := range response.Users {
		userData = models.User{
			UserID: element.UserId,
			Phone:  element.Phone,
			Email:  element.Email,
			Name:   element.Name,
		}
		userDataMap[userData.UserID] = userData
	}

	return nil
}

func (s *Service) GenerateInvoice(ctx context.Context, purchaseId int32, userId int32) error {

	requestData := &pb.PurchaseRequest{
		PurchaseId: purchaseId,
	}
	var res pb.Ack
	s.SendMailsForPurchase(ctx, requestData, &res)
	return nil
}

func (s *Service) SendPurchaseMail(ctx context.Context, req *pb.ReceiptGenerationRequest) error {

	toEmails := [10]string{"<EMAIL>", TECH_MAIL, "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
	//toEmails := [1]string{"<EMAIL>"}

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	_purchase_date := req.PurchaseDate
	purchaseDate := time.Unix(_purchase_date.Seconds, int64(_purchase_date.Nanos)).UTC()
	purchaseTimeLocal, err := GetLocalDateTime(purchaseDate)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	if len(req.PaymentMethodType) == 0 {
		var paymentMedium models.PaymentMedium
		if err := s.Data.GetPaymentMediumById(ctx, int32(req.Provider), &paymentMedium); err != nil {
			log.Println("SendPurchaseMail: Error in getting payment medium details: ", err)
		}
		if len(paymentMedium.MediumName) > 0 {
			req.PaymentMethodType = paymentMedium.MediumName
		}
	}

	var userDataMap map[int32]models.User
	userDataMap = make(map[int32]models.User)

	var usersIds []int32
	usersIds = append(usersIds, req.UserId)
	for _, memberData := range req.MembersData {
		usersIds = append(usersIds, memberData.UserId)
	}

	usersIds = append(usersIds, req.GeneratedBy)

	error1 := s.getUserData(ctx, usersIds, userDataMap)

	subjectStr := "Purchase by user : " + userDataMap[req.UserId].Name + " (" + strconv.Itoa(int(req.UserId)) + ")"
	messageStr := "User Id - " + strconv.Itoa(int(req.UserId)) + "<br>"

	if error1 == nil {
		var found bool
		_, found = userDataMap[req.UserId]
		if found == true {
			messageStr += "User Name - " + userDataMap[req.UserId].Name + "<br>"
			messageStr += "Phone No - " + userDataMap[req.UserId].Phone + "<br>"
			messageStr += "Email Id - " + userDataMap[req.UserId].Email + "<br>"

			subjectStr += " Phone : " + userDataMap[req.UserId].Phone + " , Product : Sports"
		}
	}

	if req.ProductId > 0 {
		messageStr += "Product Id - " + strconv.Itoa(int(req.ProductId)) + "<br>"
	}
	messageStr += "Amount Paid - " + strconv.Itoa(int(req.Amount)) + "<br>"
	messageStr += "Purpose - " + req.Purpose + "<br>"
	messageStr += "Purchase Type - " + purchaseTypes[req.PurchaseType] + "<br>"
	messageStr += "Payment Mode - " + req.PaymentMethodType + "<br>"
	messageStr += "Purchase Date - " + purchaseTimeLocal.Format(formatYMD) + "<br><br><br>"

	for _, memberData := range req.MembersData {

		_ps_date := memberData.PlanStartDate
		psDate := time.Unix(_ps_date.Seconds, int64(_ps_date.Nanos)).UTC()
		if psDate == time.Unix(0, 0).UTC() {
			psDate = time.Now()
		}
		psTimeLocal, err := GetLocalDateTime(psDate)

		if err != nil {
			fmt.Println("time converison error -- ", err)
			return err
		}
		messageStr += "Member User Id - " + strconv.Itoa(int(memberData.UserId)) + "<br>"
		if error1 == nil {
			var found bool
			_, found = userDataMap[memberData.UserId]
			if found == true {
				messageStr += "Member User Name - " + string(userDataMap[memberData.UserId].Name) + "<br>"
			}
		}
		messageStr += "Member P.S.Date - " + psTimeLocal.Format(formatYMD) + "<br>"

		if memberData.PreferredFacilityId > 0 {
			var facilityName string
			if err := s.Data.FacilityGet(memberData.PreferredFacilityId, &facilityName); err != nil {
				return errors.New(fmt.Sprintf("error getting facilities: %v", err))
			}
			messageStr += "Preferred Facility - " + facilityName + "<br><br>"
		} else if memberData.ProductId > 0 {
			messageStr += "Product Id - " + strconv.Itoa(int(memberData.ProductId)) + "<br>"
		}

		if len(memberData.FacilitySlots) > 0 {
			for _, facilitySlot := range memberData.FacilitySlots {
				bookingClient := GetBookingClient()
				if facilitySlot.AcademySlotId > 0 {
					getAcademySlotReq := &bookingPB.AcademyFSSlotReq{
						AcademySlotId: facilitySlot.AcademySlotId,
					}
					academyFsSlotRes, err := bookingClient.GetFSSlotDataForGivenAcademySlotId(ctx, getAcademySlotReq)
					if err != nil {
						log.Printf("SendPurchaseMail, error in getting academy slot data for academy slot id: %d, user id: %d, err: %v", facilitySlot.AcademySlotId, memberData.UserId, err)
						continue
					}

					if academyFsSlotRes != nil && len(academyFsSlotRes.AcademyFsSlots) != 0 {
						academySlotData := academyFsSlotRes.AcademyFsSlots[0]
						messageStr += "Facility Sport- " + academySlotData.FsName + "<br>"
						if len(academySlotData.SlotTiming2) > 0 {
							messageStr += "Slot 1 Timing " + academySlotData.SlotTiming1 + "<br>"
							messageStr += "Slot 2 Timing " + academySlotData.SlotTiming1 + "<br>"
						} else {
							messageStr += "Slot Timing " + academySlotData.SlotTiming1 + "<br>"
						}
					}
				} else {
					getSlotReq := &bookingPB.SummercampSlot{
						Id: facilitySlot.SummercampSlotId,
					}
					bookingSlots, err := bookingClient.GetSummercampSlots(ctx, getSlotReq)
					if err != nil {
						log.Printf("Error in getting summercamp slot for ID: %d err %v", facilitySlot.SummercampSlotId, err)
						continue
					}
					var slotId, fsId int32
					for _, v := range bookingSlots.SummercampSlots {
						slotId = v.SlotId
						fsId = v.FsId
					}
					facilityClient := GetFacilitySportClient()
					slotDetailsResponse, err := facilityClient.BatchSlotGet(ctx, &facilitySportPB.BatchSlotGetRequest{
						SlotIds: []int32{slotId},
					})
					if err != nil {
						return fmt.Errorf("Error in getting slot details for slot_ids: %v, error %v", slotId, err)
					}
					slotDataMap := slotDetailsResponse.SlotsMap
					facilitySportDetails, err := facilityClient.BatchGetFacilitySportDetails(ctx, &facilitySportPB.BatchGetFacilitySportDetailsReq{
						FsIds: []int32{fsId},
					})
					if err != nil {
						log.Printf("Error in getting facility details for Id %d, err %v", fsId, err)
						return fmt.Errorf("Error in getting facility details for Id %d, err %v", fsId, err)
					}
					messageStr += "Facility - " + facilitySportDetails.FacilitySportsMap[fsId].DisplayName + "<br>"
					messageStr += "Sport - " + facilitySportDetails.FacilitySportsMap[fsId].SportName + "<br>"
					messageStr += "Slot Timing" + slotDataMap[slotId].Timing + "<br>"
				}
			}
		}
		messageStr += "<br>"
	}

	messageStr += "Generated By : " + userDataMap[req.GeneratedBy].Name

	fmt.Println("memberMessageStr -- ", messageStr)

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		Subject: subjectStr,
		Message: messageStr,
	}

	if err := s.Data.PushPurchaseEmailData(emailReq); err != nil {
		fmt.Println("Error in sending purchase email")
		return err
	}

	return nil
}

func (s *Service) PurchaseOptionsGet(ctx context.Context, req *pb.PurchaseOptionsRequest, res *pb.PurchaseOptionsResponse) error {
	fmt.Println("req ---------", req)

	//populate hot offers -- hard coded preferably for now
	var productCategoryHotOffers []int32
	productCategoryHotOffers = append(productCategoryHotOffers, 2) // 2-> master key products
	productCategoryHotOffers = append(productCategoryHotOffers, 3) // 3-> single key products

	//default city 143 -- push purchase options of gurgaon only in default case for now
	if req.CityId == 0 || req.CityId == 141 || req.CityId == 143 || req.CityId == 142 || req.CityId == 107 {
		req.CityId = 1
	}

	if req.CityId > 0 {

		var ftArr []int32
		ftArr = append(ftArr, 2)
		pcFt := &pb.ProductCategoryFeatureType{
			ProductCategoryIds: productCategoryHotOffers,
			FeatureType:        ftArr,
			CityId:             req.CityId,
		}
		var hotOffers []models.Product
		if err := s.Data.GetProductsForProductCategories(&hotOffers, pcFt); err != nil {
			fmt.Println("Error in. getting hot offers... Db error..")
			return err
		}
		var hotOffersPb []*pb.Product
		for _, offEle := range hotOffers {
			hotOffersPb = append(hotOffersPb, offEle.Proto())
		}
		offerData := &pb.Offer{
			HotOffers: hotOffersPb,
		}
		//get featured masterkey plans
		var productCategoryValues []int32
		productCategoryValues = append(productCategoryValues, 2) // 2-> master key products

		var ftArrMK []int32
		ftArrMK = append(ftArrMK, 1)
		ftArrMK = append(ftArrMK, 2)
		pcFtVal := &pb.ProductCategoryFeatureType{
			ProductCategoryIds: productCategoryValues,
			FeatureType:        ftArrMK,
			CityId:             req.CityId,
		}
		var featuredProducts []models.Product
		if err := s.Data.GetProductsForProductCategories(&featuredProducts, pcFtVal); err != nil {
			fmt.Println("Error in getting featured products.. Db error")
			return err
		}

		var mkeyProducts []*pb.Product
		for _, element := range featuredProducts {
			if element.ProductCategoryId == 2 {
				mkeyProducts = append(mkeyProducts, element.Proto())
			}
		}
		planData := &pb.Plan{
			MasterkeyPlans: mkeyProducts,
		}

		//fetch sports for single key as well
		var productCategoryIds []int32
		productCategoryIds = append(productCategoryIds, 3) // 3-> single key products
		var featuredSkeyProducts []models.Sport

		productCategories := structs.ProductCategoryCity{
			ProductCategoryIds: productCategoryIds,
			CityId:             req.CityId,
		}

		if err := s.Data.GetSportWiseProductsForProductCategories(&featuredSkeyProducts, productCategories); err != nil {
			fmt.Println("Error in getting sport wise single key products...")
			return err
		}

		productSport := make(map[int32][]models.Sport)  //key -> sport_id, val -> products
		productSportVal := make(map[int32]models.Sport) //key -> sport_id, val -> products
		for _, element := range featuredSkeyProducts {

			sport := models.Sport{
				SportID:              element.SportID,
				SportName:            element.SportName,
				Icon:                 element.IconSportsApp,
				DisplayPicture:       element.DisplayPicture,
				SquareDisplayPicture: element.SquareDisplayPicture,
				IsOffered:            element.IsOffered,
				Popularity:           element.Popularity,
			}
			productSportVal[element.SportID] = sport
			productSport[element.SportID] = append(productSport[element.SportID], element)
		}

		var skeyProducts []*pb.Sport
		for i, element := range productSport {
			sportData := productSportVal[i]
			for _, ele := range element {
				prodData := models.Product{
					ProductId:           ele.ProductId,
					DurationInDays:      ele.DurationInDays,
					DurationUnit:        ele.DurationUnit,
					SessionCount:        ele.SessionCount,
					Price:               ele.Price,
					RetailPrice:         ele.RetailPrice,
					LocationId:          ele.LocationId,
					ActiveBookingCount:  ele.ActiveBookingCount,
					ProductDescription:  ele.ProductDescription,
					ProductCategoryId:   ele.ProductCategoryId,
					FeatureType:         ele.FeatureType,
					IsTrial:             ele.IsTrial,
					CategoryName:        ele.CategoryName,
					CategoryDescription: ele.CategoryDescription,
				}
				sportData.Product = append(sportData.Product, prodData.Proto())
			}

			skeyProducts = append(skeyProducts, sportData.Proto())
		}

		// sort single key plans by sport popularity -- desc
		sort.SliceStable(skeyProducts, func(i, j int) bool {
			return skeyProducts[i].Popularity > skeyProducts[j].Popularity
		})

		planData.SinglekeyPlans = skeyProducts

		//get static points for mkey and skey plans
		planData.MasterkeyPoints = GetMasterkeyHighlightPoints()
		planData.SinglekeyPoints = GetSinglekeyHighlightPoints()

		//apply final conditions before sending success
		status := &pb.Status{
			Status:  "success",
			Message: "Purchase options fetched successfully!",
		}
		res.Status = status

		res.Offer = offerData
		res.Plan = planData

	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "No Purchase options available!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) StoreAmazonGiftCards(ctx context.Context, req *pb.AmazonGiftCard, res *pb.Ack) error {
	reqData := &models.AmazonGiftCards{
		Amount:   req.Amount,
		GiftCode: req.GiftCode,
	}

	if err := s.Data.StoreAmazonGiftCards(reqData); err != nil {
		fmt.Println("Error in inserting Amazon Gift Cards ..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in inserting Amazon Gift Cards..",
		}
		res.Status = status
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Recorded successfully!",
	}

	res.Status = status
	return nil
}

func (s *Service) GetUnusedGiftCardCount(ctx context.Context, req *pb.Empty, res *pb.UnusedAmazonGiftCardsCount) error {

	var unusedGiftCardsCount pb.UnusedAmazonGiftCardsCount
	if err := s.Data.GetUnusedGiftCardCount(&unusedGiftCardsCount); err != nil {
		fmt.Println("Error in getting gift card count", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in getting Gift Card Count.",
		}
		res.Status = status
		return err
	}
	res.GiftCardUnused = unusedGiftCardsCount.GiftCardUnused
	status := &pb.Status{
		Status:  "success",
		Message: "Unused Gift Card count fetched successfully",
	}
	res.Status = status

	return nil

}

func (s *Service) GetPurchaseCancellationRecord(ctx context.Context, req *pb.RefundRecordsRequest, res *pb.RefundRecord) error {

	condition := models.RefundRecord{
		PurchaseId:  req.PurchaseId,
		ReferenceId: req.ReferenceId,
	}
	if Int32InSlice(req.RefundStatus, []int32{REFUND_STATUS_INITIATED, REFUND_STATUS_PENDING, REFUND_STATUS_SUCCESS, REFUND_STATUS_FAILED}) {
		condition.Status = req.RefundStatus
	}
	var cancellationRecord models.RefundRecord
	if err := s.Data.GetPurchaseCancellationRecord(ctx, condition, &cancellationRecord); err != nil {
		log.Printf("Error in handler GetUserCancellationRecord in getting cancellation record- purchaseId: %d, Error: %v", req.PurchaseId, err)
		return err
	}
	cancelledOn, _ := ptypes.TimestampProto(cancellationRecord.CreatedAt)
	completeAt, _ := ptypes.TimestampProto(cancellationRecord.CompletedAt)
	res.PurchaseId = cancellationRecord.PurchaseId
	res.Status = cancellationRecord.Status
	res.CreatedAt = cancelledOn
	res.Amount = cancellationRecord.Amount
	res.CompletedAt = completeAt
	res.ReferenceId = cancellationRecord.ReferenceId
	res.Id = cancellationRecord.Id
	res.OrderId = cancellationRecord.OrderId
	res.CreatedBy = cancellationRecord.CreatedBy
	return nil
}

func (s *Service) GetPaymentDetailsByPurchaseID(ctx context.Context, req *pb.PurchaseRequest, res *pb.PurchasePaymentMappingResponse) error {
	condition := models.PurchasePaymentMapping{
		PurchaseID: req.PurchaseId,
	}

	var paymentDetails models.PurchasePaymentMapping
	if err := s.Data.GetPurchaseIDPaymentDetails(ctx, condition, &paymentDetails); err != nil {
		log.Printf("Error in handler GetPaymentDetailsByPurchaseID in getting payment details purchaseId: %d, Error: %v", req.PurchaseId, err)
		return err
	}
	*res = pb.PurchasePaymentMappingResponse{
		PurchaseId:                   paymentDetails.PurchaseID,
		PaymentMethodType:            paymentDetails.PaymentMethodType,
		PaymentInstrumentDisplayName: paymentDetails.PaymentInstrumentDisplayName,
	}
	return nil
}

func (s *Service) GetUserIdsByPurchaseId(ctx context.Context, req *pb.PurchaseRequest, res *pb.GetUserIdsByPurchaseIdResponse) error {
	userIds, err := s.Data.GetUserIdsByPurchaseId(ctx, req.PurchaseId)

	if err != nil {
		log.Printf("unable to fetch user ids for given purchase id: %d, error: %v", req.PurchaseId, err)
		return err
	}
	res.UserIds = userIds
	return nil
}

func (s *Service) GetActivePurchaseByUser(ctx context.Context, req *pb.GetPurchaseHistoryRequest, res *pb.PurchaseResponse) error {
	purchases, err := s.Data.GetActivePurchaseByUser(ctx, req.UserId)

	if err != nil {
		log.Printf("unable to get active purchase for given userId: %d, Error: %v", req.UserId, err)
		return err
	}

	for _, val := range purchases {
		res.Purchases = append(res.Purchases, val.Proto())
	}
	return nil
}

func (s *Service) publishReceiptGenerationDataToLeadSquared(ctx context.Context, req *pb.ReceiptGenerationRequest) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from GenerateReceipt publishReceiptGenerationDataToLeadSquared panic- ", r)
		}
	}()
	subscriptionCount := len(req.MembersData)
	if (req.PurchaseType != fullPaymentPurchase && req.PurchaseType != PPFirstInstallmentPurchase) || req.UserId <= 0 || subscriptionCount <= 0 {
		return
	}
	userRequest := &userPB.UserRequest{
		UserId: req.UserId,
	}
	ucl := GetUserServiceClient()
	usersData, err := ucl.UserGet(ctx, userRequest)
	if err != nil {
		log.Printf("publishReceiptGenerationDataToLeadSquared: Error in getting user details for user id %d, err: %v", req.UserId, err)
		return
	}
	if len(usersData.Users) == 0 {
		return
	}
	purchaseByUserData := usersData.Users[0]
	leadsData := &productPB.LeadSquaredLead{
		Remark:    PLAN_PURCHASED,
		FirstName: purchaseByUserData.Name,
		Mobile:    purchaseByUserData.Phone,
	}
	var subscriptionFor string
	var isSet bool
	for index, memberData := range req.MembersData {
		if req.UserId == memberData.UserId {
			if subscriptionCount > 1 {
				subscriptionFor = PURCHASE_BOTH
			} else {
				subscriptionFor = PURCHASE_SELF
			}
			isSet = true
		} else if index == subscriptionCount-1 {
			subscriptionFor = PURCHASE_CHILD
			isSet = true
		}
		if !isSet {
			continue
		}
		productClient := GetProductServiceClient()
		productReq := &productPB.Product{
			ProductId: req.ProductId,
		}
		product, err := productClient.GetProductDetails(ctx, productReq)
		if err != nil {
			log.Printf("publishReceiptGenerationDataToLeadSquared: Error in getting product details for product id: %d, err: %v", req.ProductId, err)
			return
		}
		purchaseData := &productPB.PurchaseDetails{
			Age:               memberData.Age,
			PlanDuration:      fmt.Sprint(product.DurationInDays) + " Days",
			AmountPaid:        float32(req.Amount),
			ProductName:       product.ProductDescription,
			SubscriptionCount: int32(subscriptionCount),
			SubscriptionFor:   subscriptionFor,
			PlanStartDate:     memberData.PlanStartDate,
		}
		planStartDate, err := ptypes.Timestamp(memberData.PlanStartDate)
		if err != nil {
			log.Printf("publishReceiptGenerationDataToLeadSquared: Error in converting google.protobuf.Timestamp planStartDate to time.Time %s, error: %v", memberData.PlanStartDate, err)
			return
		}
		planStartDateLocal, err := GetLocalDateTime(planStartDate)
		if err != nil {
			log.Printf("publishReceiptGenerationDataToLeadSquared: Error in getting local date to publish purchase data to leadsquared for user_id: %d with error: %v", req.UserId, err)
			return
		}
		planEndDateLocal := planStartDateLocal.AddDate(0, 0, int(product.DurationInDays))
		planEndDate, err := ptypes.TimestampProto(Eod(planEndDateLocal))
		if err != nil {
			log.Printf("publishReceiptGenerationDataToLeadSquared: Error in converting planEndDateLocal %s to google.protobuf.Timestamp, error: %v", planEndDateLocal.String(), err)
			return
		}
		purchaseData.PlanEndDate = planEndDate
		leadsData.PurchaseDetails = purchaseData
		// publishing purchase data to leadsquared topic
		response, err := productClient.PublishLeadSquaredCaptureMessage(ctx, leadsData)
		if err != nil {
			log.Printf("publishReceiptGenerationDataToLeadSquared: Error in publishing purchase data to leadsquared for user id: %d,with res %v and err: %v", req.UserId, response, err)
			return
		}
		return
	}
}

func (s *Service) GetUsersPurchaseOfLastNthDay(ctx context.Context, req *pb.GetUsersPurchaseOfLastNthDayReq, res *pb.GetUsersPurchaseOfLastNthDayRes) error {

	purchases, err := s.Data.GetPurchasesofLastNthDay(ctx, req.NthDay)
	if err != nil {
		log.Printf("func:GetUsersPurchaseOfLastNthDay, unable to get last %dth day purchases Error: %v", req.NthDay, err)
		return err
	}

	for _, val := range purchases {
		res.UserIds = append(res.UserIds, val.UserId)
	}
	return nil
}

func (s *Service) GetUsersPurchaseForOneTimeNPSSurvey(ctx context.Context, req *pb.Empty, res *pb.GetUsersPurchaseForOneTimeNPSSurveyRes) error {
	purchases, err := s.Data.GetPurchaseForOneTimeNPSSurvey(ctx)
	if err != nil {
		log.Printf("func:GetUsersPurchaseForOneTimeNPSSurvey, unable to get user one time purchase for nps survey Error: %v", err)
		return err
	}

	for _, val := range purchases {
		res.UserIds = append(res.UserIds, val.UserId)
	}
	return nil
}
