package purchaseHandler

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"bitbucket.org/jogocoin/go_api/purchase_service/structs"
	"github.com/golang/protobuf/ptypes"

	"bytes"

	"bitbucket.org/jogocoin/go_api/purchase_service/data"
	models "bitbucket.org/jogocoin/go_api/purchase_service/data/model"
	"bitbucket.org/jogocoin/go_api/purchase_service/internal/featuresupport"
	"bitbucket.org/jogocoin/go_api/purchase_service/internal/util"
	facilitySportPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/product"
	pb "bitbucket.org/jogocoin/go_api/purchase_service/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/user"
	"github.com/micro/go-micro/config"
	"golang.org/x/net/context"
)

const (
	paymentMethodEventType = "payment_status_update"
)

func (s *Service) MakePurchase(ctx context.Context, req *pb.PurchaseInitiateRequest, res *pb.MakePurchaseResponse) error {
	userID := util.GetUserIDFromContext(ctx)

	if userID == 0 {
		res.Status = &pb.Status{
			Status:  notAuthorized,
			Message: "You are not allowed to make this request!",
		}
		return nil
	}

	pcl := GetProductServiceClient()

	var childCartUsers []*productPB.CartUser

	var parentUser *productPB.CartUser

	if req.ParentUser != nil {
		user := req.ParentUser
		parentUser = &productPB.CartUser{
			Phone:               user.Phone,
			PlanStartDate:       user.PlanStartDate,
			Name:                user.Name,
			PreferredFacilityId: user.PreferredFacilityId,
			PreferredSportId:    user.PreferredSportId,
			Age:                 user.Age,
			ProductId:           user.ProductId,
		}
	}

	var parentUserPhoneNo string
	var parentUserEmail string

	ucl := GetUserServiceClient()
	userRequest := &userPB.UserRequest{
		UserId: userID,
	}

	usersData, err := ucl.UserGet(ctx, userRequest)
	if err != nil {
		log.Printf("func:MakePurchase, error in getting user data for userID: %d, err:%v", userID, err)
		return err
	}

	parentUserData := usersData.Users[0]
	parentUserPhoneNo = parentUserData.Phone
	parentUserEmail = parentUserData.Email
	if len(parentUserData.Email) == 0 {
		parentUserEmail = "<EMAIL>"
	}

	for _, cartUser := range req.ChildUsers {
		childCartUsers = append(childCartUsers, &productPB.CartUser{
			UserId:              cartUser.UserId,
			Phone:               cartUser.Phone,
			PlanStartDate:       cartUser.PlanStartDate,
			Name:                cartUser.Name,
			PreferredFacilityId: cartUser.PreferredFacilityId,
			PreferredSportId:    cartUser.PreferredSportId,
			Age:                 cartUser.Age,
			ProductId:           cartUser.ProductId,
		})

	}

	validateCartUsers := &productPB.CalculateCartRequest{
		ProductId:         req.ProductId,
		ChildUsers:        childCartUsers,
		ParentUser:        parentUser,
		PromoCode:         req.PromoCode,
		ProductCategoryId: req.ProductCategoryId,
	}

	calculatedCart, err := pcl.CalculateCart(ctx, validateCartUsers)
	if err != nil {
		log.Printf("Can not validate cart users with userId %d, error: %v", userID, err)
		return err
	}

	if calculatedCart.CartError {
		res.Status = &pb.Status{
			Status:  failed,
			Message: calculatedCart.CartErrorMessage,
		}
		toEmails := []string{TECH_MAIL}
		s.SendEmail(toEmails, emailSubForMakePurchaseError, calculatedCart.CartErrorMessage)
		return nil
	}

	if calculatedCart.Status != nil {
		res.Status = &pb.Status{
			Status:  calculatedCart.Status.Status,
			Message: calculatedCart.Status.Message,
		}
		return nil
	}

	if calculatedCart.TotalCartValue < 0 {
		log.Printf("total cart value cannot be less than zero %v", calculatedCart)
		return nil
	}

	if len(calculatedCart.FinalCartUsers) == 0 {
		log.Printf("length of cart users cannot be empty %v", calculatedCart)
		return nil
	}

	isLoggedInUserSubscribing := false
	var loggedInCartUser *productPB.CartUser

	for _, user := range calculatedCart.FinalCartUsers {
		if user.UserId == userID {
			isLoggedInUserSubscribing = true
			loggedInCartUser = user
		}
		if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			user.ProductId = calculatedCart.SubscriptionProductId
		}
	}

	var paymentProviderRes pb.SupportedPaymentProviderResponse
	if err := s.GetSupportedPaymentProvider(ctx, &pb.Empty{}, &paymentProviderRes); err != nil {
		log.Println("error in getting supported payment provider")
		return err
	}

	var provider int32 = paymentProviderRes.PaymentProvider

	res.PaymentProvider = provider

	purchaseModel := &models.Purchase{
		UserId:           userID,
		ProductId:        calculatedCart.SubscriptionProductId,
		Provider:         provider,
		PaymentStatus:    progressPaymentStatus,
		Amount:           calculatedCart.TotalCartValue,
		PurchaseDate:     time.Now(),
		PurchaseType:     1,
		OfflinePayment:   0,
		LinkedPurchaseId: 0,
		VoucherId:        calculatedCart.VoucherId,
		Purpose:          util.GetClientIDFromContext(ctx),
		ReferralId:       calculatedCart.ReferralId,
	}

	if isLoggedInUserSubscribing {
		purchaseModel.ActualAmount = loggedInCartUser.Amount
		purchaseModel.EffectiveRevenue = loggedInCartUser.Amount
		purchaseModel.DiscountAmount = loggedInCartUser.DiscountAmount
	}

	if provider == razorpayPaymentProvider {

		orderData := &structs.PaymentHashData{
			Amount: calculatedCart.TotalCartValue,
		}
		orderId, err := getOrderIdViaRazorpay(ctx, orderData)
		if orderId == "" || err != nil {
			log.Printf("purchase cannot be created for amount %f, userId %d, err %v", orderData.Amount, userID, err)
			return fmt.Errorf("Payment hash cannot be empty for purchaseID %v", err)
		}
		purchaseModel.PaymentRequestToken = orderId

		logoImage := "https://fitso-images.curefit.co/uploads/5121641371930.png"
		themeColor := "#EF4F5F"
		if featuresupport.SupportsNewColor(ctx) {
			logoImage = FITSO_BLACK_LOGO_IMAGE
			themeColor = "#FF3866"
		}

		out := map[string]interface{}{
			"name":        "cult.fit",
			"description": "Membership Charges",
			"image":       logoImage,
			"order_id":    orderId,
			"theme.color": themeColor,
			"currency":    "INR",
			"amount":      calculatedCart.TotalCartValue,
			"key_id":      config.Get("razorpay", "keyId").String(""),
			"prefill": map[string]interface{}{
				"contact": parentUserPhoneNo,
				"email":   parentUserEmail,
			},
		}

		payload, err := json.Marshal(out)
		if err != nil {
			log.Println("error in marshalling the payload data")
			return fmt.Errorf("error in marshalling razorpay_data")
		}
		res.RazorpayData = string(payload)
	}

	err, purchaseID := s.PurchaseCreate(ctx, purchaseModel)
	if err != nil {
		log.Printf("error in creating parent purchase: %v for userID %d", err, userID)
		return err
	}

	userPurchaseMap := make(map[int32]int32)

	if isLoggedInUserSubscribing {
		userPurchaseMap[userID] = purchaseID
	}

	res.PurchaseId = fmt.Sprintf("%d", purchaseID)

	makePurchaseData := &data.MakePurchaseData{
		CalculatedCart:    calculatedCart,
		UserPurchaseMap:   userPurchaseMap,
		PaymentMethodType: req.PaymentMethodType,
	}

	err = s.CachePurchaseData(ctx, purchaseID, makePurchaseData)
	if err != nil {
		log.Printf("error in caching purchase: %d for userID %d", purchaseID, userID)
		return err
	}

	if provider == zomatoPaymentProvider {
		hashData := &structs.PaymentHashData{
			PaymentMethodID:   req.PaymentMethodId,
			PaymentMethodType: req.PaymentMethodType,
			Amount:            calculatedCart.TotalCartValue,
			CountryID:         countryIDIndia,
			PurchaseID:        purchaseID,
		}

		paymentHash := GetHashFromPaymentService(ctx, hashData)
		if paymentHash == "" {
			log.Printf("Payment hash cannot be empty for purchaseID %d", purchaseID)
			return fmt.Errorf("Payment hash cannot be empty for purchaseID %d", purchaseID)
		}

		res.PaymentHash = paymentHash
	}

	purchaseMappingData := &models.PurchasePaymentMapping{
		PurchaseID:        purchaseID,
		PaymentMethodType: req.PaymentMethodType,
		PaymentMethodID:   req.PaymentMethodId,
	}

	s.Data.CreatePurchasePaymentMapping(ctx, purchaseMappingData)

	return nil
}

func getOrderIdViaRazorpay(ctx context.Context, paymentsData *structs.PaymentHashData) (string, error) {
	host := config.Get("razorpay", "host").String("")
	keyId := config.Get("razorpay", "keyId").String("")
	keySecret := config.Get("razorpay", "keySecret").String("")

	headers := map[string]string{
		util.Authorization: fmt.Sprintf("Basic %s", util.BasicAuth(keyId, keySecret)),
		util.ContentType:   util.JsonContentType,
	}

	a := math.Round(float64(paymentsData.Amount)*100) * 100 / 100

	paymentInfo := structs.RazorpayOrderRequest{
		Amount:   int64(a),
		Currency: "INR",
	}

	serializedData, err := json.Marshal(paymentInfo)
	if err != nil {
		log.Println("func:getOrderIdViaRazorpay, error in marshalling, err:", err)
		return "", err
	}

	request := &util.Request{
		Method:      util.MethodTypePost,
		RequestURL:  fmt.Sprintf("%s/v1/orders", host),
		Headers:     headers,
		RequestBody: bytes.NewReader(serializedData),
	}

	razorpayOrderResponse := structs.RazorpayOrderResponse{}
	response, err := util.MakeHTTPRequest(ctx, request)
	if err != nil {
		log.Printf("func:getOrderIdViaRazorpay, error in making http request, err: %v", err)
		return "", err
	}

	if response.Status != http.StatusOK {
		log.Printf("func:getOrderIdViaRazorpay, Error in razorpay response %v", response)
		return "", fmt.Errorf("func:getOrderIdViaRazorpay, Error in razorpay response %v", response)
	}

	if err = json.Unmarshal([]byte(response.Body), &razorpayOrderResponse); err != nil {
		log.Println("func:getOrderIdViaRazorpay, error in unmarshalling, err:", err)
		return "", err
	}

	return razorpayOrderResponse.Id, nil
}

func GetHashFromPaymentService(ctx context.Context, paymentsData *structs.PaymentHashData) string {
	host := config.Get("payments", "host").String("")
	username := config.Get("payments", "username").String("")
	password := config.Get("payments", "password").String("")
	companyID := config.Get("payments", "company_id").String("")
	serviceType := config.Get("payments", "service_type").String("")

	headers := map[string]string{
		util.Authorization: fmt.Sprintf("Basic %s", util.BasicAuth(username, password)),
		util.ContentType:   util.FormContentType,
		"X-COMPANY-ID":     companyID,
	}

	paymentInfo := structs.PaymentInfo{
		PaymentMethodID:   paymentsData.PaymentMethodID,
		Amount:            paymentsData.Amount,
		PaymentMethodType: paymentsData.PaymentMethodType,
		CountryID:         paymentsData.CountryID,
	}

	serializedData, err := json.Marshal(paymentInfo)

	if err != nil {
		log.Printf("[Payment hash] error in serializing payment info for purchaseID: %d with error: %v", paymentsData.PurchaseID, err)

		return ""
	}

	data := url.Values{}
	data.Set("service_type", serviceType)
	data.Set("user_id", fmt.Sprintf("%v", util.GetUserIDFromContext(ctx)))
	data.Set("order_id", fmt.Sprintf("%v", paymentsData.PurchaseID))

	data.Set("payments_info_data", string(serializedData))

	request := &util.Request{
		Method:      util.MethodTypePost,
		RequestURL:  fmt.Sprintf("%s/v2/payment/get_hash", host),
		Headers:     headers,
		RequestBody: strings.NewReader(data.Encode()),
	}

	paymentsHashResponse := structs.PaymentHashReqResponse{}

	response, err := util.MakeHTTPRequest(ctx, request)

	if err != nil {
		log.Printf("[Payment hash] error in getting payment hash for purchaseID: %s with error: %v", paymentsData.PurchaseID, err)

		return ""
	}

	err = json.Unmarshal([]byte(response.Body), &paymentsHashResponse)

	if err != nil {
		log.Printf("[Payment hash]  error in unmarshalling payment hash response for purchaseID: %d with error: %v", paymentsData.PurchaseID, err)

		return ""
	}

	return paymentsHashResponse.PaymentsHash
}

func (s *Service) CachePurchaseData(ctx context.Context, purchaseID int32, data *data.MakePurchaseData) error {
	err := s.Data.CacheMakePurchaseData(ctx, purchaseID, data)
	log.Printf("CachePurchaseData: purchase id %d, data: %v", purchaseID, data)
	if err != nil {
		return err
	}

	return nil
}

// MakeSubscriptionForPurchase creates subscriptions corresponding to purchase id
func (s *Service) MakeSubscriptionForPurchase(ctx context.Context, req *pb.AddPurchaseSubscription, res *pb.Ack) error {
	purchaseID := req.PurchaseId

	if req.EventType != paymentMethodEventType {
		log.Printf("not a valid event for purchase id %d", purchaseID)

		return nil
	}

	if purchaseID == 0 {
		log.Printf("can not create subscription for invalid purchase id %d", purchaseID)

		return nil
	}

	purchase := &models.Purchase{
		PurchaseId: purchaseID,
	}

	var purchaseData []models.Purchase

	err := s.Data.PurchaseGet(purchase, &purchaseData)

	if err != nil {
		log.Printf("error in getting purchase data: %v for purchaseID %d", err, purchaseID)

		return err
	}

	if len(purchaseData) == 0 {
		log.Printf("purchase with purchaseID %d not found", purchaseID)

		return fmt.Errorf("purchase with purchaseID %d not found", purchaseID)
	}

	subPurchaseData := purchaseData[0]
	currentTime := time.Now()
	var purchaseStatusAfter24Hours bool

	if currentTime.Unix() >= subPurchaseData.CreatedAt.Add(24*time.Hour).Unix() {
		purchaseStatusAfter24Hours = true
	}
	pcl := GetProductServiceClient()

	makePurchaseData, err := s.Data.GetCachedMakePurchaseData(ctx, purchaseID)

	if err != nil {
		log.Printf("error in retrieving cached purchase data: %v for purchaseID %d", err, purchaseID)

		return err
	}

	if req.PaymentStatus == paymentServiceSuccessStatus && !purchaseStatusAfter24Hours && !makePurchaseData.IsAcademy && !makePurchaseData.IsSummerCamp {
		s.createParentChildMap(ctx, makePurchaseData, &subPurchaseData)

		s.createChildPurchase(ctx, makePurchaseData, &subPurchaseData)

		s.UpdatePurchasePaymentStatus(ctx, purchaseID, completePaymentStatus, req.RazorpayPaymentId, req.AmountCollected, req.MetaData)

		var preferredFacilityId int32
		for idx, cartUser := range makePurchaseData.CalculatedCart.FinalCartUsers {
			if idx == 0 {
				preferredFacilityId = cartUser.PreferredFacilityId
			}
			userPurchaseID := makePurchaseData.UserPurchaseMap[cartUser.UserId]

			if userPurchaseID != purchaseID {
				err = s.UpdatePurchasePaymentStatus(ctx, userPurchaseID, completePaymentStatus, "", 0.0, "")

				if err != nil {
					log.Printf("error in updating payment status %d for purchase %d with error: %v", completePaymentStatus, userPurchaseID, err)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in updating payment status %d for purchase %d with error: %v", completePaymentStatus, userPurchaseID, err))
					continue
				}
			}

			planData := &productPB.PlanData{
				PlanStartDate:       cartUser.PlanStartDate,
				PreferredFacilityId: cartUser.PreferredFacilityId,
				PreferredSportId:    cartUser.PreferredSportId,
			}

			subcriptionRequest := &productPB.CreateSubscriptionRequest{
				UserId:          cartUser.UserId,
				ProductId:       cartUser.ProductId,
				PurchaseId:      userPurchaseID,
				PlanData:        planData,
				ParentVoucherId: subPurchaseData.VoucherId,
				ParentUserId:    subPurchaseData.UserId,
			}

			_, err := pcl.CreateSubscriptionV2(ctx, subcriptionRequest)

			if err != nil {
				log.Printf("error in creating subscription for user %d with purchase %d, error: %v", cartUser.UserId, makePurchaseData.UserPurchaseMap[cartUser.UserId], err)
				toEmails := []string{TECH_MAIL}
				s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating subscription for user %d with purchase %d, error: %v", cartUser.UserId, makePurchaseData.UserPurchaseMap[cartUser.UserId], err))
				continue
			}

			log.Printf("Subscription for purchase id %d created successfully", userPurchaseID)
		}

		go s.sendWelcomeMailOnPurchaseV2(ctx, purchaseID)
		go s.sendPurchaseCompleteNotification(ctx, makePurchaseData, purchaseID)
		go deleteUserCategoryCache(ctx, subPurchaseData.UserId, subPurchaseData.ProductId)
		go publishPurchaseDataToLeadSquared(ctx, makePurchaseData.CalculatedCart, &subPurchaseData)
		go s.sendPurchaseMailToFounders(ctx, makePurchaseData, &subPurchaseData)
		s.GenerateInvoiceV2(ctx, purchaseID, preferredFacilityId)

		err := s.setReferralClaimsAndRemoveEligibility(ctx, req, &subPurchaseData)
		if err != nil {
			log.Printf("error in setting referral claims and eligibility for purchaseId: %d with error: %v", purchaseID, err)
			return err
		}

	} else if req.PaymentStatus == paymentServiceSuccessStatus && !purchaseStatusAfter24Hours && makePurchaseData.IsAcademy {
		s.createChildPurchase(ctx, makePurchaseData, &subPurchaseData)

		s.UpdatePurchasePaymentStatus(ctx, purchaseID, completePaymentStatus, req.RazorpayPaymentId, req.AmountCollected, req.MetaData)

		for _, cartUser := range makePurchaseData.CalculatedCart.FinalCartUsers {
			userPurchaseID := makePurchaseData.UserPurchaseMap[cartUser.UserId]

			if userPurchaseID != purchaseID {
				err = s.UpdatePurchasePaymentStatus(ctx, userPurchaseID, completePaymentStatus, "", 0.0, "")

				if err != nil {
					log.Printf("error in updating payment status %d for purchase %d with error: %v", completePaymentStatus, userPurchaseID, err)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in updating payment status %d for purchase %d with error: %v", completePaymentStatus, userPurchaseID, err))
					continue
				}
			}

			planData := &productPB.PlanData{
				PlanStartDate: cartUser.PlanStartDate,
			}

			academySlotId := int32(0)
			if len(cartUser.FacilitySlots) > 0 {
				academySlotId = cartUser.FacilitySlots[0].AcademySlotId
			}
			subcriptionRequest := &productPB.CreateSubscriptionRequest{
				UserId:        cartUser.UserId,
				ProductId:     cartUser.ProductId,
				PurchaseId:    userPurchaseID,
				PlanData:      planData,
				AcademySlotId: academySlotId,
			}

			subscriptionResponse, err := pcl.CreateSubscriptionV2(ctx, subcriptionRequest)
			if err != nil || subscriptionResponse.SubscriptionId == 0 {
				log.Printf("error in creating academy subscription for user %d with purchase %d, error: %v", cartUser.UserId, makePurchaseData.UserPurchaseMap[cartUser.UserId], err)
				toEmails := []string{TECH_MAIL}
				s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating academy subscription for user %d with purchase %d, error: %v", cartUser.UserId, makePurchaseData.UserPurchaseMap[cartUser.UserId], err))
				continue
			}

			for _, data := range cartUser.FacilitySlots {
				slotMappingRequest := &productPB.CreateSlotMappingRequest{
					SubscriptionId: subscriptionResponse.SubscriptionId,
					ProductId:      cartUser.ProductId,
					AcademySlotId:  data.AcademySlotId,
					UserId:         cartUser.UserId,
				}

				academyResponse, err := pcl.CreateSlotMappingsForAcademy(ctx, slotMappingRequest)
				if err != nil {
					log.Printf("error in creating academy subscription mapping academy slot %d, user %d with subscription %d, error: %v", data.AcademySlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, err)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating academy subscription mapping academy slot %d, user %d with subscription %d, error: %v", data.AcademySlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, err))
					continue
				}
				if academyResponse.Status != nil && academyResponse.Status.Status != success {
					log.Printf("error in creating academy subscription mapping academy slot %d, user %d with subscription %d, message %s", data.AcademySlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, academyResponse.Status.Message)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating academy subscription mapping academy slot %d, user %d with subscription %d, message %s", data.AcademySlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, academyResponse.Status.Message))
					continue
				}
			}

			go CreateAcademySessionBookingsForUser(ctx, cartUser.UserId)
			go deleteUserCategoryCache(ctx, cartUser.UserId, cartUser.ProductId)
			log.Printf("Academy Subscription for purchase id %d created successfully", userPurchaseID)
		}
		go s.sendWelcomeMailOnPurchaseV2(ctx, purchaseID)
		go s.sendPurchaseCompleteNotification(ctx, makePurchaseData, purchaseID)
		//go publishPurchaseDataToLeadSquared(ctx, makePurchaseData.CalculatedCart, &subPurchaseData)
		go s.sendPurchaseMailToFounders(ctx, makePurchaseData, &subPurchaseData)
		go s.sendPurchaseReceipt(ctx, purchaseID)

		err := s.setReferralClaimsAndRemoveEligibility(ctx, req, &subPurchaseData)
		if err != nil {
			log.Printf("error in setting referral claims and eligibility for purchaseId: %d with error: %v", purchaseID, err)
			return err
		}
	} else if req.PaymentStatus == paymentServiceSuccessStatus && !purchaseStatusAfter24Hours && makePurchaseData.IsSummerCamp {
		s.createChildPurchase(ctx, makePurchaseData, &subPurchaseData)

		s.UpdatePurchasePaymentStatus(ctx, purchaseID, completePaymentStatus, req.RazorpayPaymentId, req.AmountCollected, req.MetaData)

		for _, cartUser := range makePurchaseData.CalculatedCart.FinalCartUsers {
			userPurchaseID := makePurchaseData.UserPurchaseMap[cartUser.UserId]

			if userPurchaseID != purchaseID {
				err = s.UpdatePurchasePaymentStatus(ctx, userPurchaseID, completePaymentStatus, "", 0.0, "")

				if err != nil {
					log.Printf("error in updating payment status %d for purchase %d with error: %v", completePaymentStatus, userPurchaseID, err)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in updating payment status %d for purchase %d with error: %v", completePaymentStatus, userPurchaseID, err))
					continue
				}
			}

			planData := &productPB.PlanData{
				PlanStartDate: cartUser.PlanStartDate,
			}

			subcriptionRequest := &productPB.CreateSubscriptionRequest{
				UserId:     cartUser.UserId,
				ProductId:  cartUser.ProductId,
				PurchaseId: userPurchaseID,
				PlanData:   planData,
			}

			subscriptionResponse, err := pcl.CreateSubscriptionV2(ctx, subcriptionRequest)
			if err != nil || subscriptionResponse.SubscriptionId == 0 {
				log.Printf("error in creating summercamp subscription for user %d with purchase %d, error: %v", cartUser.UserId, makePurchaseData.UserPurchaseMap[cartUser.UserId], err)
				toEmails := []string{TECH_MAIL}
				s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating summer camp subscription for user %d with purchase %d, error: %v", cartUser.UserId, makePurchaseData.UserPurchaseMap[cartUser.UserId], err))
				continue
			}

			for _, data := range cartUser.FacilitySlots {
				slotMappingRequest := &productPB.CreateSlotMappingRequest{
					SubscriptionId:   subscriptionResponse.SubscriptionId,
					ProductId:        cartUser.ProductId,
					SummercampSlotId: data.SummercampSlotId,
					UserId:           cartUser.UserId,
					PlanStartDate:    cartUser.PlanStartDate,
				}

				summercampResponse, err := pcl.CreateSlotMappingsForSummercamp(ctx, slotMappingRequest)
				if err != nil {
					log.Printf("error in creating summercamp subscription mapping summercamp slot %d, user %d with subscription %d, error: %v", data.SummercampSlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, err)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating summercamp subscription mapping summercamp slot %d, user %d with subscription %d, error: %v", data.SummercampSlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, err))
					continue
				}
				if summercampResponse.Status != nil && summercampResponse.Status.Status != success {
					log.Printf("error in creating summercamp subscription mapping summercamp slot %d, user %d with subscription %d, message %s", data.SummercampSlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, summercampResponse.Status.Message)
					toEmails := []string{TECH_MAIL}
					s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("error in creating summercamp subscription mapping summercamp slot %d, user %d with subscription %d, message %s", data.SummercampSlotId, cartUser.UserId, subscriptionResponse.SubscriptionId, summercampResponse.Status.Message))
					continue
				}
			}

			//go CreateAcademySessionBookingsForUser(ctx, cartUser.UserId)
			go CreateSummercampSessionBookingsForUser(ctx, cartUser.UserId)
			go deleteUserCategoryCache(ctx, cartUser.UserId, cartUser.ProductId)
			log.Printf("Summercamp Subscription for purchase id %d created successfully", userPurchaseID)
		}
		go s.sendWelcomeMailOnPurchaseV2(ctx, purchaseID)
		go s.sendPurchaseCompleteNotification(ctx, makePurchaseData, purchaseID)
		go publishPurchaseDataToLeadSquared(ctx, makePurchaseData.CalculatedCart, &subPurchaseData)
		go s.sendPurchaseMailToFounders(ctx, makePurchaseData, &subPurchaseData)
		go s.sendPurchaseReceipt(ctx, purchaseID)

	} else if req.PaymentStatus == paymentServiceSuccessStatus {
		//initiate refund
		if subPurchaseData.PaymentStatus != failedPaymentStatus {
			err := s.UpdatePurchasePaymentStatus(ctx, subPurchaseData.PurchaseId, failedPaymentStatus, req.RazorpayPaymentId, req.AmountCollected, req.MetaData)
			if err != nil {
				log.Printf("error in updating payment status %d for purchase %d with error: %v", failedPaymentStatus, subPurchaseData.PurchaseId, err)
				return err
			}
		}
		err := s.RefundAmountForFailedPurchase(ctx, makePurchaseData, subPurchaseData)
		if err != nil {
			log.Printf("Error in initiating refund for purchase %d with error: %v", subPurchaseData.PurchaseId, err)
			return err
		}

	} else if subPurchaseData.PaymentStatus != completePaymentStatus {

		err = s.UpdatePurchasePaymentStatus(ctx, purchaseID, failedPaymentStatus, req.RazorpayPaymentId, req.AmountCollected, req.MetaData)

		if err != nil {
			log.Printf("error in updating payment status %d for purchase %d with error: %v", failedPaymentStatus, purchaseID, err)
			return err
		}
	}

	res.Status = &pb.Status{
		Status:  success,
		Message: fmt.Sprintf("Subscription's for purchase id %d created successfully", purchaseID),
	}

	return nil
}

func (s *Service) setReferralClaimsAndRemoveEligibility(ctx context.Context, req *pb.AddPurchaseSubscription, subPurchaseData *models.Purchase) error {
	//entry for giving referral rewards
	pcl := GetProductServiceClient()

	if subPurchaseData.ReferralId > 0 {

		req := &productPB.SportPendingReferralClaimReq{
			ReferralId: subPurchaseData.ReferralId,
			UserId:     subPurchaseData.UserId,
			PurchaseId: subPurchaseData.PurchaseId,
		}

		_, err := pcl.CreateSportPendingReferralClaim(ctx, req)
		if err != nil {
			log.Printf("can not create sport pending referral claim error: %v", err)
			return err
		}

	}

	// user should not be eligible for referral after successfull purchase
	_, err := pcl.RemoveReferralEligiblityOfUser(ctx, &productPB.User{UserId: subPurchaseData.UserId})
	if err != nil {
		log.Printf("can not remove user referral eligiblity after successfull purchase, error: %v", err)
		return err

	}
	return nil
}

func (s *Service) sendPurchaseMailToFounders(ctx context.Context, makePurchaseData *data.MakePurchaseData, parentPurchaseData *models.Purchase) {

	var membersData []*pb.MemberData
	for _, user := range makePurchaseData.CalculatedCart.FinalCartUsers {
		psd := time.Unix(user.PlanStartDate, 0)
		planSD, _ := ptypes.TimestampProto(psd)

		memberData := &pb.MemberData{
			UserId:              user.UserId,
			PlanStartDate:       planSD,
			PreferredFacilityId: user.PreferredFacilityId,
			ProductId:           user.ProductId,
		}

		if len(user.FacilitySlots) > 0 {
			var facilitySlots []*pb.FacilitySlot
			for _, facilitySlot := range user.FacilitySlots {
				facilitySlotPb := &pb.FacilitySlot{
					AcademySlotId:    facilitySlot.AcademySlotId,
					SummercampSlotId: facilitySlot.SummercampSlotId,
				}
				facilitySlots = append(facilitySlots, facilitySlotPb)
			}
			memberData.FacilitySlots = facilitySlots
		}

		membersData = append(membersData, memberData)
	}
	purchaseDate, err := ptypes.TimestampProto(parentPurchaseData.PurchaseDate)
	if err != nil {
		log.Println("Error in converting date in handler: sendPurchaseMailToFounders, Error: %v", err)
	}

	receiptData := &pb.ReceiptGenerationRequest{
		UserId:            parentPurchaseData.UserId,
		ProductId:         makePurchaseData.CalculatedCart.SubscriptionProductId,
		Amount:            int32(makePurchaseData.CalculatedCart.TotalCartValue),
		PurchaseType:      1,
		Purpose:           parentPurchaseData.Purpose,
		PurchaseDate:      purchaseDate,
		MembersData:       membersData,
		PaymentMethodType: makePurchaseData.PaymentMethodType,
	}
	s.SendPurchaseMail(ctx, receiptData)
}

func (s *Service) UpdatePurchasePaymentStatus(ctx context.Context, purchaseID int32, paymentStatus int32, purchaseToken string, amountCollected float32, metaData string) error {
	updatedPurchaseData := models.Purchase{
		PaymentStatus: paymentStatus,
	}
	if len(purchaseToken) > 0 {
		updatedPurchaseData.PurchaseToken = purchaseToken
	}
	if amountCollected > 0 {
		updatedPurchaseData.AmountCollected = amountCollected
	}
	if len(metaData) > 0 {
		updatedPurchaseData.MetaData = metaData
	}
	if err := s.Data.UpdatePurchase(purchaseID, updatedPurchaseData); err != nil {
		return err
	}
	purchaseModificationData := &models.PurchaseModificationLogs{
		PaymentStatus: paymentStatus,
		PurchaseID:    purchaseID,
		MetaData:      metaData,
	}
	if err := s.Data.CreatePurchaseModificationLogs(ctx, purchaseModificationData); err != nil {
		return err
	}
	return nil
}

func (s *Service) GetPurchasePaymentStatus(ctx context.Context, req *pb.PurchaseStatusRequest, res *pb.PurchaseStatusResponse) error {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	purchaseID := req.PurchaseId

	if purchaseID == 0 {
		log.Printf("can not get status, invalid purchase ID %d", purchaseID)
		return nil
	}

	if req.TotalPollTime < pollTimeThreshold {
		purchase := &models.Purchase{
			PurchaseId: purchaseID,
		}

		var purchaseData []models.Purchase
		err := s.Data.PurchaseGet(purchase, &purchaseData)
		if err != nil {
			log.Printf("error in getting purchase payment data: %v for purchaseID %d", err, purchaseID)
			return err
		}

		if len(purchaseData) == 0 {
			log.Printf("[payment status] purchase with purchaseID %d not found", purchaseID)
			return fmt.Errorf("[payment status] purchase with purchaseID %d not found", purchaseID)
		}

		subPurchaseData := purchaseData[0]
		if subPurchaseData.UserId != loggedInUserId {
			res.Status = &pb.Status{
				Status:  failed,
				Message: "You are not authorized to view this data!",
			}
			return nil
		}

		res.PaymentStatus = subPurchaseData.PaymentStatus

		productClient := util.GetProductServiceClient()
		productDetails, err := productClient.ProductGetV2(ctx, &productPB.ProductRequest{PId: subPurchaseData.ProductId})
		if err != nil {
			return fmt.Errorf("[payment status] error in getting product details for product_id: %d, err: %v", subPurchaseData.ProductId, err)
		}
		if len(productDetails.Products) > 0 {
			res.ProductCategoryId = productDetails.Products[0].ProductCategoryId
		}
		switch res.PaymentStatus {
		case completePaymentStatus:
			res.SuccessDeeplink = GetMembershipDetailsDeeplink(subPurchaseData.ProductId, loggedInUserId)

			linkedPurchases, err := s.Data.GetLinkedPurchases(ctx, purchaseID)
			if err != nil {
				log.Printf("[payment status] GetLinkedPurchases %d error: %v ", purchaseID, err)
				return err
			}

			if subPurchaseData.ActualAmount == 0 {
				if len(linkedPurchases) > 1 {
					res.SuccessDeeplink = GetMyMembershipsDeeplink(productDetails.Products[0].ProductCategoryId)
				}
				if len(linkedPurchases) == 1 {
					res.SuccessDeeplink = GetMembershipDetailsDeeplink(linkedPurchases[0].ProductId, linkedPurchases[0].UserId)
				}
			}

			res.Title = fmt.Sprintf(
				"Payment of %s processed successfully",
				strings.TrimRight(strings.TrimRight(fmt.Sprintf("₹%.2f", subPurchaseData.Amount), "0"), "."),
			)
			res.ProductId = subPurchaseData.ProductId
			res.ImageAnimation = "https://fitso-images.curefit.co/file_assets/success_coupon_lotie.json"

			successData, err := getSuccessData(ctx, linkedPurchases, subPurchaseData, purchaseID)
			if err != nil {
				log.Printf("[payment status] getSuccessData %d error: %v  ", purchaseID, err)
				return err
			}
			res.SuccessData = successData

		case failedPaymentStatus:
			res.Title = "Payment failed"
			res.Message = "If money was deducted it will be auto-refunded within 4 - 7 days."
			res.ImageAnimation = "https://fitso-images.curefit.co/file_assets/success_coupon_lotie.json"
		}
	} else {
		res.PaymentStatus = pendingPaymentStatus
		res.Title = "Payment processing"
		res.Subtitle = "Processing your payment is taking longer than usual"
		res.Message = "It may take upto 24-48 hrs to complete your transaction. Please check with your bank for updates. <semibold-300|Meanwhile, please do not attempt to pay again.>\n\nIn case, if the transaction is not successful and money is debited from your account it will be refunded within 3 working days"

		// updating payment status to 'pending' for this purchase
		err := s.UpdatePurchasePaymentStatus(ctx, purchaseID, pendingPaymentStatus, "", 0.0, "")
		if err != nil {
			log.Printf("error in updating payment status %d for purchase %d with error: %v", pendingPaymentStatus, purchaseID, err)
		}
	}

	res.PollInterval = statusPollInterval
	res.TotalPollTime = req.TotalPollTime + statusPollInterval

	res.Status = &pb.Status{
		Status:  success,
		Message: "Payment status details fetched successfully",
	}
	return nil
}

func getSuccessData(ctx context.Context, linkedPurchases []*models.Purchase, purchase models.Purchase, purchaseID int32) (*pb.PurchaseSuccessResponse, error) {

	successData := &pb.PurchaseSuccessResponse{
		Logo:             GetCDNLink("uploads/FitsoLogo1617286717.png"),
		HeaderBackground: GetCDNLink("uploads/MaskGroup(1)**********.png"),
	}

	productID := purchase.ProductId
	userID := purchase.UserId
	if productID == 0 || userID == 0 {
		log.Printf("[func]: getSuccessData, Empty productID & userID")
		return nil, nil
	}

	pcl := GetProductServiceClient()
	productRequest := &productPB.ProductRequest{
		PId: productID,
	}
	productData, err := pcl.ProductGet(ctx, productRequest)
	if err != nil {
		log.Printf("[func]: getSuccessData, error in getting product with productID %d, ERROR: %v", productID, err)
		return nil, err
	}

	if productData != nil && len(productData.Products) > 0 {
		cityID := productData.Products[0].LocationIdV2
		ProductcityRequest := &productPB.GetFitsoCityDataFromCityIdRequest{
			FitsoCityId: cityID,
		}
		cityData, err := pcl.GetFitsoCityDataFromCityId(ctx, ProductcityRequest)
		if err != nil {
			log.Printf("[func]: getSuccessData, error in handler GetFitsoCityDataFromCityId , Error: %v", err)
			return nil, err
		}
		successData.ProductCategoryId = productData.Products[0].ProductCategoryId
		if successData.ProductCategoryId == SUMMERCAMP_PRODUCT_CATEGORY_ID {
			successData.Logo = GetCDNLink("uploads/Frame2177321709193809.png")
		} else {
			successData.Logo = GetCDNLink("uploads/Frame2177321707386768.png")
		}
		successData.City = cityData.CityName
		successData.ProductDescription = productData.Products[0].ProductDescription
	}

	memberData, err := getMemberData(ctx, linkedPurchases, userID, purchaseID, successData.ProductCategoryId)
	if err != nil {
		log.Printf("[func]: getSuccessData, error getMemberData %d error: %v ", purchaseID, err)
		return nil, err
	}
	successData.MemberData = memberData
	return successData, nil
}

func getMemberData(ctx context.Context, linkedPurchases []*models.Purchase, userID int32, purchaseID int32, productCategoryId int32) (memberData []*pb.MemberData, err error) {
	userIds := []int32{userID}
	purchaseIds := []int32{purchaseID}
	for _, lp := range linkedPurchases {
		userIds = append(userIds, lp.UserId)
		purchaseIds = append(purchaseIds, lp.PurchaseId)
	}

	userDetailsMap, err := getUserDetails(ctx, userIds)
	if err != nil {
		log.Printf("[func]: getMemberData, error getUserDetails %d error: %v ", purchaseID, err)
		return nil, err
	}

	pcl := GetProductServiceClient()
	purchaseSubscriptions, err := pcl.BatchGetSubscriptionsByPurchaseID(
		ctx, &productPB.BatchGetSubscriptionsByPurchaseIDRequest{
			PurchaseId: purchaseIds,
		})
	if err != nil {
		log.Printf("func]: getMemberData, error in BatchGetSubscriptionsByPurchaseID: %d", err)
		return nil, err
	}

	fcl := util.GetFacilitySportServiceClient()
	for _, sub := range purchaseSubscriptions.Subscriptions {
		user, ok := userDetailsMap[sub.UserId]
		if !ok {
			log.Printf("func]: getMemberData, error in fetching userDetailsMap: %d", sub.UserId)
			return nil, err
		}
		var facilitiesNames []string
		var courseName, courseCategoryName, bgColor, sportName string
		if productCategoryId == ACADEMY_PRODUCT_CATEGORY_ID && len(purchaseSubscriptions.Subscriptions) == 1 {
			subscriptionFacilities, err := pcl.GetSubscriptionSlotMappings(ctx, &productPB.SubscriptionSlotMappingsRequest{
				SubscriptionId: sub.SubscriptionId,
			})
			if err != nil {
				log.Printf("error in getting academy slot data for id %d, %v", sub.SubscriptionId, err)
				return memberData, err
			}
			if subscriptionFacilities.SubscriptionSlotMappings == nil {
				continue
			}
			for _, data := range subscriptionFacilities.SubscriptionSlotMappings {
				facilitiesNames = append(facilitiesNames, data.FacilityDetails.ShortName)
			}
			courseCategoryDetails, err := fcl.GetCourseCategory(ctx, &facilitySportPB.GetAcademyCourseCategoryPageRequest{
				CourseCategoryId: subscriptionFacilities.SubscriptionSlotMappings[0].CourseCategoryId,
			})
			if err != nil {
				log.Printf("Error in getting course category data for Id %d, err %v", subscriptionFacilities.SubscriptionSlotMappings[0].CourseCategoryId, err)
				return memberData, err
			}
			courseCategoryName = courseCategoryDetails.Name
			bgColor = courseCategoryDetails.BgColor
			courseDetails, err := fcl.GetCourse(ctx, &facilitySportPB.GetAcademyCoursePageRequest{
				CourseId: courseCategoryDetails.CourseId,
			})
			if err != nil {
				log.Printf("Error in getting course data for Id %d, err %v", courseCategoryDetails.CourseId, err)
				return memberData, err
			}
			courseName = courseDetails.Title
		} else if productCategoryId == SUMMERCAMP_PRODUCT_CATEGORY_ID && len(purchaseSubscriptions.Subscriptions) == 1 {
			subscriptionFacilities, err := pcl.GetSubscriptionSummercampSlotMappings(ctx, &productPB.SubscriptionSlotMappingsRequest{
				SubscriptionId: sub.SubscriptionId,
			})
			if err != nil {
				log.Printf("error in getting summercamp slot data for id %d, %v", sub.SubscriptionId, err)
				return memberData, err
			}
			if subscriptionFacilities.SubscriptionSlotMappings == nil {
				continue
			}
			for _, data := range subscriptionFacilities.SubscriptionSlotMappings {
				sportName = data.FacilitySlot.SportName
				facilitiesNames = append(facilitiesNames, data.FacilityDetails.ShortName)
			}
		}
		member := &pb.MemberData{
			PlanStartDate:         sub.StartDate,
			PlanEndDate:           sub.SubscriptionEndDate,
			UserName:              user.Name,
			UserId:                sub.UserId,
			FacilityNames:         facilitiesNames,
			CourseName:            courseName,
			CourseCategoryName:    courseCategoryName,
			CourseCategoryBgColor: bgColor,
			SportName:             sportName,
		}
		memberData = append(memberData, member)
	}
	return memberData, nil
}

func getUserDetails(ctx context.Context, userIds []int32) (map[int32]*pb.User, error) {
	userDetailsRequest := &userPB.UserRequest{
		UserIdArray: userIds,
	}
	userClient := GetUserServiceClient()
	userDetailsResponse, err := userClient.UserGet(ctx, userDetailsRequest)
	if err != nil {
		log.Printf("Unable to fetch user details error %v", err)
		return nil, err
	}

	if userDetailsResponse == nil || len(userDetailsResponse.Users) == 0 {
		return nil, nil
	}

	userDetails := make(map[int32]*pb.User, 0)
	for _, val := range userDetailsResponse.Users {
		userObj := &pb.User{
			Name: val.Name,
		}
		userDetails[val.UserId] = userObj
	}
	return userDetails, nil
}

func (s *Service) createParentChildMap(ctx context.Context, makePurchaseData *data.MakePurchaseData, parentPurchaseData *models.Purchase) error {
	ucl := GetUserServiceClient()

	userRequest := &userPB.UserRequest{
		UserId: parentPurchaseData.UserId,
	}

	usersData, err := ucl.UserGet(ctx, userRequest)

	if err != nil {
		return err
	}

	if len(usersData.Users) == 0 {
		return err
	}

	purchaseByUserData := usersData.Users[0]
	for index, childUser := range makePurchaseData.CalculatedCart.FinalCartUsers {

		if childUser.IsParent {
			if childUser.Age > 0 {
				go CreateUserAgeRecordIfNotExists(ctx, childUser.UserId, childUser.Age)
			}
			continue
		}
		childUserID := childUser.UserId

		if len(childUser.Phone) > 0 && childUser.Phone != purchaseByUserData.Phone {
			userRequest := &userPB.UserRequest{
				Phone: childUser.Phone,
			}
			data, err := ucl.UserGet(ctx, userRequest)

			if err != nil {
				log.Printf("error in retrieving data for phone %s", childUser.Phone)
				continue
			}

			if len(data.Users) > 0 && data.Users[0].UserId > 0 {
				if childUserID <= 0 { //new child User but phone exist
					childUserID = data.Users[0].UserId

				} else {
					if data.Users[0].UserId != childUserID { //old child adding phone of other user
						log.Printf("Old Child:%d adding a phone of existing user:%d", childUserID, data.Users[0].UserId)
					}
				}
			} else if childUserID > 0 { //old child check phone new to stores
				userRequest := &userPB.UserRequest{
					UserId: childUserID,
				}
				data, err := ucl.UserGet(ctx, userRequest)
				if err != nil {
					log.Printf("error in retrieving data for user ID: %d", childUserID)
				}

				if len(data.Users) > 0 && len(data.Users[0].Phone) == 0 {
					userRequest := &userPB.UpdateUserDetailsRequest{
						UserId: childUserID,
						Phone:  childUser.Phone,
					}
					data, err := ucl.UpdateUserDetails(ctx, userRequest)
					if err != nil || data.Status.Status != success {
						log.Printf("Error in updating phone:%s for user ID: %d", childUserID, childUser.Phone)
					}
				} else if data.Users[0].Phone != childUser.Phone {
					log.Printf("Old Child:%d trying to change phone:%s to user phone:%s", childUserID, data.Users[0].Phone, childUser.Phone)
				}
			}
		}

		if childUserID <= 0 {
			userData := &userPB.User{
				Name: childUser.Name,
			}

			if childUser.Phone != purchaseByUserData.Phone {
				userData.Phone = childUser.Phone
				userData.PhoneVerifiedFlag = 0
			}

			createUserResponse, err := ucl.UserCreate(ctx, userData)

			if err != nil {
				log.Printf("error creating user for phone %s", childUser.Phone)
				continue
			}

			childUserID = createUserResponse.Users[0].UserId

		}

		if childUserID > 0 {
			userChildMapReq := &userPB.UserChildMappingRequest{
				UserId:      purchaseByUserData.UserId,
				ChildUserId: childUserID,
			}

			userChildMapData, err := ucl.UserChildMappingGetOrCreate(ctx, userChildMapReq)

			if err != nil || userChildMapData.Status.Status != success {
				log.Printf("error in getting child mapping for user id %d and child user id %d, err: %v", purchaseByUserData.UserId, childUserID, err)
				continue
			}
			go CreateUserAgeRecordIfNotExists(ctx, childUserID, childUser.Age)

			makePurchaseData.CalculatedCart.FinalCartUsers[index].UserId = userChildMapData.ChildUserId
		}
	}

	return nil
}

func (s *Service) createChildPurchase(ctx context.Context, makePurchaseData *data.MakePurchaseData, parentPurchaseData *models.Purchase) {
	for _, user := range makePurchaseData.CalculatedCart.FinalCartUsers {
		if user.UserId != parentPurchaseData.UserId {
			purchaseModel := &models.Purchase{
				UserId:           user.UserId,
				ProductId:        user.ProductId,
				Provider:         parentPurchaseData.Provider,
				PaymentStatus:    progressPaymentStatus,
				Amount:           0,
				ActualAmount:     user.Amount,
				EffectiveRevenue: user.Amount,
				PurchaseDate:     time.Now(),
				PurchaseType:     1,
				OfflinePayment:   0,
				DiscountAmount:   user.DiscountAmount,
				LinkedPurchaseId: parentPurchaseData.PurchaseId,
				Purpose:          parentPurchaseData.Purpose,
			}

			err, pID := s.PurchaseCreate(ctx, purchaseModel)

			if err != nil {
				log.Printf("error in creating purchase: %v for userID %d", err, user.UserId)
				continue
			}

			makePurchaseData.UserPurchaseMap[user.UserId] = pID
		}
	}
}

func CreateUserAgeRecordIfNotExists(ctx context.Context, userId int32, age int32) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("panic in creating user age record userId %d, err: %v", userId, r)
		}
	}()
	ucl := GetUserServiceClient()
	existingAgeRecord, err := ucl.GetUserAgeRecord(ctx, &userPB.GetUserAgeRecordReq{
		UserId: userId,
	})
	if err != nil {
		log.Printf("Error in handler CreateUserAgeRecordIfNotExists while getting user age record for userId: %d, Error: %v", userId, err)
	} else {
		if existingAgeRecord.Age != age && age > 0 {
			_, err := ucl.CreateUserAgeRecord(ctx, &userPB.CreateUserAgeRecordReq{
				UserId: userId,
				Age:    age,
			})
			if err != nil {
				log.Printf("Error in handler CreateUserAgeRecordIfNotExists while saving user age with userId: %d, Error: %v", userId, err)
			}
		}
	}
}

func (s *Service) MarkPurchaseStatusAsFailed(ctx context.Context, req *pb.Empty, res *pb.Ack) error {

	purchase := &models.Purchase{
		PaymentStatus: pendingPaymentStatus,
	}
	var purchaseData []models.Purchase
	var entriesMarkedFailed int32

	err := s.Data.GetPurchasesForLastTwoDays(ctx, purchase, &purchaseData)
	if err != nil {
		log.Printf("Error in getting purchase payment data: %v for Payment Status pending", err)
		return err
	}

	currentTime := time.Now()

	for _, data := range purchaseData {
		if currentTime.Unix() < data.CreatedAt.Add(24*time.Hour).Unix() {
			continue
		}
		if currentTime.Unix() > data.CreatedAt.Add(48*time.Hour).Unix() {
			continue
		}

		err = s.UpdatePurchasePaymentStatus(ctx, data.PurchaseId, failedPaymentStatus, "", 0.0, "")
		if err != nil {
			log.Printf("Error in updating payment status failed: %d for purchase id :%d with error: %v", failedPaymentStatus, data.PurchaseId, err)
			toEmails := []string{TECH_MAIL}
			s.SendEmail(toEmails, emailSubForMakePurchaseError, fmt.Sprintf("Error in updating payment status failed: %d for purchase id :%d with error: %v", failedPaymentStatus, data.PurchaseId, err))
			continue
		}
		entriesMarkedFailed += 1
	}
	if entriesMarkedFailed > 0 {
		res.Status = &pb.Status{
			Status:  success,
			Message: fmt.Sprintf("Pending purchases :%d, marked as failed successfully by cron at time : %s", entriesMarkedFailed, currentTime.Format("2 Jan, 2006 3:4:5 pm")),
		}
	} else {
		res.Status = &pb.Status{
			Status:  success,
			Message: fmt.Sprintf("No pending purchases found by cron at time : %s", currentTime.Format("2 Jan, 2006 3:04:05 pm")),
		}
	}

	return nil
}

func deleteUserCategoryCache(ctx context.Context, userId int32, productId int32) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from MakePurchase deleteUserCategoryCache panic- ", r)
		}
	}()
	productClient := util.GetProductServiceClient()
	productReq := &productPB.Product{
		ProductId: productId,
	}
	productDetails, err := productClient.GetProductDetails(ctx, productReq)
	if err != nil {
		log.Printf("MakePurchase deleteUserCategoryCache: Error in getting product details for product id: %d, error: %v", productId, err)
	} else {
		userClient := util.GetUserServiceClient()
		reqData := &userPB.UserCategoryCacheRequest{
			UserId:            userId,
			CityId:            productDetails.LocationIdV2,
			ProductCategoryId: MASTERKEY_PRODUCT_CATEGORY_ID,
		}
		res, err := userClient.DeleteCacheForUserCategory(ctx, reqData)
		if err != nil {
			log.Printf("MakePurchase deleteUserCategoryCache: Error in deleting user_suggestion_category cache for user id: %d, cityId: %d, error: %v", userId, productDetails.LocationIdV2, err)
		}
		log.Println("Successfully deleted user_suggestion_category cache: ", res)
	}
}

func publishPurchaseDataToLeadSquared(ctx context.Context, cartData *productPB.CalculateCartResponse, parentPurchaseData *models.Purchase) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from MakeSubscriptionForPurchase publishPurchaseDataToLeadSquared panic- ", r)
		}
	}()
	productClient := util.GetProductServiceClient()
	ucl := GetUserServiceClient()
	var countOfChildWithOutPhone = int32(0)
	parentLead := &productPB.LeadSquaredLead{
		Remark: PLAN_PURCHASED,
	}
	parentPurchaseLead := &productPB.PurchaseDetails{}
	for index, cartUser := range cartData.FinalCartUsers {
		var isSet bool
		productReq := &productPB.Product{
			ProductId: cartUser.ProductId,
		}
		product, err := productClient.GetProductDetails(ctx, productReq)
		if err != nil {
			log.Printf("publishPurchaseDataToLeadSquared: Error in getting product details for product id: %d, err: %v", cartUser.ProductId, err)
			return
		}
		leadsData := &productPB.LeadSquaredLead{
			Remark: PLAN_PURCHASED,
		}
		purchaseData := &productPB.PurchaseDetails{
			Age:          cartUser.Age,
			PlanDuration: fmt.Sprint(product.Duration) + " " + string(product.DurationUnit),
			ProductName:  product.ProductDescription,
		}
		planStartDateLocal, err := GetLocalDateTime(time.Unix(cartUser.PlanStartDate, 0))
		if err != nil {
			log.Printf("publishPurchaseDataToLeadSquared: Error in getting local date to publish purchase data to leadsquared for user_id: %d with error: %v", cartUser.UserId, err)
			return
		}
		planStartDate, err := ptypes.TimestampProto(Bod(planStartDateLocal))
		if err != nil {
			log.Printf("GetUserSubscriptionDateRange: Error in converting planStartDateLocal %s to google.protobuf.Timestamp, error: %v", planStartDateLocal.String(), err)
			return
		}
		planEndDateLocal := planStartDateLocal.AddDate(0, 0, int(product.DurationInDays))
		planEndDate, err := ptypes.TimestampProto(Eod(planEndDateLocal))
		if err != nil {
			log.Printf("GetUserSubscriptionDateRange: Error in converting planEndDateLocal %s to google.protobuf.Timestamp, error: %v", planEndDateLocal.String(), err)
			return
		}
		purchaseData.PlanStartDate = planStartDate
		purchaseData.PlanEndDate = planEndDate
		if len(cartUser.Phone) > 0 {
			if cartUser.UserId == parentPurchaseData.UserId {
				parentLead.FirstName = cartUser.Name
				parentLead.Mobile = cartUser.Phone
				purchaseData.SubscriptionCount += 1
				purchaseData.AmountPaid += cartData.AmountPerSubscription
				purchaseData.SubscriptionFor = PURCHASE_SELF
				parentPurchaseLead = purchaseData
				parentLead.PurchaseDetails = parentPurchaseLead
			} else {
				leadsData.FirstName = cartUser.Name
				leadsData.Mobile = cartUser.Phone
				purchaseData.SubscriptionCount = int32(1)
				purchaseData.SubscriptionFor = PURCHASE_CHILD
				purchaseData.AmountPaid = cartData.AmountPerSubscription
				isSet = true
			}
		} else {
			countOfChildWithOutPhone += 1
		}
		if isSet {
			leadsData.PurchaseDetails = purchaseData
			// publishing purchase data to leadsquared topic
			response, err := productClient.PublishLeadSquaredCaptureMessage(ctx, leadsData)
			if err != nil {
				log.Printf("publishPurchaseDataToLeadSquared: Error in publishing purchase data to leadsquared for user id: %d,with res %v and err: %v", cartUser.UserId, response, err)
				return
			}
		}
		if index == len(cartData.FinalCartUsers)-1 {
			if len(parentLead.FirstName) == 0 {
				userRequest := &userPB.UserRequest{
					UserId: parentPurchaseData.UserId,
				}
				usersData, err := ucl.UserGet(ctx, userRequest)
				if err != nil {
					log.Printf("publishPurchaseDataToLeadSquared: Error in getting user details for user id %d, err: %v", parentPurchaseData.UserId, err)
					return
				}
				if len(usersData.Users) > 0 {
					userDetails := usersData.Users[0]
					parentLead.FirstName = userDetails.Name
					parentLead.Mobile = userDetails.Phone
				}
			}
			if countOfChildWithOutPhone > 0 {
				parentPurchaseLead = purchaseData
				parentPurchaseLead.SubscriptionCount += countOfChildWithOutPhone
				parentPurchaseLead.AmountPaid = float32(parentPurchaseLead.SubscriptionCount) * cartData.AmountPerSubscription
				parentLead.PurchaseDetails = parentPurchaseLead
				if parentLead.PurchaseDetails.SubscriptionFor == PURCHASE_SELF {
					parentLead.PurchaseDetails.SubscriptionFor = PURCHASE_BOTH
				} else {
					parentLead.PurchaseDetails.SubscriptionFor = PURCHASE_CHILD
				}
			}

			response, err := productClient.PublishLeadSquaredCaptureMessage(ctx, parentLead)
			if err != nil {
				log.Printf("publishPurchaseDataToLeadSquared: Error in publishing parent lead data for purchase to leadsquared for user id: %d,with res %v and err: %v", parentPurchaseData.UserId, response, err)
				return
			}
		}
	}
}

func (s *Service) GetSupportedPaymentProvider(ctx context.Context, req *pb.Empty, res *pb.SupportedPaymentProviderResponse) error {

	if featuresupport.SupportsRazorpayProvider(ctx) || featuresupport.IsWebRequest(ctx) {
		res.PaymentProvider = razorpayPaymentProvider
	} else {
		res.PaymentProvider = zomatoPaymentProvider
	}

	return nil
}

func (s *Service) GetPreferredSportFromCachedPurchaseData(ctx context.Context, req *pb.GetPreferredSportFromCachedPurchaseDataReq, res *pb.GetPreferredSportFromCachedPurchaseDataRes) error {
	makePurchaseData, err := s.Data.GetCachedMakePurchaseData(ctx, req.PurchaseId)
	if err != nil {
		log.Printf("error in retrieving cached purchase data for purchaseID %d: %v", req.PurchaseId, err)
		return err
	}
	if !makePurchaseData.IsAcademy && !makePurchaseData.IsSummerCamp {
		for _, cartUser := range makePurchaseData.CalculatedCart.FinalCartUsers {
			res.SportId = cartUser.PreferredSportId
			return nil
		}
	} else if makePurchaseData.IsSummerCamp {
		for _, productDetail := range makePurchaseData.CalculatedCart.ProductDetails {
			res.SportName = productDetail.ProductName
			return nil
		}
	} else {
		for _, productDetail := range makePurchaseData.CalculatedCart.ProductDetails {
			res.SportName = productDetail.ProductName
			return nil
		}
	}
	return nil
}
