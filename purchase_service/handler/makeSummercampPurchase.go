package purchaseHandler

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"bitbucket.org/jogocoin/go_api/purchase_service/data"
	models "bitbucket.org/jogocoin/go_api/purchase_service/data/model"
	"bitbucket.org/jogocoin/go_api/purchase_service/internal/util"
	productPB "bitbucket.org/jogocoin/go_api/purchase_service/proto/product"
	pb "bitbucket.org/jogocoin/go_api/purchase_service/proto/purchase"
	structs "bitbucket.org/jogocoin/go_api/purchase_service/structs"
	"github.com/micro/go-micro/config"
	"golang.org/x/net/context"
	"bitbucket.org/jogocoin/go_api/purchase_service/internal/featuresupport"
)

func (s *Service) MakeSummercampPurchase(ctx context.Context, req *pb.PurchaseInitiateRequest, res *pb.MakePurchaseResponse) error {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		res.Status = &pb.Status{
			Status:  notAuthorized,
			Message: "You are not authorized to make this request!",
		}
		return nil
	}
	productClient := util.GetProductServiceClient()

	var parentUser *productPB.CartUser
	if req.ParentUser != nil && loggedInUserId == req.ParentUser.UserId {
		user := req.ParentUser
		parentUser = &productPB.CartUser{
			UserId:        user.UserId,
			Name:          user.Name,
			PlanStartDate: user.PlanStartDate,
			ProductId:     user.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range user.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			parentUser.FacilitySlots = facilitySlots
		}
	}

	var childCartUsers []*productPB.CartUser
	for _, cartUser := range req.ChildUsers {
		childCartUser := &productPB.CartUser{
			UserId:        cartUser.UserId,
			Name:          cartUser.Name,
			PlanStartDate: cartUser.PlanStartDate,
			ProductId:     cartUser.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range cartUser.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			childCartUser.FacilitySlots = facilitySlots
		}
		childCartUsers = append(childCartUsers, childCartUser)
	}

	calculatedCart, err := productClient.CalculateSummercampCart(ctx, &productPB.CalculateCartRequest{
		ChildUsers: childCartUsers,
		ParentUser: parentUser,
		PromoCode:  req.PromoCode,
	})
	if err != nil {
		log.Printf("Error in validating cart users for user %d, error: %v", loggedInUserId, err)
		return err
	}
	if calculatedCart.Status != nil {
		res.Status = &pb.Status{
			Status:  calculatedCart.Status.Status,
			Message: calculatedCart.Status.Message,
		}
	}
	if calculatedCart.TotalCartValue < 0 {
		log.Printf("Total cart value cannot be less than zero, cart is: %v", calculatedCart)
		return nil
	}
	if len(calculatedCart.FinalCartUsers) == 0 {
		log.Printf("Length of cart users cannot be zero, cart is: %v", calculatedCart)
		return nil
	}

	isLoggedInUserSubscribing := false
	actualAmount := float32(0)
	discountAmount := float32(0)
	var subscriptionProductId int32
	for _, user := range calculatedCart.FinalCartUsers {
		if loggedInUserId == user.UserId {
			isLoggedInUserSubscribing = true
			actualAmount = user.Amount
			discountAmount = user.DiscountAmount
			subscriptionProductId = user.ProductId
		}
	}
	if !isLoggedInUserSubscribing {
		subscriptionProductId = calculatedCart.FinalCartUsers[0].ProductId
	}

	var paymentProviderRes pb.SupportedPaymentProviderResponse
	if err := s.GetSupportedPaymentProvider(ctx, &pb.Empty{}, &paymentProviderRes); err != nil {
		log.Println("error in getting supported payment provider")
		return err
	}

	var provider int32 = paymentProviderRes.PaymentProvider

	res.PaymentProvider = provider

	purchaseModel := &models.Purchase{
		UserId:           loggedInUserId,
		ProductId:        subscriptionProductId,
		Provider:         provider,
		PaymentStatus:    progressPaymentStatus,
		Amount:           calculatedCart.TotalCartValue,
		PurchaseDate:     time.Now(),
		PurchaseType:     1,
		OfflinePayment:   0,
		LinkedPurchaseId: 0,
		VoucherId:        calculatedCart.VoucherId,
		Purpose:          util.GetClientIDFromContext(ctx),
		ReferralId:       calculatedCart.ReferralId,
	}

	if isLoggedInUserSubscribing {
		purchaseModel.ActualAmount = actualAmount
		purchaseModel.EffectiveRevenue = actualAmount
		purchaseModel.DiscountAmount = discountAmount
	}

	if provider == razorpayPaymentProvider {

		orderData := &structs.PaymentHashData{
			Amount: calculatedCart.TotalCartValue,
		}
		orderId, err := getOrderIdViaRazorpay(ctx, orderData)
		if orderId == "" || err != nil {
			log.Printf("purchase cannot be created for amount %d for user %d, err %v", orderData.Amount, loggedInUserId, err)
			return fmt.Errorf("Payment hash cannot be empty for purchaseID for user %d, err %v", loggedInUserId, err)
		}
		purchaseModel.PaymentRequestToken = orderId

		logoImage := "https://fitso-images.curefit.co/uploads/5121641371930.png"
		themeColor := "#EF4F5F"
		if featuresupport.SupportsNewColor(ctx) {
			logoImage = FITSO_BLACK_LOGO_IMAGE
			themeColor = "#FF3866"
		}

		out := map[string]interface{}{
			"name":        "cult.fit",
			"description": "Membership Charges",
			"image":       logoImage,
			"order_id":    orderId,
			"theme.color": themeColor,
			"currency":    "INR",
			"amount":      calculatedCart.TotalCartValue,
			"key_id":      config.Get("razorpay", "keyId").String(""),
		}

		payload, err := json.Marshal(out)
		if err != nil {
			log.Println("error in marshalling the payload data")
			return fmt.Errorf("error in marshalling razorpay_data")
		}
		res.RazorpayData = string(payload)
	}

	err, purchaseId := s.PurchaseCreate(ctx, purchaseModel)
	if err != nil {
		log.Printf("error in creating parent purchase: %v for userID %d", err, loggedInUserId)
		return err
	}

	userPurchaseMap := make(map[int32]int32)
	if isLoggedInUserSubscribing {
		userPurchaseMap[loggedInUserId] = purchaseId
	}

	res.PurchaseId = fmt.Sprintf("%d", purchaseId)

	err = s.CachePurchaseData(ctx, purchaseId, &data.MakePurchaseData{
		CalculatedCart:  calculatedCart,
		UserPurchaseMap: userPurchaseMap,
		IsSummerCamp:       true,
	})
	if err != nil {
		log.Printf("Error in caching purchase data for purchaseId: %d for userId %d", purchaseId, loggedInUserId)
		return err
	}

	if provider == zomatoPaymentProvider {

		paymentHash := GetHashFromPaymentService(ctx, &structs.PaymentHashData{
			PaymentMethodID:   req.PaymentMethodId,
			PaymentMethodType: req.PaymentMethodType,
			Amount:            calculatedCart.TotalCartValue,
			CountryID:         countryIDIndia,
			PurchaseID:        purchaseId,
		})
		if paymentHash == "" {
			log.Printf("Payment hash cannot be empty for purchaseId %d", purchaseId)
			return fmt.Errorf("Payment hash cannot be empty for purchaseId %d", purchaseId)
		}
		res.PaymentHash = paymentHash
	}

	if err = s.Data.CreatePurchasePaymentMapping(ctx, &models.PurchasePaymentMapping{
		PurchaseID:        purchaseId,
		PaymentMethodType: req.PaymentMethodType,
		PaymentMethodID:   req.PaymentMethodId,
	}); err != nil {
		log.Printf("Error in creating purchase payment mapping for purchaseId %d", purchaseId)
		return fmt.Errorf("Error in creating purchase payment mapping for purchaseId %d", purchaseId)
	}
	res.Status = &pb.Status{
		Status: success,
	}
	return nil
}
