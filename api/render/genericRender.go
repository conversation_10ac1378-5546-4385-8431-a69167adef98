package render

import (
	// "fmt"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"

	"github.com/gin-gonic/gin"
)

// Render one of HTML, JSON or CSV based on the 'Accept' header of the request
// If the header doesn't specify this, <PERSON>SO<PERSON> is returned
func Render(c *gin.Context, data gin.H, templateName string) {
	errorPublic := c.Errors.ByType(gin.ErrorTypePublic)
	errorPrivate := c.Errors.ByType(gin.ErrorTypePublic)

	if errorPublic != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": errorPublic,
		})
	} else if errorPrivate != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"status":  "error",
			"message": "something went wrong, we are working on it",
		})
	}

	switch c.Request.Header.Get("Accept") {
	case "application/xml":
		// Respond with XML
		c.XML(http.StatusOK, data["payload"])
	case "text/html":
		// Respond with HTML
		c.HTML(http.StatusOK, templateName, data)
	default:
		// Respond with JSON
		c.JSON(http.StatusOK, data["payload"])
	}
}

// AbortWithStatusJSON is for aborting the chain of middleware, not to stop the current handler from executing
func AbortWithStatusJSON(c *gin.Context, code int, status string, errMsg string) {
	c.AbortWithStatusJSON(code, gin.H{
		"status": common.Status{
			Status:  status,
			Message: errMsg,
		},
	})
}
