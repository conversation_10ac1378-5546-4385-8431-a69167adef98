setup:
	go mod init "bitbucket.org/jogocoin/go_api/api"

build:
	protoc --proto_path=$(GOPATH)/src/go_api/auth_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/auth/auth.proto

	protoc --proto_path=$(GOPATH)/src/go_api/facility_sport_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/facility_sport/facility_sport.proto

	protoc --proto_path=$(GOPATH)/src/go_api/product_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/product/product.proto

	protoc --proto_path=$(GOPATH)/src/go_api/user_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/user/user.proto

	protoc --proto_path=$(GOPATH)/src/go_api/booking_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/booking/booking.proto
	
	protoc --proto_path=$(GOPATH)/src/go_api/purchase_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/purchase/purchase.proto

	protoc --proto_path=$(GOPATH)/src/go_api/seo_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/seo/seo.proto
	
	protoc --proto_path=$(GOPATH)/src/go_api/notification_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/api \
		proto/notification/notification.proto
	echo "machine bitbucket.org login $(BITBUCKET_USER_NAME) password $(BITBUCKET_USER_PASS)" > $(GOPATH)/src/go_api/api/.netrc

local:
	make build
	MICRO_REGISTRY=mdns MICRO_SERVER_ADDRESS=:8080 MICRO_MODE=dev DEBUG=true GOPRIVATE="bitbucket.org" go run main.go

local2: 
	make build
	MICRO_REGISTRY=mdns MICRO_SERVER_ADDRESS=:8181 MICRO_MODE=dev go run main.go

hot-reload:
	reflex -r '.*\.go' -R '^vendor/' -s make local

aws_dev:
	make build
	MICRO_SERVER_ADDRESS=:8080 MICRO_MODE=qa go run main.go

aws_prod:
	make build
	MICRO_SERVER_ADDRESS=:8080 MICRO_MODE=prod go run main.go

docker_build:
	make build
	docker build -t go_api .

publish:
	docker tag go_api:latest 051603364801.dkr.ecr.ap-south-1.amazonaws.com/go_api:latest
	docker push 051603364801.dkr.ecr.ap-south-1.amazonaws.com/go_api:latest

rund:
	docker run --rm -it -d -p 80:8080 --name go_api -e MICRO_SERVER_ADDRESS=:8080 -e MICRO_MODE=qa go_api 
	