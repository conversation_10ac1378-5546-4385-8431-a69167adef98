package commonController

import (
	"strconv"
	facilitySportController "bitbucket.org/jogocoin/go_api/api/server/routes/facilitySport/controllers"
	facilityModel "bitbucket.org/jogocoin/go_api/api/models/facility"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func FacilityAcademyAndMasterkeyC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(facilityModel.GetFacilityRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryID, _ := strconv.Atoi(requestData.ProductCategoryId)
	if categoryID == common.AcademyCategoryID {
		facilitySportController.GetAcademyFacilityDetailsC(c)
	} else if categoryID == common.SummerCampCategoryID {
		facilitySportController.GetSummercampFacilityDetailsC(c)
	} else {
		facilitySportController.GetFacilityDetailsC(c)
	}
}

func GetCoursePageC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(structs.GetAcademyCoursePageRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryID, _ := strconv.Atoi(requestData.ProductCategoryId)
	if categoryID == common.SummerCampCategoryID && false {
		facilitySportController.GetSummercampCoursePageC(c)
	} else {
		facilitySportController.GetAcademyCoursePageC(c)
	}
}

func GetCourseTabsC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(structs.GetTabsRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryID, _ := strconv.Atoi(requestData.ProductCategoryId)
	if categoryID == common.SummerCampCategoryID {
		facilitySportController.GetSummercampTabsC(c)
	} else {
		facilitySportController.GetAcademyTabsC(c)
	}
}