// facility resource

package facilitySportResource

import (
	"fmt"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	facilityModel "bitbucket.org/jogocoin/go_api/api/models/facility"
	sportModel "bitbucket.org/jogocoin/go_api/api/models/sport"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	validator "gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func GetFacilityR(c *gin.Context) {
	validate = validator.New()

	var json structs.Facility

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetFacilityDetailsV2R(c *gin.Context) {
	validate = validator.New()

	var json facilityModel.GetFacilityRequest

	if err := c.Bind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

// GetSportDetailsR parses the request param for sport get
func GetSportDetailsR(c *gin.Context) {
	validate = validator.New()

	var json sportModel.GetSportRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("err - Bind : ", err)
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func MarkFavoriteFacilitiesR(c *gin.Context) {
	validate = validator.New()

	var json structs.FavoriteFacilityReq

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func CreateOrUpdateFacilityR(c *gin.Context) {
	validate = validator.New()

	var json structs.Facility

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func CreateUpdateFacilityRegionR(c *gin.Context) {

	validate = validator.New()

	var json structs.FacilityRegion

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()

}

func CreateOrUpdateFacilitySportMappingsR(c *gin.Context) {
	validate = validator.New()

	var json structs.FacilitySportMappingReq

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetPlayArenaSlotsR(c *gin.Context) {
	validate = validator.New()

	fmt.Println("query data: ", c.Query("facility_id"), c.Query("sport_id"))

	var json structs.PlayArenaFacilitySportSlot

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err ----> ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSportR(c *gin.Context) {
	validate = validator.New()

	var json structs.Sport

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetFacilitySportR(c *gin.Context) {
	validate = validator.New()

	fmt.Println("query data context: ", c.Query("facility_id"), c.Query("sport_id"))

	var json structs.FacilitySport

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println(json)
	c.Set("jsonData", json)

	c.Next()
}

func GetFacilitySportSlotR(c *gin.Context) {
	validate = validator.New()

	var json structs.FacilitySportSlot

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err ----> ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetPlayArenasR(c *gin.Context) {
	validate = validator.New()

	var json structs.PlayArena

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err ----> ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSlotR(c *gin.Context) {
	validate = validator.New()

	var json structs.Slot

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err ----> ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetProductArenaCategoryR(c *gin.Context) {
	validate = validator.New()

	var json structs.ProductArenaCategory

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("GetProductArenaCategoryR | Unable to bind", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("GetProductArenaCategoryR | unable to validate", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetSportInFacilityR(c *gin.Context) {
	validate = validator.New()

	var json structs.FacilitySport

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println(json)
	c.Set("jsonData", json)

	c.Next()
}

func GetFitsoFacilitiesR(c *gin.Context) {
	validate = validator.New()

	var json structs.FitsoFacilitiesReq

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println(json)
	c.Set("jsonData", json)

	c.Next()
}

func GetActiveCitiesRegionSportsR(c *gin.Context) {
	c.Next()
}

func GetFacilityRegionR(c *gin.Context) {
	c.Next()
}

func GetActiveCitiesR(c *gin.Context) {
	validate = validator.New()

	var json structs.ActiveCitiesReq

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println(json)
	c.Set("jsonData", json)
	c.Next()
}

func GetSocietiesR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetSocietiesReq

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println(json)
	c.Set("jsonData", json)

	c.Next()
}

func GetCoachingTypesR(c *gin.Context) {
	c.Next()
}

func StoreFacilityImageV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.FacilityImageV2

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
	}
	c.Set("jsonData", json)
	c.Next()

}

func StoreFacilitySportImagesR(c *gin.Context) {

	validate = validator.New()

	var json structs.FacilitySportImagesData

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetPreferredCentersR(c *gin.Context) {

	validate = validator.New()

	var json structs.PreferredCenters

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func UpsertFacilityAmenityMappingR(c *gin.Context) {

	validate = validator.New()

	var json structs.FacilityAmenitiesMapping

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetAmenitiesByFacilityIdR(c *gin.Context) {

	validate = validator.New()

	var json structs.FacilityAmenitiesMapping

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func UpsertAmenityR(c *gin.Context) {

	validate = validator.New()

	var json structs.Amenity

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func UpdateTagsR(c *gin.Context) {
	validate = validator.New()
	var json structs.UpdateTags

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}
func GetAcademyCoursePageR(c *gin.Context) {
	var json structs.GetAcademyCoursePageRequest
	validate = validator.New()

	if err := c.ShouldBindUri(&json); err != nil {
		log.Println("Binding failed in GetKASportsPageR, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Query binding failed in GetAcademyCourseCategoryPageR, error : ", err)
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("Validation failed in GetAcademyCourseCategoryPageR on the required field, error : ", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetAcademyTabsR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetTabsRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Binding failed in GetAcademyTabsR, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("Validation failed in GetAcademyTabsR on the required field, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetAcademyCourseCategoryPageR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetAcademyCourseCategoryPageRequest

	if err := c.ShouldBindUri(&json); err != nil {
		log.Println("Url binding failed in GetAcademyCourseCategoryPageR, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Query binding failed in GetAcademyCourseCategoryPageR, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("Validation failed in GetAcademyCourseCategoryPageR on the required field, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetTagMappingR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetTagMappingRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Binding failed in GetTagMappingR, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("Validation failed in GetTagMappingR on the required field, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetSportsToSelectR(c *gin.Context) {
	validate = validator.New()
	var json structs.SportSelect

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Binding failed, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("Validation failed, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetSubzoneSuggestionsR(c *gin.Context) {
	validate = validator.New()
	var json structs.SubzoneSuggestionsReq

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("GetSubzoneSuggestionsR, query params binding failed, error : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Printf("GetSubzoneSuggestionsR, Validation failed for json: %v, error : %v", json, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}
