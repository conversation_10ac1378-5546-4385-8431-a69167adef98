package facilitySportController

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"encoding/json"
	"sort"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	academyModels "bitbucket.org/jogocoin/go_api/api/models/facility"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type SummercampCoursePageTemplate struct {
	Response          *facilitySportPB.SummerCampFacilitiesRes `json:"-"`
	ProductDetails    *productPB.SummercampSportProductsRes    `json:"-"`
	Results           []*academyModels.AcademyCourseResult     `json:"results,omitempty"`
	Json              structs.GetAcademyCoursePageRequest      `json:"-"`
	Footer            *sushi.FooterSnippetType2Layout          `json:"footer,omitempty"`
	HasMore           bool                                     `json:"has_more,omitempty"`
	PostbackParams    string                                   `json:"postback_params,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                   `json:"clever_tap_tracking,omitempty"`
}

func GetSummercampCoursePageC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	jsonData := c.MustGet("jsonData").(structs.GetAcademyCoursePageRequest)
	facilityClient := util.GetFacilitySportClient()
	summercampCoursePage, err := facilityClient.GetSummercampSportPage(ctx, &facilitySportPB.SummerCampFacilitiesReq{
		SportId: jsonData.CourseId,
	})
	if err != nil {
		log.Printf("Error in GetSummercampCoursePageC for sportId: %d, error: %v", jsonData.CourseId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if summercampCoursePage.Status != nil {
		if summercampCoursePage.Status.Status == apiCommon.FAILED {
			log.Println("Failed in controller GetSummercampCoursePageC while getting details for sportId: %d", jsonData.CourseId)
			c.JSON(http.StatusOK, summercampCoursePage.Status)
			return
		}
		if summercampCoursePage.Status.Status == apiCommon.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
			return
		}
	}

	coursePageTemplate := &SummercampCoursePageTemplate{
		Response:       summercampCoursePage,
		Json:           jsonData,
		PostbackParams: "",
		HasMore:        false,
	}
	if summercampCoursePage.SportId > 0 {
		coursePageTemplate.SetProductBySport(ctx)
		coursePageTemplate.SetMedia(ctx)
		coursePageTemplate.SetCourseInfo(ctx)
		coursePageTemplate.SetHighlightsSection(ctx)
		coursePageTemplate.SetMeasuresAndBenefits(ctx)
		coursePageTemplate.SetProductsSportSection(ctx)
		coursePageTemplate.SetNearByCentersSection(ctx)
		coursePageTemplate.SetFooterSectionSummercamp(ctx)
		coursePageTemplate.SetPageTracking(ctx)
	}

	c.JSON(http.StatusOK, coursePageTemplate)
}

func (c *SummercampCoursePageTemplate) SetProductBySport(ctx context.Context) {
	productClient := util.GetProductClient()
	summercampProductResponse, err := productClient.GetFeaturedSummercampProducts(ctx, &productPB.Empty{})
	if err != nil {
		log.Printf("Error in SummercampCoursePageTemplate error: %v", err)
		return
	}
	c.ProductDetails = summercampProductResponse
}

func (c *SummercampCoursePageTemplate) SetProductsSportSection(ctx context.Context) {
	products := c.ProductDetails
	if products == nil {
		return
	}
	if products.SummercampProductsBySport[c.Json.CourseId] == nil {
		return
	}

	if len(products.SummercampProductsBySport[c.Json.CourseId].Products) == 0 {
		return
	}
	title, _ := sushi.NewTextSnippet(fmt.Sprintf("%s Plans", products.SummercampProductsBySport[c.Json.CourseId].Products[0].SportName))

	impressionEname := &sushi.EnameData{
		Ename: "summercamp_plans_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := c.GetClevertapTrackItem(ctx, nil, sectionEvents)

	c.Results = append(c.Results, &academyModels.AcademyCourseResult{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: sushi.SectionHeaderType1,
		},
		SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
			Title:             title,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		},
	})
	items := make([]*sushi.FitsoPurchaseSnippetType1SnippetItem, 0)
	sort.SliceStable(products.SummercampProductsBySport[c.Json.CourseId].Products, func(i, j int) bool {
		return len(products.SummercampProductsBySport[c.Json.CourseId].Products[i].DaysOfWeek) < len(products.SummercampProductsBySport[c.Json.CourseId].Products[j].DaysOfWeek)
	})
	sort.SliceStable(products.SummercampProductsBySport[c.Json.CourseId].Products, func(i, j int) bool {
		return products.SummercampProductsBySport[c.Json.CourseId].Products[i].Price > products.SummercampProductsBySport[c.Json.CourseId].Products[j].Price
	})
	for i, sportProduct := range products.SummercampProductsBySport[c.Json.CourseId].Products {
		item := &sushi.FitsoPurchaseSnippetType1SnippetItem{
			Id:           sportProduct.ProductId,
			CornerRadius: int32(12),
		}

		title, _ := sushi.NewTextSnippet(strconv.Itoa(int(sportProduct.Duration)))
		titleFont, _ := sushi.NewFont(sushi.FontBold, sushi.FontSize900)
		title.SetFont(titleFont)
		titleTextColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		title.SetColor(titleTextColor)
		titleGradient := sushi.Gradient{}
		color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
		color2, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint400)
		titleGradient.Colors = &[]sushi.Color{*color1, *color2}
		title.Gradient = &titleGradient
		item.Title = title

		durationText := sportProduct.DurationUnit
		if sportProduct.DurationUnit == "week" {
			durationText = "Weeks"
		}
		daysInt, daysStr := util.GetProductDaysOfWeekSummercamp(sportProduct.DaysOfWeek)
		titleText := fmt.Sprintf("\n{red-500|<regular-100|%s >}{grey-800|<medium-100|>}", daysStr)
		if len(daysInt) == 2 {
			titleText = fmt.Sprintf("\n{red-500|<regular-100|%s >}{grey-800|<medium-100|| Weekends>}", daysStr)
		} else if len(daysInt) == 0 || len(daysInt) == 1 || len(daysInt) > 5 {
			titleText = fmt.Sprintf("\n{red-500|<regular-100|%s >}{grey-800|<medium-100|}", daysStr)
		} else {
			titleText = fmt.Sprintf("\n{red-500|<regular-100|%s >}{grey-800|<medium-100|| Weekdays>}", daysStr)
		}
		if len(sportProduct.DaysOfWeek) > 0 {
			durationText = durationText + titleText
		}
		
		subtitle, _ := sushi.NewTextSnippet(durationText)
		subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		//subtitle.Gradient = &titleGradient
		subtitleTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle.SetColor(subtitleTextColor)
		subtitle.SetFont(subtitleFont)
		if len(sportProduct.DaysOfWeek) > 0 {
			subtitle.SetIsMarkdown(1)
			subtitle.SetAlignment(sushi.TextAlignmentLeft)
		}
		item.Subtitle = subtitle

		//price
		subtitle1, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(sportProduct.Price)))
		//color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle1.SetFont(font)
		//subtitle.SetColor(titleTextColor)

		perSessionPrice := int32(sportProduct.Price)
		daysIntCount := int32(len(daysInt))

		if sportProduct.Duration > 0 {
			if (len(sportProduct.DaysOfWeek) > 0 && daysIntCount > 0) {
				perSessionPrice = int32(sportProduct.Price) / (sportProduct.Duration * daysIntCount)
			} else {
				perSessionPrice = int32(sportProduct.Price) / sportProduct.Duration
			}
		}

		if sportProduct.Price > sportProduct.RetailPrice {
			if (len(sportProduct.DaysOfWeek) > 0 && daysIntCount > 0) {
				perSessionPrice = int32(sportProduct.RetailPrice) / (sportProduct.Duration * daysIntCount)
			} else {
				perSessionPrice = int32(sportProduct.RetailPrice) / sportProduct.Duration
			}
		}

		subtitle2Text := fmt.Sprintf("₹%s{black-500|<semibold-200|/session*>}\n", strconv.Itoa(int(perSessionPrice)))
		subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
		subtitle2Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		subtitle2.SetFont(subtitle2Font)
		subtitle2.SetIsMarkdown(1)
		subtitle2.SetAlignment(sushi.TextAlignmentRight)
		subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitle2.SetColor(subtitle2Color)

		if sportProduct.Price > sportProduct.RetailPrice {
			subtitle1.Strikethrough = true
			item.Subtitle2 = subtitle2
		}
		item.Subtitle1 = subtitle1

		percentDiscount := int((sportProduct.Price - sportProduct.RetailPrice) * 100 / sportProduct.Price)
		percentDiscountText := strconv.Itoa(int(percentDiscount)) + "% off"
		tagTitle, _ := sushi.NewTextSnippet(percentDiscountText)
		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		tagTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		tagTitle.SetFont(tagTitleFont)
		tagTitle.SetColor(tagColor)
		bgColorBlue, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)

		if percentDiscount > 0 {
			titleGradient := sushi.Gradient{}
			color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
			titleGradient.Colors = &[]sushi.Color{*color1, *color1}
			tagTitle.Gradient = &titleGradient
			item.Tag1 = &sushi.Tag{
				Title:   tagTitle,
				Type:    "rounded",
				BgColor: bgColorBlue,
			}
		}
		rightTitle, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(sportProduct.RetailPrice)))
		rightTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		rightTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
		rightTitle.SetFont(rightTitleFont)
		rightTitle.SetColor(rightTitleColor)
		item.RightTitle = rightTitle

		var subtitleList []*sushi.FitsoTextSnippetType7SnippetSubtitleItem
		subtitleListItemTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitleListItemTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		subtitleListItemIcon, _ := sushi.NewIcon(sushi.PointIcon, subtitleListItemTitleColor)

		subtitleListItems := getProductOfferings(ctx, sportProduct.ProductId)

		for _, subtitleItem := range subtitleListItems {
			trimmedSubtitle := strings.TrimSpace(subtitleItem)
			subtitleListItemTitle, _ := sushi.NewTextSnippet(trimmedSubtitle)
			subtitleListItemTitle.SetColor(subtitleListItemTitleColor)
			subtitleListItemTitle.SetFont(subtitleListItemTitleFont)

			subtitleListItem := &sushi.FitsoTextSnippetType7SnippetSubtitleItem{
				Title: subtitleListItemTitle,
				Icon:  subtitleListItemIcon,
			}

			subtitleList = append(subtitleList, subtitleListItem)
		}
		if len(subtitleList) > 0 {
			item.SubtitleList = subtitleList
		}
		bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint50)
		item.BgColor = bgColor
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		item.BorderColor = borderColor

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		item.BottomSeparator = separator

		if i == 0 {
			tagTitle, _ := sushi.NewTextSnippet("MOST POPULAR")
			tagTitleFont, _ := sushi.NewFont(sushi.FontBold, sushi.FontSize100)
			tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			tagTitle.SetFont(tagTitleFont)
			tagTitle.SetColor(tagTitleColor)
			tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			tagColor2, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
			tagGradient := sushi.Gradient{}
			tagGradient.Colors = &[]sushi.Color{*tagColor1, *tagColor2}
			item.Tag = &sushi.Tag{
				Title:    tagTitle,
				Gradient: &tagGradient,
			}
		}

		item.BottomContainer = getNewBottomContainer()

		//item.ClickAction = p.GetClickActionForBuy(ctx, product)
		membershipClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		urlDeeplink := util.GetPurchaseMembershipDeeplink(apiCommon.SummerCampCategoryID)

		membershipClickAction.SetDeeplink(&sushi.Deeplink{
			URL: urlDeeplink,
		})
		item.ClickAction = membershipClickAction
		trackPayload := make(map[string]interface{})
		trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
		trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
		trackPayload["product_id"] = sportProduct.ProductId

		tapEname := &sushi.EnameData{
			Ename: "course_summercamp_product_tap",
		}
		trackEvents := sushi.NewClevertapEvents()
		trackEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
		item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, item)
	}
	snippet := &sushi.FitsoPurchaseSnippetType2Snippet{
		Items: items,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoPurchaseSnippetType2,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	section := &academyModels.AcademyCourseResult{
		LayoutConfig:              layoutConfig,
		FitsoPurchaseSnippetType2: snippet,
	}
	c.Results = append(c.Results, section)
}

func getNewBottomContainer() *sushi.Tag {
	var titleText string = "Buy Now"
	title, _ := sushi.NewTextSnippet(titleText)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon("e936", color)
	title.Alignment = sushi.TextAlignmentCenter
	title.SetFont(font)
	title.SetColor(color)
	title.SetSuffixIcon(icon)
	tag := &sushi.Tag{}
	tag.Title = title
	return tag
}

func getProductOfferings(ctx context.Context, productId int32) []string {
	pcl := util.GetProductClient()
	productOfferingsReq := &productPB.GetProductOfferingsReq{
		ProductId: productId,
	}
	response, err := pcl.GetProductOfferings(ctx, productOfferingsReq)
	if err != nil {
		log.Println("func:getProductOfferings, error in getting product offerings for product id: %d, err: ", productId, err)
	} else {
		return response.ProductOfferings
	}
	return []string{}
}

func (c *SummercampCoursePageTemplate) SetMedia(ctx context.Context) {
	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	/*if c.Response.Video != nil {
		video, _ := sushi.NewVideo(c.Response.Video.VideoUrl)
		if c.Response.Video.ThumbnailUrl != "" {
			thumb, _ := sushi.NewImage(util.GetCDNLink(c.Response.Video.ThumbnailUrl))
			video.SetThumb(thumb)
		}
		video.SetAspectRatio(1.8)
		video.SetSnippetConfig(&sushi.VideoConfig{Autoplay: int8(1)})
		mediaContent := &sushi.MediaContent{
			MediaType: "video",
			Video:     video,
		}
		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
		})
	}*/
	if len(c.Response.ImagesPaths) > 0 {
		for _, image := range c.Response.ImagesPaths {
			image, _ := sushi.NewImage(util.GetCDNLink(image))
			image.SetAspectRatio(1.8)
			mediaContent := &sushi.MediaContent{
				MediaType: "image",
				Image:     image,
			}
			mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
				MediaContent: mediaContent,
			})
		}
	}
	if len(mediaItems) > 0 {
		mediaSnippet := &sushi.MediaType2Snippet{
			Items: mediaItems,
		}
		result := &academyModels.AcademyCourseResult{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType:      sushi.MediaSnippetType2,
				LayoutType:       sushi.LayoutTypeCarousel,
				SectionCount:     1,
				ShouldAutoScroll: true,
			},
			MediaSnippetType2: mediaSnippet,
		}
		c.Results = append(c.Results, result)
	}
}

func (c *SummercampCoursePageTemplate) SetHighlightsSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Summer camp at cult")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	/*separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}*/

	section := &academyModels.AcademyCourseResult{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
		//SnippetConfig:		snippetConfig,
	}

	c.Results = append(c.Results, section)

	imageTextSnippetType30Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	var items []sushi.ImageTextSnippetType30SnippetItem
	for _, data := range apiCommon.AllSportHighlights[c.Json.CourseId] {
		image, _ := sushi.NewImage(data.Image)
		title, _ := sushi.NewTextSnippet(data.Title)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint100)
		title.SetFont(font)
		title.SetColor(color)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(28)
		image.SetWidth(28)

		substitle1, _ := sushi.NewTextSnippet(data.Subtitle1)
		substitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		substitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		substitle1.SetFont(substitle1Font)
		substitle1.SetColor(substitle1Color)

		textSnippet30Item := sushi.ImageTextSnippetType30SnippetItem{
			Image:     image,
			Title:     title,
			Subtitle1: substitle1,
		}
		items = append(items, textSnippet30Item)
	}

	imageTextSnippetType30Snippet := &sushi.ImageTextSnippetType30Snippet{
		Items: &items,
	}

	sectionHighlights := &academyModels.AcademyCourseResult{
		LayoutConfig:       imageTextSnippetType30Layout,
		ImageSnippetType30: imageTextSnippetType30Snippet,
		SnippetConfig: &sushi.SnippetConfig{
			BottomSeparator: &sushi.Separator{
				Type: sushi.SeparatorTypeMedium,
				Color: &sushi.Color{
					Tint: sushi.ColorTint100,
					Type: sushi.ColorTypeGrey,
				},
			},
		},
	}
	c.Results = append(c.Results, sectionHighlights)
}

func (c *SummercampCoursePageTemplate) SetMeasuresAndBenefits(ctx context.Context) {
	for _, benefit := range c.Response.Benefits {
		benefitSectionTitle, _ := sushi.NewTextSnippet(benefit.Title)

		benefitTitleImage, _ := sushi.NewImage(benefit.Image)
		benefitTitleImage.SetAspectRatio(2.277)
		benefitTitleImage.SetType(sushi.ImageTypeRectangle)

		items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
		for _, benefitDescription := range benefit.BenefitDescription {
			benefitTitle, _ := sushi.NewTextSnippet(benefitDescription.Title)
			benefitTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			benefitTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			benefitTitle.SetFont(benefitTitleFont)
			benefitTitle.SetColor(benefitTitleColor)

			benefitImage, _ := sushi.NewImage(benefitDescription.Image1)
			benefitImage.SetAspectRatio(1)
			benefitImage.SetType(sushi.ImageTypeCircle)
			benefitImage.SetHeight(24)

			items = append(items, sushi.ImageTextSnippetType30SnippetItem{
				Image: benefitImage,
				Title: benefitTitle,
			})
		}
		accordionSnippetType4Item := &sushi.AccordionSnippetType4SnippetItem{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType:  sushi.ImageTextSnippetType30,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
				ShouldResize: true,
			},
			ImageTextSnippetType30: &sushi.ImageTextSnippetType30Snippet{
				Items: &items,
			},
		}
		benefitItems := []*sushi.AccordionSnippetType4SnippetItem{accordionSnippetType4Item}
		benefitResult := &academyModels.AcademyCourseResult{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType: sushi.AccordionSnippetType4,
			},
			AccordionSnippetType4: &sushi.AccordionSnippetType4Snippet{
				Image: benefitTitleImage,
				Title: benefitSectionTitle,
				Items: benefitItems,
			},
			SnippetConfig: &sushi.SnippetConfig{
				BottomSeparator: &sushi.Separator{
					Type: sushi.SeparatorTypeMedium,
					Color: &sushi.Color{
						Tint: sushi.ColorTint100,
						Type: sushi.ColorTypeGrey,
					},
				},
			},
		}
		c.Results = append(c.Results, benefitResult)
	}
}

func (c *SummercampCoursePageTemplate) SetCourseInfo(ctx context.Context) {

	title, _ := sushi.NewTextSnippet(apiCommon.AllSummercampCourse[c.Json.CourseId].SportName)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	title.SetFont(titleFont)

	subtitle, _ := sushi.NewTextSnippet(apiCommon.AllSummercampCourse[c.Json.CourseId].Subtitle)
	subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle.SetFont(subtitleFont)
	subtitle.SetColor(subtitleColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint200)

	item := &sushi.FitsoImageTextSnippetType10SnippetItem{
		Title:    title,
		Subtitle: subtitle,
		BgColor:  bgColor,
	}

	var items []*sushi.FitsoImageTextSnippetType10SnippetItem
	iconImage, _ := sushi.NewImage(util.GetCDNLink(apiCommon.AllSummercampCourse[c.Json.CourseId].SportIconPath))
	iconImage.SetAspectRatio(1)
	iconImage.SetType(sushi.ImageTypeCircle)
	iconImage.SetWidth(48)
	iconImage.SetHeight(48)
	item.RightImage = iconImage

	items = append(items, item)
	fitsoImageTextSnippetType10 := &sushi.FitsoImageTextSnippetType10Snippet{
		Items: items,
	}
	courseInfoResult1 := &academyModels.AcademyCourseResult{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: sushi.FitsoImageTextSnippetType10,
			LayoutType:  sushi.LayoutTypeGrid,
		},
		FitsoImageTextSnippetType10: fitsoImageTextSnippetType10,
	}
	c.Results = append(c.Results, courseInfoResult1)
}

func (c *SummercampCoursePageTemplate) SetNearByCentersSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet(fmt.Sprintf("%s centers near you", apiCommon.AllSummercampCourse[c.Json.CourseId].SportName))

	impressionEname := &sushi.EnameData{
		Ename: "summercamp_centers_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := c.GetClevertapTrackItem(ctx, nil, sectionEvents)

	c.Results = append(c.Results, &academyModels.AcademyCourseResult{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: sushi.SectionHeaderType1,
		},
		SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
			Title:             title,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		},
	})
	nearbyFacilitiesResponse := c.Response
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Centers) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoFacilityCardType2,
	}
	if len(nearbyFacilitiesResponse.Centers) == 1 {
		layoutConfig.LayoutType = sushi.LayoutTypeGrid
		layoutConfig.SectionCount = 1
	} else {
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	}

	items := make([]sushi.FitsoFacilityCardType2SnippetItem, 0)
	for _, facility := range nearbyFacilitiesResponse.Centers {
		snippet := sushi.FitsoFacilityCardType2SnippetItem{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sport {
			sportNames = append(sportNames, sport.SportName)
		}
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction

		ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}

		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		if len(facility.Attributes) > 0 {
			bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
			for _, val := range facility.Attributes {
				attrImage, _ := sushi.NewImage(val.Image)

				if val.Type == attributeMaxSafety {
					attrImage.SetAspectRatio(2.277777)
					attrImage.SetType(sushi.ImageTypeRectangle)
				} else if val.Type == attributePeopleCount {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeCircle)
				} else if val.Type == attributeVaccinatedStaff {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeRectangle)
				}

				attrTitle, _ := sushi.NewTextSnippet(val.Title)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				attrTitle.SetColor(titleColor)

				bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
					LeftImage: attrImage,
					Title:     attrTitle,
				}
				bottomItems = append(bottomItems, bottomContainerSnippetItem)
			}
			snippet.BottomContainerSnippetItems = &bottomItems
		}

		multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
		tagAdded := false
		cityId := util.GetCityIDFromContext(ctx)
		for _, sport := range facility.Sport {
			if sport.SportId != c.Json.CourseId {
				continue
			}
			if tagAdded {
				break
			}
			for _, attribute := range sport.FsInfo {
				if attribute == "Heated Pool" && apiCommon.BANGALORE_CITY_ID != cityId && false {
					continue
				}
				tagAdded = true
				tagTitle, _ := sushi.NewTextSnippet(attribute)

				if !featuresupport.SupportsNewColor(ctx) {
					multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
					multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
					gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:    tagTitle,
						Gradient: gradient,
					})
				} else {
					multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:   tagTitle,
						BgColor: multiTagSnippetItemColor,
					})
				}
			}
		}
		if len(multiTagItems) > 0 {
			snippet.TopTags = &multiTagItems
		}

		horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
			Text: facility.SubzoneName,
		}
		horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
			HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
		}
		snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "facility"
		tapEname := &sushi.EnameData{
			Ename: "summercamp_course_facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, snippet)
	}
	facilitiesSnippet := &sushi.FitsoFacilityCardType2Snippet{
		Items: &items,
	}
	section := &academyModels.AcademyCourseResult{
		LayoutConfig:           layoutConfig,
		FitsoFacilityCardType2: facilitiesSnippet,
	}
	c.Results = append(c.Results, section)
}

func (c *SummercampCoursePageTemplate) SetFooterSectionSummercamp(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	items := c.GetSummercampFooterCTA(ctx)

	if len(items) > 0 {
		bottomButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}
		snippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: bottomButton,
		}
		c.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig:       layoutConfig,
			FooterSnippetType2: snippet,
		}
	}
}

func (c *SummercampCoursePageTemplate) GetSummercampFooterCTA(ctx context.Context) []sushi.FooterSnippetType2ButtonItem {
	loggedInUser := util.GetUserIDFromContext(ctx)
	var hasSummerCampSubscription bool
	if loggedInUser > 0 {
		userClient := util.GetUserServiceClient()
		response, err := userClient.GetSummerCampSubscriptionCountByCity(ctx, &userPB.Empty{})

		if err != nil {
			log.Printf("Unable to fetch city based summer camp subscription details for user %d, Error: %v", loggedInUser, err)
		}
		if response.Count > 0 {
			hasSummerCampSubscription = true
		}
	}
	buttonText := "Buy Summer Camp membership"
	if hasSummerCampSubscription {
		buttonText = "Buy another membership"
	}
	items := []sushi.FooterSnippetType2ButtonItem{}
	membershipButton := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Text: buttonText,
	}
	membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	urlDeeplink := util.GetPurchaseMembershipDeeplink(apiCommon.SummerCampCategoryID)

	membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
		URL: urlDeeplink,
	})
	membershipButton.ClickAction = membershipButtonClickAction

	products := c.ProductDetails
	if products != nil && products.SummercampProductsBySport[c.Json.CourseId] != nil && !hasSummerCampSubscription {
		if len(products.SummercampProductsBySport[c.Json.CourseId].Products) > 0 {
			sportProduct := products.SummercampProductsBySport[c.Json.CourseId].Products[len(products.SummercampProductsBySport[c.Json.CourseId].Products)-1]
			daysInt, _ := util.GetProductDaysOfWeekSummercamp(sportProduct.DaysOfWeek)
			daysIntCount := int32(len(daysInt))
			perSessionPrice := int32(sportProduct.RetailPrice) / (sportProduct.Duration * daysIntCount)
			for _, product := range products.SummercampProductsBySport[c.Json.CourseId].Products {
				daysInt, _ = util.GetProductDaysOfWeekSummercamp(product.DaysOfWeek)
				daysIntCount = int32(len(daysInt))

				if perSessionPrice > (int32(product.RetailPrice) / (product.Duration * daysIntCount)) {
					perSessionPrice = int32(product.RetailPrice) / (product.Duration * daysIntCount)
				}
			}
			subtitle2Text := fmt.Sprintf("starts @ just ₹%s/session", strconv.Itoa(int(perSessionPrice)))
			membershipButton.Subtext = subtitle2Text
		}
	}
	items = append(items, membershipButton)
	cityId := util.GetCityIDFromContext(ctx)
	if apiCommon.BANGALORE_CITY_ID == cityId || true {
		trialButton := c.GetSummercampTrialButton(ctx)
		if trialButton != nil {
			items = append(items, *trialButton)
		}
	}
	/*trialButton := c.GetSummercampTrialButton(ctx, sportIds)
	if trialButton != nil {
		items = append(items, *trialButton)
	}*/
	return items
}

func  (c *SummercampCoursePageTemplate)GetSummercampTrialButton(ctx context.Context) *sushi.FooterSnippetType2ButtonItem {
	button := &sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeText,
		Text: "Try for free",
	}

	tapEname := &sushi.EnameData{
		Ename: "summercamp_book_trial_button_tap",
	}
	tapEvents := sushi.NewClevertapEvents()
	tapEvents.SetTap(tapEname)

	trackItem := c.GetClevertapTrackItem(ctx, nil, tapEvents)
	button.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	clickAction := c.GetSummercampTrialCTA(ctx)
	button.ClickAction = clickAction

	return button
}

func (c *SummercampCoursePageTemplate) GetSummercampTrialCTA(ctx context.Context) *sushi.ClickAction {
	buttonClickAction := sushi.GetClickAction()
	payload := map[string]int32{
		"sport_id":  c.Json.CourseId,
		"course_id": c.Json.CourseId,
		"product_category_id":	13,
	}
	postbackParams, _ := json.Marshal(payload)

	buttonClickAction.SetDeeplink(&sushi.Deeplink{
		URL:            util.GetSummercampTrialListMemberDeeplink(),
		PostbackParams: string(postbackParams),
	})

	return buttonClickAction
}

func (c *SummercampCoursePageTemplate) SetPageTracking(ctx context.Context) {
	backArrowEname := &sushi.EnameData{
		Ename: "summercamp_course_page_back_arrow_tap",
	}
	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := c.GetClevertapTrackItem(ctx, nil, backArrowEvents)

	landingEname := &sushi.EnameData{
		Ename: "summercamp_course_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := c.GetClevertapTrackItem(ctx, nil, landingEvents)

	c.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}
}

func (c *SummercampCoursePageTemplate) GetClevertapTrackItem(
	ctx context.Context,
	trackPayload map[string]interface{},
	trackEvents *sushi.EventNames,
) *sushi.ClevertapItem {
	if trackPayload == nil {
		trackPayload = make(map[string]interface{})
	}
	trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
	trackPayload["course_id"] = c.Json.CourseId
	trackPayload["sport_id"] = c.Json.CourseId
	trackPayload["source"] = "summercamp_course"

	return sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
}

type SummercampTabsTemplate struct {
	Header *academyModels.AcademyPageHeader `json:"header,omitempty"`
	Tabs   []*academyModels.AcademyTab      `json:"tabs,omitempty"`
}

func GetSummercampTabsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetTabsRequest)
	facilityClient := util.GetFacilitySportClient()

	summercampTabsResponse, err := facilityClient.GetSummercampTabs(ctx, &facilitySportPB.GetAcademyTabsRequest{
		CourseId:        json.CourseId,
		SelectedTabType: json.SelectedTabType,
	})
	if err != nil {
		log.Printf("Error in GetSummercampTabsC for page_type: %s, error: %v", json.PageType, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	if len(summercampTabsResponse.Tabs) == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	pageTitle, _ := sushi.NewTextSnippet(strings.Title(summercampTabsResponse.Header))
	pageTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	pageTitle.SetFont(pageTitleFont)

	tabsTemplate := &SummercampTabsTemplate{
		Header: &academyModels.AcademyPageHeader{
			Title: pageTitle,
		},
	}

	for _, tab := range summercampTabsResponse.Tabs {
		tabTitle, _ := sushi.NewTextSnippet(tab.Title)
		tabTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
		tabTitle.SetFont(tabTitleFont)

		trackPayload := make(map[string]interface{})
		trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
		trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
		trackPayload["tab_id"] = tab.Id
		trackPayload["tab_type"] = json.PageType

		tapEname := &sushi.EnameData{
			Ename: "course_summercamp_tab_tap",
		}
		trackEvents := sushi.NewClevertapEvents()
		trackEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)

		tabsTemplate.Tabs = append(tabsTemplate.Tabs, &academyModels.AcademyTab{
			Id:                tab.Id,
			Title:             tabTitle,
			PostbackParam:     tab.PostbackParams,
			QueryParam:        tab.QueryParams,
			IsSelected:        tab.IsSelected,
			Type:              tab.Type,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		})
	}

	c.JSON(http.StatusOK, tabsTemplate)
}
