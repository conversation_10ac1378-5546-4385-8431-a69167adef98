package facilitySportController

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	academyModels "bitbucket.org/jogocoin/go_api/api/models/facility"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type AcademyCoursePageTemplate struct {
	Response          *facilitySportPB.GetAcademyCoursePageResponse `json:"-"`
	Results           []*academyModels.AcademyCourseResult          `json:"results,omitempty"`
	Json              structs.GetAcademyCoursePageRequest           `json:"-"`
	Footer            *sushi.FooterSnippetType2Layout               `json:"footer,omitempty"`
	HasMore           bool                                          `json:"has_more,omitempty"`
	PostbackParams    string                                        `json:"postback_params,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                        `json:"clever_tap_tracking,omitempty"`
}

func GetAcademyCoursePageC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	jsonData := c.MustGet("jsonData").(structs.GetAcademyCoursePageRequest)
	log.Printf("GetAcademyCoursePageC: Error Provider: City Id - %d", util.GetCityIDFromContext(ctx))
	facilityClient := util.GetFacilitySportClient()
	academyCoursePage, err := facilityClient.GetAcademyCoursePage(ctx, &facilitySportPB.GetAcademyCoursePageRequest{
		CourseId:      jsonData.CourseId,
		FacilityCount: apiCommon.ACADEMY_COURSE_PAGE_FACILITY_COUNT,
	})
	if jsonData.CourseId == 3 || jsonData.CourseId == 7 {//summercamp hit
		GetSummercampCoursePageC(c)
		return
	}
	if err != nil {
		log.Printf("Error in GetAcademyCoursePageC for course_id: %d, error: %v", jsonData.CourseId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if academyCoursePage.Status != nil {
		if academyCoursePage.Status.Status == apiCommon.FAILED {
			log.Println("Failed in controller GetAcademyCoursePageC while getting details for course_id: %d", jsonData.CourseId)
			c.JSON(http.StatusOK, academyCoursePage.Status)
			return
		}
		if academyCoursePage.Status.Status == apiCommon.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
			return
		}
	}

	coursePageTemplate := &AcademyCoursePageTemplate{
		Response:       academyCoursePage,
		Json:           jsonData,
		PostbackParams: "",
		HasMore:        false,
	}
	if academyCoursePage.CourseId > 0 {
		coursePageTemplate.SetMedia(ctx)
		coursePageTemplate.SetCourseInfo(ctx)
		coursePageTemplate.SetCurriculumDetailsSection(ctx)
		coursePageTemplate.SetCourseCategories(ctx)
		coursePageTemplate.SetCourseCreators(ctx)
		coursePageTemplate.SetMeasuresAndBenefits(ctx)
		coursePageTemplate.SetNearbyFacilities(ctx)
		coursePageTemplate.SetFooter(ctx)
		coursePageTemplate.SetPageTracking(ctx)
	}

	c.JSON(http.StatusOK, coursePageTemplate)
}

func (c *AcademyCoursePageTemplate) SetMedia(ctx context.Context) {
	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	if c.Response.Video != nil && false {
		video, _ := sushi.NewVideo(c.Response.Video.VideoUrl)
		if c.Response.Video.ThumbnailUrl != "" {
			thumb, _ := sushi.NewImage(util.GetCDNLink(c.Response.Video.ThumbnailUrl))
			video.SetThumb(thumb)
		}
		video.SetAspectRatio(1.8)
		video.SetSnippetConfig(&sushi.VideoConfig{Autoplay: int8(1)})
		mediaContent := &sushi.MediaContent{
			MediaType: "video",
			Video:     video,
		}
		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
		})
	}
	
	if len(c.Response.Images) > 0 {
		for _, image := range c.Response.Images {
			image, _ := sushi.NewImage(util.GetCDNLink(image))
			image.SetAspectRatio(1.8)
			mediaContent := &sushi.MediaContent{
				MediaType: "image",
				Image:     image,
			}
			mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
				MediaContent: mediaContent,
			})
		}
	}
	if len(mediaItems) > 0 {
		mediaSnippet := &sushi.MediaType2Snippet{
			Items: mediaItems,
		}
		result := &academyModels.AcademyCourseResult{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType:      sushi.MediaSnippetType2,
				LayoutType:       sushi.LayoutTypeCarousel,
				SectionCount:     1,
				ShouldAutoScroll: true,
			},
			MediaSnippetType2: mediaSnippet,
		}
		c.Results = append(c.Results, result)
	}
}

func (c *AcademyCoursePageTemplate) SetCourseInfo(ctx context.Context) {
	title, _ := sushi.NewTextSnippet(c.Response.CourseName)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	title.SetFont(titleFont)

	subtitle, _ := sushi.NewTextSnippet(c.Response.CourseSubtitle)
	subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle.SetFont(subtitleFont)
	subtitle.SetColor(subtitleColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint200)

	item := &sushi.FitsoImageTextSnippetType10SnippetItem{
		Title:    title,
		Subtitle: subtitle,
		BgColor:  bgColor,
	}

	var items []*sushi.FitsoImageTextSnippetType10SnippetItem
	if len(c.Response.SportIcons) == 1 {
		iconImage, _ := sushi.NewImage(util.GetCDNLink(c.Response.SportIcons[0].IconUrl))
		iconImage.SetAspectRatio(1)
		iconImage.SetType(sushi.ImageTypeCircle)
		iconImage.SetWidth(48)
		iconImage.SetHeight(48)
		item.RightImage = iconImage
	}

	items = append(items, item)
	fitsoImageTextSnippetType10 := &sushi.FitsoImageTextSnippetType10Snippet{
		Items: items,
	}
	courseInfoResult1 := &academyModels.AcademyCourseResult{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: sushi.FitsoImageTextSnippetType10,
			LayoutType:  sushi.LayoutTypeGrid,
		},
		FitsoImageTextSnippetType10: fitsoImageTextSnippetType10,
	}
	c.Results = append(c.Results, courseInfoResult1)
}

func (c *AcademyCoursePageTemplate) SetCourseCategories(ctx context.Context) {
	var items []*sushi.FitsoImageTextSnippetType10SnippetItem
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	for _, category := range c.Response.CourseCategories {
		var item sushi.FitsoImageTextSnippetType10SnippetItem

		title, _ := sushi.NewTextSnippet(category.Name)
		titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		title.SetFont(titleFont)
		item.Title = title

		image, _ := sushi.NewImage(category.Image)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(48)
		image.SetWidth(48)
		item.RightImage = image

		var gradientColorType sushi.ColorType
		switch category.BgColor {
		case "green":
			gradientColorType = sushi.ColorTypeGreen
		case "blue":
			gradientColorType = sushi.ColorTypeBlue
		case "red":
			gradientColorType = sushi.ColorTypeRed
		default:
			gradientColorType = sushi.ColorTypeRed
		}

		gradient, _ := sushi.NewGradient([]sushi.Color{
			sushi.Color{Type: gradientColorType, Tint: sushi.ColorTint200},
			sushi.Color{Type: gradientColorType, Tint: sushi.ColorTint100},
			sushi.Color{Type: gradientColorType, Tint: sushi.ColorTint50},
		})

		if isNewColorSupported {
			switch category.BgColor {
			case "green":
				gradientColorType = sushi.ColorTypeGreen
			case "blue":
				gradientColorType = sushi.ColorTypeBlue
			case "red":
				gradientColorType = sushi.ColorTypeRed
			case "cyan":
				gradientColorType = sushi.ColorTypeCyan
			default:
				gradientColorType = sushi.ColorTypeRed
			}

			gradient, _ = sushi.NewGradient([]sushi.Color{
				sushi.Color{Type: gradientColorType, Tint: sushi.ColorTint100},
				sushi.Color{Type: gradientColorType, Tint: sushi.ColorTint50},
			})
		}
		item.Gradient = gradient

		bottomButton, _ := sushi.NewButton(sushi.ButtontypeText)
		bottomButtonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		bottomButtonSuffixIcon, _ := sushi.NewIcon(sushi.RightIcon, nil)
		bottomButton.SetFont(bottomButtonFont)
		bottomButton.SetSuffixIcon(bottomButtonSuffixIcon)
		bottomButton.SetText("See details")
		item.BottomButton = bottomButton

		clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		clickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetCourseCategoryDeeplink(category.CourseId, apiCommon.PAGE_TYPE_COURSE_CATEGORY, category.CourseCategoryId, ""),
		})
		item.ClickAction = clickAction

		trackPayload := map[string]interface{}{"course_category_id": category.CourseCategoryId}
		tapEname := &sushi.EnameData{
			Ename: "course_category_card_tap",
		}
		impressionEname := &sushi.EnameData{
			Ename: "course_category_card_impression",
		}
		trackEvents := sushi.NewClevertapEvents()
		trackEvents.SetTap(tapEname)
		trackEvents.SetImpression(impressionEname)
		trackItem := c.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
		item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, &item)
	}
	fitsoImageTextSnippetType10 := &sushi.FitsoImageTextSnippetType10Snippet{
		Items: items,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoImageTextSnippetType10,
	}
	if len(items) > 1 {
		layoutConfig.VisibleCards = 1.5
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	} else {
		layoutConfig.SectionCount = 1
		layoutConfig.LayoutType = sushi.LayoutTypeGrid
	}
	result := &academyModels.AcademyCourseResult{
		LayoutConfig:                layoutConfig,
		FitsoImageTextSnippetType10: fitsoImageTextSnippetType10,
	}
	c.Results = append(c.Results, result)
}

func (c *AcademyCoursePageTemplate) SetCurriculumDetailsSection(ctx context.Context) {
	curriculumDescriptionTitle, _ := sushi.NewTextSnippet(c.Response.CourseDescription.Title)
	curriculumDescriptionTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	curriculumDescriptionTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	curriculumDescriptionTitle.SetFont(curriculumDescriptionTitleFont)
	curriculumDescriptionTitle.SetColor(curriculumDescriptionTitleColor)
	curriculumDescriptionTitle.SetKerning(3)

	curriculumDescriptionSubitle, _ := sushi.NewTextSnippet(c.Response.CourseDescription.Description)
	curriculumDescriptionSubitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	curriculumDescriptionSubitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	curriculumDescriptionSubitle.SetFont(curriculumDescriptionSubitleFont)
	curriculumDescriptionSubitle.SetColor(curriculumDescriptionSubitleColor)

	textSnippetType1 := &sushi.TextSnippetType1Snippet{
		Title:     curriculumDescriptionTitle,
		Subtitle1: curriculumDescriptionSubitle,
	}

	courseInfoResult2 := &academyModels.AcademyCourseResult{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType:  sushi.TextSnippetType1,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		},
		TextSnippetType1: textSnippetType1,
	}
	c.Results = append(c.Results, courseInfoResult2)

	courseBenefits := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _, courseBenefit := range c.Response.CourseBenefits {
		benefitImage, _ := sushi.NewImage(util.GetCDNLink(courseBenefit.Image))
		benefitImage.SetAspectRatio(1)
		benefitImage.SetHeight(16)
		benefitImage.SetType(sushi.ImageTypeCircle)

		benefitTitle, _ := sushi.NewTextSnippet(courseBenefit.Title)
		benefitTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		benefitTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		benefitTitle.SetFont(benefitTitleFont)
		benefitTitle.SetColor(benefitTitleColor)

		courseBenefits = append(courseBenefits, sushi.ImageTextSnippetType30SnippetItem{
			Image: benefitImage,
			Title: benefitTitle,
		})
	}
}

func (c *AcademyCoursePageTemplate) SetCourseCreators(ctx context.Context) {
	items := make([]sushi.V2ImageTextSnippetType49SnippetItem, 0)
	if c.Response.CourseCreator != nil {
		for _, creator := range c.Response.CourseCreator.CourseCreators {
			title, _ := sushi.NewTextSnippet(creator.Name)
			titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
			title.SetColor(titleColor)
			title.SetFont(titleFont)

			subtitle, _ := sushi.NewTextSnippet(creator.Title)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
			subtitle.SetColor(subtitleColor)
			subtitle.SetFont(subtitleFont)

			image, _ := sushi.NewImage(util.GetCDNLink(creator.ImageUrl))
			image.SetAspectRatio(0.9)
			image.SetWidth(125)
			image.SetHeight(125)

			subtitlesList := make([]sushi.SubtitleListItem, 0)
			for _, creatorDetail := range creator.Details {
				detailTitle, _ := sushi.NewTextSnippet(creatorDetail)
				detailTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
				detailTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint600)
				detailTitle.SetColor(detailTitleColor)
				detailTitle.SetFont(detailTitleFont)
				detailTitle.SetNumberOfLines(0)

				icon, _ := sushi.NewIcon(sushi.PointIcon, nil)

				subtitlesList = append(subtitlesList, sushi.SubtitleListItem{
					Title: detailTitle,
					Icon:  icon,
				})
			}

			var bottomButton *sushi.Button
			if creator.Video != nil {
				bottomButton, _ = sushi.NewButton(sushi.ButtontypeText)
				bottomButton.SetText("Watch video")
				bottomButton.SetSuffixIcon(&sushi.Icon{Code: sushi.RightIcon})
				video := util.ConvertVideoProtoToVideoTemplate(creator.Video)
				videoClickAction := sushi.GetClickAction()
				videoClickAction.SetOpenVideoPage(video)
				bottomButton.SetClickAction(videoClickAction)
			}

			items = append(items, sushi.V2ImageTextSnippetType49SnippetItem{
				Title:         title,
				Subtitle:      subtitle,
				RightImage:    image,
				SubtitlesList: &subtitlesList,
				BottomButton:  bottomButton,
			})
		}
	}

	if len(items) > 0 {
		courseCreatorTitle, _ := sushi.NewTextSnippet(c.Response.CourseCreator.Title)
		courseCreatorTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		courseCreatorTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		courseCreatorTitle.SetFont(courseCreatorTitleFont)
		courseCreatorTitle.SetColor(courseCreatorTitleColor)

		v2ImageTextSnippetType49 := &sushi.V2ImageTextSnippetType49Snippet{
			Items: &items,
			Title: courseCreatorTitle,
		}
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.V2ImageTextSnippetType49,
		}
		if len(items) > 1 {
			layoutConfig.LayoutType = sushi.LayoutTypeCarousel
		} else {
			layoutConfig.SectionCount = 1
			layoutConfig.LayoutType = sushi.LayoutTypeGrid
		}
		creatorsResult := &academyModels.AcademyCourseResult{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType49: v2ImageTextSnippetType49,
			SnippetConfig: &sushi.SnippetConfig{
				BottomSeparator: &sushi.Separator{
					Type: sushi.SeparatorTypeMedium,
					Color: &sushi.Color{
						Tint: sushi.ColorTint100,
						Type: sushi.ColorTypeGrey,
					},
				},
			},
		}
		c.Results = append(c.Results, creatorsResult)
	}
}

func (c *AcademyCoursePageTemplate) SetMeasuresAndBenefits(ctx context.Context) {
	for _, benefit := range c.Response.Benefits {
		benefitSectionTitle, _ := sushi.NewTextSnippet(benefit.Title)

		benefitTitleImage, _ := sushi.NewImage(benefit.Image)
		benefitTitleImage.SetAspectRatio(2.277)
		benefitTitleImage.SetType(sushi.ImageTypeRectangle)

		items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
		for _, benefitDescription := range benefit.BenefitDescription {
			benefitTitle, _ := sushi.NewTextSnippet(benefitDescription.Title)
			benefitTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			benefitTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			benefitTitle.SetFont(benefitTitleFont)
			benefitTitle.SetColor(benefitTitleColor)

			benefitImage, _ := sushi.NewImage(benefitDescription.Image1)
			benefitImage.SetAspectRatio(1)
			benefitImage.SetType(sushi.ImageTypeCircle)
			benefitImage.SetHeight(24)

			items = append(items, sushi.ImageTextSnippetType30SnippetItem{
				Image: benefitImage,
				Title: benefitTitle,
			})
		}
		accordionSnippetType4Item := &sushi.AccordionSnippetType4SnippetItem{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType:  sushi.ImageTextSnippetType30,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
				ShouldResize: true,
			},
			ImageTextSnippetType30: &sushi.ImageTextSnippetType30Snippet{
				Items: &items,
			},
		}
		benefitItems := []*sushi.AccordionSnippetType4SnippetItem{accordionSnippetType4Item}
		benefitResult := &academyModels.AcademyCourseResult{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType: sushi.AccordionSnippetType4,
			},
			AccordionSnippetType4: &sushi.AccordionSnippetType4Snippet{
				Image: benefitTitleImage,
				Title: benefitSectionTitle,
				Items: benefitItems,
			},
			SnippetConfig: &sushi.SnippetConfig{
				BottomSeparator: &sushi.Separator{
					Type: sushi.SeparatorTypeMedium,
					Color: &sushi.Color{
						Tint: sushi.ColorTint100,
						Type: sushi.ColorTypeGrey,
					},
				},
			},
		}
		c.Results = append(c.Results, benefitResult)
	}
}

func (c *AcademyCoursePageTemplate) SetFooter(ctx context.Context) {
	items := homeC.GetAcademyFooterCTA(ctx, "academy_course_trial_member_select", c.Response.CourseSportIds, c.Json.CourseId)
	if len(items) > 0 {
		bottomButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}

		c.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType: sushi.FooterSnippetType2,
			},
			FooterSnippetType2: &sushi.FooterSnippetType2Snippet{
				ButtonData: bottomButton,
			},
		}
	}
}

func (c *AcademyCoursePageTemplate) SetNearbyFacilities(ctx context.Context) {
	if c.Response.Facilities != nil && len(c.Response.Facilities.NearbyFacilities) > 0 {
		title, _ := sushi.NewTextSnippet(c.Response.Facilities.Title)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetColor(titleColor)
		title.SetFont(titleFont)
		title.SetKerning(3)

		impressionEname := &sushi.EnameData{
			Ename: "academy_centers_impression",
		}
		sectionEvents := sushi.NewClevertapEvents()
		sectionEvents.SetImpression(impressionEname)
		trackItem := c.GetClevertapTrackItem(ctx, nil, sectionEvents)

		c.Results = append(c.Results, &academyModels.AcademyCourseResult{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType: sushi.SectionHeaderType1,
			},
			SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
				Title:             title,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			},
		})
	}
	if c.Response.Facilities != nil {
		isNewColorSupported := featuresupport.SupportsNewColor(ctx)
		for _, facility := range c.Response.Facilities.NearbyFacilities {
			layoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.ResSnippetType3,
			}
			snippet := &sushi.ResType3Snippet{}
			title, _ := sushi.NewTextSnippet(facility.DisplayName)
			snippet.Title = title

			sportNames := []string{}
			for _, sport := range facility.Sports {
				sportNames = append(sportNames, sport.SportName)
			}
			subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
			snippet.Subtitle1 = subtitle1

			image, _ := sushi.NewImage(facility.DisplayPicture)
			snippet.Image = image

			clickAction := sushi.GetClickAction()
			deeplink := sushi.Deeplink{
				URL: facility.Deeplink,
			}
			clickAction.SetDeeplink(&deeplink)
			snippet.ClickAction = clickAction

			ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)
			ratingSnippet := sushi.RatingSnippet{
				Type:  sushi.RatingTypeTagV2,
				TagV2: ratingSnippetBlockItem,
			}
			snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

			var tagTitle *sushi.TextSnippet
			_, div := math.Modf(facility.Distance)
			if (div >= 0.95 || div < 0.05) && div != 0 {
				tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
			} else {
				tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
			}
			tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			tagTitle.SetColor(tagColor)
			topRightTag := &sushi.Tag{
				Title:        tagTitle,
				Size:         sushi.TagSizeMedium,
				Transparency: 0.2,
			}
			snippet.TopRightTag = topRightTag

			if len(facility.Attributes) > 0 {
				bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
				for _, val := range facility.Attributes {
					attrImage, _ := sushi.NewImage(val.Image)
					if val.Type == attributeMaxSafety {
						attrImage.SetAspectRatio(2.277777)
						attrImage.SetType(sushi.ImageTypeRectangle)
					} else if val.Type == attributePeopleCount {
						attrImage.SetAspectRatio(1)
						attrImage.SetType(sushi.ImageTypeCircle)
					} else if val.Type == attributeVaccinatedStaff {
						attrImage.SetAspectRatio(1)
						attrImage.SetType(sushi.ImageTypeRectangle)
					}
					attrTitle, _ := sushi.NewTextSnippet(val.Title)
					titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					attrTitle.SetColor(titleColor)

					bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
						LeftImage: attrImage,
						Title:     attrTitle,
					}

					bottomItems = append(bottomItems, bottomContainerSnippetItem)
				}
				snippet.BottomContainerSnippetItems = &bottomItems
			}

			multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
			for _, sport := range facility.Sports {
				for _, attribute := range sport.FsInfo {
					tagTitle, _ := sushi.NewTextSnippet(attribute)

					if !isNewColorSupported {
						multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
						multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
						gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

						multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
							Title:    tagTitle,
							Gradient: gradient,
						})
					} else {
						multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

						multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
							Title:   tagTitle,
							BgColor: multiTagSnippetItemColor,
						})
					}
				}
			}
			if len(multiTagItems) > 0 {
				snippet.TopTags = &multiTagItems
			}

			horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
				Text: facility.SubzoneName,
			}
			horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
				HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
			}
			snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

			trackPayload := make(map[string]interface{})
			trackPayload["facility_id"] = facility.FacilityId
			tapEname := &sushi.EnameData{
				Ename: "academy_facility_card_tap",
			}
			facilityCardEvents := sushi.NewClevertapEvents()
			facilityCardEvents.SetTap(tapEname)
			trackItem := c.GetClevertapTrackItem(ctx, trackPayload, facilityCardEvents)
			snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

			section := &academyModels.AcademyCourseResult{
				LayoutConfig:    layoutConfig,
				ResType3Snippet: snippet,
			}
			c.Results = append(c.Results, section)
		}
	}
}

func (c *AcademyCoursePageTemplate) SetPageTracking(ctx context.Context) {
	backArrowEname := &sushi.EnameData{
		Ename: "course_page_back_arrow_tap",
	}
	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := c.GetClevertapTrackItem(ctx, nil, backArrowEvents)

	landingEname := &sushi.EnameData{
		Ename: "course_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := c.GetClevertapTrackItem(ctx, nil, landingEvents)

	c.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}
}

func (c *AcademyCoursePageTemplate) GetClevertapTrackItem(
	ctx context.Context,
	trackPayload map[string]interface{},
	trackEvents *sushi.EventNames,
) *sushi.ClevertapItem {
	if trackPayload == nil {
		trackPayload = make(map[string]interface{})
	}
	trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
	trackPayload["course_id"] = c.Response.CourseId
	trackPayload["source"] = "course"

	return sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
}

type AcademyTabsTemplate struct {
	Header *academyModels.AcademyPageHeader `json:"header,omitempty"`
	Tabs   []*academyModels.AcademyTab      `json:"tabs,omitempty"`
}

func GetAcademyTabsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetTabsRequest)
	facilityClient := util.GetFacilitySportClient()

	academyTabsResponse, err := facilityClient.GetAcademyTabs(ctx, &facilitySportPB.GetAcademyTabsRequest{
		PageType:         json.PageType,
		CourseId:         json.CourseId,
		CourseCategoryId: json.CourseCategoryId,
		SelectedTabType:  json.SelectedTabType,
	})
	if err != nil {
		log.Printf("Error in getAcademyTabsC for page_type: %s, error: %v", json.PageType, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	if len(academyTabsResponse.Tabs) == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	pageTitle, _ := sushi.NewTextSnippet(strings.Title(academyTabsResponse.Header))
	pageTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	pageTitle.SetFont(pageTitleFont)

	tabsTemplate := &AcademyTabsTemplate{
		Header: &academyModels.AcademyPageHeader{
			Title: pageTitle,
		},
	}

	for _, tab := range academyTabsResponse.Tabs {
		tabTitle, _ := sushi.NewTextSnippet(tab.Title)
		tabTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
		tabTitle.SetFont(tabTitleFont)

		trackPayload := make(map[string]interface{})
		trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
		trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
		trackPayload["tab_id"] = tab.Id
		trackPayload["tab_type"] = json.PageType

		tapEname := &sushi.EnameData{
			Ename: "course_tab_tap",
		}
		trackEvents := sushi.NewClevertapEvents()
		trackEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)

		tabsTemplate.Tabs = append(tabsTemplate.Tabs, &academyModels.AcademyTab{
			Id:                tab.Id,
			Title:             tabTitle,
			PostbackParam:     tab.PostbackParams,
			QueryParam:        tab.QueryParams,
			IsSelected:        tab.IsSelected,
			Type:              tab.Type,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		})
	}

	c.JSON(http.StatusOK, tabsTemplate)
}
