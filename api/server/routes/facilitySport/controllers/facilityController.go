// facilitySport controller

package facilitySportController

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
	"github.com/micro/go-micro"

	common "bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models"
	facilityPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/go-redis/redis"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/client/selector/shard"
)

var (
	cl facilitySportPB.FacilitySportService
)

func Ordinal(num int) string {
	var ordinalDictionary = map[int]string{
		0: "th",
		1: "st",
		2: "nd",
		3: "rd",
		4: "th",
		5: "th",
		6: "th",
		7: "th",
		8: "th",
		9: "th",
	}

	floatNum := math.Abs(float64(num))
	positiveNum := int(floatNum)

	if ((positiveNum % 100) >= 11) && ((positiveNum % 100) <= 13) {
		return "th"
	}
	return ordinalDictionary[positiveNum%10]
}

func InsertFacilitySubzoneMapC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	request := &facilitySportPB.Empty{}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	response, err := cl.InsertFacilityAndZomatoSubzoneMap(ctx, request)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilityC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.Facility

	json = c.MustGet("jsonData").(structs.Facility)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var tabId int32
	if headers.TabId > 0 {
		tabId = int32(headers.TabId)
	} else {
		tabId = json.TabId
	}
	facilityData := &facilitySportPB.Facility{
		FacilityId:              json.FacilityID,
		DisplayName:             json.DisplayName,
		AllSportFlag:            json.AllSportFlag,
		SharedFacilityFlag:      json.SharedFacilityFlag,
		DisplayAddress:          json.DisplayAddress,
		CityId:                  json.CityID,
		ZoneId:                  json.ZoneId,
		SubzoneId:               json.SubzoneId,
		FacilityType:            json.FacilityType,
		SocietyId:               json.SocietyId,
		FetchSports:             json.FetchSports,
		FetchAmenities:          json.FetchAmenities,
		FetchCoaches:            json.FetchCoaches,
		FetchReviews:            json.FetchReviews,
		FetchImages:             json.FetchImages,
		FetchRelevantFacilities: json.FetchRelevantFacilities,
		FetchHighlightedPlans:   json.FetchHighlightedPlans,
		SportId:                 json.SportID,
		TabId:                   tabId,
		NonOperationFacilities:  json.NonOperationFacilities,
	}

	timeTillContextDeadline := time.Now().Add(20 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := cl.FacilityGet(ctxbs, facilityData, shard.Strategy(strconv.Itoa(int(json.FacilityID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func MarkFavoriteFacilitiesC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FavoriteFacilityReq

	json = c.MustGet("jsonData").(structs.FavoriteFacilityReq)

	favoritefacilityData := &facilitySportPB.FavoriteFacilityReq{
		FacilitiesIds: json.FacilitiesIds,
		UserId:        json.UserId,
	}

	// session affinity based call
	response, err := cl.MarkFavorite(context.TODO(), favoritefacilityData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateOrUpdateFacilitySportMappingsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FacilitySportMappingReq
	json = c.MustGet("jsonData").(structs.FacilitySportMappingReq)

	SportIdsStrs := strings.Split(json.SportIDs, ",")
	var SportIds []int32
	for i := range SportIdsStrs {
		j, _ := strconv.Atoi(SportIdsStrs[i])
		if j > 0 {
			SportIds = append(SportIds, int32(j))
		}
	}

	facilitySportMappingData := &facilitySportPB.FacilitySportMapping{
		FacilityId:           json.FacilityID,
		SportIds:             SportIds,
		PrebookingWindowSize: json.PrebookingWindowSize,
		CoachingFlag:         json.CoachingFlag,
		OperationStatus:      json.OperationStatus,
		Description:          json.Description,
		Latitude:             json.Latitude,
		Longitude:            json.Longitude,
		FacilityCount:        json.FacilityCount,
		LocationId:           json.LocationID,
		DisplayAddress:       json.DisplayAddress,
		MapLink:              json.MapLink,
		SalesContact:         json.SalesContact,
		GooglePlaceId:        json.GooglePlaceID,
		KidsAcademy:          json.KidsAcademy,
		HasAcademy:           json.HasAcademy,
		NonOperationalDays:   json.NonOperartionalDays,
		ClearCache:           json.ClearCache,
		CityIdV2:             json.CityIdV2,
		OverrideMode:         json.OverrideMode,
		SoldOut:              json.SoldOut,
	}

	timeTillContextDeadline := time.Now().Add(20 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()
	response, err := cl.CreateOrUpdateFacilitySportMappings(ctxbs, facilitySportMappingData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateOrUpdateFacilityC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.Facility

	json = c.MustGet("jsonData").(structs.Facility)

	facilityData := &facilitySportPB.Facility{
		FacilityId:         json.FacilityID,
		DisplayName:        json.DisplayName,
		ShortName:          json.ShortName,
		AllSportFlag:       json.AllSportFlag,
		SharedFacilityFlag: json.SharedFacilityFlag,
		DisplayAddress:     json.DisplayAddress,
		ShortAddress:       json.ShortAddress,
		CityId:             json.CityID,
		ZoneId:             json.ZoneId,
		SubzoneId:          json.SubzoneId,
		FacilityType:       json.FacilityType,
		SocietyId:          json.SocietyId,
		OperationStatus:    json.OperationStatus,
		SharedActivityFlag: json.SharedActivityFlag,
		DisplayPicture:     json.DisplayPicture,
		TimingDescription:  json.TimingDescription,
		IndoorFlag:         json.IndoorFlag,
		LockerFlag:         json.LockerFlag,
		GooglePlaceId:      json.GooglePlaceId,
		FacilityRegionId:   json.FacilityRegionId,
		DatapeaceSpaceId:   json.DatapeaceSpaceId,
		Popularity:         json.Popularity,
		NoShowPenaltyOn:    json.NoShowPenaltyOn,
		FrValidationFlag:   json.FrValidationFlag,
		ClearCache:         json.ClearCache,
		CityIdV2:           json.CityIdV2,
		SubzoneIdV2:        json.SubzoneIdV2,
	}

	// session affinity based call
	response, err := cl.FacilityUpsert(context.TODO(), facilityData, shard.Strategy(strconv.Itoa(int(json.FacilityID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateUpdateFacilityRegionC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FacilityRegion

	json = c.MustGet("jsonData").(structs.FacilityRegion)
	fmt.Println("json---", json)
	facilityRegionData := &facilitySportPB.FacilityRegion{
		FacilityRegionId: json.FacilityRegionId,
		RegionName:       json.RegionName,
		CityId:           json.CityId,
	}

	// session affinity based call
	response, err := cl.FacilityRegionUpsert(context.TODO(), facilityRegionData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetPlayArenaSlotsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.PlayArenaFacilitySportSlot

	json = c.MustGet("jsonData").(structs.PlayArenaFacilitySportSlot)

	facilityData := &facilitySportPB.PlayArenaCapacitySlotRequest{
		PACSID:          json.PACSID,
		FSID:            json.FSID,
		PlayArenaID:     json.PlayArenaID,
		DayOfWeek:       json.DayOfWeek,
		SlotID:          json.SlotID,
		FetchDetails:    json.FetchDetails,
		FetchFSCapacity: json.FetchFSCapacity,
	}

	// session affinity based call
	response, err := cl.PlayArenaCapacitySlotGet(context.TODO(), facilityData, shard.Strategy(strconv.Itoa(int(json.FSID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSportC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.Sport

	json = c.MustGet("jsonData").(structs.Sport)

	sportData := &facilitySportPB.Sport{
		SportId:   json.SportID,
		SportName: json.SportName,
		IsOffered: json.IsOffered,
	}

	// session affinity based call
	response, err := cl.SportGet(context.TODO(), sportData, shard.Strategy(strconv.Itoa(int(json.SportID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilitySportC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FacilitySport

	json = c.MustGet("jsonData").(structs.FacilitySport)

	var CoachingFlagBool bool
	if json.CoachingFlag == 1 {
		CoachingFlagBool = true
	} else {
		CoachingFlagBool = false
	}

	facilitySportMappingData := &facilitySportPB.FacilitySportMapping{
		FacilityId:           json.FacilityID,
		SportId:              json.SportID,
		PrebookingWindowSize: json.PrebookingWindowSize,
		CoachingFlag:         CoachingFlagBool,
		OperationStatus:      json.OperationStatus,
		FsId:                 json.FSID,
		KidsAcademy:          json.KidsAcademy,
		HasSummerCamp:        json.SummerCamp,
	}

	var facilitySportRequest facilitySportPB.FacilitySportRequest

	facilitySportRequest.FSMapping = append(facilitySportRequest.FSMapping, facilitySportMappingData)
	facilitySportRequest.FetchDetails = json.FetchDetails
	facilitySportRequest.IsCityRequired = json.IsCityRequired

	fmt.Println("req ------------------- ", facilitySportRequest)
	// session affinity based call
	response, err := cl.FacilitySportMappingGet(context.TODO(), &facilitySportRequest, shard.Strategy(strconv.Itoa(int(json.FacilityID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilitySportSlotC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FacilitySportSlot

	json = c.MustGet("jsonData").(structs.FacilitySportSlot)

	facilitySportCapacitySlotRequest := &facilitySportPB.FacilitySportCapacitySlotRequest{
		FSID:         json.FSID,
		DayOfWeek:    json.DayOfWeek,
		SlotID:       json.SlotID,
		FetchDetails: json.FetchDetails,
	}

	// session affinity based call
	response, err := cl.FacilitySportCapacitySlotGet(context.TODO(), facilitySportCapacitySlotRequest, shard.Strategy(strconv.Itoa(int(json.FSID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetPlayArenasC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.PlayArena

	json = c.MustGet("jsonData").(structs.PlayArena)

	playArenaRequest := &facilitySportPB.PlayArena{
		FSID:                 json.FSID,
		PrebookingWindowSize: json.PrebookingWindowSize,
		PAID:                 json.PAID,
		CoachingFlag:         json.CoachingFlag,
		OperationStatus:      json.OperationStatus,
	}

	// session affinity based call
	response, err := cl.PlayArenaGet(context.TODO(), playArenaRequest, shard.Strategy(strconv.Itoa(int(json.PAID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSlotC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.Slot

	json = c.MustGet("jsonData").(structs.Slot)

	slotRequest := &facilitySportPB.Slot{
		SlotID: json.SlotID,
	}

	// session affinity based call
	response, err := cl.SlotGet(context.TODO(), slotRequest, shard.Strategy(strconv.Itoa(int(json.SlotID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetProductArenaCategoryC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	client := util.GetFacilitySportClient()
	json := c.MustGet("jsonData").(structs.ProductArenaCategory)

	req := &facilitySportPB.ProductArenaCategoryRequest{
		ProductArenaCategoryId: json.ProductArenaCategoryId,
	}
	res, err := client.GetProductArenaCategory(ctx, req)
	if err != nil {
		log.Println("unable to get product arena category details", err)
		panic(err)
	}
	c.JSON(http.StatusOK, res)
}

func GetSportInFacilityC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FacilitySport

	json = c.MustGet("jsonData").(structs.FacilitySport)

	facilitySportMappingData := &facilitySportPB.FacilitySportMapping{
		FacilityId:           json.FacilityID,
		SportId:              json.SportID,
		PrebookingWindowSize: json.PrebookingWindowSize,
		OperationStatus:      json.OperationStatus,
		FsId:                 json.FSID,
		KidsAcademy:          json.KidsAcademy,
		CityId:               json.CityID,
	}

	var facilitySportRequest facilitySportPB.FacilitySportRequest
	facilitySportRequest.FSMapping = append(facilitySportRequest.FSMapping, facilitySportMappingData)

	fmt.Println("req ------------------- ", facilitySportRequest)
	// session affinity based call
	response, err := cl.GetSportInFacility(context.TODO(), &facilitySportRequest, shard.Strategy(strconv.Itoa(int(json.FacilityID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFitsoFacilitiesC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var json structs.FitsoFacilitiesReq

	json = c.MustGet("jsonData").(structs.FitsoFacilitiesReq)

	// string to int32 conversion
	CityIdsStrs := strings.Split(json.CityIds, ",")
	var CityIds []int32
	for i := range CityIdsStrs {
		j, _ := strconv.Atoi(CityIdsStrs[i])
		if j > 0 {
			CityIds = append(CityIds, int32(j))
		}
	}

	SportIdsStrs := strings.Split(json.SportIds, ",")
	var SportIds []int32
	for i := range SportIdsStrs {
		j, _ := strconv.Atoi(SportIdsStrs[i])
		if j > 0 {
			SportIds = append(SportIds, int32(j))
		}
	}
	RegionIdsStrs := strings.Split(json.RegionIds, ",")
	var RegionIds []int32
	for i := range RegionIdsStrs {
		j, _ := strconv.Atoi(RegionIdsStrs[i])
		if j > 0 {
			RegionIds = append(RegionIds, int32(j))
		}
	}
	fitsoFacilitiesReq := &facilitySportPB.FitsoFacilitiesReq{
		Latitude:  json.Latitude,
		Longitude: json.Longitude,
		CityIds:   CityIds,
		SportIds:  SportIds,
		RegionIds: RegionIds,
	}

	response, err := cl.GetFitsoFacilities(context.TODO(), fitsoFacilitiesReq)

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetActiveCitiesRegionSportsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	var facilitySportRequest facilitySportPB.ActiveCitiesRegionSportsRequest

	// session affinity based call
	response, err := cl.GetActiveCitiesRegionSports(context.TODO(), &facilitySportRequest)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilityRegionC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	// session affinity based call
	response, err := cl.GetFacilityRegion(context.TODO(), &facilitySportPB.Empty{})

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetActiveCitiesC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var json structs.ActiveCitiesReq
	json = c.MustGet("jsonData").(structs.ActiveCitiesReq)

	reqData := &facilitySportPB.GetActiveCitiesRequest{
		AppType:          headers.AppType,
		AppVersion:       headers.AppVersion,
		FetchWithRegions: json.FetchWithRegions,
	}

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	// session affinity based call
	response, err := cl.GetActiveCities(context.TODO(), reqData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSocietiesC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	var json structs.GetSocietiesReq

	json = c.MustGet("jsonData").(structs.GetSocietiesReq)
	reqData := &facilitySportPB.GetSocietiesReq{
		SocietyId:  json.SocietyId,
		CityId:     json.CityId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	// session affinity based call
	response, err := cl.GetSocieties(context.TODO(), reqData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetCoachingTypesC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = facilitySportPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	// session affinity based call
	response, err := cl.GetCoachingTypes(context.TODO(), &facilitySportPB.Empty{})

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilityImageV2MapC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	request := &facilitySportPB.Empty{}

	ctx := util.PrepareGRPCMetaData(c)

	response, err := cl.GetFacilityImageV2Map(ctx, request)

	if err != nil {
		log.Println("Error in getting facility_images_v2 mapping", err)
	}

	statusCode := http.StatusOK

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
	return
}

func StoreFacilityImageV2C(c *gin.Context) {

	cl = util.GetFacilitySportClient()

	var json structs.FacilityImageV2

	json = c.MustGet("jsonData").(structs.FacilityImageV2)

	reqData := &facilitySportPB.FacilityImageV2DataRequest{
		FacilityImageId: json.FacilityImageId,
		FacilityId:      json.FacilityID,
		Priority:        json.Priority,
		ImageId:         json.ImageId,
		DeletedFlag:     json.DeletedFlag,
	}

	ctx := util.PrepareGRPCMetaData(c)

	response, err := cl.StoreFacilityImageV2(ctx, reqData)

	if err != nil {
		log.Println("Error in uploading facility_images_v2 mapping", err)
	}

	statusCode := http.StatusOK

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
	return
}

func StoreFacilitySportImagesMapC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	ctx := util.PrepareGRPCMetaData(c)

	response, err := cl.GetFacilitySportImagesMap(ctx, &facilitySportPB.Empty{})

	if err != nil {
		log.Println("Error in getting Facility Sport Images Data", err)
	}

	statusCode := http.StatusOK

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
	return
}

func GetAcademyCourseCategoriesAndSportsC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	ctx := util.PrepareGRPCMetaData(c)

	response, err := cl.GetAcademyCourseCategoriesAndSports(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Printf("func:GetAcademyCourseCategoriesAndSportsC: Error in getting course categories err: ", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("Error in getting course categories and sports"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
	return
}

func StoreFacilitySportImagesC(c *gin.Context) {

	cl = util.GetFacilitySportClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.FacilitySportImagesData

	json = c.MustGet("jsonData").(structs.FacilitySportImagesData)

	reqData := &facilitySportPB.FacilitySportImagesDataRequest{
		FsiId:        json.FsiId,
		FsId:         json.FsID,
		Priority:     json.Priority,
		FeaturedFlag: json.FeaturedFlag,
		ImageId:      json.ImageId,
		DeletedFlag:  json.DeletedFlag,
	}

	response, err := cl.StoreFacilitySportImages(ctx, reqData)
	if err != nil {
		log.Println("Error in adding/updating Facility Sport Images data", err)
	}

	statusCode := http.StatusOK

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
	return
}

func UpsertFacilityAmenityMappingC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	json := c.MustGet("jsonData").(structs.FacilityAmenitiesMapping)
	var amenities []*facilitySportPB.Amenity
	for _, item := range json.Amenities {
		deletedFlag := 1
		if item.IsAvailable == 1 {
			deletedFlag = 0
		}
		amenity := &facilitySportPB.Amenity{
			AmenityId:   item.AmenityId,
			DeletedFlag: int32(deletedFlag),
			Priority:    item.Priority,
		}
		amenities = append(amenities, amenity)
	}
	reqData := &facilitySportPB.FacilityAmenityMapping{
		FacilityId: json.FacilityId,
		Amenity:    amenities,
	}
	response, err := cl.UpsertFacilityAmenityMapping(c, reqData)
	if err != nil {
		log.Println("Error in adding/updating Facility Amentiy Mapping data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetAmenitiesByFacilityIdC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	json := c.MustGet("jsonData").(structs.FacilityAmenitiesMapping)
	reqData := &facilitySportPB.GetAmenitiesByFacilityIdRequest{
		FacilityId: json.FacilityId,
	}
	response, err := cl.GetAmenitiesByFacilityId(c, reqData)
	if err != nil {
		log.Println("Error in getting Facility Amentiy Mapping data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(err.Error()))
		return
	}
	if response.Status != nil && response.Status.Status == common.FAILED {
		log.Println("Error in getting Facility Amentiy Mapping data:", response.Status.Message)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(response.Status.Message))
		return
	}
	c.JSON(http.StatusOK, response)
}

func UpsertAmenityC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	json := c.MustGet("jsonData").(structs.Amenity)
	reqData := &facilitySportPB.Amenity{
		AmenityName: json.AmenityName,
		Icon:        json.Icon,
		Priority:    json.Priority,
	}
	response, err := cl.UpsertAmenity(c, reqData)
	if err != nil {
		log.Println("Error in adding Amentiy", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetSeasonalPoolMessageForFacilityPage(startDate time.Time, endDate time.Time) string {
	startDate = util.GetLocalDateTime(startDate)
	endDate = util.GetLocalDateTime(endDate)
	res := "Seasonal pools are operational from " + strconv.Itoa(startDate.Day()) + Ordinal(startDate.Day()) + " " + startDate.Month().String() + " to " + strconv.Itoa(endDate.Day()) + Ordinal(endDate.Day()) + " " + endDate.Month().String() + " every year."
	return res
}

func GetSeasonalPoolMessageForPreferredPageList(startDate time.Time, endDate time.Time, season string) string {
	var res string
	startDate = util.GetLocalDateTime(startDate)
	endDate = util.GetLocalDateTime(endDate)
	res = "<semibold-200|Seasonal pools> are operational from " + strconv.Itoa(startDate.Day()) + Ordinal(startDate.Day()) + " " + startDate.Month().String() + " till " + strconv.Itoa(endDate.Day()) + Ordinal(endDate.Day()) + " " + endDate.Month().String() + " every year. You can put your memberships on-hold when pools are not operational."
	return res
}

func GetSeasonalPoolMessageForPreferredPagePopup() string {
	res := "If your membership extends beyond the operational dates of seasonal pool, you can put your memberships on-hold during the non-operational season & it'll resume when the pool re-opens."
	return res
}

func SeasonalMessageForOldApp(citySeason *facilityPB.CitySeasonForPoolResponse) string {
	startDate, _ := ptypes.Timestamp(citySeason.StartDate)
	endDate, _ := ptypes.Timestamp(citySeason.EndDate)
	startDate = util.GetLocalDateTime(startDate)
	endDate = util.GetLocalDateTime(endDate)
	text := ""
	if citySeason.CurrentSeason != common.SWIMMING_OPEN_SEASON && citySeason.CurrentSeason != common.SWIMMING_OPENING_SEASON {
		text = "Seasonal Centers here are operational from " + strconv.Itoa(startDate.Day()) + Ordinal(startDate.Day()) + " " + startDate.Month().String() + " to " + strconv.Itoa(endDate.Day()) + Ordinal(endDate.Day()) + " " + endDate.Month().String() + " every year."
	}
	return text
}

func GetSubzoneSuggestionsC(c *gin.Context) {
	cl = util.GetFacilitySportClient()
	json := c.MustGet("jsonData").(structs.SubzoneSuggestionsReq)
	reqData := &facilitySportPB.SubzoneSuggestionsReq{
		CityId:    json.CityId,
		Latitude:  json.Latitude,
		Longitude: json.Longitude,
	}
	response, err := cl.GetSubzoneSuggestions(c, reqData)
	if err != nil {
		log.Printf("Error in getting subzone suggestions for req: %v, err: %v", reqData, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}
