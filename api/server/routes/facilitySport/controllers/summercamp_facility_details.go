package facilitySportController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	facilityModel "bitbucket.org/jogocoin/go_api/api/models/facility"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type SummercampFacilityPageData struct {
	FacilityDetails            *facilitySportPB.Facility
	NearbyFacilities           *facilitySportPB.AcademyFacilitiesResponse
	PostbackParams             map[string]interface{}
	FacilityNonOperationalDays []int32
	SummercampPageData         *facilitySportPB.SummerCampFacilitiesRes      `json:"-"`
	SportsDetails              *facilitySportPB.SportFacilitiesCountResponse `json:"-"`
}

type SummercampFacilityPageTemplate struct {
	PageRequest       facilityModel.GetFacilityRequest `json:"-"`
	PageData          SummercampFacilityPageData       `json:"-"`
	ProductDetails    *productPB.SummercampSportProductsRes    `json:"-"`
	PageHeader        *facilityModel.PageHeader        `json:"page_header,omitempty"`
	Header            *sushi.HeaderType4SnippetLayout  `json:"header,omitempty"`
	Sections          []*facilityModel.ResultSection   `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout  `json:"footer,omitempty"`
	HasMore           bool                             `json:"has_more"`
	PostbackParams    string                           `json:"postback_params,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem           `json:"clever_tap_tracking,omitempty"`
}

func GetSummercampFacilityDetailsC(c *gin.Context) {
	var f facility

	if err := c.ShouldBindUri(&f); err != nil {
		c.JSON(400, gin.H{"msg": err})
		log.Printf("Api: facility, function: GetSummercampFacilityDetailsC, Error: %v", err)
		return
	}

	var requestData facilityModel.GetFacilityRequest
	requestData = c.MustGet("jsonData").(facilityModel.GetFacilityRequest)
	requestData.FacilityID = f.FacilityID
	if querySportID, ok := c.GetQuery("sport_id"); ok {
		querySportIDInt, err := strconv.Atoi(querySportID)
		if err == nil {
			requestData.SportID = int32(querySportIDInt)
		}
	}

	ctx := util.PrepareGRPCMetaData(c)
	//cityId := util.GetCityIDFromContext(ctx)

	template := SummercampFacilityPageTemplate{
		PageRequest: requestData,
		PageData:    SummercampFacilityPageData{},
	}
	loggedInUserID := util.GetUserIDFromContext(ctx)
	template.SetFacilityPreInfoSection(ctx)
	template.SetFacilityInfoSection(ctx)
	template.SetCitySportsForSummerCamp(ctx)
	template.SetProductBySport(ctx)
	template.SetTimingSection(ctx)
	if util.Contains(common.TestUsers, loggedInUserID)  || true {
		template.SetFacilitySportsTemplate(c)
	} else {
		template.SetExploreSportsSection(ctx)
	}
	template.SetFacilityAmenitiesSection(ctx)
	template.SetCallFacilitySection(ctx)
	template.SetNearbySectionHeading(ctx)
	template.SetNearByCentersSection(ctx)
	template.SetHeaderSection(ctx)
	if true {
		template.SetFooterSection(ctx)
	} else {
		template.SetFooterSectionSummercamp(ctx)
	}
	template.SetPageHeaderSection(ctx)
	template.SetPageTracking(ctx)

	c.JSON(http.StatusOK, template)
}

func (f *SummercampFacilityPageTemplate) SetProductBySport(ctx context.Context) {
	productClient := util.GetProductClient()
	summercampProductResponse, err := productClient.GetFeaturedSummercampProducts(ctx, &productPB.Empty{})
	if err != nil {
		log.Printf("Error in SummercampFacilityPageTemplate error: %v", err)
		return
	}
	f.ProductDetails = summercampProductResponse
}

func (f *SummercampFacilityPageTemplate) SetTimingSection(ctx context.Context) {
	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Centers) == 0 {
		return
	}
	var facility *facilitySportPB.Facility
	for _, center := range nearbyFacilitiesResponse.Centers {
		if center.FacilityId == f.PageRequest.FacilityID {
			facility = center
			break
		}
	}
	if facility == nil || len(facility.Sport) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
		ShouldResize: true,
	}
	items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _,sport := range facility.Sport {
		if len(sport.SummercampTimings) == 0 || (sport.SportId != common.SWIMMING_SPORT_ID && sport.SportId != common.BADMINTON_SPORT_ID) {
			continue
		}
		item := sushi.ImageTextSnippetType30SnippetItem{}

		title, _ := sushi.NewTextSnippet(sport.SportName)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		title.SetFont(font)
		title.SetColor(color)
		item.Title = title

		subTitle, _ := sushi.NewTextSnippet(sport.SummercampTimings)
		font1, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subTitle.SetFont(font1)
		subTitle.SetColor(color)
		item.Subtitle1 = subTitle

		items = append(items, item)
	}
	if len(items) == 0 {
		return
	}

	layoutConfig1 := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	title, _ := sushi.NewTextSnippet("CENTER TIMINGS")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: separator,
	}

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig1,
		SectionHeaderType1: sectionHeaderSnippetData,
		//SnippetConfig:          snippetConfig,
	}
	log.Println(snippetConfig)
	f.AddSection(sectionHeaderSection)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	amenitySnippet := &sushi.ImageTextSnippetType30Snippet{
		BgColor: bgColor,
		Items:   &items,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType30: amenitySnippet,
	}
	f.AddSection(section)

	closedDaysOfFacility := getFacilityNonOperationDays(f.PageData.FacilityDetails.Sports)
	closedText := ""
	if len(closedDaysOfFacility) > 0 {
		dayStrings := make([]string, 0)
		for _, day := range closedDaysOfFacility {
			dayStrings = append(dayStrings, time.Weekday(day).String())
		}
		var titleText string
		if len(dayStrings) == 1 {
			titleText = fmt.Sprintf("Facility is closed on %s", dayStrings[0])
		} else {
			titleText = fmt.Sprintf(
				"Facility is closed on %s and %s",
				strings.Join(dayStrings[0:len(closedDaysOfFacility)-1], ", "),
				dayStrings[len(closedDaysOfFacility)-1],
			)
		}
		closedText = titleText
	} else {
		dayStrings := make([]string, 0)
		for _, sport := range f.PageData.FacilityDetails.Sports {
			if sport.SportId != 3 {
				continue
			}
			for _, day := range sport.NonOperationalDays {
				dayStrings = append(dayStrings, time.Weekday(day).String())
			}
		}
		if len(dayStrings) == 0 {
			return
		}
		var subtitle2Text string
		if len(dayStrings) == 1 {
			subtitle2Text = fmt.Sprintf("Swimming is closed on %s", dayStrings[0])
		} else {
			subtitle2Text = fmt.Sprintf(
				"Swimming is closed on %s and %s",
				strings.Join(dayStrings[0:len(dayStrings)-1], ", "),
				dayStrings[len(dayStrings)-1],
			)
		}
		closedText = subtitle2Text
	}
	titleDay, _ := sushi.NewTextSnippet(closedText)
	colorTitle, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint500)

	titleDay.SetFont(font)
	titleDay.SetColor(colorTitle)
	icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, colorTitle)
	titleDay.SetPrefixIcon(icon)

	sectionHeaderSnippetData1 := &sushi.SectionHeaderType1Snippet{
		Title: titleDay,
	}
	headerSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig1,
		SectionHeaderType1: sectionHeaderSnippetData1,
	}
	f.AddSection(headerSection)
}

func (f *SummercampFacilityPageTemplate) GetFacilityDetails(ctx context.Context) *facilitySportPB.Facility {
	if f.PageData.FacilityDetails != nil {
		return f.PageData.FacilityDetails
	}
	facilityClient := util.GetFacilitySportClient()
	facilityDetailRequest := &facilitySportPB.GetFacilityDetailsRequest{
		FacilityId: f.PageRequest.FacilityID,
		SummercampSportsOnly:	true,
	}
	facilityDetailsResponse, err := facilityClient.GetAcademysFacilityDetails(ctx, facilityDetailRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetAcademysFacilityDetails, Error: %v", err)
		return nil
	}
	if len(facilityDetailsResponse.Sports) == 0 {
		log.Printf("Api: facility, function: GetAcademysFacilityDetails, Error: no sport for facility id %d", f.PageRequest.FacilityID)
	} else if f.PageRequest.SportID == 0 {
		f.PageRequest.SportID = facilityDetailsResponse.Sports[0].SportId
	}

	f.PageData.FacilityDetails = facilityDetailsResponse
	f.PageData.FacilityNonOperationalDays = getFacilityNonOperationDays(facilityDetailsResponse.Sports)
	return f.PageData.FacilityDetails
}

func (f *SummercampFacilityPageTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.SummerCampFacilitiesRes {
	if f.PageData.SummercampPageData != nil {
		return f.PageData.SummercampPageData
	}

	facilityClient := util.GetFacilitySportClient()
	allFacilitiesRequest := &facilitySportPB.SummerCampFacilitiesReq{
		SportId: f.PageRequest.SportID,
	}
	if f.PageRequest.SportID == 0 {
		allFacilitiesRequest.AllSports = true
	}

	nearbyFacilitiesResponse, err := facilityClient.GetSummerCampFacilitiesForSport(ctx, allFacilitiesRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetNearbyFacilities, Error: %v", err)
		return nil
	}

	f.PageData.SummercampPageData = nearbyFacilitiesResponse
	return f.PageData.SummercampPageData
}

func (f *SummercampFacilityPageTemplate) AddSection(section *facilityModel.ResultSection) {
	f.Sections = append(f.Sections, section)
}

func (f *SummercampFacilityPageTemplate) AddFooter(footer *sushi.FooterSnippetType2Layout) {
	f.Footer = footer
}

func (f *SummercampFacilityPageTemplate) AddHeader(header *sushi.HeaderType4SnippetLayout) {
	f.Header = header
}

func (f *SummercampFacilityPageTemplate) SetPageHeader(pageHeader *facilityModel.PageHeader) {
	f.PageHeader = pageHeader
}

func (f *SummercampFacilityPageTemplate) SetFacilityPreInfoSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil || len(response.Attributes) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.TickerSnippetType1,
	}
	items := make([]sushi.TickerSnippetType1SnippetItem, 0)

	for _, attr := range response.Attributes {
		image, _ := sushi.NewImage(attr.Image)
		if attr.Type == attributeMaxSafety {
			image.SetAspectRatio(2.277777)
			image.SetType(sushi.ImageTypeRectangle)
		} else if attr.Type == attributePeopleCount {
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeCircle)
		} else if attr.Type == attributeVaccinatedStaff {
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeRectangle)
		}

		title, _ := sushi.NewTextSnippet(attr.Title)
		item := sushi.TickerSnippetType1SnippetItem{
			LeftImage: image,
			Title:     title,
		}
		items = append(items, item)
	}

	bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	preInfoSnippet := &sushi.TickerSnippetType1Snippet{
		Items:   &items,
		BgColor: bg_color,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		TickerSnippetType1: preInfoSnippet,
	}
	f.AddSection(section)
}

func (f *SummercampFacilityPageTemplate) SetFacilityInfoSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.TextSnippetType8,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	facilitySnippet := sushi.TextSnippetType8Snippet{}
	title, _ := sushi.NewTextSnippet(response.DisplayName)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetNumberOfLines(2)
	title.SetFont(font)
	facilitySnippet.Title = title

	subtitle1, _ := sushi.NewTextSnippet(response.DisplayAddress)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle1.SetFont(font)
	subtitle1.SetColor(color)
	facilitySnippet.SubTitle1 = subtitle1

	facilityNonOperationalDays := f.PageData.FacilityNonOperationalDays
	if len(facilityNonOperationalDays) > 0 {
		dayStrings := make([]string, 0)
		for _, day := range facilityNonOperationalDays {
			dayStrings = append(dayStrings, time.Weekday(day).String())
		}
		var subtitle2Text string
		if len(dayStrings) == 1 {
			subtitle2Text = fmt.Sprintf("Closed on %s", dayStrings[0])
		} else {
			subtitle2Text = fmt.Sprintf(
				"Closed on %s and %s",
				strings.Join(dayStrings[0:len(facilityNonOperationalDays)-1], ", "),
				dayStrings[len(facilityNonOperationalDays)-1],
			)
		}
		subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		colorType := sushi.ColorTypeOrange
		if featuresupport.SupportsNewColor(ctx) {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
		icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
		subtitle2.SetFont(font)
		subtitle2.SetColor(color)
		subtitle2.SetPrefixIcon(icon)
		//facilitySnippet.SubTitle2 = subtitle2
	}

	ratingSnippet := ratingSnippet(ctx, response.Tag, response.Rating)
	ratingSnippet.Size = sushi.RatingSize400
	facilitySnippet.RatingSnippet = &sushi.RatingSnippet{
		Type:  sushi.RatingTypeTagV2,
		TagV2: ratingSnippet,
	}

	// get directions
	clickAction := sushi.GetClickAction()
	openMap := sushi.OpenMap{
		Latitude:  fmt.Sprintf("%f", response.Latitude),
		Longitude: fmt.Sprintf("%f", response.Longitude),
	}
	clickAction.SetOpenMap(&openMap)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["sport_id"] = f.PageRequest.SportID
	tapPayload["source"] = "facility"
	tapEname := &sushi.EnameData{
		Ename: "summercamp_get_directions_button_tap",
	}
	getDrectionEvents := sushi.NewClevertapEvents()
	getDrectionEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, getDrectionEvents)

	directionColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	directionIcon, _ := sushi.NewIcon(sushi.DirectionIcon, directionColor)
	directionButton, _ := sushi.NewButton(sushi.ButtontypeText)
	directionButton.SetText("Get directions")
	directionButton.SetSize(sushi.ButtonSizeMedium)
	directionButton.SetPrefixIcon(directionIcon)
	directionButton.SetColor(directionColor)
	directionButton.SetClickAction(clickAction)
	directionButton.AddClevertapTrackingItem(trackItem)
	facilitySnippet.RightButton = directionButton

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	facilitySnippet.BgColor = bgColor

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:     layoutConfig,
		SnippetConfig:    snippetConfig,
		TextSnippetType8: &facilitySnippet,
	}
	f.AddSection(section)
}

func (f *SummercampFacilityPageTemplate) SetFacilityAmenitiesSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	if len(response.Amenity) == 0 {
		return
	}

	layoutConfig1 := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	title, _ := sushi.NewTextSnippet("AMENITIES")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: separator,
	}

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig1,
		SectionHeaderType1: sectionHeaderSnippetData,
		SnippetConfig:          snippetConfig,
	}
	f.AddSection(sectionHeaderSection)

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
		ShouldResize: true,
	}

	ameneties := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _, val := range response.Amenity {
		item := sushi.ImageTextSnippetType30SnippetItem{}
		image, _ := sushi.NewImage(val.Icon)
		image.SetAspectRatio(float32(1))
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(16)
		image.SetWidth(16)

		title, _ := sushi.NewTextSnippet(val.AmenityName)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		title.SetFont(font)
		title.SetColor(color)

		item.Image = image
		item.Title = title

		ameneties = append(ameneties, item)
	}

	if len(ameneties)-defaultFacilityCollapsedCount > 1 {
		layoutConfig.CollapsedCount = defaultFacilityCollapsedCount
	}

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	amenitySnippet := &sushi.ImageTextSnippetType30Snippet{
		BgColor: bgColor,
		Items:   &ameneties,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType30: amenitySnippet,
	}
	f.AddSection(section)
}

func (f *SummercampFacilityPageTemplate) SetCallFacilitySection(ctx context.Context) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Help us reach you and solve your query.")

	button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	/*clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)*/
	callClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	cityId := util.GetCityIDFromContext(ctx)
	callClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: "",
		URL:   common.CityCallClickAction[cityId],
	})

	button.SetText("Get a call")
	button.SetPrefixIcon(callIcon)
	button.SetClickAction(callClickAction)

	item.BgColor = bgColor
	item.Title = title
	item.RightButton = button
	item.BorderColor = borderColor
	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:               layoutConfig,
		SnippetConfig:              snippetConfig,
		FitsoImageTextSnippetType7: snippet,
	}
	f.AddSection(section)
}

func (f *SummercampFacilityPageTemplate) SetFacilitySportsTemplate(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	facilityDetailsResponse := f.GetFacilityDetails(ctx)
	if facilityDetailsResponse == nil {
		return
	}

	loggedInUserID := util.GetUserIDFromContext(ctx)
	if true {
		isNewColorSupported := featuresupport.SupportsNewColor(ctx)

		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.SectionHeaderType1,
		}

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
		snippetConfig := &sushi.SnippetConfig{
			TopSeparator: topSeparator,
		}

		title, _ := sushi.NewTextSnippet("SPORTS AVAILABLE HERE")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		title.SetFont(font)
		title.SetColor(color)
		title.SetKerning(3)

		sectionImpressionPayload := make(map[string]interface{})
		sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
		sectionImpressionPayload["source"] = "facility"
		sectionImpressionPayload["facility_id"] = f.PageRequest.FacilityID
		sectionImpressionPayload["sport_id"] = f.PageRequest.SportID
		impressionEname := &sushi.EnameData{
			Ename: "summercamp_facility_sports_section_impr",
		}
		sectionEvents := sushi.NewClevertapEvents()
		sectionEvents.SetImpression(impressionEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

		sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
			Title:             title,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		sectionHeaderSection := &facilityModel.ResultSection{
			LayoutConfig:       layoutConfig,
			SectionHeaderType1: sectionHeaderSnippetData,
			SnippetConfig:      snippetConfig,
		}
		f.AddSection(sectionHeaderSection)

		facilityNonOperationalDays := f.PageData.FacilityNonOperationalDays

		for _, sport := range facilityDetailsResponse.Sports {
			if sport.SportId != 3 && sport.SportId != 7 {
				continue
			}
			sportLayoutConfig := &sushi.LayoutConfig{
				SnippetType:  sushi.ImageTextSnippetType33,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
			}
			item := sushi.ImageTextSnippetType33SnippetItem{}
			item.IsSelectable = true
			item.IsSelected = sport.SportId == f.PageRequest.SportID
			leftImage, _ := sushi.NewImage(sport.Icon)
			leftImage.SetAspectRatio(float32(1))
			leftImage.SetHeight(42)
			leftImage.SetType(sushi.ImageTypeCircle)
			sportImageColor, _ := sushi.NewColor(sushi.ColorType(sport.IconBackgroundColor), sushi.ColorTint(sport.IconTint))
			leftImage.SetColor(sportImageColor)
			item.LeftImage = leftImage

			title, _ := sushi.NewTextSnippet(sport.SportName)
			font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
			color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			title.SetFont(font)
			title.SetColor(color)
			item.Title = title

			if sport.SportSubtitle != "" {
				subtitle1, _ := sushi.NewTextSnippet(sport.SportSubtitle)
				font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
				color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
				subtitle1.SetFont(font)
				subtitle1.SetColor(color)
				item.SubTitle1 = subtitle1
			}

			if sport.Tag == common.OPENING_FACILITY_SPORT_TAG {
				colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				titleTag, _ := sushi.NewTextSnippet(common.OPENING_FACILITY_SPORT_TAG)
				titleTag.SetColor(colorTag)

				bgColorTag, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
				if isNewColorSupported {
					bgColorTag, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				}
				tag := &sushi.Tag{
					Title:   titleTag,
					BgColor: bgColorTag,
				}
				item.Tag1 = tag
			}

			if sport.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				titleTag, _ := sushi.NewTextSnippet(common.ONHOLD_FACILITY_SPORT_TAG)
				titleTag.SetColor(colorTag)

				bgColorType := sushi.ColorTypeOrange
				if isNewColorSupported {
					bgColorType = sushi.ALERT_LIGHT_THEME
				}
				bgColorTag, _ := sushi.NewColor(bgColorType, sushi.ColorTint500)
				tag := &sushi.Tag{
					Title:   titleTag,
					BgColor: bgColorTag,
				}
				item.Tag1 = tag
			}

			filteredNonOperationalDays := sharedFunc.FilterIntegerArray(facilityNonOperationalDays, sport.NonOperationalDays)
			if len(filteredNonOperationalDays) > 0 && false {
				dayStrings := make([]string, 0)
				for _, day := range filteredNonOperationalDays {
					dayStrings = append(dayStrings, time.Weekday(day).String())
				}
				var subtitle2Text string
				if len(dayStrings) == 1 {
					subtitle2Text = fmt.Sprintf("Closed on %s", dayStrings[0])
				} else {
					subtitle2Text = fmt.Sprintf(
						"Closed on %s and %s",
						strings.Join(dayStrings[0:len(filteredNonOperationalDays)-1], ", "),
						dayStrings[len(filteredNonOperationalDays)-1],
					)
				}
				subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
				font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				colorType := sushi.ColorTypeOrange
				if isNewColorSupported {
					colorType = sushi.ALERT_LIGHT_THEME
				}
				color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
				icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
				subtitle2.SetFont(font)
				subtitle2.SetColor(color)
				subtitle2.SetPrefixIcon(icon)
				item.SubTitle2 = subtitle2
			}

			tapPayload := make(map[string]interface{})
			tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
			tapPayload["facility_id"] = f.PageRequest.FacilityID
			tapPayload["sport_id"] = sport.SportId
			tapPayload["source"] = "facility"

			footerButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
			footerButton.SetText(fmt.Sprintf("Book a trial class for %s", sport.SportName))
			footerButtonClickAction := sushi.GetClickAction()

			if loggedInUserID == 0 {
				authTitle, _ := sushi.NewTextSnippet("Please login to book your free trial class. Enter your phone number to login using OTP.")
				payload := map[string]interface{}{
					"post_action":         "academy_facility_trial_member_select",
					"sport_id":            sport.SportId,
					"fs_id":               sport.FsId,
					"product_category_id": common.SummerCampCategoryID,
				}
				postbackParams, _ := json.Marshal(payload)
				auth := &sushi.Auth{
					Title:          authTitle,
					PostbackParams: string(postbackParams),
					Source:         "summercamp_facility_page",
				}
				footerButtonClickAction.SetAuth(auth)
			} else {
				footerButtonClickAction = GetSummercampFacilityTrialButtonClickAction(ctx, sport.SportId, sport.FsId)
			}

			footerButton.SetClickAction(footerButtonClickAction)

			tapPayload["membership"] = "TAKE_TRIAL"
			tapEname := &sushi.EnameData{
				Ename: "summercamp_book_trial_button_tap",
			}
			buttonEvents := sushi.NewClevertapEvents()
			buttonEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)
			footerButton.AddClevertapTrackingItem(trackItem)

			changeBottomButtton := &sushi.ChangeBottomButton{
				ButtonId: "book_slot",
				Button:   footerButton,
			}
			changeBottomButttonClickAction := sushi.GetClickAction()
			changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
			item.ClickAction = changeBottomButttonClickAction

			items := make([]sushi.ImageTextSnippetType33SnippetItem, 0)
			items = append(items, item)

			snippet := &sushi.ImageTextSnippetType33Snippet{
				Items: &items,
			}
			section := &facilityModel.ResultSection{
				LayoutConfig:           sportLayoutConfig,
				Id:                     "select_sport",
				ImageTextSnippetType33: snippet,
			}
			f.AddSection(section)
		}
	}
}

func GetSummercampFacilityTrialButtonClickAction(ctx context.Context, sportID int32, fsId int32) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()

	payload := map[string]int32{
		"sport_id":     sportID,
		"fs_id":        fsId,
		"bottom_sheet": 1,
		"product_category_id": 13,
	}
	postbackParams, _ := json.Marshal(payload)

	clickAction.SetDeeplink(&sushi.Deeplink{
		URL:            util.GetSummercampTrialListMemberBottomSheetDeeplink(),
		PostbackParams: string(postbackParams),
	})

	return clickAction
}

func (f *SummercampFacilityPageTemplate) SetCitySportsForSummerCamp(ctx context.Context) {
	facilityClient := util.GetFacilitySportClient()

	response, err := facilityClient.GetSummerCampActiveSportsByCity(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Printf("Api: home, function: SetCitySportsForSummerCamp, Error: %v", err)
		return
	}
	f.PageData.SportsDetails = response
}

func (f *SummercampFacilityPageTemplate) SetExploreSportsSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Explore Sports")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
		SnippetConfig:      snippetConfig,
	}
	f.AddSection(section)

	sportsSelectionLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType33,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	var sportSnippets []sushi.ImageTextSnippetType33SnippetItem
	var sportIds []int32
	for _,sport := range f.PageData.FacilityDetails.Sports {
		sportIds = append(sportIds, sport.SportId)
	}

	for _, data := range f.PageData.SportsDetails.SportFacilitiesCount {
		if data.SportId != 3 && data.SportId != 7 {
			continue
		}
		if !util.Contains(sportIds, data.SportId) {
			continue
		}
		sportImage, _ := sushi.NewImage(data.SportImage)
		sportImageBgColor, _ := sushi.NewColor(sushi.ColorType(data.BackgroundColor), sushi.ColorTint(data.BackgroundColorShade))
		sportImage.SetAspectRatio(1)
		sportImage.SetType(sushi.ImageTypeCircle)
		sportImage.SetHeight(70)
		sportImage.SetWidth(70)
		sportImage.SetColor(sportImageBgColor)

		sportTitle, _ := sushi.NewTextSnippet(data.SportName)
		sportTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		sportTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		sportTitle.SetColor(sportTitleColor)
		sportTitle.SetFont(sportTitleFont)

		substitle1, _ := sushi.NewTextSnippet(common.SummerCampHomeSubtileSport[data.SportId])
		substitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		substitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		substitle1.SetColor(substitle1Color)
		substitle1.SetFont(substitle1Font)

		sportClickAction := sushi.GetClickAction()

		payload := map[string]int32{
			"sport_id":            data.SportId,
			"product_category_id": common.SummerCampCategoryID,
		}
		postbackParams, _ := json.Marshal(payload)

		sportClickAction.SetDeeplink(&sushi.Deeplink{
			URL:            util.GetSummercampDeeplink(data.SportId),
			PostbackParams: string(postbackParams),
		})

		sportItem := sushi.ImageTextSnippetType33SnippetItem{
			LeftImage:     sportImage,
			Title:         sportTitle,
			IsSelected:    false,
			IsSelectable:  true,
			CornerRadius:  16,
			SubTitle1:     substitle1,
			//SubTitle2:     subtitle2,
			ClickAction:   sportClickAction,
			ShowSeparator: true,
		}

		products := f.ProductDetails
		if products != nil && products.SummercampProductsBySport[data.SportId] != nil {
			if len(products.SummercampProductsBySport[data.SportId].Products) > 0 {
				sportProduct := products.SummercampProductsBySport[data.SportId].Products[len(products.SummercampProductsBySport[data.SportId].Products)-1]
				daysInt, _ := util.GetProductDaysOfWeekSummercamp(sportProduct.DaysOfWeek)
				daysIntCount := int32(len(daysInt))
				perSessionPrice := int32(sportProduct.RetailPrice) / (sportProduct.Duration * daysIntCount)

				for _, product := range products.SummercampProductsBySport[data.SportId].Products {
					daysInt, _ = util.GetProductDaysOfWeekSummercamp(product.DaysOfWeek)
					daysIntCount = int32(len(daysInt))

					if perSessionPrice > (int32(product.RetailPrice) / (product.Duration * daysIntCount)) {
						perSessionPrice = int32(product.RetailPrice) / (product.Duration * daysIntCount)
					}
				}
				subtitle2Text := fmt.Sprintf("Starts at ₹%s/session", strconv.Itoa(int(perSessionPrice)))
				subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
				subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
				subtitle2Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
				subtitle2.SetColor(subtitle2Color)
				subtitle2.SetFont(subtitle2Font)
				sportItem.SubTitle2 = subtitle2
			}
		}

		sportSnippets = append(sportSnippets, sportItem)
	}

	sportsSelectionSection := &facilityModel.ResultSection{
		LayoutConfig: sportsSelectionLayoutConfig,
		ImageTextSnippetType33: &sushi.ImageTextSnippetType33Snippet{
			Items: &sportSnippets,
		},
	}
	f.AddSection(sportsSelectionSection)
}

func (f *SummercampFacilityPageTemplate) SetNearbySectionHeading(ctx context.Context) {
	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	facilitiesResponse := f.GetFacilityDetails(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Centers) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("OTHER CENTERS NEAR YOU")

	if f.PageRequest.SportIDFlag != 0 {
		title, _ = sushi.NewTextSnippet(fmt.Sprintf("OTHER %s CENTERS NEAR YOU", strings.ToUpper(facilitiesResponse.Sports[0].SportName)))
	}
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SnippetConfig:      snippetConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	f.AddSection(sectionHeaderSection)
}

func (f *SummercampFacilityPageTemplate) SetNearByCentersSection(ctx context.Context) {

	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Centers) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoFacilityCardType2,
	}
	if len(nearbyFacilitiesResponse.Centers) == 1 {
		layoutConfig.LayoutType = sushi.LayoutTypeGrid
		layoutConfig.SectionCount = 1
	} else {
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	}

	items := make([]sushi.FitsoFacilityCardType2SnippetItem, 0)
	for _, facility := range nearbyFacilitiesResponse.Centers {
		snippet := sushi.FitsoFacilityCardType2SnippetItem{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sport {
			sportNames = append(sportNames, sport.SportName)
		}
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction

		ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}

		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		if len(facility.Attributes) > 0 {
			bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
			for _, val := range facility.Attributes {
				attrImage, _ := sushi.NewImage(val.Image)

				if val.Type == attributeMaxSafety {
					attrImage.SetAspectRatio(2.277777)
					attrImage.SetType(sushi.ImageTypeRectangle)
				} else if val.Type == attributePeopleCount {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeCircle)
				} else if val.Type == attributeVaccinatedStaff {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeRectangle)
				}

				attrTitle, _ := sushi.NewTextSnippet(val.Title)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				attrTitle.SetColor(titleColor)

				bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
					LeftImage: attrImage,
					Title:     attrTitle,
				}
				bottomItems = append(bottomItems, bottomContainerSnippetItem)
			}
			snippet.BottomContainerSnippetItems = &bottomItems
		}
		cityId := util.GetCityIDFromContext(ctx)
		multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
		for _, sport := range facility.Sport {
			if sport.SportId != f.PageRequest.SportID {
				continue
			}
			for _, attribute := range sport.FsInfo {
				if attribute == "Heated Pool" && common.BANGALORE_CITY_ID != cityId && false{
					continue
				}
				tagTitle, _ := sushi.NewTextSnippet(attribute)

				if !featuresupport.SupportsNewColor(ctx) {
					multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
					multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
					gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:    tagTitle,
						Gradient: gradient,
					})
				} else {
					multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:   tagTitle,
						BgColor: multiTagSnippetItemColor,
					})
				}
			}
		}
		if len(multiTagItems) > 0 {
			snippet.TopTags = &multiTagItems
		}

		horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
			Text: facility.SubzoneName,
		}
		horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
			HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
		}
		snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["source_facility_id"] = f.PageRequest.FacilityID
		tapPayload["sport_id"] = f.PageRequest.SportID
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "facility"
		tapEname := &sushi.EnameData{
			Ename: "summercamp_facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, snippet)
	}
	facilitiesSnippet := &sushi.FitsoFacilityCardType2Snippet{
		Items: &items,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		FitsoFacilityCardType2: facilitiesSnippet,
	}
	f.AddSection(section)
}

func (f *SummercampFacilityPageTemplate) SetHeaderSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.HeaderSnippetType4,
	}

	var items []*sushi.HeaderType4SnippetItem

	for _, val := range response.Images {
		image, _ := sushi.NewImage(val)
		image.SetAspectRatio(1.33)
		item := &sushi.HeaderType4SnippetItem{
			Image: image,
		}

		items = append(items, item)
	}
	snippet := &sushi.HeaderType4Snippet{
		Items:       items,
		AspectRatio: 1.33,
	}
	header := &sushi.HeaderType4SnippetLayout{
		LayoutConfig:       layoutConfig,
		HeaderType4Snippet: snippet,
	}
	f.AddHeader(header)
}

func (f *SummercampFacilityPageTemplate) SetFooterSectionSummercamp(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	items := f.GetSummercampFooterCTA(ctx)

	if len(items) > 0 {
		bottomButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}
		snippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: bottomButton,
		}
		f.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig:       layoutConfig,
			FooterSnippetType2: snippet,
		}
	}
}

func (f *SummercampFacilityPageTemplate) GetSummercampFooterCTA(ctx context.Context) []sushi.FooterSnippetType2ButtonItem {
	loggedInUser := util.GetUserIDFromContext(ctx)
	var hasSummerCampSubscription bool
	if loggedInUser > 0 {
		userClient := util.GetUserServiceClient()
		response, err := userClient.GetSummerCampSubscriptionCountByCity(ctx, &userPB.Empty{})

		if err != nil {
			log.Printf("Unable to fetch city based summer camp subscription details for user %d, Error: %v", loggedInUser, err)
		}
		if response.Count > 0 {
			hasSummerCampSubscription = true
		}
	}
	buttonText := "Buy Summer Camp membership"
	if hasSummerCampSubscription {
		buttonText = "Buy another membership"
	}
	items := []sushi.FooterSnippetType2ButtonItem{}
	membershipButton := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Text: buttonText,
	}
	membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	urlDeeplink := util.GetPurchaseMembershipDeeplink(common.SummerCampCategoryID)

	membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
		URL: urlDeeplink,
	})
	membershipButton.ClickAction = membershipButtonClickAction
	products := f.ProductDetails
	if products != nil && products.SummercampProductsBySport[common.BADMINTON_SPORT_ID] != nil && !hasSummerCampSubscription && false {
		if len(products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products) > 0 {
			sportProduct := products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products[len(products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products)-1]
			daysInt, _ := util.GetProductDaysOfWeekSummercamp(sportProduct.DaysOfWeek)
			daysIntCount := int32(len(daysInt))
			perSessionPrice := int32(sportProduct.RetailPrice) / (sportProduct.Duration * daysIntCount)
			for _, product := range products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products {
				daysInt, _ = util.GetProductDaysOfWeekSummercamp(sportProduct.DaysOfWeek)
				daysIntCount = int32(len(daysInt))
				if perSessionPrice > (int32(product.RetailPrice) / (product.Duration * daysIntCount)) {
					perSessionPrice = int32(product.RetailPrice) / (product.Duration * daysIntCount)
				}
			}
			subtitle2Text := fmt.Sprintf("starts @ just ₹%s/session", strconv.Itoa(int(perSessionPrice)))
			membershipButton.Subtext = subtitle2Text
		}
	}
	items = append(items, membershipButton)
	/*trialButton := h.GetSummercampTrialButton(ctx, sportIds)
	if trialButton != nil {
		items = append(items, *trialButton)
	}*/
	return items
}


func (f *SummercampFacilityPageTemplate) SetFooterSection(ctx context.Context) {
	facilityDetails := f.GetFacilityDetails(ctx)
	if facilityDetails == nil {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Type:    sushi.FooterButtonTypeSolid,
		Text:    "Book a trial class for badminton",
		Id:      "book_slot",
		BgColor: bgColor,
	}
	items = append(items, buttonItem1)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	f.AddFooter(footer)
}

func (f *SummercampFacilityPageTemplate) SetPageHeaderSection(ctx context.Context) {
	facilityDetails := f.GetFacilityDetails(ctx)
	if facilityDetails == nil {
		return
	}

	title, _ := sushi.NewTextSnippet(facilityDetails.DisplayName)

	shareClickAction := sushi.GetClickAction()
	share := &sushi.Share{
		Text: fmt.Sprintf("Find this facility on cult | %s", facilityDetails.DisplayAddress),
		URL:  facilityDetails.WebUrl,
	}
	shareClickAction.SetShare(share)

	shareIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	shareIcon, _ := sushi.NewIcon(sushi.ShareIcon, shareIconColor)
	shareIcon.SetClickAction(shareClickAction)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["sport_id"] = f.PageRequest.SportID
	tapPayload["source"] = "facility"
	tapEname := &sushi.EnameData{
		Ename: "summercamp_share_facility_button_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	shareIcon.AddClevertapTrackingItem(trackItem)

	customAction := &sushi.CustomAction{
		Type: sushi.CustomActionTypeIcon,
		Icon: shareIcon,
	}
	customActions := []*sushi.CustomAction{customAction}

	pageHeader := &facilityModel.PageHeader{
		ScrolledUpTitle: title,
		Actions:         customActions,
	}
	f.SetPageHeader(pageHeader)
}

func (f *SummercampFacilityPageTemplate) SetPageTracking(ctx context.Context) {
	backArrowPayload := make(map[string]interface{})
	backArrowPayload["user_id"] = util.GetUserIDFromContext(ctx)
	backArrowPayload["facility_id"] = f.PageRequest.FacilityID
	backArrowPayload["sport_id"] = f.PageRequest.SportID
	backArrowPayload["source"] = "facility"
	backArrowEname := &sushi.EnameData{
		Ename: "summercamp_facility_page_back_arrow_tap",
	}

	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := sushi.GetClevertapTrackItem(ctx, backArrowPayload, backArrowEvents)

	landingPayload := make(map[string]interface{})
	landingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	landingPayload["facility_id"] = f.PageRequest.FacilityID
	landingPayload["sport_id"] = f.PageRequest.SportID
	landingEname := &sushi.EnameData{
		Ename: "summercamp_facility_page_landing",
	}

	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, landingPayload, landingEvents)

	f.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}
}
