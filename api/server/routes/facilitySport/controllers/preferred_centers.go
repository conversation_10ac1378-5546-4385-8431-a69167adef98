package facilitySportController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	centreModel "bitbucket.org/jogocoin/go_api/api/models/common/centres"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilityPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
)

// AvailableCentreTemplate represents the response structure of available centres page in bottom sheet
type AvailableCentreTemplate struct {
	Header                *centreModel.Header                          `json:"header,omitempty"`
	CustomHeader          *centreModel.CustomHeader                    `json:"custom_header,omitempty"`
	ProgressBarData       *centreModel.ProgressBarStruct               `json:"progress_bar_data,omitempty"`
	Results               *[]sushi.CustomTextSnippetTypeLayout         `json:"results,omitempty"`
	Footer                *sushi.FooterSnippetType2Layout              `json:"footer"`
	PostbackParams        string                                       `json:"postback_params,omitempty"`
	HasMore               bool                                         `json:"has_more,omitempty"`
	SubscriptionDetails   *productPB.GetUserSubscriptionStatusResponse `json:"-"`
	SportName             string                                       `json:"-"`
	SportId               int32                                        `json:"-"`
	SportImage            string                                       `json:"-"`
	IsSingleSportCity     bool                                         `json:"-"`
	SportIdToReserve      int32                                        `json:"-"`
	FacilityIdToReserve   int32                                        `json:"-"`
	SoldOutReserveForUser bool                                         `json:"-"`
	ReserveForReqUser     bool                                         `json:"-"`
	RequestUserId         int32                                        `json:"-"`
	IsSingleKeyProduct    bool                                         `json:"-"`
}

func ShowCitySeasonMessage(citySeason *facilityPB.CitySeasonForPoolResponse) bool {
	if citySeason.CurrentSeason == common.SWIMMING_CLOSE_SEASON || citySeason.CurrentSeason == common.SWIMMING_CLOSING_SEASON {
		return true
	}
	return false
}

func GetPreferredCentersC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	cl = util.GetFacilitySportClient()

	var json structs.PreferredCenters
	json = c.MustGet("jsonData").(structs.PreferredCenters)
	log.Printf("GetPreferredCentersC: Error Provider: City id - %d", util.GetCityIDFromContext(ctx))
	showCitySeasonMessage := false
	isFirstCall := false
	seasonalMessageForOldApp := ""
	previousFacilityIds := getPreferredFacilityRequestPostbackParams(json.PostbackParams)
	if len(previousFacilityIds) == 0 { // first call
		var f facility
		previousFacilityIds = append(previousFacilityIds, f.FacilityID)
		citySeason, err := cl.GetCityCurrentSeasonForPool(ctx, &facilityPB.Empty{})
		if err != nil {
			log.Println(err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		isFirstCall = true
		showCitySeasonMessage = ShowCitySeasonMessage(citySeason)
		seasonalMessageForOldApp = SeasonalMessageForOldApp(citySeason)
	}
	reqData := &facilityPB.DistanceFilterBasedFacilityListingRequest{
		PreviousFacilityIds: previousFacilityIds,
		SportId:             json.SportId,
	}

	preferredCentres, err := cl.GetDistanceFilterBasedFacilitiesDetails(ctx, reqData)
	if err != nil {
		log.Println(err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	seasonalPoolVersion := featuresupport.SupportsSeasonalPools(c)
	template := AvailableCentreTemplate{}
	template.IsSingleKeyProduct = json.IsSingleKeyProduct
	requestUserId := json.RequestUserId
	template.ValidateChildUser(ctx, requestUserId)
	template.GetUserSubscriptionStatus(ctx)
	if featuresupport.SupportsMultipleFeaturedProducts(ctx) && json.SportId > 0 {
		template.SportName = common.SportIdSportNameMap[json.SportId]
		template.SportId = json.SportId
		template.IsSingleSportCity, _ = util.GetSportIdIfSingleSportCity(ctx)
		if template.SportId > 0 {
			req := facilityPB.SportRequest{
				SportId:     template.SportId,
				MinimalData: true,
			}
			sportDetails, err := cl.GetSport(ctx, &req)
			if err != nil {
				log.Println("Error getting sport details from PreferredCenter: ", err)
				c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
				return
			}
			template.SportImage = sportDetails.Sport[0].Icon
			template.ValidateSoldOutReserveForUser(ctx)
		}
	}
	template.SetCustomHeaderSection(ctx)
	if seasonalPoolVersion == true {
		template.ResultSectionSeasonalPoolVersion(ctx, preferredCentres, showCitySeasonMessage)
	} else {
		template.ResultSection(ctx, preferredCentres, isFirstCall, seasonalMessageForOldApp)
	}
	template.SetFooterSection()
	template.SetPostbackParams("previous_facility_ids", preferredCentres.PreviousFacilityIds)
	template.SetHasMore(preferredCentres.HasMore)

	c.JSON(http.StatusOK, template)
}

func getPreferredFacilityRequestPostbackParams(postbackParams string) []int32 {
	var temp map[string]interface{}
	json.Unmarshal([]byte(postbackParams), &temp)

	previousFacilityIds := make([]int32, 0)
	if facilityIds, ok := temp["previous_facility_ids"]; ok {
		facilityIdsInterface, _ := facilityIds.([]interface{})
		for _, val := range facilityIdsInterface {
			previousFacilityIds = append(previousFacilityIds, int32(val.(float64)))
		}
	}
	return previousFacilityIds
}

func (s *AvailableCentreTemplate) SetCustomHeaderSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Select preferred center")
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		title_font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	}
	title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(title_font)
	title.SetColor(title_color)

	selectedColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.DismissIcon, selectedColor)
	customHeaderType1Obj := &centreModel.CustomHeaderType1{
		Title: title,
		Icon:  icon,
	}

	if featuresupport.SupportsMultipleFeaturedProducts(ctx) && s.SportName != "" {
		stringToDisp := "Choose your preferred center to get priority booking for <semibold-300|" + s.SportName + ">"
		subtitle, _ := sushi.NewTextSnippet(stringToDisp)
		subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle.SetFont(subtitle_font)
		subtitle.SetColor(subtitle_color)
		subtitle.IsMarkdown = 1
		customHeaderType1Obj.SubTitle = subtitle
	}

	customHeaderDetails := &centreModel.CustomHeader{
		Type:              centreModel.CustomHeaderType1Val,
		CustomHeaderType1: customHeaderType1Obj,
	}
	s.CustomHeader = customHeaderDetails
}

func (s *AvailableCentreTemplate) SetFooterSection() {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItemText string
	buttonItemText = "Select a center to proceed"
	buttonItemFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItemBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:             "solid",
		Text:             buttonItemText,
		Font:             buttonItemFont,
		Id:               "bottom_button_id1",
		BgColor:          buttonItemBgColor,
		IsActionDisabled: 1,
	}
	items = append(items, buttonItem)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func showSeasonalPopup(ctx context.Context, sportId int32) bool {

	if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		return true
	} else if sportId == common.SWIMMING_SPORT_ID {
		return true
	} else {
		return false
	}
}

func (s *AvailableCentreTemplate) reserveSoldOutForUser(facilityId int32) bool {
	if s.SoldOutReserveForUser && s.SportIdToReserve == s.SportId && s.FacilityIdToReserve == facilityId {
		return true
	}
	return false
}

func (s *AvailableCentreTemplate) GetAvailableSportsString(ctx context.Context, sports []*facilityPB.Sport) string {
	sport_str := ""
	firstSportName := ""
	newSportString := make([]string, 0)
	newSportString = append(newSportString, s.SportName)
	if s.SportId > 0 && featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		firstSportName = "<semibold-200|" + s.SportName + ">"
		for _, val := range sports {
			if val.SportId != s.SportId {
				newSportString = append(newSportString, val.SportName)
			} else {
				continue
			}
		}
	} else {
		firstSportName = sports[0].SportName
		for i, val := range sports {
			if i != 0 {
				newSportString = append(newSportString, val.SportName)
			} else {
				continue
			}
		}

	}

	if len(newSportString) > 3 {
		sport_str = firstSportName + ", " + newSportString[1] + ", " + newSportString[2] + " and " + strconv.Itoa(len(sports)-3) + " more"
	} else if len(sports) == 3 {
		sport_str = firstSportName + ", " + newSportString[1] + " and " + newSportString[2]
	} else if len(sports) == 2 {
		sport_str = firstSportName + " and " + newSportString[1]
	} else if len(sports) == 1 {
		sport_str = firstSportName
	}

	return sport_str
}

func (s *AvailableCentreTemplate) ResultSectionSeasonalPoolVersion(ctx context.Context, response *facilityPB.DistanceFilterBasedFacilityListingResponse, showCitySeasonMessage bool) {
	var results []sushi.CustomTextSnippetTypeLayout
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	//this works on first call and sets result if city is in closing or close season
	if showCitySeasonMessage == true && !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		title, _ := sushi.NewTextSnippet("Centers where seasonal swimming pool is the only available sport cannot be selected as a preferred center.")
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetFont(titleFont)
		title.SetColor(titleColor)

		bgCornerRadius := sushi.BgCornerRadius8
		bgCornerType := sushi.BgCornerTypeRounded
		bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)

		itemObj := sushi.V2ImageTextSnippetType35SnippetItem{
			Title:          title,
			BgCornerRadius: bgCornerRadius,
			BgCornerType:   bgCornerType,
			BgColor:        bgColor,
			BorderColor:    borderColor,
		}

		snippet := &sushi.V2ImageTextSnippetType35Snippet{
			Items: &[]sushi.V2ImageTextSnippetType35SnippetItem{itemObj},
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType35,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType35: snippet,
		}
		results = append(results, item)
	}

	subscriptionData := s.GetUserSubscriptionStatus(ctx)
	status := subscriptionData.SubscriptionStatus
	for _, elem := range response.Facilities {
		sports := elem.Sports
		//will not show facilities with 0 sports
		if len(sports) < 1 {
			continue
		}

		// setting facility title
		facility_title, _ := sushi.NewTextSnippet(elem.DisplayName)
		facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		facility_title.SetFont(facility_title_font)
		facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_title.SetColor(facility_title_color)

		// setting facility subtitle
		facility_subtitle, _ := sushi.NewTextSnippet(elem.SubzoneName)
		facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		facility_subtitle.SetFont(facility_subtitle_font)
		facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		facility_subtitle.SetColor(facility_subtitle_color)

		// setting facility tag
		facility_tag_title, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f km", elem.Distance))
		facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		facility_tag_title.SetFont(facility_tag_title_font)
		facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_tag_title.SetColor(facility_tag_title_color)

		// setting facility tag bg color
		facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag := &sushi.Tag{
			Title:   facility_tag_title,
			BgColor: facility_tag_bgcolor,
		}

		// setting facility rating title
		facility_rating_title, _ := sushi.NewTextSnippet(elem.Rating)
		facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		facility_rating_title.SetFont(facility_rating_title_font)
		facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
		facility_rating_title.SetColor(facility_rating_title_color)
		facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
		facility_rating_title.SetPrefixIcon(ratingIcon)

		// setting facility rating bg color
		facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if isNewColorSupported {
			facility_rating_bgcolor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}
		facility_is_inactive := false
		seasonalPoolMessage := ""

		if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && len(sports) == 1 && (elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSING_SEASON) {
			facility_is_inactive = true
		}
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			if util.IsHighPeakSlotFacilitySport(ctx, elem.FsId) {
				// add your condtions for evaluation here
				if featuresupport.SupportsRenewalTileV2(ctx) && !s.reserveSoldOutForUser(elem.FacilityId) && status != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
					facility_is_inactive = true
				} else if !featuresupport.SupportsRenewalTileV2(ctx) && status != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
					facility_is_inactive = true
				}
			}
		} else if util.IsHighPeakSlotFacility(ctx, elem.FacilityId) && status != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE { // add your condtions for evaluation here
			facility_is_inactive = true
		}
		ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}
		tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, elem.Tag) {
			ratingTitle, _ := sushi.NewTextSnippet(elem.Tag)

			if !isNewColorSupported {
				color := sushi.ColorTypeBlue
				if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeTeal
				} else if elem.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeOrange
				}
				ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
				ratingSnippetBlockItem.BgColor = ratingBgColor
			} else {
				if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
					ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else if elem.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint500)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else {
					// gradient for new tag
					tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
					tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
					gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
					ratingSnippetBlockItem.Gradient = gradient
				}
			}

			ratingSnippetBlockItem.Title = ratingTitle
			ratingSnippetBlockItem.Size = sushi.RatingSize300

		} else {
			ratingSnippetBlockItem.Title = facility_rating_title
			if facility_is_inactive {
				ratingSnippetBlockItem.BgColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			} else {
				ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
			}
		}

		rating := ratingSnippetBlockItem

		facility_image := &sushi.Image{
			URL:         elem.DisplayPicture,
			AspectRatio: 1,
			Type:        sushi.ImageTypeRounded,
			Height:      64,
			Width:       64,
		}

		if facility_is_inactive {
			if ratingSnippetBlockItem.BgColor != nil {
				ratingSnippetBlockItem.BgColor.Type = sushi.ColorTypeGrey
			}
			greyFilter, _ := sushi.NewFilter(sushi.FilterTypeGrayScale)
			greyFilter.Data = "0"
			greyImage := &sushi.Image{
				URL:         elem.DisplayPicture,
				AspectRatio: 1,
				Type:        sushi.ImageTypeRounded,
				Height:      64,
				Width:       64,
				Filter:      greyFilter,
			}
			facility_image = greyImage
		}

		sport_str := s.GetAvailableSportsString(ctx, sports)

		fbc_title, _ := sushi.NewTextSnippet(sport_str)
		fbc_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fbc_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			fbc_font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		}
		fbc_title.SetColor(fbc_color)
		fbc_title.SetFont(fbc_font)
		fbc_title.IsMarkdown = 1

		facility_bottom_container := &sushi.BottomContainerSnippetItem{
			Title: fbc_title,
		}
		facility_top_container := &sushi.V2ImageTextSnippetType31TopContainer{}
		facility_top_container_is_set := false

		crossCircleImage := &sushi.Image{
			URL:         util.GetCDNLink("uploads/VectorcrossCircle1634810176.png"),
			AspectRatio: 1,
			Height:      14,
			Width:       14,
		}

		if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true {

			startDate, _ := ptypes.Timestamp(elem.SeasonalPoolInfo.StartDate)
			endDate, _ := ptypes.Timestamp(elem.SeasonalPoolInfo.EndDate)
			seasonalPoolMessage = GetSeasonalPoolMessageForPreferredPageList(startDate, endDate, elem.SeasonalPoolInfo.CurrentSeason)
			fbc_subtitle, _ := sushi.NewTextSnippet(seasonalPoolMessage)
			colorType := sushi.ColorTypeOrange
			fbc_color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
			fbc_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			iconColor, _ := sushi.NewColor(colorType, sushi.ColorTint400)
			icon, _ := sushi.NewIcon(sushi.SwimmingIcon, iconColor)
			fbc_subtitle.SetColor(fbc_color)
			fbc_subtitle.SetFont(fbc_font)
			fbc_subtitle.SetPrefixIcon(icon)
			fbc_subtitle.SetIsMarkdown(1)
			facility_bottom_container_swmming := &sushi.BottomContainerSnippetItem{
				Title:    fbc_title,
				SubTitle: fbc_subtitle,
			}
			facility_bottom_container = facility_bottom_container_swmming

			if featuresupport.SupportsMultipleFeaturedProducts(ctx) && s.SportId == common.SWIMMING_SPORT_ID {
				title, _ := sushi.NewTextSnippet(seasonalPoolMessage)
				color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
				bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
				title.SetColor(color)
				title.SetFont(font)
				title.SetIsMarkdown(1)

				swimmingImage := &sushi.Image{
					URL:         util.GetCDNLink("uploads/swimming_blue1634120225.png"),
					AspectRatio: 1,
					Height:      14,
					Width:       14,
				}

				facility_top_container.Image = swimmingImage
				facility_top_container.Title = title
				facility_top_container.BgColor = bgColor
				facility_top_container.BorderColor = borderColor
				facility_top_container.CornerRadius = 8
				facility_top_container_is_set = true
				if facility_is_inactive {
					facility_top_container.Image = crossCircleImage
					fbc_subtitle.PrefixIcon = nil
					facility_top_container.Title = fbc_subtitle
					colorType := sushi.ColorTypeOrange
					bgColor, _ := sushi.NewColor(colorType, sushi.ColorTint100)
					borderColor, _ := sushi.NewColor(colorType, sushi.ColorTint200)
					facility_top_container.BgColor = bgColor
					facility_top_container.BorderColor = borderColor
				}
			}
		}
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			if util.IsHighPeakSlotFacilitySport(ctx, elem.FsId) && status != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
				if (featuresupport.SupportsRenewalTileV2(ctx) && !s.reserveSoldOutForUser(elem.FacilityId)) || !featuresupport.SupportsRenewalTileV2(ctx) {
					title, _ := sushi.NewTextSnippet("Unlikely to get booking here in peak hours")
					colorType := sushi.ColorTypeOrange
					color, _ := sushi.NewColor(colorType, sushi.ColorTint600)
					font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
					bgColor, _ := sushi.NewColor(colorType, sushi.ColorTint100)
					borderColor, _ := sushi.NewColor(colorType, sushi.ColorTint200)
					title.SetColor(color)
					title.SetFont(font)
					facility_top_container.Image = crossCircleImage
					facility_top_container.Title = title
					facility_top_container.BgColor = bgColor
					facility_top_container.BorderColor = borderColor
					facility_top_container.CornerRadius = 8
					facility_top_container_is_set = true
				}
			}
		} else {
			if util.IsHighPeakSlotFacility(ctx, elem.FacilityId) && status != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE { // or your logic
				fbc_subtitle, _ := sushi.NewTextSnippet("Sold out, unlikely to get booking here in peak hours")
				fbc_color, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint400)
				fbc_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
				color, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint400)
				icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
				fbc_subtitle.SetColor(fbc_color)
				fbc_subtitle.SetFont(fbc_font)
				fbc_subtitle.SetPrefixIcon(icon)
				facility_bottom_container_new := &sushi.BottomContainerSnippetItem{
					Title:    fbc_title,
					SubTitle: fbc_subtitle,
				}
				facility_bottom_container = facility_bottom_container_new
			}
		}

		button_text := "Proceed with selected center"

		bottom_button_click_action := sushi.GetClickAction()
		bottom_button_click_action.SetClickActionType(sushi.ClickActionDismiss)

		dismissPage := &sushi.DismissPage{}
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			dismissPage = s.GetClickActionDataForSelectPreferredCenter(ctx, elem)
			bottom_button_click_action.DismissPage = dismissPage
		}

		button := &sushi.Button{
			Type:              "solid",
			Text:              button_text,
			ClickAction:       bottom_button_click_action,
			ClevertapTracking: []*sushi.ClevertapItem{s.GetPreferredCenterSelectionTrackingItem(ctx, elem)},
		}

		var change_bottom_button *sushi.ChangeBottomButton
		change_bottom_button = &sushi.ChangeBottomButton{
			ButtonId: "bottom_button_id1",
			Button:   button,
		}

		facility_normal_click_action, _ := sushi.NewTextClickAction("change_bottom_button")
		facility_normal_click_action.ChangeBottomButton = change_bottom_button
		facility_click_action := facility_normal_click_action

		if showSeasonalPopup(ctx, s.SportId) && elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true {
			facility_seasonal_click_action, _ := sushi.NewTextClickAction("custom_alert")

			circleImage := &sushi.Image{
				URL:         util.GetCDNLink("uploads/Vector1657632654.png"),
				AspectRatio: 1,
				Height:      40,
				Width:       40,
				Type:        sushi.ImageTypeCircle,
			}

			show_alert_title, _ := sushi.NewTextSnippet("You're selecting a seasonal pool")
			color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
			show_alert_title.SetColor(color)
			show_alert_title.SetFont(font)

			popupMessage := GetSeasonalPoolMessageForPreferredPagePopup()
			show_alert_message, _ := sushi.NewTextSnippet(popupMessage)
			color1, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			font1, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
			show_alert_message.SetColor(color1)
			show_alert_message.SetFont(font1)

			show_alert_positive_action, _ := sushi.NewButton(sushi.ButtontypeText)
			show_alert_positive_action.SetSize(sushi.ButtonSizeLarge)
			show_alert_positive_action.SetText("Confirm")
			show_alert_positive_action.ClickAction = bottom_button_click_action


			alertPayload := make(map[string]interface{})
			alertPayload["user_id"] = util.GetUserIDFromContext(ctx)
			alertPayload["facility_id"] = elem.FacilityId
			alertPayload["source"] = "prefered_center"
			alertEname := &sushi.EnameData{
				Ename: "seasonal_pool_custom_alert_showed",
			}
			alertImpressionEvents := sushi.NewClevertapEvents()
			alertImpressionEvents.SetImpression(alertEname)
			alertTrackItem := sushi.GetClevertapTrackItem(ctx, alertPayload, alertImpressionEvents)

			var custom_alert *sushi.CustomAlert
			custom_alert = &sushi.CustomAlert {
				Image: circleImage,
				Title: show_alert_title,
				Message: show_alert_message,
				PositiveAction: show_alert_positive_action,
				ClevertapTracking: []*sushi.ClevertapItem{alertTrackItem},
				DismissAfterAction: true,
			}
			facility_seasonal_click_action.CustomAlert = custom_alert
			facility_click_action = facility_seasonal_click_action
		}


		facility_obj := sushi.V2ImageTextSnippetType31SnippetItem{
			Id:         elem.FacilityId,
			Title:      facility_title,
			Subtitle:   facility_subtitle,
			Tag:        tag,
			Rating:     rating,
			Image:      facility_image,
			IsInactive: facility_is_inactive,
		}
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			facility_obj.Subtitle2 = fbc_title
			if facility_top_container_is_set {
				facility_obj.TopContainer = facility_top_container
			}
		} else {
			facility_obj.BottomContainer = facility_bottom_container
		}
		if facility_is_inactive == false {
			facility_obj.ClickAction = facility_click_action

			//COMMENTING CLEVERTAPTRACKING TILL APP GIVES A GOAHEAD
			// alertPayload := make(map[string]interface{})
			// alertPayload["user_id"] = util.GetUserIDFromContext(ctx)
			// alertPayload["facility_id"] = elem.FacilityId
			// alertPayload["source"] = "prefered_center"
			// alertEname := &sushi.EnameData{
			// 	Ename: "get_seasonal_pool_custom_alert",
			// }
			// alertImpressionEvents := sushi.NewClevertapEvents()
			// alertImpressionEvents.SetImpression(alertEname)
			// alertTrackItem := sushi.GetClevertapTrackItem(ctx, alertPayload, alertImpressionEvents)
			// facility_obj.ClevertapItem = []*sushi.ClevertapItem{alertTrackItem}

		} else {
			facility_bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
			facility_obj.BgColor = facility_bg_color
		}

		snippet := &sushi.V2ImageTextSnippetType31Snippet{
			Items: &[]sushi.V2ImageTextSnippetType31SnippetItem{facility_obj},
			Id:    "lockdown",
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType31,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType31: snippet,
		}

		results = append(results, *item)
	}

	s.Results = &results
}

func (s *AvailableCentreTemplate) GetClickActionDataForSelectPreferredCenter(ctx context.Context, facility *facilityPB.Facility) *sushi.DismissPage {
	dismissPage := &sushi.DismissPage{
		Type: "dismiss_page",
	}

	title, _ := sushi.NewTextSnippet(facility.DisplayName)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	title.SetColor(color)
	title.SetFont(font)

	subtitle, _ := sushi.NewTextSnippet(facility.SubzoneName)
	stColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	subtitle.SetColor(stColor)
	subtitle.SetFont(font)

	snippetData := &sushi.SnippetData{
		Title:    title,
		SubTitle: subtitle,
	}

	preferredCenter := &sushi.SelectPreferredCenter{
		PreferredCenterId: facility.FacilityId,
	}

	if s.SportId > 0 {
		preferredCenter.PreferredSportId = s.SportId
		leftImage, _ := sushi.NewImage(s.SportImage)
		leftImage.SetHeight(24)
		leftImage.SetWidth(24)

		subtitle1, _ := sushi.NewTextSnippet(s.SportName)
		st1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle1.SetColor(st1Color)
		subtitle1.SetFont(font)

		snippetData.LeftImage = leftImage
		snippetData.SubTitle1 = subtitle1
	}
	preferredCenter.SnippetData = snippetData

	clickAction := &sushi.ClickAction{
		Type:                  "select_preferred_center",
		SelectPreferredCenter: preferredCenter,
	}
	performCallBackClickAction := &sushi.PerformCallbackClickAction{
		PageType:    "plan_details",
		ClickAction: clickAction,
	}
	dismissPage2 := &sushi.DismissPage{
		Type:                       "perform_callback_click_action",
		PerformCallbackClickAction: performCallBackClickAction,
	}
	if s.IsSingleSportCity {
		return dismissPage2
	}
	if s.IsSingleKeyProduct && util.IsIOS(ctx) {
		return dismissPage2
	}
	dismissPage.DismissPage = dismissPage2
	return dismissPage
}

func (s *AvailableCentreTemplate) GetPreferredCenterSelectionTrackingItem(ctx context.Context, elem *facilityPB.Facility) *sushi.ClevertapItem {
	tapPayload := s.GetCommonPayloadForTracking(ctx)
	tapPayload["sport_id"] = s.SportId
	tapPayload["facility_id"] = elem.FacilityId
	tapEname := &sushi.EnameData{
		Ename: "preferred_center_selection",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func (s *AvailableCentreTemplate) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "preferred_center_selection"
	return commonPayload
}

func MakeSubtitleWithSubzoneAndSeasonalMessage(subzone string, elem *facilityPB.FacilitySeasonalPoolInfo) string {
	startDate, _ := ptypes.Timestamp(elem.StartDate)
	endDate, _ := ptypes.Timestamp(elem.EndDate)
	seasonalPoolMessage := GetSeasonalPoolMessageForPreferredPageList(startDate, endDate, elem.CurrentSeason)
	res := subzone + " \n{orange-400|<medium-100|" + seasonalPoolMessage + ">}"
	return res
}

func (s *AvailableCentreTemplate) ValidateChildUser(ctx context.Context, requestUserId int32) {
	s.RequestUserId = requestUserId
	s.ReserveForReqUser = false
	loggedInUserId := util.GetUserIDFromContext(ctx)

	if requestUserId > 0 && requestUserId != loggedInUserId {
		ucl := util.GetUserServiceClient()
		req := &userPB.UserChildMappingRequest{
			UserId:      loggedInUserId,
			ChildUserId: requestUserId,
		}
		res, err := ucl.CheckUserIsChildUser(ctx, req)
		if err != nil {
			log.Printf("Error in validating child:%d and parent:%d mapping, error:%s", requestUserId, loggedInUserId, err)
			s.ReserveForReqUser = false
			return
		}
		if res.UserId == loggedInUserId && res.ChildUserId == requestUserId {
			s.ReserveForReqUser = true
			return
		} else {
			log.Printf("Child parent mapping does not exist for child:%d and parent:%d mapping, error:%s", requestUserId, loggedInUserId, err)
		}
	}
}

func (s *AvailableCentreTemplate) ValidateSoldOutReserveForUser(ctx context.Context) {
	if s.RequestUserId == 0 && featuresupport.SupportsSoldOutReserveForChild(ctx) {
		s.SoldOutReserveForUser = false
		return
	}
	if s.SubscriptionDetails.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED {
		today := time.Now()
		y, m, d := today.Date()
		location, _ := time.LoadLocation("Asia/Kolkata")
		today = time.Date(y, m, d, int(0), int(0), int(0), int(0), location)

		endDate := util.GetLocalDateTimeFromProtoTime(s.SubscriptionDetails.LatestSubEndDate) // will never be nil if status = ALL_SUBSCRIPTION_EXPIRED
		y, m, d = endDate.Date()
		endDate = time.Date(y, m, d, int(0), int(0), int(0), int(0), location)

		daysFromSubEnd := today.Sub(endDate).Hours() / 24
		reservedTill := endDate.AddDate(0, 0, common.DAYS_TO_RESERVE_SOLDOUT_FACILITY_FOR_EXPIRED_USER)

		if daysFromSubEnd > 0 && daysFromSubEnd <= common.DAYS_TO_RESERVE_SOLDOUT_FACILITY_FOR_EXPIRED_USER && reservedTill.After(today) {
			if s.SubscriptionDetails.PreferredSportId > 0 && s.SubscriptionDetails.PreferredFacilityId > 0 {
				s.SportIdToReserve = s.SubscriptionDetails.PreferredSportId
				s.FacilityIdToReserve = s.SubscriptionDetails.PreferredFacilityId
				s.SoldOutReserveForUser = true
			} else {
				s.SoldOutReserveForUser = false
			}
		} else {
			s.SoldOutReserveForUser = false
		}
	} else {
		s.SoldOutReserveForUser = false
	}
}

func (s *AvailableCentreTemplate) ResultSection(ctx context.Context, response *facilityPB.DistanceFilterBasedFacilityListingResponse, isFirstCall bool, topMessage string) {
	var results []sushi.CustomTextSnippetTypeLayout
	if isFirstCall && topMessage != "" {
		title, _ := sushi.NewTextSnippet(topMessage)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetFont(titleFont)
		title.SetColor(titleColor)

		bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)

		itemObj := sushi.V2ImageTextSnippetType35SnippetItem{
			Title:       title,
			BgColor:     bgColor,
			BorderColor: borderColor,
		}

		snippet := &sushi.V2ImageTextSnippetType35Snippet{
			Items: &[]sushi.V2ImageTextSnippetType35SnippetItem{itemObj},
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType35,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType35: snippet,
		}
		results = append(results, item)
	}
	subscriptionData := s.GetUserSubscriptionStatus(ctx)
	status := subscriptionData.SubscriptionStatus
	for _, elem := range response.Facilities {

		// Removing naraina and gulmohar pool from here till we make seasonal update live
		// if elem.FacilityId == 46 || elem.FacilityId == 45 {
		// 	continue
		// }
		// setting facility title

		sports := elem.Sports

		if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && len(sports) == 1 && (elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSING_SEASON) {
			continue
		}

		if util.IsHighPeakSlotFacility(ctx, elem.FacilityId) && status != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
			continue
		}

		facility_title, _ := sushi.NewTextSnippet(elem.DisplayName)
		facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		facility_title.SetFont(facility_title_font)
		facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_title.SetColor(facility_title_color)

		// setting facility subtitle
		subtitle := ""
		if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true {
			subtitle = MakeSubtitleWithSubzoneAndSeasonalMessage(elem.SubzoneName, elem.SeasonalPoolInfo)
		} else {
			subtitle = elem.SubzoneName
		}
		facility_subtitle, _ := sushi.NewTextSnippet(subtitle)
		facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
		facility_subtitle.SetFont(facility_subtitle_font)
		facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		facility_subtitle.SetColor(facility_subtitle_color)
		if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true {
			facility_subtitle.IsMarkdown = 1
		}

		// setting facility tag
		facility_tag_title, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f km", elem.Distance))
		facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		facility_tag_title.SetFont(facility_tag_title_font)
		facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_tag_title.SetColor(facility_tag_title_color)

		// setting facility tag bg color
		facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag := &sushi.Tag{
			Title:   facility_tag_title,
			BgColor: facility_tag_bgcolor,
		}

		// setting facility rating title
		facility_rating_title, _ := sushi.NewTextSnippet(elem.Rating)
		facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		facility_rating_title.SetFont(facility_rating_title_font)
		facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
		facility_rating_title.SetColor(facility_rating_title_color)
		facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
		facility_rating_title.SetPrefixIcon(ratingIcon)

		// setting facility rating bg color
		facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)

		ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}
		tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, elem.Tag) {
			ratingTitle, _ := sushi.NewTextSnippet(elem.Tag)
			color := sushi.ColorTypeBlue
			if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeTeal
			} else if elem.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeOrange
			}
			ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)

			ratingSnippetBlockItem.Title = ratingTitle
			ratingSnippetBlockItem.BgColor = ratingBgColor
			ratingSnippetBlockItem.Size = sushi.RatingSize300

		} else {
			ratingSnippetBlockItem.Title = facility_rating_title
			ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
		}

		rating := ratingSnippetBlockItem

		facility_image := &sushi.Image{
			URL:         elem.DisplayPicture,
			AspectRatio: 1,
			Type:        sushi.ImageTypeRounded,
			Height:      64,
			Width:       64,
		}

		button_text := "Proceed with selected center"

		bottom_button_click_action := sushi.GetClickAction()
		bottom_button_click_action.SetClickActionType(sushi.ClickActionDismiss)

		button := &sushi.Button{
			Type:        "solid",
			Text:        button_text,
			ClickAction: bottom_button_click_action,
		}

		var change_bottom_button *sushi.ChangeBottomButton
		change_bottom_button = &sushi.ChangeBottomButton{
			ButtonId: "bottom_button_id1",
			Button:   button,
		}

		facility_click_action, _ := sushi.NewTextClickAction("change_bottom_button")
		facility_click_action.ChangeBottomButton = change_bottom_button

		facility_obj := sushi.V2ImageTextSnippetType31SnippetItem{
			Id:          elem.FacilityId,
			Title:       facility_title,
			Subtitle:    facility_subtitle,
			Tag:         tag,
			Rating:      rating,
			Image:       facility_image,
			ClickAction: facility_click_action,
		}

		snippet := &sushi.V2ImageTextSnippetType31Snippet{
			Items: &[]sushi.V2ImageTextSnippetType31SnippetItem{facility_obj},
			Id:    "lockdown",
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType31,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType31: snippet,
		}

		results = append(results, *item)
	}

	s.Results = &results
}

func (s *AvailableCentreTemplate) GetUserSubscriptionStatus(ctx context.Context) *productPB.GetUserSubscriptionStatusResponse {
	if s.SubscriptionDetails != nil {
		return s.SubscriptionDetails
	}
	productClient := util.GetProductClient()
	footerDetailsRequest := &productPB.GetUserSubscriptionStatusRequest{
		UserId: util.GetUserIDFromContext(ctx),
	}
	if featuresupport.SupportsSoldOutReserveForChild(ctx) && s.ReserveForReqUser {
		footerDetailsRequest.UserId = s.RequestUserId
	}
	footerDetailsResponse, err := productClient.GetUserSubscriptionStatus(ctx, footerDetailsRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetUserSubscriptionStatus, Error: %v", err)
		return nil
	}
	s.SubscriptionDetails = footerDetailsResponse
	return s.SubscriptionDetails
}

func (s *AvailableCentreTemplate) SetPostbackParams(key string, val interface{}) {
	payload := map[string]interface{}{
		key:               val,
		"request_user_id": s.RequestUserId,
	}

	postback_params, err := json.Marshal(payload)
	if err != nil {
		log.Println("Func: SetPostbackParams")
		log.Println("error in marshalling postback_params", err)
	}
	s.PostbackParams = string(postback_params)
}

func (s *AvailableCentreTemplate) SetHasMore(hasMore bool) {
	s.HasMore = hasMore
}
