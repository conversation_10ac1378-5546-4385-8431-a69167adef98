package facilitySportController

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"

	"golang.org/x/text/language"
	"golang.org/x/text/message"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models"
	academyModels "bitbucket.org/jogocoin/go_api/api/models/facility"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type AcademyCourseCategoryPageTemplate struct {
	CourseCategoryId  int32                                                 `json:"-"`
	Response          *facilitySportPB.GetAcademyCourseCategoryPageResponse `json:"-"`
	Results           []*academyModels.CourseCategoryPageResult             `json:"results,omitempty"`
	Tabs              []*academyModels.AcademyTab                           `json:"tabs,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout                       `json:"footer,omitempty"`
	FacilityCarousel  []*sushi.CustomTextSnippetTypeLayout                  `json:"facility_carousel,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                                `json:"clever_tap_tracking,omitempty"`
	SelectedTabType   string                                                `json:"selected_tab_type,omitempty"`
}

func GetAcademyCourseCategoryPageC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetAcademyCourseCategoryPageRequest)
	courseCategoryID := json.CourseCategoryID

	if courseCategoryID <= 0 {
		log.Printf("Error in GetAcademyCourseCategoryPageC - invalid course_category_id: %d", courseCategoryID)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	facilityClient := util.GetFacilitySportClient()
	academyCourseCategoryPage, err := facilityClient.GetAcademyCourseCategoryPage(ctx, &facilitySportPB.GetAcademyCourseCategoryPageRequest{
		CourseCategoryId: courseCategoryID,
	})

	if err != nil {
		log.Printf("Error in GetAcademyCourseCategoryPageC - server error for course_category_id: %d, error: %v", courseCategoryID, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.INTERNAL_SERVER_ERROR))
		return
	}

	academyCourseCategoryPageTemplate := &AcademyCourseCategoryPageTemplate{
		CourseCategoryId: courseCategoryID,
		Response:         academyCourseCategoryPage,
		SelectedTabType:  json.SelectedTabType,
	}

	if academyCourseCategoryPage.CourseCategory.CourseCategoryId > 0 {
		academyCourseCategoryPageTemplate.setBannerImage(ctx)
		academyCourseCategoryPageTemplate.setTabHeading(ctx)
		academyCourseCategoryPageTemplate.setSkillsTab(ctx)
		academyCourseCategoryPageTemplate.setPlanTab(ctx)
		academyCourseCategoryPageTemplate.setFooter(ctx)
		academyCourseCategoryPageTemplate.setPageTracking(ctx)

		if academyCourseCategoryPage.ShowNearbyFacilities {
			academyCourseCategoryPageTemplate.setFacilityCarousel(ctx)
		}
	} else {
		log.Printf("Error in GetAcademyCourseCategoryPageC - couldn't get data for course_category_id: %d", courseCategoryID)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	c.JSON(http.StatusOK, academyCourseCategoryPageTemplate)
}

func (template *AcademyCourseCategoryPageTemplate) setPlanTab(ctx context.Context) {
	productClient := util.GetProductClient()

	plansReq := &productPB.GetPreferredPlanRequest{
		CourseCategoryId: template.CourseCategoryId,
	}
	plansRes, err := productClient.GetAcademyProductsForCourseCategory(ctx, plansReq)

	if err != nil {
		return
	}
	if plansRes.Status == nil || plansRes.Status.Status != apiCommon.SUCCESS {
		return
	}
	if len(plansRes.Products) == 0 {
		return
	}

	isNoCostEMISupported := featuresupport.SupportsNoCostEMI(ctx)
	noCostEMIBenifit := getNoCostEMIBenifit(template.Response.Benefits)

	tabTitle, _ := sushi.NewTextSnippet("PLANS")
	tabTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize600)
	tabTitle.SetFont(tabTitleFont)

	var tabSections []*academyModels.AcademyTabSectionItem

	sectionHeaderLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	tabSection := &academyModels.AcademyTabSectionItem{}

	sectionHeaderTitle, _ := sushi.NewTextSnippet("PLANS & PRICING")
	sectionHeaderTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	sectionHeaderTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	sectionHeaderTitle.SetFont(sectionHeaderTitleFont)
	sectionHeaderTitle.SetColor(sectionHeaderTitleColor)
	sectionHeaderTitle.SetKerning(3)

	tabSection.SectionHeader = &academyModels.AcademyTabSectionHeader{
		LayoutConfig: sectionHeaderLayout,
		SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
			Title: sectionHeaderTitle,
		},
	}

	var planItems []*sushi.CustomTextSnippetTypeLayout
	dowWiseProductsMap := make(map[int][]*productPB.AcademyProductDetails)
	for _, product := range plansRes.Products {
		dowWiseProductsMap[len(product.ProductCourseMappings[0].Days)] = append(dowWiseProductsMap[len(product.ProductCourseMappings[0].Days)], product)
	}
	for _, products := range dowWiseProductsMap {
		product := products[0]

		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.FitsoTextSnippetType8,
		}

		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

		daysCount := 0
		for _, mapping := range product.ProductCourseMappings {
			daysCount = daysCount + len(mapping.Days)
		}

		planTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d days/week", daysCount))
		planTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		planTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
		planTitle.SetFont(planTitleFont)
		planTitle.SetColor(planTitleColor)

		durationUnit := "session"
		if template.CourseCategoryId == 3 {
			durationUnit = "day"
		}

		var sessionDuration string
		if product.SessionDuration%60 == 0 {
			sessionDuration = fmt.Sprintf("%d hour/%s", product.SessionDuration/60, durationUnit)
		} else {
			sessionDuration = fmt.Sprintf("%.1f hour/%s", float32(product.SessionDuration)/60, durationUnit)
		}
		subtitle1, _ := sushi.NewTextSnippet(sessionDuration)
		subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		subtitle1.SetFont(subtitle1Font)
		subtitle1.SetColor(subtitle1Color)

		printer := message.NewPrinter(language.Hindi)

		rightTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("<regular-600|{grey-600|~~₹%s~~}> ₹%s", printer.Sprintf("%d", int32(product.Price)), printer.Sprintf("%d", int32(product.RetailPrice))))
		rightTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
		rightTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
		rightTitle.SetFont(rightTitleFont)
		rightTitle.SetColor(rightTitleColor)
		rightTitle.SetIsMarkdown(1)

		rightSubtitle, _ := sushi.NewTextSnippet(product.Duration)
		rightSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		rightSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		rightSubtitle.SetFont(rightSubtitleFont)
		rightSubtitle.SetColor(rightSubtitleColor)

		var subtitle2 *sushi.TextSnippet
		var subtitle2TextArray []string

		for _, product := range products {
			if len(product.ProductCourseMappings) > 1 {
				continue
			}
			productCourseData := product.ProductCourseMappings[0]
			slotIdsMap := make(map[string]int8)
			var slotsArray []string
			for _, s := range productCourseData.Slots {
				key := fmt.Sprintf("%d_%d", s.SlotId1, s.SlotId2)
				if _, ok := slotIdsMap[key]; !ok {
					if s.SlotId2 > 0 {
						slotsArray = append(slotsArray, fmt.Sprintf("%s, %s", s.SlotTiming1, s.SlotTiming2))
					} else {
						slotsArray = append(slotsArray, s.SlotTiming1)
					}
					slotIdsMap[key] = 1
				}
			}
			subtitle2TextArray = append(subtitle2TextArray, fmt.Sprintf("%s | {grey-600|<medium-100|%s>}", productCourseData.DaysText, strings.Join(slotsArray, " & ")))
		}

		if !isNoCostEMISupported {
			subtitle2TextArray = append(subtitle2TextArray, "\n{grey-900|<medium-100|No cost EMI available>}")
		}

		if len(subtitle2TextArray) > 0 {
			subtitle2, _ = sushi.NewTextSnippet(strings.Join(subtitle2TextArray, "\n"))
			subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			subtitle2.SetFont(subtitle2Font)
			subtitle2.SetColor(subtitle2Color)
			subtitle2.SetIsMarkdown(1)
		}

		var tag1 *sushi.Tag

		if template.CourseCategoryId == 1 {
			tagTitle, _ := sushi.NewTextSnippet("_Limited time deal_ ")
			colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			tagIcon, _ := sushi.NewIcon(sushi.DiscountIcon, colorWhite500)
			tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
			tagTitle.SetColor(colorWhite500)
			tagTitle.SetPrefixIcon(tagIcon)
			tagTitle.SetFont(tagFont)
			tagTitle.SetIsMarkdown(1)

			colorYellow500, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
			colorYellow700, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint700)
			gradColors := []sushi.Color{*colorYellow700, *colorYellow500, *colorYellow500, *colorYellow700}
			tagGradient, _ := sushi.NewGradient(gradColors)
			tag1 = &sushi.Tag{
				Title:    tagTitle,
				Gradient: tagGradient,
			}
		}

		planSnippet := &sushi.FitsoTextSnippetType8Snippet{
			BorderColor:   borderColor,
			BgColor:       bgColor,
			Title:         planTitle,
			Subtitle1:     subtitle1,
			Subtitle2:     subtitle2,
			RightTitle:    rightTitle,
			RightSubtitle: rightSubtitle,
			Tag1:          tag1,
		}

		if isNoCostEMISupported && noCostEMIBenifit != nil {

			bottomInfoTitle, _ := sushi.NewTextSnippet(noCostEMIBenifit.Title)
			bottomInfoTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			bottomInfoTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			suffixColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			icon, _ := sushi.NewIcon("e805", suffixColor)

			bottomInfoTitle.SetColor(bottomInfoTitleColor)
			bottomInfoTitle.SetFont(bottomInfoTitleFont)
			bottomInfoTitle.SetIsMarkdown(1)
			bottomInfoTitle.SetSuffixIcon(icon)

			clickAction := sushi.GetClickAction()

			titleSnippet, _ := sushi.NewTextSnippet(noCostEMIBenifit.Description)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			titleSnippet.SetColor(titleColor)
			titleSnippet.SetFont(titleFont)

			subtitleSnippet, _ := sushi.NewTextSnippet("OK")
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitleSnippet.SetColor(subtitleColor)
			subtitleSnippet.SetFont(subtitleFont)

			bgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			showToolTip := &sushi.ShowTooltip{
				Title:    titleSnippet,
				Subtitle: subtitleSnippet,
				BgColor:  bgColor,
			}

			clickAction.SetClickActionType(sushi.ClickActionShowToolTip)
			clickAction.SetShowTooltip(showToolTip)

			planSnippet.BottomInfo = &sushi.Tag{
				Title:       bottomInfoTitle,
				ClickAction: clickAction,
			}
		}

		planItem := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:          layoutConfig,
			FitsoTextSnippetType8: planSnippet,
		}
		planItems = append(planItems, planItem)
	}

	disclaimerLayoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("You can purchase the membership at the center after your assessment")
	colorBlue600, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint600)
	fontReg300, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	title.SetColor(colorBlue600)
	title.SetFont(fontReg300)

	disclaimerItem := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig: disclaimerLayoutConfig,
		SectionHeaderType1Snippet: &sushi.SectionHeaderType1Snippet{
			Title: title,
		},
	}
	planItems = append(planItems, disclaimerItem)

	tabSection.Items = planItems
	tabSections = append(tabSections, tabSection)

	trackPayload := make(map[string]interface{})
	trackPayload["tab_type"] = "plans"
	tapEname := &sushi.EnameData{
		Ename: "course_category_tab_tap",
	}
	trackEvents := sushi.NewClevertapEvents()
	trackEvents.SetTap(tapEname)
	trackItem := template.GetClevertapTrackItem(ctx, trackPayload, trackEvents)

	planTab := &academyModels.AcademyTab{
		Id:                1,
		Title:             tabTitle,
		Type:              "TAB_TYPE_KIDS_SPORTS_PLANS",
		Sections:          tabSections,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	template.Tabs = append(template.Tabs, planTab)
}

func (template *AcademyCourseCategoryPageTemplate) setSkillsTab(ctx context.Context) {
	tabTitle, _ := sushi.NewTextSnippet("SKILLS")
	tabTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize600)
	tabTitle.SetFont(tabTitleFont)

	var tabSections []*academyModels.AcademyTabSectionItem

	for _, section := range template.Response.CourseCategory.LevelsSections {
		tabSection := &academyModels.AcademyTabSectionItem{}

		if len(template.Response.CourseCategory.LevelsSections) > 1 {
			sectionHeaderLayout := &sushi.LayoutConfig{
				SnippetType: sushi.SectionHeaderType1,
			}

			sectionHeaderTitle, _ := sushi.NewTextSnippet(section.SectionName)
			sectionHeaderTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
			sectionHeaderTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			sectionHeaderTitle.SetFont(sectionHeaderTitleFont)
			sectionHeaderTitle.SetColor(sectionHeaderTitleColor)
			sectionHeaderTitle.SetKerning(3)

			tabSection.SectionHeader = &academyModels.AcademyTabSectionHeader{
				LayoutConfig: sectionHeaderLayout,
				SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
					Title: sectionHeaderTitle,
				},
			}
		}

		var sectionLevelItems []*sushi.CustomTextSnippetTypeLayout

		for _, level := range section.CourseCategoryLevels {
			layoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.FitsoTextSnippetType7,
			}

			levelTitle, _ := sushi.NewTextSnippet(level.Name)
			levelTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			levelTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
			levelTitle.SetFont(levelTitleFont)
			levelTitle.SetColor(levelTitleColor)

			levelSnippet := &sushi.FitsoTextSnippetType7Snippet{
				Title: levelTitle,
			}

			var subTitlesList []*sushi.FitsoTextSnippetType7SnippetSubtitleItem

			for _, skill := range level.Skills {
				icon, _ := sushi.NewIcon(sushi.PointIcon, &sushi.Color{
					Tint: sushi.ColorTint700,
					Type: sushi.ColorTypeGrey,
				})
				skillTitle, _ := sushi.NewTextSnippet(skill)
				skillTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
				skillTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
				skillTitle.SetFont(skillTitleFont)
				skillTitle.SetColor(skillTitleColor)

				skillTitleSnippet := &sushi.FitsoTextSnippetType7SnippetSubtitleItem{
					Title: skillTitle,
					Icon:  icon,
				}

				subTitlesList = append(subTitlesList, skillTitleSnippet)
			}

			levelSnippet.SubtitleList = subTitlesList

			sectionLevelItem := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:          layoutConfig,
				FitsoTextSnippetType7: levelSnippet,
			}

			sectionLevelItems = append(sectionLevelItems, sectionLevelItem)
		}

		tabSection.Items = sectionLevelItems

		tabSections = append(tabSections, tabSection)
	}

	trackPayload := make(map[string]interface{})
	trackPayload["tab_type"] = "skills"
	tapEname := &sushi.EnameData{
		Ename: "course_category_tab_tap",
	}
	trackEvents := sushi.NewClevertapEvents()
	trackEvents.SetTap(tapEname)
	trackItem := template.GetClevertapTrackItem(ctx, trackPayload, trackEvents)

	skillTab := &academyModels.AcademyTab{
		Id:                2,
		Title:             tabTitle,
		Type:              "TAB_TYPE_KIDS_SPORTS_SKILL",
		Sections:          tabSections,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	template.Tabs = append(template.Tabs, skillTab)
}

func (template *AcademyCourseCategoryPageTemplate) setTabHeading(ctx context.Context) {
	headingLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	headingTitle, _ := sushi.NewTextSnippet("CURRICULUM & LESSONS")
	headingTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	headingTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	headingTitle.SetFont(headingTitleFont)
	headingTitle.SetColor(headingTitleColor)
	headingTitle.SetKerning(3)

	template.Results = append(template.Results, &academyModels.CourseCategoryPageResult{
		LayoutConfig: headingLayout,
		SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
			Title: headingTitle,
		},
	})

	descriptionLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	descriptionTitle, _ := sushi.NewTextSnippet(template.Response.CourseCategory.Description)
	descriptionTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	descriptionTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	descriptionTitle.SetFont(descriptionTitleFont)
	descriptionTitle.SetColor(descriptionTitleColor)

	template.Results = append(template.Results, &academyModels.CourseCategoryPageResult{
		LayoutConfig: descriptionLayout,
		SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
			Title: descriptionTitle,
		},
	})
}

func (template *AcademyCourseCategoryPageTemplate) setBannerImage(ctx context.Context) {
	bannerImageLayoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoImageTextSnippetType11,
		LayoutType:  sushi.LayoutTypeGrid,
	}

	title, _ := sushi.NewTextSnippet(template.Response.CourseCategory.Name)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	subtitle, _ := sushi.NewTextSnippet(template.Response.CourseCategory.Levels)
	subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle.SetFont(subtitleFont)
	subtitle.SetColor(subtitleColor)

	image, _ := sushi.NewImage(template.Response.CourseCategory.Image)

	gradient, _ := sushi.NewGradient([]sushi.Color{
		sushi.Color{Type: sushi.ColorTypeGrey, Tint: sushi.ColorTint200},
		sushi.Color{Type: sushi.ColorTypeGrey, Tint: sushi.ColorTint50},
		sushi.Color{Type: sushi.ColorTypeGrey, Tint: sushi.ColorTint50},
		sushi.Color{Type: sushi.ColorTypeGrey, Tint: sushi.ColorTint200},
	})

	imageTextSnippetItem := &sushi.FitsoImageTextSnippetType11SnippetItem{
		Title:    title,
		Image:    image,
		Gradient: gradient,
	}

	template.Results = append(template.Results, &academyModels.CourseCategoryPageResult{
		LayoutConfig: bannerImageLayoutConfig,
		FitsoImageTextSnippetType11: &sushi.FitsoImageTextSnippetType11Snippet{
			Items: []*sushi.FitsoImageTextSnippetType11SnippetItem{imageTextSnippetItem},
		},
	})
}

func (template *AcademyCourseCategoryPageTemplate) setFooter(ctx context.Context) {
	items := homeC.GetAcademyFooterCTA(ctx, "academy_course_trial_member_select", template.Response.CourseSportIds, template.Response.CourseCategory.CourseId)
	if len(items) > 0 {
		bottomButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}

		template.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig: &sushi.LayoutConfig{
				SnippetType: sushi.FooterSnippetType2,
			},
			FooterSnippetType2: &sushi.FooterSnippetType2Snippet{
				ButtonData: bottomButton,
			},
		}
	}
}

func (template *AcademyCourseCategoryPageTemplate) setFacilityCarousel(ctx context.Context) {
	req := &facilitySportPB.AcademyFacilitiesRequest{
		Count:    apiCommon.ACADEMY_COURSE_PAGE_FACILITY_COUNT,
		CourseId: template.Response.CourseCategory.CourseId,
	}
	facilityClient := util.GetFacilitySportClient()
	res, err := facilityClient.GetAcademyFacilities(ctx, req)
	if err != nil {
		log.Printf("nearby academy facilities | course category page | Error: %v", err)
		return
	}
	if res.Status != nil && res.Status.Status == apiCommon.FAILED {
		log.Printf("nearby academy facilities | course category page | Error: %v", res.Status.Message)
		return
	}
	if len(res.Facilities) == 0 {
		return
	}

	carouselItems := make([]*sushi.CustomTextSnippetTypeLayout, 0)

	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("NEARBY CENTERS")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontReg300, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	title.SetColor(colorGrey900)
	title.SetFont(fontReg300)
	title.SetKerning(3)

	impressionEname := &sushi.EnameData{
		Ename: "academy_centers_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := template.GetClevertapTrackItem(ctx, nil, sectionEvents)

	headerItem := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig: headerLayout,
		SectionHeaderType1Snippet: &sushi.SectionHeaderType1Snippet{
			Title:             title,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		},
	}
	carouselItems = append(carouselItems, headerItem)

	snippetLayout := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoFacilityCardType2,
		LayoutType:  sushi.LayoutTypeCarousel,
	}

	items := make([]sushi.FitsoFacilityCardType2SnippetItem, 0)
	for _, facility := range res.Facilities {
		snippet := sushi.FitsoFacilityCardType2SnippetItem{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sports {
			sportNames = append(sportNames, sport.SportName)
		}
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction

		ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}

		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		if len(facility.Attributes) > 0 {
			bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
			for _, val := range facility.Attributes {
				attrImage, _ := sushi.NewImage(val.Image)

				if val.Type == attributeMaxSafety {
					attrImage.SetAspectRatio(2.277777)
					attrImage.SetType(sushi.ImageTypeRectangle)
				} else if val.Type == attributePeopleCount {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeCircle)
				} else if val.Type == attributeVaccinatedStaff {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeRectangle)
				}

				attrTitle, _ := sushi.NewTextSnippet(val.Title)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				attrTitle.SetColor(titleColor)

				bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
					LeftImage: attrImage,
					Title:     attrTitle,
				}
				bottomItems = append(bottomItems, bottomContainerSnippetItem)
			}
			snippet.BottomContainerSnippetItems = &bottomItems
		}

		multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
		for _, sport := range facility.Sports {
			if sport.SportId != template.Response.CourseSportIds[0] {
				continue
			}
			for _, attribute := range sport.FsInfo {
				tagTitle, _ := sushi.NewTextSnippet(attribute)
				tagColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
				tagColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
				gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})

				multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
					Title:    tagTitle,
					Gradient: gradient,
				})
			}
		}
		if len(multiTagItems) > 0 {
			snippet.TopTags = &multiTagItems
		}

		horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
			Text: facility.SubzoneName,
		}
		horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
			HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
		}
		snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

		trackPayload := make(map[string]interface{})
		trackPayload["facility_id"] = facility.FacilityId
		tapEname := &sushi.EnameData{
			Ename: "academy_facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := template.GetClevertapTrackItem(ctx, trackPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, snippet)
	}
	snippetItem := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig: snippetLayout,
		FitsoFacilityCardType2: &sushi.FitsoFacilityCardType2Snippet{
			Items: &items,
		},
	}
	carouselItems = append(carouselItems, snippetItem)
	template.FacilityCarousel = carouselItems
}

func (template *AcademyCourseCategoryPageTemplate) setPageTracking(ctx context.Context) {
	backArrowEname := &sushi.EnameData{
		Ename: "course_category_page_back_arrow_tap",
	}
	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := template.GetClevertapTrackItem(ctx, nil, backArrowEvents)

	landingEname := &sushi.EnameData{
		Ename: "course_category_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := template.GetClevertapTrackItem(ctx, nil, landingEvents)

	template.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}
}

func (template *AcademyCourseCategoryPageTemplate) GetClevertapTrackItem(
	ctx context.Context,
	trackPayload map[string]interface{},
	trackEvents *sushi.EventNames,
) *sushi.ClevertapItem {
	if trackPayload == nil {
		trackPayload = make(map[string]interface{})
	}
	trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
	trackPayload["course_category_id"] = template.CourseCategoryId
	trackPayload["source"] = "course_category"

	return sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
}

func getNoCostEMIBenifit(benefits *facilitySportPB.Benefit) *facilitySportPB.BenefitDescription {
	for _, benefit := range benefits.BenefitDescription {
		if benefit.Title == "No cost EMI available" {
			return benefit
		}
	}
	return nil
}
