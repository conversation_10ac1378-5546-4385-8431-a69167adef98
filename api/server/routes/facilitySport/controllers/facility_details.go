package facilitySportController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	facilityModel "bitbucket.org/jogocoin/go_api/api/models/facility"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
)

var (
	defaultFacilityCollapsedCount = 4
)

type FacilityPageData struct {
	FacilityDetails            *facilitySportPB.Facility
	NearbyFacilities           *facilitySportPB.DistanceFilterBasedFacilityListingResponse
	SubscriptionDetails        *productPB.GetUserSubscriptionStatusResponse
	PostbackParams             map[string]interface{}
	FacilityNonOperationalDays []int32
}

type FacilityPageTemplate struct {
	PageRequest       facilityModel.GetFacilityRequest `json:"-"`
	PageData          FacilityPageData                 `json:"-"`
	PageHeader        *facilityModel.PageHeader        `json:"page_header,omitempty"`
	Header            *sushi.HeaderType4SnippetLayout  `json:"header,omitempty"`
	Sections          []*facilityModel.ResultSection   `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout  `json:"footer,omitempty"`
	HasMore           bool                             `json:"has_more"`
	PostbackParams    string                           `json:"postback_params,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem           `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item                    `json:"jumbo_tracking,omitempty"`
}

type facility struct {
	FacilityID int32 `uri:"id" binding:"required"`
}

func GetFacilityDetailsC(c *gin.Context) {
	var f facility

	if err := c.ShouldBindUri(&f); err != nil {
		c.JSON(400, gin.H{"msg": err})
		log.Printf("Api: facility, function: GetFacilityDetailsC, Error: %v", err)
		return
	}

	var requestData facilityModel.GetFacilityRequest
	requestData = c.MustGet("jsonData").(facilityModel.GetFacilityRequest)
	requestData.FacilityID = f.FacilityID
	if querySportID, ok := c.GetQuery("sport_id"); ok {
		querySportIDInt, err := strconv.Atoi(querySportID)
		if err == nil {
			requestData.SportID = int32(querySportIDInt)
		}
	}
	requestData.SportIDFlag = requestData.SportID
	requestData.UnmarshalledPostbackParams = getFacilityDetailsRequestPostbackParams(requestData.PostbackParams)
	ctx := util.PrepareGRPCMetaData(c)
	log.Printf("GetFacilityDetailsC: Error Provider: City id - %d", util.GetCityIDFromContext(ctx))
	template := FacilityPageTemplate{
		PageRequest: requestData,
		PageData:    FacilityPageData{},
	}

	//seasonalPoolVersion := featuresupport.SupportsSeasonalPools(c)

	if template.PageRequest.PostbackParams == "" {

		template.SetFacilityPreInfoSection(ctx)
		template.SetFacilityInfoSectionNew(ctx)
		template.SetFacilityOperationalInfo(ctx)
		template.SetFacilityAmenitiesSection(ctx)
		template.SetFacilitySportsTemplate(c)
		//template.SetCallFacilitySection(ctx)
		template.SetNearbySectionHeading(ctx)
		template.SetNearByCentersSection(ctx)
		template.SetHeaderSection(ctx)
		template.SetFooterSection(ctx)
		template.SetPageHeaderSection(ctx)
		template.SetPostbackParams(ctx)
		template.SetPageTracking(ctx)
	} else {
		template.GetFacilityDetails(ctx)
		template.SetNearByCentersSection(ctx)
		template.SetPostbackParams(ctx)
	}
	c.JSON(http.StatusOK, template)
}

func (f *FacilityPageTemplate) GetFacilityDetails(ctx context.Context) *facilitySportPB.Facility {
	if f.PageData.FacilityDetails != nil {
		return f.PageData.FacilityDetails
	}
	facilityClient := util.GetFacilitySportClient()
	facilityDetailRequest := &facilitySportPB.GetFacilityDetailsRequest{
		FacilityId: f.PageRequest.FacilityID,
		SportId:    f.PageRequest.SportID,
	}
	facilityDetailsResponse, err := facilityClient.GetFacilityDetails(ctx, facilityDetailRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetFacilityDetails, Error: %v", err)
		return nil
	}
	if len(facilityDetailsResponse.Sports) == 0 {
		log.Printf("Api: facility, function: GetFacilityDetails, Error: no sport for facility id %d", f.PageRequest.FacilityID)
	} else if f.PageRequest.SportID == 0 {
		f.PageRequest.SportID = facilityDetailsResponse.Sports[0].SportId
	}

	f.PageData.FacilityDetails = facilityDetailsResponse
	f.PageData.FacilityNonOperationalDays = getFacilityNonOperationDays(facilityDetailsResponse.Sports)
	return f.PageData.FacilityDetails
}

func (f *FacilityPageTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.DistanceFilterBasedFacilityListingResponse {
	if f.PageData.NearbyFacilities != nil {
		return f.PageData.NearbyFacilities
	}

	previousFacilityIds := f.PageRequest.UnmarshalledPostbackParams.PreviousFacilityIds
	if len(previousFacilityIds) == 0 { // first call
		previousFacilityIds = append(previousFacilityIds, f.PageRequest.FacilityID)
	}

	facilityClient := util.GetFacilitySportClient()
	nearbyFacilitiesRequest := &facilitySportPB.DistanceFilterBasedFacilityListingRequest{
		SportId:             f.PageRequest.SportID,
		Count:               f.PageRequest.Count,
		PreviousFacilityIds: previousFacilityIds,
	}
	nearbyFacilitiesResponse, err := facilityClient.GetDistanceFilterBasedFacilitiesDetails(ctx, nearbyFacilitiesRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetNearbyFacilities, Error: %v", err)
		return nil
	}
	f.PageData.NearbyFacilities = nearbyFacilitiesResponse
	return f.PageData.NearbyFacilities
}

func (f *FacilityPageTemplate) GetUserSubscriptionStatus(ctx context.Context) *productPB.GetUserSubscriptionStatusResponse {
	if f.PageData.SubscriptionDetails != nil {
		return f.PageData.SubscriptionDetails
	}
	productClient := util.GetProductClient()
	footerDetailsRequest := &productPB.GetUserSubscriptionStatusRequest{
		UserId:                    util.GetUserIDFromContext(ctx),
		ProductSuggestionRequired: true,
		CheckChildSub:             true,
	}
	footerDetailsResponse, err := productClient.GetUserSubscriptionStatus(ctx, footerDetailsRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetUserSubscriptionStatus, Error: %v", err)
		return nil
	}
	f.PageData.SubscriptionDetails = footerDetailsResponse
	return f.PageData.SubscriptionDetails
}

func getFacilityDetailsRequestPostbackParams(postbackParams string) *facilityModel.RequestPostbackParams {
	var temp map[string]interface{}
	json.Unmarshal([]byte(postbackParams), &temp)

	previousFacilityIds := make([]int32, 0)
	if facilityIds, ok := temp["previous_facility_ids"]; ok {
		facilityIdsInterface, _ := facilityIds.([]interface{})
		for _, val := range facilityIdsInterface {
			previousFacilityIds = append(previousFacilityIds, int32(val.(float64)))
		}
	}
	requestPostbackParams := &facilityModel.RequestPostbackParams{
		PreviousFacilityIds: previousFacilityIds,
	}
	return requestPostbackParams
}

func (f *FacilityPageTemplate) AddSection(section *facilityModel.ResultSection) {
	f.Sections = append(f.Sections, section)
}

func (f *FacilityPageTemplate) AddFooter(footer *sushi.FooterSnippetType2Layout) {
	f.Footer = footer
}

func (f *FacilityPageTemplate) AddHeader(header *sushi.HeaderType4SnippetLayout) {
	f.Header = header
}

func (f *FacilityPageTemplate) SetHasMore(hasMore bool) {
	f.HasMore = hasMore
}

func (f *FacilityPageTemplate) SetPageHeader(pageHeader *facilityModel.PageHeader) {
	f.PageHeader = pageHeader
}

func (f *FacilityPageTemplate) SetPostbackParams(ctx context.Context) {
	if len(f.PageData.PostbackParams) == 0 {
		return
	}
	postbackParam, _ := json.Marshal(f.PageData.PostbackParams)
	f.PostbackParams = string(postbackParam)
}

func (f *FacilityPageTemplate) AddPostbackParam(key string, val interface{}) {
	if f.PageData.PostbackParams == nil {
		f.PageData.PostbackParams = make(map[string]interface{})
	}
	f.PageData.PostbackParams[key] = val
}

func (f *FacilityPageTemplate) SetFacilityPreInfoSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil || len(response.Attributes) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.TickerSnippetType1,
	}
	items := make([]sushi.TickerSnippetType1SnippetItem, 0)

	for _, attr := range response.Attributes {
		image, _ := sushi.NewImage(attr.Image)
		if attr.Type == attributeMaxSafety {
			image.SetAspectRatio(2.277777)
			image.SetType(sushi.ImageTypeRectangle)
		} else if attr.Type == attributePeopleCount {
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeCircle)
		} else if attr.Type == attributeVaccinatedStaff {
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeRectangle)
		}

		title, _ := sushi.NewTextSnippet(attr.Title)
		item := sushi.TickerSnippetType1SnippetItem{
			LeftImage: image,
			Title:     title,
		}
		items = append(items, item)
	}

	bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	preInfoSnippet := &sushi.TickerSnippetType1Snippet{
		Items:   &items,
		BgColor: bg_color,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		TickerSnippetType1: preInfoSnippet,
	}
	f.AddSection(section)
}

func (f *FacilityPageTemplate) SetFacilityInfoSectionNew(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)
	if response == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.TextSnippetType8,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	facilitySnippet := sushi.TextSnippetType8Snippet{}

	title, _ := sushi.NewTextSnippet(response.DisplayName)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetNumberOfLines(2)
	title.SetFont(font)
	facilitySnippet.Title = title

	subtitle1, _ := sushi.NewTextSnippet(response.DisplayAddress)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle1.SetFont(font)
	subtitle1.SetColor(color)
	facilitySnippet.SubTitle1 = subtitle1

	ratingSnippet := ratingSnippet(ctx, response.Tag, response.Rating)
	ratingSnippet.Size = sushi.RatingSize400
	facilitySnippet.RatingSnippet = &sushi.RatingSnippet{
		Type:  sushi.RatingTypeTagV2,
		TagV2: ratingSnippet,
	}

	// get directions
	clickAction := sushi.GetClickAction()
	openMap := sushi.OpenMap{
		Latitude:  fmt.Sprintf("%f", response.Latitude),
		Longitude: fmt.Sprintf("%f", response.Longitude),
	}
	clickAction.SetOpenMap(&openMap)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["sport_id"] = f.PageRequest.SportID
	tapPayload["source"] = "facility"
	tapEname := &sushi.EnameData{
		Ename: "get_directions_button_tap",
	}
	getDrectionEvents := sushi.NewClevertapEvents()
	getDrectionEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, getDrectionEvents)

	directionColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	directionIcon, _ := sushi.NewIcon(sushi.DirectionIcon, directionColor)
	directionButton, _ := sushi.NewButton(sushi.ButtontypeText)
	directionButton.SetText("Get directions")
	directionButton.SetSize(sushi.ButtonSizeMedium)
	directionButton.SetPrefixIcon(directionIcon)
	directionButton.SetColor(directionColor)
	directionButton.SetClickAction(clickAction)
	directionButton.AddClevertapTrackingItem(trackItem)
	facilitySnippet.RightButton = directionButton

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	facilitySnippet.BgColor = bgColor

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:     layoutConfig,
		SnippetConfig:    snippetConfig,
		TextSnippetType8: &facilitySnippet,
	}
	f.AddSection(section)
}

func (f *FacilityPageTemplate) SetFacilityOperationalInfo(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)
	isSeasonalPoolVersion := featuresupport.SupportsSeasonalPoolsUsingContext(ctx)
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	if response == nil {
		return
	}
	facilityNonOperationalDays := f.PageData.FacilityNonOperationalDays
	facilitySeasonalInfo := response.SeasonalPoolInfo
	if len(facilityNonOperationalDays) < 1 && facilitySeasonalInfo == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	facilitySnippet := sushi.ImageTextSnippetType30Snippet{}

	items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	if response.SeasonalPoolInfo != nil && (response.SeasonalPoolInfo.Flag == true || response.SeasonalPoolInfo.Message != "") {
		seasonalPoolMessage := response.SeasonalPoolInfo.Message
		startDate, _ := ptypes.Timestamp(response.SeasonalPoolInfo.StartDate)
		endDate, _ := ptypes.Timestamp(response.SeasonalPoolInfo.EndDate)
		seasonalPoolMainText := ""
		if response.SeasonalPoolInfo.Flag == true {
			seasonalPoolMessage = GetSeasonalPoolMessageForFacilityPage(startDate, endDate)
			seasonalPoolMainText = "Seasonal Pool"
		}
		if response.SeasonalPoolInfo.Message == "All-weather Pool" {
			seasonalPoolMessage = "All-weather pools are operational throughout the year."
			seasonalPoolMainText = "All-weather Pool"
		}

		item := sushi.ImageTextSnippetType30SnippetItem{}
		image, _ := sushi.NewImage(util.GetCDNLink("uploads/Group244281629793147.png"))
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(14)
		image.SetWidth(14)

		title, _ := sushi.NewTextSnippet(seasonalPoolMainText)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
		suffixColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint400)
		icon, _ := sushi.NewIcon("e879", suffixColor)
		prefixIcon, _ := sushi.NewIcon(sushi.SwimmingIcon, color)
		title.SetFont(font)
		title.SetColor(color)
		if isSeasonalPoolVersion {
			title.SetSuffixIcon(icon)
		}
		if !isNewColorSupported {
			item.Image = image
		} else {
			title.SetPrefixIcon(prefixIcon)
		}
		item.Title = title

		clickAction := sushi.GetClickAction()
		titleSnippet, _ := sushi.NewTextSnippet(seasonalPoolMessage)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		titleSnippet.SetColor(titleColor)
		titleSnippet.SetFont(titleFont)
		subtitleSnippet, _ := sushi.NewTextSnippet("OK")
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		subtitleSnippet.SetColor(subtitleColor)
		subtitleSnippet.SetFont(subtitleFont)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		showToolTip := &sushi.ShowTooltip{
			Title:    titleSnippet,
			Subtitle: subtitleSnippet,
			BgColor:  bgColor,
		}
		clickAction.SetClickActionType(sushi.ClickActionShowToolTip)
		clickAction.SetShowTooltip(showToolTip)
		if isSeasonalPoolVersion {
			item.ClickAction = clickAction
			tooltipTapPayload := make(map[string]interface{})
			tooltipTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
			tooltipTapPayload["facility_id"] = f.PageRequest.FacilityID
			tooltipTapPayload["sport_id"] = f.PageRequest.SportID
			tooltipTapPayload["source"] = "facility"
			tapEname := &sushi.EnameData{
				Ename: "get_seasonal_pool_tool_tip_button_tap",
			}
			getToolTipEvents := sushi.NewClevertapEvents()
			getToolTipEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tooltipTapPayload, getToolTipEvents)
			item.ClevertapItem = []*sushi.ClevertapItem{trackItem}
		}
		items = append(items, item)
	}

	if len(facilityNonOperationalDays) > 0 {
		item := sushi.ImageTextSnippetType30SnippetItem{}
		image, _ := sushi.NewImage(util.GetCDNLink("uploads/Vector1629790646.png"))
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(14)
		image.SetWidth(14)

		dayStrings := make([]string, 0)
		for _, day := range facilityNonOperationalDays {
			dayStrings = append(dayStrings, time.Weekday(day).String())
		}
		var titleText string
		if len(dayStrings) == 1 {
			titleText = fmt.Sprintf("Closed on %s", dayStrings[0])
		} else {
			titleText = fmt.Sprintf(
				"Closed on %s and %s",
				strings.Join(dayStrings[0:len(facilityNonOperationalDays)-1], ", "),
				dayStrings[len(facilityNonOperationalDays)-1],
			)
		}
		title, _ := sushi.NewTextSnippet(titleText)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
		icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
		title.SetFont(font)
		title.SetColor(color)
		if !isNewColorSupported {
			item.Image = image
		} else {
			title.SetPrefixIcon(icon)
		}
		item.Title = title
		items = append(items, item)
	}

	facilitySnippet.Items = &items
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		SnippetConfig:          snippetConfig,
		ImageTextSnippetType30: &facilitySnippet,
	}
	f.AddSection(section)
}

func (f *FacilityPageTemplate) SetFacilityAmenitiesSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	if len(response.Amenity) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
		ShouldResize: true,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	ameneties := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _, val := range response.Amenity {
		item := sushi.ImageTextSnippetType30SnippetItem{}
		image, _ := sushi.NewImage(val.Icon)
		image.SetAspectRatio(float32(1))
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(16)
		image.SetWidth(16)

		title, _ := sushi.NewTextSnippet(val.AmenityName)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		title.SetFont(font)
		title.SetColor(color)

		item.Image = image
		item.Title = title

		ameneties = append(ameneties, item)
	}

	if len(ameneties)-defaultFacilityCollapsedCount > 1 {
		layoutConfig.CollapsedCount = defaultFacilityCollapsedCount
	}

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	amenitySnippet := &sushi.ImageTextSnippetType30Snippet{
		BgColor: bgColor,
		Items:   &ameneties,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		SnippetConfig:          snippetConfig,
		ImageTextSnippetType30: amenitySnippet,
	}
	f.AddSection(section)
}

func (f *FacilityPageTemplate) SetCallFacilitySection(ctx context.Context) {
	subscriptionData := f.GetUserSubscriptionStatus(ctx)
	if subscriptionData == nil {
		return
	}
	status := subscriptionData.SubscriptionStatus
	tomorrowSubscription := subscriptionData.TomorrowParentSubscription || subscriptionData.TomorrowChildSubscription
	if status == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || tomorrowSubscription {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Contact <EMAIL>.")

	button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)

	button.SetText("Call")
	button.SetPrefixIcon(callIcon)
	button.SetClickAction(clickAction)

	item.BgColor = bgColor
	item.Title = title
	//item.RightButton = button
	rightButton1, _ := sushi.NewButton(sushi.ButtontypeText)
	rightButton1.SetText(" ")
	item.RightButton = rightButton1
	
	item.BorderColor = borderColor
	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:               layoutConfig,
		SnippetConfig:              snippetConfig,
		FitsoImageTextSnippetType7: snippet,
	}
	f.AddSection(section)
}

func AllowSwimmingSelect(facilityDetailsResponse *facilitySportPB.Facility) bool {
	if facilityDetailsResponse.SeasonalPoolInfo != nil && facilityDetailsResponse.SeasonalPoolInfo.Flag == true && facilityDetailsResponse.SeasonalPoolInfo.Action == 0 {
		return false
	} else {
		return true
	}
}

func IsSeasonalPool(facilityDetailsResponse *facilitySportPB.Facility) bool {
	if facilityDetailsResponse.SeasonalPoolInfo != nil && facilityDetailsResponse.SeasonalPoolInfo.Flag == true && facilityDetailsResponse.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSING_SEASON {
		return true
	} else {
		return false
	}
}

func (f *FacilityPageTemplate) SetFacilitySportsTemplate(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	isSeasonalPoolVersion := featuresupport.SupportsSeasonalPools(c)
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	facilityDetailsResponse := f.GetFacilityDetails(ctx)
	if facilityDetailsResponse == nil {
		return
	}
	subscriptionData := f.GetUserSubscriptionStatus(ctx)
	if subscriptionData == nil {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	title, _ := sushi.NewTextSnippet("SPORTS AVAILABLE HERE")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	source := "facility"

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["source"] = source
	sectionImpressionPayload["facility_id"] = f.PageRequest.FacilityID
	sectionImpressionPayload["sport_id"] = f.PageRequest.SportID
	impressionEname := &sushi.EnameData{
		Ename: "facility_sports_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	f.AddSection(sectionHeaderSection)

	facilityNonOperationalDays := f.PageData.FacilityNonOperationalDays

	for _, sport := range facilityDetailsResponse.Sports {

		sportLayoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType33,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		item := sushi.ImageTextSnippetType33SnippetItem{}
		item.IsSelected = sport.SportId == f.PageRequest.SportID
		item.IsSelectable = true
		item.IsInactive = false

		if sport.SportId == common.SWIMMING_SPORT_ID && AllowSwimmingSelect(facilityDetailsResponse) == false && isSeasonalPoolVersion {
			item.IsInactive = true
			item.IsSelected = false
			item.IsSelectable = false
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
			item.BgColor = color
		}

		subscriptionStatus := subscriptionData.SubscriptionStatus

		if util.IsHighPeakSlotFacilitySport(ctx, sport.FsId) && subscriptionStatus != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
			item.IsInactive = true
			item.IsSelected = false
			item.IsSelectable = false
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
			item.BgColor = color
		}
		leftImage, _ := sushi.NewImage(sport.Icon)
		leftImage.SetAspectRatio(float32(1))
		leftImage.SetHeight(42)
		leftImage.SetType(sushi.ImageTypeCircle)
		sportImageColor, _ := sushi.NewColor(sushi.ColorType(sport.IconBackgroundColor), sushi.ColorTint(sport.IconTint))
		leftImage.SetColor(sportImageColor)
		item.LeftImage = leftImage

		if item.IsInactive {
			greyFilter, _ := sushi.NewFilter(sushi.FilterTypeGrayScale)
			greyFilter.Data = "0"
			item.LeftImage.Filter = greyFilter
			sportImageColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint(sport.IconTint))
			leftImage.SetColor(sportImageColor)
		}

		title, _ := sushi.NewTextSnippet(sport.SportName)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		title.SetFont(font)
		title.SetColor(color)
		item.Title = title
		if sport.SportSubtitle != "" {
			subtitle1, _ := sushi.NewTextSnippet(sport.SportSubtitle)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			subtitle1.SetFont(font)
			subtitle1.SetColor(color)
			item.SubTitle1 = subtitle1
		}
		seasonalPoolMessage := ""
		if facilityDetailsResponse.SeasonalPoolInfo != nil {
			startDate, _ := ptypes.Timestamp(facilityDetailsResponse.SeasonalPoolInfo.StartDate)
			endDate, _ := ptypes.Timestamp(facilityDetailsResponse.SeasonalPoolInfo.EndDate)
			seasonalPoolMessage = GetSeasonalPoolMessageForFacilityPage(startDate, endDate)
		}

		if item.IsInactive && len(seasonalPoolMessage) > 0 {
			subtitle2, _ := sushi.NewTextSnippet(seasonalPoolMessage)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			colorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_LIGHT_THEME
			}
			color, _ = sushi.NewColor(colorType, sushi.ColorTint400)
			icon, _ := sushi.NewIcon(sushi.SwimmingIcon, color)

			subtitle2.SetFont(font)
			subtitle2.SetColor(color)
			if isSeasonalPoolVersion {
				subtitle2.SetPrefixIcon(icon)
			}
			item.SubTitle2 = subtitle2
		}
		if util.IsHighPeakSlotFacilitySport(ctx, sport.FsId) && subscriptionStatus != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
			newSubtitle2, _ := sushi.NewTextSnippet("Sold out, unlikely to get booking here in peak hours")
			colorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_LIGHT_THEME
			}
			nst_color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
			nst_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
			icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
			newSubtitle2.SetColor(nst_color)
			newSubtitle2.SetFont(nst_font)
			newSubtitle2.SetPrefixIcon(icon)
			item.SubTitle2 = newSubtitle2
		}

		if sport.Tag == common.OPENING_FACILITY_SPORT_TAG || sport.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
			colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleTag, _ := sushi.NewTextSnippet(common.OPENING_FACILITY_SPORT_TAG)
			titleTag.SetColor(colorTag)

			bgColorType := sushi.ColorTypeTeal
			bgColorTint := sushi.ColorTint500

			if isNewColorSupported && sport.Tag == common.OPENING_FACILITY_SPORT_TAG {
				bgColorType = sushi.ColorTypeBlue
				bgColorTint = sushi.ColorTint400
			} else if sport.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				bgColorType = sushi.ColorTypeOrange
				if isNewColorSupported {
					bgColorType = sushi.ALERT_LIGHT_THEME
				}
			}
			bgColorTag, _ := sushi.NewColor(bgColorType, bgColorTint)

			tag := &sushi.Tag{
				Title:   titleTag,
				BgColor: bgColorTag,
			}
			item.Tag1 = tag
		}

		if item.IsInactive == false {
			var filteredNonOperationalDays []int32
			if len(facilityDetailsResponse.Sports) > 1 {
				filteredNonOperationalDays = sharedFunc.FilterIntegerArray(facilityNonOperationalDays, sport.NonOperationalDays)
			}
			if len(filteredNonOperationalDays) > 0 {
				dayStrings := make([]string, 0)
				for _, day := range filteredNonOperationalDays {
					dayStrings = append(dayStrings, time.Weekday(day).String())
				}
				var subtitle2Text string
				if len(dayStrings) == 1 {
					subtitle2Text = fmt.Sprintf("Closed on %s", dayStrings[0])
				} else {
					subtitle2Text = fmt.Sprintf(
						"Closed on %s and %s",
						strings.Join(dayStrings[0:len(filteredNonOperationalDays)-1], ", "),
						dayStrings[len(filteredNonOperationalDays)-1],
					)
				}
				subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
				font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				colorType := sushi.ColorTypeOrange
				if isNewColorSupported {
					colorType = sushi.ALERT_LIGHT_THEME
				}
				color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
				icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
				subtitle2.SetFont(font)
				subtitle2.SetColor(color)
				subtitle2.SetPrefixIcon(icon)
				item.SubTitle2 = subtitle2
			}

			tapPayload := make(map[string]interface{})
			tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
			tapPayload["facility_id"] = f.PageRequest.FacilityID
			tapPayload["sport_id"] = sport.SportId
			tapPayload["source"] = "facility"

			status := subscriptionData.SubscriptionStatus
			loggedInUser := util.GetUserIDFromContext(ctx)
			loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)
			tomorrowParentSubscription := subscriptionData.TomorrowParentSubscription
			tomorrowChildSubscription := subscriptionData.TomorrowChildSubscription
			productCategoryId, _ := strconv.Atoi(f.PageRequest.ProductCategoryId)

			switch status {
			case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
				footerButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
				footerButton.SetText(fmt.Sprintf("Book a slot for %s", sport.SportName))
				footerButtonClickAction := sushi.GetClickAction()

				var openBuddyList bool
				if subscriptionData.ActiveChildSubscriptions {
					openBuddyList = true
				} else {
					if subscriptionData.ProductCategoryId == common.SinglekeyCategoryID && sport.SportId != common.BADMINTON_SPORT_ID {
						tapPayload["product"] = "singlekey_trial"
						footerButton.SetText(fmt.Sprintf("Book a trial slot for %s", sport.SportName))
					}
					openBuddyList = false
				}

				addAgeDeeplink, addBookingFlowDeeplink, addMedicalFormDeeplink := false, false, false

				if openBuddyList == true {
					addBookingFlowDeeplink = true
				} else { // single user
					if !featuresupport.SupportsMedicalForm(ctx) {
						if loggedInUserAge >= util.GetMinAgeForBooking(false, int32(productCategoryId)) {
							addBookingFlowDeeplink = true
						} else {
							addAgeDeeplink = true
						}
					} else { // supports medical form UI
						if util.NeedMedicalDetails(ctx, loggedInUser) {
							addMedicalFormDeeplink = true
						} else if loggedInUserAge < util.GetMinAgeForBooking(false, int32(productCategoryId)) {
							addAgeDeeplink = true
						} else {
							addBookingFlowDeeplink = true
						}
					}
				}

				if addBookingFlowDeeplink {
					deeplink, buddyListOpen := util.GetBookingFlowDeepLink(ctx, sport.FsId, openBuddyList, source)
					if !buddyListOpen && subscriptionData.ProductCategoryId == common.SinglekeyCategoryID && sport.SportId != common.BADMINTON_SPORT_ID && subscriptionData.SinglekeyTrialNotEligible {
						customAlert := util.GetTrialUsedupPopup(ctx, subscriptionData)
						footerButtonClickAction.SetCustomAlertAction(customAlert)
					} else {
						footerButtonClickAction.SetDeeplink(&deeplink)
					}
				} else if addAgeDeeplink {
					obj := map[string]interface{}{
						"fs_id":        sport.FsId,
						"bottom_sheet": 1,
						"source":       source,
					}
					post_params, _ := json.Marshal(obj)
					payload := map[string]interface{}{
						"post_action":     "slots_get_facility",
						"user_id":         loggedInUser,
						"postback_params": string(post_params),
					}
					postback_params, _ := json.Marshal(payload)
					footerButtonClickAction.Type = sushi.ClickActionOpenAgeBottomSheet
					footerButtonClickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, false, int32(productCategoryId))
				} else if addMedicalFormDeeplink {
					payload := map[string]interface{}{
						"page_type":    common.PAGE_TYPE_FACILITY,
						"user_id":      loggedInUser,
						"fs_id":        sport.FsId,
						"bottom_sheet": 1,
						"source":       source,
					}
					postbackParams, _ := json.Marshal(payload)
					alert := util.GetCompleteMedicalDetailsAlert(ctx, loggedInUser, string(postbackParams))
					footerButtonClickAction.SetCustomAlertAction(alert)
				}

				footerButton.SetClickAction(footerButtonClickAction)

				tapPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
				tapEname := &sushi.EnameData{
					Ename: "book_slot_button_tap",
				}
				buttonEvents := sushi.NewClevertapEvents()
				buttonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)
				footerButton.AddClevertapTrackingItem(trackItem)

				changeBottomButtton := &sushi.ChangeBottomButton{
					ButtonId: "book_slot",
					Button:   footerButton,
				}
				changeBottomButttonClickAction := sushi.GetClickAction()
				changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
				if item.IsSelectable == true && item.IsInactive == false {
					item.ClickAction = changeBottomButttonClickAction
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
				footerButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
				footerButtonClickAction := sushi.GetClickAction()
				if tomorrowParentSubscription || tomorrowChildSubscription {
					footerButton.SetText(fmt.Sprintf("Book a slot for %s", sport.SportName))
					openBuddyList := false
					if tomorrowChildSubscription {
						openBuddyList = true
					} else if subscriptionData.ProductCategoryId == common.SinglekeyCategoryID && sport.SportId != common.BADMINTON_SPORT_ID {
						tapPayload["product"] = "singlekey_trial"
						footerButton.SetText(fmt.Sprintf("Book a trial slot for %s", sport.SportName))
					}
					addAgeDeeplink, addBookingFlowDeeplink, addMedicalFormDeeplink := false, false, false

					if openBuddyList == true {
						addBookingFlowDeeplink = true
					} else { // single user
						if !featuresupport.SupportsMedicalForm(ctx) {
							if loggedInUserAge >= util.GetMinAgeForBooking(false, int32(productCategoryId)) {
								addBookingFlowDeeplink = true
							} else {
								addAgeDeeplink = true
							}
						} else { // supports medical form UI
							if util.NeedMedicalDetails(ctx, loggedInUser) {
								addMedicalFormDeeplink = true
							} else if loggedInUserAge < util.GetMinAgeForBooking(false, int32(productCategoryId)) {
								addAgeDeeplink = true
							} else {
								addBookingFlowDeeplink = true
							}
						}
					}

					if addBookingFlowDeeplink {
						deeplink, buddyListOpen := util.GetBookingFlowDeepLink(ctx, sport.FsId, openBuddyList, source)
						if !buddyListOpen && subscriptionData.ProductCategoryId == common.SinglekeyCategoryID && sport.SportId != common.BADMINTON_SPORT_ID && subscriptionData.SinglekeyTrialNotEligible {
							customAlert := util.GetTrialUsedupPopup(ctx, subscriptionData)
							footerButtonClickAction.SetCustomAlertAction(customAlert)
						} else {
							footerButtonClickAction.SetDeeplink(&deeplink)
						}
					} else if addAgeDeeplink {
						obj := map[string]interface{}{
							"fs_id":        sport.FsId,
							"bottom_sheet": 1,
							"source":       source,
						}
						post_params, _ := json.Marshal(obj)
						payload := map[string]interface{}{
							"post_action":     "slots_get_facility",
							"user_id":         loggedInUser,
							"postback_params": string(post_params),
						}
						postback_params, _ := json.Marshal(payload)
						footerButtonClickAction.Type = sushi.ClickActionOpenAgeBottomSheet
						footerButtonClickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, false, int32(productCategoryId))
					} else if addMedicalFormDeeplink {
						payload := map[string]interface{}{
							"page_type": common.PAGE_TYPE_FACILITY,
							"user_id":   loggedInUser,
							"fs_id":     sport.FsId,
							"source":    source,
						}
						postbackParams, _ := json.Marshal(payload)
						alert := util.GetCompleteMedicalDetailsAlert(ctx, loggedInUser, string(postbackParams))
						footerButtonClickAction.SetCustomAlertAction(alert)
					}
					footerButton.SetClickAction(footerButtonClickAction)

					tapPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
					tapEname := &sushi.EnameData{
						Ename: "book_slot_button_tap",
					}
					buttonEvents := sushi.NewClevertapEvents()
					buttonEvents.SetTap(tapEname)
					trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)
					footerButton.AddClevertapTrackingItem(trackItem)

					changeBottomButtton := &sushi.ChangeBottomButton{
						ButtonId: "book_slot",
						Button:   footerButton,
					}
					changeBottomButttonClickAction := sushi.GetClickAction()
					changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
					if item.IsSelectable == true && item.IsInactive == false {
						item.ClickAction = changeBottomButttonClickAction
					}
				} else {
					footerButton.SetText(fmt.Sprintf("Explore slots for %s", sport.SportName))
					customAlert := util.CustomAlertForExploreSlots(ctx, sport.FsId, subscriptionData, source)
					footerButtonClickAction.SetCustomAlertAction(customAlert)
					footerButton.SetClickAction(footerButtonClickAction)

					tapPayload["membership"] = "FUTURE_PREMIUM_SUBSCRIPTION"
					tapEname := &sushi.EnameData{
						Ename: "book_slot_button_tap",
					}
					buttonEvents := sushi.NewClevertapEvents()
					buttonEvents.SetTap(tapEname)
					trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)
					footerButton.AddClevertapTrackingItem(trackItem)

					changeBottomButtton := &sushi.ChangeBottomButton{
						ButtonId: "book_slot",
						Button:   footerButton,
					}
					changeBottomButttonClickAction := sushi.GetClickAction()
					changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
					if item.IsSelectable == true && item.IsInactive == false {
						item.ClickAction = changeBottomButttonClickAction
					}
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
				source = "facility_trial"
				tapPayload["source"] = source
				footerButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
				footerButton.SetText(fmt.Sprintf("Book a trial slot for %s", sport.SportName))
				if subscriptionData.TrialLeft == 1 {
					footerButton.SetSubtext(fmt.Sprintf("You have %d free trial left", subscriptionData.TrialLeft))
				} else if subscriptionData.TrialLeft > 0 {
					footerButton.SetSubtext(fmt.Sprintf("You have %d free trials left", subscriptionData.TrialLeft))
				}

				footerButtonClickAction := sushi.GetClickAction()
				// Age validation check
				if loggedInUserAge < util.GetMinAgeForBooking(true, int32(productCategoryId)) && loggedInUser > 0 {
					obj := map[string]interface{}{
						"fs_id":        sport.FsId,
						"bottom_sheet": 1,
						"source":       source,
					}
					post_params, _ := json.Marshal(obj)
					payload := map[string]interface{}{
						"post_action":     "slots_get_facility",
						"user_id":         loggedInUser,
						"postback_params": string(post_params),
					}
					postback_params, _ := json.Marshal(payload)
					footerButtonClickAction.Type = sushi.ClickActionOpenAgeBottomSheet
					footerButtonClickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, true, int32(productCategoryId))
				} else {
					deeplink, _ := util.GetBookingFlowDeepLink(ctx, sport.FsId, false, source)
					footerButtonClickAction.SetDeeplink(&deeplink)
				}
				if !IsSeasonalPool(facilityDetailsResponse) || sport.SportId != common.SWIMMING_SPORT_ID {
					footerButton.SetClickAction(footerButtonClickAction)
				}

				if IsSeasonalPool(facilityDetailsResponse) && sport.SportId == common.SWIMMING_SPORT_ID {
					bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
					footerButton.SetBgColor(bgColor)
				}

				tapPayload["membership"] = "TAKE_TRIAL"
				tapEname := &sushi.EnameData{
					Ename: "book_trial_slot_button_tap",
				}
				buttonEvents := sushi.NewClevertapEvents()
				buttonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)

				if !IsSeasonalPool(facilityDetailsResponse) || sport.SportId != common.SWIMMING_SPORT_ID {
					footerButton.AddClevertapTrackingItem(trackItem)
				}
				changeBottomButtton := &sushi.ChangeBottomButton{
					ButtonId: "book_slot",
					Button:   footerButton,
				}
				changeBottomButttonClickAction := sushi.GetClickAction()
				changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
				if item.IsSelectable == true && item.IsInactive == false {
					item.ClickAction = changeBottomButttonClickAction
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
				tapPayload["membership"] = "TRIAL_SUBSCRIPTION_ACTIVE"
				fallthrough
			case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
				footerButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
				footerButton.SetText(fmt.Sprintf("Explore slots for %s", sport.SportName))
				footerButtonClickAction := sushi.GetClickAction()
				customAlert := util.CustomAlertForExploreSlots(ctx, sport.FsId, subscriptionData, source)
				footerButtonClickAction.SetCustomAlertAction(customAlert)
				footerButton.SetClickAction(footerButtonClickAction)

				tapPayload["membership"] = "ALL_SUBSCRIPTION_EXPIRED"
				tapEname := &sushi.EnameData{
					Ename: "book_slot_button_tap",
				}
				buttonEvents := sushi.NewClevertapEvents()
				buttonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)
				footerButton.AddClevertapTrackingItem(trackItem)

				changeBottomButtton := &sushi.ChangeBottomButton{
					ButtonId: "book_slot",
					Button:   footerButton,
				}
				changeBottomButttonClickAction := sushi.GetClickAction()
				changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
				if item.IsSelectable == true && item.IsInactive == false {
					item.ClickAction = changeBottomButttonClickAction
				}
				break
			}
		}
		items := make([]sushi.ImageTextSnippetType33SnippetItem, 0)
		items = append(items, item)

		snippet := &sushi.ImageTextSnippetType33Snippet{
			Items: &items,
		}
		section := &facilityModel.ResultSection{
			LayoutConfig:           sportLayoutConfig,
			Id:                     "select_sport",
			ImageTextSnippetType33: snippet,
		}
		f.AddSection(section)

	}
}

func (f *FacilityPageTemplate) SetNearbySectionHeading(ctx context.Context) {
	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	facilitiesResponse := f.GetFacilityDetails(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Facilities) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("OTHER CENTERS NEAR YOU")

	if f.PageRequest.SportIDFlag != 0 {
		title, _ = sushi.NewTextSnippet(fmt.Sprintf("OTHER %s CENTERS NEAR YOU", strings.ToUpper(facilitiesResponse.Sports[0].SportName)))
	}
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["source"] = "facility"
	sectionImpressionPayload["facility_id"] = f.PageRequest.FacilityID
	sectionImpressionPayload["sport_id"] = f.PageRequest.SportID
	impressionEname := &sushi.EnameData{
		Ename: "fitso_centers_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeThick, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SnippetConfig:      snippetConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	f.AddSection(sectionHeaderSection)
}

func (f *FacilityPageTemplate) ReplicateOldPostbackParamsToNewOne(ctx context.Context) {
	var temp map[string]interface{}
	json.Unmarshal([]byte(f.PageRequest.PostbackParams), &temp)
	f.PageData.PostbackParams = temp
}

func (f *FacilityPageTemplate) SetNearByCentersSection(ctx context.Context) {
	f.ReplicateOldPostbackParamsToNewOne(ctx)

	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Facilities) == 0 {
		return
	}

	f.SetHasMore(nearbyFacilitiesResponse.HasMore)

	f.AddPostbackParam("previous_facility_ids", nearbyFacilitiesResponse.PreviousFacilityIds)
	for _, facility := range nearbyFacilitiesResponse.Facilities {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.ResSnippetType3,
		}
		snippet := &sushi.ResType3Snippet{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sports {
			sportNames = append(sportNames, sport.SportName)
		}
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction

		ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}

		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		if len(facility.Attributes) > 0 {
			bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
			for _, val := range facility.Attributes {
				attrImage, _ := sushi.NewImage(val.Image)

				if val.Type == attributeMaxSafety {
					attrImage.SetAspectRatio(2.277777)
					attrImage.SetType(sushi.ImageTypeRectangle)
				} else if val.Type == attributePeopleCount {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeCircle)
				} else if val.Type == attributeVaccinatedStaff {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeRectangle)
				}

				attrTitle, _ := sushi.NewTextSnippet(val.Title)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				attrTitle.SetColor(titleColor)

				bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
					LeftImage: attrImage,
					Title:     attrTitle,
				}
				bottomItems = append(bottomItems, bottomContainerSnippetItem)
			}
			snippet.BottomContainerSnippetItems = &bottomItems
		}

		multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
		for _, sport := range facility.Sports {
			if sport.SportId != f.PageRequest.SportID {
				continue
			}
			for _, attribute := range sport.FsInfo {
				tagTitle, _ := sushi.NewTextSnippet(attribute)

				if !featuresupport.SupportsNewColor(ctx) {
					multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
					multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
					gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:    tagTitle,
						Gradient: gradient,
					})
				} else {
					multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:   tagTitle,
						BgColor: multiTagSnippetItemColor,
					})
				}
			}
		}
		if len(multiTagItems) > 0 {
			snippet.TopTags = &multiTagItems
		}

		horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
			Text: facility.SubzoneName,
		}
		horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
			HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
		}
		snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["source_facility_id"] = f.PageRequest.FacilityID
		tapPayload["sport_id"] = f.PageRequest.SportID
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "facility"
		tapEname := &sushi.EnameData{
			Ename: "facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		section := &facilityModel.ResultSection{
			LayoutConfig:    layoutConfig,
			ResType3Snippet: snippet,
		}
		f.AddSection(section)
	}
}

func (f *FacilityPageTemplate) SetHeaderSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.HeaderSnippetType4,
	}

	var items []*sushi.HeaderType4SnippetItem

	for _, val := range response.Images {
		image, _ := sushi.NewImage(val)
		image.SetAspectRatio(1.33)
		item := &sushi.HeaderType4SnippetItem{
			Image: image,
		}

		items = append(items, item)
	}
	snippet := &sushi.HeaderType4Snippet{
		Items:       items,
		AspectRatio: 1.33,
	}
	header := &sushi.HeaderType4SnippetLayout{
		LayoutConfig:       layoutConfig,
		HeaderType4Snippet: snippet,
	}
	f.AddHeader(header)
}

func (f *FacilityPageTemplate) SetFooterSection(ctx context.Context) {
	facilityDetails := f.GetFacilityDetails(ctx)
	if facilityDetails == nil {
		return
	}
	footerResponse := f.GetUserSubscriptionStatus(ctx)
	if footerResponse == nil {
		return
	}
	status := footerResponse.SubscriptionStatus

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	clickActionPurchase := sushi.GetClickAction()
	deeplinkPurchase := sushi.Deeplink{
		URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
	}
	clickActionPurchase.SetDeeplink(&deeplinkPurchase)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["sport_id"] = f.PageRequest.SportID
	tapPayload["source"] = "facility"

	switch status {
	case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
		tapPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:    sushi.FooterButtonTypeSolid,
			Text:    "Select sport",
			Id:      "book_slot",
			BgColor: bgColor,
		}
		items = append(items, buttonItem1)

		if !footerResponse.ActiveParentSubscriptions {
			tapEname := &sushi.EnameData{
				Ename: "buy_membership_button_tap",
			}
			buttonItem2Events := sushi.NewClevertapEvents()
			buttonItem2Events.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem2Events)

			buttonItem2 := sushi.FooterSnippetType2ButtonItem{
				Type:              sushi.FooterButtonTypeText,
				Text:              "or become a cultpass PLAY member",
				ClickAction:       clickActionPurchase,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			items = append(items, buttonItem2)
		}
		break

	case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:    sushi.FooterButtonTypeSolid,
			Text:    "Select sport",
			Id:      "book_slot",
			BgColor: bgColor,
		}
		items = append(items, buttonItem1)
		break

	case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
		tapPayload["membership"] = "TAKE_TRIAL"
		tapPayload["source"] = "facility_trial"
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:    sushi.FooterButtonTypeSolid,
			Text:    "Select sport for trial booking",
			Id:      "book_slot",
			BgColor: bgColor,
		}
		items = append(items, buttonItem1)

		tapEname := &sushi.EnameData{
			Ename: "buy_membership_button_tap",
		}
		buttonItem2Events := sushi.NewClevertapEvents()
		buttonItem2Events.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem2Events)

		buttonItem2 := sushi.FooterSnippetType2ButtonItem{
			Type:              sushi.FooterButtonTypeText,
			Text:              "or become a cultpass PLAY member",
			ClickAction:       clickActionPurchase,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		items = append(items, buttonItem2)
		break

	case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
		tapPayload["membership"] = "TRIAL_SUBSCRIPTION_ACTIVE"
		fallthrough
	case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
		tapPayload["membership"] = "ALL_SUBSCRIPTION_EXPIRED"
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:    sushi.FooterButtonTypeSolid,
			Text:    "Select sport",
			Id:      "book_slot",
			BgColor: bgColor,
		}
		items = append(items, buttonItem1)

		tapEname := &sushi.EnameData{
			Ename: "buy_membership_button_tap",
		}
		buttonItem2Events := sushi.NewClevertapEvents()
		buttonItem2Events.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem2Events)

		buttonItem2 := sushi.FooterSnippetType2ButtonItem{
			Type:              sushi.FooterButtonTypeText,
			Text:              "or become a cultpass PLAY member",
			ClickAction:       clickActionPurchase,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		items = append(items, buttonItem2)
		break
	}

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	f.AddFooter(footer)
}

func (f *FacilityPageTemplate) SetPageHeaderSection(ctx context.Context) {
	facilityDetails := f.GetFacilityDetails(ctx)
	if facilityDetails == nil {
		return
	}

	title, _ := sushi.NewTextSnippet(facilityDetails.DisplayName)

	shareClickAction := sushi.GetClickAction()
	share := &sushi.Share{
		Text: fmt.Sprintf("Find this facility on cult | %s", facilityDetails.DisplayAddress),
		URL:  facilityDetails.WebUrl,
	}
	shareClickAction.SetShare(share)

	shareIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	shareIcon, _ := sushi.NewIcon(sushi.ShareIcon, shareIconColor)
	shareIcon.SetClickAction(shareClickAction)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["sport_id"] = f.PageRequest.SportID
	tapPayload["source"] = "facility"
	tapEname := &sushi.EnameData{
		Ename: "share_facility_button_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	shareIcon.AddClevertapTrackingItem(trackItem)

	customAction := &sushi.CustomAction{
		Type: sushi.CustomActionTypeIcon,
		Icon: shareIcon,
	}
	customActions := []*sushi.CustomAction{customAction}

	pageHeader := &facilityModel.PageHeader{
		ScrolledUpTitle: title,
		Actions:         customActions,
	}
	f.SetPageHeader(pageHeader)
}

func (f *FacilityPageTemplate) SetPageTracking(ctx context.Context) {
	backArrowPayload := make(map[string]interface{})
	backArrowPayload["user_id"] = util.GetUserIDFromContext(ctx)
	backArrowPayload["facility_id"] = f.PageRequest.FacilityID
	backArrowPayload["sport_id"] = f.PageRequest.SportID
	backArrowPayload["source"] = "facility"
	backArrowEname := &sushi.EnameData{
		Ename: "facility_page_back_arrow_tap",
	}

	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := sushi.GetClevertapTrackItem(ctx, backArrowPayload, backArrowEvents)

	landingPayload := make(map[string]interface{})
	landingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	landingPayload["facility_id"] = f.PageRequest.FacilityID
	landingPayload["sport_id"] = f.PageRequest.SportID
	landingEname := &sushi.EnameData{
		Ename: "facility_page_landing",
	}

	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, landingPayload, landingEvents)

	f.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}

	f.JumboTracking = []*jumbo.Item{f.getFacilityPageLandingJumboTrackItem()}
}

func (f *FacilityPageTemplate) getFacilityPageLandingJumboTrackItem() *jumbo.Item {
	jumboLandingPayload := make(map[string]interface{})
	jumboLandingPayload["sport_id"] = f.PageRequest.SportID
	jumboLandingPayload["facility_id"] = f.PageRequest.FacilityID
	events := jumbo.NewEvents()
	facilityPageLandingEvent := jumbo.GetEventNameObject(jumbo.FacilityPageLanding)
	events.SetPageSuccess(facilityPageLandingEvent)
	jumboLandingTrackItem := jumbo.GetJumboTrackItem(jumboLandingPayload, events, jumbo.EventsTableName)

	return jumboLandingTrackItem
}

func ratingSnippet(ctx context.Context, tag string, rating string) *sushi.RatingSnippetBlockItem {
	ratingSnippet := &sushi.RatingSnippetBlockItem{}
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
	if sharedFunc.ContainsString(tags, tag) {
		ratingTitle, _ := sushi.NewTextSnippet(tag)
		if !isNewColorSupported {
			color := sushi.ColorTypeBlue
			if tag == common.OPENING_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeTeal
			} else if tag == common.ONHOLD_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeOrange
			}
			ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
			ratingSnippet.BgColor = ratingBgColor
		} else {
			if tag == common.OPENING_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				ratingSnippet.BgColor = ratingBgColor
			} else if tag == common.ONHOLD_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
				ratingSnippet.BgColor = ratingBgColor
			} else {
				// gradient for new tag
				tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
				gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
				ratingSnippet.Gradient = gradient
			}
		}

		ratingSnippet.Title = ratingTitle
		ratingSnippet.Size = sushi.RatingSize300

	} else {
		ratingTitle, _ := sushi.NewTextSnippet(rating)
		ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if isNewColorSupported {
			ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}
		ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

		ratingSnippet.Title = ratingTitle
		ratingSnippet.BgColor = ratingBgColor
		ratingSnippet.RightIcon = ratingIcon
	}
	return ratingSnippet
}

func getFacilityNonOperationDays(facilitySportsList []*facilitySportPB.Sport) []int32 {
	var facilityNonOperationalDays []int32
	if len(facilitySportsList) != 0 {
		facilityNonOperationalDays = facilitySportsList[0].NonOperationalDays
		for _, facilitySport := range facilitySportsList {
			if len(facilitySport.NonOperationalDays) == 0 || len(facilityNonOperationalDays) == 0 {
				facilityNonOperationalDays = make([]int32, 0)
				return facilityNonOperationalDays
			} else {
				facilityNonOperationalDays = sharedFunc.FindIntersectionForIntegerArrays(facilityNonOperationalDays, facilitySport.NonOperationalDays)
			}
		}
	}
	return facilityNonOperationalDays
}
