package facilitySportController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	sportModel "bitbucket.org/jogocoin/go_api/api/models/sport"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"

	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

const (
	SportsFetchTypeForTrialBooking int32 = 0
	SportsFetchTypeForPurchase     int32 = 1
)

type SportSelect struct {
	data              *facilitySportPB.AvailableSportsInCity
	userTrialDetails  *userPB.TrialDetailsResponse
	SportsFetchType   int32
	Header            *sportModel.SportSelectHeader
	CustomHeader      *sportModel.CustomHeader
	ProgressBar       *sportModel.SportSelectProgressBar
	Results           []*sportModel.SportResultSection
	Footer            *sportModel.SportResultSection
	ClevertapTracking []*sushi.ClevertapItem
	JumboTracking     []*jumbo.Item
	RequestUserId     int32
}

func (s *SportSelect) setSportSelectData(data *facilitySportPB.AvailableSportsInCity) {
	s.data = data
}

func (s *SportSelect) setSportSelectHeader(ctx context.Context) {
	if s.SportsFetchType == SportsFetchTypeForTrialBooking {
		title, _ := sushi.NewTextSnippet("Book free trial")
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

		title.SetFont(titleFont)
		title.SetColor(titleColor)

		var trialText string
		if s.userTrialDetails.TrialDetails.TrialSessionsLeft == 1 {
			trialText = fmt.Sprintf("%d free trial available", s.userTrialDetails.TrialDetails.TrialSessionsLeft)
		} else {
			trialText = fmt.Sprintf("%d free trials available", s.userTrialDetails.TrialDetails.TrialSessionsLeft)
		}

		tag, _ := sushi.NewTag(trialText)
		tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
		tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
		if featuresupport.SupportsNewColor(ctx) {
			tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
			tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
		}

		tag.Title.SetFont(tagFont)
		tag.Title.SetColor(tagColor)
		tag.SetBgColor(tagBgColor)
		tag.SetBorderColor(tagBorderColor)

		bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
		s.Header = &sportModel.SportSelectHeader{
			Title: title,
			Tag:   tag,
			Color: bgColor,
		}

		progressBarBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

		progressBarColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if featuresupport.SupportsNewColor(ctx) {
			progressBarColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint500)
		}

		s.ProgressBar = &sportModel.SportSelectProgressBar{
			Progress:       33,
			Color:          progressBarBgColor,
			ProgressColors: []*sushi.Color{progressBarColor},
		}
	}
}

func (s *SportSelect) setSportSelectCustomHeader() {
	customHeaderDetails := &sportModel.CustomHeader{}

	if s.SportsFetchType == SportsFetchTypeForPurchase {
		title, _ := sushi.NewTextSnippet("Select your preferred sport")
		title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetFont(title_font)
		title.SetColor(title_color)

		stringToDisp := "Select your preferred sport to get priority booking on your preferred center"
		subtitle, _ := sushi.NewTextSnippet(stringToDisp)
		subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle.SetFont(subtitle_font)
		subtitle.SetColor(subtitle_color)

		bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		selectedColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		icon, _ := sushi.NewIcon(sushi.DismissIcon, selectedColor)
		customHeaderType1Obj := &sportModel.CustomHeaderType1{
			Title:    title,
			Icon:     icon,
			SubTitle: subtitle,
			BgColor:  bgColor,
		}

		customHeaderDetails = &sportModel.CustomHeader{
			Type:              sportModel.CustomHeaderType1Val,
			CustomHeaderType1: customHeaderType1Obj,
		}
	}
	s.CustomHeader = customHeaderDetails
}

func (s *SportSelect) setSportSelectOptions(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	layoutConfig := &sushi.LayoutConfig{
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 3,
	}

	if featuresupport.SupportsNewHomeSportSnippet(c) {
		layoutConfig.SnippetType = sushi.V2ImageTextSnippetType42
	} else {
		layoutConfig.SnippetType = sushi.ImageTextSnippetType41
	}
	snippetTitle, _ := sushi.NewTextSnippet("Select your sport")

	var items []sushi.ImageTextSnippetType41SnippetItem
	var newItems []sushi.V2ImageTextSnippetType42SnippetItem

	for _, sport := range s.data.Sports {

		sportTitle, _ := sushi.NewTextSnippet(sport.SportName)
		sportFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
		sportColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

		sportTitle.SetFont(sportFont)
		sportTitle.SetColor(sportColor)

		sportImage, _ := sushi.NewImage(sport.DisplayPicture)
		sportImage.SetAspectRatio(1)
		sportImage.SetType(sushi.ImageTypeCircle)
		sportImage.SetHeight(40)
		sportImage.SetWidth(40)

		sportImageColor, _ := sushi.NewColor(sushi.ColorType(sport.IconBackgroundColor), sushi.ColorTint(sport.IconTint))
		sportImage.SetColor(sportImageColor)

		clickAction := sushi.GetClickAction()
		clickActionButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)

		var clickActionButtonText string
		buttonClickAction := sushi.GetClickAction()
		var clevertapEname string

		sportClickPayload := make(map[string]interface{})

		sportClickPayload["user_id"] = util.GetUserIDFromContext(ctx)

		if s.SportsFetchType == SportsFetchTypeForTrialBooking {
			clickActionButtonText = "Proceed to select slot"

			payload := map[string]interface{}{
				"sport_id": sport.SportId,
				"is_trial": 1,
				"source":   "home_trial",
			}
			params, _ := json.Marshal(payload)
			deeplink_url := util.GetTrialSlotsPageDeeplinkWithSportID(sport.SportId)

			deeplink := &sushi.Deeplink{
				URL:            deeplink_url,
				PostbackParams: string(params),
			}
			buttonClickAction.SetDeeplink(deeplink)
			clevertapEname = "proceed_to_select_slot_click"

			if s.userTrialDetails.TrialDetails.TrialSessionsLeft > 0 {
				sportClickPayload["free_trial_count"] = s.userTrialDetails.TrialDetails.TrialSessionsLeft
			} else {
				sportClickPayload["free_trial_count"] = 2
			}
			sportClickPayload["sport_id"] = sport.SportId
			sportClickPayload["source"] = "home"
			sportClickPayload["is_trial"] = 1
			tapEname := &sushi.EnameData{
				Ename: clevertapEname,
			}

			trialEvents := sushi.NewClevertapEvents()
			trialEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, sportClickPayload, trialEvents)
			clickActionButton.AddClevertapTrackingItem(trackItem)

		} else if s.SportsFetchType == SportsFetchTypeForPurchase {
			clickActionButtonText = "Proceed with selected sport"

			payload := map[string]interface{}{
				"sport_id": sport.SportId,
			}
			params, _ := json.Marshal(payload)
			deeplink_url := util.GetPreferredCenterPageDeeplinkWithSportIDAndUserID(sport.SportId, s.RequestUserId)

			deeplink := &sushi.Deeplink{
				URL:            deeplink_url,
				PostbackParams: string(params),
			}
			buttonClickAction.SetDeeplink(deeplink)
			clevertapEname1 := "proceed_to_select_preferred_sport_tap"

			sportClickPayload["sport_id"] = sport.SportId
			sportClickPayload["source"] = "purchase_page_sport_selection"
			tapEname := &sushi.EnameData{
				Ename: clevertapEname1,
			}

			trialEvents := sushi.NewClevertapEvents()
			trialEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, sportClickPayload, trialEvents)
			clickActionButton.AddClevertapTrackingItem(trackItem)
		}
		clickActionButton.SetText(clickActionButtonText)

		if s.SportsFetchType == SportsFetchTypeForTrialBooking {
			clickActionButton.AddJumboTrackingItem(s.getProceedButtonJumboTrackItem(ctx, sport))
		}

		clickActionButton.SetClickAction(buttonClickAction)

		changeBottomButton := &sushi.ChangeBottomButton{
			ButtonId: "button_1",
			Button:   clickActionButton,
		}

		clickAction.SetChangeBottomButton(changeBottomButton)

		if featuresupport.SupportsNewHomeSportSnippet(c) {
			item := sushi.V2ImageTextSnippetType42SnippetItem{
				Title:       sportTitle,
				Image:       sportImage,
				ClickAction: clickAction,
			}
			if s.SportsFetchType == SportsFetchTypeForTrialBooking {
				item.JumboTracking = []*jumbo.Item{s.getSportIconJumboTrackItem(ctx, sport)}
			}

			newItems = append(newItems, item)
		} else {
			item := sushi.ImageTextSnippetType41SnippetItem{
				Title:       sportTitle,
				Image:       sportImage,
				ClickAction: clickAction,
			}

			items = append(items, item)
		}
	}

	resultSection := &sportModel.SportResultSection{
		LayoutConfig: layoutConfig,
	}
	if s.SportsFetchType == SportsFetchTypeForPurchase { // for new purchase flow sport selection
		snippet := &sushi.V2ImageTextSnippetType42Snippet{
			Items: &newItems,
		}
		resultSection.V2ImageTextSnippetType42 = snippet
	} else if featuresupport.SupportsNewHomeSportSnippet(c) {
		snippet := &sushi.V2ImageTextSnippetType42Snippet{
			Items: &newItems,
			Title: snippetTitle,
		}

		resultSection.V2ImageTextSnippetType42 = snippet
	} else {
		snippet := &sushi.ImageTextSnippetType41Snippet{
			Items: &items,
			Title: snippetTitle,
		}

		resultSection.ImageTextSnippetType41 = snippet
	}

	s.Results = []*sportModel.SportResultSection{resultSection}
}

func (s *SportSelect) getProceedButtonJumboTrackItem(ctx context.Context, sport *facilitySportPB.Sport) *jumbo.Item {
	payload := make(map[string]interface{})

	payload["user_trial_available_count"] = s.userTrialDetails.TrialDetails.TrialSessionsLeft
	payload["source_page"] = jumbo.SourcePageHomeTrial
	payload["sport_id"] = sport.SportId
	payload["is_trial"] = true
	payload["total_trial_sports"] = len(s.data.Sports)

	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.SportSelectButtonClick)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (s *SportSelect) getSportIconJumboTrackItem(ctx context.Context, sport *facilitySportPB.Sport) *jumbo.Item {
	payload := make(map[string]interface{})

	payload["user_trial_available_count"] = s.userTrialDetails.TrialDetails.TrialSessionsLeft
	payload["source_page"] = jumbo.SourcePageHomeTrial
	payload["sport_id"] = sport.SportId
	payload["is_trial"] = true
	payload["total_trial_sports"] = len(s.data.Sports)

	events := jumbo.NewEvents()
	tapEvent := jumbo.GetEventNameObject(jumbo.TrialSportIconClick)
	events.SetTap(tapEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (s *SportSelect) setSportSelectFooter() {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	buttonItemColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)

	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Type:             sushi.FooterButtonTypeSolid,
		Text:             "Select a sport to proceed",
		Id:               "button_1",
		IsActionDisabled: 1,
		BgColor:          buttonItemColor,
	}

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &([]sushi.FooterSnippetType2ButtonItem{buttonItem1}),
	}

	s.Footer = &sportModel.SportResultSection{
		LayoutConfig: layoutConfig,
		FooterSnippetType2: &sushi.FooterSnippetType2Snippet{
			ButtonData: buttonData,
		},
	}
}

func (s *SportSelect) setPageTracking(ctx context.Context) {
	payload := make(map[string]interface{})
	jumboPayload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["total_sports"] = len(s.data.Sports)
	payload["source"] = "home"
	payload["is_trial"] = 1
	if s.userTrialDetails.TrialDetails.TrialSessionsLeft > 0 {
		payload["free_trial_count"] = s.userTrialDetails.TrialDetails.TrialSessionsLeft
		jumboPayload["user_trial_available_count"] = s.userTrialDetails.TrialDetails.TrialSessionsLeft
	} else {
		payload["free_trial_count"] = 2
	}
	landingEname := &sushi.EnameData{
		Ename: "sports_list_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}

	jumboPayload["source_page"] = jumbo.SourcePageHomeTrial
	jumboPayload["is_trial"] = true
	jumboPayload["trial_total_sports"] = len(s.data.Sports)

	jumboEvents := jumbo.NewEvents()
	jumboImpressionEvent := jumbo.GetEventNameObject(jumbo.TrialSportsPageView)
	jumboEvents.SetImpression(jumboImpressionEvent)
	jumboEvent := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	jumboEvent.SetPayload(jumboPayload)
	jumboEvent.SetEventNames(jumboEvents)

	s.JumboTracking = []*jumbo.Item{jumboEvent}
}

func (s *SportSelect) SetUserTrialDetails(ctx context.Context) {
	userClient := util.GetUserServiceClient()
	userId := util.GetUserIDFromContext(ctx)

	trialDetails, err := userClient.GetTrialDetails(ctx, &userPB.UserRequest{UserId: userId})
	if err != nil {
		log.Printf("Api: sports get, function: SetUserTrialDetails, error: %v", err)
		return
	}
	s.userTrialDetails = trialDetails
}

func (s *SportSelect) SetSportsFetchType(ctx context.Context, req *structs.SportSelect) {
	s.SportsFetchType = req.FetchType

	if req.RequestUserId > 0 {
		s.RequestUserId = req.RequestUserId
	} else {
		s.RequestUserId = 0
	}
	s.RequestUserId = req.RequestUserId
}

// GetSportsToSelectC returns sports to select
func GetSportsToSelectC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.SportSelect
	json = c.MustGet("jsonData").(structs.SportSelect)

	fcl := util.GetFacilitySportClient()
	sports, err := fcl.GetAvailableSportsInCity(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Printf("func:GetSportsToSelect, error in getting nearest available sports with error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{})
		return
	}

	page := &SportSelect{}
	page.SetSportsFetchType(ctx, &json)
	page.SetUserTrialDetails(ctx)
	page.setSportSelectData(sports)

	page.setSportSelectHeader(ctx)
	page.setSportSelectCustomHeader()
	page.setSportSelectOptions(c)
	page.setSportSelectFooter()

	if page.SportsFetchType == SportsFetchTypeForTrialBooking {
		page.setPageTracking(ctx)
	}

	sushiResponse := &sportModel.SportSelectResponse{
		Header:            page.Header,
		ProgressBar:       page.ProgressBar,
		Results:           page.Results,
		Footer:            page.Footer,
		ClevertapTracking: page.ClevertapTracking,
		JumboTracking:     page.JumboTracking,
	}

	if json.BottomSheet == 1 {
		sushiResponse.CustomHeader = page.CustomHeader
	}

	c.JSON(
		http.StatusOK,
		sushiResponse,
	)
}
