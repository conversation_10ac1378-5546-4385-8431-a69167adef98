package facilitySportController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	sportModel "bitbucket.org/jogocoin/go_api/api/models/sport"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

var (
	attributePeopleCount       = "PEOPLE_COUNT"
	attributeMaxSafety         = "MAX_SAFETY"
	attributeVaccinatedStaff   = "VACCINATED_STAFF"
	maxSafetyBenefits          = "Max Safety measures"
	fitsoBenefits              = "Benefits of cultpass PLAY"
	defaultSportCollapsedCount = 2
)

type sport struct {
	SportID int32 `uri:"id" binding:"required"`
}

type SportPageData struct {
	SportDetails        *facilitySportPB.SportResponse
	NearbyFacilities    *facilitySportPB.DistanceFilterBasedFacilityListingResponse
	SubscriptionDetails *productPB.GetUserSubscriptionStatusResponse
	PostbackParams      map[string]interface{}
}

type SportPageTemplate struct {
	PageRequest       sportModel.GetSportRequest      `json:"-"`
	PageData          SportPageData                   `json:"-"`
	StickyHeader      *sportModel.StickyHeader        `json:"sticky_header,omitempty"`
	Header            *sushi.HeaderType4SnippetLayout `json:"header,omitempty"`
	Sections          []*sportModel.ResultSection     `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout `json:"footer,omitempty"`
	HasMore           bool                            `json:"has_more"`
	PostbackParams    string                          `json:"postback_params,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem          `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item                   `json:"jumbo_tracking,omitempty"`
}

func GetSportController(c *gin.Context) {
	var s sport
	if err := c.ShouldBindUri(&s); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{})
		return
	}

	var requestData sportModel.GetSportRequest
	requestData = c.MustGet("jsonData").(sportModel.GetSportRequest)
	requestData.SportId = s.SportID
	requestData.UnmarshalledPostbackParams = getSportDetailsRequestPostbackParams(requestData.PostbackParams)
	ctx := util.PrepareGRPCMetaData(c)
	log.Printf("GetSportController: Error Provider: City id - %d", util.GetCityIDFromContext(ctx))
	template := SportPageTemplate{
		PageRequest: requestData,
		PageData:    SportPageData{},
	}

	if template.PageRequest.PostbackParams == "" { // first api call
		template.SetStickyHeaderSection(ctx)
		template.SetHeaderSection(ctx)
		template.SetSportDetailsSection(ctx)
		template.SetSportBenifitsSection(ctx)
		template.SetBenefitsAndMaxSafety(ctx)
		template.SetNearbySectionHeading(ctx)
		template.SetNearByCentersSection(ctx)
		template.SetFooterSection(c)
		template.SetPostbackParams(ctx)
		template.SetPageTracking(ctx)
	} else { // load more call
		template.SetNearByCentersSection(ctx)
		template.SetPostbackParams(ctx)
	}
	c.JSON(http.StatusOK, template)
}

func (s *SportPageTemplate) AddSection(section *sportModel.ResultSection) {
	s.Sections = append(s.Sections, section)
}

func (s *SportPageTemplate) AddPostbackParam(key string, val interface{}) {
	if s.PageData.PostbackParams == nil {
		s.PageData.PostbackParams = make(map[string]interface{})
	}
	s.PageData.PostbackParams[key] = val
}

func (s *SportPageTemplate) SetHasMore(hasMore bool) {
	s.HasMore = hasMore
}

func getSportDetailsRequestPostbackParams(postbackParams string) *sportModel.RequestPostbackParams {
	var temp map[string]interface{}
	json.Unmarshal([]byte(postbackParams), &temp)

	previousFacilityIds := make([]int32, 0)
	if facilityIds, ok := temp["previous_facility_ids"]; ok {
		facilityIdsInterface, _ := facilityIds.([]interface{})
		for _, val := range facilityIdsInterface {
			previousFacilityIds = append(previousFacilityIds, int32(val.(float64)))
		}
	}
	requestPostbackParams := &sportModel.RequestPostbackParams{
		PreviousFacilityIds: previousFacilityIds,
	}
	return requestPostbackParams
}

func (s *SportPageTemplate) GetSportDetails(ctx context.Context) *facilitySportPB.SportResponse {
	if s.PageData.SportDetails != nil {
		return s.PageData.SportDetails
	}
	facilityClient := util.GetFacilitySportClient()
	sportRequest := &facilitySportPB.SportRequest{
		SportId:          s.PageRequest.SportId,
		DistanceFilterId: s.PageRequest.DistanceFilterID,
		Count:            s.PageRequest.Count,
	}

	sportsData, err := facilityClient.GetSport(ctx, sportRequest)
	if err != nil {
		log.Printf("Api: sport, function: GetSportDetails, Error: %v", err)
		return nil
	}
	s.PageData.SportDetails = sportsData
	return s.PageData.SportDetails
}

func (s *SportPageTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.DistanceFilterBasedFacilityListingResponse {
	if s.PageData.NearbyFacilities != nil {
		return s.PageData.NearbyFacilities
	}

	facilityClient := util.GetFacilitySportClient()
	nearbyFacilitiesRequest := &facilitySportPB.DistanceFilterBasedFacilityListingRequest{
		SportId:             s.PageRequest.SportId,
		Count:               s.PageRequest.Count,
		PreviousFacilityIds: s.PageRequest.UnmarshalledPostbackParams.PreviousFacilityIds,
	}
	nearbyFacilitiesResponse, err := facilityClient.GetDistanceFilterBasedFacilitiesDetails(ctx, nearbyFacilitiesRequest)
	if err != nil {
		log.Printf("Api: sport, function: GetNearbyFacilities, Error: %v", err)
		return nil
	}
	s.PageData.NearbyFacilities = nearbyFacilitiesResponse
	return s.PageData.NearbyFacilities
}

func (s *SportPageTemplate) GetUserSubscriptionStatus(ctx context.Context) *productPB.GetUserSubscriptionStatusResponse {
	if s.PageData.SubscriptionDetails != nil {
		return s.PageData.SubscriptionDetails
	}
	productClient := util.GetProductClient()
	footerDetailsRequest := &productPB.GetUserSubscriptionStatusRequest{
		UserId:                    util.GetUserIDFromContext(ctx),
		ProductSuggestionRequired: true,
		CheckChildSub:             true,
	}
	footerDetailsResponse, err := productClient.GetUserSubscriptionStatus(ctx, footerDetailsRequest)
	if err != nil {
		log.Printf("Api: sport, function: GetUserSubscriptionStatus, Error: %v", err)
		return nil
	}
	s.PageData.SubscriptionDetails = footerDetailsResponse
	return s.PageData.SubscriptionDetails
}

func (s *SportPageTemplate) SetSportBenifitsSection(ctx context.Context) {
	response := s.GetSportDetails(ctx)
	if response == nil {
		return
	}

	sportData := response.Sport[0]
	imageTextSnippetType30Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	var sportBenefits []sushi.ImageTextSnippetType30SnippetItem
	for _, sportBenefit := range sportData.SportBenefits {
		image, _ := sushi.NewImage(sportBenefit.Image)
		title, _ := sushi.NewTextSnippet(sportBenefit.Title)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		title.SetFont(font)
		title.SetColor(color)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(24)

		textSnippet30Item := sushi.ImageTextSnippetType30SnippetItem{
			Image: image,
			Title: title,
		}
		sportBenefits = append(sportBenefits, textSnippet30Item)
	}

	if len(sportBenefits)-defaultSportCollapsedCount > 0 {
		imageTextSnippetType30Layout.CollapsedCount = defaultSportCollapsedCount
	}

	imageTextSnippetType30SnippetBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	imageTextSnippetType30Snippet := &sushi.ImageTextSnippetType30Snippet{
		Items:   &sportBenefits,
		BgColor: imageTextSnippetType30SnippetBgColor,
	}

	sportBenefitBottomSeparatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	sportBenefitBottomSeparator := &sushi.Separator{
		Type:  sushi.SeparatorTypeMedium,
		Color: sportBenefitBottomSeparatorColor,
	}

	sportBenefitLayout := &sportModel.ResultSection{
		LayoutConfig:           imageTextSnippetType30Layout,
		ImageTextSnippetType30: imageTextSnippetType30Snippet,
		SnippetConfig: &sushi.SnippetConfig{
			BottomSeparator: sportBenefitBottomSeparator,
		},
	}
	s.AddSection(sportBenefitLayout)
}

func (s *SportPageTemplate) SetBenefitsAndMaxSafety(ctx context.Context) {
	response := s.GetSportDetails(ctx)
	if response == nil {
		return
	}

	sportData := response.Sport[0]
	for _, benefit := range sportData.Benefits {
		title, _ := sushi.NewTextSnippet(benefit.Title)
		image, _ := sushi.NewImage(benefit.Image)
		if benefit.Title == maxSafetyBenefits {
			image.SetAspectRatio(2.277777)
		} else if benefit.Title == fitsoBenefits {
			image.SetAspectRatio(2.21)
		}

		image.SetType(sushi.ImageTypeRectangle)

		var items []sushi.ImageTextSnippetType30SnippetItem
		benefitDescriptionLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType30,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
			ShouldResize: true,
		}

		for _, benefitDescription := range benefit.BenefitDescription {
			title, _ := sushi.NewTextSnippet(benefitDescription.Title)
			image, _ := sushi.NewImage(benefitDescription.Image1)

			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeCircle)
			image.SetHeight(24)

			font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)

			title.SetFont(font)
			title.SetColor(color)

			imageSnippetType30 := sushi.ImageTextSnippetType30SnippetItem{
				Image: image,
				Title: title,
			}

			items = append(items, imageSnippetType30)
		}

		accordionSnippetType4SnippetItemLayout := &sushi.AccordionSnippetType4SnippetItem{
			LayoutConfig: benefitDescriptionLayout,
			ImageTextSnippetType30: &sushi.ImageTextSnippetType30Snippet{
				Items: &items,
			},
		}

		accordianSnippet := &sushi.AccordionSnippetType4Snippet{
			Image: image,
			Title: title,
			Items: []*sushi.AccordionSnippetType4SnippetItem{accordionSnippetType4SnippetItemLayout},
		}

		accordionLayout := &sushi.LayoutConfig{
			SnippetType: sushi.AccordionSnippetType4,
		}

		accordionSeparatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		accordionBottomSeparator := &sushi.Separator{
			Type:  sushi.SeparatorTypeMedium,
			Color: accordionSeparatorColor,
		}
		section := &sportModel.ResultSection{
			LayoutConfig:                 accordionLayout,
			AccordionSnippetType4Snippet: accordianSnippet,
			SnippetConfig: &sushi.SnippetConfig{
				BottomSeparator: accordionBottomSeparator,
			},
		}
		s.AddSection(section)
	}
}

func (s *SportPageTemplate) SetSportDetailsSection(ctx context.Context) {
	response := s.GetSportDetails(ctx)
	if response == nil {
		return
	}

	sportData := response.Sport[0]
	sectionHeader1Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.SectionHeaderType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	sportTitle, _ := sushi.NewTextSnippet(sportData.SportName)
	sportFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	sportTitle.SetFont(sportFont)

	sportImage, _ := sushi.NewImage(sportData.Icon)
	sportImage.SetHeight(60)
	sportImage.SetWidth(60)
	sportImage.SetAspectRatio(1)
	sportImage.SetType(sushi.ImageTypeCircle)
	sportImageColor, _ := sushi.NewColor(sushi.ColorType(sportData.IconBackgroundColor), sushi.ColorTint(sportData.IconTint))
	sportImage.SetColor(sportImageColor)

	sportResultItem := &sportModel.ResultSection{
		LayoutConfig: sectionHeader1Layout,
		SectionHeaderType1: &sushi.SectionHeaderType1Snippet{
			Title:      sportTitle,
			RightImage: sportImage,
		},
	}

	if len(sportData.SportSubtitle) > 0 {
		sportSubTitle, _ := sushi.NewTextSnippet(sportData.SportSubtitle)
		sportSubFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		sportSubColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		sportSubTitle.SetFont(sportSubFont)
		sportSubTitle.SetColor(sportSubColor)
		sportResultItem.SectionHeaderType1.Subtitle = sportSubTitle
	}

	s.AddSection(sportResultItem)
}

func (s *SportPageTemplate) SetNearbySectionHeading(ctx context.Context) {
	response := s.GetSportDetails(ctx)
	if response == nil {
		return
	}

	sportData := response.Sport[0]
	nearbyFacilitiesHeading, _ := sushi.NewTextSnippet(fmt.Sprintf("NEARBY %s CENTERS", strings.ToUpper(sportData.SportName)))
	nearbyFacilitiesHeadingFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	nearbyFacilitiesHeadingColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	nearbyFacilitiesHeading.SetFont(nearbyFacilitiesHeadingFont)
	nearbyFacilitiesHeading.SetColor(nearbyFacilitiesHeadingColor)
	nearbyFacilitiesHeading.SetKerning(3)

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["sport_id"] = s.PageRequest.SportId
	sectionImpressionPayload["source"] = "sports"
	impressionEname := &sushi.EnameData{
		Ename: "fitso_centers_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	nearbyFacilitiesLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	nearbyFacilitiesSection := &sushi.SectionHeaderType1Snippet{
		Title:             nearbyFacilitiesHeading,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	section := &sportModel.ResultSection{
		LayoutConfig:       nearbyFacilitiesLayout,
		SectionHeaderType1: nearbyFacilitiesSection,
	}
	s.AddSection(section)
}

func (s *SportPageTemplate) SetAllDistanceFilters(ctx context.Context) {
	response := s.GetSportDetails(ctx)

	if response == nil {
		return
	}
	filters := response.FacilityFilters

	tabSnippetType1Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.FilterRailType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	selectedBorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	defaultBorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	selectedBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	defaultBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	selectedTextColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	defaultTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)

	filterRailType1SnippetConfig := &sushi.FilterRailType1SnippetConfig{
		BorderColor:          selectedBorderColor,
		DefaultBorderColor:   defaultBorderColor,
		SelectedBgColor:      selectedBgColor,
		DefaultBgColor:       defaultBgColor,
		SelectedTextColor:    selectedTextColor,
		DefaultSelectedColor: defaultTextColor,
	}

	var tabOptions []sushi.FilterRailType1SnippetItem
	for _, filter := range filters {
		title, _ := sushi.NewTextSnippet(filter.FilterName)
		tabSnippetType1Item := sushi.FilterRailType1SnippetItem{
			ID:         filter.FilterId,
			Title:      title,
			IsSelected: filter.IsSelected,
		}
		tabOptions = append(tabOptions, tabSnippetType1Item)
	}

	section := &sportModel.ResultSection{
		LayoutConfig: tabSnippetType1Layout,
		FilterRailType1: &sushi.FilterRailType1Snippet{
			Config: filterRailType1SnippetConfig,
			Items:  &tabOptions,
		},
	}

	s.AddSection(section)
}

func (s *SportPageTemplate) GetNotAvailableSnippet(ctx context.Context, distance int32) *sportModel.ResultSection {
	response := s.GetSportDetails(ctx)
	if response == nil {
		return nil
	}
	sportData := response.Sport[0]

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.TextSnippetType1,
	}

	titleText := fmt.Sprintf("Currently, %s centers are not available within %d kms", sportData.SportName, distance)
	title, _ := sushi.NewTextSnippet(titleText)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	title.SetAlignment(sushi.TextAlignmentCenter)
	title.SetColor(color)
	title.SetFont(font)

	snippet := &sushi.TextSnippetType1Snippet{
		Title: title,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	result := &sportModel.ResultSection{
		LayoutConfig:     layoutConfig,
		TextSnippetType1: snippet,
		SnippetConfig:    snippetConfig,
	}
	return result
}

func (s *SportPageTemplate) GetOutsideXKmsTextSnippet(ctx context.Context, distance int32) *sportModel.ResultSection {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	title, _ := sushi.NewTextSnippet(fmt.Sprintf("OTHER CENTERS OUTSIDE %d KMS", distance))
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetColor(color)
	title.SetFont(font)
	title.SetKerning(3)

	snippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	result := &sportModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: snippet,
	}

	key := fmt.Sprintf("outside_%d_title_shown", distance)
	s.AddPostbackParam(key, true)

	return result
}

func (s *SportPageTemplate) GetOutsideXKmsButtonSnippet(ctx context.Context, distance int32) *sportModel.ResultSection {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoTextSnippetType7,
	}
	id := "see_other_centers"
	title, _ := sushi.NewTextSnippet(fmt.Sprintf("See other centers outside %d kms", distance))

	button, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	prefixIcon, _ := sushi.NewIcon(sushi.ArrowDownIcon, nil)
	button.SetPrefixIcon(prefixIcon)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint200)

	clickAction := sushi.GetClickAction()
	loadMoreSnippet := &sushi.ForceLoadMoreAndRemoveSnippet{
		SnippetIds: []string{id},
	}
	clickAction.SetForceLoadMoreAndRemoveSnippet(loadMoreSnippet)

	snippet := &sushi.FitsoTextSnippetType7Snippet{
		Id:          id,
		Title:       title,
		Button:      button,
		BgColor:     bgColor,
		BorderColor: borderColor,
		ClickAction: clickAction,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: separator,
	}

	section := &sportModel.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType7: snippet,
		SnippetConfig:         snippetConfig,
	}

	key := fmt.Sprintf("outside_%d_button_shown", distance)
	s.AddPostbackParam(key, true)

	return section
}

func (s *SportPageTemplate) RemoveFacilityIdsFromPrevious(previousIds []int32, moreThanXKmsFacilityIds []int32) []int32 {
	previousIdsMap := make(map[int32]int8)
	for _, id := range previousIds {
		previousIdsMap[id] = 1
	}
	for _, id := range moreThanXKmsFacilityIds {
		delete(previousIdsMap, id)
	}

	updatedPreviousIds := make([]int32, 0)
	for key := range previousIdsMap {
		updatedPreviousIds = append(updatedPreviousIds, key)
	}
	return updatedPreviousIds
}

func (s *SportPageTemplate) GetFacilityCard(ctx context.Context, facility *facilitySportPB.Facility) *sportModel.ResultSection {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.ResSnippetType3,
	}
	resSnippet := &sushi.ResType3Snippet{}

	title, _ := sushi.NewTextSnippet(facility.DisplayName)
	resSnippet.Title = title

	sportNames := []string{}
	for _, sport := range facility.Sports {
		sportNames = append(sportNames, sport.SportName)
	}
	subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
	subtitle1.SetNumberOfLines(1)
	resSnippet.Subtitle1 = subtitle1

	image, _ := sushi.NewImage(facility.DisplayPicture)
	resSnippet.Image = image

	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: facility.Deeplink,
	}
	clickAction.SetDeeplink(deeplink)
	resSnippet.ClickAction = clickAction

	ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

	if len(facility.Tag) > 0 {
		ratingTitle, _ := sushi.NewTextSnippet(facility.Tag)

		if !featuresupport.SupportsNewColor(ctx) {
			color := sushi.ColorTypeBlue
			if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeTeal
			} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeOrange
			}
			ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
			ratingSnippetBlockItem.BgColor = ratingBgColor
		} else {
			if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				ratingSnippetBlockItem.BgColor = ratingBgColor
			} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
				ratingSnippetBlockItem.BgColor = ratingBgColor
			} else {
				// gradient for new tag
				tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
				gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
				ratingSnippetBlockItem.Gradient = gradient
			}
		}

		ratingSnippetBlockItem.Title = ratingTitle
		ratingSnippetBlockItem.Size = sushi.RatingSize300

	} else {
		ratingTitle, _ := sushi.NewTextSnippet(facility.Rating)
		ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

		ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if featuresupport.SupportsNewColor(ctx) {
			ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}

		ratingSnippetBlockItem.Title = ratingTitle
		ratingSnippetBlockItem.BgColor = ratingBgColor
		ratingSnippetBlockItem.RightIcon = ratingIcon
	}

	ratingSnippet := sushi.RatingSnippet{
		Type:  sushi.RatingTypeTagV2,
		TagV2: ratingSnippetBlockItem,
	}

	resSnippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

	var tagTitle *sushi.TextSnippet
	_, div := math.Modf(facility.Distance)
	if (div >= 0.95 || div < 0.05) && div != 0 {
		tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
	} else {
		tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
	}

	tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	tagTitle.SetColor(tagColor)

	topRightTag := &sushi.Tag{
		Title:        tagTitle,
		Size:         sushi.TagSizeMedium,
		Transparency: 0.2,
	}
	resSnippet.TopRightTag = topRightTag

	if len(facility.Attributes) > 0 {
		bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
		for _, attribute := range facility.Attributes {
			image, _ := sushi.NewImage(attribute.Image)

			name, _ := sushi.NewTextSnippet(attribute.Title)
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			name.SetColor(color)

			if attribute.Type == attributeMaxSafety {
				image.SetAspectRatio(2.277777)
				image.SetType(sushi.ImageTypeRectangle)
			} else if attribute.Type == attributePeopleCount {
				image.SetAspectRatio(1)
				image.SetType(sushi.ImageTypeCircle)
			} else if attribute.Type == attributeVaccinatedStaff {
				image.SetAspectRatio(1)
				image.SetType(sushi.ImageTypeRectangle)
			}

			bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
				Title:     name,
				LeftImage: image,
			}
			bottomItems = append(bottomItems, bottomContainerSnippetItem)
		}
		resSnippet.BottomContainerSnippetItems = &bottomItems
	}

	horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
		Text: facility.SubzoneName,
	}
	horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
		HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
	}
	resSnippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

	var multiTagItems []sushi.MultiTagSnippetItem
	for _, sport := range facility.Sports {
		if sport.SportId != s.PageRequest.SportId {
			continue
		}
		for _, attribute := range sport.FsInfo {
			multiTagSnippetItemTitle, _ := sushi.NewTextSnippet(attribute)
			if !featuresupport.SupportsNewColor(ctx) {
				multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
				multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
				gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

				multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
					Title:    multiTagSnippetItemTitle,
					Gradient: gradient,
				})
			} else {
				multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

				multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
					Title:   multiTagSnippetItemTitle,
					BgColor: multiTagSnippetItemColor,
				})
			}
		}
	}
	if len(multiTagItems) > 0 {
		resSnippet.TopTags = &multiTagItems
	}

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = facility.FacilityId
	tapPayload["sport_id"] = s.PageRequest.SportId
	tapPayload["source"] = "sports"
	tapEname := &sushi.EnameData{
		Ename: "facility_card_tap",
	}
	facilityCardEvents := sushi.NewClevertapEvents()
	facilityCardEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
	resSnippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	s.SetJumboTracking(s.PageRequest.SportId, facility.FacilityId)
	resSnippet.JumboTracking = s.JumboTracking

	section := &sportModel.ResultSection{
		LayoutConfig:    layoutConfig,
		ResType3Snippet: resSnippet,
	}
	return section
}

func (s *SportPageTemplate) SetJumboTracking(sportId int32, facilityId int32) {
	jumboTapPayload := make(map[string]interface{})
	jumboTapPayload["sport_id"] = sportId
	jumboTapPayload["facility_id"] = facilityId

	jumboFacilityCardEvents := jumbo.NewEvents()
	facilityCardTapEvent := jumbo.GetEventNameObject(jumbo.FacilityCardTap)
	jumboFacilityCardEvents.SetTap(facilityCardTapEvent)
	jumboTrackItem := jumbo.GetJumboTrackItem(jumboTapPayload, jumboFacilityCardEvents, jumbo.EventsTableName)
	s.JumboTracking = []*jumbo.Item{jumboTrackItem}
}

func (s *SportPageTemplate) ReplicateOldPostbackParamsToNewOne(ctx context.Context) {
	var temp map[string]interface{}
	json.Unmarshal([]byte(s.PageRequest.PostbackParams), &temp)
	s.PageData.PostbackParams = temp
}

func (s *SportPageTemplate) SetNearByCentersSection(ctx context.Context) {
	s.ReplicateOldPostbackParamsToNewOne(ctx)

	nearbyFacilitiesResponse := s.GetNearbyFacilities(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Facilities) == 0 {
		return
	}

	s.SetHasMore(nearbyFacilitiesResponse.HasMore)

	s.AddPostbackParam("previous_facility_ids", nearbyFacilitiesResponse.PreviousFacilityIds)

	for _, facility := range nearbyFacilitiesResponse.Facilities {
		s.AddSection(s.GetFacilityCard(ctx, facility))
	}
}

func (s *SportPageTemplate) SetStickyHeaderSection(ctx context.Context) {
	response := s.GetSportDetails(ctx)
	if response == nil {
		return
	}

	sportData := response.Sport[0]
	title, _ := sushi.NewTextSnippet(sportData.SportName)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	title.SetFont(font)

	image, _ := sushi.NewImage(sportData.Icon)
	bgColor, _ := sushi.NewColor(sushi.ColorType(sportData.IconBackgroundColor), sushi.ColorTint(sportData.IconTint))
	image.SetHeight(40)
	image.SetWidth(40)
	image.SetColor(bgColor)

	stickyHeader := &sportModel.StickyHeader{
		Title:      title,
		RightImage: image,
	}
	s.StickyHeader = stickyHeader
}

func (s *SportPageTemplate) SetHeaderSection(ctx context.Context) {
	response := s.GetNearbyFacilities(ctx)
	if response == nil {
		return
	}
	if len(response.Facilities) == 0 {
		return
	}

	var items []*sushi.HeaderType4SnippetItem
	for _, facility := range response.Facilities {
		image, _ := sushi.NewImage(facility.DisplayPicture)
		image.SetAspectRatio(1.33)
		headerBottomContainerTitle, _ := sushi.NewTextSnippet(facility.DisplayName)
		headerBottomContainerSubtitle, _ := sushi.NewTextSnippet(facility.DistanceText)
		headerBottomContainerPrefixIconColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		headerBottomContainerPrefixIcon, _ := sushi.NewIcon(sushi.LocationIcon, headerBottomContainerPrefixIconColor)
		headerBottomContainerSubtitle.SetPrefixIcon(headerBottomContainerPrefixIcon)

		headerBottom := &sushi.BottomContainerSnippetItem{
			Title:    headerBottomContainerTitle,
			SubTitle: headerBottomContainerSubtitle,
		}
		sportHeader := &sushi.HeaderType4SnippetItem{
			Image:           image,
			BottomContainer: headerBottom,
		}
		items = append(items, sportHeader)
	}

	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.HeaderSnippetType4,
	}

	header := &sushi.HeaderType4SnippetLayout{
		LayoutConfig: headerLayout,
		HeaderType4Snippet: &sushi.HeaderType4Snippet{
			Items:       items,
			AspectRatio: 1.33,
		},
	}
	s.Header = header
}

func (s *SportPageTemplate) SetFooterSection(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	source := "sports"

	subDetailsResponse := s.GetUserSubscriptionStatus(ctx)
	if subDetailsResponse == nil {
		return
	}

	subscriptionStatus := subDetailsResponse.SubscriptionStatus
	tomorrowSubscription := subDetailsResponse.TomorrowParentSubscription || subDetailsResponse.TomorrowChildSubscription
	loggedInUser := util.GetUserIDFromContext(ctx)
	productCategoryId := subDetailsResponse.ProductCategoryId

	var productSubtext string
	if len(subDetailsResponse.SuggestedProducts) > 0 {
		subscriptionProduct := subDetailsResponse.SuggestedProducts[0]
		productSubtext = fmt.Sprintf("₹%d for %d %ss", int32(subscriptionProduct.RetailPrice), subscriptionProduct.Duration, subscriptionProduct.DurationUnit)
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			perMonthPrice := int32(subscriptionProduct.RetailPrice)
			if subscriptionProduct.Duration > 0 {
				perMonthPrice = int32(subscriptionProduct.RetailPrice) / subscriptionProduct.Duration
			}
			productSubtext = fmt.Sprintf("starts from ₹%d per month", int32(perMonthPrice))
		}
	}

	footerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footerButtonItems := make([]sushi.FooterSnippetType2ButtonItem, 0)

	clickActionPurchase := sushi.GetClickAction()
	deeplinkPurchase := sushi.Deeplink{
		URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
	}
	clickActionPurchase.SetDeeplink(&deeplinkPurchase)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = loggedInUser
	tapPayload["city_id"] = util.GetCityIDFromContext(ctx)
	tapPayload["sport_id"] = s.PageRequest.SportId
	tapPayload["source"] = source

	switch subscriptionStatus {
	case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
		tapPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
		if !subDetailsResponse.ActiveParentSubscriptions {
			tapEname := &sushi.EnameData{
				Ename: "buy_membership_button_tap",
			}
			buttonItem1Events := sushi.NewClevertapEvents()
			buttonItem1Events.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

			buttonItem1 := sushi.FooterSnippetType2ButtonItem{
				Type:              "solid",
				Text:              "Become a cultpass PLAY member",
				Subtext:           productSubtext,
				ClickAction:       clickActionPurchase,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			footerButtonItems = append(footerButtonItems, buttonItem1)
		} else {
			var openBottomSheet int32
			buttonText := "Book a slot for "
			if featuresupport.SupportsFullPageSlot(ctx) {
				openBottomSheet = 0
			} else {
				openBottomSheet = 1
			}

			footerButtonClickAction := sushi.GetClickAction()

			loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)
			var openBuddyList bool
			if subDetailsResponse.ActiveChildSubscriptions {
				openBuddyList = true
			} else if subDetailsResponse.ProductCategoryId == common.SinglekeyCategoryID && s.PageRequest.SportId != common.BADMINTON_SPORT_ID {
				tapPayload["product"] = "singlekey_trial"
				buttonText = "Book a trial slot for "
				openBuddyList = false
			}

			addAgeDeeplink, addBookingFlowDeeplink, addMedicalFormDeeplink := false, false, false

			if openBuddyList == true {
				addBookingFlowDeeplink = true
			} else { // single user
				if !featuresupport.SupportsMedicalForm(ctx) {
					if loggedInUserAge >= util.GetMinAgeForBooking(false, productCategoryId) {
						addBookingFlowDeeplink = true
					} else {
						addAgeDeeplink = true
					}
				} else { // supports medical form UI
					if util.NeedMedicalDetails(ctx, loggedInUser) {
						addMedicalFormDeeplink = true
					} else if loggedInUserAge < util.GetMinAgeForBooking(false, productCategoryId) {
						addAgeDeeplink = true
					} else {
						addBookingFlowDeeplink = true
					}
				}
			}

			if addBookingFlowDeeplink {

				deeplink, buddyListOpen := util.GetBookingFlowDeepLinkForSports(ctx, s.PageRequest.SportId, openBuddyList, source, openBottomSheet)
				if !buddyListOpen && subDetailsResponse.ProductCategoryId == common.SinglekeyCategoryID && s.PageRequest.SportId != common.BADMINTON_SPORT_ID && subDetailsResponse.SinglekeyTrialNotEligible {
					if util.IsAndroid(ctx) {
						return
					}
					customAlert := util.GetTrialUsedupPopup(ctx, subDetailsResponse)
					footerButtonClickAction.SetCustomAlertAction(customAlert)
				} else {
					footerButtonClickAction.SetDeeplink(&deeplink)
				}
			} else if addAgeDeeplink {
				obj := map[string]interface{}{
					"sport_id":     s.PageRequest.SportId,
					"bottom_sheet": openBottomSheet,
					"source":       source,
				}

				post_params, _ := json.Marshal(obj)
				payload := map[string]interface{}{
					"post_action":     "slots_get_sport",
					"user_id":         loggedInUser,
					"postback_params": string(post_params),
				}
				postback_params, _ := json.Marshal(payload)
				footerButtonClickAction.Type = sushi.ClickActionOpenAgeBottomSheet
				footerButtonClickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, false, productCategoryId)
			} else if addMedicalFormDeeplink {
				payload := map[string]interface{}{
					"page_type":    common.PAGE_TYPE_SPORT,
					"user_id":      loggedInUser,
					"sport_id":     s.PageRequest.SportId,
					"source":       source,
					"bottom_sheet": openBottomSheet,
				}
				postbackParams, _ := json.Marshal(payload)
				alert := util.GetCompleteMedicalDetailsAlert(ctx, loggedInUser, string(postbackParams))
				footerButtonClickAction.SetCustomAlertAction(alert)
			}

			tapEname := &sushi.EnameData{
				Ename: "book_sport_slot_button_tap",
			}
			buttonItem1Events := sushi.NewClevertapEvents()
			buttonItem1Events.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

			buttonItem1 := sushi.FooterSnippetType2ButtonItem{
				Type:              "solid",
				Text:              buttonText + s.PageData.SportDetails.Sport[0].SportName,
				ClickAction:       footerButtonClickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			footerButtonItems = append(footerButtonItems, buttonItem1)
		}
		break

	case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
		if tomorrowSubscription {
			var openBottomSheet int32
			buttonText := "Book a slot for "
			if featuresupport.SupportsFullPageSlot(ctx) {
				openBottomSheet = 0
			} else {
				openBottomSheet = 1
			}

			footerButtonClickAction := sushi.GetClickAction()

			loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)
			var openBuddyList bool
			if subDetailsResponse.ActiveChildSubscriptions {
				openBuddyList = true
			} else if subDetailsResponse.ProductCategoryId == common.SinglekeyCategoryID && s.PageRequest.SportId != common.BADMINTON_SPORT_ID {
				tapPayload["product"] = "singlekey_trial"
				buttonText = "Book a trial slot for "
				openBuddyList = false
			}

			addAgeDeeplink, addBookingFlowDeeplink, addMedicalFormDeeplink := false, false, false

			if openBuddyList == true {
				addBookingFlowDeeplink = true
			} else { // single user
				if !featuresupport.SupportsMedicalForm(ctx) {
					if loggedInUserAge >= util.GetMinAgeForBooking(false, productCategoryId) {
						addBookingFlowDeeplink = true
					} else {
						addAgeDeeplink = true
					}
				} else { // supports medical form UI
					if util.NeedMedicalDetails(ctx, loggedInUser) {
						addMedicalFormDeeplink = true
					} else if loggedInUserAge < util.GetMinAgeForBooking(false, productCategoryId) {
						addAgeDeeplink = true
					} else {
						addBookingFlowDeeplink = true
					}
				}
			}

			if addBookingFlowDeeplink {
				deeplink, buddyListOpen := util.GetBookingFlowDeepLinkForSports(ctx, s.PageRequest.SportId, openBuddyList, source, openBottomSheet)
				if !buddyListOpen && subDetailsResponse.ProductCategoryId == common.SinglekeyCategoryID && s.PageRequest.SportId != common.BADMINTON_SPORT_ID && subDetailsResponse.SinglekeyTrialNotEligible {
					if util.IsAndroid(ctx) {
						return
					}
					customAlert := util.GetTrialUsedupPopup(ctx, subDetailsResponse)
					footerButtonClickAction.SetCustomAlertAction(customAlert)
				} else {
					footerButtonClickAction.SetDeeplink(&deeplink)
				}
			} else if addAgeDeeplink {
				obj := map[string]interface{}{
					"sport_id":     s.PageRequest.SportId,
					"bottom_sheet": openBottomSheet,
					"source":       source,
				}

				post_params, _ := json.Marshal(obj)
				payload := map[string]interface{}{
					"post_action":     "slots_get_sport",
					"user_id":         loggedInUser,
					"postback_params": string(post_params),
				}
				postback_params, _ := json.Marshal(payload)
				footerButtonClickAction.Type = sushi.ClickActionOpenAgeBottomSheet
				footerButtonClickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, false, productCategoryId)
			} else if addMedicalFormDeeplink {
				payload := map[string]interface{}{
					"page_type":    common.PAGE_TYPE_SPORT,
					"user_id":      loggedInUser,
					"sport_id":     s.PageRequest.SportId,
					"source":       source,
					"bottom_sheet": openBottomSheet,
				}
				postbackParams, _ := json.Marshal(payload)
				alert := util.GetCompleteMedicalDetailsAlert(ctx, loggedInUser, string(postbackParams))
				footerButtonClickAction.SetCustomAlertAction(alert)
			}

			tapEname := &sushi.EnameData{
				Ename: "book_sport_slot_button_tap",
			}
			buttonItem1Events := sushi.NewClevertapEvents()
			buttonItem1Events.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

			buttonItem1 := sushi.FooterSnippetType2ButtonItem{
				Type:              "solid",
				Text:              buttonText + s.PageData.SportDetails.Sport[0].SportName,
				ClickAction:       footerButtonClickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			footerButtonItems = append(footerButtonItems, buttonItem1)
		}
		break

	case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
		tapPayload["membership"] = "TAKE_TRIAL"
		tapEname := &sushi.EnameData{
			Ename: "buy_membership_button_tap",
		}
		buttonItem1Events := sushi.NewClevertapEvents()
		buttonItem1Events.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

		buttonItemPurchase := sushi.FooterSnippetType2ButtonItem{
			ClickAction:       clickActionPurchase,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}

		clickAction := sushi.GetClickAction()
		if loggedInUser > 0 {
			loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)
			// Age validation check
			payload := structs.BookingSlotsV3{
				SportId: s.PageRequest.SportId,
				Source:  "sport_trial",
			}

			params, _ := json.Marshal(payload)
			if loggedInUserAge < util.GetMinAgeForBooking(true, productCategoryId) {
				payload := map[string]interface{}{
					"post_action":     "trial_slots",
					"user_id":         loggedInUser,
					"postback_params": string(params),
				}
				postback_params, _ := json.Marshal(payload)
				clickAction.Type = sushi.ClickActionOpenAgeBottomSheet
				clickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, true, productCategoryId)
			} else {
				deeplink := sushi.Deeplink{
					URL:            util.GetTrialSlotsPageDeeplink(),
					PostbackParams: string(params),
				}
				clickAction.SetDeeplink(&deeplink)
			}
		} else {
			payload := map[string]interface{}{
				"post_action": "trial_slots",
			}

			postback_params, _ := json.Marshal(payload)
			auth := &sushi.Auth{
				PostbackParams: string(postback_params),
				Source:         source,
			}
			clickAction.SetAuth(auth)
		}

		tapEname = &sushi.EnameData{
			Ename: "book_trial_slot_button_tap",
		}
		buttonItem2Events := sushi.NewClevertapEvents()
		buttonItem2Events.SetTap(tapEname)
		trackItem = sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem2Events)

		buttonItemTrial := sushi.FooterSnippetType2ButtonItem{
			ClickAction:       clickAction,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}

		firstTrial := false
		if subDetailsResponse.TrialLeft > 0 && subDetailsResponse.MaxTrials > 0 && subDetailsResponse.TrialLeft == subDetailsResponse.MaxTrials {
			firstTrial = true
		}
		if firstTrial == true || loggedInUser == 0 {

			buttonItemTrial.Type = sushi.FooterButtonTypeSolid
			buttonItemTrial.Text = "Book a free trial session"
			footerButtonItems = append(footerButtonItems, buttonItemTrial)
		} else {
			buttonItemPurchase.Type = sushi.FooterButtonTypeSolid
			buttonItemPurchase.Text = "Become a cultpass PLAY member"
			buttonItemPurchase.Subtext = productSubtext
			footerButtonItems = append(footerButtonItems, buttonItemPurchase)

			buttonItemTrial.Type = sushi.FooterButtonTypeText
			buttonItemTrial.Text = "or book a free trial session"
			footerButtonItems = append(footerButtonItems, buttonItemTrial)
		}

		break

	case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
		tapPayload["membership"] = "TRIAL_SUBSCRIPTION_ACTIVE"
		fallthrough
	case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
		tapPayload["membership"] = "ALL_SUBSCRIPTION_EXPIRED"
		tapEname := &sushi.EnameData{
			Ename: "buy_membership_button_tap",
		}
		buttonItem1Events := sushi.NewClevertapEvents()
		buttonItem1Events.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:              sushi.FooterButtonTypeSolid,
			Text:              "Become a cultpass PLAY member",
			Subtext:           productSubtext,
			ClickAction:       clickActionPurchase,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		footerButtonItems = append(footerButtonItems, buttonItem1)
		break
	}

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &footerButtonItems,
	}
	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       footerLayout,
		FooterSnippetType2: footerSnippet,
	}
	s.Footer = footer
}

func (s *SportPageTemplate) SetPostbackParams(ctx context.Context) {
	serialised, _ := json.Marshal(s.PageData.PostbackParams)
	s.PostbackParams = string(serialised)
}

func (s *SportPageTemplate) SetPageTracking(ctx context.Context) {
	backArrowPayload := make(map[string]interface{})
	backArrowPayload["user_id"] = util.GetUserIDFromContext(ctx)
	backArrowPayload["sport_id"] = s.PageRequest.SportId
	backArrowPayload["source"] = "sports"
	backArrowEname := &sushi.EnameData{
		Ename: "sport_page_back_arrow_tap",
	}

	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := sushi.GetClevertapTrackItem(ctx, backArrowPayload, backArrowEvents)

	landingPayload := make(map[string]interface{})
	landingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	landingPayload["sport_id"] = s.PageRequest.SportId
	landingEname := &sushi.EnameData{
		Ename: "sport_page_landing",
	}

	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, landingPayload, landingEvents)

	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}

	events := jumbo.NewEvents()
	sportPageLandingEvent := jumbo.GetEventNameObject(jumbo.SportPageLanding)
	events.SetPageSuccess(sportPageLandingEvent)
	jumboLandingTrackItem := jumbo.GetJumboTrackItem(landingPayload, events, jumbo.EventsTableName)

	s.JumboTracking = []*jumbo.Item{jumboLandingTrackItem}
}
