package facilitySportController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	facilityModel "bitbucket.org/jogocoin/go_api/api/models/facility"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type AcademyFacilityPageData struct {
	FacilityDetails            *facilitySportPB.Facility
	NearbyFacilities           *facilitySportPB.AcademyFacilitiesResponse
	PostbackParams             map[string]interface{}
	FacilityNonOperationalDays []int32
}

type AcademyFacilityPageTemplate struct {
	PageRequest       facilityModel.GetFacilityRequest `json:"-"`
	PageData          AcademyFacilityPageData          `json:"-"`
	PageHeader        *facilityModel.PageHeader        `json:"page_header,omitempty"`
	Header            *sushi.HeaderType4SnippetLayout  `json:"header,omitempty"`
	Sections          []*facilityModel.ResultSection   `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout  `json:"footer,omitempty"`
	HasMore           bool                             `json:"has_more"`
	PostbackParams    string                           `json:"postback_params,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem           `json:"clever_tap_tracking,omitempty"`
}

func GetAcademyFacilityDetailsC(c *gin.Context) {
	var f facility

	if err := c.ShouldBindUri(&f); err != nil {
		c.JSON(400, gin.H{"msg": err})
		log.Printf("Api: facility, function: GetAcademyFacilityDetailsC, Error: %v", err)
		return
	}

	var requestData facilityModel.GetFacilityRequest
	requestData = c.MustGet("jsonData").(facilityModel.GetFacilityRequest)
	requestData.FacilityID = f.FacilityID
	if queryCourseID, ok := c.GetQuery("course_id"); ok {
		queryCourseIDInt, err := strconv.Atoi(queryCourseID)
		if err == nil {
			requestData.CourseId = int32(queryCourseIDInt)
		}
	}

	requestData.UnmarshalledPostbackParams = getAcademyFacilitiesPostbackParams(requestData.PostbackParams)
	ctx := util.PrepareGRPCMetaData(c)

	template := AcademyFacilityPageTemplate{
		PageRequest: requestData,
		PageData:    AcademyFacilityPageData{},
	}

	if template.PageRequest.PostbackParams == "" {
		template.SetFacilityPreInfoSection(ctx)
		template.SetFacilityInfoSection(ctx)
		template.SetFacilityAmenitiesSection(ctx)
		template.SetCoachDetails(ctx)
		template.SetFacilitySportsTemplate(c)
		//template.SetCallFacilitySection(ctx)
		template.SetNearbySectionHeading(ctx)
		template.SetNearByCentersSection(ctx)
		template.SetHeaderSection(ctx)
		template.SetFooterSection(ctx)
		template.SetPageHeaderSection(ctx)
		template.SetPostbackParams(ctx)
		template.SetPageTracking(ctx)
	} else {
		template.GetFacilityDetails(ctx)
		template.SetNearByCentersSection(ctx)
		template.SetPostbackParams(ctx)
	}
	c.JSON(http.StatusOK, template)
}

func (f *AcademyFacilityPageTemplate) GetFacilityDetails(ctx context.Context) *facilitySportPB.Facility {
	if f.PageData.FacilityDetails != nil {
		return f.PageData.FacilityDetails
	}
	facilityClient := util.GetFacilitySportClient()
	facilityDetailRequest := &facilitySportPB.GetFacilityDetailsRequest{
		FacilityId: f.PageRequest.FacilityID,
	}
	facilityDetailsResponse, err := facilityClient.GetAcademysFacilityDetails(ctx, facilityDetailRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetAcademysFacilityDetails, Error: %v", err)
		return nil
	}
	if len(facilityDetailsResponse.Sports) == 0 {
		log.Printf("Api: facility, function: GetAcademysFacilityDetails, Error: no sport for facility id %d", f.PageRequest.FacilityID)
	} else if f.PageRequest.SportID == 0 {
		f.PageRequest.SportID = facilityDetailsResponse.Sports[0].SportId
	}

	f.PageData.FacilityDetails = facilityDetailsResponse
	f.PageData.FacilityNonOperationalDays = getFacilityNonOperationDays(facilityDetailsResponse.Sports)
	return f.PageData.FacilityDetails
}

func (f *AcademyFacilityPageTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.AcademyFacilitiesResponse {
	if f.PageData.NearbyFacilities != nil {
		return f.PageData.NearbyFacilities
	}

	previousFacilityIds := f.PageRequest.UnmarshalledPostbackParams.PreviousFacilityIds
	if len(previousFacilityIds) == 0 { // first call
		previousFacilityIds = append(previousFacilityIds, f.PageRequest.FacilityID)
	}

	facilityClient := util.GetFacilitySportClient()
	allFacilitiesRequest := &facilitySportPB.AcademyFacilitiesRequest{
		CourseId:            f.PageRequest.CourseId,
		Count:               common.ACADEMY_FACILITY_PAGE_FACILITY_COUNT,
		PreviousFacilityIds: previousFacilityIds,
	}

	nearbyFacilitiesResponse, err := facilityClient.GetAcademyFacilities(ctx, allFacilitiesRequest)
	if err != nil {
		log.Printf("Api: facility, function: GetNearbyFacilities, Error: %v", err)
		return nil
	}

	f.PageData.NearbyFacilities = nearbyFacilitiesResponse
	return f.PageData.NearbyFacilities
}

func getAcademyFacilitiesPostbackParams(postbackParams string) *facilityModel.RequestPostbackParams {
	var temp map[string]interface{}
	json.Unmarshal([]byte(postbackParams), &temp)

	previousFacilityIds := make([]int32, 0)
	if facilityIds, ok := temp["previous_facility_ids"]; ok {
		facilityIdsInterface, _ := facilityIds.([]interface{})
		for _, val := range facilityIdsInterface {
			previousFacilityIds = append(previousFacilityIds, int32(val.(float64)))
		}
	}
	requestPostbackParams := &facilityModel.RequestPostbackParams{
		PreviousFacilityIds: previousFacilityIds,
	}
	return requestPostbackParams
}

func (f *AcademyFacilityPageTemplate) AddSection(section *facilityModel.ResultSection) {
	f.Sections = append(f.Sections, section)
}

func (f *AcademyFacilityPageTemplate) AddFooter(footer *sushi.FooterSnippetType2Layout) {
	f.Footer = footer
}

func (f *AcademyFacilityPageTemplate) AddHeader(header *sushi.HeaderType4SnippetLayout) {
	f.Header = header
}

func (f *AcademyFacilityPageTemplate) SetHasMore(hasMore bool) {
	f.HasMore = hasMore
}

func (f *AcademyFacilityPageTemplate) SetPageHeader(pageHeader *facilityModel.PageHeader) {
	f.PageHeader = pageHeader
}

func (f *AcademyFacilityPageTemplate) SetPostbackParams(ctx context.Context) {
	if len(f.PageData.PostbackParams) == 0 {
		return
	}
	postbackParam, _ := json.Marshal(f.PageData.PostbackParams)
	f.PostbackParams = string(postbackParam)
}

func (f *AcademyFacilityPageTemplate) AddPostbackParam(key string, val interface{}) {
	if f.PageData.PostbackParams == nil {
		f.PageData.PostbackParams = make(map[string]interface{})
	}
	f.PageData.PostbackParams[key] = val
}

func (f *AcademyFacilityPageTemplate) SetFacilityPreInfoSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil || len(response.Attributes) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.TickerSnippetType1,
	}
	items := make([]sushi.TickerSnippetType1SnippetItem, 0)

	for _, attr := range response.Attributes {
		image, _ := sushi.NewImage(attr.Image)
		if attr.Type == attributeMaxSafety {
			image.SetAspectRatio(2.277777)
			image.SetType(sushi.ImageTypeRectangle)
		} else if attr.Type == attributePeopleCount {
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeCircle)
		} else if attr.Type == attributeVaccinatedStaff {
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeRectangle)
		}

		title, _ := sushi.NewTextSnippet(attr.Title)
		item := sushi.TickerSnippetType1SnippetItem{
			LeftImage: image,
			Title:     title,
		}
		items = append(items, item)
	}

	bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	preInfoSnippet := &sushi.TickerSnippetType1Snippet{
		Items:   &items,
		BgColor: bg_color,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		TickerSnippetType1: preInfoSnippet,
	}
	f.AddSection(section)
}

func (f *AcademyFacilityPageTemplate) SetCoachDetails(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)
	if response == nil {
		return
	}
	if len(response.CoachDetails) == 0 {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType49,
		SectionCount: 1,
	}
	if len(response.CoachDetails) > 1 {
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	} else {
		layoutConfig.LayoutType = sushi.LayoutTypeGrid
	}

	coachDetailsSnippet := sushi.V2ImageTextSnippetType49Snippet{}

	title, _ := sushi.NewTextSnippet("Coach's Credentials")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title.SetFont(font)
	coachDetailsSnippet.Title = title

	items := make([]sushi.V2ImageTextSnippetType49SnippetItem, 0)
	for _, val := range response.CoachDetails {
		item := sushi.V2ImageTextSnippetType49SnippetItem{}

		title, _ := sushi.NewTextSnippet(val.Name)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
		title.SetFont(font)
		title.SetColor(color)
		item.Title = title

		image, _ := sushi.NewImage(val.CoachImage)
		image.SetAspectRatio(float32(1))
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(56)
		image.SetWidth(56)
		item.LeftImage = image

		if len(val.DisplayText) == 0 {
			return
		}
		points := make([]sushi.SubtitleListItem, 0)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint600)
		for _, data := range val.DisplayText {
			point := sushi.SubtitleListItem{}

			title, _ := sushi.NewTextSnippet(data)
			title.SetFont(font)
			title.SetColor(color)
			point.Title = title

			icon, _ := sushi.NewIcon(sushi.PointIcon, color)
			point.Icon = icon
			points = append(points, point)
		}
		item.SubtitlesList = &points
		items = append(items, item)
	}
	coachDetailsSnippet.Items = &items

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeThick, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:             layoutConfig,
		SnippetConfig:            snippetConfig,
		V2ImageTextSnippetType49: &coachDetailsSnippet,
	}
	f.AddSection(section)
}

func (f *AcademyFacilityPageTemplate) SetFacilityInfoSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.TextSnippetType8,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	facilitySnippet := sushi.TextSnippetType8Snippet{}

	title, _ := sushi.NewTextSnippet(response.DisplayName)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetNumberOfLines(2)
	title.SetFont(font)
	facilitySnippet.Title = title

	subtitle1, _ := sushi.NewTextSnippet(response.DisplayAddress)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle1.SetFont(font)
	subtitle1.SetColor(color)
	facilitySnippet.SubTitle1 = subtitle1

	facilityNonOperationalDays := f.PageData.FacilityNonOperationalDays
	if len(facilityNonOperationalDays) > 0 {
		dayStrings := make([]string, 0)
		for _, day := range facilityNonOperationalDays {
			dayStrings = append(dayStrings, time.Weekday(day).String())
		}
		var subtitle2Text string
		if len(dayStrings) == 1 {
			subtitle2Text = fmt.Sprintf("Closed on %s", dayStrings[0])
		} else {
			subtitle2Text = fmt.Sprintf(
				"Closed on %s and %s",
				strings.Join(dayStrings[0:len(facilityNonOperationalDays)-1], ", "),
				dayStrings[len(facilityNonOperationalDays)-1],
			)
		}
		subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		colorType := sushi.ColorTypeOrange
		if featuresupport.SupportsNewColor(ctx) {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
		icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
		subtitle2.SetFont(font)
		subtitle2.SetColor(color)
		subtitle2.SetPrefixIcon(icon)
		facilitySnippet.SubTitle2 = subtitle2
	}

	ratingSnippet := ratingSnippet(ctx, response.Tag, response.Rating)
	ratingSnippet.Size = sushi.RatingSize400
	facilitySnippet.RatingSnippet = &sushi.RatingSnippet{
		Type:  sushi.RatingTypeTagV2,
		TagV2: ratingSnippet,
	}

	// get directions
	clickAction := sushi.GetClickAction()
	openMap := sushi.OpenMap{
		Latitude:  fmt.Sprintf("%f", response.Latitude),
		Longitude: fmt.Sprintf("%f", response.Longitude),
	}
	clickAction.SetOpenMap(&openMap)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["course_id"] = f.PageRequest.CourseId
	tapPayload["source"] = "facility"
	tapEname := &sushi.EnameData{
		Ename: "academy_get_directions_button_tap",
	}
	getDrectionEvents := sushi.NewClevertapEvents()
	getDrectionEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, getDrectionEvents)

	directionColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	directionIcon, _ := sushi.NewIcon(sushi.DirectionIcon, directionColor)
	directionButton, _ := sushi.NewButton(sushi.ButtontypeText)
	directionButton.SetText("Get directions")
	directionButton.SetSize(sushi.ButtonSizeMedium)
	directionButton.SetPrefixIcon(directionIcon)
	directionButton.SetColor(directionColor)
	directionButton.SetClickAction(clickAction)
	directionButton.AddClevertapTrackingItem(trackItem)
	facilitySnippet.RightButton = directionButton

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	facilitySnippet.BgColor = bgColor

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:     layoutConfig,
		SnippetConfig:    snippetConfig,
		TextSnippetType8: &facilitySnippet,
	}
	f.AddSection(section)
}

func (f *AcademyFacilityPageTemplate) SetFacilityAmenitiesSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	if len(response.Amenity) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
		ShouldResize: true,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	ameneties := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _, val := range response.Amenity {
		item := sushi.ImageTextSnippetType30SnippetItem{}
		image, _ := sushi.NewImage(val.Icon)
		image.SetAspectRatio(float32(1))
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(16)
		image.SetWidth(16)

		title, _ := sushi.NewTextSnippet(val.AmenityName)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		title.SetFont(font)
		title.SetColor(color)

		item.Image = image
		item.Title = title

		ameneties = append(ameneties, item)
	}

	if len(ameneties)-defaultFacilityCollapsedCount > 1 {
		layoutConfig.CollapsedCount = defaultFacilityCollapsedCount
	}

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	amenitySnippet := &sushi.ImageTextSnippetType30Snippet{
		BgColor: bgColor,
		Items:   &ameneties,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		SnippetConfig:          snippetConfig,
		ImageTextSnippetType30: amenitySnippet,
	}
	f.AddSection(section)
}

func (f *AcademyFacilityPageTemplate) SetCallFacilitySection(ctx context.Context) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Contact <EMAIL>.")

	button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)

	button.SetText("Call")
	button.SetPrefixIcon(callIcon)
	button.SetClickAction(clickAction)

	item.BgColor = bgColor
	item.Title = title
	//item.RightButton = button
	rightButton1, _ := sushi.NewButton(sushi.ButtontypeText)
	rightButton1.SetText(" ")
	item.RightButton = rightButton1
	
	item.BorderColor = borderColor
	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	section := &facilityModel.ResultSection{
		LayoutConfig:               layoutConfig,
		SnippetConfig:              snippetConfig,
		FitsoImageTextSnippetType7: snippet,
	}
	f.AddSection(section)
}

func (f *AcademyFacilityPageTemplate) SetFacilitySportsTemplate(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	facilityDetailsResponse := f.GetFacilityDetails(ctx)
	if facilityDetailsResponse == nil {
		return
	}

	loggedInUserID := util.GetUserIDFromContext(ctx)
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	title, _ := sushi.NewTextSnippet("COURSES AVAILABLE HERE")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["source"] = "facility"
	sectionImpressionPayload["facility_id"] = f.PageRequest.FacilityID
	sectionImpressionPayload["course_id"] = f.PageRequest.CourseId
	impressionEname := &sushi.EnameData{
		Ename: "academy_facility_sports_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	f.AddSection(sectionHeaderSection)

	facilityNonOperationalDays := f.PageData.FacilityNonOperationalDays

	for _, sport := range facilityDetailsResponse.Sports {
		sportLayoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType33,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		item := sushi.ImageTextSnippetType33SnippetItem{}
		item.IsSelectable = true
		item.IsSelected = sport.SportId == f.PageRequest.SportID
		leftImage, _ := sushi.NewImage(sport.Icon)
		leftImage.SetAspectRatio(float32(1))
		leftImage.SetHeight(42)
		leftImage.SetType(sushi.ImageTypeCircle)
		sportImageColor, _ := sushi.NewColor(sushi.ColorType(sport.IconBackgroundColor), sushi.ColorTint(sport.IconTint))
		leftImage.SetColor(sportImageColor)
		item.LeftImage = leftImage

		title, _ := sushi.NewTextSnippet(sport.SportName)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		title.SetFont(font)
		title.SetColor(color)
		item.Title = title

		if sport.SportSubtitle != "" {
			subtitle1, _ := sushi.NewTextSnippet(sport.SportSubtitle)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			subtitle1.SetFont(font)
			subtitle1.SetColor(color)
			item.SubTitle1 = subtitle1
		}

		if sport.Tag == common.OPENING_FACILITY_SPORT_TAG {
			colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleTag, _ := sushi.NewTextSnippet(common.OPENING_FACILITY_SPORT_TAG)
			titleTag.SetColor(colorTag)

			bgColorTag, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if isNewColorSupported {
				bgColorTag, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
			}
			tag := &sushi.Tag{
				Title:   titleTag,
				BgColor: bgColorTag,
			}
			item.Tag1 = tag
		}

		if sport.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
			colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleTag, _ := sushi.NewTextSnippet(common.ONHOLD_FACILITY_SPORT_TAG)
			titleTag.SetColor(colorTag)

			bgColorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				bgColorType = sushi.ALERT_LIGHT_THEME
			}
			bgColorTag, _ := sushi.NewColor(bgColorType, sushi.ColorTint500)
			tag := &sushi.Tag{
				Title:   titleTag,
				BgColor: bgColorTag,
			}
			item.Tag1 = tag
		}

		filteredNonOperationalDays := sharedFunc.FilterIntegerArray(facilityNonOperationalDays, sport.NonOperationalDays)
		if len(filteredNonOperationalDays) > 0 {
			dayStrings := make([]string, 0)
			for _, day := range filteredNonOperationalDays {
				dayStrings = append(dayStrings, time.Weekday(day).String())
			}
			var subtitle2Text string
			if len(dayStrings) == 1 {
				subtitle2Text = fmt.Sprintf("Closed on %s", dayStrings[0])
			} else {
				subtitle2Text = fmt.Sprintf(
					"Closed on %s and %s",
					strings.Join(dayStrings[0:len(filteredNonOperationalDays)-1], ", "),
					dayStrings[len(filteredNonOperationalDays)-1],
				)
			}
			subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			colorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_LIGHT_THEME
			}
			color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
			icon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, color)
			subtitle2.SetFont(font)
			subtitle2.SetColor(color)
			subtitle2.SetPrefixIcon(icon)
			item.SubTitle2 = subtitle2
		}

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["facility_id"] = f.PageRequest.FacilityID
		tapPayload["course_id"] = f.PageRequest.CourseId
		tapPayload["sport_id"] = sport.SportId
		tapPayload["source"] = "facility"

		footerButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
		footerButton.SetText(fmt.Sprintf("Book a trial class for %s", sport.SportName))
		footerButtonClickAction := sushi.GetClickAction()

		if loggedInUserID == 0 {
			authTitle, _ := sushi.NewTextSnippet("Please login to book your free trial class. Enter your phone number to login using OTP.")
			payload := map[string]interface{}{
				"post_action": "academy_facility_trial_member_select",
				"sport_id":    sport.SportId,
				"fs_id":       sport.FsId,
			}
			postbackParams, _ := json.Marshal(payload)
			auth := &sushi.Auth{
				Title:          authTitle,
				PostbackParams: string(postbackParams),
				Source:         "academy_facility_page",
			}
			footerButtonClickAction.SetAuth(auth)
		} else {
			footerButtonClickAction = GetAcademyFacilityTrialButtonClickAction(ctx, sport.SportId, sport.FsId)
		}

		footerButton.SetClickAction(footerButtonClickAction)

		tapPayload["membership"] = "TAKE_TRIAL"
		tapEname := &sushi.EnameData{
			Ename: "academy_book_trial_button_tap",
		}
		buttonEvents := sushi.NewClevertapEvents()
		buttonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonEvents)
		footerButton.AddClevertapTrackingItem(trackItem)

		changeBottomButtton := &sushi.ChangeBottomButton{
			ButtonId: "book_slot",
			Button:   footerButton,
		}
		changeBottomButttonClickAction := sushi.GetClickAction()
		changeBottomButttonClickAction.SetChangeBottomButton(changeBottomButtton)
		item.ClickAction = changeBottomButttonClickAction

		items := make([]sushi.ImageTextSnippetType33SnippetItem, 0)
		items = append(items, item)

		snippet := &sushi.ImageTextSnippetType33Snippet{
			Items: &items,
		}
		section := &facilityModel.ResultSection{
			LayoutConfig:           sportLayoutConfig,
			Id:                     "select_sport",
			ImageTextSnippetType33: snippet,
		}
		f.AddSection(section)
	}
}

func GetAcademyFacilityTrialButtonClickAction(ctx context.Context, sportID int32, fsId int32) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()

	payload := map[string]int32{
		"sport_id":     sportID,
		"fs_id":        fsId,
		"bottom_sheet": 1,
	}
	postbackParams, _ := json.Marshal(payload)

	clickAction.SetDeeplink(&sushi.Deeplink{
		URL:            util.GetAcademyTrialListMemberBottomSheetDeeplink(),
		PostbackParams: string(postbackParams),
	})

	return clickAction
}

func (f *AcademyFacilityPageTemplate) SetNearbySectionHeading(ctx context.Context) {
	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	facilitiesResponse := f.GetFacilityDetails(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Facilities) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("OTHER CENTERS NEAR YOU")

	if f.PageRequest.SportIDFlag != 0 {
		title, _ = sushi.NewTextSnippet(fmt.Sprintf("OTHER %s CENTERS NEAR YOU", strings.ToUpper(facilitiesResponse.Sports[0].SportName)))
	}
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["source"] = "facility"
	sectionImpressionPayload["facility_id"] = f.PageRequest.FacilityID
	sectionImpressionPayload["course_id"] = f.PageRequest.CourseId
	impressionEname := &sushi.EnameData{
		Ename: "academy_fitso_centers_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeThick, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	sectionHeaderSection := &facilityModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SnippetConfig:      snippetConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	f.AddSection(sectionHeaderSection)
}

func (f *AcademyFacilityPageTemplate) ReplicateOldPostbackParamsToNewOne(ctx context.Context) {
	var temp map[string]interface{}
	json.Unmarshal([]byte(f.PageRequest.PostbackParams), &temp)
	f.PageData.PostbackParams = temp
}

func (f *AcademyFacilityPageTemplate) SetNearByCentersSection(ctx context.Context) {
	f.ReplicateOldPostbackParamsToNewOne(ctx)

	nearbyFacilitiesResponse := f.GetNearbyFacilities(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Facilities) == 0 {
		return
	}

	f.SetHasMore(nearbyFacilitiesResponse.HasMore)

	f.AddPostbackParam("previous_facility_ids", nearbyFacilitiesResponse.PreviousFacilityIds)

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoFacilityCardType2,
	}
	if len(nearbyFacilitiesResponse.Facilities) == 1 {
		layoutConfig.LayoutType = sushi.LayoutTypeGrid
		layoutConfig.SectionCount = 1
	} else {
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	}

	items := make([]sushi.FitsoFacilityCardType2SnippetItem, 0)
	for _, facility := range nearbyFacilitiesResponse.Facilities {
		snippet := sushi.FitsoFacilityCardType2SnippetItem{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sports {
			sportNames = append(sportNames, sport.SportName)
		}
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction

		ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}

		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		if len(facility.Attributes) > 0 {
			bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
			for _, val := range facility.Attributes {
				attrImage, _ := sushi.NewImage(val.Image)

				if val.Type == attributeMaxSafety {
					attrImage.SetAspectRatio(2.277777)
					attrImage.SetType(sushi.ImageTypeRectangle)
				} else if val.Type == attributePeopleCount {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeCircle)
				} else if val.Type == attributeVaccinatedStaff {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeRectangle)
				}

				attrTitle, _ := sushi.NewTextSnippet(val.Title)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				attrTitle.SetColor(titleColor)

				bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
					LeftImage: attrImage,
					Title:     attrTitle,
				}
				bottomItems = append(bottomItems, bottomContainerSnippetItem)
			}
			snippet.BottomContainerSnippetItems = &bottomItems
		}

		multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
		for _, sport := range facility.Sports {
			if sport.SportId != f.PageRequest.SportID {
				continue
			}
			for _, attribute := range sport.FsInfo {
				tagTitle, _ := sushi.NewTextSnippet(attribute)

				if !featuresupport.SupportsNewColor(ctx) {
					multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
					multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
					gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:    tagTitle,
						Gradient: gradient,
					})
				} else {
					multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:   tagTitle,
						BgColor: multiTagSnippetItemColor,
					})
				}
			}
		}
		if len(multiTagItems) > 0 {
			snippet.TopTags = &multiTagItems
		}

		horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
			Text: facility.SubzoneName,
		}
		horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
			HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
		}
		snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["source_facility_id"] = f.PageRequest.FacilityID
		tapPayload["course_id"] = f.PageRequest.CourseId
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "facility"
		tapEname := &sushi.EnameData{
			Ename: "academy_facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, snippet)
	}
	facilitiesSnippet := &sushi.FitsoFacilityCardType2Snippet{
		Items: &items,
	}
	section := &facilityModel.ResultSection{
		LayoutConfig:           layoutConfig,
		FitsoFacilityCardType2: facilitiesSnippet,
	}
	f.AddSection(section)
}

func (f *AcademyFacilityPageTemplate) SetHeaderSection(ctx context.Context) {
	response := f.GetFacilityDetails(ctx)

	if response == nil {
		return
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.HeaderSnippetType4,
	}

	var items []*sushi.HeaderType4SnippetItem

	for _, val := range response.Images {
		image, _ := sushi.NewImage(val)
		image.SetAspectRatio(1.33)
		item := &sushi.HeaderType4SnippetItem{
			Image: image,
		}

		items = append(items, item)
	}
	snippet := &sushi.HeaderType4Snippet{
		Items:       items,
		AspectRatio: 1.33,
	}
	header := &sushi.HeaderType4SnippetLayout{
		LayoutConfig:       layoutConfig,
		HeaderType4Snippet: snippet,
	}
	f.AddHeader(header)
}

func (f *AcademyFacilityPageTemplate) SetFooterSection(ctx context.Context) {
	facilityDetails := f.GetFacilityDetails(ctx)
	if facilityDetails == nil {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Type:    sushi.FooterButtonTypeSolid,
		Text:    "Book a trial class for badminton",
		Id:      "book_slot",
		BgColor: bgColor,
	}
	items = append(items, buttonItem1)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	f.AddFooter(footer)
}

func (f *AcademyFacilityPageTemplate) SetPageHeaderSection(ctx context.Context) {
	facilityDetails := f.GetFacilityDetails(ctx)
	if facilityDetails == nil {
		return
	}

	title, _ := sushi.NewTextSnippet(facilityDetails.DisplayName)

	shareClickAction := sushi.GetClickAction()
	share := &sushi.Share{
		Text: fmt.Sprintf("Find this facility on cult | %s", facilityDetails.DisplayAddress),
		URL:  facilityDetails.WebUrl,
	}
	shareClickAction.SetShare(share)

	shareIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	shareIcon, _ := sushi.NewIcon(sushi.ShareIcon, shareIconColor)
	shareIcon.SetClickAction(shareClickAction)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["facility_id"] = f.PageRequest.FacilityID
	tapPayload["course_id"] = f.PageRequest.CourseId
	tapPayload["source"] = "facility"
	tapEname := &sushi.EnameData{
		Ename: "academy_share_facility_button_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	shareIcon.AddClevertapTrackingItem(trackItem)

	customAction := &sushi.CustomAction{
		Type: sushi.CustomActionTypeIcon,
		Icon: shareIcon,
	}
	customActions := []*sushi.CustomAction{customAction}

	pageHeader := &facilityModel.PageHeader{
		ScrolledUpTitle: title,
		Actions:         customActions,
	}
	f.SetPageHeader(pageHeader)
}

func (f *AcademyFacilityPageTemplate) SetPageTracking(ctx context.Context) {
	backArrowPayload := make(map[string]interface{})
	backArrowPayload["user_id"] = util.GetUserIDFromContext(ctx)
	backArrowPayload["facility_id"] = f.PageRequest.FacilityID
	backArrowPayload["course_id"] = f.PageRequest.CourseId
	backArrowPayload["source"] = "facility"
	backArrowEname := &sushi.EnameData{
		Ename: "academy_facility_page_back_arrow_tap",
	}

	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := sushi.GetClevertapTrackItem(ctx, backArrowPayload, backArrowEvents)

	landingPayload := make(map[string]interface{})
	landingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	landingPayload["facility_id"] = f.PageRequest.FacilityID
	landingPayload["course_id"] = f.PageRequest.CourseId
	landingEname := &sushi.EnameData{
		Ename: "academy_facility_page_landing",
	}

	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, landingPayload, landingEvents)

	f.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}
}
