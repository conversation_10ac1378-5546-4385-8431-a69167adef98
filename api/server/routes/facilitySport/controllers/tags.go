package facilitySportController

import (
	"log"
	"net/http"

	model "bitbucket.org/jogocoin/go_api/api/models"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type UpdateTagsTemplate struct {
}

func UpdateTagsC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)

	cl = util.GetFacilitySportClient()

	var json structs.UpdateTags
	json = c.MustGet("jsonData").(structs.UpdateTags)

	reqData := &facilitySportPB.TagMapping{
		TagMappingId: json.TagMappingId,
		CityId:       json.CityId,
		FacilityId:   json.FacilityId,
		SportId:      json.SportId,
		TagId:        json.TagId,
		DeletedFlag:  json.DeletedFlag,
		MappingType:  json.MappingType,
	}

	response, err := cl.UpdateTags(ctx, reqData)
	if err != nil {
		log.Printf("Error in Updating tag mapping: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(err.Error()))
		return
	}

	c.JSON(http.StatusOK, response)
}

func GetTagMappingC(c *gin.Context) {
	cl = util.GetFacilitySportClient()

	var json structs.GetTagMappingRequest
	json = c.MustGet("jsonData").(structs.GetTagMappingRequest)

	reqData := &facilitySportPB.TagMappingRequest{
		MappingType: json.MappingType,
	}

	response, err := cl.GetTagMappings(c, reqData)
	if err != nil {
		log.Println("Error in getting tag mapping data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}
