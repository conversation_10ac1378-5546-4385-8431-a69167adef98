package facility_sport

import (
	commonController "bitbucket.org/jogocoin/go_api/api/server/routes/facilitySport/commonController"
	facilitySportController "bitbucket.org/jogocoin/go_api/api/server/routes/facilitySport/controllers"
	facilitySportResource "bitbucket.org/jogocoin/go_api/api/server/routes/facilitySport/resources"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func FacilitySportRoute(routerVersion *gin.RouterGroup) {
	// Group auth related routes together

	facilitySportRoutes := routerVersion.Group("/facility_sport")
	{
		facilitySportRoutes.GET("/getFacility", sharedFunc.ValidateDashboardUser, facilitySportResource.GetFacilityR, facilitySportController.GetFacilityC)
		facilitySportRoutes.POST("/createOrUpdateFacility", sharedFunc.ValidateDashboardUser, facilitySportResource.CreateOrUpdateFacilityR, facilitySportController.CreateOrUpdateFacilityC)
		facilitySportRoutes.POST("/createOrUpdateFacilitySportMappingseFacility", sharedFunc.ValidateDashboardUser, facilitySportResource.CreateOrUpdateFacilitySportMappingsR, facilitySportController.CreateOrUpdateFacilitySportMappingsC)
		facilitySportRoutes.GET("/getPlayArenas", facilitySportResource.GetPlayArenasR, facilitySportController.GetPlayArenasC)
		facilitySportRoutes.GET("/getPlayArenaSlots", facilitySportResource.GetPlayArenaSlotsR, facilitySportController.GetPlayArenaSlotsC)
		facilitySportRoutes.GET("/getSport", facilitySportResource.GetSportR, facilitySportController.GetSportC)
		facilitySportRoutes.GET("/getFacilitySport", facilitySportResource.GetFacilitySportR, facilitySportController.GetFacilitySportC)
		facilitySportRoutes.GET("/getFacilitySportCapacitySlot", facilitySportResource.GetFacilitySportSlotR, facilitySportController.GetFacilitySportSlotC)
		facilitySportRoutes.GET("/getSlot", facilitySportResource.GetSlotR, facilitySportController.GetSlotC)
		facilitySportRoutes.GET("/getProductArenaCategory", facilitySportResource.GetProductArenaCategoryR, facilitySportController.GetProductArenaCategoryC)
		facilitySportRoutes.GET("/getSportsInFacility", facilitySportResource.GetSportInFacilityR, facilitySportController.GetSportInFacilityC)
		facilitySportRoutes.GET("/getActiveCitiesRegionSports", facilitySportResource.GetActiveCitiesRegionSportsR, facilitySportController.GetActiveCitiesRegionSportsC)
		facilitySportRoutes.GET("/getFitsoFacilities", facilitySportResource.GetFitsoFacilitiesR, facilitySportController.GetFitsoFacilitiesC)
		facilitySportRoutes.POST("/markFavoriteFacilities", facilitySportResource.MarkFavoriteFacilitiesR, facilitySportController.MarkFavoriteFacilitiesC)
		facilitySportRoutes.GET("/getFacilityRegion", facilitySportResource.GetFacilityRegionR, facilitySportController.GetFacilityRegionC)
		facilitySportRoutes.POST("/createUpdateFacilityRegion", sharedFunc.ValidateDashboardUser, facilitySportResource.CreateUpdateFacilityRegionR, facilitySportController.CreateUpdateFacilityRegionC)
		facilitySportRoutes.GET("/getActiveCities", facilitySportResource.GetActiveCitiesR, facilitySportController.GetActiveCitiesC)
		facilitySportRoutes.GET("/getSocieties", facilitySportResource.GetSocietiesR, facilitySportController.GetSocietiesC)
		facilitySportRoutes.GET("/getCoachingTypes", facilitySportResource.GetCoachingTypesR, facilitySportController.GetCoachingTypesC)
		facilitySportRoutes.GET("/insertFacilitySubzoneMap", sharedFunc.ValidateDashboardUser, facilitySportController.InsertFacilitySubzoneMapC)
		facilitySportRoutes.GET("/getFacilityImageV2Map", facilitySportController.GetFacilityImageV2MapC)
		facilitySportRoutes.POST("/storeFacilityImageV2", facilitySportResource.StoreFacilityImageV2R, facilitySportController.StoreFacilityImageV2C)
		facilitySportRoutes.GET("/storeFacilitySportImagesMap", facilitySportController.StoreFacilitySportImagesMapC)
		facilitySportRoutes.POST("/storeFacilitySportImages", facilitySportResource.StoreFacilitySportImagesR, facilitySportController.StoreFacilitySportImagesC)
		facilitySportRoutes.POST("/amenity/upsert", sharedFunc.ValidateDashboardUser, facilitySportResource.UpsertAmenityR, facilitySportController.UpsertAmenityC)
		facilitySportRoutes.GET("/getSubzonesSuggestions", sharedFunc.ValidateDashboardUser, facilitySportResource.GetSubzoneSuggestionsR, facilitySportController.GetSubzoneSuggestionsC)
		facilitySportRoutes.POST(
			"/facility-amenity-map/upsert",
			sharedFunc.ValidateDashboardUser,
			facilitySportResource.UpsertFacilityAmenityMappingR,
			facilitySportController.UpsertFacilityAmenityMappingC,
		)
		facilitySportRoutes.GET(
			"/facility-amenity-map/list",
			sharedFunc.ValidateTokenShared,
			facilitySportResource.GetAmenitiesByFacilityIdR,
			facilitySportController.GetAmenitiesByFacilityIdC,
		)
		facilitySportRoutes.POST(
			"/sport/:id",
			facilitySportResource.GetSportDetailsR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			facilitySportController.GetSportController,
		)
		facilitySportRoutes.POST(
			"/facility/:id",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			facilitySportResource.GetFacilityDetailsV2R,
			commonController.FacilityAcademyAndMasterkeyC,
		)
		facilitySportRoutes.GET(
			"/sport-select",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			facilitySportResource.GetSportsToSelectR,
			facilitySportController.GetSportsToSelectC,
		)
		facilitySportRoutes.GET(
			"/preferred-centers",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			facilitySportResource.GetPreferredCentersR,
			facilitySportController.GetPreferredCentersC,
		)
		facilitySportRoutes.POST(
			"/tag/update",
			sharedFunc.ValidateDashboardUser,
			facilitySportResource.UpdateTagsR,
			facilitySportController.UpdateTagsC,
		)
		facilitySportRoutes.GET(
			"/academy/courses/:id",
			facilitySportResource.GetAcademyCoursePageR,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetUserIDInContextFromToken,
			commonController.GetCoursePageC,
		)
		facilitySportRoutes.GET(
			"/academy/tabs",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			facilitySportResource.GetAcademyTabsR,
			commonController.GetCourseTabsC,
		)
		facilitySportRoutes.GET(
			"/academy/course-category/:id",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			facilitySportResource.GetAcademyCourseCategoryPageR,
			facilitySportController.GetAcademyCourseCategoryPageC,
		)
		facilitySportRoutes.GET(
			"/academy/courseCategories",
			sharedFunc.ValidateDashboardUser,
			facilitySportController.GetAcademyCourseCategoriesAndSportsC,
		)
		facilitySportRoutes.GET(
			"/tag/list",
			sharedFunc.ValidateDashboardUser,
			facilitySportResource.GetTagMappingR,
			facilitySportController.GetTagMappingC,
		)
	}
}
