package paymentController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"

	"bitbucket.org/jogocoin/go_api/api/common"

	"bitbucket.org/jogocoin/go_api/api/models"
	paymentModel "bitbucket.org/jogocoin/go_api/api/models/payment"
	"bitbucket.org/jogocoin/go_api/api/server/commonFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
	"github.com/micro/go-micro/config"
)

type AccessCodeResponse struct {
	TokenType  string `json:"token_type"`
	ExpiresIn  int32  `json:"expires_in"`
	StatusCode int32  `json:"status_code"`
	AccessCode string `json:"access_token"`
}

var hiddenPaymentMethods = []string{"pay_later", "lazypay", "simpl", "ola_postpaid"}

const (
	countryIDIndia = 1
)

func SdkInitC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	userID := util.GetUserIDFromContext(ctx)

	if userID == 0 {
		c.JSON(
			http.StatusUnauthorized,
			models.StatusFailure("You are not allowed to make this request"),
		)
		return
	}

	code := GetAccessTokenFromPaymentService(ctx)

	if code.AccessCode == "" {
		c.JSON(
			http.StatusOK,
			models.StatusFailure("Something went wrong. Please try again later!"),
		)
		return
	}

	var paymentMethods []paymentModel.PaymentMethodData

	for _, paymentMethod := range hiddenPaymentMethods {
		paymentMethods = append(paymentMethods, paymentModel.PaymentMethodData{
			Type: paymentMethod,
		})
	}

	additionalParams := paymentModel.AdditionalParams{
		HiddenPaymentMethods: paymentMethods,
	}

	encodedParams, _ := json.Marshal(additionalParams)
	serviceType := config.Get("payments", "service_type").String("")

	paymentData := &paymentModel.PaymentData{
		AdditionalParams: string(encodedParams),
		CountryID:        countryIDIndia,
		ServiceType:      serviceType,
	}

	c.JSON(http.StatusOK, &paymentModel.SdkInitResponse{
		Code:        code.AccessCode,
		ExpiresIn:   code.ExpiresIn,
		Status:      common.SUCCESS,
		PaymentData: paymentData,
	})
}

func GetAccessTokenFromPaymentService(ctx context.Context) AccessCodeResponse {
	host := config.Get("payments", "host").String("")
	username := config.Get("payments", "username").String("")
	password := config.Get("payments", "password").String("")
	companyID := config.Get("payments", "company_id").String("")
	serviceType := config.Get("payments", "service_type").String("")

	headers := map[string]string{
		commonFunc.Authorization: fmt.Sprintf("Basic %s", commonFunc.BasicAuth(username, password)),
		commonFunc.ContentType:   commonFunc.FormContentType,
		"X-COMPANY-ID":           companyID,
	}

	data := url.Values{}
	data.Set("service_type", serviceType)
	data.Set("user_id", fmt.Sprintf("%v", util.GetUserIDFromContext(ctx)))

	request := &commonFunc.Request{
		Method:      commonFunc.MethodTypePost,
		RequestURL:  fmt.Sprintf("%s/v2/oauth/get_access_token", host),
		Headers:     headers,
		RequestBody: strings.NewReader(data.Encode()),
	}

	accessCodeResponse := AccessCodeResponse{}

	response, err := commonFunc.MakeHTTPRequest(ctx, request)

	log.Printf("[Sdk Init] access code response: %v", response)

	if err != nil {
		log.Printf("[Sdk Init] error in getting access code for service: %s with error: %v", serviceType, err)

		return accessCodeResponse
	}

	err = json.Unmarshal([]byte(response.Body), &accessCodeResponse)

	if err != nil {
		log.Printf("[Sdk Init] error in unmarshalling access code for service: %s with error: %v", serviceType, err)

		return accessCodeResponse
	}

	return accessCodeResponse
}
