package payment

import (
	paymentController "bitbucket.org/jogocoin/go_api/api/server/routes/payment/controllers"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func PaymentRouteV1(routerVersion *gin.RouterGroup) {
	paymentRoutes := routerVersion.Group("/payment")
	{
		paymentRoutes.GET(
			"/sdkInit",
			sharedFunc.SetUserIDInContextFromToken,
			paymentController.SdkInitC,
		)
	}
}
