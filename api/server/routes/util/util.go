package util

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"time"
	"strings"

	"bitbucket.org/jogocoin/go_api/api/models/sushi"

	common "bitbucket.org/jogocoin/go_api/api/common"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/structs"
	mobiledetect "github.com/Shaked/gomobiledetect"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/metadata"
)

// PrepareGRPCMetaData takes gin context and returns GRPC metadata
func PrepareGRPCMetaData(c *gin.Context) context.Context {
	md := make(metadata.Metadata)

	subzoneID := c.Value("requestHeaders").(structs.Headers).SubzoneId
	appVersion := c.Value("requestHeaders").(structs.Headers).AppVersion
	appType := c.Value("requestHeaders").(structs.Headers).AppType
	presentLat := c.Value("requestHeaders").(structs.Headers).PresentLat
	presentLong := c.Value("requestHeaders").(structs.Headers).PresentLong
	userDefinedLat := c.Value("requestHeaders").(structs.Headers).UserDefinedLat
	userDefinedLong := c.Value("requestHeaders").(structs.Headers).UserDefinedLong
	featureType := c.Value("requestHeaders").(structs.Headers).FeatureType
	locationProvider := c.Value("requestHeaders").(structs.Headers).LocationProvider
	appsflyerId := c.Value("requestHeaders").(structs.Headers).AppsflyerId
	deviceId := c.Value("requestHeaders").(structs.Headers).DeviceID

	if c.Value("user_id") != nil {
		md["user_id"] = strconv.FormatInt(int64(c.Value("user_id").(int32)), 10)
	}

	if c.Value("city_id") != nil {
		md["city_id"] = strconv.FormatInt(int64(c.Value("city_id").(int32)), 10)
	}

	if c.Value("zone_id") != nil {
		md["zone_id"] = strconv.FormatInt(int64(c.Value("zone_id").(int32)), 10)
	}

	md["present_lat"] = fmt.Sprintf("%f", presentLat)
	md["present_long"] = fmt.Sprintf("%f", presentLong)
	md["user_defined_lat"] = fmt.Sprintf("%f", userDefinedLat)
	md["user_defined_long"] = fmt.Sprintf("%f", userDefinedLong)
	md["subzone_id"] = strconv.FormatInt(subzoneID, 10)
	md["version"] = appVersion
	md["client"] = appType
	md["feature_type"] = featureType
	md["location_provider"] = strconv.FormatInt(locationProvider, 10)
	md["device_id"] = deviceId
	md["appsflyer_id"] = appsflyerId

	if appType == common.AppTypeWebFrontend {
		md[IsMobileCtx] = strconv.FormatBool(IsMobile(c.Request))
	}

	if c.Value("location_display_title") != nil {
		log.Printf("setting location display title: %s", c.Value("location_display_title").(string))
		md[LocationDisplayTitleCtx] = c.Value("location_display_title").(string)
	}

	return metadata.NewContext(c, md)
}

// GetCDNLink return link with CDN appended host
func GetCDNLink(path string) string {
	b := strings.HasPrefix(path, "https")
	if b {
		return path
	}
	return fmt.Sprintf("%s%s", cdnHost, path)
}

// GetUserIDFromContext returns user id from the context
func GetUserIDFromContext(ctx context.Context) int32 {
	userID, _ := ctx.Value(UserIDCtx).(int32)
	return userID
}

// GetCityIDFromContext returns city id from the context
func GetCityIDFromContext(ctx context.Context) int32 {
	cityID, _ := ctx.Value(CityIDCtx).(int32)
	return cityID
}

// GetIsMobileFromContext returns is_mobile from the context
func GetIsMobileFromContext(ctx context.Context) bool {
	isMobile, _ := metadata.Get(ctx, IsMobileCtx)
	parsedIsMobile, _ := strconv.ParseBool(isMobile)
	return parsedIsMobile
}

// GetSubzoneIDFromContext returns subzone id from the context
func GetSubzoneIDFromContext(ctx context.Context) int64 {
	subzoneID := ctx.Value(RequestHeadersCtx).(structs.Headers).SubzoneId
	return subzoneID
}

func GetSessionIDFromContext(ctx context.Context) string {
	sessionID := ctx.Value(RequestHeadersCtx).(structs.Headers).SessionId
	return sessionID
}

func GetFitsoTrackIDFromContext(ctx context.Context) string {
	fitsoTrackID := ctx.Value(RequestHeadersCtx).(structs.Headers).FitsoTrackId
	return fitsoTrackID
}

func GetAppTypeFromContext(ctx context.Context) string {
	appType := ctx.Value(RequestHeadersCtx).(structs.Headers).AppType
	return appType
}

func GetAppVersionFromContext(ctx context.Context) string {
	appVersion := ctx.Value(RequestHeadersCtx).(structs.Headers).AppVersion
	return appVersion
}

func GetLocationDisplayTitleFromContext(ctx context.Context) string {
	if ctx.Value(LocationDisplayTitleCtx) != nil {
		log.Printf(" func: GetLocationDisplayTitleFromContext (original), value: %s", ctx.Value(LocationDisplayTitleCtx).(string))
		decodedTitle, err := url.PathUnescape(ctx.Value(LocationDisplayTitleCtx).(string))
		log.Printf("func: GetLocationDisplayTitleFromContext (decoded), value: %s", decodedTitle)
		if err == nil {
			return decodedTitle
		}
	}
	return ""
}

func UpdateParamsInRequestHeaders(c *gin.Context, key string, val interface{}) {
	headers := c.Value(RequestHeadersCtx).(structs.Headers)
	switch key {
	case "subzone_id":
		subzoneID := val.(int64)
		headers.SubzoneId = subzoneID
		break
	}
	c.Set(RequestHeadersCtx, headers)
}

// IsInternalRequest checks if the request is coming from internal vpc
func IsInternalRequest(c *gin.Context) bool {
	clientIP := c.ClientIP()

	log.Print("[callback debug] ip: ", clientIP)

	for _, ipRange := range internalIPRanges {
		_, ipNetwork, err := net.ParseCIDR(ipRange)

		if err != nil {
			log.Printf("not a valid IP range: %s, err: %v", ipRange, err)

			return false
		}

		return ipNetwork.Contains(net.ParseIP(clientIP))
	}

	return false
}

// AddGetParamURL adds a get param to url
func AddGetParamURL(URL string, key string, value string) string {
	u, err := url.Parse(URL)
	if err != nil {
		return URL
	}
	q := u.Query()
	q.Set(key, value)
	u.RawQuery = q.Encode()

	return u.String()
}

func IsStringInSlice(haystack []string, needle string) bool {
	for _, value := range haystack {
		if value == needle {
			return true
		}
	}
	return false
}

func Int32InSlice(a int32, list []int32) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

func GetInt32FromInterface(input interface{}) (int32, error) {
	if input != nil {
		inputType := fmt.Sprintf("%T", input)
		if inputType == "float64" || inputType == "int" {
			castedInput := input.(float64)
			input = int32(castedInput)
		}
		if inputType == "string" {
			parsedProductId, err := strconv.Atoi(input.(string))
			if err != nil {
				return 0, errors.New("Error in controller CalculateCartC while converting input type from string to int32")
			}
			input = int32(parsedProductId)
		}
		castedInput := input.(int32)
		return castedInput, nil
	}
	return 0, errors.New("Input is nil")
}

// Returns an int32 slice after removing duplicate entries
func DeduplicateSlice(input []int32) []int32 {
	unique := make([]int32, 0, len(input))
	occurrence_map := make(map[int32]bool)

	for _, val := range input {
		if _, ok := occurrence_map[val]; !ok {
			occurrence_map[val] = true
			unique = append(unique, val)
		}
	}
	return unique
}

func SetCookiesInWebResponse(c *gin.Context, ctx context.Context) {
	cityID := GetCityIDFromContext(ctx)
	if cityID > 0 {
		cityIdStr := strconv.FormatInt(int64(cityID), 10)
		setCookie(c, "city_id", cityIdStr, 0)
	}

	locationDisplayTitle := GetLocationDisplayTitleFromContext(ctx)
	if len(locationDisplayTitle) > 0 {
		setCookie(c, "location_display_title", locationDisplayTitle, 0)
	}

	sessionID := GetSessionIDFromContext(ctx)
	if sessionID != "" {
		sessionIdMaxAge := int(time.Duration(30*time.Minute) / time.Second)
		setCookie(c, "session_id", sessionID, sessionIdMaxAge)
	} else {
		log.Printf("func:SetCookiesInWebResponse, Error : Session ID not found in headers ")

	}

	fitsoTrackID := GetFitsoTrackIDFromContext(ctx)
	if fitsoTrackID != "" {
		fitsoTrackIdMaxAge := int(time.Duration(4*30*24*time.Hour) / time.Second)
		setCookie(c, "fitso_track_id", fitsoTrackID, fitsoTrackIdMaxAge)
	} else {
		log.Printf("func:SetCookiesInWebResponse, Error : Fitso Track ID not found in headers ")

	}

	presentCoordinates := GetUserPresentCoordinates(ctx)
	if presentCoordinates.Lat > 0 && presentCoordinates.Long > 0 {
		setCookie(c, "x-present-lat", fmt.Sprintf("%f", presentCoordinates.Lat), 0)
		setCookie(c, "x-present-long", fmt.Sprintf("%f", presentCoordinates.Long), 0)
	} else {
		log.Printf("func:SetCookiesInWebResponse, Error : Present coordinates not found in headers ")
	}

	userDefinedCoordinates := GetUserDefinedCoordinates(ctx)
	if userDefinedCoordinates.Lat > 0 && userDefinedCoordinates.Long > 0 {
		setCookie(c, "x-user-defined-lat", fmt.Sprintf("%f", presentCoordinates.Lat), 0)
		setCookie(c, "x-user-defined-long", fmt.Sprintf("%f", presentCoordinates.Long), 0)
	} else {
		log.Printf("func:SetCookiesInWebResponse, Error : User defined coordinates not found in headers ")
	}
}

func setCookie(c *gin.Context, key string, value string, maxAge int) {
	http.SetCookie(c.Writer, &http.Cookie{
		Name:   key,
		Value:  value,
		Path:   "/",
		Domain: ".getfitso.com",
		Secure: true,
		MaxAge: maxAge,
	})
}

func IsMobile(req *http.Request) bool {
	detect := mobiledetect.NewMobileDetect(req, nil)
	return detect.IsMobile()
}

func IsTablet(req *http.Request) bool {
	detect := mobiledetect.NewMobileDetect(req, nil)
	return detect.IsTablet()
}

func IsIOS(ctx context.Context) bool {
	appType := GetAppTypeFromContext(ctx)
	return appType == common.AppTypeFitsoIos
}

func IsAndroid(ctx context.Context) bool {
	appType := GetAppTypeFromContext(ctx)
	return appType == common.AppTypeFitsoAndroid
}

func IsRequestFromDev(c *gin.Context) bool {
	requestHeader := c.Request.Header.Get("origin")
	return requestHeader == "https://dev.getfitso.com" || requestHeader == "dev.getfitso.com"
}

func NeedMedicalDetails(ctx context.Context, userId int32) bool {
	if userId == 0 {
		return false
	}
	purchaseReq := &purchasePB.GetPurchaseHistoryRequest{
		UserId: userId,
	}
	purchaseClient := GetPurchaseClient()
	purchaseRes, err := purchaseClient.GetActivePurchaseByUser(ctx, purchaseReq)
	if err != nil {
		log.Printf("Unable to get active purchase for userId: %d, Error:%v", userId, err)
		return false
	}
	if purchaseRes == nil || len(purchaseRes.Purchases) == 0 {
		return false
	}

	userClient := GetUserServiceClient()
	medicalReq := &userPB.GetUsersMedicalDetailStatusRequest{
		UserIds: []int32{userId},
	}
	medicalRes, err := userClient.BatchGetUserMedicalDetailStatus(ctx, medicalReq)
	if err != nil {
		log.Printf("Unable to get medical status for userId: %d, Error:%v", userId, err)
		return false
	}

	medicalFormStatus := medicalRes.Status
	if status, ok := medicalFormStatus[userId]; !ok || status == common.MEDICAL_FORM_FILLED_STATUS {
		return false
	}
	return true
}

func GetMonsoonAlertLastDate() time.Time {
	loc, _ := time.LoadLocation("Asia/Kolkata")
	return time.Date(2021, time.September, 15, 23, 59, 59, 0, loc)
}

func GetDiwaliSaleLastDate() time.Time {
	loc, _ := time.LoadLocation("Asia/Kolkata")
	return time.Date(2021, time.November, 14, 23, 59, 59, 0, loc)
}

func ConvertBoolToInt8(val bool) int8 {
	var intVal int8
	if val {
		intVal = 1
	} else {
		intVal = 0
	}
	return intVal
}

func GetFeatureTypeFromContext(ctx context.Context) string {
	featureType := ctx.Value(RequestHeadersCtx).(structs.Headers).FeatureType
	return featureType
}

func CheckAcademy(ctx context.Context) bool {
	featureType := GetFeatureTypeFromContext(ctx)
	return featureType == common.Academy
}

func CheckSummerCamp(c *gin.Context) bool {
	hasSummerCamp, _ := c.Get("has_summer_camp")
	return hasSummerCamp == 1
}

func ConvertVideoProtoToVideoTemplate(videoDetails *facilitySportPB.Video) *sushi.Video {
	thumbnail, _ := sushi.NewImage(GetCDNLink(videoDetails.ThumbnailUrl))
	video := &sushi.Video{
		Id:          strconv.Itoa(int(videoDetails.VideoId)),
		Url:         videoDetails.VideoUrl,
		AspectRatio: videoDetails.AspectRatio,
		Thumbnail:   thumbnail,
	}
	config := &sushi.VideoConfig{
		ShowMute:        ConvertBoolToInt8(videoDetails.ShowMute),
		Autoplay:        ConvertBoolToInt8(videoDetails.Autoplay),
		HasAudio:        ConvertBoolToInt8(videoDetails.HasAudio),
		Orientation:     videoDetails.Orientation,
		ForceFullScreen: ConvertBoolToInt8(videoDetails.ShowFullScreen),
	}
	if videoDetails.ShowFullScreen {
		video.Config = config
	} else {
		video.SnippetConfig = config
	}
	return video
}

func IsHighPeakSlotFacilitySportV2(ctx context.Context, fsId int32) bool {
	fsIds := []int32{2, 110, 101, 25, 33, 190}
	for _, elem := range fsIds {
		if elem == fsId {
			return true
		}
	}

	return false
}

func IsHighPeakSlotFacilitySport(ctx context.Context, fsId int32) bool {
	facilityClient := GetFacilitySportClient()
	res, err := facilityClient.CheckIfSoldOutFacilitySport(ctx, &facilitySportPB.FacilitySportMapping{FsId: fsId})
	if err != nil {
		log.Printf("func:IsHighPeakSlotFacilitySport, error in checking sold out status for fsID:%d, err:%v ", fsId, err)
		return false
	}

	if res.IsSoldOut {
		return true
	}

	return false
}

func IsHighPeakSlotFacilityV2(ctx context.Context, facilityId int32) bool {
	facilityIds := []int32{20, 90, 83, 27, 33, 112}
	for _, elem := range facilityIds {
		if elem == facilityId {
			return true
		}
	}

	return false
}

func IsHighPeakSlotFacility(ctx context.Context, facilityId int32) bool {
	facilityClient := GetFacilitySportClient()
	res, err := facilityClient.CheckIfSoldOutFacility(ctx, &facilitySportPB.Facility{FacilityId: facilityId})
	if err != nil {
		log.Printf("func:IsHighPeakSlotFacility, error in getting sold out status for facilityId:%D, err:%v ", facilityId, err)
		return false
	}

	if res.IsSoldOut {
		return true
	}
	return false
}

func IsNonOperationalFacility(ctx context.Context, facilityId int32) bool {
	facilityIds := []int32{}
	for _, elem := range facilityIds {
		if elem == facilityId {
			return true
		}
	}
	return false
}

func Contains(arr []int32, val int32) bool {
	for _, a := range arr {
		if a == val {
			return true
		}
	}
	return false
}

func GetSportIdIfSingleSportCity(ctx context.Context) (bool, int32) {
	facilityClient := GetFacilitySportClient()
	res, err := facilityClient.GetAvailableSportsInCity(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Println("Error in getting sport count for city in GetSportIdIfSingleSportCity: ", err)
		return false, 0
	}
	if len(res.Sports) > 1 || len(res.Sports) == 0 {
		return false, 0
	}
	return true, res.Sports[0].SportId
}

func GetLocalDateTime(date time.Time) time.Time {
	location, _ := time.LoadLocation("Asia/Kolkata") //IST
	return date.In(location)
}

func GetLocalDateTimeFromProtoTime(protoTimestamp *timestamp.Timestamp) time.Time {

	convertedTs := time.Unix(protoTimestamp.Seconds, 0)

	LocalTime := GetLocalDateTime(convertedTs)

	return LocalTime
}

func GetSubzoneDetailsFromSubzoneId(ctx context.Context, subzoneId int32) (*facilitySportPB.FacilityDisplaySubzoneDetailResponse, error) {
	facilitySportClient := GetFacilitySportClient()
	subzoneDetailRequest := &facilitySportPB.FacilityDisplaySubzoneDetailRequest{
		SubzoneId: subzoneId,
	}
	res, err := facilitySportClient.GetFacilityDisplaySubzoneDetails(ctx, subzoneDetailRequest)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func GetCityData(ctx context.Context) (*productPB.GetFitsoCityDataFromCityIdResponse, error) {
	productClient := GetProductClient()
	cityDataReq := &productPB.GetFitsoCityDataFromCityIdRequest{
		FitsoCityId: GetCityIDFromContext(ctx),
	}
	cityDataResponse, err := productClient.GetFitsoCityDataFromCityId(ctx, cityDataReq)
	if err != nil {
		return nil, err
	}
	return cityDataResponse, nil
}

func ShowGuestList(ctx context.Context) bool {
	loggedInUser := GetUserIDFromContext(ctx)
	userClient := GetUserServiceClient()

	showGuestListRes, err := userClient.ShowGuestList(ctx, &userPB.ShowGuestListReq{ShowGuestList: true})

	if err != nil {
		log.Printf("func:ShowGuestList, error in fetching guest list display validation for user: %d", loggedInUser)
		return false
	}
	return showGuestListRes.ShowGuestList

}

func GetLocationProviderFromContext(ctx context.Context) int64 {
	locationProvider := ctx.Value(RequestHeadersCtx).(structs.Headers).LocationProvider
	return locationProvider
}

func UpdateContextWithCoordinates(c *gin.Context, cityId int32, originalCityId int32, zoneId int32) {
	headers := c.Value(RequestHeadersCtx).(structs.Headers)
	if cityId == 0 {
		cityId = common.DELHI_NCR
	}
	ctx := PrepareGRPCMetaData(c)

	if cityId == common.DELHI_NCR && zoneId > 0 {
		//Setting DEFAULT COORDINATES FOR DELHI NCR - NOIDA, DELHI, FARIDABAD, GURGAON, GURUGRAM
		if _, ok := common.DEFAULT_COORDINATES_NCR[zoneId]; ok {
			log.Printf("func UpdateContextWithCoordinates: Zone Id - %d", zoneId)
			coordinates := common.DEFAULT_COORDINATES_NCR[zoneId]
			headers.PresentLat = float64(coordinates.Lat)
			headers.PresentLong = float64(coordinates.Long)
			headers.UserDefinedLat = float64(coordinates.Lat)
			headers.UserDefinedLong = float64(coordinates.Long)
			c.Set(RequestHeadersCtx, headers)
		}
	}
	productClient := GetProductClient()
	log.Printf("func UpdateContextWithCoordinates: Before Processing - Original City - %d, Final City - %d, Zone ID - %d", originalCityId, cityId, zoneId)
	if cityId == originalCityId {
		locationDetailsResponse, err := productClient.GetLocationDetails(ctx, &productPB.GetLocationDetailsRequest{
			Lat:  float32(headers.PresentLat),
			Long: float32(headers.PresentLong),
		})
		if err != nil {
			log.Printf("fun UpdateContextWithCoordinates: Error in getting city data on latitudes and longitudes - %v", err)
		} else {
			if locationDetailsResponse != nil && locationDetailsResponse.Location != nil && locationDetailsResponse.Location.CityId > 0 {
				originalCityId = locationDetailsResponse.Location.CityId
			}
		}
	}
	log.Printf("func UpdateContextWithCoordinates: Original City - %d, Final City - %d, PresentLat - %f, PresentLong - %f, UserDefinedLat - %f, UserDefinedLong - %f, ZoneId - %d", originalCityId, cityId, headers.PresentLat, headers.PresentLong, headers.UserDefinedLat, headers.UserDefinedLong, zoneId)
	if cityId > 0 && ((headers.PresentLat == 0 && headers.PresentLong == 0 && headers.UserDefinedLat == 0 && headers.UserDefinedLong == 0) || (cityId != originalCityId)) {
		coordinates := common.DEFAULT_COORDINATES[cityId]
		headers.PresentLat = float64(coordinates.Lat)
		headers.PresentLong = float64(coordinates.Long)
		headers.UserDefinedLat = float64(coordinates.Lat)
		headers.UserDefinedLong = float64(coordinates.Long)
		log.Printf("func UpdateContextWithCoordinates: lat:%f,long:%f,zoneId:%d", coordinates.Lat, coordinates.Long, zoneId)
		c.Set(RequestHeadersCtx, headers)
	}

}

func GetUserPresentCoordinates(ctx context.Context) structs.GetLocationDetails {
	latitude := ctx.Value(RequestHeadersCtx).(structs.Headers).PresentLat
	longitude := ctx.Value(RequestHeadersCtx).(structs.Headers).PresentLong

	return structs.GetLocationDetails{
		Lat:  float32(latitude),
		Long: float32(longitude),
	}
}

func GetUserDefinedCoordinates(ctx context.Context) structs.GetLocationDetails {
	latitude := ctx.Value(RequestHeadersCtx).(structs.Headers).UserDefinedLat
	longitude := ctx.Value(RequestHeadersCtx).(structs.Headers).UserDefinedLong

	return structs.GetLocationDetails{
		Lat:  float32(latitude),
		Long: float32(longitude),
	}
}

func getCultFitSportsApiHost() string {
	return config.Get("static", "cureFitSportsApiHost").String("")
}

func getCultAppUsageApiEndpoint(userId int32, cityId int32) string {
	cultFitSportsApiHost := getCultFitSportsApiHost()
	return fmt.Sprintf("%s/booking/%d/checkCultAppUsage?cityId=%d", cultFitSportsApiHost, userId, cityId)
}

func CheckIfCultAppUser(ctx context.Context, res *structs.CultAppUsageApiResponse) error {
	userId := GetUserIDFromContext(ctx)
	cityId := GetCityIDFromContext(ctx)
	if userId <= 0 || cityId <= 0 {
		res.BlockFitsoApp = false
		return nil
	}
	userClient := GetUserServiceClient()
	reqData := &userPB.CultAppUsageReq{
		UserId: userId,
	}
	cultAppUsage, err := userClient.GetCultAppUsage(ctx, reqData)
	if err != nil {
		log.Printf("CheckIfCultAppUser: Error in getting cult app usage from redis for user id: %d, cityId: %d with err: %v", userId, cityId, err)
		return err
	}
	res.BlockFitsoApp = cultAppUsage.BlockFitsoApp
	res.Message = cultAppUsage.Message
	if userId == 697642 {
		res.BlockFitsoApp = false
		res.Message = ""
	}
	return nil
}

func CheckIfBlacklistedUser(ctx context.Context) bool {
	userId := GetUserIDFromContext(ctx)
	if userId <= 0 {
		return false
	}
	userClient := GetUserServiceClient()
	reqData := &userPB.UserBlacklistStatusReq{
		UserId: userId,
	}
	blacklistResponse, err := userClient.GetUserBlacklistStatus(ctx, reqData)
	if err != nil {
		log.Printf("GetUserBlacklistStatus: Error in getting blacklist user status for user id: %d with err: %v", userId, err)
		return false
	}
	return blacklistResponse.IsUserBlacklisted
}

func GetProductDaysOfWeekSummercamp(daysOfWeek string) ([]int32, string) {
	var daysInt []int32
	var days []string
	var dayStr string

	arrayString := strings.Split(daysOfWeek, ",")
	for _, value := range arrayString {
		value = strings.TrimSpace(value)
		day, _ := strconv.ParseInt(value, 10, 64)
		var dayName string
		switch day {
		case 1:
			dayName = "Mon"
		case 2:
			dayName = "Tue"
		case 3:
			dayName = "Wed"
		case 4:
			dayName = "Thu"
		case 5:
			dayName = "Fri"
		case 6:
			dayName = "Sat"
		case 7:
			dayName = "Sun"
		}
		days = append(days, dayName)
		daysInt = append(daysInt, int32(day))
	}

	if len(days) > 3 {
		dayStr = days[0] + " - " + days[len(days)-1]
	} else if len(days) > 1 {
		dayStr = fmt.Sprintf("%s & %s", strings.Join(days[:len(days)-1], ", "), days[len(days)-1])
	} else if len(days) == 1 {
		dayStr = days[0]
	}
	return daysInt, dayStr
}