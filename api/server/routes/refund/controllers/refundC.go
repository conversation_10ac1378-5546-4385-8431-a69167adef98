package refundController

import (
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models"
	"github.com/gin-gonic/gin"

	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

func ListRefundRequestsC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	cl := util.GetPurchaseClient()

	json := c.MustGet("jsonData").(structs.RefundRequest)
	refundData := &purchasePB.ListRefundRequest{
		StartDate:  json.StartDate,
		EndDate:    json.EndDate,
		Status:     json.Status,
		RefundMode: json.RefundMode,
		CreatedBy:  json.CreatedBy,
		Count:      json.Count,
		Start:      json.Start,
		ChildFlag:  json.ChildFlag,
	}
	response, err := cl.ListRefundRequests(ctx, refundData)
	if err != nil {
		log.Println("Func:ListRefundRequestsC")
		log.Println("Error in getting refund requests data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}
	c.JSON(statusCode, response)

	return
}

func RefundStatusUpdateC(c *gin.Context) {

	cl := util.GetPurchaseClient()
	var json structs.UpdateRefundRequest

	json = c.MustGet("jsonData").(structs.UpdateRefundRequest)

	reqData := &purchasePB.UpdateRefundStatusRequest{
		Id:               json.Id,
		Status:           json.Status,
		ReferenceReason:  json.ReferenceReason,
		RrnNumber:        json.RrnNumber,
	}
	ctx := util.PrepareGRPCMetaData(c)
	response, err := cl.UpdateRefundStatus(ctx, reqData)

	if err != nil {
		log.Println("Error in updating refund status", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}
	c.JSON(statusCode, response)
	
	return
}