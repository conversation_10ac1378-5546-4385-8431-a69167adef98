package refund

import (
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"

	refundController "bitbucket.org/jogocoin/go_api/api/server/routes/refund/controllers"
	refundResource "bitbucket.org/jogocoin/go_api/api/server/routes/refund/resources"
	"github.com/gin-gonic/gin"
)

func RefundRouteV1(routerVersion *gin.RouterGroup) {
	// Group product related routes together
	refundRoutes := routerVersion.Group("/refunds")
	{
		refundRoutes.GET("/request/list", 
			refundResource.ListRefundRequestsR, 
			sharedFunc.ValidateDashboardUser, 
			refundController.ListRefundRequestsC,
		)
		refundRoutes.POST("/status/update", 
			refundResource.RefundStatusUpdateR, 
			sharedFunc.ValidateTokenShared,
			refundController.RefundStatusUpdateC,
		)
	}
}