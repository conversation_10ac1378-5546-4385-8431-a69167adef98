package refundResource

import (
	"log"
	"fmt"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func ListRefundRequestsR(c *gin.Context) {
	validate = validator.New()

	var json structs.RefundRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("Params not passed in required json format, err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("Check on the required field failed, err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func RefundStatusUpdateR(c *gin.Context) {
	validate = validator.New()

	var json structs.UpdateRefundRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}