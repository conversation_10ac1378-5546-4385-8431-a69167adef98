// facilitySport controller

package configController

import (
	// "context"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"

	// "github.com/micro/go-micro"
	// "github.com/micro/go-micro/config"
	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

func GetSportsAppConfigurationC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	configData := &structs.SportsAppConfiguration{
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
		UserId:     int32(headers.UserId),
	}

	var fUpdate structs.ForceUpdate
	var forceUpdate bool
	fUpdate.AskFavoriteInterval = 5
	if err := CompareVersions(configData, &forceUpdate); err != nil {
		fmt.Println("Compare versions error - ", err)

		status := structs.Status{
			Status:  "failure",
			Message: "Error in app version comparison",
		}
		fUpdate.Status = status

		fUpdate.IsAppUnsupported = false
	} else {
		status := structs.Status{
			Status:  "success",
			Message: "Verification successful",
		}
		fUpdate.Status = status

		if forceUpdate {
			fUpdate.IsAppUnsupported = true
		} else {
			fUpdate.IsAppUnsupported = false
		}
	}

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": fUpdate}, "index.html")
}
