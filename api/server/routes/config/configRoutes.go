// authRoutes.go

package config

import (
	"github.com/gin-gonic/gin"
	configController "bitbucket.org/jogocoin/go_api/api/server/routes/config/controllers"
	configResource "bitbucket.org/jogocoin/go_api/api/server/routes/config/resources"
)

func ConfigRoute(routerVersion *gin.RouterGroup) {
	// Group auth related routes together

	configRoutes := routerVersion.Group("/config")
	{
		configRoutes.GET("/getSportsAppConfiguration", configResource.GetSportsAppConfigurationR, configController.GetSportsAppConfigurationC)
	}
}
