package notificationResource

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func GetNotificationsForUserR(c *gin.Context) {
	validate = validator.New()

	var json structs.Notification

	if err := c.ShouldBind<PERSON>uery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func MarkNotificationR(c *gin.Context) {
	validate = validator.New()
	var json structs.Notification

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("resourceq ---- ", json)

	c.Set("jsonData", json)

	c.Next()
}

func UnreadNotificationCountR(c *gin.Context) {
	validate = validator.New()
	var json structs.UnreadNotification

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("resourceq ---- ", json)

	c.Set("jsonData", json)

	c.Next()
}

func PublishLeadsFromProviderR(c *gin.Context) {
	validate = validator.New()
	var json structs.PublishLeadRequest

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	fmt.Println("resourceq ---- ", json)

	c.Set("jsonData", json)

	c.Next()
}
