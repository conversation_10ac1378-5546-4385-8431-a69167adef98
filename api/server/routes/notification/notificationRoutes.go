// authRoutes.go

package notification

import (
	notificationController "bitbucket.org/jogocoin/go_api/api/server/routes/notification/controllers"
	notificationResource "bitbucket.org/jogocoin/go_api/api/server/routes/notification/resources"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func NotificationRoute(routerVersion *gin.RouterGroup) {
	// Group auth related routes together

	notificationRoutes := routerVersion.Group("/notification")
	{
		notificationRoutes.GET(
			"/getNotificationsForUser",
			sharedFunc.SetUserIDInContextFromToken,
			notificationResource.GetNotificationsForUserR,
			notificationController.GetNotificationsForUserC,
		)
		notificationRoutes.GET(
			"/getUnreadNotificationCount",
			notificationResource.UnreadNotificationCountR,
			notificationController.UnreadNotificationCountC,
		)
		notificationRoutes.POST(
			"/markNotificationReadStatus",
			sharedFunc.SetUserIDInContextFromToken,
			notificationResource.MarkNotificationR,
			notificationController.MarkNotificationC,
		)
		notificationRoutes.POST(
			"/publishLeadsFromProvider",
			notificationResource.PublishLeadsFromProviderR,
			notificationController.PublishLeadsFromProviderC,
		)
	}
}
