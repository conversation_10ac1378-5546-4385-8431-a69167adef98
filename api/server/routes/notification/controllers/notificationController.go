package notificationController

import (
	// "context"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"

	"context"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	notificationPB "bitbucket.org/jogocoin/go_api/api/proto/notification"
	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
)

var (
	cl notificationPB.NotificationService
)

func MarkNotificationC(c *gin.Context) {

	notificationClient := util.GetNotificationServiceClient()
	ctx := util.PrepareGRPCMetaData(c)
	var json structs.Notification
	json = c.MustGet("jsonData").(structs.Notification)

	notiData := &notificationPB.SportNotification{
		NotificationId: json.NotificationId,
		IsRead:         json.IsRead,
		MarkAll:		json.MarkAll,
	}

	response, err := notificationClient.MarkNotificationReadStatus(ctx, notiData)
	if err != nil {
		log.Println("Error in marking notification Status as read ", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
		return
	}
	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
}

func UnreadNotificationCountC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = notificationPB.NewNotificationService(serviceList.Notification, service.Client())

	var json structs.UnreadNotification
	json = c.MustGet("jsonData").(structs.UnreadNotification)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = int32(json.UserId)
	} else {
		userId = int32(headers.UserId)
	}

	notiData := &notificationPB.UnreadNotification{
		UserId: userId,
		Token:  headers.AccessToken,
	}

	response, err := cl.UnreadNotificationCountGet(context.TODO(), notiData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}
	if response.Status != nil && response.Status.Message == common.NOT_AUTHORIZED {
		render.AbortWithStatusJSON(c, http.StatusUnauthorized, response.Status.Status, response.Status.Message)
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func PublishLeadsFromProviderC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = notificationPB.NewNotificationService(serviceList.Notification, service.Client())

	var req structs.PublishLeadRequest
	req = c.MustGet("jsonData").(structs.PublishLeadRequest)

	json_obj := req.Data

	notiData := &notificationPB.LeadRequest{
		CallerId:         json_obj.CallerId,
		AgentId:          json_obj.AgentId,
		AgentName:        json_obj.AgentName,
		AgentPhoneNumber: json_obj.AgentPhoneNumber,
		StartTime:        json_obj.StartTime,
		TimeToAnswer:     json_obj.TimeToAnswer,
		EndTime:          json_obj.EndTime,
		CallDuration:     json_obj.CallDuration,
		Status:           json_obj.Status,
		AudioFile:        json_obj.AudioFile,
		AgentStatus:      json_obj.AgentStatus,
		DialedNumber:     json_obj.DialedNumber,
		CampaignName:     json_obj.CampaignName,
		DId:              json_obj.DId,
		Disposition:      json_obj.Disposition,
		Location:         json_obj.Location,
		PhoneName:        json_obj.PhoneName,
		Skill:            json_obj.Skill,
		HangupBy:         json_obj.HangupBy,
		Apikey:           json_obj.Apikey,
	}

	response, err := cl.PublishLeadsFromProvider(context.TODO(), notiData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}
