package notificationController

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	timestamp "github.com/golang/protobuf/ptypes/timestamp"

	"bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	notificationPB "bitbucket.org/jogocoin/go_api/api/proto/notification"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
)

var (
	ClickActionOpenGenericBottomSheet int32 = 22
	ActionSpecialNotice               int32 = 1
	ActionBooking                     int32 = 4
	ActionUpcomingBooking             int32 = 6
	ActionPastBooking                 int32 = 7
	EpochTimeISTSeconds               int64 = -19800
)

const (
	Booking_Successful    = 1
	Attendance_Marked     = 2
	NoShow_Reverted       = 3
	NoShow_Not_Reverted   = 4
	Booking_Cancelled     = 5
	Notification_General  = 6
	Notification_Reminder = 7
	Membership_Purchased  = 8
	Membership_Cancelled  = 9
	Refund_Initiated      = 10
)

var NotificationIconMap = map[int32]string{
	Booking_Successful:    "uploads/Notification_BookingConfirmation1624279143.png",
	Attendance_Marked:     "uploads/Notification_AttendanceMarked1624279189.png",
	NoShow_Reverted:       "uploads/Notification_AttendanceMarked1624279189.png",
	NoShow_Not_Reverted:   "uploads/Notification_General1624279385.png", //"uploads/Notification_NoShow1624279278.png",
	Booking_Cancelled:     "uploads/Notification_Cancel1624279338.png",
	Notification_General:  "uploads/Notification_General1624279385.png",
	Notification_Reminder: "uploads/Notification_Reminder1624279420.png",
	Membership_Purchased:  "uploads/Notification_MebershipPurchase1624279475.png",
	Membership_Cancelled:  "uploads/Notification_MembershipCanceled1624279517.png",
	Refund_Initiated:      "uploads/Notification_RefundInitiated1624279559.png",
}

type NotificationResponse struct {
	Status            *notificationPB.Status  `json:"status,omitempty"`
	SportNotification []SportNotificationList `json:"sport_notification,omitempty"`
}

type SportNotificationList struct {
	NotificationId int32                `json:"notification_id,omitempty"`
	UserId         int32                `json:"user_id,omitempty"`
	Title          string               `json:"title,omitempty"`
	Text           string               `json:"text,omitempty"`
	ActionId       int32                `json:"action_id,omitempty"`
	Id             int32                `json:"id,omitempty"`
	CtaText        string               `json:"cta_text,omitempty"`
	IsRead         bool                 `json:"is_read,omitempty"`
	CreatedAt      *timestamp.Timestamp `json:"created_at,omitempty"`
	Action         string               `json:"action,omitempty"`
	ActionIcon     string               `json:"action_icon,omitempty"`
	Url            string               `json:"url,omitempty"`
	ClickAction    *sushi.ClickAction   `json:"click_action,omitempty"`
}

func GetNotificationsForUserC(c *gin.Context) {

	notificationClient := util.GetNotificationServiceClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.Notification
	json = c.MustGet("jsonData").(structs.Notification)

	notificationData := &notificationPB.SportNotification{
		NotificationId: json.NotificationId,
		Start:          json.Start,
		Count:          json.Count,
	}

	response, err := notificationClient.GetNotificationLogs(ctx, notificationData)
	if err != nil {
		log.Println("Error in GetNotificationsForUserC when getting entries for notification %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
		return
	}
	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Message == common.NOT_AUTHORIZED {
		log.Println("Error in GetNotificationsForUserC, User not authorized %v", err)
		c.JSON(http.StatusUnauthorized, models.StatusFailure(response.Status.Message))
		return
	}

	if featuresupport.SupportsNewNotification(c) {
		notificationResponse := GetClickActionOfNotification(response)
		c.JSON(statusCode, notificationResponse)
	} else {
		c.JSON(statusCode, response)
	}
}

func GetClickActionOfNotification(NotificationData *notificationPB.GetNotificationResponse) NotificationResponse {

	var notificationResponse NotificationResponse
	var sportNotificationList []SportNotificationList
	for _, notification := range NotificationData.SportNotification {
		SportNotification := SetNotificationData(notification)
		if len(notification.ClickAction) > 0 {
			var clickAction *sushi.ClickAction
			_ = json.Unmarshal([]byte(notification.ClickAction), &clickAction)
			if notification.ActionId == ClickActionOpenGenericBottomSheet {
				SetSubtitleOfBottomCard(clickAction, notification)
			}
			SportNotification.ClickAction = clickAction
		} else {
			clickAction := SetOldNotificationDeeplink(notification)
			SportNotification.ClickAction = clickAction
		}
		sportNotificationList = append(sportNotificationList, SportNotification)
	}
	if len(sportNotificationList) > 0 {
		notificationResponse.SportNotification = sportNotificationList
	}
	notificationResponse.Status = NotificationData.Status
	return notificationResponse
}

func SetOldNotificationDeeplink(notification *notificationPB.SportNotification) *sushi.ClickAction {

	clickAction := sushi.GetClickAction()
	if notification.ActionId == ActionBooking || notification.ActionId == ActionUpcomingBooking || notification.ActionId == ActionPastBooking {
		deeplink := &sushi.Deeplink{
			URL: util.GetYourBookingsDeeplink(),
		}
		clickAction.SetDeeplink(deeplink)

	} else if notification.ActionId == ActionSpecialNotice && len(notification.Title) > 0 && len(notification.Text) > 0 {
		reasonsSheetTitle, _ := sushi.NewTextSnippet("Notification")
		bottomSheetLeftImage, _ := sushi.NewImage(util.GetCDNLink("uploads/Frame2177401707461271.png"))
		header := &sushi.GenericBottomSheetHeader{
			Title: reasonsSheetTitle,
			Image: bottomSheetLeftImage,
		}

		fontTitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		title, _ := sushi.NewTextSnippet(notification.Title)
		title.SetFont(fontTitle)

		createdAt, _ := ptypes.Timestamp(notification.CreatedAt)
		subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s", createdAt.Format("2 Jan, 2006")))
		fontSubtitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		colorSubtitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		subtitle.SetFont(fontSubtitle)
		subtitle.SetColor(colorSubtitle)

		pointer_title_item := &sushi.TextSnippetType1Snippet{
			Title:     title,
			Subtitle1: subtitle,
		}

		fontText, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
		text, _ := sushi.NewTextSnippet(notification.Text)
		text.SetFont(fontText)

		link_config_color := &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorTypeBlue,
		}
		link_config := &sushi.LinkConfig{
			Color: link_config_color,
		}
		pointer_text_item := &sushi.TextSnippetType12Snippet{
			Title:      text,
			LinkConfig: link_config,
		}

		layoutConfig1 := &sushi.LayoutConfig{
			SnippetType:  sushi.TextSnippetType1,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		pointer_title_snippet_item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:            layoutConfig1,
			TextSnippetType1Snippet: pointer_title_item,
		}

		layoutConfig2 := &sushi.LayoutConfig{
			SnippetType:  sushi.TextSnippetType12,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		pointer_text_snippet_item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig2,
			TextSnippetType12Snippet: pointer_text_item,
		}

		openGenericBottomSheet := &sushi.OpenGenericBottomSheet{
			Header: header,
			Items:  []*sushi.CustomTextSnippetTypeLayout{pointer_title_snippet_item, pointer_text_snippet_item},
		}
		clickAction.SetGenericBottomSheet(openGenericBottomSheet)

	} else {
		deeplink := &sushi.Deeplink{
			URL: util.GetHomeDeeplink(),
		}
		clickAction.SetDeeplink(deeplink)
	}
	return clickAction
}

func SetSubtitleOfBottomCard(clickAction *sushi.ClickAction, notification *notificationPB.SportNotification) {
	createdAt, _ := ptypes.Timestamp(notification.CreatedAt)
	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s", createdAt.Format("2 Jan, 2006")))
	fontSubtitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	colorSubtitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	subtitle.SetFont(fontSubtitle)
	subtitle.SetColor(colorSubtitle)
	if clickAction.OpenGenericBottomSheet != nil && len(clickAction.OpenGenericBottomSheet.Items) > 0 {
		clickAction.OpenGenericBottomSheet.Items[0].TextSnippetType1Snippet.Subtitle1 = subtitle
	}
}

func SetNotificationData(notification *notificationPB.SportNotification) SportNotificationList {

	title := notification.Title
	if notification.ActionId != ClickActionOpenGenericBottomSheet && notification.ActionId != ActionSpecialNotice {
		title = notification.Text
	}
	isRead := notification.IsRead
	if notification.ExpiryDate.Seconds == EpochTimeISTSeconds {
		createdAt, _ := ptypes.Timestamp(notification.CreatedAt)
		if createdAt.AddDate(0, 0, 30).Before(time.Now()) {
			isRead = true
		}
	}
	var icon string
	if len(NotificationIconMap[notification.SubCategory]) > 0 {
		icon = NotificationIconMap[notification.SubCategory]
	} else {
		icon = "uploads/booking1622118792.png"
	}

	response := SportNotificationList{
		NotificationId: notification.NotificationId,
		UserId:         notification.UserId,
		Title:          title,
		Text:           notification.Text,
		ActionId:       notification.ActionId,
		Action:         notification.Action,
		Id:             notification.Id,
		CtaText:        notification.CtaText,
		IsRead:         isRead,
		CreatedAt:      notification.CreatedAt,
		ActionIcon:     util.GetCDNLink(icon),
		Url:            notification.Url,
	}
	return response
}
