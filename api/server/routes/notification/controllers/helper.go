package notificationController

import (
	"fmt"
	ver "github.com/hashicorp/go-version"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

func CompareVersions(configData *structs.SportsAppConfiguration, forceUpdate *bool) error {
	//compare with min app version - if current app-version less than minimum, forceUpdate = true, else false
	fmt.Println("Req config data --- ", configData) //contains current app-type and app-version

	var minAppVersion string //min working app-version
	if configData.AppType == "sports-android" {
		minAppVersion = "1.0.5"
	} else if configData.AppType == "sports-ios" {
		minAppVersion = "1.0.4"
	}

	cv1, err1 := ver.NewVersion(configData.AppVersion)
	if err1 != nil {
		fmt.Println("error in parsing current app version: ", err1)
		return err1
	}

	cv2, err2 := ver.<PERSON>Vers<PERSON>(minAppVersion)
	if err2 != nil {
		fmt.Println("error in parsing minimum app version: ", err2)
		return err2
	}

	success := bool(cv1.LessThan(cv2))

	if success {
		*forceUpdate = true
	} else {
		*forceUpdate = false
	}

	return nil
}
