package commonController

import (
	"strconv"
	userController "bitbucket.org/jogocoin/go_api/api/server/routes/user/controllers"
	userModels "bitbucket.org/jogocoin/go_api/api/models/user"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"github.com/gin-gonic/gin"
)

func PurchaseMembersListC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(userModels.PurchaseMemberList)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryID, _ := strconv.Atoi(requestData.ProductCategoryId)
	if categoryID == common.SummerCampCategoryID {
		userController.GetSummercampPurchaseMembersC(c)
	} else {
		userController.GetAcademyPurchaseMembersC(c)
	}
}
