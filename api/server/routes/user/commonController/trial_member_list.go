package commonController

import (
	"strconv"
	"fmt"
	userController "bitbucket.org/jogocoin/go_api/api/server/routes/user/controllers"
	"bitbucket.org/jogocoin/go_api/api/structs"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"github.com/gin-gonic/gin"
)

func TrialMembersListC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(structs.GetAcademyTrialUsersRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryId := fmt.Sprintf("%v", requestData.ProductCategoryId)
	categoryID, _ := strconv.Atoi(categoryId)
	if categoryID == common.SummerCampCategoryID {
		userController.GetSummercampTrialUsersC(c)
	} else {
		userController.GetAcademyTrialUsersC(c)
	}
}
