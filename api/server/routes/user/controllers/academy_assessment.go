package userController

import (
	"context"
	"fmt"
	"log"
	"net/http"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	academyModels "bitbucket.org/jogocoin/go_api/api/models/booking"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	featureSupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type AssessmentPageTemplate struct {
	Response *userPB.GetAcademyAssessmentResponse  `json:"-"`
	Results  []*academyModels.AssessmentPageResult `json:"results,omitempty"`
	Header   *academyModels.AssessmentPageHeader   `json:"header,omitempty"`
	Footer   *sushi.FooterSnippetType2Layout       `json:"footer,omitempty"`
}

func GetAcademyAssessmentC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.AcademyAssessmentRequest)
	assessmentID := json.AssessmentID

	loggedInUserId := util.GetUserIDFromContext(ctx)

	if loggedInUserId == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure("You need to login for this action"))
		return
	}

	if assessmentID <= 0 {
		log.Printf("Error in GetAcademyAssessmentC - invalid assessment_id: %d", assessmentID)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	userClient := util.GetUserServiceClient()
	assessment, err := userClient.GetAcademyAssessment(ctx, &userPB.GetAcademyAssessmentRequest{
		AssessmentId: assessmentID,
	})

	if err != nil {
		log.Printf("Error in GetAcademyAssessmentC for user_id: %d, error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}

	if assessment.Status != nil && assessment.Status.Status == apiCommon.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure("You are not authorized"))
		return
	}

	assessmentPageTemplate := &AssessmentPageTemplate{
		Response: assessment,
	}

	assessmentPageTemplate.setHeader(ctx)
	assessmentPageTemplate.setResults(ctx)
	assessmentPageTemplate.setFooter(ctx)

	c.JSON(http.StatusOK, assessmentPageTemplate)
}

func (template *AssessmentPageTemplate) setHeader(ctx context.Context) {
	title, _ := sushi.NewTextSnippet(fmt.Sprintf("%s's Assessment", template.Response.UserName))
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	template.Header = &academyModels.AssessmentPageHeader{
		Title: title,
	}
}

func (template *AssessmentPageTemplate) setResults(ctx context.Context) {
	infoCardImage, _ := sushi.NewImage(template.Response.SportImage)
	infoCardImage.SetAspectRatio(1)
	infoCardImage.SetType(sushi.ImageTypeCircle)
	infoCardImage.SetHeight(48)
	infoCardImage.SetWidth(48)

	infoCardTitle, _ := sushi.NewTextSnippet(template.Response.SportName)
	infoCardTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	infoCardTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	infoCardTitle.SetFont(infoCardTitleFont)
	infoCardTitle.SetColor(infoCardTitleColor)

	infoCardSubtitle, _ := sushi.NewTextSnippet(template.Response.BookingDate)
	infoCardSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	infoCardSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	infoCardSubtitle.SetColor(infoCardSubtitleColor)
	infoCardSubtitle.SetFont(infoCardSubtitleFont)

	infoCardSubtitle1, _ := sushi.NewTextSnippet(template.Response.CourseCategoryName)
	infoCardSubtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	infoCardSubtitle1Color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	prefixIcon, _ := sushi.NewIcon(sushi.CheckIcon, iconColor)

	infoCardSubtitle1.SetColor(infoCardSubtitle1Color)
	infoCardSubtitle1.SetFont(infoCardSubtitle1Font)
	infoCardSubtitle1.SetPrefixIcon(prefixIcon)

	infoCardBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)

	infoCardContainer1Title, _ := sushi.NewTextSnippet("Assessment Center")
	infoCardContainer1TitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	infoCardContainer1TitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	infoCardContainer1Title.SetFont(infoCardContainer1TitleFont)
	infoCardContainer1Title.SetColor(infoCardContainer1TitleColor)

	infoCardContainer1subtitle, _ := sushi.NewTextSnippet(template.Response.FacilityName)
	infoCardContainer1subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	infoCardContainer1subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	infoCardContainer1subtitle.SetFont(infoCardContainer1subtitleFont)
	infoCardContainer1subtitle.SetColor(infoCardContainer1subtitleColor)

	infoCardContainer1 := &sushi.FitsoImageTextSnippetType14ItemContainer{
		Title:    infoCardContainer1Title,
		Subtitle: infoCardContainer1subtitle,
	}

	infoCardLayout := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoImageTextSnippetType14,
	}

	infoCardSnippet := &sushi.FitsoImageTextSnippetType14SnippetItem{
		Image:      infoCardImage,
		Title:      infoCardTitle,
		Subtitle:   infoCardSubtitle,
		Subtitle1:  infoCardSubtitle1,
		BgColor:    infoCardBgColor,
		Container1: infoCardContainer1,
		//Container2: infoCardContainer2,
	}

	if template.Response != nil && len(template.Response.CoachName) > 0 {
		infoCardContainer2Title, _ := sushi.NewTextSnippet("Assessment coach")
		infoCardContainer2TitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
		infoCardContainer2TitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		infoCardContainer2Title.SetFont(infoCardContainer2TitleFont)
		infoCardContainer2Title.SetColor(infoCardContainer2TitleColor)

		infoCardContainer2subtitle, _ := sushi.NewTextSnippet(template.Response.CoachName)
		infoCardContainer2subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		infoCardContainer2subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		infoCardContainer2subtitle.SetFont(infoCardContainer2subtitleFont)
		infoCardContainer2subtitle.SetColor(infoCardContainer2subtitleColor)

		infoCardContainer2 := &sushi.FitsoImageTextSnippetType14ItemContainer{
			Title:    infoCardContainer2Title,
			Subtitle: infoCardContainer2subtitle,
		}
		infoCardSnippet.Container2 = infoCardContainer2
	}

	template.Results = append(template.Results, &academyModels.AssessmentPageResult{
		LayoutConfig: infoCardLayout,
		FitsoImageTextSnippetType14: &sushi.FitsoImageTextSnippetType14Snippet{
			Items: []*sushi.FitsoImageTextSnippetType14SnippetItem{infoCardSnippet},
		},
	})

	skillHeadingLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	skillHeadingTitle, _ := sushi.NewTextSnippet("ASSESSMENT REPORT")
	skillHeadingTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	skillHeadingTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

	skillHeadingTitle.SetFont(skillHeadingTitleFont)
	skillHeadingTitle.SetColor(skillHeadingTitleColor)
	skillHeadingTitle.SetKerning(3)

	skillHeadingSnippet := &sushi.SectionHeaderType1Snippet{
		Title: skillHeadingTitle,
	}

	template.Results = append(template.Results, &academyModels.AssessmentPageResult{
		LayoutConfig:       skillHeadingLayout,
		SectionHeaderType1: skillHeadingSnippet,
	})

	for assessmentSkillIndex, assessmentSkill := range template.Response.AssessmentSkills {
		skillLayout := &sushi.LayoutConfig{
			SnippetType: sushi.SectionHeaderType1,
		}

		if assessmentSkill.Name != "" {
			skillTitle, _ := sushi.NewTextSnippet(assessmentSkill.Name)
			skillTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			skillTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

			skillTitle.SetFont(skillTitleFont)
			skillTitle.SetColor(skillTitleColor)

			skillHeadingSnippet := &sushi.SectionHeaderType1Snippet{
				Title: skillTitle,
			}

			template.Results = append(template.Results, &academyModels.AssessmentPageResult{
				LayoutConfig:       skillLayout,
				SectionHeaderType1: skillHeadingSnippet,
			})
		}

		for assessmentSkillAttributeIndex, assessmentSkillAttribute := range assessmentSkill.SkillAttributes {
			var items []*sushi.FitsoImageTextSnippetType4SnippetItem

			attributeLayout := &sushi.LayoutConfig{
				SnippetType: sushi.FitsoImageTextSnippetType4,
			}

			attributeTitle, _ := sushi.NewTextSnippet(assessmentSkillAttribute.Name)
			attributeTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			attributeTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)

			if assessmentSkill.Name == "" {
				attributeLayout.LayoutType = sushi.LayoutTypeGrid
				attributeLayout.SectionCount = 1
				attributeTitleFont, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
				attributeTitleColor, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			}

			attributeTitle.SetFont(attributeTitleFont)
			attributeTitle.SetColor(attributeTitleColor)

			starColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if featureSupport.SupportsNewColor(ctx) {
				starColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			}
			starUnfilledColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
			bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			defaultColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

			attributeMarksSnippet := &sushi.RatingSnippetBlockItem{
				Value:             assessmentSkillAttribute.MarksObtained,
				Size:              sushi.RatingSizeLarge,
				StarColor:         starColor,
				StarUnfilledColor: starUnfilledColor,
				BgColor:           bgColor,
				DefaultColor:      defaultColor,
			}

			attributeSnippet := &sushi.FitsoImageTextSnippetType4SnippetItem{
				Title: attributeTitle,
				Rating: &sushi.RatingSnippet{
					Type:  sushi.RatingTypeStars,
					Stars: attributeMarksSnippet,
				},
			}

			items = append(items, attributeSnippet)

			assessmentResult := &academyModels.AssessmentPageResult{
				LayoutConfig: attributeLayout,
				FitsoImageTextSnippetType4: &sushi.FitsoImageTextSnippetType4Snippet{
					Items: items,
				},
			}

			if (assessmentSkillAttributeIndex == len(assessmentSkill.SkillAttributes)-1 && assessmentSkillIndex != len(template.Response.AssessmentSkills)-1) || (assessmentSkillAttributeIndex < len(assessmentSkill.SkillAttributes)-1 && assessmentSkill.Name == "") {
				bottomSeparatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
				bottomSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, bottomSeparatorColor)

				snippetConfig := &sushi.SnippetConfig{
					BottomSeparator: bottomSeparator,
				}

				assessmentResult.SnippetConfig = snippetConfig
			}

			template.Results = append(template.Results, assessmentResult)
		}
	}
}

func (template *AssessmentPageTemplate) setFooter(ctx context.Context) {
	assessmentSportId := template.Response.SportId
	facilityClient := util.GetFacilitySportClient()
	response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
	if err != nil {
		return
	}
	if !util.Contains(response.AcademySportIds, assessmentSportId) {
		return
	}

	items := []sushi.FooterSnippetType2ButtonItem{}
	if !featureSupport.SupportsAcademyPurchaseFlow(ctx) {
		buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)

		footerButtonItem := sushi.FooterSnippetType2ButtonItem{
			Type: sushi.FooterButtonTypeSolid,
			Text: "Explore membership plans",
			Font: buttonFont,
		}

		clickAction := sushi.GetClickAction()
		clickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetCourseCategoryDeeplink(template.Response.CourseId, apiCommon.PAGE_TYPE_COURSE_CATEGORY, template.Response.CourseCategoryId, "TAB_TYPE_KIDS_SPORTS_PLANS"),
		})

		footerButtonItem.ClickAction = clickAction
		items = ([]sushi.FooterSnippetType2ButtonItem{footerButtonItem})
	} else {
		items = homeC.GetAcademyFooterCTA(ctx, "academy_trial_sport_select", response.AcademySportIds, 0)
	}

	if len(items) > 0 {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.FooterSnippetType2,
		}
		footerButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}
		template.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig: layoutConfig,
			FooterSnippetType2: &sushi.FooterSnippetType2Snippet{
				ButtonData: footerButton,
			},
		}
	}
}
