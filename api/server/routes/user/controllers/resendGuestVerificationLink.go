package userController

import (
	"context"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type ResendVerificationLinkTemplate struct {
	Status     string            `json:"status,omitempty"`
	ActionList *[]ActionListItem `json:"action_list,omitempty"`
}

type ActionListItem struct {
	Type      sushi.ActionType `json:"type,omitempty"`
	ShowToast *sushi.ShowToast `json:"show_toast,omitempty"`
}

// AddEditGuestC is the controller layer which verifies and adds guest user mappings
func ResendLinkC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	loggedInUserId := util.GetUserIDFromContext(ctx)

	template := ResendVerificationLinkTemplate{}
	if !(loggedInUserId > 0) {
		template.SetTemplate(ctx, "YOU ARE UNATHORISED")
		c.JSON(http.StatusUnauthorized, template)
		return
	}
	cl = util.GetUserServiceClient()
	request := prepareResendLinkRequest(c)
	response, err := cl.ResendVerificationLinkToGuest(ctx, request)
	if err != nil {
		log.Println("func:ResendLinkC, error in resending link to guest user ", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if err != nil || response.Status != nil && response.Status.Status == common.FAILURE {
		template.SetTemplate(ctx, response.Status.Message)
		c.JSON(http.StatusOK, template)
		return
	}

	template.SetTemplate(ctx, response.Status.Message)
	c.JSON(http.StatusOK, template)
}

func prepareResendLinkRequest(c *gin.Context) *userPB.ResendGuestVerificationLinkReq {

	var json structs.ResendLinkReq
	json = c.MustGet("jsonData").(structs.ResendLinkReq)

	return &userPB.ResendGuestVerificationLinkReq{
		GuestUserId: json.GuestUserId,
	}
}

func (t *ResendVerificationLinkTemplate) SetTemplate(ctx context.Context, message string) {
	title, _ := sushi.NewTextSnippet("Some error occured")
	t.Status = common.FAILED
	if message != "" {
		title, _ = sushi.NewTextSnippet(message)
		t.Status = common.SUCCESS
	}

	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	title.SetFont(font)
	title.SetColor(color)

	bg_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)

	iconColor, _ := sushi.NewColor(sushi.ColorTypeGreen, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.CheckCircleIcon, iconColor)

	toast := &sushi.ShowToast{
		Icon1:   icon,
		Title:   title,
		BgColor: bg_color,
	}
	item := &ActionListItem{
		Type:      sushi.ActionTypeShowToast,
		ShowToast: toast,
	}
	actionList := make([]ActionListItem, 0)
	actionList = append(actionList, *item)
	t.ActionList = &actionList
}
