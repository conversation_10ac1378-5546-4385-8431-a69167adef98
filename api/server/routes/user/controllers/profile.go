package userController

import (
	"context"
	"fmt"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	profileModel "bitbucket.org/jogocoin/go_api/api/models/user"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	notificationPB "bitbucket.org/jogocoin/go_api/api/proto/notification"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

const (
	SUBSCRIPTION_STATUS_EXPIRED   = "Expired"
	SUBSCRIPTION_STATUS_CANCELLED = "Cancelled"
	SUBSCRIPTION_STATUS_CURRENT   = "Current"
)

type ProfilePage struct {
	data               *userPB.ProfilePageResponse
	ProfileItems       []*profileModel.ProfileItem
	ProfileHeader      *profileModel.ProfileHeader
	BottomSectionItems []*profileModel.BottomSectionItem
	ClevertapTracking  []*sushi.ClevertapItem
}

type BottomItemData struct {
	Title           string
	ClickTitle      string
	WebURL          string
	ClickActionType string
	TapEname        *sushi.EnameData
}

func (p *ProfilePage) setProfileData(data *userPB.ProfilePageResponse) {
	p.data = data
}

func (p *ProfilePage) setProfileItems(ctx context.Context, unreadNotificationCount int32, totalNotificationCount int) {
	var profileItems []*profileModel.ProfileItem

	loggedInUser := util.GetUserIDFromContext(ctx)

	for _, profileItem := range p.data.ProfilePageOptions {
		title, _ := sushi.NewTextSnippet(profileItem.Title)
		fontReg300, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		colorGrey800, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		fontMed300, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		title.SetFont(fontMed400)
		title.SetColor(colorGrey900)

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		item := &profileModel.ProfileItem{
			Title: title,
		}

		if len(profileItem.Deeplink) > 0 {
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			clickDeeplink := &sushi.Deeplink{
				URL:            profileItem.Deeplink,
				PostbackParams: profileItem.PostbackParams,
			}
			clickAction.SetDeeplink(clickDeeplink)
			item.ClickAction = clickAction
		}

		payload := make(map[string]interface{})
		payload["user_id"] = loggedInUser

		var iconCode1 sushi.IconCode
		switch profileItem.Type {
		case userPB.ProfilePageOption_BOOKING:
			iconCode1 = sushi.MembershipIcon
			tapEname := &sushi.EnameData{}
			impressionEname := &sushi.EnameData{}
			if profileItem.KeyValue == common.PAGE_TYPE_ACADEMY {
				tapEname.Ename = "academy_sessions_tapped"
				impressionEname.Ename = "academy_sessions_impression"
			} else if profileItem.KeyValue == common.PAGE_TYPE_SUMMER_CAMP {
				tapEname.Ename = "summer_camp_bookings_tapped"
				impressionEname.Ename = "summer_camp_bookings_impression"
			} else {
				tapEname.Ename = "your_bookings_tapped"
				impressionEname.Ename = "your_bookings_impression"
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_MEMBERSHIP:
			iconCode1 = sushi.Memberships
			tapEname := &sushi.EnameData{}
			impressionEname := &sushi.EnameData{}
			if profileItem.KeyValue == common.PAGE_TYPE_ACADEMY {
				tapEname.Ename = "academy_memberships_tapped"
				impressionEname.Ename = "academy_memberships_impression"
			} else if profileItem.KeyValue == common.PAGE_TYPE_SUMMER_CAMP {
				tapEname.Ename = "summercamp_memberships_tapped"
				impressionEname.Ename = "summercamp_memberships_impression"
			} else {
				tapEname.Ename = "your_membership_tapped"
				impressionEname.Ename = "your_membership_impression"
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_BUY_MEMBERSHIP:
			iconCode1 = sushi.BuyMembership
			tapEname := &sushi.EnameData{
				Ename: "buy_membership_tapped",
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "buy_membership_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_REFER_AND_EARN:
			iconCode1 = sushi.ReferEarnIcon
			tapEname := &sushi.EnameData{
				Ename: "refer_and_earn_tapped",
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "refer_and_earn_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_BRING_A_GUEST:
			iconCode1 = sushi.BringGuestIcon
			tapEname := &sushi.EnameData{
				Ename: "bring_a_guest_tapped",
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "bring_a_guest_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

			tagTitle, _ := sushi.NewTextSnippet("NEW")
			tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			tagTitle.SetColor(tagTitleColor)

			bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

			tag := &sushi.Tag{
				Title:   tagTitle,
				BgColor: bgColor,
				Size:    sushi.TagSizeTiny,
			}
			item.Tag = tag

		case userPB.ProfilePageOption_NOTIFICATION:
			iconCode1 = sushi.NotificationIcon
			if unreadNotificationCount > 0 {
				tagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d", unreadNotificationCount))
				tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				tagTitle.SetColor(tagTitleColor)

				bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

				tag := &sushi.Tag{
					Title:   tagTitle,
					BgColor: bgColor,
					Size:    sushi.TagSizeTiny,
				}
				item.Tag = tag
			}
			tapEname := &sushi.EnameData{
				Ename: "notifications_tapped",
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "notifications_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_SUPPORT:
			iconCode1 = sushi.SupportIcon
			tapEname := &sushi.EnameData{
				Ename: "support_tapped",
			}
			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "support_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_SETTING:
			iconCode1 = sushi.SettingIcon
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenSettings)
			item.ClickAction = clickAction
			tapEname := &sushi.EnameData{
				Ename: "settings_tapped",
			}

			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "settings_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_EDIT_PROFILE:
			iconCode1 = sushi.EditIcon
			tapEname := &sushi.EnameData{
				Ename: "edit_profile_tapped",
			}

			tapPayload := payload
			trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)

			impressionPayload := payload
			impressionEname := &sushi.EnameData{
				Ename: "edit_profile_impression",
			}
			tileImpressionEvents := sushi.NewClevertapEvents()
			tileImpressionEvents.SetImpression(impressionEname)
			impressionTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, tileImpressionEvents)

			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem, impressionTrackItem}

		case userPB.ProfilePageOption_HEADING:
			item.Type = "header"
			item.Title.SetFont(fontReg300)
			item.Title.SetKerning(3)

		case userPB.ProfilePageOption_EMPTY_CONTAINER:
			item.Type = "empty_container"
			item.Title.SetFont(fontMed300)
			item.Title.SetColor(colorGrey800)
			item.Title.SetAlignment(sushi.TextAlignmentCenter)
			if len(profileItem.ButtonText) > 0 {
				buttonClickAction := GetExploreFeatureClickAction(profileItem.KeyValue)
				tapEname := &sushi.EnameData{}
				if profileItem.KeyValue == common.PAGE_TYPE_ACADEMY {
					tapEname.Ename = "explore_academy_tap"
				} else {
					tapEname.Ename = "explore_masterkey_tap"
				}
				tapPayload := payload
				trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)
				button := &sushi.Button{
					Type:              sushi.ButtontypeText,
					Text:              profileItem.ButtonText,
					Size:              sushi.ButtonSizeMedium,
					Font:              fontMed400,
					ClickAction:       buttonClickAction,
					ClevertapTracking: []*sushi.ClevertapItem{trackItem},
				}
				item.Button = button
			}
		}
		if profileItem.BottomLine {
			item.BottomSeparator = separator
		}

		icon1, _ := sushi.NewIcon(iconCode1, nil)
		if profileItem.Type != userPB.ProfilePageOption_HEADING && profileItem.Type != userPB.ProfilePageOption_EMPTY_CONTAINER {
			icon2, _ := sushi.NewIcon(sushi.ChevronRightIcon, nil)
			item.Icon2 = icon2
		}

		item.Icon1 = icon1
		if profileItem.Title != "Notifications" || totalNotificationCount > 0 {
			profileItems = append(profileItems, item)
		}
	}

	p.ProfileItems = profileItems
}

func (p *ProfilePage) setHeaderData(ctx context.Context) {
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	profileHeader := profileModel.ProfileHeader{}

	if len(p.data.Name) == 0 {
		p.data.Name = "User"
	}
	title, _ := sushi.NewTextSnippet(p.data.Name)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetFont(font)
	profileHeader.Title = title

	if len(p.data.Email) > 0 {
		subtitle1, _ := sushi.NewTextSnippet(p.data.Email)
		subtitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle1.SetFont(subtitle1Font)
		profileHeader.Subtitle1 = subtitle1
	}
	if len(p.data.Number) > 0 {
		subtitle2, _ := sushi.NewTextSnippet(p.data.Number)
		subtitle2Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle2.SetFont(subtitle2Font)
		profileHeader.Subtitle2 = subtitle2
	}

	if len(p.data.ProfileImage) > 0 {
		image, _ := sushi.NewImage(p.data.ProfileImage)
		profileHeader.ProfileImage = image
	}
	for _, data := range p.data.MembershipData {
		membershipData := profileModel.MembershipData{}
		if len(data.MembershipTitle) > 0 {
			membershipTitle, _ := sushi.NewTextSnippet(data.MembershipTitle)
			titleColor, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
			membershipTitle.SetFont(titleColor)
			membershipData.Title = membershipTitle
		}

		if len(data.MembershipSubtitle) > 0 {
			membershipSubtitle, _ := sushi.NewTextSnippet(data.MembershipSubtitle)
			colorType := sushi.ColorTypeBlue
			if isNewColorSupported {
				colorType = sushi.NEUTRAL_LIGHT_THEME
			}
			membershipSubtitleColor, _ := sushi.NewColor(colorType, sushi.ColorTint500)
			if data.MembershipStatus == SUBSCRIPTION_STATUS_EXPIRED || data.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED {
				membershipSubtitleColor, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			} else if data.MembershipCompletion > 0 {
				membershipSubtitleColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			} else if data.MembershipCompletion == 0 && data.MembershipStatus == SUBSCRIPTION_STATUS_CURRENT {
				colorType := sushi.ColorTypeOrange
				if featuresupport.SupportsNewColor(ctx) {
					colorType = sushi.ALERT_LIGHT_THEME
				}
				membershipSubtitleColor, _ = sushi.NewColor(colorType, sushi.ColorTint500)
			}
			membershipSubtitle.SetColor(membershipSubtitleColor)
			subTitleColor, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			membershipSubtitle.SetFont(subTitleColor)
			membershipData.Subtitle = membershipSubtitle
		}
		if data.IsSubcriptionActive {
			if data.MembershipCompletion > 0 {
				progressBar := &sushi.ProgressBar{
					Progress: data.MembershipCompletion,
				}
				bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
				progressColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

				progressBar.BgColor = bgColor
				progressBar.ProgressColors = []*sushi.Color{progressColor}

				membershipData.ProgressBarData = progressBar
			}
		}

		clickActionPurchase := sushi.GetClickAction()
		deeplinkPurchase := sushi.Deeplink{
			URL: data.ButtonDeeplink,
		}

		var buttonType sushi.ButtonType
		tapEname := &sushi.EnameData{}
		if data.ButtonText == "Buy another membership" {
			buttonType = sushi.ButtonTypeOutlined
			tapEname = &sushi.EnameData{
				Ename: "buy_membership_tapped",
			}
		} else {
			buttonType = sushi.ButtonTypeSolid
			tapEname = &sushi.EnameData{
				Ename: "renew_membership_tapped",
			}
		}
		clickActionPurchase.SetDeeplink(&deeplinkPurchase)
		button, _ := sushi.NewButton(buttonType)
		button.SetSize(sushi.ButtonSizeLarge)
		button.SetText(data.ButtonText)
		buttonColor, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		button.SetFont(buttonColor)
		button.ClickAction = clickActionPurchase

		tapPayload := make(map[string]interface{})
		trackItem := getClevertapTrackItem(ctx, tapEname, tapPayload)
		button.AddClevertapTrackingItem(trackItem)

		membershipData.Button = button
		profileHeader.MembershipData = &membershipData
	}
	p.ProfileHeader = &profileHeader
}

func GetExploreFeatureClickAction(keyValue string) *sushi.ClickAction {
	actionList := make([]*sushi.SaveActionItem, 0)

	refreshPageItem := &sushi.RefreshPageItem{
		Type: common.PAGE_TYPE_HOME,
	}
	dismissPage := &sushi.DismissPage{
		Type:         "refresh_pages",
		RefreshPages: []*sushi.RefreshPageItem{refreshPageItem},
	}

	saveActionItem := &sushi.SaveActionItem{
		Type:        sushi.ActionTypeDismissPage,
		DismissPage: dismissPage,
	}
	actionList = append(actionList, saveActionItem)

	saveKeyItem := &sushi.SaveKeyItem{
		Key:   "product_type",
		Value: keyValue,
	}
	saveKey := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{saveKeyItem},
		ActionList: actionList,
	}
	saveKeyClickAction := sushi.GetClickAction()
	saveKeyClickAction.SetSaveKey(saveKey)
	return saveKeyClickAction
}

func (p *ProfilePage) setAcademyMkHeaderData(ctx context.Context) {
	profileHeader := profileModel.ProfileHeader{}

	if len(p.data.Name) == 0 {
		p.data.Name = "User"
	}
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)

	fontBold700, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	fontReg300, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	fontMed500, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	fontMed50, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
	fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	fontMed300, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title, _ := sushi.NewTextSnippet(p.data.Name)
	title.SetFont(fontBold700)
	profileHeader.Title = title

	if len(p.data.Email) > 0 {
		subtitle1, _ := sushi.NewTextSnippet(p.data.Email)
		subtitle1.SetFont(fontReg300)
		profileHeader.Subtitle1 = subtitle1
	}
	if len(p.data.Number) > 0 {
		subtitle2, _ := sushi.NewTextSnippet(p.data.Number)
		subtitle2.SetFont(fontReg300)
		profileHeader.Subtitle2 = subtitle2
	}

	if len(p.data.ProfileImage) > 0 {
		image, _ := sushi.NewImage(p.data.ProfileImage)
		profileHeader.ProfileImage = image
	}
	for _, data := range p.data.MembershipData {
		membershipData := profileModel.MembershipData{}
		if len(data.MembershipTitle) > 0 {
			membershipTitle, _ := sushi.NewTextSnippet(data.MembershipTitle)
			membershipTitle.SetFont(fontMed500)
			membershipData.Title = membershipTitle
		}

		if len(data.MembershipSubtitle1) > 0 {
			membershipSubtitle1, _ := sushi.NewTextSnippet(data.MembershipSubtitle1)
			membershipSubtitle1.SetColor(colorGrey900)
			membershipSubtitle1.SetFont(fontMed50)
			membershipData.Subtitle1 = membershipSubtitle1
		}

		if len(data.MembershipSubtitle) > 0 {
			membershipSubtitle, _ := sushi.NewTextSnippet(data.MembershipSubtitle)
			colorType := sushi.ColorTypeBlue
			membershipSubtitleColor, _ := sushi.NewColor(colorType, sushi.ColorTint500)
			if data.MembershipStatus == SUBSCRIPTION_STATUS_EXPIRED || data.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED {
				membershipSubtitleColor, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint600)
			} else if len(data.ButtonText) > 0 && data.MembershipStatus == SUBSCRIPTION_STATUS_CURRENT {
				colorType := sushi.ColorTypeOrange
				if featuresupport.SupportsNewColor(ctx) {
					colorType = sushi.ALERT_LIGHT_THEME
				}
				membershipSubtitleColor, _ = sushi.NewColor(colorType, sushi.ColorTint500)
			} else if data.MembershipStatus == SUBSCRIPTION_STATUS_CURRENT {
				membershipSubtitleColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			}
			membershipSubtitle.SetColor(membershipSubtitleColor)
			membershipSubtitle.SetFont(fontMed200)
			membershipData.Subtitle = membershipSubtitle
		}

		if data.MembershipCompletion > 0 {
			progressBar := &sushi.ProgressBar{
				Progress: data.MembershipCompletion,
			}
			bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
			progressColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			if data.MembershipStatus == SUBSCRIPTION_STATUS_EXPIRED || data.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED {
				progressColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			}
			progressBar.BgColor = bgColor
			progressBar.ProgressColors = []*sushi.Color{progressColor}

			membershipData.ProgressBarData = progressBar
		}
		if data.ChangeFeature { //change feature type
			buttonClickAction := GetExploreFeatureClickAction(data.KeyValue)
			tapEname := &sushi.EnameData{}
			if data.KeyValue == common.PAGE_TYPE_ACADEMY {
				tapEname.Ename = "explore_academy_tap"
			} else {
				tapEname.Ename = "explore_masterkey_tap"
			}
			trackItem := getClevertapTrackItem(ctx, tapEname, payload)

			button := &sushi.Button{
				Type:              sushi.ButtonTypeOutlined,
				Text:              data.ButtonText,
				Size:              sushi.ButtonSizeMedium,
				Font:              fontMed300,
				ClickAction:       buttonClickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			membershipData.Button = button
		} else if data.IsAcademyTrial { //trial bottom sheet
			buttonClickAction := p.GetTrialButtonClickAction(ctx)
			tapEname := &sushi.EnameData{
				Ename: data.Ename,
			}
			trackItem := getClevertapTrackItem(ctx, tapEname, payload)
			button := &sushi.Button{
				Type:              sushi.ButtonTypeOutlined,
				Text:              data.ButtonText,
				Size:              sushi.ButtonSizeMedium,
				Font:              fontMed300,
				ClickAction:       buttonClickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			membershipData.Button = button
		} else if len(data.ButtonText) > 0 {
			deeplinkPurchase := sushi.Deeplink{
				URL: data.ButtonDeeplink,
			}
			clickAction := sushi.GetClickAction()
			clickAction.SetDeeplink(&deeplinkPurchase)

			tapEname := &sushi.EnameData{
				Ename: data.Ename,
			}
			trackItem := getClevertapTrackItem(ctx, tapEname, payload)
			button := &sushi.Button{
				Text:              data.ButtonText,
				ClickAction:       clickAction,
				Size:              sushi.ButtonSizeMedium,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			if data.MembershipCompletion == 0 { //full button
				button.Type = sushi.ButtonTypeOutlined
				button.Font = fontMed300
			} else { //small button
				button.Type = sushi.ButtonTypeSolid
				button.Font = fontMed200
			}

			membershipData.Button = button
		}
		impressionEname := &sushi.EnameData{}
		if data.KeyValue == common.PAGE_TYPE_ACADEMY {
			impressionEname.Ename = "academy_card_impression"
		} else if data.KeyValue == common.PAGE_TYPE_SUMMER_CAMP {
			impressionEname.Ename = "summercamp_card_impression"
		} else {
			impressionEname.Ename = "masterkey_card_impression"
		}
		tileImpressionEvents := sushi.NewClevertapEvents()
		tileImpressionEvents.SetImpression(impressionEname)
		impressionTrackItem := sushi.GetClevertapTrackItem(ctx, payload, tileImpressionEvents)
		membershipData.ClevertapTracking = []*sushi.ClevertapItem{impressionTrackItem}

		profileHeader.MembershipList = append(profileHeader.MembershipList, &membershipData)
	}
	p.ProfileHeader = &profileHeader
}

func (p *ProfilePage) GetTrialButtonClickAction(ctx context.Context) *sushi.ClickAction {
	facilityClient := util.GetFacilitySportClient()
	response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
	if err != nil {
		return nil
	}
	clickAction := homeC.GetTrialBookClickAction(ctx, response.AcademySportIds, 0)
	return clickAction
}

func (p *ProfilePage) setBottomSections(c *gin.Context) {

	clientId := featuresupport.GetClientID(c)
	var rateUsAppText string
	if clientId == featuresupport.Ios {
		rateUsAppText = "Rate us on the App Store"
	} else {
		rateUsAppText = "Rate us on the Play Store"
	}

	ctx := util.PrepareGRPCMetaData(c)

	bottomSectionData := []BottomItemData{
		BottomItemData{
			Title:           "FAQs",
			ClickTitle:      "See FAQs",
			WebURL:          "https://www.getfitso.com/faq?force_browser=1",
			ClickActionType: sushi.ClickActionOpenWebview,
			TapEname:        &sushi.EnameData{Ename: "faq_tapped"},
		},
		BottomItemData{
			Title:           "Terms of Use",
			ClickTitle:      "Terms & Conditions",
			WebURL:          "https://static.cult.fit/terms__cult.html?force_browser=1",
			ClickActionType: sushi.ClickActionOpenWebview,
			TapEname:        &sushi.EnameData{Ename: "terms_and_conditions_tapped"},
		},
		BottomItemData{
			Title:           "Privacy policy",
			ClickTitle:      "Privacy policy",
			WebURL:          "https://www.getfitso.com/privacy?force_browser=1",
			ClickActionType: sushi.ClickActionOpenWebview,
			TapEname:        &sushi.EnameData{Ename: "privacy_policy_tapped"},
		},
		BottomItemData{
			Title:           rateUsAppText,
			ClickActionType: sushi.ClickActionAppRate,
			TapEname:        &sushi.EnameData{Ename: "rate_us_tapped"},
		},
		BottomItemData{
			Title:           "Log Out",
			ClickActionType: sushi.ClickActionLogout,
			TapEname:        &sushi.EnameData{Ename: "logout_tapped"},
		},
	}

	for _, data := range bottomSectionData {
		title, _ := sushi.NewTextSnippet(data.Title)

		clickAction, _ := sushi.NewTextClickAction(data.ClickActionType)

		if data.ClickActionType == sushi.ClickActionOpenWebview {
			webView := &sushi.OpenWebview{
				URL:   data.WebURL,
				Title: data.ClickTitle,
				InApp: true,
			}
			clickAction.SetOpenWebview(webView)
		}

		tapPayload := make(map[string]interface{})
		trackItem := getClevertapTrackItem(ctx, data.TapEname, tapPayload)

		p.BottomSectionItems = append(p.BottomSectionItems, &profileModel.BottomSectionItem{
			Title:             title,
			ClickAction:       clickAction,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		})
	}
}

func getClevertapTrackItem(ctx context.Context, tapEname *sushi.EnameData, tapPayload map[string]interface{}) *sushi.ClevertapItem {

	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["source"] = "profile"
	profileSectionEvents := sushi.NewClevertapEvents()
	profileSectionEvents.SetTap(tapEname)
	profileSectionEventsTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, profileSectionEvents)

	return profileSectionEventsTrackItem
}

func (p *ProfilePage) SetProfilePageTracking(ctx context.Context) {

	landingPayload := make(map[string]interface{})
	landingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	landingEname := &sushi.EnameData{
		Ename: "profile_page_landing",
	}

	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, landingPayload, landingEvents)

	backArrowPayload := make(map[string]interface{})
	backArrowPayload["user_id"] = util.GetUserIDFromContext(ctx)
	backArrowEname := &sushi.EnameData{
		Ename: "profile_page_back_arrow_tap",
	}

	backArrowEvents := sushi.NewClevertapEvents()
	backArrowEvents.SetPageDismiss(backArrowEname)
	backArrowTrackItem := sushi.GetClevertapTrackItem(ctx, backArrowPayload, backArrowEvents)

	p.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem, backArrowTrackItem}
}

func UserProfileC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	userID := util.GetUserIDFromContext(ctx)
	if userID == 0 {
		c.JSON(
			http.StatusUnauthorized,
			models.StatusFailure("You are not allowed to make this request!"),
		)
		return
	}

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	profilePage := ProfilePage{}

	userClient := util.GetUserServiceClient()
	if featuresupport.SupportsAcademyFlowPage(ctx) {

		profilePageData, err := userClient.GetNewProfilePageDetails(ctx, &userPB.Empty{})
		if err != nil {
			log.Printf("error in getting user's profile page details: %v, userID: %d", err, userID)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		profilePage.setProfileData(profilePageData)
	} else {
		profilePageData, err := userClient.GetProfilePageDetails(ctx, &userPB.Empty{})
		if err != nil {
			log.Printf("error in getting user's profile page details: %v, userID: %d", err, userID)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		profilePage.setProfileData(profilePageData)
	}

	notificationClient := util.GetNotificationServiceClient()
	req := &notificationPB.UnreadNotification{UserId: userID}
	unreadNotificationCount, err := notificationClient.UnreadNotificationCountGet(ctx, req)
	if err != nil {
		log.Println("Error in getting unread notification count for user id:", userID, " Error:", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	notificationData := &notificationPB.SportNotification{
		UserId: userID,
		Count:  int32(1),
		Token:  headers.AccessToken,
	}
	totalUserNotification, err := notificationClient.GetNotificationLogs(ctx, notificationData)
	if err != nil {
		log.Println("Error in getting total notification count for user id:", userID, " Error:", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	profilePage.setProfileItems(ctx, unreadNotificationCount.UnreadCount, len(totalUserNotification.SportNotification))
	if featuresupport.SupportsAcademyFlowPage(ctx) {
		profilePage.setAcademyMkHeaderData(ctx)
	} else {
		profilePage.setHeaderData(ctx)
	}
	profilePage.setBottomSections(c)
	profilePage.SetProfilePageTracking(ctx)

	response := &profileModel.Response{
		ProfileItems:       profilePage.ProfileItems,
		ProfileHeader:      profilePage.ProfileHeader,
		BottomSectionItems: profilePage.BottomSectionItems,
		ClevertapTracking:  profilePage.ClevertapTracking,
	}

	c.JSON(http.StatusOK, response)
}
