package userController

import (
	"context"
	"encoding/json"

	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	common "bitbucket.org/jogocoin/go_api/api/common"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	facilitySportC "bitbucket.org/jogocoin/go_api/api/server/routes/facilitySport/controllers"
	homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

const (
	Profile                              string = "profile"
	TrialSports                          string = "trial_sports"
	TrialSlots                           string = "trial_slots"
	BuyForMe                             string = "buy_for_me"
	BuyForOthers                         string = "buy_for_others"
	PublishBooking                       string = "publish_booking"
	AcademySportSelect                   string = "academy_trial_sport_select"
	AcademyCoursePageTrialMemberSelect   string = "academy_course_trial_member_select"
	AcademyFacilityPageTrialMemberSelect string = "academy_facility_trial_member_select"
)

func PostLoginResponse(ctx context.Context, response *userPB.PostLoginRes, req structs.PhoneLogin) *sushi.ResponseAction {

	success_action := &sushi.ResponseAction{}
	switch req.PostAction {
	case Profile:
		success_action.Type = sushi.ActionTypeOpenProfile
		break
	case TrialSports:
		if response.TrialDetails.IsTrialActive {
			success_action.Type = sushi.ActionTypeDeeplink
			deep_link := &sushi.Deeplink{
				URL: util.GetTrialSportsPageDeeplink(),
			}
			success_action.Deeplink = deep_link
		} else {
			success_action.Type = sushi.ActionTypeCustomAlert
			success_action.CustomAlert = CustomAlertForTrial()
		}
		break
	case TrialSlots:
		if response.TrialDetails.IsTrialActive {
			success_action.Type = sushi.ActionTypeDeeplink
			deep_link := &sushi.Deeplink{
				URL: util.GetTrialSlotsPageDeeplink(),
			}
			success_action.Deeplink = deep_link
		} else {
			success_action.Type = sushi.ActionTypeCustomAlert
			success_action.CustomAlert = CustomAlertForTrial()
		}
		break
	case BuyForMe:
		if !response.ActiveSubscription {
			success_action.Type = sushi.ActionTypeDeeplink
			deep_link := &sushi.Deeplink{
				URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
				//URL: util.GetBuyingForMeDeeplink(response.ProductId, false),
			}
			success_action.Deeplink = deep_link
		} else if req.ProductId > 0 {
			success_action.Type = sushi.ActionTypeDeeplink
			deep_link := &sushi.Deeplink{
				URL: util.GetBuyingForMeDeeplink(req.ProductId, false),
			}
			success_action.Deeplink = deep_link
		} else {
			success_action.Type = sushi.ActionTypeCustomAlert
			success_action.CustomAlert = CustomAlertForBuyMembership(response)
		}
		break
	case BuyForOthers:
		success_action.Type = sushi.ActionTypeDeeplink
		deep_link := &sushi.Deeplink{
			URL: util.GetBuyingForOthersDeeplink(response.ProductId, false),
		}
		if req.ProductId > 0 {
			deep_link.URL = util.GetBuyingForOthersDeeplink(req.ProductId, false)
		}
		success_action.Deeplink = deep_link
	case PublishBooking:
		success_action.Type = sushi.ActionTypeApiCallMultiAction
		loggedInUserId := util.GetUserIDFromContext(ctx)
		out := map[string]interface{}{
			"fs_id":                     req.FsId,
			"slot_id":                   req.SlotId,
			"booking_time":              req.BookingTime,
			"product_arena_category_id": req.ProductArenaCategoryId,
			"booking_users":             &bookingPB.BookingUser{UserID: loggedInUserId},
			"source":                    req.Source,
		}
		payload, _ := json.Marshal(out)
		api_call_tap := &sushi.APICallAction{
			RequestType: sushi.POSTRequestType,
			URL:         "v2/booking/publish",
			Body:        string(payload),
		}
		success_action.ApiCallMultiAction = api_call_tap

	case AcademySportSelect:
		clickAction := homeC.GetTrialBookClickAction(ctx, req.SportIds, 0)
		success_action.Type = sushi.ActionType(clickAction.Type)
		success_action.OpenSportSelectionBottomSheet = clickAction.OpenSportSelectionBottomSheet
		success_action.Deeplink = clickAction.Deeplink

	case AcademyCoursePageTrialMemberSelect:
		clickAction := homeC.GetTrialBookClickAction(ctx, req.SportIds, req.CourseId)
		success_action.Type = sushi.ActionType(clickAction.Type)
		success_action.OpenSportSelectionBottomSheet = clickAction.OpenSportSelectionBottomSheet
		success_action.Deeplink = clickAction.Deeplink

	case AcademyFacilityPageTrialMemberSelect:
		trialButtonClickAction := facilitySportC.GetAcademyFacilityTrialButtonClickAction(ctx, req.SportId, req.FsId)
		success_action.Type = sushi.ActionType(trialButtonClickAction.Type)
		success_action.Deeplink = trialButtonClickAction.Deeplink
	}

	return success_action
}

func CustomAlertForTrial() *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("Not available")
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(title_font)
	title.SetColor(title_color)

	message, _ := sushi.NewTextSnippet("You've already used your trial sessions")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	message.SetFont(message_font)

	animation, _ := sushi.NewAnimation(util.GetFailedLoty())
	image, _ := sushi.NewImageWithAnimation(animation)

	custom_alert := &sushi.CustomAlert{
		Title:   title,
		Message: message,
		Image:   image,
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		DismissAfterAction: true,
	}

	return custom_alert
}

func CustomAlertForBuyMembership(response *userPB.PostLoginRes) *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("Membership already exists!")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("You have an active membership plan, Do you want buy another membership?")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)

	pos_action_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	pos_click_action := sushi.GetClickAction()
	pos_deep_link := &sushi.Deeplink{
		URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID), //sending to purchase landing page till dec 2021
		//URL: util.GetBuyingForMeDeeplink(response.ProductId, false),
	}
	pos_click_action.SetDeeplink(pos_deep_link)
	positiveAction := &sushi.Button{
		Type:        "text",
		Text:        "Buy another membership",
		Color:       pos_action_color,
		ClickAction: pos_click_action,
	}

	neg_action_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	neg_click_action := sushi.GetClickAction()
	neg_deep_link := &sushi.Deeplink{
		URL: util.GetHomeDeeplink(),
	}
	neg_click_action.SetDeeplink(neg_deep_link)
	negativeAction := &sushi.Button{
		Type:        "text",
		Text:        "Continue to home",
		Color:       neg_action_color,
		ClickAction: neg_click_action,
	}

	custom_alert := &sushi.CustomAlert{
		Title:          title,
		Message:        message,
		IsBlocking:     true,
		PositiveAction: positiveAction,
		NegativeAction: negativeAction,
		DismissAfterAction: true,
	}

	return custom_alert
}
