package userController

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
)

func GetFileMd5(file multipart.File, md5Str *string) error {
	h := md5.New()
	if _, err := file.Seek(0, 0); err != nil {
		fmt.Println("Get md5 file seek error -- ", err)
		return err
	}
	if _, err := io.Copy(h, file); err != nil {
		fmt.Println("Get md5 file copy error -- ", err)
		return err
	}

	*md5Str = hex.EncodeToString(h.Sum(nil))

	return nil
}
