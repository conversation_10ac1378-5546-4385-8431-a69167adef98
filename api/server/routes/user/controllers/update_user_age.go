package userController

import (
	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModel "bitbucket.org/jogocoin/go_api/api/models/user"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"context"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
)

type UpdateUserAgeTemplate struct {
	ActionList *[]userModel.Actions `json:"action_list,omitempty"`
}

func UpdateUserAgeC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			model.StatusUnauthorized(common.UNEXPECTED_ERROR),
		)
		return
	}

	var json structs.UpdateUserAge
	json = c.MustGet("jsonData").(structs.UpdateUserAge)

	userClient := util.GetUserServiceClient()
	if loggedInUser != json.UserId {
		userRequest := &userPB.UserChildMappingRequest{
			UserId:      loggedInUser,
			ChildUserId: json.UserId,
		}
		userChildResponse, err := userClient.CheckUserIsChildUser(ctx, userRequest)
		if err != nil {
			log.Println("Func:UpdateUserAgeC")
			log.Printf("error in validating parent(%d) and child(%d) mapping", loggedInUser, json.UserId)
			c.AbortWithStatusJSON(
				http.StatusUnauthorized,
				model.StatusUnauthorized(common.UNEXPECTED_ERROR),
			)
			return
		}

		if userChildResponse.UserId != loggedInUser && userChildResponse.ChildUserId != json.UserId {
			log.Println("Func: UpdateUserAgeC")
			log.Printf("Anonymous request:")
			c.AbortWithStatusJSON(
				http.StatusUnauthorized,
				model.StatusUnauthorized(common.UNEXPECTED_ERROR),
			)
			return
		}
	}

	reqData := &userPB.CreateUserAgeRecordReq{
		UserId: json.UserId,
		Age:    json.Age,
	}

	_, err := userClient.CreateUserAgeRecord(ctx, reqData)
	if err != nil {
		log.Println(err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			model.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	template := UpdateUserAgeTemplate{}
	template.AddActions(ctx, json)
	c.JSON(http.StatusOK, template)
}

func (s *UpdateUserAgeTemplate) AddActions(ctx context.Context, req structs.UpdateUserAge) {
	var action_list []userModel.Actions
	if req.PostAction == "buddy_list" {
		// Age updation in case of multiple membership exist for booking.
		refresh_page := &userModel.RefreshPage{
			Type: "buddies",
		}
		action1 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page,
			},
		}
		action_list = append(action_list, *action1)
	} else if req.PostAction == "slots_get_home" {
		// Age updation in case of single membership exist for booking.
		deeplink := &sushi.Deeplink{
			URL:            util.GetSlotsDeeplink(),
			PostbackParams: req.PostbackParams,
		}
		dismiss_page := &userModel.DismissPage{
			Type:     sushi.ClickActionDeeplink,
			Deeplink: deeplink,
		}
		action1 := &userModel.Actions{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismiss_page,
		}
		action_list = append(action_list, *action1)

		refresh_page := &userModel.RefreshPage{
			Type: "home",
		}
		action2 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page,
			},
		}
		action_list = append(action_list, *action2)
	} else if req.PostAction == "slots_get_facility" {
		// Age updation in case of single membership exist for booking.
		deeplink := &sushi.Deeplink{
			URL:            util.GetSlotsDeeplink(),
			PostbackParams: req.PostbackParams,
		}
		dismiss_page := &userModel.DismissPage{
			Type:     sushi.ClickActionDeeplink,
			Deeplink: deeplink,
		}
		action1 := &userModel.Actions{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismiss_page,
		}
		action_list = append(action_list, *action1)

		refresh_page1 := &userModel.RefreshPage{
			Type: "home",
		}
		refresh_page2 := &userModel.RefreshPage{
			Type: "sport",
		}
		refresh_page3 := &userModel.RefreshPage{
			Type: "facility",
		}
		action2 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page1,
				refresh_page2,
				refresh_page3,
			},
		}
		action_list = append(action_list, *action2)
	} else if req.PostAction == "slots_get_my_bookings" {
		// Age updation in case of single membership exist for booking.
		deeplink := &sushi.Deeplink{
			URL:            util.GetSlotsDeeplink(),
			PostbackParams: req.PostbackParams,
		}
		dismiss_page := &userModel.DismissPage{
			Type:     sushi.ClickActionDeeplink,
			Deeplink: deeplink,
		}
		action1 := &userModel.Actions{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismiss_page,
		}
		action_list = append(action_list, *action1)

		refresh_page1 := &userModel.RefreshPage{
			Type: "home",
		}
		refresh_page2 := &userModel.RefreshPage{
			Type: "my_bookings",
		}
		action2 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page1,
				refresh_page2,
			},
		}
		action_list = append(action_list, *action2)
	} else if req.PostAction == "trial_sports" {
		// Age updation in case of book a trial flow.
		deeplink := &sushi.Deeplink{
			URL: util.GetTrialSportsPageDeeplink(),
		}
		dismiss_page := &userModel.DismissPage{
			Type:     sushi.ClickActionDeeplink,
			Deeplink: deeplink,
		}
		action1 := &userModel.Actions{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismiss_page,
		}
		action_list = append(action_list, *action1)

		refresh_page := &userModel.RefreshPage{
			Type: "home",
		}
		action2 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page,
			},
		}
		action_list = append(action_list, *action2)
	} else if req.PostAction == "trial_slots" {
		// Age updation in case of book a trial flow.
		deeplink := &sushi.Deeplink{
			URL:            util.GetTrialSlotsPageDeeplink(),
			PostbackParams: req.PostbackParams,
		}
		dismiss_page := &userModel.DismissPage{
			Type:     sushi.ClickActionDeeplink,
			Deeplink: deeplink,
		}
		action1 := &userModel.Actions{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismiss_page,
		}
		action_list = append(action_list, *action1)

		refresh_page1 := &userModel.RefreshPage{
			Type: "home",
		}
		refresh_page2 := &userModel.RefreshPage{
			Type: "sport",
		}
		action2 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page1,
				refresh_page2,
			},
		}
		action_list = append(action_list, *action2)
	} else if req.PostAction == "slots_get_sport" {
		// Age updation in case of single membership exist for sports.
		var deeplink_url string
		if featuresupport.SupportsFullPageSlot(ctx) {
			// full page deep link
			deeplink_url = util.GetFullPageSlotsDeeplink()
		} else {
			// bottom sheet deep link
			deeplink_url = util.GetSlotsDeeplink()
		}
		deeplink := &sushi.Deeplink{
			URL:            deeplink_url,
			PostbackParams: req.PostbackParams,
		}
		dismiss_page := &userModel.DismissPage{
			Type:     sushi.ClickActionDeeplink,
			Deeplink: deeplink,
		}
		action1 := &userModel.Actions{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismiss_page,
		}
		action_list = append(action_list, *action1)

		refresh_page1 := &userModel.RefreshPage{
			Type: "home",
		}
		refresh_page2 := &userModel.RefreshPage{
			Type: "sport",
		}
		action2 := &userModel.Actions{
			Type: sushi.ActionTypeRefreshPages,
			RefreshPages: []*userModel.RefreshPage{
				refresh_page1,
				refresh_page2,
			},
		}
		action_list = append(action_list, *action2)
	}

	s.ActionList = &action_list
}
