package userController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModels "bitbucket.org/jogocoin/go_api/api/models/user"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	featureSupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type YourMembershipsPageTemplate struct {
	Header            *userModels.Header                   `json:"header,omitempty"`
	HasMore           bool                                 `json:"has_more"`
	Results           []*sushi.CustomTextSnippetTypeLayout `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout      `json:"footer,omitempty"`
	EmptyView         *sushi.EmptyViewType1Layout          `json:"empty_view,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
	NumOfCards        int                                  `json:"-"`
	ProductCategoryId int32                                `json:"-"`
}

func GetUserMembershipsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userService := util.GetUserServiceClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	jsonData := c.MustGet("jsonData").(structs.GetUserMembershipsRequest)
	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You need to login to view this data!"),
		)
		return
	}
	templateResponse := YourMembershipsPageTemplate{
		ProductCategoryId: jsonData.ProductCategoryId,
	}

	response, err := userService.GetYourMembershipsPage(ctx, &userPB.YourMembershipsPageRequest{
		ProductCategoryId: jsonData.ProductCategoryId,
	})

	if err != nil {
		log.Printf("Error in controller GetUserMembershipsC while getting memberhip page details for userId: %d, Error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	templateResponse.SetHeader(response)
	templateResponse.SetHasMore(response)
	if len(response.MembershipCards) == 0 {
		templateResponse.SetEmptyMembershipSection(response)
	} else {
		templateResponse.SetResults(ctx, response)
	}
	if jsonData.ProductCategoryId != common.AcademyCategoryID {
		templateResponse.SetClevertapTracking(ctx)
	}
	templateResponse.SetFooter(ctx, response) // do not shift this function call above as NumOfCards (set in SetResults) is used in it

	c.JSON(
		http.StatusOK,
		templateResponse,
	)
}

func (u *YourMembershipsPageTemplate) SetHeader(pageData *userPB.YourMembershipsPageResponse) {
	title, _ := sushi.NewTextSnippet(pageData.Title)
	u.Header = &userModels.Header{
		Title: title,
	}
}

func (u *YourMembershipsPageTemplate) SetHasMore(pageData *userPB.YourMembershipsPageResponse) {
	u.HasMore = false
}

func (u *YourMembershipsPageTemplate) SetResults(ctx context.Context, pageData *userPB.YourMembershipsPageResponse) {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	for _, membershipCard := range pageData.MembershipCards {
		var isLoggedInUserCard = false
		if membershipCard.UserId == loggedInUserId {
			isLoggedInUserCard = true
		}
		switch membershipCard.ProductCategoryId {
		case common.AcademyCategoryID:
			layoutConfig, _ := sushi.NewLayout(sushi.FitsoImageTextSnippetType16, sushi.LayoutTypeGrid, 1)
			var items []*sushi.FitsoImageTextSnippetType16SnippetItem
			templateCard := GetAcademyTemplatizedCard(ctx, membershipCard, isLoggedInUserCard)
			items = append(items, templateCard)
			fitsoImageTextSnippetType16 := &sushi.FitsoImageTextSnippetType16Snippet{
				Items: items,
			}
			result := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:                layoutConfig,
				FitsoImageTextSnippetType16: fitsoImageTextSnippetType16,
			}
			u.Results = append(u.Results, result)
		case common.SummerCampCategoryID:
			layoutConfig, _ := sushi.NewLayout(sushi.FitsoImageTextSnippetType16, sushi.LayoutTypeGrid, 1)
			var items []*sushi.FitsoImageTextSnippetType16SnippetItem
			templateCard := GetSummerCampTemplatizedCard(ctx, membershipCard, isLoggedInUserCard)
			items = append(items, templateCard)
			fitsoImageTextSnippetType16 := &sushi.FitsoImageTextSnippetType16Snippet{
				Items: items,
			}
			result := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:                layoutConfig,
				FitsoImageTextSnippetType16: fitsoImageTextSnippetType16,
			}
			u.Results = append(u.Results, result)
		default:
			layoutConfig, _ := sushi.NewLayout(sushi.FitsoImageTextSnippetType5, sushi.LayoutTypeGrid, 1)
			var items []*sushi.FitsoImageTextSnippetType5SnippetItem
			templateCard := GetTemplatizedCard(ctx, membershipCard, isLoggedInUserCard)
			items = append(items, templateCard)
			fitsoImageTextSnippetType5 := &sushi.FitsoImageTextSnippetType5Snippet{
				Items: items,
			}
			result := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:        layoutConfig,
				FitsoImageTextType5: fitsoImageTextSnippetType5,
			}
			u.Results = append(u.Results, result)
		}
	}

	u.NumOfCards = len(u.Results)
	if u.ProductCategoryId != common.AcademyCategoryID && u.ProductCategoryId != common.SummerCampCategoryID {
		for index, result := range u.Results {
			templatizedCard := result.FitsoImageTextType5.Items[0]
			tapPayload := make(map[string]interface{})
			tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
			tapPayload["source"] = "your_memberships"
			tapPayload["num_cards_present"] = u.NumOfCards
			tapPayload["member"] = pageData.MembershipCards[index].UserId
			tapEname := &sushi.EnameData{
				Ename: "membership_card_tap",
			}
			membershipCardEvents := sushi.NewClevertapEvents()
			membershipCardEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, membershipCardEvents)
			templatizedCard.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

			if templatizedCard.BottomButton != nil {
				tapEname := &sushi.EnameData{
					Ename: "membership_card_renew_tap",
				}
				cardButtonEvents := sushi.NewClevertapEvents()
				cardButtonEvents.SetTap(tapEname)
				cardTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, cardButtonEvents)
				templatizedCard.BottomButton.AddClevertapTrackingItem(cardTrackItem)
			}
		}
	}
}

func GetTemplatizedCard(ctx context.Context, membershipCard *userPB.MembershipCard, isLoggedInUserCard bool) *sushi.FitsoImageTextSnippetType5SnippetItem {
	profilePicture, _ := sushi.NewImage(membershipCard.ProfilePicture)
	profilePicture.SetType(sushi.ImageTypeCircle)
	profilePicture.SetAspectRatio(1)
	profilePicture.SetHeight(60)
	profilePicture.SetWidth(60)

	titleText := membershipCard.Name
	isMarkdown := int32(0)
	if isLoggedInUserCard {
		titleText = fmt.Sprintf("%s <%s-%s|(You)>", titleText, sushi.FontSemiBold, sushi.FontSize300)
		isMarkdown = 1
	}
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title, _ := sushi.NewTextSnippet(titleText)
	title.SetIsMarkdown(isMarkdown)
	title.SetFont(titleFont)

	var subtitle *sushi.TextSnippet
	if membershipCard.Phone != "" {
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle, _ = sushi.NewTextSnippet(membershipCard.Phone)
		subtitle.SetColor(subtitleColor)
		subtitle.SetFont(subtitleFont)
	}

	subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	var productName string
	if len(membershipCard.ProductName) > 25 {
		productName = membershipCard.ProductName[0:25] + "..."
	} else {
		productName = membershipCard.ProductName
	}

	var subtitle1 *sushi.TextSnippet
	if len(productName) > 0 {
		subtitle1, _ = sushi.NewTextSnippet(productName)
		subtitle1.SetColor(subtitle1Color)
		subtitle1.SetFont(subtitle1Font)
	}

	var subtitle2 *sushi.TextSnippet
	if membershipCard.NumSessions != 0 {
		subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		sessionText := "session"
		if membershipCard.NumSessions > 1 {
			sessionText = "sessions"
		}
		subtitle2, _ = sushi.NewTextSnippet(fmt.Sprintf("%d %s booked", membershipCard.NumSessions, sessionText))
		subtitle2.SetColor(subtitle2Color)
		subtitle2.SetFont(subtitle2Font)
	}

	var tag *sushi.Tag
	if membershipCard.ProductLocation != "" {
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		tagTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		tag, _ = sushi.NewTag(membershipCard.ProductLocation)
		tag.Title.SetColor(tagTextColor)
		tag.SetBgColor(tagBgColor)
	}

	subtitle3 := GetMembershipStatusMarkdown(ctx, membershipCard.MembershipStatus, membershipCard.DisplayDate)

	var bottomButton *sushi.Button
	if membershipCard.RenewalButtonDeeplink != "" {
		bottomButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		bottomButtonDeeplink := &sushi.Deeplink{
			URL: membershipCard.RenewalButtonDeeplink,
		}
		bottomButtonClickAction.SetDeeplink(bottomButtonDeeplink)
		bottomButton, _ = sushi.NewButton(sushi.ButtonTypeSolid)
		bottomButton.SetText("Renew now")
		bottomButton.SetSize(sushi.ButtonSizeSmall)
		bottomButton.SetClickAction(bottomButtonClickAction)
	}

	cardClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	cardDeeplink := &sushi.Deeplink{
		URL: util.GetMembershipDetailsDeeplink(membershipCard.ProductId, membershipCard.UserId),
	}
	cardClickAction.SetDeeplink(cardDeeplink)

	return &sushi.FitsoImageTextSnippetType5SnippetItem{
		Image:        profilePicture,
		Title:        title,
		Subtitle:     subtitle,
		Subtitle1:    subtitle1,
		Tag:          tag,
		Subtitle2:    subtitle2,
		Subtitle3:    subtitle3,
		BottomButton: bottomButton,
		ClickAction:  cardClickAction,
	}
}

func GetMembershipStatusMarkdown(ctx context.Context, membershipStatus userPB.MembershipCard_MembershipStatus, displayDate string) *sushi.TextSnippet {
	membershipStatusText := ""
	isMarkdown := int32(1)
	membershipStatusColorType := sushi.ColorTypeGrey
	membershipStatusColorTint := sushi.ColorTint900
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	switch membershipStatus {
	case userPB.MembershipCard_EXPIRED:
		membershipStatusText = fmt.Sprintf("{%s-%s|Expired}", sushi.ColorTypeRed, sushi.ColorTint600)
	case userPB.MembershipCard_EXPIRING_SOON:
		colorType := sushi.ColorTypeOrange
		if featuresupport.SupportsNewColor(ctx) {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		membershipStatusText = fmt.Sprintf("{%s-%s|Expiring soon}", colorType, sushi.ColorTint500)
	case userPB.MembershipCard_EXPIRES:
		membershipStatusText = "Expires"
		isMarkdown = 0
	case userPB.MembershipCard_STARTING:
		membershipStatusText = "Starting"
		colorType := sushi.ColorTypeBlue
		if isNewColorSupported {
			colorType = sushi.NEUTRAL_DARK_THEME
		}
		membershipStatusColorType = colorType
		membershipStatusColorTint = sushi.ColorTint500
		isMarkdown = 0
	case userPB.MembershipCard_CANCELLED:
		membershipStatusText = fmt.Sprintf("{%s-%s|Cancelled}", sushi.ColorTypeRed, sushi.ColorTint600)
	}

	membershipStatusMarkdownColor, _ := sushi.NewColor(membershipStatusColorType, membershipStatusColorTint)
	membershipStatusMarkdownFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	membershipStatusMarkdown, _ := sushi.NewTextSnippet(fmt.Sprintf("%s on %s", membershipStatusText, displayDate))
	membershipStatusMarkdown.SetColor(membershipStatusMarkdownColor)
	membershipStatusMarkdown.SetFont(membershipStatusMarkdownFont)
	membershipStatusMarkdown.SetIsMarkdown(isMarkdown)
	return membershipStatusMarkdown
}

func (u *YourMembershipsPageTemplate) SetFooter(ctx context.Context, pageData *userPB.YourMembershipsPageResponse) {

	footerLayoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	var buttonDataItems []sushi.FooterSnippetType2ButtonItem
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if len(pageData.MembershipCards) != 0 && u.ProductCategoryId != common.AcademyCategoryID && u.ProductCategoryId != common.SummerCampCategoryID {

		purchaseUserClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		purchaseUserClickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
		})
		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["source"] = "your_memberships"
		tapPayload["num_cards_present"] = u.NumOfCards
		tapEname := &sushi.EnameData{
			Ename: "buy_another_membership_tap",
		}
		footerButtonEvents := sushi.NewClevertapEvents()
		footerButtonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, footerButtonEvents)
		purchaseUserButton := sushi.FooterSnippetType2ButtonItem{
			Type:              sushi.FooterButtonTypeSolid,
			Text:              pageData.Cta,
			ClickAction:       purchaseUserClickAction,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		buttonDataItems = append(buttonDataItems, purchaseUserButton)
	} else if u.ProductCategoryId == common.AcademyCategoryID {
		facilityClient := util.GetFacilitySportClient()
		response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
		if err != nil {
			return
		}
		if len(response.AcademySportIds) == 0 {
			return
		}
		if !featureSupport.SupportsAcademyPurchaseFlow(ctx) {
			academyTrialClickAction := homeC.GetTrialBookClickAction(ctx, response.AcademySportIds, 0)
			tapPayload := make(map[string]interface{})
			tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
			tapPayload["source"] = "your_memberships"
			tapPayload["num_cards_present"] = u.NumOfCards
			tapEname := &sushi.EnameData{
				Ename: "academy_book_trial_button_tap",
			}
			footerButtonEvents := sushi.NewClevertapEvents()
			footerButtonEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, footerButtonEvents)
			academyTrialButton := sushi.FooterSnippetType2ButtonItem{
				Type:              sushi.FooterButtonTypeSolid,
				Text:              "Book a free trial class",
				ClickAction:       academyTrialClickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			buttonDataItems = append(buttonDataItems, academyTrialButton)
		} else {
			if len(pageData.MembershipCards) != 0 {
				membershipButton := sushi.FooterSnippetType2ButtonItem{
					Type: sushi.FooterButtonTypeSolid,
					Text: "Buy another membership",
				}
				membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
				membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
					URL: util.GetPurchaseMembershipDeeplink(common.AcademyCategoryID),
				})
				membershipButton.ClickAction = membershipButtonClickAction
				buttonDataItems = append(buttonDataItems, membershipButton)
			} else {
				buttonDataItems = homeC.GetAcademyFooterCTA(ctx, "academy_trial_sport_select", response.AcademySportIds, 0)
			}
		}
	} else if u.ProductCategoryId == common.SummerCampCategoryID {
		facilityClient := util.GetFacilitySportClient()
		response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
		if err != nil {
			return
		}
		if len(response.SummerCampSportIds) == 0 || len(pageData.MembershipCards) == 0 {
			return
		}
		membershipButton := sushi.FooterSnippetType2ButtonItem{
			Type: sushi.FooterButtonTypeSolid,
			Text: "Buy another membership",
		}
		membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetPurchaseMembershipDeeplink(common.SummerCampCategoryID),
		})
		membershipButton.ClickAction = membershipButtonClickAction
		buttonDataItems = append(buttonDataItems, membershipButton)
	} else {

		productClient := util.GetProductClient()
		footerDetailsRequest := &productPB.GetUserSubscriptionStatusRequest{
			UserId:                    loggedInUserId,
			ProductSuggestionRequired: true,
		}
		footerDetailsResponse, err := productClient.GetUserSubscriptionStatus(ctx, footerDetailsRequest)
		if err != nil {
			log.Printf("Api: YourMembership, function: SetFooterSection, Error: %v", err)
			return
		}
		if footerDetailsResponse == nil {
			log.Println("Api: YourMembership, function: SetFooterSection, no response")
			return
		}
		status := footerDetailsResponse.SubscriptionStatus
		if status == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
			return
		}
		var productSubtext string
		if len(footerDetailsResponse.SuggestedProducts) > 0 {
			subscriptionProduct := footerDetailsResponse.SuggestedProducts[0]
			productSubtext = fmt.Sprintf("₹%d for %d %ss", int32(subscriptionProduct.RetailPrice), subscriptionProduct.Duration, subscriptionProduct.DurationUnit)
			if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
				perMonthPrice := int32(subscriptionProduct.RetailPrice)
				if subscriptionProduct.Duration > 0 {
					perMonthPrice = int32(subscriptionProduct.RetailPrice) / subscriptionProduct.Duration
				}
				productSubtext = fmt.Sprintf("starts from ₹%d per month", int32(perMonthPrice))
			}
		}

		clickActionPurchase := sushi.GetClickAction()
		deeplinkPurchase := sushi.Deeplink{
			URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
		}
		clickActionPurchase.SetDeeplink(&deeplinkPurchase)

		switch status {
		case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
			buttonItem1 := sushi.FooterSnippetType2ButtonItem{
				Type:        "solid",
				Text:        "Become a member",
				Subtext:     productSubtext,
				ClickAction: clickActionPurchase,
			}
			buttonDataItems = append(buttonDataItems, buttonItem1)

			clickActionTrialSports := sushi.GetClickAction()
			if loggedInUserId > 0 {
				deeplink := sushi.Deeplink{
					URL: util.GetTrialSportsPageDeeplink(),
				}
				clickActionTrialSports.SetDeeplink(&deeplink)
			} else {
				auth_title, _ := sushi.NewTextSnippet("Please login to book your free trial. Enter your phone number to login using OTP.")
				payload := map[string]interface{}{
					"post_action": "trial_sports",
				}

				postback_params, _ := json.Marshal(payload)
				auth := &sushi.Auth{
					Title:          auth_title,
					PostbackParams: string(postback_params),
					Source:         "user_memberships",
				}
				clickActionTrialSports.SetAuth(auth)
			}

			buttonItem2 := sushi.FooterSnippetType2ButtonItem{
				Type:        "text",
				Text:        "or book a free trial session",
				ClickAction: clickActionTrialSports,
			}
			buttonDataItems = append(buttonDataItems, buttonItem2)
			break

		case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
			fallthrough
		case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
			buttonItem1 := sushi.FooterSnippetType2ButtonItem{
				Type:        "solid",
				Text:        "Become a member",
				Subtext:     productSubtext,
				ClickAction: clickActionPurchase,
			}
			buttonDataItems = append(buttonDataItems, buttonItem1)
			break
		}

	}

	buttonData := &sushi.FooterSnippetType2Button{
		Items: &buttonDataItems,
	}
	if len(pageData.MembershipCards) == 0 {
		buttonData.Orientation = sushi.FooterButtonOrientationVertical
	}

	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	u.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       footerLayoutConfig,
		FooterSnippetType2: footerSnippet,
	}
}

func (u *YourMembershipsPageTemplate) SetEmptyMembershipSection(pageData *userPB.YourMembershipsPageResponse) {

	title, _ := sushi.NewTextSnippet(pageData.EmptyViewInfo.Text)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	title.SetColor(color)
	title.SetFont(font)

	image, _ := sushi.NewImage(util.GetCDNLink(pageData.EmptyViewInfo.Image))
	image.SetHeight(158)
	image.SetWidth(160)
	snippet := &sushi.EmptyViewType1Snippet{
		Title: title,
		Image: image,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}

	u.EmptyView = &sushi.EmptyViewType1Layout{
		LayoutConfig:          layoutConfig,
		EmptyViewType1Snippet: snippet,
	}
}

func (u *YourMembershipsPageTemplate) SetClevertapTracking(ctx context.Context) {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)

	landingEName := &sushi.EnameData{
		Ename: "your_memberships_landing",
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	dismissEName := &sushi.EnameData{
		Ename: "your_memberships_back_button_tap",
	}
	pageDismissEvent := sushi.NewClevertapEvents()
	pageDismissEvent.SetPageDismiss(dismissEName)
	pageDismissTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageDismissEvent)

	u.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem, pageDismissTrackItem}
}

func GetAcademyTemplatizedCard(ctx context.Context, membershipCard *userPB.MembershipCard, isLoggedInUserCard bool) *sushi.FitsoImageTextSnippetType16SnippetItem {
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	profilePicture, _ := sushi.NewImage(membershipCard.ProfilePicture)
	profilePicture.SetType(sushi.ImageTypeCircle)
	profilePicture.SetAspectRatio(1)
	profilePicture.SetHeight(60)
	profilePicture.SetWidth(60)

	titleText := membershipCard.Name
	isMarkdown := int32(0)
	if isLoggedInUserCard {
		titleText = fmt.Sprintf("%s <%s-%s|(You)>", titleText, sushi.FontSemiBold, sushi.FontSize300)
		isMarkdown = 1
	}
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title, _ := sushi.NewTextSnippet(titleText)
	title.SetIsMarkdown(isMarkdown)
	title.SetFont(titleFont)

	subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitle1, _ := sushi.NewTextSnippet(membershipCard.ProductName)
	subtitle1.SetColor(subtitle1Color)
	subtitle1.SetFont(subtitle1Font)

	subtitle2, _ := sushi.NewTextSnippet(membershipCard.CourseLevel)
	subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	colorType := sushi.ColorTypeBlue
	if isNewColorSupported {
		colorType = sushi.NEUTRAL_LIGHT_THEME
	}
	subtitle2Color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
	prefixIconColor, _ := sushi.NewColor(colorType, sushi.ColorTint500)
	prefixIcon, _ := sushi.NewIcon(sushi.CheckIcon, prefixIconColor)
	subtitle2.SetPrefixIcon(prefixIcon)
	subtitle2.SetColor(subtitle2Color)
	subtitle2.SetFont(subtitle2Font)

	subtitle3 := GetMembershipStatusMarkdown(ctx, membershipCard.MembershipStatus, membershipCard.DisplayDate)

	var tag *sushi.Tag
	if membershipCard.CourseName != "" {
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		tagTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		tag, _ = sushi.NewTag(membershipCard.CourseName)
		tag.Title.SetColor(tagTextColor)
		tag.SetBgColor(tagBgColor)
	}

	var bottomButton *sushi.Button
	if membershipCard.RenewalButtonDeeplink != "" {
		bottomButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		bottomButtonDeeplink := &sushi.Deeplink{
			URL: membershipCard.RenewalButtonDeeplink,
		}
		bottomButtonClickAction.SetDeeplink(bottomButtonDeeplink)
		bottomButton, _ = sushi.NewButton(sushi.ButtonTypeSolid)
		bottomButton.SetText("Renew now")
		bottomButton.SetSize(sushi.ButtonSizeSmall)
		bottomButton.SetClickAction(bottomButtonClickAction)
	}

	cardClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	cardDeeplink := &sushi.Deeplink{
		URL: util.GetMembershipDetailsDeeplink(membershipCard.ProductId, membershipCard.UserId),
	}
	cardClickAction.SetDeeplink(cardDeeplink)

	var infoContainer, infoContainer2 *sushi.InfoContainer
	if len(membershipCard.FacilitySlots) <= 2 {
		facilitySlot1 := membershipCard.FacilitySlots[0]

		black500Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		grey900Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

		medium300Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		regular100Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)

		leftTitle, _ := sushi.NewTextSnippet(facilitySlot1.FacilityName)
		leftTitle.SetColor(black500Color)
		leftTitle.SetFont(medium300Font)
		rightTitle, _ := sushi.NewTextSnippet(facilitySlot1.SlotTiming)
		rightTitle.SetColor(black500Color)
		leftTitle.SetFont(medium300Font)
		leftSubtitle, _ := sushi.NewTextSnippet(facilitySlot1.ShortAddress)
		leftSubtitle.SetColor(grey900Color)
		leftSubtitle.SetFont(regular100Font)
		rightSubtitle, _ := sushi.NewTextSnippet(facilitySlot1.DaysOfWeek)
		rightSubtitle.SetColor(grey900Color)
		rightSubtitle.SetFont(regular100Font)

		infoContainer = &sushi.InfoContainer{
			LeftTitle:     leftTitle,
			RightTitle:    rightTitle,
			LeftSubtitle:  leftSubtitle,
			RightSubtitle: rightSubtitle,
		}
		if len(membershipCard.FacilitySlots) == 2 {
			grey700Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			medium100Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

			infoContainerTitle, _ := sushi.NewTextSnippet(facilitySlot1.SportName)
			infoContainerTitle.SetFont(medium100Font)
			infoContainerTitle.SetColor(grey700Color)
			infoContainerTitle.SetKerning(3)
			infoContainer.Title = infoContainerTitle

			facilitySlot2 := membershipCard.FacilitySlots[1]
			infoContainerTitle2, _ := sushi.NewTextSnippet(facilitySlot2.SportName)
			infoContainerTitle2.SetFont(medium100Font)
			infoContainerTitle2.SetColor(grey700Color)
			infoContainerTitle2.SetKerning(3)

			leftTitle, _ := sushi.NewTextSnippet(facilitySlot2.FacilityName)
			leftTitle.SetColor(black500Color)
			leftTitle.SetFont(medium300Font)
			rightTitle, _ := sushi.NewTextSnippet(facilitySlot2.SlotTiming)
			rightTitle.SetColor(black500Color)
			leftTitle.SetFont(medium300Font)
			leftSubtitle, _ := sushi.NewTextSnippet(facilitySlot2.ShortAddress)
			leftSubtitle.SetColor(grey900Color)
			leftSubtitle.SetFont(regular100Font)
			rightSubtitle, _ := sushi.NewTextSnippet(facilitySlot2.DaysOfWeek)
			rightSubtitle.SetColor(grey900Color)
			rightSubtitle.SetFont(regular100Font)

			infoContainer = &sushi.InfoContainer{
				Title:         infoContainerTitle2,
				LeftTitle:     leftTitle,
				RightTitle:    rightTitle,
				LeftSubtitle:  leftSubtitle,
				RightSubtitle: rightSubtitle,
			}
		}
	}

	return &sushi.FitsoImageTextSnippetType16SnippetItem{
		Image:          profilePicture,
		Title:          title,
		Subtitle1:      subtitle1,
		Subtitle2:      subtitle2,
		Subtitle3:      subtitle3,
		Tag:            tag,
		BottomButton:   bottomButton,
		ClickAction:    cardClickAction,
		InfoContainer:  infoContainer,
		InfoContainer2: infoContainer2,
	}
}

func GetSummerCampTemplatizedCard(ctx context.Context, membershipCard *userPB.MembershipCard, isLoggedInUserCard bool) *sushi.FitsoImageTextSnippetType16SnippetItem {
	profilePicture, _ := sushi.NewImage(membershipCard.ProfilePicture)
	profilePicture.SetType(sushi.ImageTypeCircle)
	profilePicture.SetAspectRatio(1)
	profilePicture.SetHeight(60)
	profilePicture.SetWidth(60)

	titleText := membershipCard.Name
	isMarkdown := int32(0)
	if isLoggedInUserCard {
		titleText = fmt.Sprintf("%s <%s-%s|(You)>", titleText, sushi.FontSemiBold, sushi.FontSize300)
		isMarkdown = 1
	}
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title, _ := sushi.NewTextSnippet(titleText)
	title.SetIsMarkdown(isMarkdown)
	title.SetFont(titleFont)

	subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitle1, _ := sushi.NewTextSnippet(membershipCard.ProductName)
	subtitle1.SetColor(subtitle1Color)
	subtitle1.SetFont(subtitle1Font)

	subtitle3 := GetMembershipStatusMarkdown(ctx, membershipCard.MembershipStatus, membershipCard.DisplayDate)

	var tag *sushi.Tag
	if membershipCard.ProductLocation != "" {
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		tagTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		tag, _ = sushi.NewTag(membershipCard.ProductLocation)
		tag.Title.SetColor(tagTextColor)
		tag.SetBgColor(tagBgColor)
	}

	var bottomButton *sushi.Button
	if membershipCard.RenewalButtonDeeplink != "" {
		bottomButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		bottomButtonDeeplink := &sushi.Deeplink{
			URL: membershipCard.RenewalButtonDeeplink,
		}
		bottomButtonClickAction.SetDeeplink(bottomButtonDeeplink)
		bottomButton, _ = sushi.NewButton(sushi.ButtonTypeSolid)
		bottomButton.SetText("Renew now")
		bottomButton.SetSize(sushi.ButtonSizeSmall)
		bottomButton.SetClickAction(bottomButtonClickAction)
	}

	cardClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	cardDeeplink := &sushi.Deeplink{
		URL: util.GetMembershipDetailsDeeplink(membershipCard.ProductId, membershipCard.UserId),
	}
	cardClickAction.SetDeeplink(cardDeeplink)

	var infoContainer *sushi.InfoContainer
	if len(membershipCard.FacilitySlots) <= 2 {
		facilitySlot1 := membershipCard.FacilitySlots[0]

		black500Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		grey900Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

		medium300Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		regular100Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)

		leftTitle, _ := sushi.NewTextSnippet(facilitySlot1.SportName + " • " + facilitySlot1.SlotTiming)
		leftTitle.SetColor(black500Color)
		leftTitle.SetFont(medium300Font)
		rightTitle, _ := sushi.NewTextSnippet(facilitySlot1.FacilityName)
		rightTitle.SetColor(black500Color)
		leftTitle.SetFont(medium300Font)
		leftSubtitle, _ := sushi.NewTextSnippet("Sport")
		leftSubtitle.SetColor(grey900Color)
		leftSubtitle.SetFont(regular100Font)
		rightSubtitle, _ := sushi.NewTextSnippet("Center")
		rightSubtitle.SetColor(grey900Color)
		rightSubtitle.SetFont(regular100Font)

		infoContainer = &sushi.InfoContainer{
			LeftTitle:     leftTitle,
			RightTitle:    rightTitle,
			LeftSubtitle:  leftSubtitle,
			RightSubtitle: rightSubtitle,
		}
	}

	return &sushi.FitsoImageTextSnippetType16SnippetItem{
		Image:         profilePicture,
		Title:         title,
		Subtitle1:     subtitle1,
		Subtitle3:     subtitle3,
		Tag:           tag,
		ClickAction:   cardClickAction,
		InfoContainer: infoContainer,
	}
}
