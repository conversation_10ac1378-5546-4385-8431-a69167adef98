package userController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModels "bitbucket.org/jogocoin/go_api/api/models/user"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	//homeC "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type SummercampPurchaseMembersTemplate struct {
	MembersResponse    *userPB.PurchaseUsersListResponse           `json:"-"`
	Title              *sushi.TextSnippet                          `json:"title,omitempty"`
	UsersList          []*userModels.AcademyPurchaseMember         `json:"users_list,omitempty"`
	DefaultSection     *userModels.DefaultSection                  `json:"default_section,omitempty"`
	BottomButtonStates *sushi.BottomButtonStates                   `json:"bottom_button_states,omitempty"`
	ItemsConfig        *userModels.AcademyPurchaseMemberItemConfig `json:"items_config,omitempty"`
	ClevertapTracking  []*sushi.ClevertapItem                      `json:"clever_tap_tracking,omitempty"`
}

func GetSummercampPurchaseMembersC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	loggedInUserId := util.GetUserIDFromContext(ctx)

	if loggedInUserId == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure("You need to login for this action"))
		return
	}

	userClient := util.GetUserServiceClient()
	response, err := userClient.GetSummerCampPurchaseMembers(ctx, &userPB.Empty{})

	if err != nil {
		log.Printf("Error in GetSummercampPurchaseMembersC for user_id: %d, error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.NOT_AUTHORIZED {
		c.JSON(http.StatusUnauthorized, models.StatusFailure(response.Status.Message))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.BAD_REQUEST {
		c.JSON(http.StatusBadRequest, models.StatusFailure(response.Status.Message))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.FAILED {
		c.JSON(http.StatusInternalServerError, models.StatusFailure(response.Status.Message))
		return
	}

	template := &SummercampPurchaseMembersTemplate{
		MembersResponse: response,
	}
	template.SetHeader(ctx)
	template.SetMembers(ctx)
	template.SetDefaultSection(ctx)
	template.SetPageTracking(ctx)
	template.SetBottomButtonStates(ctx)
	template.SetItemsConfig(ctx)
	c.JSON(http.StatusOK, template)
}

func (a *SummercampPurchaseMembersTemplate) SetHeader(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Become a member")
	a.Title = title
}

func (a *SummercampPurchaseMembersTemplate) SetMembers(ctx context.Context) {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	a.UsersList = make([]*userModels.AcademyPurchaseMember, 0)

	for _, member := range a.MembersResponse.Users {
		memberP := &userModels.AcademyPurchaseMember{
			UserId: member.UserId,
		}
		rightButton, _ := sushi.NewButton(sushi.ButtontypeText)
		rightButton.SetText("Add")
		color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		rightButton.SetColor(color)
		tapPayload := make(map[string]interface{})
		tapPayload["p_user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["user_id"] = member.UserId
		tapPayload["source"] = "add_purchase_member_screen"
		tapEname := &sushi.EnameData{
			Ename: "summercamp_add_details_click",
		}
		addButtonEvents := sushi.NewClevertapEvents()
		addButtonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, addButtonEvents)
		rightButton.AddClevertapTrackingItem(trackItem)

		var name, age, noTrialTaken *sushi.TextSnippet
		var noTrialTakenItem, ageItem *sushi.Item

		name, _ = sushi.NewTextSnippet(member.Name)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		name.SetColor(color)

		if member.Age != 0 {
			age, _ = sushi.NewTextSnippet(fmt.Sprintf("%d years", member.Age))
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			age.SetColor(color)
			font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
			age.SetFont(font)
		}

		if member.IsEditable {
			button, _ := sushi.NewButton(sushi.ButtontypeText)
			button.SetText("Edit")
			color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			button.SetColor(color)
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			clickActionDeeplink := &sushi.Deeplink{
				URL: util.GetAcademyEditUserDetailsDeeplink(member.UserId, 0),
			}
			type temp struct {
				PageOpenType string `json:"page_open_type"`
				FlowType     string `json:"flow_type"`
			}
			m := &temp{PageOpenType: "bottom_sheet", FlowType: apiCommon.ACADEMY_PURCHASE_FLOW}
			postbackParams, err := json.Marshal(m)
			if err != nil {
				log.Printf("Error while marshalling page_open_type in SetMembers, Error: %v", err)
			} else {
				clickActionDeeplink.PostbackParams = string(postbackParams)
			}
			clickAction.SetDeeplink(clickActionDeeplink)
			button.SetClickAction(clickAction)
			memberP.BottomButton = button
		}
		if member.ErrorText != "" {
			note, _ := sushi.NewTextSnippet(member.ErrorText)
			color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint600)
			note.SetColor(color)
			memberP.Note = note
		}

		subtitles := make([]*sushi.SubtitleItem, 0)

		if age != nil {
			ageItem = &sushi.Item{
				Text: age,
			}
		}
		if noTrialTaken != nil {
			noTrialTakenItem = &sushi.Item{
				Text: noTrialTaken,
			}
		}
		a.AddSubtitle(ctx, "age", ageItem, &subtitles)
		a.AddSubtitle(ctx, "status", noTrialTakenItem, &subtitles)

		if !member.IsSelectable {
			disableColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			if name != nil {
				name.SetColor(disableColor)
			}
			if age != nil {
				age.SetColor(disableColor)
			}
			rightButton.SetActionDisabled()
			rightButton.SetColor(disableColor)
		} else {
			clickAction := sushi.GetClickAction()
			userData := &sushi.UserData{
				UserId:                    member.UserId,
				CourseCategoryId:          0,
				SummercampProductMappingId: 0,
				ProductCategoryId:         apiCommon.SummerCampCategoryID,
			}
			preferredCourse := &sushi.OpenAcademyPreferredCourse{
				UserData:                  userData,
				IsRecommendedCourseOpened: false,
			}
			clickAction.SetOpenAcademyPreferredCourse(preferredCourse)
			rightButton.SetClickAction(clickAction)
		}

		memberP.Title = name
		memberP.RightButton = rightButton

		isParent := false
		if loggedInUserId == member.UserId {
			isParent = true
		}
		memberP.UserObject = &sushi.UserObject{
			UserId:   member.UserId,
			Name:     member.Name,
			Age:      member.Age,
			IsParent: isParent,
		}
		memberP.Subtitles = subtitles
		a.UsersList = append(a.UsersList, memberP)
	}
}

func (a *SummercampPurchaseMembersTemplate) AddSubtitle(ctx context.Context, itemType string, item *sushi.Item, list *[]*sushi.SubtitleItem) {
	if item == nil || len(itemType) == 0 {
		return
	}
	subtitle := &sushi.SubtitleItem{
		Type: itemType,
		Item: item,
	}
	*list = append(*list, subtitle)
}

func (a *SummercampPurchaseMembersTemplate) SetDefaultSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Add member")
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	title.SetColor(color)

	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("age must be between %d-%d years", apiCommon.SUMMER_CAMP_MIN_AGE, apiCommon.SUMMER_CAMP_MAX_AGE))

	rightIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	rightIcon, _ := sushi.NewIcon(sushi.PlusCircleIcon, rightIconColor)

	editInfoSections := make([]*sushi.EditInfoSection, 0)
	for _, section := range a.MembersResponse.DefaultSection.Sections {
		editInfoSection := GetEditInfoSection(section)
		editInfoSections = append(editInfoSections, editInfoSection)
	}
	defaultSectionClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenAddDetailsPage)
	defaultSectionClickAction.OpenAddDetailsPage = &sushi.OpenAddDetailsPage{
		EditInfo: &sushi.EditInfo{
			Title: &sushi.TextSnippet{
				Text: "Add member",
			},
			Sections:           editInfoSections,
			BottomButtonStates: a.GetBottomButtonStatesForAddMember(),
			EditPageType:       "bottom_sheet",
		},
	}

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["source"] = "add_purchase_member_screen"
	tapEname := &sushi.EnameData{
		Ename: "summercamp_add_member_click",
	}
	addButtonEvents := sushi.NewClevertapEvents()
	addButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, addButtonEvents)

	a.DefaultSection = &userModels.DefaultSection{
		Title:     title,
		Subtitle:  subtitle,
		RightIcon: rightIcon,
		TopSeparator: &sushi.Separator{
			Type: sushi.SeparatorTypeThick,
		},
		ClickAction:       defaultSectionClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
}

func (a *SummercampPurchaseMembersTemplate) GetBottomButtonStatesForAddMember() *sushi.BottomButtonStates {
	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: "Save Details",
				BgColor: &sushi.Color{
					Tint: sushi.ColorTint400,
					Type: sushi.ColorTypeGrey,
				},
				IsActionDisabled: 1,
			},
		},
		Completed: &sushi.BottomButtonState{
			Button: &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: "Save Details",
				BgColor: &sushi.Color{
					Tint: sushi.ColorTint500,
					Type: sushi.ColorTypeRed,
				},
				ClickAction: &sushi.ClickAction{
					Type: sushi.ApiCallMultiAction,
					ApiCallMultiAction: &sushi.APICallAction{
						RequestType: sushi.POSTRequestType,
						URL:         "v1/user/academy/add-member",
					},
				},
			},
		},
	}
	type temp struct {
		FlowType          string `json:"flow_type"`
		ProductCategoryId int32  `json:"product_category_id"`
	}
	postRequestBody := temp{
		FlowType:          apiCommon.ACADEMY_PURCHASE_FLOW,
		ProductCategoryId: apiCommon.SummerCampCategoryID,
	}
	postRequestBodyParams, err := json.Marshal(postRequestBody)
	if err != nil {
		log.Printf("Summer camp: Error marshalling flow_type in GetBottomButtonStatesForAddMember of SetValidResponse, err: %v", err)
	} else {
		bottomButtonStates.Completed.Button.ClickAction.ApiCallMultiAction.Body = string(postRequestBodyParams)
	}
	return bottomButtonStates
}

func (a *SummercampPurchaseMembersTemplate) SetBottomButtonStates(ctx context.Context) {
	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButton.SetText("Select member(s) to proceed")
	disabledBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(disabledBgColor)
	disabledButton.SetActionDisabled()

	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButton.SetText("Proceed with 1 person")
	completedBgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButton.SetBgColor(completedBgColor)
	completedClickAction := sushi.GetClickAction()
	if featuresupport.SupportsFullPageCart(ctx) {
		completedClickAction.Type = sushi.ClickActionOpenCart
	} else {
		completedClickAction.Type = sushi.ClickActionProceedToCart
	}
	completedButton.SetClickAction(completedClickAction)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["source"] = "add_purchase_member_screen"
	tapEname := &sushi.EnameData{
		Ename: "summercamp_proceed_with_selected_members_tap",
	}
	addButtonEvents := sushi.NewClevertapEvents()
	addButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, addButtonEvents)
	completedButton.AddClevertapTrackingItem(trackItem)

	buttonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button:     completedButton,
			PluralText: "Proceed with ${members_count} persons",
		},
	}
	a.BottomButtonStates = buttonStates
}

func (a *SummercampPurchaseMembersTemplate) SetItemsConfig(ctx context.Context) {
	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Edit")
	clickAction := sushi.GetClickAction()
	clickAction.SetOpenAcademyRecommendedCourse(nil)
	button.SetClickAction(clickAction)

	itemsConfig := &userModels.AcademyPurchaseMemberItemConfig{
		BottomButton: button,
	}
	a.ItemsConfig = itemsConfig
}

func (a *SummercampPurchaseMembersTemplate) SetPageTracking(ctx context.Context) {
	landingPayload := make(map[string]interface{})
	landingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	landingPayload["city_id"] = util.GetCityIDFromContext(ctx)
	landingPayload["source"] = "add_purchase_member_screen"
	landingEname := &sushi.EnameData{
		Ename: "summercamp_purchase_member_list_landing",
	}

	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, landingPayload, landingEvents)

	a.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}