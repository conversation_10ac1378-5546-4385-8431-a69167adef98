package userController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModels "bitbucket.org/jogocoin/go_api/api/models/user"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type SummercampTrialUsersTemplate struct {
	Header             *userModels.AcademyTrialHeader       `json:"header,omitempty"`
	ProgressBar        *sushi.ProgressBar                   `json:"progess_bar,omitempty"`
	SelectionTitle     *sushi.TextSnippet                   `json:"selection_title,omitempty"`
	MembersData        *userModels.AcademyMembersData       `json:"members_data,omitempty"`
	BottomButtonStates *sushi.BottomButtonStates            `json:"bottom_button_states,omitempty"`
	Response           *userPB.GetAcademyTrialUsersResponse `json:"-"`
	SportId            int32                                `json:"-"`
	Json               structs.GetAcademyTrialUsersRequest  `json:"-"`
	ClevertapTracking  []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
}

func GetSummercampTrialUsersC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	json := c.MustGet("jsonData").(structs.GetAcademyTrialUsersRequest)
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure("You need to login for this action"))
		return
	}
	trialUsersResponse, err := userClient.GetSummercampTrialUsers(ctx, &userPB.GetAcademyTrialUsersRequest{
		SportId: json.SportId,
	})
	if err != nil {
		log.Printf("Error in GetSummercampTrialUsersC for sport_id: %d, error: %v", json.SportId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	} else if trialUsersResponse.Status != nil && trialUsersResponse.Status.Status == apiCommon.FAILED {
		log.Printf("Error in GetSummercampTrialUsersC for sport_id: %d, error: %v", json.SportId, trialUsersResponse.Status.Message)
		c.AbortWithStatusJSON(http.StatusBadRequest, trialUsersResponse.Status)
		return
	}

	trialUsersTemplate := SummercampTrialUsersTemplate{
		Response: trialUsersResponse,
		SportId:  json.SportId,
		Json:     json,
	}

	trialUsersTemplate.SetHeader(ctx)
	if trialUsersTemplate.Json.BottomSheet == 0 {
		trialUsersTemplate.SetProgressBar(ctx)
		trialUsersTemplate.SetSelectionTitle()
	}
	trialUsersTemplate.SetMembersData(ctx)
	trialUsersTemplate.SetBottomButtonStates(ctx)
	trialUsersTemplate.SetPageTracking(ctx)

	c.JSON(http.StatusOK, trialUsersTemplate)
}

func (a *SummercampTrialUsersTemplate) SetHeader(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Booking for")
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

	var tag *sushi.Tag
	var bgColor *sushi.Color
	if a.Json.BottomSheet == 0 {
		title.Text = "Book free trial class"
		title.SetFont(titleFont)
		title.SetColor(titleColor)
		trialText := "1 free trial available for each sport"

		facilityClient := util.GetFacilitySportClient()
		response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
		if err != nil {
			log.Printf("Error in getting city based sports %v", err)
			return
		}
		if len(response.SummerCampSportIds) == 1 {
			trialText = "One free trial available"
		}
		tag, _ = sushi.NewTag(trialText)
		tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		tagTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		tag.Title.SetFont(tagTitleFont)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
		tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
		if featuresupport.SupportsNewColor(ctx) {
			tagTitleColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
			tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
		}
		tag.Title.SetColor(tagTitleColor)
		tag.SetBgColor(tagBgColor)
		tag.SetBorderColor(tagBorderColor)

		bgColor, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	}

	a.Header = &userModels.AcademyTrialHeader{
		Title:   title,
		Tag:     tag,
		BgColor: bgColor,
	}
}

func (a *SummercampTrialUsersTemplate) SetProgressBar(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	progressBarColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		progressBarColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint500)
	}
	a.ProgressBar = &sushi.ProgressBar{
		Progress: int32(33),
		BgColor:  bgColor,
		ProgressColors: []*sushi.Color{
			progressBarColor,
		},
	}
}

func (a *SummercampTrialUsersTemplate) SetSelectionTitle() {
	selectionTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Book free trial for %s", a.Response.SportName))
	selectionColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	selectionFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	selectionTitle.SetFont(selectionFont)
	selectionTitle.SetColor(selectionColor)
	a.SelectionTitle = selectionTitle
}

func (a *SummercampTrialUsersTemplate) SetMembersData(ctx context.Context) {
	title, _ := sushi.NewTextSnippet(a.Response.DefaultSection.Title)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	title.SetColor(titleColor)

	subtitle, _ := sushi.NewTextSnippet(a.Response.DefaultSection.Subtitle)

	rightIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	rightIcon, _ := sushi.NewIcon(sushi.PlusCircleIcon, rightIconColor)

	clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenAddDetailsPage)
	editInfoSections := make([]*sushi.EditInfoSection, 0)
	for _, section := range a.Response.DefaultSection.Sections {
		editInfoSection := GetEditInfoSection(section)
		editInfoSections = append(editInfoSections, editInfoSection)
	}
	payload := GetClevertapTrackingDefaultPayload(ctx, a.SportId, a.Json.FsId, a.Json.CourseId)

	addMemberEvent := sushi.NewClevertapEvents()
	addMemberEvent.SetPageSuccess(&sushi.EnameData{
		Ename: "add_more_member_page_landing",
	})
	addMemberEvent.SetTap(&sushi.EnameData{
		Ename: "add_more_member_card_click",
	})
	addMemberTrackingItem := sushi.GetClevertapTrackItem(ctx, payload, addMemberEvent)
	clickAction.OpenAddDetailsPage = &sushi.OpenAddDetailsPage{
		EditInfo: &sushi.EditInfo{
			Title: &sushi.TextSnippet{
				Text: "Add member",
			},
			BottomButtonStates: a.GetBottomButtonStatesForAddMember(ctx, a.Response.DefaultSection.BottomButtonStates),
			Sections:           editInfoSections,
			EditPageType:       "bottom_sheet",
		},
		ClevertapTracking: []*sushi.ClevertapItem{addMemberTrackingItem},
	}

	members := make([]*sushi.AcademyMember, 0)

	loggedInUserMember := a.GetSummercampMember(ctx, a.Response.User)
	members = append(members, loggedInUserMember)

	for _, childUser := range a.Response.ChildUsers {
		childMember := a.GetSummercampMember(ctx, childUser)
		members = append(members, childMember)
	}
	if len(a.Json.PreviouslySelectedUserIds) > 0 {
		for _, member := range members {
			if member.DisplayMember.IsSelectable && util.Int32InSlice(member.UserId, a.Json.PreviouslySelectedUserIds) {
				member.DisplayMember.IsSelected = true
			} else {
				member.DisplayMember.IsSelected = false
			}
		}
	}
	addMemberImpressionEvent := sushi.NewClevertapEvents()
	addMemberImpressionEvent.SetImpression(&sushi.EnameData{
		Ename: "add_more_member_card_impression",
	})
	addMemberImpressionTrackingItem := sushi.GetClevertapTrackItem(ctx, payload, addMemberImpressionEvent)
	a.MembersData = &userModels.AcademyMembersData{
		Members: members,
		DefaultSection: &userModels.DefaultSection{
			Title:     title,
			Subtitle:  subtitle,
			RightIcon: rightIcon,
			TopSeparator: &sushi.Separator{
				Type: sushi.SeparatorTypeThick,
			},
			ClickAction:       clickAction,
			ClevertapTracking: []*sushi.ClevertapItem{addMemberImpressionTrackingItem},
		},
	}
}

func (a *SummercampTrialUsersTemplate) GetSummercampMember(ctx context.Context, user *userPB.AcademyTrialUser) *sushi.AcademyMember {
	member := &sushi.AcademyMember{
		UserId: user.UserId,
		DisplayMember: &sushi.DisplayMember{
			Name: &sushi.TextSnippet{
				Text: user.Name,
			},
			DisplayAge: &sushi.TextSnippet{
				Text: fmt.Sprintf("%d yrs", user.Age),
			},
			Phone: &sushi.TextSnippet{
				Text: user.Phone,
			},
			IsSelectable: user.IsSelectable,
			IsSelected:   user.IsSelected,
		},
	}
	if user.IsEditable {
		member.EditButton = a.GetMemberEditButton(ctx, user)
	}
	if user.ErrorText != "" {
		member.DisplayMember.ErrorText = &sushi.TextSnippet{
			Text: user.ErrorText,
			Color: &sushi.Color{
				Type: sushi.ColorTypeRed,
				Tint: sushi.ColorTint600,
			},
		}
	}
	if user.InfoText != "" {
		member.DisplayMember.InfoText = &sushi.TextSnippet{
			Text: user.InfoText,
			Color: &sushi.Color{
				Type: sushi.ColorTypeBlue,
				Tint: sushi.ColorTint500,
			},
		}
		if !user.IsSelectable {
			colorType := sushi.ColorTypeOrange
			if featuresupport.SupportsNewColor(ctx) {
				colorType = sushi.ALERT_LIGHT_THEME
			}
			member.DisplayMember.InfoText.Color = &sushi.Color{
				Type: colorType,
				Tint: sushi.ColorTint500,
			}
		}
	}
	if !user.IsSelectable {
		member.DisplayMember.BgColor = &sushi.Color{
			Type: sushi.ColorTypeGrey,
			Tint: sushi.ColorTint50,
		}
		member.DisplayMember.Name.Color = &sushi.Color{
			Type: sushi.ColorTypeGrey,
			Tint: sushi.ColorTint500,
		}
		member.DisplayMember.DisplayAge.Color = &sushi.Color{
			Type: sushi.ColorTypeGrey,
			Tint: sushi.ColorTint500,
		}
		member.DisplayMember.Phone.Color = &sushi.Color{
			Type: sushi.ColorTypeGrey,
			Tint: sushi.ColorTint500,
		}
	}
	return member
}

func (a *SummercampTrialUsersTemplate) GetMemberEditButton(ctx context.Context, user *userPB.AcademyTrialUser) *sushi.EditButton {
	clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	clickActionDeeplink := &sushi.Deeplink{
		URL: util.GetAcademyEditUserDetailsDeeplink(user.UserId, a.Json.SportId),
	}
	type temp struct {
		PageOpenType string `json:"page_open_type"`
		ProductCategoryId int32 `json:"product_category_id"`
	}
	m := &temp{PageOpenType: "bottom_sheet", ProductCategoryId:	13}
	postbackParams, err := json.Marshal(m)
	if err != nil {
		log.Printf("Error while marshalling page_open_type in GetMemberEditButton, Error: %v", err)
	} else {
		clickActionDeeplink.PostbackParams = string(postbackParams)
	}
	clickAction.SetDeeplink(clickActionDeeplink)

	payload := GetClevertapTrackingDefaultPayload(ctx, a.SportId, a.Json.FsId, a.Json.CourseId)
	editButtonEvent := sushi.NewClevertapEvents()
	editButtonEvent.SetTap(&sushi.EnameData{
		Ename: "summercamp_edit_button_clicked",
	})
	editButtonTrackItem := sushi.GetClevertapTrackItem(ctx, payload, editButtonEvent)
	return &sushi.EditButton{
		Text:              "Edit",
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{editButtonTrackItem},
	}
}

func (a *SummercampTrialUsersTemplate) SetBottomButtonStates(ctx context.Context) {
	bottomButtonStates := a.Response.BottomButtonStates
	a.BottomButtonStates = &sushi.BottomButtonStates{}
	if bottomButtonStates.DisabledText != "" {
		a.BottomButtonStates.Disabled = &sushi.BottomButtonState{
			Button: &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: bottomButtonStates.DisabledText,
				BgColor: &sushi.Color{
					Tint: sushi.ColorTint400,
					Type: sushi.ColorTypeGrey,
				},
				IsActionDisabled: 1,
			},
		}
	}

	if bottomButtonStates.CompletedText != "" {
		payload := GetClevertapTrackingDefaultPayload(ctx, a.SportId, a.Json.FsId, a.Json.CourseId)
		proceedButtonEvent := sushi.NewClevertapEvents()
		proceedButtonEvent.SetTap(&sushi.EnameData{
			Ename: "academy_trial_proceed_to_slots_tap",
		})
		proceedButtonTrackItem := sushi.GetClevertapTrackItem(ctx, payload, proceedButtonEvent)
		a.BottomButtonStates.Completed = &sushi.BottomButtonState{
			Button: &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: bottomButtonStates.CompletedText,
				BgColor: &sushi.Color{
					Tint: sushi.ColorTint500,
					Type: sushi.ColorTypeRed,
				},
				ClickAction: &sushi.ClickAction{
					Type: sushi.ClickActionDeeplink,
					Deeplink: &sushi.Deeplink{
						URL: util.GetTrialCentersPageDeeplink(),
					},
				},
				ClevertapTracking: []*sushi.ClevertapItem{proceedButtonTrackItem},
			},
		}
		if a.Json.BottomSheet == 1 {
			a.BottomButtonStates.Completed.Button.ClickAction.Deeplink.URL = util.GetSlotsDeeplink()
		}
		if bottomButtonStates.PluralCompletedText != "" {
			a.BottomButtonStates.Completed.PluralText = bottomButtonStates.PluralCompletedText
		}
		type temp struct {
			SportId           int32 `json:"sport_id"`
			FsId              int32 `json:"fs_id,omitempty"`
			BottomSheet       int32 `json:"bottom_sheet,omitempty"`
			ProductCategoryId int32 `json:"product_category_id,omitempty"`
		}
		m := temp{SportId: a.SportId, FsId: a.Json.FsId, BottomSheet: a.Json.BottomSheet, ProductCategoryId: apiCommon.SummerCampCategoryID}
		postbackParams, err := json.Marshal(m)
		if err != nil {
			log.Printf("Error marshalling sport_id in SetBottomButtonStates of GetAcademyTrialUsersC, err: %v", err)
		} else {
			a.BottomButtonStates.Completed.Button.ClickAction.Deeplink.PostbackParams = string(postbackParams)
		}
	}
}

func (a *SummercampTrialUsersTemplate) GetBottomButtonStatesForAddMember(ctx context.Context, bottomButtonStatesResponse *userPB.BottomButtonStates) *sushi.BottomButtonStates {
	payload := GetClevertapTrackingDefaultPayload(ctx, a.SportId, a.Json.FsId, a.Json.CourseId)
	saveDetailsEvent := sushi.NewClevertapEvents()
	saveDetailsEvent.SetTap(&sushi.EnameData{
		Ename: "academy_add_member_save_details_tap",
	})
	saveDetailsItem := sushi.GetClevertapTrackItem(ctx, payload, saveDetailsEvent)
	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: bottomButtonStatesResponse.DisabledText,
				BgColor: &sushi.Color{
					Tint: sushi.ColorTint400,
					Type: sushi.ColorTypeGrey,
				},
				IsActionDisabled: 1,
			},
		},
		Completed: &sushi.BottomButtonState{
			Button: &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: bottomButtonStatesResponse.CompletedText,
				BgColor: &sushi.Color{
					Tint: sushi.ColorTint500,
					Type: sushi.ColorTypeRed,
				},
				ClevertapTracking: []*sushi.ClevertapItem{saveDetailsItem},
				ClickAction: &sushi.ClickAction{
					Type: sushi.ApiCallMultiAction,
					ApiCallMultiAction: &sushi.APICallAction{
						RequestType: sushi.POSTRequestType,
						URL:         "v1/user/academy/add-member",
					},
				},
			},
		},
	}
	type temp struct {
		SportId  int32 `json:"sport_id"`
		FsId     int32 `json:"fs_id"`
		CourseId int32 `json:"course_id"`
		ProductCategoryId int32 `json:"product_category_id"`
	}
	postRequestBody := temp{SportId: a.Json.SportId, FsId: a.Json.FsId, CourseId: a.Json.CourseId, ProductCategoryId: 13}
	postRequestBodyParams, err := json.Marshal(postRequestBody)
	if err != nil {
		log.Printf("Error marshalling in GetBottomButtonStatesForAddMember of SetValidResponse, err: %v", err)
	} else {
		bottomButtonStates.Completed.Button.ClickAction.ApiCallMultiAction.Body = string(postRequestBodyParams)
	}
	return bottomButtonStates
}

func (a *SummercampTrialUsersTemplate) SetPageTracking(ctx context.Context) {
	payload := GetClevertapTrackingDefaultPayload(ctx, a.SportId, a.Json.FsId, a.Json.CourseId)
	landingEname := &sushi.EnameData{
		Ename: "summercamp_trial_members_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	a.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}