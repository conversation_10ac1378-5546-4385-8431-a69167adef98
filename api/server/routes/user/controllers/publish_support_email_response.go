package userController

import (
	"context"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModel "bitbucket.org/jogocoin/go_api/api/models/user"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// UserSupportQueryTemplate represents the response structure
type UserSupportQueryTemplate struct {
	Status     string                             `json:"status,omitempty"`
	ActionList []*userModel.UserQuerySubmitAction `json:"action_list,omitempty"`
}

func PublishSupportEmailC(c *gin.Context) {

	userClient := util.GetUserServiceClient()

	ctx := util.PrepareGRPCMetaData(c)

	template := UserSupportQueryTemplate{}

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var json structs.PublishSupportEmailReq
	json = c.MustGet("jsonData").(structs.PublishSupportEmailReq)
	reqData := &userPB.PublishUserQueryRequest{
		Message:    json.Message,
		BookingId:  json.BookingId,
		DeviceInfo: headers.UserAgent,
		Email:      json.Email,
		DeviceId:   headers.DeviceID,
	}

	response, err := userClient.PublishUserQueryAndSupportMail(ctx, reqData)

	if err != nil {
		log.Println("Error in Inserting Record and Sending mail to CX for userid : ", util.GetUserIDFromContext(ctx), " ERROR : ", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		message := response.Status.Message
		c.JSON(http.StatusUnauthorized, template.failedResponse(ctx, message, reqData.Email))
		return
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		message := response.Status.Message
		c.JSON(http.StatusBadRequest, template.failedResponse(ctx, message, reqData.Email))
		return
	}

	c.JSON(http.StatusOK, template.successResponse(ctx, reqData.Email))
	return

}

func PublishUserQueryRequestTemplate() *UserSupportQueryTemplate {
	return &UserSupportQueryTemplate{}
}

func (s *UserSupportQueryTemplate) setTitle(text string) *sushi.TextSnippet {
	title, _ := sushi.NewTextSnippet(text)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	title.SetColor(color)
	title.SetFont(font)
	return title
}

func (s *UserSupportQueryTemplate) setMessage(text string) *sushi.TextSnippet {
	message, _ := sushi.NewTextSnippet(text)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	message.SetFont(font)
	message.SetColor(color)
	return message
}

func (s *UserSupportQueryTemplate) setImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (s *UserSupportQueryTemplate) setStatus(status string) {
	s.Status = status
}

func (s *UserSupportQueryTemplate) successResponse(ctx context.Context, userEmailRequest string) *UserSupportQueryTemplate {
	response := PublishUserQueryRequestTemplate()
	response.setStatus(common.SUCCESS)

	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Query submitted"),
		Message: s.setMessage("We'll get back to you on the mentioned email ID in 24-48 hrs"),
		Image:   s.setImage(common.SUCCESS_IMAGE),
		AutoDismissData: &sushi.AutoDismissData{
			Time:              3,
			DismissActionType: "positive",
		},
		DismissAfterAction: true,
	}

	impressionEname := &sushi.EnameData{
		Ename: "support_ticket_submit_popup",
	}

	impressionPayload := make(map[string]interface{})
	impressionPayload["status"] = common.SUCCESS
	impressionPayload["email_id"] = userEmailRequest
	impressionTrackItem := getClevertapImpressionTrackItem(ctx, impressionEname, impressionPayload)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{impressionTrackItem}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	SupportQueryAction := &userModel.UserQuerySubmitAction{
		Type:                       sushi.ActionTypeUserQuerySuccess,
		PublishSupportEmailSuccess: action,
	}
	response.ActionList = append(response.ActionList, SupportQueryAction)
	return response
}

func (s *UserSupportQueryTemplate) failedResponse(ctx context.Context, errorMessage string, userEmailRequest string) *UserSupportQueryTemplate {
	response := PublishUserQueryRequestTemplate()
	response.setStatus(common.FAILURE)

	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Your query/request cannot be submitted!"),
		Message: s.setMessage(errorMessage),
		Image:   s.setImage(common.FAILURE_IMAGE),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		DismissAfterAction: true,
	}

	impressionEname := &sushi.EnameData{
		Ename: "support_ticket_submit_popup",
	}

	impressionPayload := make(map[string]interface{})
	impressionPayload["status"] = common.FAILURE
	impressionPayload["email_id"] = userEmailRequest
	impressionPayload["message"] = errorMessage
	impressionTrackItem := getClevertapImpressionTrackItem(ctx, impressionEname, impressionPayload)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{impressionTrackItem}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	SupportQueryAction := &userModel.UserQuerySubmitAction{
		Type:                       sushi.ActionTypeUserQuerySuccess,
		PublishSupportEmailSuccess: action,
	}
	response.ActionList = append(response.ActionList, SupportQueryAction)
	return response
}

func getClevertapImpressionTrackItem(ctx context.Context, impressionEname *sushi.EnameData, impressionPayload map[string]interface{}) *sushi.ClevertapItem {

	impressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	SupportSectionEvents := sushi.NewClevertapEvents()
	SupportSectionEvents.SetImpression(impressionEname)
	supportSectionEventsTrackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, SupportSectionEvents)

	return supportSectionEventsTrackItem
}
