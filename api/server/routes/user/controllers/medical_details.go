package userController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModel "bitbucket.org/jogocoin/go_api/api/models/user"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type GetMedicalDetailsTemplate struct {
	SafetyInfoPages []*userModel.MedicalPage   `json:"safety_info_pages,omitempty"`
	PageIndex       int                        `json:"page_index"`
	ApiResponse     *userPB.UserMedicalDetails `json:"-"`
}

type medicalUser struct {
	UserId int32 `uri:"id" binding:"required"`
}

func GetUserMedicalDetailsC(c *gin.Context) {
	var m medicalUser
	if err := c.ShouldBindUri(&m); err != nil || m.UserId == 0 {
		c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
		return
	}

	ctx := util.PrepareGRPCMetaData(c)

	req := &userPB.GetUserMedicalDetailsRequest{
		UserId: m.UserId,
	}
	userClient := util.GetUserServiceClient()
	response, err := userClient.GetUserMedicalDetails(ctx, req)
	if err != nil {
		log.Printf("Unable to fetch medical details for userId: %d, Error: %v", m.UserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}
	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure(common.NOT_AUTHORIZED))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
		return
	}

	template := &GetMedicalDetailsTemplate{
		ApiResponse:     response,
		SafetyInfoPages: make([]*userModel.MedicalPage, 0),
		PageIndex:       int(-1),
	}
	template.SetPages(ctx)

	if template.PageIndex == -1 {
		template.PageIndex = 0
	}

	c.JSON(http.StatusOK, template)
}

func (m *GetMedicalDetailsTemplate) AddPage(page *userModel.MedicalPage) {
	m.SafetyInfoPages = append(m.SafetyInfoPages, page)
}

func (m *GetMedicalDetailsTemplate) SetPageIndex(index int) {
	if m.PageIndex == -1 {
		m.PageIndex = index
	}
}

func (m *GetMedicalDetailsTemplate) SetPages(ctx context.Context) {
	if len(m.ApiResponse.Categories) == 0 {
		return
	}

	for index, val := range m.ApiResponse.Categories {
		param := userModel.MedicalPageType(val.Param)

		switch param {
		case userModel.EmergencyContactPage:
			page := m.GetEmergencyDetailsPage(ctx, index, val)
			m.AddPage(page)
			break

		case userModel.ConsentFormPage:
			page := m.GetConsentFormPage(ctx, index, val)
			m.AddPage(page)
			break

		case userModel.MedicalDeclarationPage:
			page := m.GetDeclarationPage(ctx, index, val)
			m.AddPage(page)
			break
		}
	}
}

func (m *GetMedicalDetailsTemplate) GetEmergencyDetailsPage(ctx context.Context, index int, category *userPB.MedicalCategory) *userModel.MedicalPage {
	header := m.GetHeaders(ctx, index)
	progressData := m.GetProgress(ctx, index)
	progressUnit := progressData.Progress
	pageData := m.GetEmergencyPageData(ctx, index, category)
	footerButtons := m.GetBottomButtons(ctx)
	return &userModel.MedicalPage{
		Header:             header,
		ProgressBar:        progressData,
		PageProgress:       progressUnit,
		PageData:           pageData,
		BottomButtonStates: footerButtons,
	}
}

func (m *GetMedicalDetailsTemplate) GetEmergencyPageData(ctx context.Context, index int, category *userPB.MedicalCategory) *userModel.MedicalPageData {
	data := &userModel.MedicalEmergencyContact{}

	pageTitle, _ := sushi.NewTextSnippet(category.Title)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	pageTitle.SetFont(titleFont)
	pageTitle.SetColor(titleColor)
	data.PageTitle = pageTitle

	showPage := false

	for _, val := range category.Inputs {
		if val.Value == "" { // incomplete page
			showPage = true
		}

		placeholder, _ := sushi.NewTextSnippet(val.Title)
		textInput := &sushi.InputField{
			Id:          strconv.FormatInt(int64(val.Id), 10),
			Optional:    false,
			Placeholder: placeholder,
			Value:       val.Value,
		}

		if val.Id == common.MEDICAL_FORM_NAME_INPUT_ID {
			emptyTitle, _ := sushi.NewTextSnippet("Name cannot be empty")
			textInput.States = &sushi.InputFieldStates{
				Empty: &sushi.InputFieldStateTitle{
					Title: emptyTitle,
				},
			}
			data.Name = textInput
		} else if val.Id == common.MEDICAL_FORM_CONTACT_NO_INPUT_ID {
			emptyTitle, _ := sushi.NewTextSnippet("Mobile number must be 10 digits")
			textInput.States = &sushi.InputFieldStates{
				CharLength: &sushi.InputFieldStateCharLength{
					Length:     int32(10),
					ErrorTitle: emptyTitle,
				},
			}
			data.Mobile = textInput
		}
	}

	if showPage {
		m.SetPageIndex(index)
	}

	return &userModel.MedicalPageData{
		Type:             userModel.EmergencyContactPage,
		EmergencyContact: data,
	}
}

func (m *GetMedicalDetailsTemplate) GetConsentFormPage(ctx context.Context, index int, category *userPB.MedicalCategory) *userModel.MedicalPage {
	header := m.GetHeaders(ctx, index)
	progressData := m.GetProgress(ctx, index)
	progressUnit := progressData.Progress
	pageData := m.GetConsentFormPageData(ctx, index, category)
	footerButtons := m.GetBottomButtons(ctx)
	return &userModel.MedicalPage{
		Header:             header,
		ProgressBar:        progressData,
		PageProgress:       progressUnit,
		PageData:           pageData,
		BottomButtonStates: footerButtons,
	}
}

func (m *GetMedicalDetailsTemplate) GetConsentFormPageData(ctx context.Context, index int, category *userPB.MedicalCategory) *userModel.MedicalPageData {
	data := &userModel.MedicalConsentForm{}

	pageTitle, _ := sushi.NewTextSnippet(category.Title)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	pageTitle.SetFont(titleFont)
	pageTitle.SetColor(titleColor)
	data.PageTitle = pageTitle

	pageSubtitle, _ := sushi.NewTextSnippet("Check all that is applicable to you")
	subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	pageSubtitle.SetFont(subtitleFont)
	pageSubtitle.SetColor(titleColor)
	data.PageSubtitle = pageSubtitle

	showPage := false

	options := make([]*userModel.CheckboxField, 0)
	for _, val := range category.Inputs {
		checkboxTitle, _ := sushi.NewTextSnippet(val.Title)
		checkboxColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		checkboxFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		checkboxTitle.SetColor(checkboxColor)
		checkboxTitle.SetFont(checkboxFont)

		isSelected := true
		if val.Value == "" || val.Value == "0" {
			isSelected = false
		}
		if val.Value == "" {
			showPage = true
		}

		option := &userModel.CheckboxField{
			Id:         strconv.FormatInt(int64(val.Id), 10),
			IsSelected: isSelected,
			Title:      checkboxTitle,
		}
		options = append(options, option)
	}
	data.Options = options

	declaration, _ := sushi.NewTextSnippet(`Information contained in this section is necessary to ensure that the participant’s 
		medical conditions are properly managed however, no swimmer with special needs will be 
		excluded from swimming unless on medical advice.newlnewlI hereby declare that, to the best 
		of my knowledge and belief, the particulars given above and the declaration made therein 
		are true. I agree to intimate the company if there is any change in the information given 
		above newlnewl I release cult and the coaches  from any liability for any injury or illness 
		that my child or I may suffer while undertaking sports training or subsequently occurring 
		in connection with sports training.`)

	declaration.Text = strings.Replace(declaration.Text, "\n", "", -1)
	declaration.Text = strings.Replace(declaration.Text, "\t", "", -1)
	declaration.Text = strings.Replace(declaration.Text, "newl", "\n", -1)

	declarationColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	declarationFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	declaration.SetFont(declarationFont)
	declaration.SetColor(declarationColor)
	data.Declaration = declaration

	if showPage {
		m.SetPageIndex(index)
	}

	return &userModel.MedicalPageData{
		Type:        userModel.ConsentFormPage,
		ConsentForm: data,
	}
}

func (m *GetMedicalDetailsTemplate) GetDeclarationPage(ctx context.Context, index int, category *userPB.MedicalCategory) *userModel.MedicalPage {
	header := m.GetHeaders(ctx, index)
	progressData := m.GetProgress(ctx, index)
	progressUnit := progressData.Progress
	pageData := m.GetDeclarationPageData(ctx, index, category)
	footerButtons := m.GetBottomButtons(ctx)
	return &userModel.MedicalPage{
		Header:             header,
		ProgressBar:        progressData,
		PageProgress:       progressUnit,
		PageData:           pageData,
		BottomButtonStates: footerButtons,
	}
}

func (m *GetMedicalDetailsTemplate) GetDeclarationPageData(ctx context.Context, index int, category *userPB.MedicalCategory) *userModel.MedicalPageData {
	data := &userModel.MedicalDeclaration{}

	showPage := false

	if len(category.Inputs) > 0 {
		submitInput := category.Inputs[0]
		data.Id = strconv.FormatInt(int64(submitInput.Id), 10)
		if submitInput.Value == "" {
			showPage = true
		}
	}

	pageTitle, _ := sushi.NewTextSnippet(category.Title)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	pageTitle.SetFont(titleFont)
	data.PageTitle = pageTitle

	title, _ := sushi.NewTextSnippet(`In consideration of, and as a condition of cult permitting me to use the 
		Facilities, and the equipment contained within the Premises, I hereby acknowledge that: newlnewl
		1. There are significant risks associated with the use of the Facilities and the equipment contained within. 
		Those risks include, but are not limited to, personal injury arising from: 
		newl  a. Use and misuse of the equipment 
		newl  b. Equipment failure 
		newl  c. The acts, omissions or negligence of other Facilities users 
		newl  d. Genuine accidents newl
		2. At all times whilst I am using the Facilities I shall assume sole responsibility for all the 
		risks associated with my property and my person. I will not hold Cult, its management and their staff 
		members liable for any personal loss, damage. newl
		3. The Management shall not be held responsible for loss/theft of any valuables. The Management shall not 
		be held responsible for any losses (mobiles, valuables etc). Please avoid bringing valuables to the Facility. newl
		4. I understand that parking inside the premises can't be allowed. Cult shall not be held responsible 
		for any loss or damages to my vehicles parked inside or outside the Premises. Parking will be done at my own risk. newlnewl
		I agree that I will not allow any other person (an “Uninvited Guest”) to use my access card or access the Facilities 
		on behalf of my absence nor will I allow any uninvited guest to accompany me to the Facilities if they do 
		not have the express permission by Cult to do so. If I do bring an Uninvited Guest to the Facilities, I 
		agree to indemnify Cult for any liability or damages that may arise in relation to the Uninvited Guest. I 
		also undertake not to sue Cult Management or their employees or agents for any loss or damages relating to 
		personal injury, loss or damage to property, arising from the use of the Facilities or the Facilities equipment. 
		This document shall be effective and binding upon my heirs, next of kin, executors, administrators and assigns, 
		in the event of my death or incapacity. newlnewl
		I HAVE READ AND UNDERSTOOD THIS AGREEMENT, AND I AM AWARE THAT BY DECLARING THIS AGREEMENT, I AM WAIVING CERTAIN 
		LEGAL RIGHTS WHICH I OR MY HEIRS, NEXT OF KIN, EXECUTORS, ADMINISTRATORS AND ASSIGNS MAY HAVE AGAINST THE RELEASES. 
`)

	title.Text = strings.Replace(title.Text, "\n", "", -1)
	title.Text = strings.Replace(title.Text, "\t", "", -1)
	title.Text = strings.Replace(title.Text, "newl", "\n", -1)
	titleFont, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	title.SetFont(titleFont)
	title.SetColor(titleColor)
	data.Title = title

	scrollTitle, _ := sushi.NewTextSnippet("Scroll down to accept")
	scrollIcon, _ := sushi.NewIcon(sushi.ArrowDownIcon, nil)
	floatingAction := &userModel.FloatingAction{
		Title: scrollTitle,
		Icon:  scrollIcon,
	}
	data.FloatingAction = floatingAction

	if showPage {
		m.SetPageIndex(index)
	}

	return &userModel.MedicalPageData{
		Type:        userModel.MedicalDeclarationPage,
		Declaration: data,
	}
}

func (m *GetMedicalDetailsTemplate) GetHeaders(ctx context.Context, index int) *userModel.MedicalPageHeader {
	title, _ := sushi.NewTextSnippet("Safety Information")
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	totalPages := len(m.ApiResponse.Categories)
	currentPage := index + 1
	tagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Step %d of %d", currentPage, totalPages))
	titleColor, _ = sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	titleFont, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
	tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
	if featuresupport.SupportsNewColor(ctx) {
		titleColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint500)
		tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
		tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
	}

	tagTitle.SetColor(titleColor)
	tagTitle.SetFont(titleFont)
	tag := &sushi.Tag{
		Title:       tagTitle,
		BgColor:     tagBgColor,
		BorderColor: tagBorderColor,
	}
	headerBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)

	return &userModel.MedicalPageHeader{
		Title:   title,
		Tag:     tag,
		BgColor: headerBgColor,
	}
}

func (m *GetMedicalDetailsTemplate) GetProgress(ctx context.Context, index int) *sushi.ProgressBar {
	totalPages := float64(len(m.ApiResponse.Categories))
	currentPage := float64(index + 1)
	percent := math.Floor((currentPage / totalPages) * 100)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	progressColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		progressColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint500)
	}

	return &sushi.ProgressBar{
		Progress:       int32(percent),
		ProgressColors: []*sushi.Color{progressColor},
		BgColor:        bgColor,
	}
}

func (m *GetMedicalDetailsTemplate) GetBottomButtons(ctx context.Context) *sushi.BottomButtonStates {
	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButton.SetText("Proceed")
	disabledButtonColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(disabledButtonColor)
	disabledButton.SetActionDisabled()
	disabledButtonWrap := &sushi.BottomButtonState{
		Button: disabledButton,
	}

	enabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	enabledButton.SetText("Proceed")
	enabledButtonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	enabledButton.SetBgColor(enabledButtonColor)
	enabledButtonWrap := &sushi.BottomButtonState{
		Button: enabledButton,
	}

	return &sushi.BottomButtonStates{
		Disabled: disabledButtonWrap,
		Enabled:  enabledButtonWrap,
	}
}

type MedicalUserListTemplate struct {
	Header        *userModel.MedicalPageHeader    `json:"header,omitempty"`
	Footer        *sushi.FooterSnippetType2Layout `json:"footer,omitempty"`
	UsersList     []*userModel.MedicalUser        `json:"users_list,omitempty"`
	UserIds       []int32                         `json:"-"`
	UsersData     map[int32]*userPB.User          `json:"-"`
	MedicalStatus map[int32]int32                 `json:"-"`
}

func GetUserListForMedicalDetailsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.GetUserListForMedicalDetails
	json = c.MustGet("jsonData").(structs.GetUserListForMedicalDetails)
	var userIds []int32

	if json.PurchaseId > 0 {
		purchaseReq := &purchasePB.PurchaseRequest{
			PurchaseId: json.PurchaseId,
		}
		purchaseClient := util.GetPurchaseClient()
		purchaseRes, err := purchaseClient.GetUserIdsByPurchaseId(ctx, purchaseReq)
		if err != nil {
			log.Printf("Unable to get users for purchaseId: %d, Error: %v", json.PurchaseId, err)
			c.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailure(common.UNEXPECTED_ERROR),
			)
			return
		}
		if len(purchaseRes.UserIds) == 0 {
			c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
			return
		}
		userIds = purchaseRes.UserIds

	} else if len(json.UserIds) > 0 {

		userIdsStrArr := strings.Split(json.UserIds, ",")
		userIDs := make([]int32, 0)
		for _, val := range userIdsStrArr {
			userIDInt, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				log.Println("Unable to convert user ID: %s to int32 value,Error: %v", val, err)
				continue
			}
			userIDs = append(userIDs, int32(userIDInt))
		}
		if len(userIDs) == 0 {
			c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
			return
		}
		userIds = userIDs
	} else {
		c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
		return
	}
	userClient := util.GetUserServiceClient()

	userReq := &userPB.UserRequest{
		UserIdArray: userIds,
	}
	userRes, err := userClient.UserGet(ctx, userReq)
	if err != nil {
		log.Printf("Unable to get users details in medical users list, Error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}
	if len(userRes.Users) == 0 {
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	medicalReq := &userPB.GetUsersMedicalDetailStatusRequest{
		UserIds: userIds,
	}
	medicalRes, err := userClient.BatchGetUserMedicalDetailStatus(ctx, medicalReq)
	if err != nil {
		log.Printf("Unable to get users for purchaseId: %d, Error: %v", json.PurchaseId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}
	if len(medicalRes.Status) == 0 {
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}
	userMap := make(map[int32]*userPB.User)
	for _, val := range userRes.Users {
		userMap[val.UserId] = val
	}

	// reorder users list as per design
	hasLoggedInUserInList := false
	loggedInUserId := util.GetUserIDFromContext(ctx)
	orderedUserIds := make([]int32, 0)

	for userId, status := range medicalRes.Status {
		if userId == loggedInUserId {
			hasLoggedInUserInList = true
			continue
		}
		if status == 0 {
			orderedUserIds = append(orderedUserIds, userId)
		} else {
			orderedUserIds = append([]int32{userId}, orderedUserIds...)
		}
	}
	if hasLoggedInUserInList {
		orderedUserIds = append([]int32{loggedInUserId}, orderedUserIds...)
	}

	template := &MedicalUserListTemplate{
		UserIds:       orderedUserIds,
		UsersData:     userMap,
		MedicalStatus: medicalRes.Status,
	}

	template.SetHeader(ctx)
	template.SetFooter(ctx)
	template.SetUsers(ctx, &json)

	c.JSON(http.StatusOK, template)
}

func (l *MedicalUserListTemplate) AddUser(user *userModel.MedicalUser) {
	l.UsersList = append(l.UsersList, user)
}

func (l *MedicalUserListTemplate) SetHeader(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Update Safety Information for")
	l.Header = &userModel.MedicalPageHeader{
		Title: title,
	}
}

func (l *MedicalUserListTemplate) SetFooter(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:             sushi.FooterButtonTypeSolid,
		Text:             "Select member to proceed",
		Font:             font,
		Id:               "bottom_button_id1",
		BgColor:          bgColor,
		IsActionDisabled: int32(1),
	}
	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &([]sushi.FooterSnippetType2ButtonItem{buttonItem}),
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	l.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
}

func (l *MedicalUserListTemplate) SetUsers(ctx context.Context, appRequest *structs.GetUserListForMedicalDetails) {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	markedUserSelected := false
	userIdsStr := make([]string, 0)

	for _, userId := range l.UserIds {
		userIdsStr = append(userIdsStr, strconv.FormatInt(int64(userId), 10))
	}

	for _, userId := range l.UserIds {
		item := &userModel.MedicalUser{}

		userData, ok := l.UsersData[userId]
		if !ok {
			continue
		}
		name := userData.Name
		if userId == loggedInUserId {
			name = userData.Name + " (You)"
		}
		item.Image, _ = sushi.NewImage(userData.ProfilePictureHash)
		item.Title, _ = sushi.NewTextSnippet(name)

		status, ok := l.MedicalStatus[userId]
		if !ok {
			continue
		}
		if status == 1 {
			iconColor, _ := sushi.NewColor(sushi.ColorTypeGreen, sushi.ColorTint500)
			item.RightIcon, _ = sushi.NewIcon(sushi.TickMarkIconV2, iconColor)
			item.IsSelectable = false
		} else {
			item.IsSelectable = true
			if !markedUserSelected {
				item.IsSelected = true
				markedUserSelected = true
			}

			medicalPageClickAction := sushi.GetClickAction()
			postbackParams := map[string]interface{}{
				"page_type": appRequest.PageType,
				"user_id":   userId,
				"user_ids":  strings.Join(userIdsStr, ","),
			}
			serializedParams, _ := json.Marshal(postbackParams)
			deeplink := &sushi.Deeplink{
				URL:            util.GetMedicalDetailsDeeplink(userId),
				PostbackParams: string(serializedParams),
			}
			medicalPageClickAction.SetDeeplink(deeplink)

			bottomButtonClickAction := sushi.GetClickAction()
			bottomButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
			bottomButton.SetText("Proceed with selected member")
			bottomButton.SetClickAction(medicalPageClickAction)
			changeBottomButton := &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id1",
				Button:   bottomButton,
			}
			bottomButtonClickAction.SetChangeBottomButton(changeBottomButton)
			item.ClickAction = bottomButtonClickAction
		}

		l.AddUser(item)
	}
}

func SetMedicalDetailsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var jsonData structs.SetUserMedicalDetails
	jsonData = c.MustGet("jsonData").(structs.SetUserMedicalDetails)

	req := &userPB.MedicalInputsUserMap{
		Details: jsonData.Data,
		UserId:  jsonData.UserId,
	}
	userClient := util.GetUserServiceClient()
	response, err := userClient.SaveUserMedicalDetails(ctx, req)

	if err != nil {
		log.Printf("Unable to set medical details for userId: %d, Error: %v", jsonData.UserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}
	if response != nil && response.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure(common.NOT_AUTHORIZED))
		return
	}
	if response != nil && response.Status == common.BAD_REQUEST {
		c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
		return
	}
	if featuresupport.SupportsEmergencyContactDataValidation(ctx) && *jsonData.PageIndex == 0 && response != nil && response.Status == common.FAILED {
		c.JSON(http.StatusOK, models.StatusFailure(response.Message))
		return
	}

	if *jsonData.PageIndex != 2 {
		c.JSON(http.StatusOK, models.StatusSuccess("Medical info added successfully"))
		return
	}

	submittedUserIds := make([]int32, 0)
	notSubmittedUserIds := make([]int32, 0)
	loggedInUserId := util.GetUserIDFromContext(ctx)

	if jsonData.UserIds == "" {
		submittedUserIds = append(submittedUserIds, jsonData.UserId)
	} else {
		userIdsStrArr := strings.Split(jsonData.UserIds, ",")
		userIds := make([]int32, 0)
		for _, val := range userIdsStrArr {
			x, _ := strconv.ParseInt(val, 10, 64)
			userIds = append(userIds, int32(x))
		}
		medicalReq := &userPB.GetUsersMedicalDetailStatusRequest{
			UserIds: userIds,
		}
		medicalRes, err := userClient.BatchGetUserMedicalDetailStatus(ctx, medicalReq)
		if err != nil {
			log.Printf("Unable to get users for users: %s, Error: %v", jsonData.UserIds, err)
			c.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailure(common.UNEXPECTED_ERROR),
			)
			return
		}
		if len(medicalRes.Status) == 0 {
			c.AbortWithStatusJSON(
				http.StatusInternalServerError,
				models.StatusFailure(common.UNEXPECTED_ERROR),
			)
			return
		}
		for userId, status := range medicalRes.Status {
			if status == 1 {
				submittedUserIds = append(submittedUserIds, userId)
			} else {
				notSubmittedUserIds = append(notSubmittedUserIds, userId)
			}
		}
	}

	var positiveAction *sushi.Button
	var negativeAction *sushi.Button
	var titleText string
	var subtitleText string
	var isBlocking bool
	var autoDismiss *sushi.AutoDismissData
	var refreshPages []string

	animation, _ := sushi.NewAnimation("https://fitso-images.curefit.co/file_assets/success_image.json")
	animation.SetRepeatCount(3)
	image, _ := sushi.NewImageWithAnimation(animation)

	switch {
	case len(notSubmittedUserIds) == 0:
		isBlocking = true
		autoDismiss = &sushi.AutoDismissData{
			DismissActionType: sushi.ActionTypePositive,
			Time:              int(3),
		}
		clickAction := sushi.GetClickAction()

		switch jsonData.PageType {
		case common.PAGE_TYPE_FACILITY:
			if sharedFunc.GetLoggedInUserAge(ctx) >= util.MIN_AGE_FOR_BOOKING {
				deeplink,_ := util.GetBookingFlowDeepLink(ctx, jsonData.FsId, false, jsonData.Source)
				dismissPage := &sushi.DismissPage{
					Type:     sushi.ClickActionDeeplink,
					Deeplink: &deeplink,
				}
				clickAction.Type = sushi.ClickActionDismiss
				clickAction.DismissPage = dismissPage
			} else {
				clickAction.Type = sushi.ClickActionDismiss
			}
			refreshPages = append(refreshPages, common.PAGE_TYPE_FACILITY)
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)

		case common.PAGE_TYPE_SPORT:
			if sharedFunc.GetLoggedInUserAge(ctx) >= util.MIN_AGE_FOR_BOOKING {
				var openBottomSheet int32
				if featuresupport.SupportsFullPageSlot(ctx) {
					openBottomSheet = 0
				} else {
					openBottomSheet = 1
				}

				deeplink,_ := util.GetBookingFlowDeepLinkForSports(ctx, jsonData.SportId, false, jsonData.Source, openBottomSheet)
				dismissPage := &sushi.DismissPage{
					Type:     sushi.ClickActionDeeplink,
					Deeplink: &deeplink,
				}
				clickAction.Type = sushi.ClickActionDismiss
				clickAction.DismissPage = dismissPage
			} else {
				clickAction.Type = sushi.ClickActionDismiss
			}
			refreshPages = append(refreshPages, common.PAGE_TYPE_SPORT)
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)

		case common.PAGE_TYPE_BUDDY_LIST:
			clickAction.Type = sushi.ClickActionDismiss
			refreshPages = append(refreshPages, common.PAGE_TYPE_BUDDY_LIST)
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)

		case common.PAGE_TYPE_AEROBAR:
			deeplink := &sushi.Deeplink{
				URL: util.GetHomeDeeplink(),
			}
			clickAction.SetDeeplink(deeplink)
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)

		case common.PAGE_TYPE_MY_BOOKINGS:
			if sharedFunc.GetLoggedInUserAge(ctx) >= util.MIN_AGE_FOR_BOOKING {
				deeplink,_ := util.GetBookingFlowDeepLink(ctx, jsonData.FsId, false, jsonData.Source)
				dismissPage := &sushi.DismissPage{
					Type:     sushi.ClickActionDeeplink,
					Deeplink: &deeplink,
				}
				clickAction.Type = sushi.ClickActionDismiss
				clickAction.DismissPage = dismissPage
			} else {
				clickAction.Type = sushi.ClickActionDismiss
			}
			refreshPages = append(refreshPages, common.PAGE_TYPE_MY_BOOKINGS)
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)

		case common.PAGE_TYPE_HOME:
			if sharedFunc.GetLoggedInUserAge(ctx) >= util.MIN_AGE_FOR_BOOKING {
				deeplink,_ := util.GetBookingFlowDeepLink(ctx, jsonData.FsId, false, jsonData.Source)
				dismissPage := &sushi.DismissPage{
					Type:     sushi.ClickActionDeeplink,
					Deeplink: &deeplink,
				}
				clickAction.Type = sushi.ClickActionDismiss
				clickAction.DismissPage = dismissPage
			} else {
				clickAction.Type = sushi.ClickActionDismiss
			}
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)

		default:
			deeplink := &sushi.Deeplink{
				URL: util.GetHomeDeeplink(),
			}
			clickAction.SetDeeplink(deeplink)
			refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)
		}
		positiveAction = &sushi.Button{
			ClickAction: clickAction,
		}

		switch {
		case len(submittedUserIds) > 1:
			titleText = "Information submitted!"
			subtitleText = "Book sessions together and enjoy friends and family benefits!"

		case len(submittedUserIds) == 1:
			userId := submittedUserIds[0]
			if userId == loggedInUserId {
				titleText = "Information submitted!"
				subtitleText = "Book your session now and enjoy membership benefits"
			} else {
				titleText = "Information submitted!"
				subtitleText = "Book session now and enjoy membership benefits"
			}
		}

	case len(notSubmittedUserIds) == 1:
		isBlocking = true
		userReq := &userPB.UserRequest{
			UserIdArray: []int32{notSubmittedUserIds[0], jsonData.UserId},
		}
		userRes, err := userClient.UserGet(ctx, userReq)
		if err != nil || len(userRes.Users) == 0 {
			titleText = "Information submitted!"
			subtitleText = "Book session now and enjoy membership benefits"

			clickAction := sushi.GetClickAction()
			deeplink := &sushi.Deeplink{
				URL: util.GetHomeDeeplink(),
			}
			clickAction.SetDeeplink(deeplink)
			positiveAction = &sushi.Button{
				ClickAction: clickAction,
			}
			autoDismiss = &sushi.AutoDismissData{
				DismissActionType: sushi.ActionTypePositive,
				Time:              int(3),
			}
		} else {
			userMap := make(map[int32]string)
			for _, val := range userRes.Users {
				userMap[val.UserId] = val.Name
			}
			titleText = fmt.Sprintf("Information submitted for %s!", userMap[jsonData.UserId])
			positiveActionText := fmt.Sprintf("Fill for %s now!", userMap[notSubmittedUserIds[0]])
			negativeActionText := "Complete later"

			positiveAction, _ = sushi.NewButton(sushi.ButtonTypeSolid)
			positiveAction.SetText(positiveActionText)
			positiveAction.SetSize(sushi.ButtonSizeMedium)
			color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			positiveAction.SetColor(color)
			positiveAction.SetBgColor(bgColor)
			positiveAction.SetBorderColor(bgColor)

			params := map[string]interface{}{
				"page_type": jsonData.PageType,
				"user_id":   notSubmittedUserIds[0],
				"user_ids":  jsonData.UserIds,
			}
			serializedParams, _ := json.Marshal(params)
			deeplink := &sushi.Deeplink{
				URL:            util.GetMedicalDetailsDeeplink(notSubmittedUserIds[0]),
				PostbackParams: string(serializedParams),
			}
			dismissSafetyPage := &sushi.DismissSafetyInfoPages{
				Type:     sushi.ClickActionDeeplink,
				Deeplink: deeplink,
			}
			clickAction := sushi.GetClickAction()
			clickAction.SetDismissSafetyInfoPages(dismissSafetyPage)
			positiveAction.SetClickAction(clickAction)

			negativeAction, _ = sushi.NewButton(sushi.ButtonTypeOutlined)
			negativeAction.SetText(negativeActionText)
			negativeAction.SetSize(sushi.ButtonSizeMedium)
			color, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			negativeAction.SetColor(color)
			deeplink = &sushi.Deeplink{
				URL: util.GetHomeDeeplink(),
			}
			clickAction = sushi.GetClickAction()
			clickAction.SetDeeplink(deeplink)
			negativeAction.SetClickAction(clickAction)
		}

		refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)
		refreshPages = append(refreshPages, "safety_info_list")

	case len(notSubmittedUserIds) > 1:
		isBlocking = true
		userReq := &userPB.UserRequest{
			UserId: jsonData.UserId,
		}
		userRes, err := userClient.UserGet(ctx, userReq)
		if err != nil || len(userRes.Users) == 0 {
			titleText = "Information submitted!"
			subtitleText = "Book session now and enjoy membership benefits"

			clickAction := sushi.GetClickAction()
			deeplink := &sushi.Deeplink{
				URL: util.GetHomeDeeplink(),
			}
			clickAction.SetDeeplink(deeplink)
			positiveAction = &sushi.Button{
				ClickAction: clickAction,
			}
			autoDismiss = &sushi.AutoDismissData{
				DismissActionType: sushi.ActionTypePositive,
				Time:              int(3),
			}
		} else {
			name := userRes.Users[0].Name
			titleText = fmt.Sprintf("Information submitted for %s!", name)
			positiveActionText := "Fill for other members"
			negativeActionText := "Complete later"

			positiveAction, _ = sushi.NewButton(sushi.ButtonTypeSolid)
			positiveAction.SetText(positiveActionText)
			positiveAction.SetSize(sushi.ButtonSizeMedium)
			color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			positiveAction.SetColor(color)
			positiveAction.SetBgColor(bgColor)
			positiveAction.SetBorderColor(bgColor)
			clickAction := &sushi.ClickAction{
				Type: sushi.ClickActionDismiss,
			}
			positiveAction.SetClickAction(clickAction)

			negativeAction, _ = sushi.NewButton(sushi.ButtonTypeOutlined)
			negativeAction.SetText(negativeActionText)
			negativeAction.SetSize(sushi.ButtonSizeMedium)
			color, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			negativeAction.SetColor(color)
			deeplink := &sushi.Deeplink{
				URL: util.GetHomeDeeplink(),
			}
			clickAction = sushi.GetClickAction()
			clickAction.SetDeeplink(deeplink)
			negativeAction.SetClickAction(clickAction)
		}

		refreshPages = append(refreshPages, common.PAGE_TYPE_HOME)
		refreshPages = append(refreshPages, "safety_info_list")
	}

	template := &userModel.MedicalInfoSubmitResponse{
		Status:     "success",
		ActionList: make([]*userModel.Actions, 0),
	}

	alert := &sushi.CustomAlert{}
	if titleText != "" {
		title, _ := sushi.NewTextSnippet(titleText)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
		title.SetColor(titleColor)
		title.SetFont(titleFont)
		alert.Title = title
	}
	if subtitleText != "" {
		subtitle, _ := sushi.NewTextSnippet(subtitleText)
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
		subtitle.SetColor(subtitleColor)
		subtitle.SetFont(subtitleFont)
		alert.Message = subtitle
	}

	alert.IsBlocking = isBlocking
	alert.Image = image

	if autoDismiss != nil {
		alert.AutoDismissData = autoDismiss
	}
	if positiveAction != nil {
		alert.PositiveAction = positiveAction
	}
	if negativeAction != nil {
		alert.NegativeAction = negativeAction
	}

	actionItem1 := &userModel.Actions{
		Type: sushi.ActionTypeSafetyInfoSubmitted,
		SafetyInfoSubmitted: &userModel.SafetyInfoSubmitted{
			Type:        sushi.ActionTypeCustomAlert,
			CustomAlert: alert,
		},
	}
	template.ActionList = append(template.ActionList, actionItem1)

	if len(refreshPages) > 0 {
		pages := make([]*userModel.RefreshPage, 0)
		for _, page := range refreshPages {
			refreshPage := &userModel.RefreshPage{
				Type: page,
			}
			pages = append(pages, refreshPage)
		}
		actionItem2 := &userModel.Actions{
			Type:         sushi.ActionTypeRefreshPages,
			RefreshPages: pages,
		}
		template.ActionList = append(template.ActionList, actionItem2)
	}

	c.JSON(http.StatusOK, template)
}
