// user controller

package userController

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"log"
	"strconv"
	"strings"
	"bytes"
	jsonPackage "encoding/json"
	"regexp"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/server/commonFunc"
	"bitbucket.org/jogocoin/go_api/api/models"
	model "bitbucket.org/jogocoin/go_api/api/models"
	userModel "bitbucket.org/jogocoin/go_api/api/models/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"

	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	micro "github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/client/selector/shard"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"

	//not used in development environment

	"net/http"
	"time"

	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
)

var (
	cl userPB.UserService
)

const (
	bucketName       = "images.getfitso.com"
	bucketFolderName = "profile_picture"
)

func GetC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("You are not authorized"))
		return
	}

	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.GetUsers

	json = c.MustGet("jsonData").(structs.GetUsers)

	userData := &userPB.UserRequest{
		UserId: loggedInUser,
		Phone:  json.Phone,
	}

	uidArray := strings.Split(json.UserIdArray, ",")
	for _, strUId := range uidArray {
		intUId, convErr := strconv.Atoi(strUId)

		if convErr == nil {
			userData.UserIdArray = append(userData.UserIdArray, int32(intUId))
		}
	}
	var UsersGetResponse structs.UsersGet
	response, err := cl.UserGet(context.TODO(), userData, shard.Strategy(json.UserID))
	statusCode := http.StatusOK

	if err != nil {
		fmt.Println(err)
		statusCode = http.StatusInternalServerError
		c.AbortWithStatusJSON(statusCode, models.StatusFailure(common.UNEXPECTED_ERROR))
	}

	if len(response.Users) > 0 {
		UsersGetResponse.Status = *response.Status
		UsersGetResponse.User.UserId = response.Users[0].UserId
		UsersGetResponse.User.Name = response.Users[0].Name
		UsersGetResponse.User.Gender = response.Users[0].Gender
		UsersGetResponse.User.Email = response.Users[0].Email
		UsersGetResponse.User.Phone = response.Users[0].Phone
		UsersGetResponse.User.ProfilePictureHash = response.Users[0].ProfilePictureHash
		UsersGetResponse.User.Birthday = response.Users[0].Birthday
		UsersGetResponse.User.DoesProfilePicExist = response.Users[0].DoesProfilePicExist
	}
	c.JSON(statusCode, UsersGetResponse)
}

func ClearCacheC(c *gin.Context) {
	cl = util.GetUserServiceClient()

	var json structs.Cache

	json = c.MustGet("jsonData").(structs.Cache)

	RedisKey := &userPB.CacheRequest{
		Key: json.Key,
	}

	response, err := cl.ClearCache(context.TODO(), RedisKey)
	if err != nil {
		log.Println("func:ClearCacheC: Error in Clear Redis Cache Controller for Key ? err: ", RedisKey.Key, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("No cache found or Error in getting value for key: "+RedisKey.Key))
		return
	}

	c.JSON(http.StatusOK, response.Status)
}

func checkDashboardUserAuthorised(toolPermissions interface{}, permissions []string, toolIds []int32) bool {
	if maptoolsPermissions, ok := toolPermissions.(map[int32]*userPB.ListOfPermissions);ok  {
		for j:= 0 ; j <len(toolIds); j++ {
			log.Println("checkDashboardUserAuthorised: ", maptoolsPermissions[toolIds[j]])
			if maptoolsPermissions[toolIds[j]] != nil && len(maptoolsPermissions[toolIds[j]].Permissions) > 0{
				for _,permission := range maptoolsPermissions[toolIds[j]].Permissions {
					for i := 0; i <len(permissions); i++ {
						log.Println("checkDashboardUserAuthorised permissions",permission, permissions[i], maptoolsPermissions[toolIds[j]])
						if permissions[i] == permission {
							log.Println("checkDashboardUserAuthorised permissions granted ",permission, permissions[i],maptoolsPermissions[toolIds[j]])
							return true
						}
					}
				}
			}
		}
	}

	return false
}

func checkPasswordValid(req *userPB.DashboardUserDetail) (bool, string) {
	//check password does not contain words
	//check length
	//uppercase and lower case
	//number
	if len(req.Password) < 8 {
		return false, "Password length should be greater than 8 characters"
	}
	hasUpperCase := false
	hasLowerCase := false
	hasNumber := false
	hasSpecialChar := false
	specialChars := "!@#$%^&*()-_=+[]{}|;:'\",.<>?/"
	for _, char := range req.Password {
		if strings.ContainsRune(specialChars, char) {
			hasSpecialChar = true
		} else if 'A' <= char && char <= 'Z' {
			hasUpperCase = true
		} else if 'a' <= char && char <= 'z' {
			hasLowerCase = true
		} else if '0' <= char && char <= '9' {
			hasNumber = true
		}
	}
	if !hasUpperCase || !hasLowerCase || !hasNumber || !hasSpecialChar {
		log.Println("checkPasswordValid: case and char", hasUpperCase, hasLowerCase, hasNumber, hasSpecialChar)
		return false, "Password should have upper case, number and any special character like (@,&,$....)"
	}
	if matched, _ := regexp.MatchString(`(.)\1{2,}`, req.Password); matched {
		log.Println("checkPasswordValid: repeat char", matched)
		//return false
	}
	wordList := []string{"password", "admin", "123", "qwerty", "fitso", "getfitso", "zomato", "curefit","test", "qwerty", "1111", "1q2w3e", "info", "1q2w3d4r5t", "welcome", "default", "000", "321", "666", "pass", "98765", "iloveyou"}
	if passwordContainsWord(req.Password, wordList) {
		log.Println("checkPasswordValid: word exist")
		return false, "Not Strong Password and Password should not have common words"
	}
	return true, "success"
}

func passwordContainsWord(password string, wordList []string) bool {
	for _, word := range wordList {
		if strings.Contains(strings.ToLower(password), word) {
			return true
		}
	}
	return false
}

func GetLatestAcademyAssessmentsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	cl = util.GetUserServiceClient()

	var json structs.GetShortNoticeRequest

	json = c.MustGet("jsonData").(structs.GetShortNoticeRequest)

	request := &userPB.UserRequest{
		UserId: json.UserID,
	}

	response, err := cl.GetLatestAcademyAssessmentBySport(ctx, request)
	if err != nil {
		log.Printf("func:GetLatestAcademyAssessmentsC: Error in getting assessement for ID %d err: ", json.UserID, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("Error in getting assessements"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
}

func ViewCacheC(c *gin.Context) {
	cl = util.GetUserServiceClient()

	var json structs.Cache

	json = c.MustGet("jsonData").(structs.Cache)

	RedisKey := &userPB.CacheRequest{
		Key: json.Key,
	}

	response, err := cl.ViewCache(context.TODO(), RedisKey)
	if err != nil {
		log.Println("func:ViewCacheR: Error in View Redis Cache Controller for key: ? err: ", RedisKey.Key, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("No cache found or Error in getting value for key: "+RedisKey.Key))
		return
	}

	c.JSON(http.StatusOK, response)

}

func AuthenticateC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Login

	json = c.MustGet("jsonData").(structs.Login)

	userID, _ := strconv.Atoi(json.UserID)

	userData := &userPB.LoginRequest{
		UserId:   int32(userID),
		Phone:    json.Phone,
		Email:    json.Email,
		Password: json.Password,
	}

	response, err := cl.Authenticate(context.TODO(), userData, shard.Strategy(json.UserID))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ValidateC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Token

	json = c.MustGet("jsonData").(structs.Token)

	var headers structs.Headers

	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserID) > 0 {
		uid, _ := strconv.Atoi(json.UserID)
		userId = int32(uid)
	} else {
		userId = int32(headers.UserId)
	}

	//in case of child user switch -- access token from json body is userd
	var accessToken string
	if len(json.Token) > 0 {
		accessToken = json.Token
	} else {
		accessToken = headers.AccessToken
	}

	userData := &userPB.UserRequest{
		UserId: userId,
		Token:  accessToken,
	}

	response, err := cl.ValidateToken(context.TODO(), userData, shard.Strategy(json.UserID))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func PhoneLoginWithOTPC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.PhoneLogin

	json = c.MustGet("jsonData").(structs.PhoneLogin)

	phoneLoginData := &userPB.PhoneLoginRequest{
		Phone:   json.Phone,
		Otp:     json.Otp,
		IsdCode: json.IsdCode,
		Task:    json.Task,
	}

	response, err := cl.PhoneLogin(context.TODO(), phoneLoginData, shard.Strategy(json.Phone))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func PhoneLoginWithOTPCV2(c *gin.Context) {

	userClient := util.GetUserServiceClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.PhoneLogin
	json = c.MustGet("jsonData").(structs.PhoneLogin)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	phoneLoginData := &userPB.PhoneLoginRequest{
		Phone:       json.Phone,
		Otp:         json.Otp,
		IsdCode:     json.IsdCode,
		Task:        json.Task,
		ValidateFor: userPB.GenerateOTP_LOGIN_OTP,
		DeviceId:    headers.DeviceID,
		AppsflyerId: headers.AppsflyerId,
	}

	if featuresupport.SupportsNewLogin(c) {
		response, err := userClient.PhoneLoginV2New(ctx, phoneLoginData, shard.Strategy(json.Phone))

		statusCode := http.StatusOK
		if err != nil {
			log.Println("Error in verifying otp for login ", err)
			c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
			return
		}

		if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
			statusCode = http.StatusUnauthorized
		}
		if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
			statusCode = http.StatusBadRequest
		}

		result := userModel.PhoneLoginV2NewRes{
			Status:     response.Status,
			User:       response.User,
			Token:      response.Token,
			UserExists: response.UserExists,
		}

		if response.Status.Status == "success" && len(json.PostAction) > 0 {
			reqData := &userPB.PostLoginReq{
				PostAction: json.PostAction,
			}

			if result.UserExists {
				c.Set("user_id", result.User.UserId)
				reqData.UserId = result.User.UserId
			}

			ctx := util.PrepareGRPCMetaData(c)
			postLoginresponse, err := userClient.PostLoginUserDetails(ctx, reqData)
			if err != nil {
				log.Println("Error in getting user post login details", err)
				c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
				return
			}

			if postLoginresponse.Status.Status == common.SUCCESS {
				result.SuccessAction = PostLoginResponse(ctx, postLoginresponse, json)
			}
		}

		c.JSON(statusCode, result)
	} else {
		response, err := userClient.PhoneLoginV2(ctx, phoneLoginData, shard.Strategy(json.Phone))
		statusCode := http.StatusOK
		if err != nil {
			log.Println("Error in verifying otp for login ", err)
			c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
			return
		}

		if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
			statusCode = http.StatusUnauthorized
		}
		if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, response)
	}
}

func UploadPushInfoC(c *gin.Context) {

	userClient := util.GetUserServiceClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.PushInfo
	json = c.MustGet("jsonData").(structs.PushInfo)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if int32(headers.UserId) > 0 {
		userId = int32(headers.UserId)
	} else {
		userId = util.GetUserIDFromContext(ctx)
	}

	pushInfo := &userPB.PushInfo{
		DeviceToken: json.DeviceToken,
		FcmToken:    json.FCMToken,
		UserId:      userId,
		AppType:     headers.AppType,
		AppVersion:  headers.AppVersion,
	}

	log.Println("PUSH INFO", pushInfo)
	response, err := userClient.UploadPushInfo(ctx, pushInfo, shard.Strategy(json.FCMToken))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GenerateOTPForLoginC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	ctx := util.PrepareGRPCMetaData(c)
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.GenerateOTP

	json = c.MustGet("jsonData").(structs.GenerateOTP)

	userID, _ := strconv.Atoi(json.UserID)

	userData := &userPB.GenerateOTPRequest{
		UserId: int32(userID),
		Phone:  json.Phone,
		Email:  json.Email,
		OtpFor: userPB.GenerateOTP_LOGIN_OTP,
	}

	response, err := cl.GenerateOTPForLogin(ctx, userData, shard.Strategy(json.UserID))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func LogoutC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Logout

	json = c.MustGet("jsonData").(structs.Logout)

	var headers structs.Headers

	headers = c.MustGet("requestHeaders").(structs.Headers)

	userData := &userPB.LogoutRequest{
		Token:    headers.AccessToken,
		FcmToken: json.FCMToken,
	}

	response, err := cl.Logout(context.TODO(), userData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UpdateUserInfoC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	headers := c.MustGet("requestHeaders").(structs.Headers)
	json := c.MustGet("jsonData").(structs.UserInfo)

	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You are not authorized"),
		)
		return
	}

	var tokenVal string
	if len(json.Token) > 0 {
		tokenVal = json.Token
	} else {
		tokenVal = headers.AccessToken
	}

	userData := &userPB.UserInfo{
		UserId:      loggedInUserId,
		Email:       json.Email,
		About:       json.About,
		Gender:      json.Gender,
		DateOfBirth: json.DateOfBirth,
		Token:       tokenVal,
	}

	response, err := userClient.UpdateUserInfo(ctx, userData)

	if err != nil {
		log.Printf("error in updating user details for user %d with error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("something went wrong"),
		)
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)
}

func UploadProfilePictureC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	headers := c.MustGet("requestHeaders").(structs.Headers)
	json := c.MustGet("jsonData").(structs.ProfilePic)

	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You are not authorized"),
		)
		return
	}

	header := json.DisplayPicture
	file, err := header.Open()

	defer file.Close()

	if err != nil {
		log.Printf("error opening uploaded profile picture: %v for user %d", err, loggedInUserId)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusUnauthorized("something went wrong"),
		)
		return
	}

	bytes, err := ioutil.ReadAll(file)
	if err != nil {
		log.Printf("error reading uploaded profile picture: %v for user %d", err, loggedInUserId)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusUnauthorized("something went wrong"),
		)
		return
	}

	var r *http.Request = c.Request

	fdp, hdp, err := r.FormFile("display_picture")

	if err != nil {
		log.Printf("error in getting image from form-data for user %d with error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("Something went wrong"),
		)
		return
	}
	defer fdp.Close()

	mimeType := hdp.Header.Get("Content-Type")

	userData := &userPB.ProfilePic{
		Image:       bytes,
		Token:       headers.AccessToken,
		ContentType: mimeType,
	}
	response, err := userClient.UploadProfilePicture(ctx, userData)

	if err != nil {
		log.Printf("error in saving uploaded file for user %d with error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("something went wrong"),
		)
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)

}

func RemoveProfilePictureC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	headers := c.MustGet("requestHeaders").(structs.Headers)

	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You are not authorized"),
		)
		return
	}

	userData := &userPB.ProfilePic{
		Token: headers.AccessToken,
	}
	response, err := userClient.RemoveProfilePicture(ctx, userData)
	if err != nil {
		log.Printf("error in removing profile picture for user %d with error: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("something went wrong"),
		)
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)
}

func SendBroadcastC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Broadcast
	json = c.MustGet("jsonData").(structs.Broadcast)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.SenderUserId > 0 {
		userId = json.SenderUserId
	} else {
		userId = int32(headers.UserId)
	}

	brodData := &userPB.Broadcast{
		SenderUserId:      userId,
		ReceivingUserIds:  json.ReceivingUserIds,
		FsIds:             json.FsIds,
		EmailSubject:      json.EmailSubject,
		EmailBody:         json.EmailBody,
		SmsBody:           json.SmsBody,
		NotificationTitle: json.NotificationTitle,
		NotificationBody:  json.NotificationBody,
		EmailFlag:         json.EmailFlag,
		SmsFlag:           json.SmsFlag,
		NotificationFlag:  json.NotificationFlag,
		FilterText:        json.FilterText,
		ProductIds:        json.ProductIds,
	}
	response, err := cl.SendBroadcast(context.TODO(), brodData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func SaveUserSkillForSportC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserSportSkill
	json = c.MustGet("jsonData").(structs.UserSportSkill)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	if true {
		log.Printf("SaveUserSkillForSportC: Used In old version of APP")
		c.JSON(http.StatusOK, model.LegacyFailedStatus("This version is no longer supported. Please update your app to continue playing with Fitso."))
		return
	}
	var userId int32
	if len(json.UserId) > 0 {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	} else {
		userId = int32(headers.UserId)
	}

	var tokenVal string
	if len(json.Token) > 0 {
		tokenVal = json.Token
	} else {
		tokenVal = headers.AccessToken
	}

	skillData := &userPB.UserSportSkillRequest{
		UserId:       userId,
		SportId:      json.SportId,
		SkillLevelId: json.SkillLevelId,
		CreatedBy:    int32(headers.UserId),
		Token:        tokenVal,
	}

	response, err := cl.SaveUserSportSkill(context.TODO(), skillData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetUserSkillLevelC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserSportSkillGet
	json = c.MustGet("jsonData").(structs.UserSportSkillGet)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserId) > 0 {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	} else {
		userId = int32(headers.UserId)
	}

	skillData := &userPB.UserSportSkillRequest{
		UserId:  userId,
		SportId: json.SportId,
		//SkillLevel: json.SkillLevel,
	}

	response, err := cl.GetUserSkillLevel(context.TODO(), skillData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetChildUsersC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserInfo
	json = c.MustGet("jsonData").(structs.UserInfo)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserID) > 0 {
		uid, _ := strconv.Atoi(json.UserID)
		userId = int32(uid)
	} else {
		userId = int32(headers.UserId)
	}

	userData := &userPB.UserInfo{
		UserId: userId,
	}

	response, err := cl.GetChildUsers(context.TODO(), userData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GenerateAccessTokenC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserInfo
	json = c.MustGet("jsonData").(structs.UserInfo)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserID) > 0 {
		uid, _ := strconv.Atoi(json.UserID)
		userId = int32(uid)
	} else {
		userId = int32(headers.UserId)
	}

	userData := &userPB.UserInfo{
		UserId: userId,
	}

	response, err := cl.GenerateAccessToken(context.TODO(), userData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetBroadcastLogsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Broadcast
	json = c.MustGet("jsonData").(structs.Broadcast)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = int32(json.UserId)
	} else {
		userId = int32(headers.UserId)
	}

	broadData := &userPB.Broadcast{
		BroadcastId:  json.BroadcastId,
		StartDate:    json.StartDate,
		EndDate:      json.EndDate,
		UserId:       userId,
		Start:        json.Start,
		Count:        json.Count,
		SenderUserId: json.SenderUserId,
	}

	response, err := cl.GetBroadcastLogs(context.TODO(), broadData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func MarkBroadcastC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Broadcast
	json = c.MustGet("jsonData").(structs.Broadcast)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = int32(json.UserId)
	} else {
		userId = int32(headers.UserId)
	}

	broadData := &userPB.Broadcast{
		BroadcastId: json.BroadcastId,
		UserId:      userId,
	}

	response, err := cl.MarkBroadcastReadStatus(context.TODO(), broadData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func HarvestUserCoordinatesC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Harvest
	json = c.MustGet("jsonData").(structs.Harvest)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = int32(json.UserId)
	} else {
		userId = int32(headers.UserId)
	}

	HarvestData := &userPB.HarvestRequest{
		UserId:        userId,
		UserLatitude:  json.UserLatitude,
		UserLongitude: json.UserLongitude,
		AppType:       headers.AppType,
		AppVersion:    headers.AppVersion,
	}

	response, err := cl.HarvestData(context.TODO(), HarvestData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetSuggestionsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Suggestion
	json = c.MustGet("jsonData").(structs.Suggestion)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = int32(json.UserId)
	} else {
		userId = int32(headers.UserId)
	}
	fmt.Println("Header City Id ", int32(headers.CityId))

	var tabIds string
	if len(json.TabIds) > 0 {
		tabIds = json.TabIds
	} else if json.TabId > 0 {
		tabIds = fmt.Sprint(json.TabId)
	} else {
		tabIds = fmt.Sprint(headers.TabId)
	}

	var city_ids string

	if len(json.CityIds) > 0 {
		city_ids = json.CityIds
	} else if json.CityId > 0 {
		city_ids = strconv.Itoa(int(json.CityId))
	} else {
		city_ids = strconv.Itoa(int(headers.CityId))
	}

	suggData := &userPB.Suggestion{
		UserId:       userId,
		SuggestionId: json.SuggestionId,
		CityIds:      city_ids,
		CityId:       int32(headers.CityId),
		TabIds:       tabIds,
	}

	response, err := cl.GetSuggestionsForMasterkey(ctx, suggData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetSportSuggestionsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Suggestion
	json = c.MustGet("jsonData").(structs.Suggestion)

	sportSuggestionCond := &userPB.Suggestion{
		CityIds:     json.CityIds,
		CategoryIds: json.CategoryIds,
		IsActive:    json.IsActive,
		ZoneIdsStr:  json.ZoneIds,
	}

	response, err := cl.GetSportSuggestions(context.TODO(), sportSuggestionCond)
	if err != nil {
		fmt.Println(err)
		c.JSON(http.StatusBadRequest, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	c.JSON(http.StatusOK, response)
}

func CreateOrUpdateSuggestionC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL}, []int32{common.SUGGESTIONS_TOOL})
	if !authorized {
		log.Println("CreateOrUpdateSuggestionC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.Suggestion

	json = c.MustGet("jsonData").(structs.Suggestion)
	suggData := &userPB.Suggestion{
		SuggestionId:        json.SuggestionId,
		AllCitiesFlag:       json.AllCitiesFlag,
		CityIds:             json.CityIds,
		CategoryIds:         json.CategoryIds,
		Title:               json.Title,
		Text:                json.Text,
		SuggestionImage:     json.SuggestionImage,
		ActionId:            json.ActionId,
		Id:                  json.CtaId,
		CtaText:             json.CtaText,
		CtaPostbackParams:   json.CtaPostbackParams,
		SuggestionStartTime: json.SuggestionStartTime,
		SuggestionEndTime:   json.SuggestionEndTime,
		OfferEndTime:        json.OfferEndTime,
		PriorityOrder:       json.PriorityOrder,
		AllTabFlag:          json.AllTabFlag,
		TabIds:              json.TabIds,
		VideoId:             json.VideoId,
		ZoneIds:             sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.ZoneIds),
	}

	response, err := cl.CreateOrUpdateSuggestion(context.TODO(), suggData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetUserCategoriesC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	empty := &userPB.Empty{}
	response, err := cl.GetUserCategories(context.TODO(), empty)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func DumpAttendanceDataPeaceC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DumpAttendanceDataPeace
	json = c.MustGet("jsonData").(structs.DumpAttendanceDataPeace)

	// var headers structs.Headers
	// headers = c.MustGet("requestHeaders").(structs.Headers)

	visitData := json.Visit
	fmt.Println("Dump data from datapeace Request ----- ", visitData)

	checkinTime, _ := time.Parse(time.RFC3339Nano, visitData.CheckinTime)
	checkoutTime, _ := time.Parse(time.RFC3339Nano, visitData.CheckoutTime)
	fmt.Println("DumpAttendanceDataPeaceC: data and dates", checkinTime,checkoutTime	)
	phoneSplit := strings.Split(visitData.Phone, " ")
	phone := ""

	if len(phoneSplit) > 1 {
		phone = phoneSplit[1]
	}
	var purpose_of_visit string
	var sport_name string

	custom_data := visitData.CustomJson.(map[string]interface{})
	if sharedFunc.KeyExists(custom_data, "data") {
		custom_data_n := custom_data["data"].([]interface{})

		for _, question_data := range custom_data_n {

			question_data_n := question_data.(map[string]interface{})

			if sharedFunc.KeyExists(question_data_n, "label") {
				if question_data_n["label"].(string) == "Purpose of your visit" {
					if sharedFunc.KeyExists(question_data_n, "value") {
						answer_data := question_data_n["value"].([]interface{})

						for _, answer := range answer_data {
							if sharedFunc.KeyExists(answer.(map[string]interface{}), "label") {
								purpose_of_visit = (answer.(map[string]interface{}))["label"].(string)
								break
							}
						}
					}
				}
				if question_data_n["label"].(string) == "Please select the sports " {
					if sharedFunc.KeyExists(question_data_n, "value") {
						answer_data := question_data_n["value"].([]interface{})
						for _, answer := range answer_data {
							if sharedFunc.KeyExists(answer.(map[string]interface{}), "label") {
								sport_name = (answer.(map[string]interface{}))["label"].(string)
								break
							}
						}
					}
				}

				if question_data_n["label"].(string) == "Please select your sport" {
					if sharedFunc.KeyExists(question_data_n, "value") {
						answer_data := question_data_n["value"].([]interface{})
						for _, answer := range answer_data {
							if sharedFunc.KeyExists(answer.(map[string]interface{}), "label") {
								sport_name = (answer.(map[string]interface{}))["label"].(string)
								break
							}
						}
					}
				}
			}
		}
	}

		if true {
			var headersReq structs.Headers
			headersReq = c.MustGet("requestHeaders").(structs.Headers)
			
			headers := map[string]string{
				"X-ServiceHook-Signature": 	headersReq.CustomhookSignature,
				"X-ServiceHook-Event":   	headersReq.CustomhookEvent,
				"X-ServiceHook-UUID":        headersReq.CustomhookUuid,
				//"virtual-cluster-name": 	"test-attendance3",
				commonFunc.ContentType:     "application/json",
			}
		
			requestBody, _ := jsonPackage.Marshal(json)
			
			fmt.Println("DumpAttendanceDataPeaceC hook sportsapi: test ", json)
			request := &commonFunc.Request{
				Method:      commonFunc.MethodTypePost,
				RequestURL:  "http://sports-api.production.cure.fit.internal/attendance/dumpAttendance/datapeace",
				Headers:     headers,
				RequestBody: bytes.NewBuffer(requestBody),
			}
					
			dataPeaceHookResponse, _ := commonFunc.MakeHTTPRequest(context.TODO(), request)
			var responseData *userPB.ClearCacheResponse

			fmt.Println("DumpAttendanceDataPeaceC: test response", dataPeaceHookResponse)
			if dataPeaceHookResponse.Status == 200 {
				responseData = &userPB.ClearCacheResponse {
					Status:		&userPB.Status {
						Status:	"success",
					},
				}
			} else {
				responseData = &userPB.ClearCacheResponse {
					Status:		&userPB.Status {
						Status:	"failure",
					},
				}
			}
			render.Render(c, gin.H{
				"title":   "Home Page",
				"payload": responseData}, "index.html")
			return
		}

	createAttData := &userPB.AttendanceDumpData{
		EntryId:          visitData.EntryId,
		Medium:           1,
		PeopleId:         visitData.PeopleId,
		CenterId:         visitData.CenterId,
		Name:             visitData.FirstName + " " + visitData.LastName,
		Email:            visitData.Email,
		Phone:            phone,
		DesignatedKey:    visitData.DesignatedKey,
		CheckinTime:      checkinTime.Unix(),
		CheckoutTime:     checkoutTime.Unix(),
		CheckinPhotoUrl:  visitData.CheckinPhotoUrl,
		CheckoutPhotoUrl: visitData.CheckoutPhotoUrl,
		PurposeOfVisit:   purpose_of_visit,
		SportName:        sport_name,
	}

	timeTillContextDeadline := time.Now().Add(15 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	fmt.Println("Dump data from datapeace ----- ", createAttData)
	response, err := cl.RecordAttendanceDump(ctxbs, createAttData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ValidateDataPeaceUserC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DataPeaceHookRequest 
	json = c.MustGet("jsonData").(structs.DataPeaceHookRequest)

	req_obj := json.Hook.Request.Data
	person_obj := req_obj.Person
	log.Println("ValidateDataPeaceUserC: request", json)

	log.Println("ValidateDataPeaceUserC: request body", req_obj)
	phoneSplit := strings.Split(person_obj.MobileNumber, " ")
	phone := ""

	if len(phoneSplit) > 1 {
		phone = phoneSplit[1]
	}

	var purpose_of_visit string
	custom_data := req_obj.CheckinFlowReq.CheckinCustomData.(map[string]interface{})
	if sharedFunc.KeyExists(custom_data, "data") {
		custom_data_n := custom_data["data"].([]interface{})

		for _, question_data := range custom_data_n {

			question_data_n := question_data.(map[string]interface{})

			if sharedFunc.KeyExists(question_data_n, "label") {
				if question_data_n["label"].(string) == "Purpose of your visit" {
					if sharedFunc.KeyExists(question_data_n, "value") {
						answer_data := question_data_n["value"].([]interface{})

						for _, answer := range answer_data {
							if sharedFunc.KeyExists(answer.(map[string]interface{}), "label") {
								purpose_of_visit = (answer.(map[string]interface{}))["label"].(string)
								break
							}
						}
					}
				}
			}
		}
	}

	reqData := &userPB.ValidateDataPeaceUserRequest{
		Id:             person_obj.Id,
		FirstName:      person_obj.FirstName,
		LastName:       person_obj.LastName,
		MobileNumber:   phone,
		Email:          person_obj.Email,
		PurposeOfVisit: purpose_of_visit,
		SpaceId:        req_obj.SpaceId,
	}
	
		if true {
			var headersReq structs.Headers
			headersReq = c.MustGet("requestHeaders").(structs.Headers)
			
			headers := map[string]string{
				"X-InlineHook-Signature": 	headersReq.CustomhookSignature,
				"X-InlineHook-Event":   	headersReq.CustomhookEvent,
				"X-InlineHook-UUID":        headersReq.CustomhookUuid,
				//"virtual-cluster-name": 	"test-attendance3",
				commonFunc.ContentType:     "application/json",
			}
		
			requestBody1, _ := jsonPackage.Marshal(json)
			fmt.Println("ValidateDataPeaceUserC: hook sportsapi test", json)
			request := &commonFunc.Request{
				Method:      commonFunc.MethodTypePost,
				RequestURL:  "http://sports-api.production.cure.fit.internal/attendance/validateUser/datapeace",//"https://stage.cult.fit/api/v2/fitso/attendance/validateUser",
				Headers:     headers,
				RequestBody: bytes.NewBuffer(requestBody1),
			}
					
			dataPeaceHookResponse, _ := commonFunc.MakeHTTPRequest(context.TODO(), request)
			fmt.Println("ValidateDataPeaceUserC: test resp ", dataPeaceHookResponse)
			var responseData structs.DataPeaceHookResponse
			if err := jsonPackage.Unmarshal([]byte(dataPeaceHookResponse.Body), &responseData); err != nil {
				fmt.Println("ValidateDataPeaceUserC Error: ", err)
				return
			}
			render.Render(c, gin.H{
				"title":   "Home Page",
				"payload": responseData}, "index.html")
			return
		}

	response, err := cl.ValidateDataPeaceUser(context.TODO(), reqData)
	if err != nil {
		log.Printf("Error in validating data peace for user %s with phone %s at space id %d, err: %v", reqData.FirstName+" "+reqData.LastName, reqData.MobileNumber, reqData.SpaceId, err)
		panic(err)
	}
	fmt.Println("handler response", response)

	b := new(bool)
	var checkinFlow structs.CheckinFlow
	if response.Hook.Response.Data.CheckinFlow.Allowed {
		*b = true

		checkinFlow = structs.CheckinFlow{
			Allowed:     b,
			AllowReason: map[string]string{"title": response.Hook.Response.Data.CheckinFlow.AllowReason.Title, "display_text": response.Hook.Response.Data.CheckinFlow.AllowReason.DisplayText},
		}
	} else {
		*b = false

		checkinFlow = structs.CheckinFlow{
			Allowed:    b,
			DenyReason: map[string]string{"title": response.Hook.Response.Data.CheckinFlow.DenyReason.Title, "display_text": response.Hook.Response.Data.CheckinFlow.DenyReason.DisplayText},
		}
	}

	hookResponseData := structs.HookResponseData{
		CheckinFlow: checkinFlow,
	}

	hookResponse := structs.HookResponse{
		Data: hookResponseData,
	}

	hookResponseBody := structs.HookResponseBody{
		Id:       "before_checkin",
		Response: hookResponse,
	}

	dataPeaceHookResponse := structs.DataPeaceHookResponse{
		Hook: hookResponseBody,
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": dataPeaceHookResponse}, "index.html")
}

func GetProcessedAttendanceDataC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.ProcessedAttendanceRequest
	json = c.MustGet("jsonData").(structs.ProcessedAttendanceRequest)

	reqData := &userPB.ProcessedAttendanceDataRequest{
		FacilityId: json.FacilityId,
		SportIds:   sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.SportIds),
		FsIds:      sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.FsIds),
		SlotIds:    sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.SlotIds),
		Date:       json.Date,
	}

	timeTillContextDeadline := time.Now().Add(10 * time.Second)
	ctx, cancel := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer cancel()

	response, err := cl.GetProcessedAttendanceData(ctx, reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetAttendanceCountC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.ProcessedAttendanceRequest
	json = c.MustGet("jsonData").(structs.ProcessedAttendanceRequest)

	reqData := &userPB.ProcessedAttendanceDataRequest{
		FacilityId: json.FacilityId,
		SportIds:   sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.SportIds),
		FsIds:      sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.FsIds),
		SlotIds:    sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.SlotIds),
		Date:       json.Date,
		StartDate:  json.StartDate,
		EndDate:    json.EndDate,
	}

	response, err := cl.GetAttendanceCount(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func AddAttendanceManualC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	toolPermissions,_ := c.Get("tool_access")
	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL}, []int32{common.ATTENDANCE_VIEW_TOOL})
	if !authorized {
		log.Println("AddAttendanceManualC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.AddAttendanceManualRequest
	json = c.MustGet("jsonData").(structs.AddAttendanceManualRequest)

	reqData := &userPB.AddAttendanceManualRequest{
		Name:                   json.Name,
		Phone:                  json.Phone,
		UserId:                 json.UserId,
		CheckinTime:            json.CheckinTime,
		FacilityId:             json.FacilityId,
		BookingReferenceNumber: json.BookingReferenceNumber,
		BookingId:              json.BookingId,
		AttendanceMarkedBy:     json.AttendanceMarkedBy,
	}

	response, err := cl.AddAttendanceManual(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func UnMarkAttendanceC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.AddAttendanceManualRequest
	json = c.MustGet("jsonData").(structs.AddAttendanceManualRequest)

	reqData := &userPB.AddAttendanceManualRequest{
		UserId:                 json.UserId,
		BookingReferenceNumber: json.BookingReferenceNumber,
		AttendanceMarkedBy:     json.AttendanceMarkedBy,
		BookingId:              json.BookingId,
	}

	response, err := cl.UnMarkAttendance(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func ReprocessAttendanceC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.ReprocessAttendanceRequest
	json = c.MustGet("jsonData").(structs.ReprocessAttendanceRequest)

	reqData := &userPB.ReprocessAttendanceRequest{
		ProcessedSportsAttendanceId: json.ProcessedSportsAttendanceId,
	}
	log.Println("Test ReprocessAttendanceC --", reqData)
	response, err := cl.ReprocessAttendance(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetAttendanceDispositionsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	empty := &userPB.Empty{}
	response, err := cl.GetAttendanceDispositions(context.TODO(), empty)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UpdateDispositionForPsAttendanceC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UpdateDisposition
	json = c.MustGet("jsonData").(structs.UpdateDisposition)

	reqData := &userPB.UpdateDispositionForAttendanceReq{
		ProcessedSportsAttendanceId: json.ProcessedSportsAttendanceId,
		AttendanceDispositionId:     json.AttendanceDispositionId,
		Remarks:                     json.Remarks,
	}

	response, err := cl.UpdateDispositionForAttendance(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}
func UpdateUserCitySelectedC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserCitySelectLog
	json = c.MustGet("jsonData").(structs.UserCitySelectLog)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	var userId int32
	if json.UserId > 0 {
		userId = int32(json.UserId)
	} else {
		userId = int32(headers.UserId)
	}

	reqData := &userPB.UpdateUserCitySelectedRequest{
		CityId:   json.CityId,
		UserId:   userId,
		CityName: json.CityName,
	}
	response, err := cl.UpdateUserCitySelected(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UserInterstitialC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserInfo
	json = c.MustGet("jsonData").(structs.UserInfo)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserID) > 0 {
		uid, _ := strconv.Atoi(json.UserID)
		userId = int32(uid)
	} else {
		userId = int32(headers.UserId)
	}

	uInterstitialData := &userPB.UserInfo{
		UserId:         userId,
		AppLaunchCount: json.AppLaunchCount,
	}

	response, err := cl.GetUserInterstitialData(context.TODO(), uInterstitialData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetShortNoticeC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to view Short Notice details."))
		return
	}

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.GetShortNoticeRequest
	json = c.MustGet("jsonData").(structs.GetShortNoticeRequest)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var cityId int32
	if json.CityID > 0 {
		cityId = json.CityID
	} else {
		cityId = int32(headers.CityId)
	}

	var tabId int32
	if json.TabId > 0 {
		tabId = json.TabId
	} else {
		tabId = int32(headers.TabId)
	}

	shortNoticeData := &userPB.GetShortNoticeRequest{
		UserId:            loggedInUserId,
		CityId:            cityId,
		TabId:             tabId,
		ProductCategoryId: common.MasterkeyCategoryID,
	}

	response, err := cl.GetShortNotice(ctx, shortNoticeData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSuggestedContentC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.GetSuggestedContentRequest
	json = c.MustGet("jsonData").(structs.GetSuggestedContentRequest)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = json.UserId
	} else {
		userId = int32(headers.UserId)
	}
	var cityId int32
	if json.CityId > 0 {
		cityId = json.CityId
	} else {
		cityId = int32(headers.CityId)
	}
	var tabId int32
	if json.TabId > 0 {
		tabId = json.TabId
	} else {
		tabId = int32(headers.TabId)
	}
	getSuggestedContent := &userPB.SuggestedContentRequest{
		UserId: userId,
		CityId: cityId,
		TabId:  tabId,
	}

	response, err := cl.SuggestedContentGet(context.TODO(), getSuggestedContent)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetUserScratchCardsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.GetUserScratchCards
	json = c.MustGet("jsonData").(structs.GetUserScratchCards)

	reqData := &userPB.ScratchCards{
		UserId:        json.UserId,
		UserEncodeVal: json.UserEncodeVal,
	}

	response, err := cl.GetUserScratchCards(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ScratchRewardC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.ScratchCard
	json = c.MustGet("jsonData").(structs.ScratchCard)

	reqData := &userPB.ScratchCards{
		ScratchCardId: json.ScratchCardId,
	}

	response, err := cl.ScratchReward(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ClaimRewardC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.ScratchCard
	json = c.MustGet("jsonData").(structs.ScratchCard)

	reqData := &userPB.ScratchCards{
		ScratchCardId: json.ScratchCardId,
	}

	response, err := cl.ClaimReward(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetWinnerDetailsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	empty := &userPB.Empty{}

	response, err := cl.GetWinnerDetails(context.TODO(), empty)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func RecordPlatinumMemberShipC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.PlatinumMemberShip
	json = c.MustGet("jsonData").(structs.PlatinumMemberShip)

	var userId int32
	sharedFunc.DecodeData(json.UserEncodeVal, &userId)
	if userId == 0 {
		userId = json.UserId
	}

	reqData := &userPB.PlatinumMemberShipReq{
		UserId: userId,
	}

	response, err := cl.RecordPlatinumMemberShip(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetPlatinumMemberShipC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.PlatinumMemberShip
	json = c.MustGet("jsonData").(structs.PlatinumMemberShip)

	var userId int32
	sharedFunc.DecodeData(json.UserEncodeVal, &userId)
	if userId == 0 {
		userId = json.UserId
	}

	reqData := &userPB.PlatinumMemberShipReq{
		UserId: userId,
	}

	response, err := cl.GetPlatinumMemberShip(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetNotificationCategoriesC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	empty := &userPB.Empty{}
	response, err := cl.GetNotificationCategories(context.TODO(), empty)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func AddEditNotificationCategoriesC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.NotificationUserCategory
	json = c.MustGet("jsonData").(structs.NotificationUserCategory)

	reqData := &userPB.NotificationUserCategory{
		UserCategoryId: json.UserCategoryId,
		Description:    json.Description,
		SqlQuery:       json.SqlQuery,
		CreatedBy:      json.CreatedBy,
	}
	response, err := cl.AddEditNotificationCategories(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func LogoutUserC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.LogoutUser
	json = c.MustGet("jsonData").(structs.LogoutUser)

	reqData := &userPB.UserAccessTokens{
		UserId:    json.UserId,
		ExpiredBy: json.ExpiredBy,
	}
	response, err := cl.LogoutUser(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func DashboardUserPasswordResetC(c *gin.Context) {
	cl = util.GetUserServiceClient()

	var json structs.DashboardUserPasswordReset
	json = c.MustGet("jsonData").(structs.DashboardUserPasswordReset)

	reqData := &userPB.DashboardUserPasswordResetReq{
		Email: json.Email,
		Phone: json.Phone,
	}
	dashboardUser, err := cl.CheckIfDashboardUserActive(context.TODO(), reqData)
	if err != nil {
		log.Println("func:DashboardUserPasswordResetC: Error in getting dashboard user details if active in controller layer for email: %s err: ", json.Email, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if dashboardUser.Status.Status != "success" {
		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": dashboardUser}, "index.html")
		return
	}

	pfData := &userPB.DashboardUserDetail{
		Email:      json.Email,
		Phone:      json.Phone,
		GroupId:    dashboardUser.DashboardUser.GroupId,
		CreatedBy:  dashboardUser.DashboardUser.CreatedBy,
		FacilityId: dashboardUser.DashboardUser.FacilityId,
		ResetPassword: true,
	}

	response, err := cl.GenerateDashboardLoginAuthCode(context.TODO(), pfData)
	if err != nil {
		log.Println("func:DashboardUserPasswordResetC: Error in generating dashboard user reset password authcode in controller layer for email: %s err: ", json.Email, err)
	}
	if response.Status.Status == "success" {
		response.Status.Message = "Authentication Code has been sent to your email ID. Please check your email to reset your password."
	}
	render.Render(c, gin.H{
		"title":   "Password Reset",
		"payload": response}, "index.html")
}

func GenerateDashboardLoginAuthCodeC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL}, []int32{common.MANAGE_DASHBOARD_USER_TOOL})
	if !authorized {
		log.Println("GenerateDashboardLoginAuthCodeC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)

	pfData := &userPB.DashboardUserDetail{
		Phone:      json.Phone,
		Email:      json.Email,
		GroupId:    json.GroupId,
		CreatedBy:  json.CreatedBy,
		FacilityId: json.FacilityId,
	}

	response, err := cl.GenerateDashboardLoginAuthCode(context.TODO(), pfData)
	if err != nil {
		fmt.Println(err)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetDashboardUsersC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)

	reqdata := &userPB.DashboardUserDetail{
		GroupId: json.GroupId,
	}

	response, err := cl.GetDashboardUsers(context.TODO(), reqdata)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func DashboardLoginC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)
	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	reqdata := &userPB.DashboardUserDetail{
		Email:      json.Email,
		Password:   json.Password,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	response, err := cl.GetDashboardLoginDetails(context.TODO(), reqdata)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UpdateDashboardUserInfoC(c *gin.Context) {
	log.Println("UpdateDashboardUserInfoC:")
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)

	reqdata := &userPB.DashboardUserDetail{
		DuId:     json.DuId,
		UserId:   json.UserId,
		Name:     json.Name,
		Email:    json.Email,
		Password: json.Password,
		Phone:    json.Phone,
		IsActive: 1,
		Token:	  json.NewToken,
		CheckToken: true,
	}
	valid, message:= checkPasswordValid(reqdata)
	if !valid {
		log.Println("UpdateDashboardUserInfoC: Password not stronng ", valid, message)
		status := &userPB.Status{
			Status:  "failure",
			Message: message,
		}
		res := &userPB.ClearCacheResponse {
			Status: status,
		}
		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": res}, "index.html")
		return
	}

	response, err := cl.DashboardUserUpsert(context.TODO(), reqdata)
	if err != nil {
		fmt.Println(err)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ValidateUserAuthCodeC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)

	reqdata := &userPB.DashboardUserDetail{
		Email:    json.Email,
		AuthCode: json.AuthCode,
	}

	response, err := cl.ValidateUserAuthCode(context.TODO(), reqdata)
	if err != nil {
		fmt.Println(err)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetDashboardUserGroupsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardTeamGroup
	json = c.MustGet("jsonData").(structs.DashboardTeamGroup)

	reqdata := &userPB.DashboardTeamGroup{
		TeamId: json.TeamId,
	}

	response, err := cl.GetDashboardUserGroups(context.TODO(), reqdata)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetDashboardGroupToolPermissionsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardTeamGroup
	json = c.MustGet("jsonData").(structs.DashboardTeamGroup)

	reqdata := &userPB.DashboardGroups{
		GroupId: json.GroupId,
	}

	response, err := cl.GetDashboardGroupToolPermissions(context.TODO(), reqdata)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func SubmitCompetitiveUserC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UserInfo
	json = c.MustGet("jsonData").(structs.UserInfo)

	userID, _ := strconv.Atoi(json.UserID)

	reqData := &userPB.UserInfo{
		UserId: int32(userID),
	}

	response, err := cl.SubmitCompetitiveUser(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetUsersOldestCheckInImageC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.UsersOldestCheckInImageRequest
	json = c.MustGet("jsonData").(structs.UsersOldestCheckInImageRequest)

	reqData := &userPB.GetUsersOldestCheckInImageRequest{
		UserIds: json.UserIds,
	}
	response, err := cl.GetUsersOldestCheckInImage(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ListUsersMedicalDetailC(c *gin.Context) {
	cl = util.GetUserServiceClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.ListUsersMedicalDetailsRequest
	json = c.MustGet("jsonData").(structs.ListUsersMedicalDetailsRequest)

	reqData := &userPB.GetUsersMedicalDetailStatusRequest{
		UserIds: json.UserIds,
	}

	response, err := cl.ListUsersMedicalDetail(ctx, reqData)
	if err != nil {
		log.Printf("func:ListUsersMedicalDetailC: Error in getting batch users medical details Controller err: ", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	c.JSON(http.StatusOK, response)
}

func ChangeDashboardGroupC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL}, []int32{common.MANAGE_DASHBOARD_USER_TOOL})
	if !authorized {
		log.Println("ChangeDashboardGroupC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)

	reqData := &userPB.DashboardUserDetail{
		DashboardUserIds: sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.DashboardUserIds),
		GroupId:          json.GroupId,
	}

	response, err := cl.ChangeDashboardGroup(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateUpdateDashboardTeamGroupC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL}, []int32{common.CREATE_USER_PERMISSIONS_TOOL})
	if !authorized {
		log.Println("CreateUpdateDashboardTeamGroupC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardTeamGroup
	json = c.MustGet("jsonData").(structs.DashboardTeamGroup)

	reqData := &userPB.DashboardTeamGroup{
		GroupId:       json.GroupId,
		GroupName:     json.GroupName,
		TeamId:        json.TeamId,
		TeamHead:      json.TeamHead,
		CreatedBy:     json.CreatedBy,
		PermissionIds: sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.PermissionIds),
	}

	response, err := cl.CreateUpdateDashboardTeamGroup(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetDashboardTeamsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	reqData := &userPB.Empty{}
	response, err := cl.GetDashboardTeams(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetAllDashboardToolsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	reqData := &userPB.Empty{}
	response, err := cl.GetAllDashboardTools(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetToolsPermissionsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardTeamGroup
	json = c.MustGet("jsonData").(structs.DashboardTeamGroup)

	reqData := &userPB.DashboardTeamGroup{
		TeamId:  json.TeamId,
		GroupId: json.GroupId,
	}

	response, err := cl.GetToolsPermissions(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateToolPermissionC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL}, []int32{common.CREATE_USER_PERMISSIONS_TOOL})
	if !authorized {
		log.Println("CreateToolPermissionC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardToolPermission
	json = c.MustGet("jsonData").(structs.DashboardToolPermission)

	reqData := &userPB.DashboardToolPermission{
		PermissionName: json.PermissionName,
		ToolId:         json.ToolId,
	}
	response, err := cl.CreateDashboardToolPermission(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func RemoveUserFromDashboardGroupC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL}, []int32{common.CREATE_USER_PERMISSIONS_TOOL, common.MANAGE_DASHBOARD_USER_TOOL})
	if !authorized {
		log.Println("RemoveUserFromDashboardGroupC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = userPB.NewUserService(serviceList.User, service.Client())

	var json structs.DashboardUser
	json = c.MustGet("jsonData").(structs.DashboardUser)

	reqData := &userPB.DashboardUserDetail{
		DuId: json.DuId,
	}
	response, err := cl.RemoveUserFromDashboardGroup(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

// FitsoZomatoMappingC handles mapping request for zomato user id and fitso user id
func FitsoZomatoMappingC(c *gin.Context) {
	cl = util.GetUserServiceClient()

	var json structs.FitsoZomatoUser
	json = c.MustGet("jsonData").(structs.FitsoZomatoUser)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	reqData := &userPB.FitsoZomatoUser{
		FitsoUid:  json.FitsoUserId,
		ZomatoUid: json.ZomatoUserId,
		Token:     headers.ApiKey,
	}

	response, err := cl.FitsoZomatoMapping(c, reqData)

	if err != nil {
		log.Printf("error in mapping zomato user: %v", err)
		panic(err)
	}

	statusCode := http.StatusOK

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
	return
}

// LoginSMSCallbackC handles mapping request for login sms callback
func LoginSMSCallbackC(c *gin.Context) {
	var response interface{}
	var json structs.LoginSMSCallback
	json = c.MustGet("jsonData").(structs.LoginSMSCallback)

	log.Printf("Login SMS Callback status: %s, messageid: %s, provider: %s", json.Status, json.MessageID, json.Provider)

	c.JSON(http.StatusOK, response)
	return
}

func CreateOrUpdateShortNoticeC(c *gin.Context) {
	userClient := util.GetUserServiceClient()

	var json structs.ShortNotice
	json = c.MustGet("jsonData").(structs.ShortNotice)
	shortNoticeData := &userPB.ShortNotice{
		ShortNoticeId:           json.ShortNoticeId,
		Title:                   json.Title,
		Subtitle:                json.Subtitle,
		BottomSheetHeader:       json.BottomSheetHeader,
		ShortNoticePoints:       json.ShortNoticePoints,
		BackgroundColor:         json.BackgroundColor,
		ActionId:                json.ActionId,
		ActionUrl:               json.ActionUrl,
		ActionUrlPostbackParams: json.ActionUrlPostbackParams,
		ActionContent:           json.ActionContent,
		ActionContentColor:      json.ActionContentColor,
		StartTime:               json.StartTime,
		EndTime:                 json.EndTime,
		UserCategories:          json.UserCategories,
		AllCitiesFlag:           json.AllCitiesFlag,
		CityIds:                 json.CityIds,
		ZoneIds:                 json.ZoneIds,
		Priority:                json.Priority,
		IsDismissible:           json.IsDismissible,
		CreatedBy:               json.CreatedBy,
		UpdatedBy:               json.UpdatedBy,
	}

	timeTillContextDeadline := time.Now().Add(3 * time.Second)
	ctx, cancel := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer cancel()

	response, err := userClient.CreateOrUpdateShortNotice(ctx, shortNoticeData)
	if err != nil {
		log.Println("error in creating/updating short notice: ", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(err.Error()),
		)
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)
}

func BatchGetShortNoticeC(c *gin.Context) {
	userClient := util.GetUserServiceClient()

	var json structs.ShortNotice
	json = c.MustGet("jsonData").(structs.ShortNotice)
	sportSuggestionCond := &userPB.BatchGetShortNoticeRequest{
		ZoneIds:        json.ZoneIds,
		UserCategories: json.UserCategories,
		IsActive:       json.IsActive,
		CityIds:        json.CityIds,
	}

	timeTillContextDeadline := time.Now().Add(3 * time.Second)
	ctx, cancel := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer cancel()

	response, err := userClient.BatchGetShortNotice(ctx, sportSuggestionCond)
	if err != nil {
		log.Println("error in getting short notices: ", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(err.Error()),
		)
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)
}

func LogoutV2C(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	userId := util.GetUserIDFromContext(ctx)

	if userId == 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailure("You are not allowed to make this request!"),
		)
		return
	}

	var json structs.Logout
	json = c.MustGet("jsonData").(structs.Logout)
	token := c.Value("requestHeaders").(structs.Headers).AccessToken

	req := &userPB.LogoutRequest{
		Token:    token,
		FcmToken: json.FCMToken,
	}

	_, err := userClient.LogoutV2(ctx, req)
	if err != nil {
		log.Printf("unable to logout userId: %d, token: %s, Err: %v", userId, token, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(http.StatusOK, models.StatusSuccess("User logged out successfully!!"))
}

func SaveUserAppFeedback(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	json := c.MustGet("jsonData").(structs.AppFeedbackParams)

	tokenHash := getAppFeedbackHashForUser(json.UserID)

	if tokenHash != json.TokenHash {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusFailure("You are not allowed to make this request!"),
		)
		return
	}

	userIDInt, err := strconv.ParseInt(json.UserID, 10, 32)

	if err != nil {
		log.Printf("error converting string to int while saving user app feedback for user id %s, err %v", json.UserID, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("Something went wrong. Please try again"),
		)
		return
	}

	userReq := &userPB.UserRequest{
		UserId: int32(userIDInt),
	}

	_, err = userClient.SaveUserFeedback(ctx, userReq)
	if err != nil {
		log.Printf("unable to save feedback for user with userId: %s, err: %v", json.UserID, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.Redirect(http.StatusMovedPermanently, "https://docs.google.com/forms/d/1w5MMQAHX1YLhSH9aCB-S-EIDfFT8SZ71pF_rhYQtMNU")
}

func getAppFeedbackHashForUser(userID string) string {
	tokenHash := config.Get("app_feedback_secret").String("")
	sha256Bytes := sha256.Sum256([]byte(userID + tokenHash))

	return hex.EncodeToString(sha256Bytes[:])
}

func SendOTPC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	json := c.MustGet("jsonData").(structs.SendOTPRequest)

	otpResponse, err := userClient.GenerateOTPForLogin(ctx, &userPB.GenerateOTPRequest{
		Phone:  json.Phone,
		OtpFor: userPB.GenerateOTP_VERIFY_PHONE,
	})

	if err != nil {
		log.Printf("Error in SendOTPC for post object: %v, error: %v", json, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.BAD_REQUEST))
		return
	} else if otpResponse.Status != nil && otpResponse.Status.Status == common.FAILURE {
		log.Printf("Error in SendOTPC for post object: %v, error: %v", json, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(otpResponse.Status.Message))
		return
	}
	c.JSON(http.StatusOK, otpResponse)
}

func GetAllLatestAcademyAssessmentsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	cl = util.GetUserServiceClient()

	json := c.MustGet("jsonData").(structs.GetAllAssessments)

	var fsIds []int32
	if len(json.FsIds) > 0 {
		fsIdsAr := strings.Split(json.FsIds, ",")
		for _, fsIdStr := range fsIdsAr {
			fsId, _ := strconv.Atoi(fsIdStr)
			fsIds = append(fsIds, int32(fsId))
		}
	}

	request := &userPB.GetAllLatestAcademyAssessmentBySportReq{
		Offset: json.Offset,
		FsIds:  fsIds,
		Count:  json.Count,
	}
	if request.Count <= 0 {
		request.Count = 10
	}

	response, err := cl.GetAllLatestAcademyAssessmentBySport(ctx, request)
	if err != nil {
		log.Printf("func:GetAllLatestAcademyAssessmentsC: Error in getting assessement: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("Error in getting assessements"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
}

func GetAllChildUsersC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	cl = util.GetUserServiceClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("User Un-authorized"))
		return
	}
	request := &userPB.UserInfo{
		UserId:       loggedInUserId,
		FetchUserAge: true,
	}

	response, err := cl.GetChildUsers(ctx, request)
	if err != nil {
		log.Printf("func:GetAllChildUsersC: Error in getting child users: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("Error in getting child users"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
}

func CheckCultSignUpStatusOfUserC(c *gin.Context) {
	cl = util.GetUserServiceClient()
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.CultSignUpUserRequest)
	request := &userPB.CultSignUpUserReq{
		Phone:       json.Phone,
		Name: 		 json.Name,
		CultUserId:  json.CultUserId,
	}
	if len(json.Phone) != 10 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}
	response, err := cl.CheckOrCreateCultSignUpUser(ctx, request)
	if err != nil {
		log.Printf("func:CheckCultSignUpStatusOfUserC: Error in checking user details for phone: %v,err: %v", err, json.Phone)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("Error in checking results"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure("something went wrong"))
		return
	}
	c.JSON(http.StatusOK, response)
}

func UserClickActionC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var jsonData structs.UserEventClick
	jsonData = c.MustGet("jsonData").(structs.UserEventClick)

	req := &userPB.UserEventRequest{
		UserId:  		jsonData.UserId,
		HaveMembership: jsonData.HaveMembership,
	}
	userClient := util.GetUserServiceClient()
	response, err := userClient.RecordUserClickAction(ctx, req)
	log.Println("Test the response", req, response)
	if err != nil {
		log.Printf("Unable to send event for userId: %d, Error: %v", jsonData.UserId, err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	type ActionListItem1 struct {
		Type        string             `json:"type"`
		OpenWebview *sushi.OpenWebview `json:"open_webview,omitempty"`
	}
	type UserClickRedirectTemplate struct {
		Status     string            `json:"status,omitempty"`
		ActionList []ActionListItem1 `json:"action_list,omitempty"`
	}
	template := UserClickRedirectTemplate{}
	openWebview := &sushi.OpenWebview{
		Title: "redirect",
		URL:   "https://cure.app.link/OoQhm3IEpDb?force_browser=1",
		InApp: false,
	}
	if jsonData.HaveMembership {
		openWebview.URL = "https://cure.app.link/OoQhm3IEpDb?force_browser=1"
	}
	actionListItem := ActionListItem1{
		Type:         sushi.ClickActionOpenWebview,
		OpenWebview:  openWebview,
	}
	template.Status = "success"
	template.ActionList = []ActionListItem1{actionListItem}

	c.JSON(http.StatusOK, template)
}

func SendAttendanceNotificationC(c *gin.Context) {
	userClient := util.GetUserServiceClient()
	ctx := util.PrepareGRPCMetaData(c)
	var json structs.AttendanceNotification
	json = c.MustGet("jsonData").(structs.AttendanceNotification)
	requestData := &userPB.FitsoBookingAttendanceNotification{
		UserId:           		json.UserId,
		FacilityName:          	json.FacilityName,
		SportName:              json.SportName,
		Timing:       			json.Timing,
		BookingId:       		json.BookingId,
		BookingRef:         	json.BookingRef,
		CityIdV2:				json.CityIdV2,
	}
	log.Println("SendAttendanceNotificationC: ", requestData)
	response, err := userClient.SendAttendanceMarkedNotificationFitsoBooking(ctx, requestData)
	if err != nil {
		log.Println("error in sending notification", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(err.Error()),
		)
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)
}