package userController

import (
	"log"
	"net/http"
	"strconv"
	"context"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userModel "bitbucket.org/jogocoin/go_api/api/models/user"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	//commonFunc "bitbucket.org/jogocoin/go_api/api/server/commonFunc"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/micro/go-plugins/client/selector/shard"
)

// CreateUserRequestTemplate represents the response structure
type CreateUserRequestTemplate struct {
	Status     *userPB.Status                `json:"status,omitempty"`
	ActionList []*userModel.CreateUserAction `json:"action_list,omitempty"`
	User       *userPB.User                  `json:"user,omitempty"`
	Token      *userPB.Token                 `json:"token,omitempty"`
	UserExists bool                          `json:"user_exists,omitempty"`
}

func CreateUserC(c *gin.Context) {

	userClient := util.GetUserServiceClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.CreateUser
	json = c.MustGet("jsonData").(structs.CreateUser)

	var age int32

	if featuresupport.SupportsV2(c) {
		ageFloat, ok := (json.Age).(float64)
		if !ok {
			ageString, ok := (json.Age).(string)
			ageInt, err := strconv.ParseInt(ageString, 10, 32)

			if !ok || err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("Invalid request!"))
				return
			}

			age = int32(ageInt)
		} else {
			age = int32(ageFloat)
		}
	}
	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)


	createUserData := &userPB.CreateUser{
		Name:        json.Name,
		Email:       json.Email,
		Phone:       json.Phone,
		Gender:      json.Gender,
		DateOfBirth: json.DateOfBirth,
		Age:         age,
		Token:       json.Token,
		DeviceId:    headers.DeviceID,
		AppsflyerId: headers.AppsflyerId,
	}
	log.Println("test headers appsflyer", createUserData)

	if featuresupport.SupportsV2(c) {
		response, err := userClient.CreateNewUserV2(ctx, createUserData)
		template := CreateUserRequestTemplate{}
		if err != nil {
			log.Println("Error in creating new user in v2", err)
			response = &userPB.PhoneLoginResponseV2{
				Status: &userPB.Status{
					Status:  common.FAILED,
					Message: "Something went wrong",
				},
			}

			// go commonFunc.SendUserSignUpMetric(ctx, common.FAILED)
			c.AbortWithStatusJSON(http.StatusInternalServerError, template.failedResponse(response))
			return
		}
		if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
			// go commonFunc.SendUserSignUpMetric(ctx, common.FAILED)
			c.JSON(http.StatusUnauthorized, template.failedResponse(response))
			return
		}
		if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
			// go commonFunc.SendUserSignUpMetric(ctx, common.FAILED)
			c.JSON(http.StatusBadRequest, template.failedResponse(response))
			return
		}

		// go commonFunc.SendUserSignUpMetric(ctx, common.SUCCESS)
		c.JSON(http.StatusOK, template.successResponse(ctx, response))

	} else {
		response, err := userClient.CreateNewUser(ctx, createUserData, shard.Strategy(json.Email))
		statusCode := http.StatusOK
		if err != nil {
			log.Println("Error in creating new user ", err)
			c.JSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
			statusCode = http.StatusUnauthorized
		}
		if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, response)
	}
}

func NewCreateUserTemplate() *CreateUserRequestTemplate {
	return &CreateUserRequestTemplate{}
}

func (s *CreateUserRequestTemplate) setTitle(text string) *sushi.TextSnippet {
	title, _ := sushi.NewTextSnippet(text)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize800)
	title.SetColor(color)
	title.SetFont(font)
	return title
}

func (s *CreateUserRequestTemplate) setMessage(text string) *sushi.TextSnippet {
	message, _ := sushi.NewTextSnippet(text)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	message.SetFont(font)
	message.SetColor(color)
	return message
}

func (s *CreateUserRequestTemplate) setImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (s *CreateUserRequestTemplate) failedResponse(res *userPB.PhoneLoginResponseV2) *CreateUserRequestTemplate {
	response := NewCreateUserTemplate()
	response.Status = res.Status
	response.UserExists = res.UserExists
	response.Token = res.Token

	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Failure!"),
		Message: s.setMessage(res.Status.Message),
		Image:   s.setImage(common.FAILURE_IMAGE),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		DismissAfterAction: true,
	}
	createUserAction := &userModel.CreateUserAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	response.ActionList = append(response.ActionList, createUserAction)
	return response
}

func (s *CreateUserRequestTemplate) successResponse(ctx context.Context, res *userPB.PhoneLoginResponseV2) *CreateUserRequestTemplate {
	response := NewCreateUserTemplate()
	response.Status = res.Status
	response.User = res.User
	response.UserExists = res.UserExists
	response.Token = res.Token
	generalAppsflyerImpressionEname := &sushi.EnameData {
		Ename: "signup_user",
	}
	generalAppsflyerUserEvent := sushi.NewAppsflyerEvents()
	generalAppsflyerUserEvent.SetImpression(generalAppsflyerImpressionEname)
	generalAppsflyerTrackItem := sushi.GetAppsflyerTrackItem(ctx, generalAppsflyerUserEvent)
	appslyerTrackPayload := make(map[string]interface{})
	if res.User != nil {
		appslyerTrackPayload["af_customer_user_id"] = res.User.UserId
	}
	generalAppsflyerTrackItem.SetPayload(appslyerTrackPayload)
	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Success!"),
		Message: s.setMessage("Your account has been created"),
		Image:   s.setImage(common.SUCCESS_IMAGE),
		AutoDismissData: &sushi.AutoDismissData{
			Time:              3,
			DismissActionType: "positive",
		},
		IsBlocking: true,
		DismissAfterAction: true,
		AppsflyerTracking: []*sushi.AppsflyerItem{generalAppsflyerTrackItem},
	}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	createUserAction := &userModel.CreateUserAction{
		Type:              sushi.ActionTypeCreateUserSuccess,
		CreateUserSuccess: action,
	}
	response.ActionList = append(response.ActionList, createUserAction)
	return response
}
