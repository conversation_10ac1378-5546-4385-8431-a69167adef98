// product resource

package purchaseResource

import (
	"fmt"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"

	model "bitbucket.org/jogocoin/go_api/api/models"
	purchaseModel "bitbucket.org/jogocoin/go_api/api/models/purchase"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	validator "gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func GetR(c *gin.Context) {
	validate = validator.New()

	var json structs.Purchase

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}
	c.Set("jsonData", json)

	c.Next()
}

func GenerateReceiptR(c *gin.Context) {
	validate = validator.New()

	var json structs.GenerateReceipt

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func ActivateTrialR(c *gin.Context) {
	validate = validator.New()

	var json structs.ActivateTrial

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetPurchaseOptionsR(c *gin.Context) {
	validate = validator.New()

	var json structs.Purchase

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}
	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}
	c.Set("jsonData", json)
	c.Next()
}

func StoreAmazonGiftCardsR(c *gin.Context) {
	validate = validator.New()

	var json structs.AmazonGiftCards

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}
	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetUnusedGiftCardsR(c *gin.Context) {
	c.Next()
}

// MakePurchaseR validates make purchase API request body
func MakePurchaseR(c *gin.Context) {
	validate = validator.New()

	var json purchaseModel.MakePurchaseRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for make purchase: %v", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for make purchase : %v", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

// MakeSubscriptionR validates make purchase API request body
func MakeSubscriptionR(c *gin.Context) {
	validate = validator.New()

	c.Request.ParseForm()

	c.Set("jsonData", c.Request.PostForm)

	c.Next()
}

// PaymentCallbackR validates make purchase API request body
func PaymentCallbackR(c *gin.Context) {
	validate = validator.New()

	c.Request.ParseForm()

	c.Set("jsonData", c.Request.PostForm)

	c.Next()
}

func RefundCallbackR(c *gin.Context) {
	validate = validator.New()

	c.Request.ParseForm()

	c.Set("jsonData", c.Request.PostForm)

	c.Next()
}

// PurchaseStatusR validates purchase status API request body
func PurchaseStatusR(c *gin.Context) {
	validate = validator.New()

	var json purchaseModel.PurchaseStatusRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for creating subscription for purchase: %v", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for subscription for purchase: %v", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

// PurchaseHistoryR validates purchase history API request body
func PurchaseHistoryR(c *gin.Context) {
	validate = validator.New()

	var json purchaseModel.PurchaseHistoryRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for purchase history: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for purchase history: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func SendUserMailsForPurchaseR(c *gin.Context) {
	validate = validator.New()

	var json purchaseModel.SendMailForPurchaseRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for sending purchase welcome mail: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for sending purchase welcome mail: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func MakeAcademyPurchaseR(c *gin.Context) {
	validate = validator.New()

	var json structs.MakeAcademyPurchaseRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("Error in binding request for make academy purchase: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error in validating request for make academy purchase: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
	}

	c.Set("jsonData", json)

	c.Next()
}

func SendTaxInvoiceR(c *gin.Context) {
	validate = validator.New()

	var json structs.SendTaxInvoice

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("func:SendTaxInvoiceR, error in binding request, err: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:SendTaxInvoiceR, error in validate request, err: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func DashboardRefundAndCancelR(c *gin.Context) {
	validate = validator.New()

	var json structs.CancelMembershipReq

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for cancelling and refunding membership: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for cancelling and refunding membership: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetRefundAmountR(c *gin.Context) {
	validate = validator.New()

	var json structs.Subscription

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}
	c.Set("jsonData", json)

	c.Next()
}

// PurchaseStatusR validates purchase status API request body
func PurchasePaymentStatusR(c *gin.Context) {
	validate = validator.New()

	var json purchaseModel.PurchaseStatusRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("fun PurchasePaymentStatusR: error in binding request for creating subscription for web purchase: %v", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func PurchasePaymentStatusR: error in validate request for subscription for web purchase: %v", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func SetDiscountAmountMapR(c *gin.Context) {
	validate = validator.New()

	var json structs.SetDiscountRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("fun SetDiscountAmountMapR: error in binding request for setting discount for offline purchase: %v", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func SetDiscountAmountMapR: error in validate request for setting discount for offline purchase: %v", err)
	}

	c.Set("jsonData", json)

	c.Next()
}
