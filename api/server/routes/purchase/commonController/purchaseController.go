package commonController

import (
	purchaseController "bitbucket.org/jogocoin/go_api/api/server/routes/purchase/controllers"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"strconv"
	"fmt"
)

func MakePurchaseCommonC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(structs.MakeAcademyPurchaseRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryId := fmt.Sprintf("%v", requestData.ProductCategoryId)
	categoryID, _ := strconv.Atoi(categoryId)
	if categoryID == common.SummerCampCategoryID {
		purchaseController.MakeSummercampPurchaseC(c)
	} else {
		purchaseController.MakeAcademyPurchaseC(c)
	}
}