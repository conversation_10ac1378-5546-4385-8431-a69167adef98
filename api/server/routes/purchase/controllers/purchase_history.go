package purchaseController

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"time"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	model "bitbucket.org/jogocoin/go_api/api/models/purchase"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
)

type PurchaseHistoryTemplate struct {
	Header         *model.Header                          `json:"header,omitempty"`
	Results        []*model.ResultSection                 `json:"results,omitempty"`
	Response       *purchasePB.GetPurchaseHistoryResponse `json:"-"`
	PostbackParams string                                 `json:"postback_params,omitempty"`
	Has<PERSON>ore        bool                                   `json:"has_more,omitempty"`
}

// PurchaseHistoryC handles purchase history request
func PurchaseHistoryC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(model.PurchaseHistoryRequest)

	loggedInUserID := util.GetUserIDFromContext(ctx)
	if loggedInUserID <= 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	previousPurchaseIds := parseRequestPostbackParams(json.PostbackParams)

	pcl := util.GetPurchaseClient()
	request := &purchasePB.GetPurchaseHistoryRequest{
		UserId:              json.UserID,
		ProductId:           json.ProductID,
		PreviousPurchaseIds: previousPurchaseIds,
	}
	response, err := pcl.GetPurchaseHistory(ctx, request)

	if err != nil {
		log.Printf("Api: purchase history, Failed request, Error: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
		return
	}

	statusCode := http.StatusOK
	if response.Status != nil {
		switch response.Status.Status {
		case common.NOT_AUTHORIZED:
			statusCode = http.StatusUnauthorized
			break
		case common.FAILED:
			statusCode = http.StatusInternalServerError
			log.Printf("Api: purchase history, Failed request, Error: %v", response.Status.Message)
			break
		case common.BAD_REQUEST:
			statusCode = http.StatusBadRequest
			log.Printf("Api: purchase history, Bad request, Error: %v", response.Status.Message)
			break

		}
		if response.Status.Status != common.SUCCESS {
			c.JSON(statusCode, models.StatusFailure(response.Status.Message))
			return
		}
	}

	template := PurchaseHistoryTemplate{
		Response: response,
	}

	template.setHeaders(ctx)
	err = template.setResults(ctx)
	if err != nil {
		log.Printf("Api: purchase history, Failed request, Error: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
		return
	}
	c.JSON(http.StatusOK, template)
}

func (p *PurchaseHistoryTemplate) setPostbackParams(key string, val interface{}) {
	payload := map[string]interface{}{
		key: val,
	}

	postback_params, err := json.Marshal(payload)
	if err != nil {
		log.Println("Func: SetPostbackParams")
		log.Println("error in marshalling postback_params", err)
	}
	p.PostbackParams = string(postback_params)
}

func (p *PurchaseHistoryTemplate) setHasMore(hasMore bool) {
	p.HasMore = hasMore
}

func parseRequestPostbackParams(postbackParams string) []int32 {
	var temp map[string]interface{}
	json.Unmarshal([]byte(postbackParams), &temp)

	previousPurchaseIds := make([]int32, 0)
	if purchaseIds, ok := temp["purchase_ids"]; ok {
		purchaseIdsInterface, _ := purchaseIds.([]interface{})
		for _, val := range purchaseIdsInterface {
			previousPurchaseIds = append(previousPurchaseIds, int32(val.(float64)))
		}
	}
	return previousPurchaseIds
}

func (p *PurchaseHistoryTemplate) setHeaders(ctx context.Context) {
	headerTitle, _ := sushi.NewTextSnippet("Purchase history")
	p.Header = &model.Header{
		Title: headerTitle,
	}
}

func (p *PurchaseHistoryTemplate) setResults(ctx context.Context) error {

	p.setPostbackParams("purchase_ids", p.Response.PreviousPurchaseIds)
	p.setHasMore(p.Response.HasMore)

	for _, purchaseHistory := range p.Response.PurchaseHistory {

		item := &sushi.FitsoTextSnippetType4Item{}
		p.setProductName(ctx, item, purchaseHistory)
		p.setAmount(ctx, item, purchaseHistory.Purchase.ActualAmount)
		err := p.setUserDetail(ctx, item)
		if err != nil {
			log.Printf("Error while setting user details snippet %s", err)
			return err
		}
		p.setPurchaseDetail(ctx, item, purchaseHistory)
		p.setValidThru(ctx, item, purchaseHistory)

		fitsoTextType4 := &sushi.FitsoTextType4{
			Items: []*sushi.FitsoTextSnippetType4Item{item},
		}
		result := &model.ResultSection{
			LayoutConfig:          layoutConfigSnippetType4(),
			FitsoTextSnippetType4: fitsoTextType4,
		}
		if purchaseHistory.CancellationDetails == nil {
			result.SnippetConfig = snippetConfig()
		}
		p.addResultSection(result)

		err = p.setRefundSubtotal(ctx, purchaseHistory)
		if err != nil {
			log.Printf("Error while setting refund total snippet %s", err)
			return err
		}
	}
	return nil
}

func layoutConfigSnippetType4() *sushi.LayoutConfig {
	return &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType4,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
}

func snippetConfig() *sushi.SnippetConfig {
	separatorColorBottom, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	separatorBottom, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColorBottom)
	return &sushi.SnippetConfig{
		BottomSeparator: separatorBottom,
	}
}

func (p *PurchaseHistoryTemplate) addResultSection(section *model.ResultSection) {
	p.Results = append(p.Results, section)
}

// helper function to print date in specific format
func formatDateString(timestamp int64) string {
	dateObj := time.Unix(timestamp, 0)
	year, month, day := dateObj.Date()
	monthStr := month.String()
	return fmt.Sprintf("%d %s %d", day, monthStr[:3], year)
}

func (p *PurchaseHistoryTemplate) setProductName(ctx context.Context, item *sushi.FitsoTextSnippetType4Item, purchaseHistory *purchasePB.PurchaseHistory) {
	productName := p.Response.ProductName

	var status string
	switch purchaseHistory.Purchase.PaymentStatus {
	case completePaymentStatus:
		status = ""
		if purchaseHistory.Subscription != nil && purchasePB.SubscriptionStatus_CANCELLED == purchaseHistory.Subscription.Status {
			status = "Cancelled"
		}
		break
	case failedPaymentStatus:
		status = "Failed"
		break
	case pendingPaymentStatus:
		status = "Pending"
		break
	}

	if len(status) > 0 {
		productName += " " + fmt.Sprintf("%s %s %s", "{red-600|<regular-200|(", status, ")>}")
	}

	title, _ := sushi.NewTextSnippet(productName)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint800)
	title.SetFont(font)
	title.SetColor(color)
	title.SetIsMarkdown(1)
	item.Title = title
}

func (p *PurchaseHistoryTemplate) setAmount(ctx context.Context, item *sushi.FitsoTextSnippetType4Item, amount float32) {
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)

	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", amount))
	subtitle.SetFont(font)
	subtitle.SetColor(color)
	item.Subtitle = subtitle
}

func (p *PurchaseHistoryTemplate) setUserDetail(ctx context.Context, item *sushi.FitsoTextSnippetType4Item) error {
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint800)
	userDetailsStr := p.Response.UserDetails.Name
	if len(p.Response.UserDetails.Phone) > 0 {
		userDetailsStr += ", " + p.Response.UserDetails.Phone
	}
	subtitle1, err := sushi.NewTextSnippet(userDetailsStr)
	if err != nil {
		return err
	}
	subtitle1.SetFont(font)
	subtitle1.SetColor(color)
	item.Subtitle1 = subtitle1
	return nil
}

func (p *PurchaseHistoryTemplate) setPurchaseDetail(ctx context.Context, item *sushi.FitsoTextSnippetType4Item, purchaseHistory *purchasePB.PurchaseHistory) {
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	subtitle3Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	purchasedOnStr := "Purchased on " + formatDateString(purchaseHistory.Purchase.PurchaseDate.Seconds)
	subtitle3, _ := sushi.NewTextSnippet(purchasedOnStr)
	subtitle3.SetFont(font)
	subtitle3.SetColor(subtitle3Color)
	item.Subtitle3 = subtitle3

	subtitle4Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	subtitle4Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle4, _ := sushi.NewTextSnippet("Purchased by: " + purchaseHistory.PurchasedBy)
	subtitle4.SetFont(subtitle4Font)
	subtitle4.SetColor(subtitle4Color)
	item.Subtitle4 = subtitle4

}

func (p *PurchaseHistoryTemplate) setValidThru(ctx context.Context, item *sushi.FitsoTextSnippetType4Item, purchaseHistory *purchasePB.PurchaseHistory) {
	if purchaseHistory.Subscription == nil {
		return
	}
	// todo: add a check  for subscription active status
	startDateStr := formatDateString(purchaseHistory.Subscription.StartDate.Seconds)
	endDateStr := formatDateString(purchaseHistory.Subscription.EndDate.Seconds)

	subtitle2Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	snippetSubtitle2, _ := sushi.NewTextSnippet("Valid thru: " + startDateStr + " - " + endDateStr)
	snippetSubtitle2.SetFont(subtitle2Font)
	snippetSubtitle2.SetColor(subtitle2Color)
	item.Subtitle2 = snippetSubtitle2
}

func (p *PurchaseHistoryTemplate) setRefundSubtotal(ctx context.Context, purchaseHistory *purchasePB.PurchaseHistory) error {
	if purchaseHistory.CancellationDetails == nil {
		return nil
	}

	fitsoTextSnippetType4Items := make([]*sushi.FitsoTextSnippetType4Item, 0)

	snippetItem := &sushi.FitsoTextSnippetType4Item{}

	snippetTitle, _ := sushi.NewTextSnippet("Refund Subtotal")
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	snippetTitle.SetFont(titleFont)
	snippetTitle.SetColor(titleColor)
	snippetItem.Title = snippetTitle

	subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

	var refundTotal string
	cancellationAmount := purchaseHistory.CancellationDetails.Amount
	_, float := math.Modf(float64(cancellationAmount))
	if float != 0 {
		refundTotal = fmt.Sprintf("₹%.2f", cancellationAmount)
	} else {
		refundTotal = fmt.Sprintf("₹%.f", cancellationAmount)
	}

	snippetSubtitle, _ := sushi.NewTextSnippet(refundTotal)
	snippetSubtitle.SetFont(subtitleFont)
	snippetSubtitle.SetColor(subtitleColor)
	snippetItem.Subtitle = snippetSubtitle

	if len(purchaseHistory.PaymentMethod) != 0 {
		subtitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		snippetSubtitle1, _ := sushi.NewTextSnippet(fmt.Sprintf("Refunded to %s", purchaseHistory.PaymentMethod))
		snippetSubtitle1.SetFont(subtitle1Font)
		snippetSubtitle1.SetColor(subtitle1Color)
		snippetItem.Subtitle1 = snippetSubtitle1
	}

	snippetItem.IsExpanded = false

	verticalExpandedSubtitles := make([]*sushi.TextSnippet, 0)
	cancelledDateStr := formatDateString(purchaseHistory.CancellationDetails.CreatedAt.Seconds)

	snippetSubtitle8, _ := sushi.NewTextSnippet("Cancelled on " + cancelledDateStr)
	titleFont4, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	snippetSubtitle8.SetFont(titleFont4)
	titleColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	snippetSubtitle8.SetColor(titleColor)
	verticalExpandedSubtitles = append(verticalExpandedSubtitles, snippetSubtitle8)
	snippetItem.VerticalExpandedSubtitles = verticalExpandedSubtitles

	expandedData, err := expandedData(ctx, purchaseHistory)
	if err != nil {
		return err
	}
	snippetItem.ExpandedData = expandedData
	fitsoTextSnippetType4Items = append(fitsoTextSnippetType4Items, snippetItem)

	fitsoTextType4 := &sushi.FitsoTextType4{
		Items: fitsoTextSnippetType4Items,
	}
	result := &model.ResultSection{
		LayoutConfig:          layoutConfigSnippetType4(),
		FitsoTextSnippetType4: fitsoTextType4,
		SnippetConfig:         snippetConfig(),
	}

	p.addResultSection(result)
	return nil
}

func expandedData(ctx context.Context, purchaseHistory *purchasePB.PurchaseHistory) (*sushi.FitsoBottomSheetType1ResultSection, error) {
	vertical_list_items := make([]sushi.VerticalListItem, 0)

	title, _ := sushi.NewTextSnippet("Grand Total")
	titleFontSublist, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	titleColorSublist, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	title.SetFont(titleFontSublist)
	title.SetColor(titleColorSublist)

	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", purchaseHistory.Purchase.ActualAmount))
	subtitle.SetFont(titleFontSublist)
	subtitle.SetColor(titleColorSublist)

	snippetItemSublist1 := &sushi.FitsoTextSnippetType5Item{
		Title:    title,
		Subtitle: subtitle,
	}
	vertical_list_item1 := setSnippetFitsoTextType5Sublist(ctx, snippetItemSublist1)
	vertical_list_items = append(vertical_list_items, *vertical_list_item1)

	endDate, err := ptypes.Timestamp(purchaseHistory.CancellationDetails.CreatedAt)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	var remainingDays string
	if purchaseHistory.Subscription == nil {
		return nil, errors.New("No subscription available")
	}
	startDate, err := ptypes.Timestamp(purchaseHistory.Subscription.StartDate)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	startDate = GetLocalDateTime(startDate)
	endDate = GetLocalDateTime(endDate)
	endDate = Bod(endDate)
	startDate = Bod(startDate)
	days := endDate.Sub(startDate).Hours() / 24
	if days >= 1 {
		days = days + 1
		remainingDays = fmt.Sprintf("Cost of service used (%d days)", int(days))
	} else if days >= 0 {
		remainingDays = fmt.Sprintf("Cost of service used (%d day)", 1)
	} else {
		remainingDays = fmt.Sprintf("Cost of service used (%d days)", 0)
	}

	snippetTitleSublist2, _ := sushi.NewTextSnippet(remainingDays)
	snippetTitleSublist2.SetFont(titleFontSublist)
	snippetTitleSublist2.SetColor(titleColorSublist)
	var amount string
	_, floatDigits := math.Modf(float64(purchaseHistory.Purchase.ActualAmount - purchaseHistory.CancellationDetails.Amount))
	if floatDigits != 0 {
		amount = fmt.Sprintf("₹%.2f", purchaseHistory.Purchase.ActualAmount-purchaseHistory.CancellationDetails.Amount)
	} else {
		amount = fmt.Sprintf("₹%.f", purchaseHistory.Purchase.ActualAmount-purchaseHistory.CancellationDetails.Amount)
	}
	snippetSubtitleSublist2, _ := sushi.NewTextSnippet(amount)
	snippetSubtitleSublist2.SetFont(titleFontSublist)
	snippetSubtitleSublist2.SetColor(titleColorSublist)

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint200)
	if featuresupport.SupportsNewColor(ctx) {
		separatorColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint200)
	}
	separator := &sushi.TextSnippet{
		Type:  "line",
		Color: separatorColor,
	}

	snippetItemSublist2 := &sushi.FitsoTextSnippetType5Item{
		Title:               snippetTitleSublist2,
		Subtitle:            snippetSubtitleSublist2,
		BottomSeparatorData: separator,
	}
	vertical_list_item2 := setSnippetFitsoTextType5Sublist(ctx, snippetItemSublist2)
	vertical_list_items = append(vertical_list_items, *vertical_list_item2)

	snippetTitleSublist3, _ := sushi.NewTextSnippet("Total Refund")
	titleFontSublist3, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	titleColorSublist3, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

	snippetTitleSublist3.SetFont(titleFontSublist3)
	snippetTitleSublist3.SetColor(titleColorSublist3)
	var refundTotal string
	_, float := math.Modf(float64(purchaseHistory.CancellationDetails.Amount))
	if float != 0 {
		refundTotal = fmt.Sprintf("₹%.2f", purchaseHistory.CancellationDetails.Amount)
	} else {
		refundTotal = fmt.Sprintf("₹%.f", purchaseHistory.CancellationDetails.Amount)
	}
	snippetSubtitleSublist3, _ := sushi.NewTextSnippet(refundTotal)
	snippetSubtitleSublist3.SetFont(titleFontSublist3)
	snippetSubtitleSublist3.SetColor(titleColorSublist3)

	snippetItemSublist3 := &sushi.FitsoTextSnippetType5Item{
		Title:    snippetTitleSublist3,
		Subtitle: snippetSubtitleSublist3,
	}
	vertical_list_item3 := setSnippetFitsoTextType5Sublist(ctx, snippetItemSublist3)
	vertical_list_items = append(vertical_list_items, *vertical_list_item3)

	vertical_list_border, _ := sushi.NewBorderSnippet()
	vertical_list_border_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
	if featuresupport.SupportsNewColor(ctx) {
		vertical_list_border_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
	}
	vertical_list_border.SetBorderColor(vertical_list_border_color)
	vertical_list_border.SetBorderType(sushi.BorderTypeRounded)
	vertical_list_border.SetBorderRadius(sushi.BorderRadius8)

	vertical_list_bg_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint50)
	if featuresupport.SupportsNewColor(ctx) {
		vertical_list_bg_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint50)
	}
	vertical_list_type_1 := &sushi.VerticalListType1{
		Items:      vertical_list_items,
		BgColor:    vertical_list_bg_color,
		BorderData: vertical_list_border,
	}

	vertical_list_type_1_layout_config := &sushi.LayoutConfig{
		SnippetType: sushi.VerticalListSnipppetType1,
		LayoutType:  sushi.LayoutTypeGrid,
	}
	return &sushi.FitsoBottomSheetType1ResultSection{
		LayoutConfig:      vertical_list_type_1_layout_config,
		VerticalListType1: vertical_list_type_1,
	}, nil

}

func setSnippetFitsoTextType5Sublist(ctx context.Context, snippetItem *sushi.FitsoTextSnippetType5Item) *sushi.VerticalListItem {
	fitso_text_snippet_type_5_items_sublist := make([]*sushi.FitsoTextSnippetType5Item, 0)
	fitso_text_snippet_type_5_items_sublist = append(fitso_text_snippet_type_5_items_sublist, snippetItem)
	fitso_text_snippet_type_5_sublist := &sushi.FitsoTextType5{
		Items: fitso_text_snippet_type_5_items_sublist,
	}

	vl_layout_config1 := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType5,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	vertical_list_item := &sushi.VerticalListItem{
		LayoutConfig:          vl_layout_config1,
		FitsoTextSnippetType5: fitso_text_snippet_type_5_sublist,
	}
	return vertical_list_item
}

func GetLocalDateTime(date time.Time) time.Time {
	location, _ := time.LoadLocation("Asia/Kolkata") //IST
	return date.In(location)
}
func Bod(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, t.Location())
}
