package purchaseController

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"

	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/render"

	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
	"github.com/micro/go-micro"

	// "github.com/micro/go-plugins/wrapper/select/roundrobin"
	// "github.com/micro/go-micro/client"
	"strconv"

	purchaseModel "bitbucket.org/jogocoin/go_api/api/models/purchase"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/go-redis/redis"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/client/selector/shard"
)

var (
	cl        purchasePB.PurchaseService
	clProduct productPB.ProductService
)

func GetC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = purchasePB.NewPurchaseService(serviceList.Purchase, service.Client())
	// cl = authPB.NewAuthService("product.service.auth", client.DefaultClient)

	var json structs.Purchase

	json = c.MustGet("jsonData").(structs.Purchase)

	purchaseData := &purchasePB.PurchaseRequest{
		PurchaseId: json.PurchaseId,
		UserId:     json.UserId,
	}

	// session affinity based call
	response, err := cl.PurchaseGet(context.TODO(), purchaseData, shard.Strategy(string(json.PurchaseId)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func checkDashboardUserAuthorised(toolPermissions interface{}, permissions []string, toolIds []int32) bool {
	if maptoolsPermissions, ok := toolPermissions.(map[int32]*userPB.ListOfPermissions); ok {
		for j := 0; j < len(toolIds); j++ {
			log.Println("checkDashboardUserAuthorised: ", maptoolsPermissions[toolIds[j]])
			if maptoolsPermissions[toolIds[j]] != nil && len(maptoolsPermissions[toolIds[j]].Permissions) > 0 {
				for _, permission := range maptoolsPermissions[toolIds[j]].Permissions {
					for i := 0; i < len(permissions); i++ {
						log.Println("checkDashboardUserAuthorised permissions", permission, permissions[i], maptoolsPermissions[toolIds[j]])
						if permissions[i] == permission {
							log.Println("checkDashboardUserAuthorised permissions granted ", permission, permissions[i], maptoolsPermissions[toolIds[j]])
							return true
						}
					}
				}
			}
		}
	}

	return false
}

func GetPurchaseOptionsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = purchasePB.NewPurchaseService(serviceList.Purchase, service.Client())
	// cl = authPB.NewAuthService("product.service.auth", client.DefaultClient)

	var json structs.Purchase
	json = c.MustGet("jsonData").(structs.Purchase)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = json.UserId
	} else {
		userId = int32(headers.UserId)
	}
	purchaseData := &purchasePB.PurchaseOptionsRequest{
		UserId: userId,
		CityId: json.CityId,
	}

	// session affinity based call
	response, err := cl.PurchaseOptionsGet(context.TODO(), purchaseData, shard.Strategy(string(int(userId))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetMaxDiscountAllowedC(c *gin.Context) {
	cl := util.GetPurchaseClient()

	empty := &purchasePB.Empty{}

	response, err := cl.GetMaxDiscountAllowed(context.TODO(), empty)
	if err != nil {
		log.Printf("Error in getting max discount allowed: %v", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	c.JSON(http.StatusOK, response)
}

func GetDiscountAmountMapC(c *gin.Context) {
	cl := util.GetPurchaseClient()

	empty := &purchasePB.Empty{}

	response, err := cl.GetDiscountAmountMap(context.TODO(), empty)

	if err != nil {
		log.Printf("Error in getting discount map: %v", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	if response.DiscountMap == nil {
		log.Println("func:GetDiscountAmountMapC DiscountMap not set in redis")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("No Discount Amount Found"),
		)
		return
	}

	c.JSON(http.StatusOK, response)
}

func SetDiscountAmountMapC(c *gin.Context) {
	cl := util.GetPurchaseClient()

	var json structs.SetDiscountRequest
	json = c.MustGet("jsonData").(structs.SetDiscountRequest)

	reqData := &purchasePB.SetDiscountRequest{
		ProductCategoryId:     json.ProductCategoryId,
		ProductDurationInDays: json.ProductDurationInDays,
		DiscountAmount:        json.DiscountAmount,
	}

	response, err := cl.SetDiscountAmountMap(context.TODO(), reqData)

	if err != nil {
		log.Printf("Error in setting discount map: %v", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	if response.DiscountMap == nil {
		log.Println("func:GetDiscountAmountMapC DiscountMap not set in redis")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("No Discount Amount Found"),
		)
		return
	}

	c.JSON(http.StatusOK, response)
}

func GenerateReceiptC(c *gin.Context) {
	cl := util.GetPurchaseClient()
	ctx := util.PrepareGRPCMetaData(c)
	toolPermissions, _ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL, common.EDIT_DISCOUNT_PERMISSION, common.EDIT_AMOUNT_PERMISSION}, []int32{common.RECIEPT_GENERATION_TOOL})
	if !authorized {
		log.Println("GenerateReceiptC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var json structs.GenerateReceipt

	json = c.MustGet("jsonData").(structs.GenerateReceipt)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var members_data []*purchasePB.MemberData

	for _, member_json := range json.MembersData {

		psd := time.Unix(member_json.PlanStartDate, 0)
		planSD, _ := ptypes.TimestampProto(psd)

		member_data := &purchasePB.MemberData{
			UserId:              member_json.UserId,
			SubscriptionId:      member_json.SubscriptionId,
			PlanStartDate:       planSD,
			PreferredFacilityId: member_json.PreferredFacilityId,
			PreferredSportId:    member_json.PreferredSportId,
			Age:                 member_json.Age,
		}
		if len(member_json.FacilitySlots) > 0 {
			for _, v := range member_json.FacilitySlots {
				fs := &purchasePB.FacilitySlot{
					AcademySlotId:    v.AcademySlotId,
					SummercampSlotId: v.SummercampSlotId,
				}
				member_data.FacilitySlots = append(member_data.FacilitySlots, fs)
			}
		}
		members_data = append(members_data, member_data)
	}
	purDate := time.Unix(json.PurchaseDate, 0)
	purchaseDate, _ := ptypes.TimestampProto(purDate)
	userID := util.GetUserIDFromContext(ctx)

	receiptData := &purchasePB.ReceiptGenerationRequest{
		UserId:            json.UserId,
		ProductId:         json.ProductId,
		Amount:            json.Amount,
		DiscountPerPerson: json.DiscountPerPerson,
		PurchaseType:      json.PurchaseType,
		Provider:          purchasePB.PaymentProvider(json.Provider),
		Purpose:           json.Purpose,
		TransactionNumber: json.TransactionNumber,
		CollectedBy:       json.CollectedBy,
		PurchaseDate:      purchaseDate,
		MembersData:       members_data,
		AppType:           headers.AppType,
		AppVersion:        headers.AppVersion,
		GeneratedBy:       userID,
		VoucherId:         json.VoucherId,
	}
	log.Println("ReceiptGenerationRequest", receiptData)

	timeTillContextDeadline := time.Now().Add(30 * time.Second)
	ctxb, ctxCancelFuncb := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncb()

	// session affinity based call
	response, err := cl.GenerateReceipt(ctxb, receiptData)
	if err != nil {
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetEligibleVouchersC(c *gin.Context) {
	pcl := util.GetProductClient()
	ucl := util.GetUserServiceClient()
	toolPermissions, _ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL, common.SHOW__TOOL, common.EDIT_DISCOUNT_PERMISSION, common.EDIT_AMOUNT_PERMISSION}, []int32{common.RECIEPT_GENERATION_TOOL})
	if !authorized {
		log.Println("GetEligibleVouchersC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var json structs.GenerateReceipt

	json = c.MustGet("jsonData").(structs.GenerateReceipt)
	c.Set("user_id", json.UserId)
	ctx := util.PrepareGRPCMetaData(c)

	var parentUser *productPB.CartUser
	var childUsersToSubscribe []*productPB.CartUser
	var isSummerCamp bool
	var isAcademy bool
	for _, member_json := range json.MembersData {
		userRequest := &userPB.UserRequest{
			UserId: member_json.UserId,
		}

		usersData, err := ucl.UserGet(ctx, userRequest)
		if err != nil {
			log.Println("GetEligibleVouchersC: User not authorised ", toolPermissions)
			c.AbortWithStatusJSON(http.StatusBadRequest, common.StatusFailed(common.BAD_REQUEST))
			return
		}

		userData := usersData.Users[0]

		cartUser := &productPB.CartUser{
			UserId:              member_json.UserId,
			Name:                userData.Name,
			PlanStartDate:       member_json.PlanStartDate,
			ProductId:           json.ProductId,
			Age:                 userData.Age,
			PreferredFacilityId: member_json.PreferredFacilityId,
			PreferredSportId:    member_json.PreferredSportId,
			Phone:               userData.Phone,
		}
		if len(member_json.FacilitySlots) > 0 {
			var facilitySlots []*productPB.FacilitySlot
			for _, facilitySlot := range member_json.FacilitySlots {
				facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
					SummercampSlotId: facilitySlot.SummercampSlotId,
					AcademySlotId:    facilitySlot.AcademySlotId,
				})
				if facilitySlot.SummercampSlotId > 0 {
					isSummerCamp = true
				} else if facilitySlot.AcademySlotId > 0 {
					isAcademy = true
				}
			}
			cartUser.FacilitySlots = facilitySlots
		}
		if member_json.UserId == json.UserId {
			parentUser = cartUser
		} else {
			childUsersToSubscribe = append(childUsersToSubscribe, cartUser)
		}
	}

	var cartResponse *productPB.CalculateCartResponse

	if isSummerCamp {
		cartResponse, _ = pcl.CalculateSummercampCart(ctx, &productPB.CalculateCartRequest{
			ChildUsers:        childUsersToSubscribe,
			ReferralCode:      "",
			PromoCode:         "",
			ParentUser:        parentUser,
			ProductCategoryId: common.SummerCampCategoryID,
		})
	} else if isAcademy {
		cartResponse, _ = pcl.CalculateAcademyCart(ctx, &productPB.CalculateCartRequest{
			ChildUsers:        childUsersToSubscribe,
			ReferralCode:      "",
			PromoCode:         "",
			ParentUser:        parentUser,
			ProductCategoryId: common.AcademyCategoryID,
		})
	} else {
		cartResponse, _ = pcl.CalculateCart(ctx, &productPB.CalculateCartRequest{
			ChildUsers:        childUsersToSubscribe,
			ReferralCode:      "",
			PromoCode:         "",
			ParentUser:        parentUser,
			ProductCategoryId: common.MasterkeyCategoryID,
		})
	}

	log.Println("GetEligibleVouchersC, cartResponse: ", cartResponse)

	eligibleVoucherReq := &productPB.GetEligibleVouchersReq{
		ProductDetails:     cartResponse.ProductDetails,
		UsersCount:         int32(len(cartResponse.FinalCartUsers)),
		IsDashboardRequest: true,
	}
	response, err := pcl.GetEligibleVouchers(ctx, eligibleVoucherReq)

	// session affinity based call
	// response, err := cl.GetEligibleVouchersForDashboardRequest(ctxb, receiptData)
	if err != nil {
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ActivateTrialC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = purchasePB.NewPurchaseService(serviceList.Purchase, service.Client())

	var json structs.ActivateTrial

	json = c.MustGet("jsonData").(structs.ActivateTrial)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	uid, _ := strconv.Atoi(json.UserId)
	var userId int32

	if headers.AppType == "sports-ios" || headers.AppType == "sports-android" {
		userId = int32(headers.UserId)
	} else {
		userId = int32(uid)
	}

	if json.CityId == 0 {
		json.CityId = 143
	}

	var user_id int32
	sharedFunc.DecodeData(json.UserEncodeVal, &user_id)

	// authentication for web users
	if (headers.AppType == "web" && headers.AppVersion == "1.0.2") && (user_id != int32(uid) || user_id == 0) {
		status := structs.Status{
			Status:  "failure",
			Message: "Unauthorized user",
		}

		response := structs.Ack{
			Status: status,
		}
		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": response}, "index.html")
	} else {

		// Check is trial available
		fmt.Println("GetTrialDetailsForUser Request -- ", json)

		clProduct = productPB.NewProductService(serviceList.Product, service.Client())

		upSubPb := &productPB.TrialDetailsRequest{
			UserId:     userId,
			CityId:     json.CityId,
			AppType:    headers.AppType,
			AppVersion: headers.AppVersion,
			Token:      headers.AccessToken,
		}

		responsePd, errPd := clProduct.GetTrialDetailsForUser(context.TODO(), upSubPb)

		if errPd != nil {
			fmt.Println(errPd)
			panic(errPd)
		}

		if !responsePd.IsAvailable || json.ProductKey != responsePd.Trials[0].ProductKey {

			statusCode := http.StatusOK

			if responsePd.Status != nil && responsePd.Status.Status == common.NOT_AUTHORIZED {
				statusCode = http.StatusUnauthorized
			}

			if responsePd.Status != nil && responsePd.Status.Status == common.BAD_REQUEST {
				statusCode = http.StatusBadRequest
			}

			c.JSON(statusCode, responsePd)
			return

		} else {

			var members_data []*purchasePB.MemberData

			psd := time.Now()
			planSD, _ := ptypes.TimestampProto(psd)

			member_data := &purchasePB.MemberData{
				UserId:        userId,
				PlanStartDate: planSD,
			}

			productId := responsePd.Trials[0].ProductId

			members_data = append(members_data, member_data)

			purDate := time.Now()
			purchaseDate, _ := ptypes.TimestampProto(purDate)

			receiptData := &purchasePB.ReceiptGenerationRequest{
				UserId:            userId,
				ProductId:         productId,
				Amount:            int32(0),
				PurchaseType:      int32(1),
				Provider:          purchasePB.PaymentProvider(1),
				Purpose:           "FSP-TRIAL-" + headers.AppType,
				TransactionNumber: "nil",
				CollectedBy:       "",
				PurchaseDate:      purchaseDate,
				MembersData:       members_data,
			}

			timeTillContextDeadline := time.Now().Add(10 * time.Second)
			ctxb, ctxCancelFuncb := context.WithDeadline(context.Background(), timeTillContextDeadline)
			defer ctxCancelFuncb()
			// session affinity based call
			response, err := cl.GenerateReceipt(ctxb, receiptData)

			if err != nil {
				fmt.Println(err)
				panic(err)
			}

			render.Render(c, gin.H{
				"title":   "Home Page",
				"payload": response}, "index.html")

		}

	}
}

func StoreAmazonGiftCardsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = purchasePB.NewPurchaseService(serviceList.Purchase, service.Client())

	var json structs.AmazonGiftCards
	json = c.MustGet("jsonData").(structs.AmazonGiftCards)

	reqData := &purchasePB.AmazonGiftCard{
		GiftCode: json.GiftCode,
		Amount:   json.Amount,
	}

	response, err := cl.StoreAmazonGiftCards(context.TODO(), reqData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetUnusedGiftCardsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = purchasePB.NewPurchaseService(serviceList.Purchase, service.Client())

	empty := &purchasePB.Empty{}

	response, err := cl.GetUnusedGiftCardCount(context.TODO(), empty)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func TestC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = purchasePB.NewPurchaseService(serviceList.Purchase, service.Client())

	empty := &purchasePB.Empty{}

	response, err := cl.Ping(context.TODO(), empty)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

// MakePurchaseC handles make order request
func MakePurchaseC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	json := c.MustGet("jsonData").(purchaseModel.MakePurchaseRequest)

	pcl := util.GetPurchaseClient()

	httpStatusCode := http.StatusOK

	var childUsersToSubscribe []*purchasePB.CartUser

	var parentUser *purchasePB.CartUser

	if json.ParentUser != nil {
		user := json.ParentUser
		parentUser = &purchasePB.CartUser{
			Phone:               user.Phone,
			PlanStartDate:       user.PlanStartDate,
			Name:                user.Name,
			PreferredFacilityId: user.PreferredFacilityId,
			PreferredSportId:    user.PreferredSportId,
			Age:                 user.Age,
			ProductId:           user.ProductId,
		}
	}

	for _, user := range json.ChildUsers {
		childUsersToSubscribe = append(childUsersToSubscribe, &purchasePB.CartUser{
			UserId:              user.UserId,
			Phone:               user.Phone,
			PlanStartDate:       user.PlanStartDate,
			Name:                user.Name,
			PreferredFacilityId: user.PreferredFacilityId,
			PreferredSportId:    user.PreferredSportId,
			Age:                 user.Age,
			ProductId:           user.ProductId,
		})
	}

	provider, er := pcl.GetSupportedPaymentProvider(ctx, &purchasePB.Empty{})
	if er != nil {
		log.Printf("func:MakePurchaseC, error in getting supported payment provider code, err:%v", er)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	var paymentMethodID int32

	if provider.PaymentProvider == 31 {
		paymentMethodIDFloat, ok := (json.PaymentMethodID).(float64)

		if !ok {
			paymentMethodIDString, ok := (json.PaymentMethodID).(string)

			paymentMethodIDInt, err := strconv.ParseInt(paymentMethodIDString, 10, 32)

			if !ok || err != nil {
				c.AbortWithStatusJSON(
					http.StatusBadRequest,
					models.StatusFailure("Invalid request!"),
				)
				return
			}

			paymentMethodID = int32(paymentMethodIDInt)
		} else {
			paymentMethodID = int32(paymentMethodIDFloat)
		}
	}

	var productId int32
	var err error
	if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		productId, err = util.GetInt32FromInterface(json.ProductId)
		if err != nil {
			log.Println(err.Error())
			c.JSON(
				http.StatusOK,
				models.StatusFailure("Something went wrong!"),
			)
			return
		}
	}

	if len(json.PromoCode) == 0 && !featuresupport.IsWebRequestV2(c) {
		pcl := util.GetProductClient()
		reqData := &productPB.Empty{}
		referralEligiblity, err := pcl.GetReferralEligiblity(ctx, reqData)
		if err == nil {
			if referralEligiblity.EligibleForReferral && referralEligiblity.ReferralUserId > 0 {
				refCodeData := &productPB.ReferralCodeGenerateOrGet{
					UserId: referralEligiblity.ReferralUserId,
				}
				referralCodeRes, err := pcl.GenerateOrGetReferralCode(ctx, refCodeData)
				if err != nil && referralCodeRes.Status.Status != common.SUCCESS && len(referralCodeRes.Code) == 0 {
					log.Printf("func:MakePurchaseC, error in getting referral code, err:%v", err)
					c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusUnauthorized(common.UNEXPECTED_ERROR))
					return
				}
				json.PromoCode = referralCodeRes.Code
			}
		}
	}

	purchaseRequest := &purchasePB.PurchaseInitiateRequest{
		ChildUsers:        childUsersToSubscribe,
		ReferralCode:      json.ReferralCode,
		ParentUser:        parentUser,
		PaymentMethodId:   paymentMethodID,
		PaymentMethodType: json.PaymentMethodType,
		PromoCode:         json.PromoCode,
		ProductId:         productId,
		ProductCategoryId: json.ProductCategoryId,
	}

	response, err := pcl.MakePurchase(ctx, purchaseRequest)

	if err != nil {
		log.Printf("error in calculating cart, %v", err)

		c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
		return
	}

	c.JSON(
		httpStatusCode,
		response,
	)
}

// MakeSubscriptionC handles make subscription request
func MakeSubscriptionC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	jsonData := c.MustGet("jsonData").(url.Values)

	pcl := util.GetPurchaseClient()

	purchaseIDString := jsonData.Get("order_id")

	purchaseID, err := strconv.ParseInt(purchaseIDString, 10, 32)

	if purchaseID == 0 || err != nil {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("Invalid request!"),
		)
		return
	}

	paymentStatus := jsonData.Get("event_data[status]")
	eventType := jsonData.Get("event_type")

	log.Print("[callback debug] event data payment status for purchase: ", paymentStatus)

	request := &purchasePB.AddPurchaseSubscription{
		PurchaseId:    int32(purchaseID),
		PaymentStatus: paymentStatus,
		EventType:     eventType,
	}

	response, err := pcl.MakeSubscriptionForPurchase(ctx, request)

	if err != nil {
		log.Printf("error in creating subscription for purchase: %d, error: %v", purchaseID, err)

		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	c.AbortWithStatusJSON(
		http.StatusOK,
		response,
	)
}

func PaymentCallbackC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	body := c.Request.Body
	x, err := ioutil.ReadAll(body)
	if err != nil {
		log.Println("func:PaymentCallbackC, error in reading body data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	var decoded map[string]interface{}
	if err := json.Unmarshal([]byte(string(x)), &decoded); err != nil {
		log.Println("func:PaymentCallbackC, error in unmarshalling: ", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	event := decoded["event"].(string)
	payload := decoded["payload"].(map[string]interface{})
	payment := payload["payment"].(map[string]interface{})
	entity := payment["entity"].(map[string]interface{})
	orderId := entity["order_id"].(string)
	razorpayPaymentId := entity["id"].(string)

	if len(orderId) == 0 || len(razorpayPaymentId) == 0 {
		log.Printf("func:PaymentCallbackC, invalid orderId:%s or razorpayPaymentId:%s ", orderId, razorpayPaymentId)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("something went wrong!"),
		)
		return
	}

	pcl := util.GetPurchaseClient()

	purchaseData := &purchasePB.PurchaseRequest{
		PaymentRequestToken: orderId,
	}
	purchaseResponse, err := pcl.PurchaseGet(ctx, purchaseData)
	if err != nil {
		log.Printf("func:PaymentCallbackC, error in getting purchase record  for order_id:%s, err: %v", orderId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	if len(purchaseResponse.Purchases) == 0 {
		log.Printf("func:PaymentCallbackC, purchase record not found for order_id:%s, ignore this callback", orderId)
		c.AbortWithStatusJSON(http.StatusOK, models.StatusSuccess("record not found, ignore the callback"))
		return
	}

	var paymentMethod, bank, wallet, errorCode, errorDescription string
	if entity["method"] != nil {
		paymentMethod = entity["method"].(string)
	}
	if entity["bank"] != nil {
		bank = entity["bank"].(string)
	}
	if entity["wallet"] != nil {
		wallet = entity["wallet"].(string)
	}
	if entity["error_code"] != nil {
		errorCode = entity["error_code"].(string)
	}
	if entity["error_description"] != nil {
		errorDescription = entity["error_description"].(string)
	}

	var amountCollected, fee, tax float64
	if entity["amount"] != nil {
		amountCollected = entity["amount"].(float64) / 100
	}
	if entity["fee"] != nil {
		fee = entity["fee"].(float64) / 100
	}
	if entity["tax"] != nil {
		tax = entity["tax"].(float64) / 100
	}
	amountCollectedFloat32 := float32(amountCollected)
	metaDataProto := &purchasePB.PurchaseMetaData{
		PaymentMethod:    paymentMethod,
		Bank:             bank,
		Wallet:           wallet,
		ErrorCode:        errorCode,
		ErrorDescription: errorDescription,
		Fee:              float32(fee),
		Tax:              float32(tax),
	}
	metaData, _ := json.Marshal(metaDataProto)
	metaDataStr := string(metaData)

	var paymentStatus string
	var finalAmountCollected float32
	if event == "payment.captured" {
		log.Println("decoded Success Razorpay response: ", decoded)
		paymentStatus = "success"
		finalAmountCollected = amountCollectedFloat32
	} else if event == "payment.failed" {
		paymentStatus = "failed"
	} else {
		log.Println("func:PaymentCallbackC, invalid event, aborting the process, event:", event)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("invalid event"),
		)
		return
	}

	request := &purchasePB.AddPurchaseSubscription{
		PurchaseId:        purchaseResponse.Purchases[0].PurchaseId,
		PaymentStatus:     paymentStatus,
		EventType:         "payment_status_update",
		RazorpayPaymentId: razorpayPaymentId,
		AmountCollected:   finalAmountCollected,
		MetaData:          metaDataStr,
	}

	response, err := pcl.MakeSubscriptionForPurchase(ctx, request)

	if err != nil {
		log.Printf("func:PaymentCallbackC, error in creating subscription for purchase: %d, error: %v", request.PurchaseId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	c.AbortWithStatusJSON(
		http.StatusOK,
		response,
	)
}

func SendUserMailsForPurchaseC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(purchaseModel.SendMailForPurchaseRequest)

	pcl := util.GetPurchaseClient()

	request := &purchasePB.PurchaseRequest{
		PurchaseId: json.PurchaseId,
	}

	response, err := pcl.SendMailsForPurchase(ctx, request)

	if err != nil {
		log.Printf("error in sending welcome and receipt mails for purchase: %d, error: %v", json.PurchaseId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	c.AbortWithStatusJSON(
		http.StatusOK,
		response,
	)

}

func SendTaxInvoiceC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.SendTaxInvoice)

	pcl := util.GetPurchaseClient()
	purchaseDetailsRequest := purchasePB.GetPurchaseDetailsFromSubscriptionIdRequest{
		SubscriptionId: json.SubscriptionId,
	}

	purchaseDetails, err := pcl.GetPurchaseDetailsFromSubscriptionId(ctx, &purchaseDetailsRequest)
	if err != nil {
		log.Printf("error in finding purchaseId for subscription_id: %d in sending tax invoice , error: %v", json.SubscriptionId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}
	request := &purchasePB.PurchaseRequest{
		PurchaseId: purchaseDetails.Purchase.PurchaseId,
	}

	response, err := pcl.SendTaxInvoice(ctx, request)
	if err != nil {
		log.Printf("error in sending tax invoice for purchase_id: %d, error: %v", purchaseDetails.Purchase.PurchaseId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	c.AbortWithStatusJSON(
		http.StatusOK,
		response,
	)
}
