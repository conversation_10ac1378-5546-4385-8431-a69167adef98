package purchaseController

import (
	"encoding/json"
	"io/ioutil"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	model "bitbucket.org/jogocoin/go_api/api/models"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	"bitbucket.org/jogocoin/go_api/api/render"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	structs "bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// CancelMembershipC is the controller layer which raised the cancel membership request and refund from dashboard
func CancelMembershipC(c *gin.Context) {
	cl := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)

	loggedInUser := util.GetUserIDFromContext(c)
	if loggedInUser == 0 {
		log.Println("[CancelMembershipC], LoggedInUserId is 0")
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	reqData := prepareCancellationMembershipRequest(c)

	response, err := cl.CancelMembership(ctx, reqData)
	if err != nil {
		log.Println("Could not create User Query for Cancel Membership and refund from dashboard---", err)
		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": err}, "index.html")
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
	return
}

func prepareCancellationMembershipRequest(c *gin.Context) *productPB.CancelMembershipReq {

	var json structs.CancelMembershipReq
	json = c.MustGet("jsonData").(structs.CancelMembershipReq)

	var subscription_ids []int32
	for _, membershipInfo := range json.MembershipsInfo {
		subscription_ids = append(subscription_ids, membershipInfo.SubscriptionId)
	}
	subscription_ids = util.DeduplicateSlice(subscription_ids)

	var membershipsInfo []*productPB.MembershipInfo
	for _, subscription_id := range subscription_ids {
		membershipObj := &productPB.MembershipInfo{
			SubscriptionId: subscription_id,
		}
		membershipsInfo = append(membershipsInfo, membershipObj)
	}

	var cancellation_reason_ids []int32
	for _, reason := range json.CancellationReasons {
		cancellation_reason_ids = append(cancellation_reason_ids, reason.ReasonId)
	}

	cancellation_reason_ids = util.DeduplicateSlice(cancellation_reason_ids)

	return &productPB.CancelMembershipReq{
		MembershipsInfo:       membershipsInfo,
		CancellationReasonIds: cancellation_reason_ids,
		PaymentId:             json.PaymentId, //the razorpay paymnet id
		RefundAmount:          json.RefundAmount,
		IsDashboardRequest:    true,
	}
}

func RefundCallbackC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	body := c.Request.Body
	x, err := ioutil.ReadAll(body)
	if err != nil {
		log.Println("func:RefundCallbackC, error in reading body data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

	var decoded map[string]interface{}
	if err := json.Unmarshal([]byte(string(x)), &decoded); err != nil {
		log.Println("func:PaymentCallbackC, error in unmarshalling: ", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}
	log.Println("func:RefundCallbackC, Razorpay Object:", string(x))

	event := decoded["event"].(string)
	payload := decoded["payload"].(map[string]interface{})
	refund := payload["refund"].(map[string]interface{})
	entity := refund["entity"].(map[string]interface{})
	razorpayRefundReferenceId := entity["id"].(string)
	acquirerData := entity["acquirer_data"].(map[string]interface{})
	rnNumber := ""
	if acquirerData["arn"] != nil {
		rnNumber = acquirerData["arn"].(string)
	}else if acquirerData["rrn"] != nil {
		rnNumber = acquirerData["rrn"].(string)
	}
	completedAtFloat := entity["created_at"].(float64)

	completedAt := int64(completedAtFloat)
	if len(razorpayRefundReferenceId) == 0 {
		log.Printf("func:RefundCallbackC, invalid razorpayRefundReferenceId:%s ", razorpayRefundReferenceId)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure("something went wrong!"),
		)
		return
	}

	pcl := util.GetPurchaseClient()

	refundStatus := identifyRefundStatusFromString(event)
	if refundStatus == purchasePB.RefundStatus_REFUND_UNKNOWN {
		log.Printf("func:RefundCallbackC, invalid razorpay event: %v", event)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("method not allowed"),
		)
		return
	}
	refundData := &purchasePB.UpdateRefundStatusRequest{
		ReferenceId: razorpayRefundReferenceId,
		RefundEvent: refundStatus,
		CompletedAt: completedAt,
		RrnNumber:   rnNumber,
	}
	if _, err := pcl.UpdateRefundFromRazorpayTransactionStatus(ctx, refundData); err != nil {
		log.Printf("func:PaymentCallbackC, error in updating refund record  for razorpayRefundReferenceId:%s, err: %v", razorpayRefundReferenceId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}

}

func identifyRefundStatusFromString(event string) purchasePB.RefundStatus {
	if event == "refund.created" {
		return purchasePB.RefundStatus_CREATED
	} else if event == "refund.processed" {
		return purchasePB.RefundStatus_PROCESSED
	} else if event == "refund.failed" {
		return purchasePB.RefundStatus_FAILED
	} else {
		return purchasePB.RefundStatus_REFUND_UNKNOWN
	}
}

func GetRefundAmountC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	loggedInUser := util.GetUserIDFromContext(c)
	if loggedInUser == 0 {
		log.Println("func GetRefundAmountC: Invalid user")
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}
	json := c.MustGet("jsonData").(structs.Subscription)
	pcl := util.GetPurchaseClient()

	getRefundRequest := &purchasePB.GetPurchaseDetailsFromSubscriptionIdRequest{
		SubscriptionId: json.SubscriptionID,
	}
	response, err := pcl.GetRefundAmount(ctx, getRefundRequest)
	if err != nil {
		log.Printf("func:GetRefundAmountC, error in getting refund record  for subscriptionId: %d, err: %v", json.SubscriptionID, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure("something went wrong!"))
		return
	}
	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}
