package purchaseController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	purchaseModel "bitbucket.org/jogocoin/go_api/api/models/purchase"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type PostbackParams struct {
	TotalPollTime int32 `json:"total_poll_time"`
}

const (
	completePaymentStatus = 2
	failedPaymentStatus   = 3
	pendingPaymentStatus  = 4
)

// PurchaseStatusC handles make subscription request
func PurchaseStatusC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(purchaseModel.PurchaseStatusRequest)

	pcl := util.GetPurchaseClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)

	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You are not authorized to view this data!"),
		)
		return
	}
	purchaseID, err := strconv.ParseInt(json.PurchaseId, 10, 32)

	if err != nil || purchaseID == 0 {
		log.Printf("Error PurchaseStatusC error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("Invalid request!"),
		)
		return
	}

	var apiResponse purchaseModel.PurchaseStatusResponse

	request := &purchasePB.PurchaseStatusRequest{
		PurchaseId:    int32(purchaseID),
		TotalPollTime: getTotalPolltime(json.PostbackParams),
	}
	response, err := pcl.GetPurchasePaymentStatus(ctx, request)
	log.Println(response)
	if err != nil {
		log.Printf("error in getting payment status for purchase, %d: %v", int32(purchaseID), err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("Invalid request!"),
		)
		return
	} else if response.Status.Status != common.SUCCESS {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			response.Status,
		)
		return
	} else {
		var actionListItem, actionListItem2 *purchaseModel.ActionListItem = nil, nil
		var actionList []*purchaseModel.ActionListItem
		imageAnimation, _ := sushi.NewAnimation(response.ImageAnimation)
		image, _ := sushi.NewImageWithAnimation(imageAnimation)
		trackingPayload := GetCommonPayloadForTracking(ctx)
		trackingPayload["purchase_id"] = purchaseID
		impressionEname := "purchase_status_response"
		if isProductCategoryAcademy(response.ProductCategoryId) {
			impressionEname = "academy_purchase_status_response"
		} else if isProductCategorySummercamp(response.ProductCategoryId) {
			impressionEname = "summercamp_purchase_status_response"
		}
		impressionEnameData := &sushi.EnameData{
			Ename: impressionEname,
		}
		purchaseStatusEvent := sushi.NewClevertapEvents()
		purchaseStatusEvent.SetImpression(impressionEnameData)

		appsflyerImpressionEname := &sushi.EnameData{
			Ename: "masterkey_purchase",
		}
		if isProductCategoryAcademy(response.ProductCategoryId) {
			appsflyerImpressionEname.Ename = "academy_purchase"
		} else if isProductCategorySummercamp(response.ProductCategoryId) {
			appsflyerImpressionEname.Ename = "summercamp_purchase"
		}
		cityID := util.GetCityIDFromContext(ctx)
		preferredSportRequest := &purchasePB.GetPreferredSportFromCachedPurchaseDataReq{
			PurchaseId: int32(purchaseID),
		}
		preferredSport, err := pcl.GetPreferredSportFromCachedPurchaseData(ctx, preferredSportRequest)
		if err != nil {
			log.Printf("PurchaseStatusC, error in getting preferred sport details for purchase id, %d", int32(purchaseID))
			c.AbortWithStatusJSON(
				http.StatusBadRequest,
				models.StatusFailure("Something Unexpected Happened!"),
			)
			return
		}
		generalAppsflyerImpressionEname := &sushi.EnameData {
			Ename: appsflyerImpressionEname.Ename,
		}
		generalAppsflyerPurchaseStatusEvent := sushi.NewAppsflyerEvents()
		generalAppsflyerPurchaseStatusEvent.SetImpression(generalAppsflyerImpressionEname)
		if preferredSport.SportId > 0 {
			sportName := common.SportIdSportNameMap[preferredSport.SportId]
			if cityID > 0 {
				cityName := common.CityIdCityNameMap[cityID]
				appsflyerImpressionEname.Ename = fmt.Sprintf("%s_%s_%s", sportName, cityName, appsflyerImpressionEname.Ename)
			} else {
				appsflyerImpressionEname.Ename = fmt.Sprintf("%s_%s", sportName, appsflyerImpressionEname.Ename)
			}
		} else if len(preferredSport.SportName) > 0 {
			sportName := strings.ReplaceAll(preferredSport.SportName, " ", "_")
			if cityID > 0 {
				cityName := common.CityIdCityNameMap[cityID]
				appsflyerImpressionEname.Ename = fmt.Sprintf("%s_%s_%s", sportName, cityName, appsflyerImpressionEname.Ename)
			} else {
				appsflyerImpressionEname.Ename = fmt.Sprintf("%s_%s", sportName, appsflyerImpressionEname.Ename)
			}
		}
		appsflyerPurchaseStatusEvent := sushi.NewAppsflyerEvents()
		appsflyerPurchaseStatusEvent.SetImpression(appsflyerImpressionEname)

		switch response.PaymentStatus {
		case completePaymentStatus:
			title, _ := sushi.NewTextSnippet(response.Title)
			titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			title.SetFont(titleFont)
			title.SetColor(titleColor)

			positiveClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			if featuresupport.SupportsPurchaseCompletionSnippet(c) {
				positiveClickOpenPaymentCompletionPage := openPaymentCompletionPage(ctx, purchaseID, response.SuccessData)
				positiveClickAction.SetOpenPaymentCompletionPage(positiveClickOpenPaymentCompletionPage)
			} else {
				positiveClickDeeplink := &sushi.Deeplink{
					URL: response.SuccessDeeplink,
				}
				positiveClickAction.SetDeeplink(positiveClickDeeplink)
			}

			trackingPayload["payment_status"] = "success"
			trackItem := sushi.GetClevertapTrackItem(ctx, trackingPayload, purchaseStatusEvent)
			appsflyerTrackItem := sushi.GetAppsflyerTrackItem(ctx, appsflyerPurchaseStatusEvent)
			generalAppsflyerTrackItem := sushi.GetAppsflyerTrackItem(ctx, generalAppsflyerPurchaseStatusEvent)
			appslyerTrackPayload := make(map[string]interface{})
			appslyerTrackPayload["af_customer_user_id"] = loggedInUserId
			appsflyerTrackItem.SetPayload(appslyerTrackPayload)
			generalAppsflyerTrackItem.SetPayload(appslyerTrackPayload)

			jumboTrackItem := getPurchaseStatusResponseJumboTrackItem(purchaseID, "success")

			actionListItem = &purchaseModel.ActionListItem{
				Type: "buy_membership_dismiss",
				BuyMembershipDismiss: &purchaseModel.ActionListItemSnippet{
					Type: sushi.ActionTypeCustomAlert,
					CustomAlert: &sushi.CustomAlert{
						Title:      title,
						IsBlocking: true,
						PositiveAction: &sushi.Button{
							ClickAction: positiveClickAction,
						},
						Image: image,
						AutoDismissData: &sushi.AutoDismissData{
							Time:              3,
							DismissActionType: sushi.ActionTypePositive,
						},
						ClevertapTracking: []*sushi.ClevertapItem{trackItem},
						AppsflyerTracking: []*sushi.AppsflyerItem{generalAppsflyerTrackItem},
						JumboTracking:     []*jumbo.Item{jumboTrackItem},
						DismissAfterAction: true,
					},
				},
			}
			if featuresupport.SupportsAcademyPurchaseFlow(ctx) {
				value := common.PAGE_TYPE_MASTERKEY
				if isProductCategoryAcademy(response.SuccessData.ProductCategoryId) {
					value = common.PAGE_TYPE_ACADEMY
				} else if isProductCategorySummercamp(response.SuccessData.ProductCategoryId) {
					value = common.PAGE_TYPE_SUMMER_CAMP
				}
				saveKeyItem := &sushi.SaveKeyItem{
					Key:   "product_type",
					Value: value,
				}
				saveKey := &sushi.SaveKey{
					Items: []*sushi.SaveKeyItem{saveKeyItem},
				}
				actionListItem2 = &purchaseModel.ActionListItem{
					Type:    sushi.ClickActionSaveKey,
					SaveKey: saveKey,
				}
			}

		case failedPaymentStatus:
			title, _ := sushi.NewTextSnippet(response.Title)
			titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			title.SetFont(titleFont)
			title.SetColor(titleColor)

			message, _ := sushi.NewTextSnippet(response.Message)
			messageFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
			message.SetFont(messageFont)

			trackingPayload["payment_status"] = "failed"
			trackItem := sushi.GetClevertapTrackItem(ctx, trackingPayload, purchaseStatusEvent)

			jumboTrackItem := getPurchaseStatusResponseJumboTrackItem(purchaseID, "failed")

			actionListItem = &purchaseModel.ActionListItem{
				Type: sushi.ActionTypeCustomAlert,
				CustomAlert: &sushi.CustomAlert{
					Title:   title,
					Message: message,
					Image:   image,
					AutoDismissData: &sushi.AutoDismissData{
						Time: 4,
					},
					ClevertapTracking: []*sushi.ClevertapItem{trackItem},
					JumboTracking:     []*jumbo.Item{jumboTrackItem},
					DismissAfterAction: true,
				},
			}

		case pendingPaymentStatus:
			title, _ := sushi.NewTextSnippet(response.Title)
			titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
			title.SetFont(titleFont)

			subtitle, _ := sushi.NewTextSnippet(response.Subtitle)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			subtitle.SetFont(subtitleFont)
			subtitle.SetColor(subtitleColor)

			bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)

			cartHeader := &sushi.CartPendingPaymentHeader{
				Title:    title,
				Subtitle: subtitle,
				BgColor:  bgColor,
			}

			cartTitle, _ := sushi.NewTextSnippet(response.Message)
			cartTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
			cartTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			cartTitle.SetFont(cartTitleFont)
			cartTitle.SetColor(cartTitleColor)
			cartTitle.SetIsMarkdown(1)

			cartContainer := &sushi.CartPendingPaymentContainer{
				Title: cartTitle,
			}

			cartPendingData := &sushi.CartPendingPayment{
				Header:    cartHeader,
				Container: cartContainer,
			}

			actionListItem = &purchaseModel.ActionListItem{
				Type:               sushi.ActionTypeCartPendingpPaymentData,
				CartPendingPayment: cartPendingData,
			}
		}

		if actionListItem2 != nil {
			actionList = append(actionList, actionListItem2)
		}
		if actionListItem != nil {
			actionList = append(actionList, actionListItem)
		}
		apiResponse = purchaseModel.PurchaseStatusResponse{
			Status:         common.SUCCESS,
			PaymentStatus:  response.PaymentStatus,
			ActionList:     actionList,
			PollInterval:   response.PollInterval,
			PostbackParams: calculatePostbackParams(response.TotalPollTime),
		}
	}

	c.JSON(
		http.StatusOK,
		apiResponse,
	)
}

func openPaymentCompletionPage(ctx context.Context, purchaseID int64, successData *purchasePB.PurchaseSuccessResponse) *sushi.OpenPaymentCompletionPage {
	bgImage, _ := sushi.NewImage(successData.HeaderBackground)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	buttonColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button.SetColor(buttonColor)
	button.SetText("Close")

	positiveClickOpenPaymentCompletionPage := &sushi.OpenPaymentCompletionPage{
		BgImage: bgImage,
		Header: &sushi.OpenPaymentCompletionPageHeader{
			Button: button,
		},
		Results: setOpenPaymentCompletionPageResults(ctx, successData),
	}
	if successData.ProductCategoryId != common.AcademyCategoryID && successData.ProductCategoryId != common.SummerCampCategoryID {
		positiveClickOpenPaymentCompletionPage.Footer = setOpenPaymentCompletionPageFooter(ctx, purchaseID, successData)
	}
	return positiveClickOpenPaymentCompletionPage
}

func setOpenPaymentCompletionPageFooter(
	ctx context.Context,
	purchaseID int64,
	successData *purchasePB.PurchaseSuccessResponse,
) *sushi.FooterSnippetType2Layout {
	if !featuresupport.SupportsMedicalForm(ctx) {
		return nil
	}

	loggedInUserId := util.GetUserIDFromContext(ctx)
	usersToFillInfo := getUsersNeedToFillMedicalInfo(ctx, successData)
	if len(usersToFillInfo) == 0 {
		return nil
	}

	deeplinkUrl := util.GetMedicalInfoUserListDeeplink()
	payload := make(map[string]interface{})
	payload["page_type"] = common.PAGE_TYPE_PURCHASE_SUCCESS

	if len(usersToFillInfo) == 1 && usersToFillInfo[0] == loggedInUserId {
		deeplinkUrl = util.GetMedicalDetailsDeeplink(loggedInUserId)
		payload["user_id"] = loggedInUserId
	} else {
		payload["purchase_id"] = purchaseID
	}
	postbackParams, _ := json.Marshal(payload)

	dismissPage := &sushi.DismissPage{
		Type: sushi.ClickActionDeeplink,
		Deeplink: &sushi.Deeplink{
			URL:            deeplinkUrl,
			PostbackParams: string(postbackParams),
		},
	}

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Size: sushi.FooterButtonSizeLarge,
		Text: "Update your safety information",
		ClickAction: &sushi.ClickAction{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismissPage,
		},
	}
	items = append(items, buttonItem)
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: &sushi.FooterSnippetType2Button{
			Items:       &items,
			Orientation: sushi.FooterButtonOrientationVertical,
		},
	}

	return &sushi.FooterSnippetType2Layout{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType: sushi.FooterSnippetType2,
		},
		FooterSnippetType2: snippet,
	}
}

func getUsersNeedToFillMedicalInfo(ctx context.Context, successData *purchasePB.PurchaseSuccessResponse) []int32 {
	userIds := make([]int32, 0)
	for _, v := range successData.MemberData {
		userIds = append(userIds, v.UserId)
	}

	finalUserIds := make([]int32, 0)

	userClient := util.GetUserServiceClient()
	medicalReq := &userPB.GetUsersMedicalDetailStatusRequest{
		UserIds: userIds,
	}
	medicalRes, err := userClient.BatchGetUserMedicalDetailStatus(ctx, medicalReq)
	if err != nil {
		log.Printf("Unable to get medical info for users, Error: %v", err)
		return finalUserIds
	}

	for userId, status := range medicalRes.Status {
		if status == 0 {
			finalUserIds = append(finalUserIds, userId)
		}
	}
	return finalUserIds
}

func setOpenPaymentCompletionPageResults(ctx context.Context, successData *purchasePB.PurchaseSuccessResponse) []*sushi.OpenPaymentCompletionPageResultSection {
	results := make([]*sushi.OpenPaymentCompletionPageResultSection, 0)
	results = append(results, fitsoHeaderSnippetType1Result(ctx, successData))
	results = append(results, fitsoImageTextSnippetType8Result(ctx, successData))
	return results
}

func fitsoHeaderSnippetType1Result(ctx context.Context, successData *purchasePB.PurchaseSuccessResponse) *sushi.OpenPaymentCompletionPageResultSection {

	semiBold500Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)

	image, _ := sushi.NewImage(successData.Logo)
	image.SetAspectRatio(2.21)

	titleHeading := successData.ProductDescription
	if successData.ProductCategoryId == common.SummerCampCategoryID {
		titleHeading = "CULT SUMMER CAMP"
	}

	title, _ := sushi.NewTextSnippet(titleHeading)
	title.SetFont(semiBold500Font)
	title.SetKerning(1)

	fitsoHeaderSnippetType1 := &sushi.FitsoHeaderSnippetType1Snippet{
		Title: title,
		Image: image,
	}

	fitsoHeaderSnippetType1Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoHeaderSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	return &sushi.OpenPaymentCompletionPageResultSection{
		LayoutConfig:            fitsoHeaderSnippetType1Layout,
		FitsoHeaderSnippetType1: fitsoHeaderSnippetType1,
	}
}

func fitsoImageTextSnippetType8Result(ctx context.Context, successData *purchasePB.PurchaseSuccessResponse) *sushi.OpenPaymentCompletionPageResultSection {

	semiBold500Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	semiBold300Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	medium100Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	medium300Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	medium400Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	regular200Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	loggedInUserId := util.GetUserIDFromContext(ctx)

	topContainerImage, _ := sushi.NewImage(util.GetCDNLink("uploads/Crown1625815419.png"))
	topContainerImage.SetWidth(78)
	topContainerImage.SetHeight(69)

	userNames := []string{}
	memberDataLength := len(successData.MemberData)
	isLoggedInUserMember := false
	for _, v := range successData.MemberData {
		if loggedInUserId == v.UserId {
			isLoggedInUserMember = true
			continue
		}
		userNames = append(userNames, v.UserName)
	}

	if isLoggedInUserMember {
		userNames = append([]string{"You"}, userNames...)
	}
	if len(userNames) > 2 {
		userNames = userNames[0:2]
	}

	nameText := "Congratulations!\n"
	if memberDataLength == 1 {
		if loggedInUserId == successData.MemberData[0].UserId {
			nameText = nameText + "You are now cult member"
		} else {
			nameText = nameText + successData.MemberData[0].UserName + " is now cult member"
		}

	} else if memberDataLength > 1 {
		nameText = nameText + strings.Join(userNames, ", ")
		if memberDataLength > 2 {
			nameText = nameText + " + " + fmt.Sprintf("%d more are now cult members", memberDataLength-2)
		} else {
			nameText = nameText + " are now cult members"
		}
	}

	topContainerTitle, _ := sushi.NewTextSnippet(nameText)
	topContainerTitle.SetFont(semiBold500Font)
	topContainerTitle.SetAlignment(sushi.TextAlignmentCenter)

	topContainer := &sushi.FitsoImageTextSnippetType8TopContainer{
		Image: topContainerImage,
		Title: topContainerTitle,
	}
	if successData.ProductCategoryId == common.AcademyCategoryID && len(successData.MemberData) == 1 {
		title, _ := sushi.NewTextSnippet(successData.MemberData[0].CourseName)
		title.SetFont(medium400Font)
		title.SetKerning(2)

		tagTitle, _ := sushi.NewTextSnippet(successData.MemberData[0].CourseCategoryName)
		tagBgColor, _ := sushi.NewColor(sushi.ColorType(successData.MemberData[0].CourseCategoryBgColor), sushi.ColorTint400)
		tagBorderColor, _ := sushi.NewColor(sushi.ColorType(successData.MemberData[0].CourseCategoryBgColor), sushi.ColorTint400)
		tagtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagTitle.SetColor(tagtitleColor)

		tagContainer := &sushi.Tag{
			Title:       tagTitle,
			BgColor:     tagBgColor,
			BorderColor: tagBorderColor,
		}

		var textSnippets []*sushi.TextSnippet
		for _, facilityName := range successData.MemberData[0].FacilityNames {
			title, _ := sushi.NewTextSnippet(facilityName)
			title.SetFont(medium100Font)
			textSnippets = append(textSnippets, title)
		}

		detailsContainer := &sushi.DetailsContainer{
			Title: title,
			Tag:   tagContainer,
			Items: textSnippets,
		}
		topContainer.DetailsContainer = detailsContainer
	} else if successData.ProductCategoryId == common.SummerCampCategoryID && len(successData.MemberData) == 1 {
		title, _ := sushi.NewTextSnippet(successData.MemberData[0].SportName)
		title.SetFont(medium400Font)
		title.SetKerning(2)

		tagTitle, _ := sushi.NewTextSnippet("CULT SUMMER CAMP")
		tagBgColor, _ := sushi.NewColor(sushi.ColorType(sushi.ColorTypeTeal), sushi.ColorTint500)
		tagBorderColor, _ := sushi.NewColor(sushi.ColorType(sushi.ColorTypeTeal), sushi.ColorTint500)
		tagtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagTitle.SetColor(tagtitleColor)

		tagContainer := &sushi.Tag{
			Title:       tagTitle,
			BgColor:     tagBgColor,
			BorderColor: tagBorderColor,
		}

		var textSnippets []*sushi.TextSnippet
		for _, facilityName := range successData.MemberData[0].FacilityNames {
			title, _ := sushi.NewTextSnippet(facilityName)
			title.SetFont(medium100Font)
			t := &sushi.TextSnippet{
				Title: title,
			}
			textSnippets = append(textSnippets, t)
		}

		detailsContainer := &sushi.DetailsContainer{
			Title: title,
			Tag:   tagContainer,
			Items: textSnippets,
		}
		topContainer.DetailsContainer = detailsContainer
	} else if successData.ProductCategoryId != common.AcademyCategoryID && successData.ProductCategoryId != common.SummerCampCategoryID {
		tagTitle, _ := sushi.NewTextSnippet("Valid in <semibold-200|{black-500|" + successData.City + "}> only")
		tagTitle.SetIsMarkdown(1)
		tagTitle.SetFont(regular200Font)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
		topContainerTag := &sushi.Tag{
			Title:   tagTitle,
			BgColor: tagBgColor,
		}
		topContainer.Tag = topContainerTag
	}

	if memberDataLength == 1 {
		topContainer.TimelineData = timelineData(ctx, successData)
	} else if memberDataLength > 1 {
		topContainerSubtitle, _ := sushi.NewTextSnippet("You can view all details under “Your Membership” section")
		topContainerSubtitle.SetFont(medium300Font)
		topContainer.Subtitle = topContainerSubtitle
	}

	bottomContainerImage, _ := sushi.NewImage(util.GetCDNLink("uploads/DoubleQuotes1625815442.png"))
	bottomContainerImage.SetWidth(28)
	bottomContainerImage.SetHeight(28)

	bottomContainerTitle, _ := sushi.NewTextSnippet("Welcome to a happier and healthier life")
	bottomContainerTitle.SetFont(semiBold300Font)
	bottomContainerTitle.SetAlignment(sushi.TextAlignmentCenter)

	bottomContainerSubtitle, _ := sushi.NewTextSnippet("Enjoy unlimited access to exciting sports and certified coaching at our premium facilities")
	if successData.ProductCategoryId == common.AcademyCategoryID || successData.ProductCategoryId == common.SummerCampCategoryID {
		bottomContainerSubtitle, _ = sushi.NewTextSnippet("Unleash your potential and surprise yourself as you grow with every session")
	}
	bottomContainerSubtitle.SetFont(medium300Font)
	bottomContainerSubtitle.SetAlignment(sushi.TextAlignmentCenter)

	bottomContainer := &sushi.FitsoImageTextSnippetType8BottomContainer{
		Image:    bottomContainerImage,
		Title:    bottomContainerTitle,
		Subtitle: bottomContainerSubtitle,
	}
	fitsoImageTextSnippetType8 := &sushi.FitsoImageTextSnippetType8Snippet{
		TopContainer:    topContainer,
		BottomContainer: bottomContainer,
	}

	fitsoImageTextSnippetType8Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType8,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	return &sushi.OpenPaymentCompletionPageResultSection{
		LayoutConfig:               fitsoImageTextSnippetType8Layout,
		FitsoImageTextSnippetType8: fitsoImageTextSnippetType8,
	}
}

func timelineData(ctx context.Context, successData *purchasePB.PurchaseSuccessResponse) *sushi.FitsoImageTextSnippetType8TopContainerTimelineData {

	medium300Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	regular100Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)

	timelineDataTitle, _ := sushi.NewTextSnippet("From")
	timelineDataTitle.SetFont(regular100Font)

	timelineDataSubtitle, _ := sushi.NewTextSnippet(formatDateString(successData.MemberData[0].PlanStartDate.Seconds))
	timelineDataSubtitle.SetFont(medium300Font)

	timelineDataRightTitle, _ := sushi.NewTextSnippet("Till")
	timelineDataRightTitle.SetFont(regular100Font)

	timelineDataRightSubtitle, _ := sushi.NewTextSnippet(formatDateString(successData.MemberData[0].PlanEndDate.Seconds))
	timelineDataRightSubtitle.SetFont(medium300Font)

	return &sushi.FitsoImageTextSnippetType8TopContainerTimelineData{
		Title:         timelineDataTitle,
		Subtitle:      timelineDataSubtitle,
		RightTitle:    timelineDataRightTitle,
		RightSubtitle: timelineDataRightSubtitle,
	}
}

func GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	return commonPayload
}

func getTotalPolltime(postbackParams string) int32 {
	postbackParamsObj := PostbackParams{}

	err := json.Unmarshal([]byte(postbackParams), &postbackParamsObj)

	if err != nil {
		log.Printf("[purchase status] error in decoding postback %s with params: %v", postbackParams, err)
	}

	return postbackParamsObj.TotalPollTime
}

func calculatePostbackParams(totalPollTime int32) string {
	postbackParams := PostbackParams{
		TotalPollTime: totalPollTime,
	}

	encodedPostbackParams, err := json.Marshal(postbackParams)

	if err != nil {
		log.Printf("[purchase status] error in encoding postback %v with params: %v", postbackParams, err)
		return ""
	}

	return string(encodedPostbackParams)
}

func getPurchaseStatusResponseJumboTrackItem(purchaseId int64, status string) *jumbo.Item {
	jumboPayload := make(map[string]interface{})
	jumboPayload["purchase_id"] = purchaseId
	jumboPayload["source_page"] = "buy_membership"
	jumboPayload["status"] = status
	jumboEvents := jumbo.NewEvents()
	jumboPurchaseStatusEvent := jumbo.GetEventNameObject(jumbo.PurchaseStatusResponse)
	jumboEvents.SetImpression(jumboPurchaseStatusEvent)
	jumboTrackItem := jumbo.GetJumboTrackItem(jumboPayload, jumboEvents, jumbo.PurchaseEventsTableName)

	return jumboTrackItem
}

func isProductCategoryAcademy(productCategoryId int32) bool {
	if productCategoryId == common.AcademyCategoryID {
		return true
	}
	return false
}

func isProductCategorySummercamp(productCategoryId int32) bool {
	if productCategoryId == common.SummerCampCategoryID {
		return true
	}
	return false
}

// PurchasePaymentStatusC handles make subscription request
func PurchasePaymentStatusC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(purchaseModel.PurchaseStatusRequest)

	pcl := util.GetPurchaseClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)

	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You are not authorized to view this data!"),
		)
		return
	}
	purchaseID, err := strconv.ParseInt(json.PurchaseId, 10, 32)

	if err != nil || purchaseID == 0 {
		log.Printf("Error PurchaseStatusC error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("Invalid request!"),
		)
		return
	}

	var apiResponse purchaseModel.PurchasePaymentStatusResponse

	request := &purchasePB.PurchaseStatusRequest{
		PurchaseId:    int32(purchaseID),
		TotalPollTime: getTotalPolltime(json.PostbackParams),
	}
	response, err := pcl.GetPurchasePaymentStatus(ctx, request)
	if err != nil {
		log.Printf("func PurchasePaymentStatusC: error in getting payment status for web purchase, %d - err : %v", int32(purchaseID), err)
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			models.StatusFailure("Invalid request!"),
		)
		return
	} else if response.Status.Status != common.SUCCESS {
		c.AbortWithStatusJSON(
			http.StatusBadRequest,
			response.Status,
		)
		return
	} else {
		apiResponse = purchaseModel.PurchasePaymentStatusResponse{
			Status:         common.SUCCESS,
			PaymentStatus:  response.PaymentStatus,
			PollInterval:   response.PollInterval,
			Title:          response.Title,
			Subtitle:       response.Subtitle,
			Message:        response.Message,
			PostbackParams: calculatePostbackParams(response.TotalPollTime),
		}
	}

	c.JSON(
		http.StatusOK,
		apiResponse,
	)
}
