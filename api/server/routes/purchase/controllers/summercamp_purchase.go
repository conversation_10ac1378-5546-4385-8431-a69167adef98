package purchaseController

import (
	"log"
	"net/http"
	"strconv"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	structs "bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func MakeSummercampPurchaseC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.MakeAcademyPurchaseRequest)
	purchaseClient := util.GetPurchaseClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		c.JSON(http.StatusUnauthorized, models.StatusFailure("You need to login to perform this action"))
		return
	}

	var parentUser *purchasePB.CartUser
	if json.ParentUser != nil {
		user := json.ParentUser
		parentUser = &purchasePB.CartUser{
			UserId:        user.UserId,
			Name:          user.Name,
			PlanStartDate: user.PlanStartDate,
			ProductId:     user.ProductId,
		}
		var facilitySlots []*purchasePB.FacilitySlot
		for _, facilitySlot := range user.FacilitySlots {
			facilitySlots = append(facilitySlots, &purchasePB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			parentUser.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not selected for logged in user"))
			return
		}
	}

	var childUsers []*purchasePB.CartUser
	for _, user := range json.ChildUsers {
		childUser := &purchasePB.CartUser{
			UserId:        user.UserId,
			Name:          user.Name,
			ProductId:     user.ProductId,
			PlanStartDate: user.PlanStartDate,
		}
		var facilitySlots []*purchasePB.FacilitySlot
		for _, facilitySlot := range user.FacilitySlots {
			facilitySlots = append(facilitySlots, &purchasePB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			childUser.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not selected for a child user"))
			return
		}
		childUsers = append(childUsers, childUser)
	}

	provider, er := purchaseClient.GetSupportedPaymentProvider(ctx, &purchasePB.Empty{})
	if er != nil {
		log.Printf("func:MakePurchaseC, error in getting referral code, err:%v", er)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusUnauthorized(apiCommon.UNEXPECTED_ERROR),
		)
		return
	}

	var paymentMethodID int32
	if provider.PaymentProvider == 31 {
		paymentMethodIDFloat, ok := (json.PaymentMethodID).(float64)
		if !ok {
			paymentMethodIDString, ok := (json.PaymentMethodID).(string)
			paymentMethodIDInt, err := strconv.ParseInt(paymentMethodIDString, 10, 32)
			if !ok || err != nil {
				c.AbortWithStatusJSON(
					http.StatusBadRequest,
					models.StatusFailure("Invalid request!"),
				)
				return
			}
			paymentMethodID = int32(paymentMethodIDInt)
		} else {
			paymentMethodID = int32(paymentMethodIDFloat)
		}
	}

	purchaseRequest := &purchasePB.PurchaseInitiateRequest{
		ParentUser:        parentUser,
		ChildUsers:        childUsers,
		PaymentMethodId:   paymentMethodID,
		PaymentMethodType: json.PaymentMethodType,
		PromoCode:         json.PromoCode,
	}

	makePurchaseResponse, err := purchaseClient.MakeSummercampPurchase(ctx, purchaseRequest)
	if err != nil {
		log.Printf("Error in MakeSummercampPurchaseC, error: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("Something went wrong!"))
		return
	} else if makePurchaseResponse.Status != nil && makePurchaseResponse.Status.Status == apiCommon.NOT_AUTHORIZED {
		c.JSON(http.StatusUnauthorized, makePurchaseResponse.Status)
		return
	} else if makePurchaseResponse.Status != nil && makePurchaseResponse.Status.Status == apiCommon.FAILED {
		c.JSON(http.StatusInternalServerError, makePurchaseResponse.Status)
		return
	}

	c.JSON(
		http.StatusOK,
		makePurchaseResponse,
	)
}
