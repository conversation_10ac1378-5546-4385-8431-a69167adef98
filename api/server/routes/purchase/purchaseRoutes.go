// purchaseRoutes.go

package purchase

import (
	commonController "bitbucket.org/jogocoin/go_api/api/server/routes/purchase/commonController"
	purchaseController "bitbucket.org/jogocoin/go_api/api/server/routes/purchase/controllers"
	purchaseResource "bitbucket.org/jogocoin/go_api/api/server/routes/purchase/resources"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func PurchaseRouteV1(routerVersion *gin.RouterGroup) {
	// Group product related routes together

	purchaseRoutes := routerVersion.Group("/purchase")
	{
		purchaseRoutes.GET("/get", sharedFunc.ValidateDashboardUser, purchaseResource.GetR, purchaseController.GetC)
		purchaseRoutes.POST("/generateReceipt", sharedFunc.ValidateDashboardUser, purchaseResource.GenerateReceiptR, purchaseController.GenerateReceiptC)
		purchaseRoutes.POST("/activateTrial", purchaseResource.ActivateTrialR, purchaseController.ActivateTrialC)
		purchaseRoutes.GET("/getPurchaseOptions", purchaseResource.GetPurchaseOptionsR, purchaseController.GetPurchaseOptionsC)
		purchaseRoutes.POST("/storeAmazonGiftCards", sharedFunc.ValidateDashboardUser, purchaseResource.StoreAmazonGiftCardsR, purchaseController.StoreAmazonGiftCardsC)
		purchaseRoutes.GET("/getUnusedAmazonGiftCards", sharedFunc.ValidateDashboardUser, purchaseResource.GetUnusedGiftCardsR, purchaseController.GetUnusedGiftCardsC)
		purchaseRoutes.GET("/test", sharedFunc.ValidateTokenShared, purchaseController.TestC)
		purchaseRoutes.POST(
			"/makePurchase",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			purchaseResource.MakePurchaseR,
			purchaseController.MakePurchaseC,
		)
		purchaseRoutes.POST(
			"/academy/makePurchase",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			purchaseResource.MakeAcademyPurchaseR,
			commonController.MakePurchaseCommonC,
		)

		purchaseRoutes.POST(
			"/sendUserMailsForPurchase",
			purchaseResource.SendUserMailsForPurchaseR,
			purchaseController.SendUserMailsForPurchaseC,
		)

		purchaseRoutes.POST(
			"/makeSubscription",
			purchaseResource.MakeSubscriptionR,
			sharedFunc.ValidatePaymentsCallback,
			purchaseController.MakeSubscriptionC,
		)

		purchaseRoutes.POST(
			"/status",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			purchaseResource.PurchaseStatusR,
			purchaseController.PurchaseStatusC,
		)

		purchaseRoutes.POST(
			"/history",
			sharedFunc.SetUserIDInContextFromToken,
			purchaseResource.PurchaseHistoryR,
			purchaseController.PurchaseHistoryC,
		)

		purchaseRoutes.POST(
			"/sendTaxInvoice",
			sharedFunc.ValidateDashboardUser,
			purchaseResource.SendTaxInvoiceR,
			purchaseController.SendTaxInvoiceC,
		)
		purchaseRoutes.POST(
			"/paymentCallback",
			sharedFunc.ValidateRazorpayWebhook,
			purchaseResource.PaymentCallbackR,
			purchaseController.PaymentCallbackC,
		)
		purchaseRoutes.POST(
			"/refundAndCancelFromDashboard",
			sharedFunc.ValidateDashboardUser,
			purchaseResource.DashboardRefundAndCancelR,
			purchaseController.CancelMembershipC,
		)
		purchaseRoutes.POST(
			"/refundCallback",
			sharedFunc.ValidateRazorpayWebhook,
			purchaseResource.RefundCallbackR,
			purchaseController.RefundCallbackC,
		)
		purchaseRoutes.GET(
			"/refundAmount",
			sharedFunc.ValidateDashboardUser,
			purchaseResource.GetRefundAmountR,
			purchaseController.GetRefundAmountC,
		)
		purchaseRoutes.GET(
			"/getMaxDiscountAllowed",
			sharedFunc.ValidateDashboardUser,
			purchaseController.GetMaxDiscountAllowedC,
		)
		purchaseRoutes.GET(
			"/getDiscountMap",
			sharedFunc.ValidateDashboardUser,
			purchaseController.GetDiscountAmountMapC,
		)
		purchaseRoutes.POST(
			"/setDiscountMap",
			sharedFunc.ValidateDashboardUser,
			purchaseResource.SetDiscountAmountMapR,
			purchaseController.SetDiscountAmountMapC,
		)
		purchaseRoutes.POST(
			"/payment-status",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			purchaseResource.PurchasePaymentStatusR,
			purchaseController.PurchasePaymentStatusC,
		)
		purchaseRoutes.POST(
			"/eligibleVouchers",
			sharedFunc.ValidateDashboardUser,
			purchaseResource.GenerateReceiptR,
			purchaseController.GetEligibleVouchersC,
		)
	}
}
