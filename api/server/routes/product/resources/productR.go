// product resource

package productResource

import (
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func GetR(c *gin.Context) {
	validate = validator.New()

	var json structs.Product

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateUpdateProductR(c *gin.Context) {
	validate = validator.New()

	var json structs.Product

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetProductLinkR(c *gin.Context) {
	validate = validator.New()

	var json structs.ProductLinkRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("req Validation err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GenerateBulkPurchaseLinkR(c *gin.Context) {
	validate = validator.New()

	var json structs.BulkProductLinkRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}
	if err := validate.Struct(&json); err != nil {
		log.Println("req Validation err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSubscriptionR(c *gin.Context) {
	validate = validator.New()

	var json structs.Subscription

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func IsVoucherValidR(c *gin.Context) {
	validate = validator.New()

	var json structs.IsVoucherValid

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateSubscriptionR(c *gin.Context) {
	validate = validator.New()

	var json structs.CreateSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSportsForSubscriptionR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetSportsForSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetFacilitiesForSubscriptionR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetFacilitiesForSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateProductFSMappingR(c *gin.Context) {
	validate = validator.New()

	var json structs.CreateProductFSMapping

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func UpdateSubscriptionR(c *gin.Context) {
	validate = validator.New()

	var json structs.UpdateSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetTrialDetailsForUserR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetTrialDetailsForUser

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetAboutFitsoDetailsR(c *gin.Context) {
	validate = validator.New()

	var json structs.AboutFitso

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetHomeGroundDetailsR(c *gin.Context) {
	validate = validator.New()

	var json structs.AboutFistoHomeGround

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetHomeGroundOfferedSportDetailsR(c *gin.Context) {
	validate = validator.New()

	var json structs.HomeGroundOfferedSports

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func MarkFreezeR(c *gin.Context) {
	validate = validator.New()
	var json structs.MarkFreeze

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetPlanStatusForUserR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetPlanStatusForUser

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetDetailsForMarkingFreezeR(c *gin.Context) {
	validate = validator.New()
	var json structs.PreMarkFreeze

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GenerateOrGetReferralCodeR(c *gin.Context) {
	validate = validator.New()
	var json structs.ReferralCodeGenerateOrGet

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetPlanUtilizationR(c *gin.Context) {
	validate = validator.New()
	var json structs.PlanUtilizationRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetRenewalCharactersticsR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetRenewalRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetFreezeLogsForSubscriptionR(c *gin.Context) {
	validate = validator.New()
	var json structs.FreezeLogGet

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetFreezeReasonsR(c *gin.Context) {
	validate = validator.New()
	var json structs.FreezeReasonGet

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSportsOffersR(c *gin.Context) {
	validate = validator.New()
	var json structs.SportsOffersGet

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSportsOfferCampaignsR(c *gin.Context) {
	validate = validator.New()
	var json structs.SportsOfferCampaignsGet

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateUpdateSportsOfferR(c *gin.Context) {
	validate = validator.New()
	var json structs.SportsOffer

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateUpdateSportsOfferCampaignR(c *gin.Context) {
	validate = validator.New()
	var json structs.OfferCampaign

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func UnfreezeSubscriptionR(c *gin.Context) {
	validate = validator.New()
	var json structs.Unfreeze

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateOrUpdateVouchersR(c *gin.Context) {
	validate = validator.New()
	var json structs.Vouchers

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetVouchersR(c *gin.Context) {

	validate = validator.New()
	var json structs.GetVouchers

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}
	c.Set("jsonData", json)

	c.Next()
}

func PostFreezeOrUnfreezeDetailsR(c *gin.Context) {

	validate = validator.New()
	var json structs.PostFreezeOrUnfreeze

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}
	c.Set("jsonData", json)

	c.Next()
}

func GetFacilitiesForProductR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetFacilitiesForProduct

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetProductFacilitySportR(c *gin.Context) {
	validate = validator.New()

	var json structs.ProductFacilitySport

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func UpdateProductFacilitySportR(c *gin.Context) {
	validate = validator.New()

	var json structs.ProductFacilitySport

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetTimelineForSubscriptionR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetTimelineForSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func OptInSubscriptionR(c *gin.Context) {
	validate = validator.New()
	var json structs.OptInSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func InsertHomeGroundLeadR(c *gin.Context) {
	validate = validator.New()
	var json structs.HomeGroundLeadData

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetProductCategoriesR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetProductCategories

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}
func SealsUpgradeR(c *gin.Context) {
	validate = validator.New()
	var json structs.SealsUpgrade

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetFacilitiesForSubscriptionV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.GetFacilitiesForSubscription

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetDurationsR(c *gin.Context) {
	c.Next()
}

func UploadImageToS3BucketR(c *gin.Context) {
	validate = validator.New()

	var json structs.ImageUpload

	file, err := c.FormFile("file")
	if err != nil {
		log.Println(err)
	}
	json.UploadImage = *file

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		//handle error here
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

// CalculateCartR validates calculate cart API request body
func CalculateCartR(c *gin.Context) {
	validate = validator.New()

	var json productModel.CalculateCartRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for calculate cart: %v", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for calculate cart : %v", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetPurchasePageDetailsR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetPurchasePageDetailsRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Printf("Error in Binding while fetching purchase page details, Error is: %v", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error in Validation while fetching purchase page details, Error is: %v", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetBuyOrRenewDetailsR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetBuyOrRenewDetailsRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetMembershipDetailsR(c *gin.Context) {
	validate = validator.New()
	var json structs.MembershipDetailsRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func MembershipCancellationDetailsR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetMembershipsEligibleForRefundReq

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)
	c.Next()
}

func CancelMembershipR(c *gin.Context) {
	validate = validator.New()

	var json structs.CancelMembershipReq

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("error in binding request for cancelling membership: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for cancelling membership: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func AcademyPreferredPlansR(c *gin.Context) {
	validate = validator.New() 

	var json structs.GetAcademyPreferredPlan

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("error in binding request for academy preferred plans: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for academy preferred plans: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func AcademyPreferredPlansV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.GetAcademyPreferredPlan

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("error in binding request for academy preferred plans v2: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for academy preferred plans v2: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func AcademyRecommendedCourseR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetAcademyRecommendedCourse

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("error in binding request for academy recommended course: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for academy recommended course: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func PushLeadToLeadSquaredCaptureR(c *gin.Context) {
	validate = validator.New()

	var json structs.LeadSquaredCaptureLead

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("error in binding request for PushLeadToLeadSquaredCapture: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validate request for PushLeadToLeadSquaredCapture: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func CalculateAcademyCartR(c *gin.Context) {
	validate = validator.New()

	var json structs.CalculateAcademyCartRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("Error in binding request for calculate academy cart: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error in validating request for calculate academy cart : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}
