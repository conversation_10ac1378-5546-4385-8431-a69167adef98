package commonController

import (
	"github.com/gin-gonic/gin"

	common "bitbucket.org/jogocoin/go_api/api/common"
	productController "bitbucket.org/jogocoin/go_api/api/server/routes/product/controllers"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

func PreferredPlansC(c *gin.Context) {
	json := c.MustGet("jsonData").(structs.GetAcademyPreferredPlan)
	if json.ProductCategoryId == common.SummerCampCategoryID {
		productController.SummerCampPreferredPlansC(c)
	} else {
		productController.AcademyPreferredPlansC(c)
	}
}

func RecommendedCourseC(c *gin.Context) {
	json := c.MustGet("jsonData").(structs.GetAcademyRecommendedCourse)
	if json.ProductCategoryId == common.SummerCampCategoryID {
		productController.SummerCampRecommendedPlansC(c)
	} else {
		productController.AcademyRecommendedCourseC(c)
	}
}
