package commonController

import (
	productController "bitbucket.org/jogocoin/go_api/api/server/routes/product/controllers"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"strconv"
	"fmt"
)

func PurchasePageMKandAcademyC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(structs.GetPurchasePageDetailsRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryID, _ := strconv.Atoi(requestData.ProductCategoryId)
	if categoryID == common.AcademyCategoryID  {
		productController.GetAcademyPurchasePageC(c)
	} else if categoryID == common.SummerCampCategoryID {
		productController.GetSummerCampPurchasePageC(c)
	} else {
		productController.GetPurchasePageDetailsC(c)
	}
}

func CalculateCartCommonC(c *gin.Context) {
	requestData := c.MustGet("jsonData").(structs.CalculateAcademyCartRequest)
	if queryProductCategoryId, ok := c.GetQuery("product_category_id"); ok {
		requestData.ProductCategoryId = queryProductCategoryId
	}
	categoryId := fmt.Sprintf("%v", requestData.ProductCategoryId)
	categoryID, _ := strconv.Atoi(categoryId)
	if categoryID == common.SummerCampCategoryID {
		productController.CalculateSummercampCartC(c)
	} else {
		productController.CalculateAcademyCartC(c)
	}
}