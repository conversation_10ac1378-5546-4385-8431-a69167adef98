package productController

import (
	"net/http"
	"encoding/json"
	"sort"

	"bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"

	"context"
	"log"
	"fmt"
	"strconv"

	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type FeaturedProductListResponse struct {
	Header  *productModel.HeaderData              `json:"header,omitempty"`
	Results *[]FeaturedProductListResponseResults `json:"results,omitempty"`
	TabData *[]FeaturedProductListTabDataItem                `json:"tab_data,omitempty"`
}

type FeaturedProductListTabDataItem struct {
	Title *sushi.TextSnippet `json:"title"`
	Subtitle *sushi.TextSnippet `json:"subtitle"`
	Sections *[]FeaturedProductListResponseResults `json:"sections,omitempty"`
	ClevertapTracking             []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
	IsDisabled bool `json:"is_disabled"`
}

type FeaturedProductListResponseResults struct {
	LayoutConfig                  *sushi.LayoutConfig                  `json:"layout_config,omitempty"`
	FitsoTextSnippetType10Snippet *sushi.FitsoTextSnippetType10Snippet `json:"fitso_text_snippet_type_10,omitempty"`
	SectionHeaderType1Snippet     *sushi.SectionHeaderType1Snippet     `json:"section_header_type_1,omitempty"`
	ClevertapTracking             []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
}

func GetFeaturedProductsForCityIdC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)

	cl := util.GetProductClient()

	cityId := util.GetCityIDFromContext(ctx)
	if cityId > 0 {
		req := &productPB.GetFeaturedProductsForCityIdReq{
			CityId: cityId,
		}
		res, err := cl.GetFeaturedProductsForCityId(ctx, req)
		if err != nil {
			log.Println("Error in getting Featured Procucts for CityId: ", cityId, err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		totalProducts := 0
		for _, productList := range res.ProductMap {
			totalProducts = totalProducts + len(productList.Products)
		}
		if totalProducts > 0 {
			response := GetFeaturedProductListResponse(ctx, res)
			render.Render(c, gin.H{
				"title":   "Products",
				"payload": response}, "index.html")
		}
	} else {
		return
	}
}
func GetFeaturedProductListResponse(ctx context.Context, res *productPB.ProductResponse) FeaturedProductListResponse {
	productMap := res.ProductMap
	response := FeaturedProductListResponse{}
	title, _ := sushi.NewTextSnippet("Select your plan")
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(font)
	title.SetColor(color)
	header := &productModel.HeaderData{
		Title: title,
	}
	response.Header = header
	items := make([]FeaturedProductListResponseResults, 0)
	layout_config := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType10,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	productCategories := make([]int, 0, len(productMap))
	for k := range productMap {
		productCategories = append(productCategories, int(k))
	}
	sort.Ints(productCategories)
	tabItems := make([]FeaturedProductListTabDataItem, 0)
	for _, productCategory := range productCategories {
		productCategory := int32(productCategory)
		productList := productMap[productCategory]
		if len(productList.Products) == 0 {
			continue
		}
		title := &sushi.TextSnippet{}
		subtitle := &sushi.TextSnippet{}
		colorType := sushi.ColorTypeBlue
		t := "CULTPASS PLAY"

		if productCategory == common.MasterkeyCategoryID {
			title, _ = sushi.NewTextSnippet(t)
			subtitle, _ = sushi.NewTextSnippet("All sports")
		} else if productCategory == common.SinglekeyCategoryID {
			title, _ = sushi.NewTextSnippet(t + " <medium-50|Lite>")
			title.SetIsMarkdown(1)
			subtitle, _ = sushi.NewTextSnippet("Badminton")
		}

		if !featuresupport.SupportSingleKeyProduct(ctx) {
			color, _ := sushi.NewColor(colorType, sushi.ColorTint600)
			font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			title.SetFont(font)
			title.SetColor(color)
			title.SetKerning(2)	

			layoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.SectionHeaderType1,
			}
			snippet := &sushi.SectionHeaderType1Snippet{
				Title: title,
			}
	
			item := FeaturedProductListResponseResults{
				LayoutConfig:              layoutConfig,
				SectionHeaderType1Snippet: snippet,
			}

			items = append(items, item)
		}
		
		productItems := make([]FeaturedProductListResponseResults, 0)

		for _, product := range productList.Products {
			trackingItem1 := GetProductListImpression(ctx, product.PId)
			item := FeaturedProductListResponseResults{
				LayoutConfig:      layout_config,
				ClevertapTracking: []*sushi.ClevertapItem{trackingItem1},
			}
			snippet := &sushi.FitsoTextSnippetType10Snippet{
				Id:           product.PId,
				CornerRadius: int32(8),
			}
			gradient := sushi.Gradient{}
			title, _ := sushi.NewTextSnippet(strconv.Itoa(int(product.Duration)))
			font, _ := sushi.NewFont(sushi.FontBold, sushi.FontSize900)
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			title.SetFont(font)
			title.SetColor(color)
			snippet.Title = title

			durationUnit := product.DurationUnit
			if product.DurationUnit == "month" {
				durationUnit = "Months"
			}
			subtitle, _ := sushi.NewTextSnippet(durationUnit)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			subtitle.SetFont(font)
			subtitle.SetColor(color)
			snippet.Subtitle = subtitle

			//price
			subtitle1, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(product.Price)))
			font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			subtitle1.SetFont(font)
			subtitle1.SetColor(color)

			perMonthPrice := int32(product.Price)
			if product.Duration > 0 {
				perMonthPrice = int32(product.Price) / product.Duration
			}

			subtitle2, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(product.RetailPrice)))
			if product.Price > product.RetailPrice {
				subtitle1.Strikethrough = true
				font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
				color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
				subtitle2.SetFont(font)
				subtitle2.SetColor(color)
				snippet.Subtitle2 = subtitle2
				perMonthPrice = int32(product.RetailPrice) / product.Duration
			}

			snippet.Subtitle1 = subtitle1
			perMonthText := "At just {grey-900|₹" + strconv.Itoa(int(perMonthPrice)) + "}/mo"
			subtitle3, _ := sushi.NewTextSnippet(perMonthText)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			subtitle3.SetFont(font)
			subtitle3.SetColor(color)
			subtitle3.IsMarkdown = 1
			snippet.Subtitle3 = subtitle3

			percentDiscount := int((product.Price - product.RetailPrice) * 100 / product.Price)
			percentDiscountText := "{blue-400|" + strconv.Itoa(int(percentDiscount)) + "%} off"
			tagTitle, _ := sushi.NewTextSnippet(percentDiscountText)
			color, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint600)
			font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
			tagTitle.SetFont(font)
			tagTitle.SetColor(color)
			tagTitle.IsMarkdown = 1
			bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			bgColor.Alpha = 0.8
			if product.Duration == 6 && product.DurationUnit == "month" {
				bgColor, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
			}

			if percentDiscount > 0 {
				snippet.Tag = &sushi.Tag{
					Title:   tagTitle,
					Size:    sushi.TagSizeSmall,
					BgColor: bgColor,
				}
			}

			if product.Popularity == 1 {
				color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				color2, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint600)
				gradient.Colors = &[]sushi.Color{*color1, *color2}

				topLeftTitle, _ := sushi.NewTextSnippet("MOST POPULAR")
				color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint900)
				font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
				topLeftTitle.SetFont(font)
				topLeftTitle.SetColor(color)
				topLeftTitle.Alignment = sushi.TextAlignmentRight
				topLeftTitle.Kerning = 3

				bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
				topLeftTag := &sushi.Tag{
					Title:   topLeftTitle,
					Size:    sushi.TagSizeSmall,
					BgColor: bgColor,
					Type:    "right_diagonal_rounded",
				}
				snippet.TopLeftTag = topLeftTag

				color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

				titleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				title.SetColor(titleColor)

				subtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				subtitle.SetColor(subtitleColor)

				subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				subtitle1.SetColor(subtitle1Color)

				subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				subtitle2.SetColor(subtitle2Color)

				perMonthText := "At just <semibold-050|₹" + strconv.Itoa(int(perMonthPrice)) + ">/mo"
				subtitle3, _ := sushi.NewTextSnippet(perMonthText)
				subtitle3Color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				subtitle3Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
				subtitle3.SetFont(subtitle3Font)
				subtitle3.SetColor(subtitle3Color)
				subtitle3.IsMarkdown = 1
				snippet.Subtitle3 = subtitle3
			} else if product.Popularity == 2 {
				color1, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				gradient.Colors = &[]sushi.Color{*color1}
				borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
				snippet.BorderColor = borderColor
				if percentDiscount > 0 {
					tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
					snippet.Tag = &sushi.Tag{
						Title:   tagTitle,
						Size:    sushi.TagSizeSmall,
						BgColor: tagBgColor,
					}
				}

			} else {
				color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
				color2, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
				gradient.Colors = &[]sushi.Color{*color1, *color2}
			}
			snippet.Gradient = &gradient
			snippet.ClickAction = getPlanSelectionClickAction(ctx, product)
			snippet.ClevertapTracking = []*sushi.ClevertapItem{GetTrackingItemForProductClick(ctx, product.PId)}
			item.FitsoTextSnippetType10Snippet = snippet

			if featuresupport.SupportSingleKeyProduct(ctx) {
				productItems = append(productItems, item)
			} else {
				items = append(items, item)
			}
		}
		tabItem := FeaturedProductListTabDataItem{
			Title:       title,
			Subtitle:    subtitle,
			Sections:	&productItems,
		}

		tabItems = append(tabItems, tabItem)
	} 

	if featuresupport.SupportSingleKeyProduct(ctx) {
		response.TabData = 	&tabItems
	} else {	
		response.Results = &items
	}
	return response
}

func GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "featured_product_dropdown_list"
	return commonPayload
}

func GetTrackingItemForProductClick(ctx context.Context, PId int32) *sushi.ClevertapItem {
	tapPayload := GetCommonPayloadForTracking(ctx)
	tapPayload["product_id"] = int(PId)
	tapEname := &sushi.EnameData{
		Ename: "featured_product_list_product_tap",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func GetProductListImpression(ctx context.Context, PId int32) *sushi.ClevertapItem {
	tapPayload := GetCommonPayloadForTracking(ctx)
	tapPayload["product_id"] = PId
	tapEname := &sushi.EnameData{
		Ename: "featured_product_list_product_impression",
	}
	event := sushi.NewClevertapEvents()
	event.SetImpression(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func getPlanSelectionClickAction(ctx context.Context, product *productPB.Product) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()

	durationUnit := "Month"
	if product.DurationUnit == "month" && product.Duration > 1 {
		durationUnit = "Months"
	} else if product.DurationUnit == "month" {
		durationUnit = "Month"
	}
	productName := "CULTPASS PLAY"

	if product.ProductCategoryId == common.SinglekeyCategoryID {
		productName = productName + " Lite"
	}
	duration := fmt.Sprintf("%s - %d %s", productName, product.Duration, durationUnit)

	if duration != "" {
		clickAction.SetClickActionType("dismiss_page")

		durationTextSnippet, _ := sushi.NewTextSnippet(duration)

		changePlanButton, _ := sushi.NewButton(sushi.ButtontypeText)
		changePlanButton.Text = duration
		changePlanButton.Color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		sufficColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		suffixIcon := &sushi.Icon{
			Code:  "e900",
			Color: sufficColor,
		}
		changePlanButton.SuffixIcon = suffixIcon
		changePlanButtonClickAction := sushi.GetClickAction()

		deeplink := &sushi.Deeplink{
			URL: util.GetSelectPlansDeeplink(),
		}
		changePlanButtonClickAction.SetDeeplink(deeplink)
		changePlanButton.ClickAction = changePlanButtonClickAction
		price := "₹" + strconv.Itoa(int(product.RetailPrice))
		priceTextSnippet, _ := sushi.NewTextSnippet(price)

		preferredCenterSection := &sushi.PlanEditInfoSection{
			Type:             "preferred_center",
		}

		titleText := "Select your Preferred sport & center"
		if product.ProductCategoryId == common.SinglekeyCategoryID {
			titleText = "Select your Preferred center"
		}
		title, _ := sushi.NewTextSnippet(titleText)

		rightIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		rightIcon, _ := sushi.NewIcon(sushi.InfoIcon, rightIconColor)
		buttonTitle, _ := sushi.NewTextSnippet("Get priority while booking your favourite sport at your preferred center.")
		buttonTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonTitle.SetColor(buttonTitleColor)
		buttonTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		buttonTitle.SetFont(buttonTitleFont)
		buttonSubtitle, _ := sushi.NewTextSnippet("OK")
		buttonSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonSubtitle.SetColor(buttonSubtitleColor)
		buttonSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		buttonSubtitle.SetFont(buttonSubtitleFont)
		buttonBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		showTooltip := &sushi.ShowTooltip{
			Title:    buttonTitle,
			Subtitle: buttonSubtitle,
			BgColor:  buttonBgColor,
		}
		showTooltipClickAction := sushi.GetClickAction()
		showTooltipClickAction.SetShowTooltip(showTooltip)
		rightIcon.SetClickAction(showTooltipClickAction)
		trackingItemPreferredCenter := getTrackingItemForToolTips(ctx, "select_preferred_center_tooltip_tap")
		rightIcon.ClevertapTracking = []*sushi.ClevertapItem{trackingItemPreferredCenter}

		placeholderContainerTitle, _ := sushi.NewTextSnippet("Set preference")
		placeholderContainerTitleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		placeholderContainerTitle.SetColor(placeholderContainerTitleColor)
		placeholderContainerTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		placeholderContainerTitle.SetFont(placeholderContainerTitleFont)

		placeholderContainerIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		placeholderContainerIcon, _ := sushi.NewIcon(sushi.RightIcon, placeholderContainerIconColor)


		preferredCenterClickAction := getClickActionForSelectPreferredCenter(ctx)
		if product.ProductCategoryId == common.SinglekeyCategoryID {
			deeplink := util.GetPreferredCenterPageDeeplinkWithSportID(common.BADMINTON_SPORT_ID, true)
		
			payload := map[string]interface{}{
				"sport_id":               common.BADMINTON_SPORT_ID,
				"is_singlekey_product":	  true,
			}
			postback_params, _ := json.Marshal(payload)

			preferredCenterClickAction = sushi.GetClickAction()
			preferredCenterClickAction.SetClickActionType(sushi.ClickActionDeeplink)
			preferredCenterClickAction.Deeplink = &sushi.Deeplink{
				URL: deeplink,
				PostbackParams: string(postback_params),
			}
		}

		preferredCenterSection.PreferredCenter = &sushi.PreferredCenterSectionItem{
			Title:          title,
			RightIcon:      rightIcon,
			DurationInDays: 14,
			EditButton: &sushi.EditButtonCustom{
				Text:        "Change",
				ClickAction: preferredCenterClickAction,
			},
			PlaceholderContainer: &sushi.EditInfoSectionFieldPlaceholderContainer{
				Title:       placeholderContainerTitle,
				Icon:        placeholderContainerIcon,
				ClickAction: preferredCenterClickAction,
			},
			Optional: false,
		}

		cityID := util.GetCityIDFromContext(ctx)
		if cityID != product.LocationIdV2 {

			cityChangeClickAction := sushi.GetClickAction()
			customAlert := CustomAlertForCityChange()
			cityChangeClickAction.SetCustomAlertAction(customAlert)
			editButton := &sushi.EditButtonCustom{
				Text:        "Change",
				ClickAction: cityChangeClickAction,
			}

			preferredCenterSection.PreferredCenter.EditButton = editButton
			preferredCenterSection.PreferredCenter.PlaceholderContainer.ClickAction = cityChangeClickAction
		} else {
			editButton := &sushi.EditButtonCustom{
				Text: "Change",
			}
			preferredCenterSection.PreferredCenter.EditButton = editButton
		}

		savePlanDetails := &sushi.SavePlanDetails{
			ProductId:        product.PId,
			Duration:         durationTextSnippet,
			ChangePlanButton: changePlanButton,
			Price:            priceTextSnippet,
			PreferredCenterButton:  preferredCenterSection,
		}

		dismissPage := &sushi.DismissPage{
			Type:            "save_plan_details",
			SavePlanDetails: savePlanDetails,
		}
		clickAction.DismissPage = dismissPage
		return clickAction
	}
	return clickAction

}
