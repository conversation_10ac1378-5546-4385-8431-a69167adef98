package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	ptypes "github.com/golang/protobuf/ptypes"
)

const (
	SUBSCRIPTION_STATUS_CURRENT   = "Current"
	SUBSCRIPTION_STATUS_UPCOMING  = "Upcoming"
	SUBSCRIPTION_STATUS_EXPIRED   = "Expired"
	SUBSCRIPTION_STATUS_CANCELLED = "Cancelled"
	SUBSCRIPTION_STATUS_RENEWED   = "Renewed"
	SUBSCRIPTION_STATUS_OLDENTRY  = "Oldentry"
)

type MembershipDetails struct {
	Header            *productModel.HeaderData             `json:"header,omitempty"`
	Footer            *productModel.ResultSection          `json:"footer,omitempty"`
	Results           []*productModel.ResultSection        `json:"results,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
	Response          *productPB.MembershipDetailsResponse `json:"-"`
	UserId            int32                                `json:"-"`
	ProductId         int32                                `json:"-"`
}

func GetMembershipDetails(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	productClient := util.GetProductClient()
	json := c.MustGet("jsonData").(structs.MembershipDetailsRequest)
	req := &productPB.MembershipDetailsRequest{
		UserId:    json.UserId,
		ProductId: json.ProductId,
	}

	response, err := productClient.GetUserMembershipDetails(ctx, req)
	if err != nil {
		log.Println(err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("something went wrong"))
		return
	}
	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
		c.JSON(statusCode, models.StatusFailure(common.NOT_AUTHORIZED))
		return
	}
	if response.Status != nil && response.Status.Status == common.FAILED {
		statusCode = http.StatusInternalServerError
		log.Printf("Api: membership details, Failed request, Error: %v", response.Status.Message)
		c.JSON(statusCode, models.StatusFailure(response.Status.Message))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
		log.Printf("Api: membership details, Bad request, Error: %v", response.Status.Message)
		c.JSON(statusCode, models.StatusFailure(response.Status.Message))
		return
	}
	template := MembershipDetails{
		Response:  response,
		UserId:    req.UserId,
		ProductId: req.ProductId,
	}
	template.SetHeaders(ctx)
	if response.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED || response.MembershipStatus == SUBSCRIPTION_STATUS_EXPIRED {
		template.SetFooter(ctx)
	}
	template.SetMembershipBasicInfo(ctx)
	template.SetMembershipDetailsection(ctx)
	if response.ProductCategoryId != common.AcademyCategoryID && response.ProductCategoryId != common.SummerCampCategoryID {
		if len(response.SportBookingCount) > 0 {
			template.SetBookedSessionDetailsSection(ctx)
		} else {
			template.SetEmptyBookedSessionDetailsSection(ctx)
		}
	}
	if featuresupport.SupportsPurchaseHistory(c) {
		template.SetPurchaseHistorySection(ctx)
	}
	if response.PreferredFacility != nil && response.ProductCategoryId != common.AcademyCategoryID {
		template.SetPreferredCenterSection(ctx)
	} else if response.FacilitySessionDetails != nil && (response.ProductCategoryId == common.AcademyCategoryID || response.ProductCategoryId == common.SummerCampCategoryID) {
		template.SetAcademyFacilitiesSessions(ctx)
	}
	if response.ProductCategoryId != common.AcademyCategoryID && response.ProductCategoryId != common.SummerCampCategoryID {
		template.SetFitsoBenefitsSection(ctx)
	}
	template.SetFooterButtonsSection(ctx)
	template.SetClevertapTracking(ctx)

	c.JSON(http.StatusOK, template)
}

func (m *MembershipDetails) AddResultSection(section *productModel.ResultSection) {
	m.Results = append(m.Results, section)
}

func (m *MembershipDetails) SetHeaders(ctx context.Context) {
	productName := m.Response.ProductDetails.ProductName
	tagText := m.Response.ProductDetails.ProductLocation
	loggedInUser := util.GetUserIDFromContext(ctx)
	if m.Response.ProductCategoryId == common.AcademyCategoryID {
		productName = "CULT ACADEMY"

		if len(m.Response.FacilitySessionDetails) == 1 {
			tagText = m.Response.FacilitySessionDetails[0].FacilityDetails.SportName
		} else if len(m.Response.FacilitySessionDetails) == 2 {
			tagText = "Combo"
		}
	} else if m.Response.ProductCategoryId == common.SummerCampCategoryID {
		productName = "CULT SUMMER CAMP"
		if len(m.Response.FacilitySessionDetails) == 1 {
			tagText = m.Response.FacilitySessionDetails[0].FacilityDetails.SportName
		} else if len(m.Response.FacilitySessionDetails) == 2 {
			tagText = "Combo"
		}
	}
	headerTitle, _ := sushi.NewTextSnippet(productName)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	headerTitle.SetFont(font)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	headerTitle.SetColor(color)
	headerTitle.SetKerning(1)

	image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/fitsoicon1618823223.png")
	if util.Contains(common.TestSummercampUsers, loggedInUser) || true {
		if m.Response.ProductCategoryId == common.SummerCampCategoryID {
			image, _ = sushi.NewImage("https://fitso-images.curefit.co/uploads/Frame217991(1)1707461146.png")
		} else if m.Response.ProductCategoryId == common.MasterkeyCategoryID {
			image, _ = sushi.NewImage("https://fitso-images.curefit.co/uploads/Frame51708577698.png")
		} else {
			image, _ = sushi.NewImage("https://fitso-images.curefit.co/uploads/Frame2177311707386866.png")
		}
	}
	image.SetType(sushi.ImageTypeRectangle)
	image.SetAspectRatio(2.230769)
	image.SetHeight(46)

	tagTitle, _ := sushi.NewTextSnippet(tagText)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	tagTitle.SetColor(color)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
	tag := &sushi.Tag{
		Title:   tagTitle,
		BgColor: tagBgColor,
	}

	gradientColor1, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	gradientColor2, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint200)
	gradient := &sushi.Gradient{
		Colors: &([]sushi.Color{*gradientColor1, *gradientColor2}),
	}

	m.Header = &productModel.HeaderData{
		//Title:    headerTitle,
		Image:    image,
		Tag:      tag,
		Gradient: gradient,
	}
	if !util.Contains(common.TestSummercampUsers, loggedInUser) && false {
		m.Header.Title  = headerTitle
	}
}

func (m *MembershipDetails) SetFooter(ctx context.Context) {
	if m.Response.ProductCategoryId != common.AcademyCategoryID && m.Response.ProductCategoryId != common.SummerCampCategoryID {
		footerLayout := &sushi.LayoutConfig{
			SnippetType: sushi.FooterSnippetType2,
		}

		clickAction := sushi.GetClickAction()
		url := util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID)
		ctaText := "Renew Membership"
		if !m.Response.NewProductMembership {
			url = util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID)
			ctaText = "Buy now"
		} else {
			loggedInUserId := util.GetUserIDFromContext(ctx)
			if loggedInUserId == m.UserId {
				url = util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID) //sending to purchase landing page till dec 2021
				//url = util.GetBuyingForMeDeeplink(m.ProductId, true)
			}
		}
		deeplink := &sushi.Deeplink{
			URL: url,
		}
		clickAction.SetDeeplink(deeplink)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

		buttonItem := sushi.FooterSnippetType2ButtonItem{
			Type:        sushi.FooterButtonTypeSolid,
			Text:        ctaText,
			ClickAction: clickAction,
			Font:        font,
		}
		buttonItems := []sushi.FooterSnippetType2ButtonItem{buttonItem}
		buttonData := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationHorizontal,
			Items:       &buttonItems,
		}
		footerSnippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: buttonData,
		}
		m.Footer = &productModel.ResultSection{
			LayoutConfig:       footerLayout,
			FooterSnippetType2: footerSnippet,
		}
	}

}

func (m *MembershipDetails) SetMembershipBasicInfo(ctx context.Context) {
	productName := m.Response.ProductDetails.ProductName
	if m.Response.ProductCategoryId == common.SummerCampCategoryID {
		productName = "SUMMER CAMP - " + m.Response.FacilitySessionDetails[0].FacilityDetails.SportName
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.PurchaseWidgetSnippet3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	item := &sushi.PurchaseWidgetSnippet3Item{}
	if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED {

		subtitle1, _ := sushi.NewTextSnippet("You have cancelled your membership!")
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle1.SetColor(color)
		subtitle1.SetFont(font)

		item = &sushi.PurchaseWidgetSnippet3Item{
			TopContainer:    m.GetMembershipBasicInfoTopContainer(ctx),
			BottomContainer: m.GetMembershipBasicInfoBottomContainer(ctx),
			Subtitle1:       subtitle1,
		}
	} else if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_EXPIRED {

		subtitle1, _ := sushi.NewTextSnippet("Your membership has been expired!")
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_DARK_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle1.SetColor(color)
		subtitle1.SetFont(font)

		item = &sushi.PurchaseWidgetSnippet3Item{
			TopContainer:    m.GetMembershipBasicInfoTopContainer(ctx),
			BottomContainer: m.GetMembershipBasicInfoBottomContainer(ctx),
			Subtitle1:       subtitle1,
		}
	} else if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CURRENT {
		today := time.Now()
		today = Bod(today)
		title, _ := sushi.NewTextSnippet(productName)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetColor(color)
		title.SetKerning(3)
		endDate, _ := ptypes.Timestamp(m.Response.UserSubscription.SubscriptionEndDate)
		endDate = GetLocalDateTime(endDate)
		days := endDate.Sub(today).Hours() / 24
		if days <= 14 {
			subTitleText := SubscriptionDaysRemaining(int(days))

			subtitle1, _ := sushi.NewTextSnippet(subTitleText)
			colorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_DARK_THEME
			}
			color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
			font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
			subtitle1.SetColor(color)
			subtitle1.SetFont(font)

			item = &sushi.PurchaseWidgetSnippet3Item{
				TopContainer:      m.GetMembershipBasicInfoTopContainer(ctx),
				BottomContainer:   m.GetMembershipBasicInfoBottomContainer(ctx),
				ProgressContainer: m.GetMembershipBasicInfoProgressContainer(ctx),
				Title:             title,
				Subtitle1:         subtitle1,
			}
		} else {
			item = &sushi.PurchaseWidgetSnippet3Item{
				TopContainer:      m.GetMembershipBasicInfoTopContainer(ctx),
				ProgressContainer: m.GetMembershipBasicInfoProgressContainer(ctx),
				Title:             title,
			}
		}
	} else if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_RENEWED {
		title, _ := sushi.NewTextSnippet(productName)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetColor(color)
		title.SetKerning(3)
		bookingTimeObj := time.Unix(m.Response.UserFutureSubscription.StartDate.Seconds, 0)
		bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
		bookingMonthStr := bookingMonth.String()

		subtitle1, _ := sushi.NewTextSnippet("Next membership starts on")
		color, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
		subtitle1.SetColor(color)
		subtitle1.SetFont(font)

		subtitle2, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle2.SetColor(color)
		subtitle2.SetFont(font)

		item = &sushi.PurchaseWidgetSnippet3Item{
			TopContainer:      m.GetMembershipBasicInfoTopContainer(ctx),
			ProgressContainer: m.GetMembershipBasicInfoProgressContainer(ctx),
			Title:             title,
			Subtitle1:         subtitle1,
			Subtitle2:         subtitle2,
		}
	} else if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_UPCOMING {
		bookingTimeObj := time.Unix(m.Response.UserFutureSubscription.StartDate.Seconds, 0)
		bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
		bookingMonthStr := bookingMonth.String()
		title, _ := sushi.NewTextSnippet(productName)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetColor(color)
		title.SetKerning(3)

		subtitle1, _ := sushi.NewTextSnippet("Next membership starts on")
		color, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
		subtitle1.SetColor(color)
		subtitle1.SetFont(font)

		subtitle2, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle2.SetColor(color)
		subtitle2.SetFont(font)

		item = &sushi.PurchaseWidgetSnippet3Item{
			TopContainer: m.GetMembershipBasicInfoTopContainer(ctx),
			Title:        title,
			Subtitle1:    subtitle1,
			Subtitle2:    subtitle2,
		}
	} else if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_OLDENTRY {
		subtitle1, _ := sushi.NewTextSnippet("Your membership has been expired!")
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle1.SetColor(color)
		subtitle1.SetFont(font)

		item = &sushi.PurchaseWidgetSnippet3Item{
			TopContainer: m.GetMembershipBasicInfoTopContainer(ctx),
			Subtitle1:    subtitle1,
		}
	}

	snippet := &sushi.PurchaseWidgetSnippetType3{
		Items: []*sushi.PurchaseWidgetSnippet3Item{item},
	}
	section := &productModel.ResultSection{
		LayoutConfig:           layoutConfig,
		PurchaseWidgetSnippet3: snippet,
	}
	m.AddResultSection(section)
}

func (m *MembershipDetails) GetMembershipBasicInfoTopContainer(ctx context.Context) *sushi.PurchaseWidgetItemTopContainer {
	image, _ := sushi.NewImage(m.Response.UserDetails.ProfilePictureHash)
	image.SetAspectRatio(1)
	image.SetType(sushi.ImageTypeCircle)
	image.SetHeight(60)
	image.SetWidth(60)
	loggedInUserId := util.GetUserIDFromContext(ctx)
	var titleName string
	if m.UserId == loggedInUserId {
		titleName = m.Response.UserDetails.Name + " <medium-200|(You)>"
	} else {
		titleName = m.Response.UserDetails.Name
	}
	title, _ := sushi.NewTextSnippet(titleName)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title.SetColor(color)
	title.SetFont(font)
	title.SetIsMarkdown(1)

	topContainer := &sushi.PurchaseWidgetItemTopContainer{
		Title: title,
		Image: image,
	}
	if len(m.Response.UserDetails.Phone) > 0 {
		subtitle, _ := sushi.NewTextSnippet(m.Response.UserDetails.Phone)
		color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle.SetColor(color)
		subtitle.SetFont(font)
		topContainer.Subtitle = subtitle
	}
	return topContainer
}

func (m *MembershipDetails) GetMembershipBasicInfoBottomContainer(ctx context.Context) *sushi.PurchaseWidgetItemBottomContainer {
	url := util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID)
	if m.Response.ProductCategoryId == common.AcademyCategoryID {
		url = util.GetPurchaseMembershipDeeplink(common.AcademyCategoryID)
	} else if m.Response.ProductCategoryId == common.SummerCampCategoryID {
		return nil
	}
	ctaText := "Renew Now"
	if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CURRENT {
		ctaText = "Renew membership"
	}
	if !m.Response.NewProductMembership {
		ctaText = "Buy Now"
	}

	if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED || m.Response.MembershipStatus == SUBSCRIPTION_STATUS_EXPIRED {
		var titleStatus string
		var endDate int64
		if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED {
			titleStatus = "Cancelled on"
			endDate = m.Response.CancellationDetails.CreatedAt.Seconds
		} else {
			titleStatus = "Expired on"
			endDate = m.Response.UserSubscription.SubscriptionEndDate.Seconds
		}
		title, _ := sushi.NewTextSnippet(titleStatus)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize50)
		title.SetColor(color)
		title.SetFont(font)

		bookingTimeObj := time.Unix(endDate, 0)
		bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
		bookingMonthStr := bookingMonth.String()
		subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subtitle.SetColor(color)
		subtitle.SetFont(font)

		clickAction := sushi.GetClickAction()
		deeplink := &sushi.Deeplink{
			URL: url,
		}
		clickAction.SetDeeplink(deeplink)

		tapPayload := m.GetCommonPayloadForTracking(ctx)
		tapEname := &sushi.EnameData{
			Ename: "user_card_renew_tap",
		}
		cardButtonEvents := sushi.NewClevertapEvents()
		cardButtonEvents.SetTap(tapEname)
		cardTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, cardButtonEvents)

		button, _ := sushi.NewButton(sushi.ButtonTypeSolid)
		button.SetText(ctaText)
		button.SetSize(sushi.ButtonSizeMedium)
		button.SetClickAction(clickAction)
		button.AddClevertapTrackingItem(cardTrackItem)

		bottomContainer := &sushi.PurchaseWidgetItemBottomContainer{
			Title:    title,
			Subtitle: subtitle,
		}
		if m.Response.ProductCategoryId != common.AcademyCategoryID && m.Response.ProductCategoryId != common.SummerCampCategoryID {
			bottomContainer.Button = button
		}
		return bottomContainer
	} else if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CURRENT {
		button, _ := sushi.NewButton(sushi.ButtonTypeSolid)
		button.SetText(ctaText)
		button.SetSize(sushi.ButtonSizeMedium)
		clickAction := sushi.GetClickAction()
		deeplink := &sushi.Deeplink{
			URL: url,
		}
		clickAction.SetDeeplink(deeplink)
		button.SetClickAction(clickAction)
		if m.Response.ProductCategoryId != common.AcademyCategoryID && m.Response.ProductCategoryId != common.SummerCampCategoryID {
			bottomContainer := &sushi.PurchaseWidgetItemBottomContainer{
				Button: button,
			}
			return bottomContainer
		}
	}
	return nil
}

func (m *MembershipDetails) GetMembershipBasicInfoProgressContainer(ctx context.Context) *sushi.PurchaseWidgetItemProgressContainer {

	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	titleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize50)

	title1, _ := sushi.NewTextSnippet("Started on")
	title1.SetColor(titleColor)
	title1.SetFont(titleFont)

	title2, _ := sushi.NewTextSnippet("Expires on")
	title2.SetColor(titleColor)
	title2.SetFont(titleFont)

	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

	bookingTimeObj := time.Unix(m.Response.UserSubscription.StartDate.Seconds, 0)
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	bookingMonthStr := bookingMonth.String()
	subtitle1, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
	subtitle1.SetColor(subtitleColor)
	subtitle1.SetFont(subtitleFont)

	bookingTimeObj = time.Unix(m.Response.UserSubscription.SubscriptionEndDate.Seconds, 0)
	bookingYear, bookingMonth, bookingDay = bookingTimeObj.Date()
	bookingMonthStr = bookingMonth.String()
	subtitle2, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
	subtitle2.SetColor(subtitleColor)
	subtitle2.SetFont(subtitleFont)

	today := time.Now()
	endDate, _ := ptypes.Timestamp(m.Response.UserSubscription.SubscriptionEndDate)
	startDate, _ := ptypes.Timestamp(m.Response.UserSubscription.StartDate)
	endDate = GetLocalDateTime(endDate)
	startDate = GetLocalDateTime(startDate)
	days := endDate.Sub(startDate).Hours() / 24
	usedDays := today.Sub(startDate).Hours() / 24
	progress := int32(usedDays * 100 / days)

	progressBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	progressColor1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint700)
	progressColor2, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint700)

	progressBar := &sushi.ProgressBar{
		MaxValue:       int32(100),
		Progress:       progress,
		ProgressColors: []*sushi.Color{progressColor1, progressColor2},
		BgColor:        progressBgColor,
	}

	progressContainer := &sushi.PurchaseWidgetItemProgressContainer{
		Title1:      title1,
		Title2:      title2,
		Subtitle1:   subtitle1,
		Subtitle2:   subtitle2,
		ProgressBar: progressBar,
	}
	return progressContainer
}

func (m *MembershipDetails) SetBookedSessionDetailsSection(ctx context.Context) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType2,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Details")
	button.SetSize(sushi.ButtonSizeSmall)
	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: "fitso://my_bookings",
	}
	clickAction.SetDeeplink(deeplink)
	button.SetClickAction(clickAction)

	tapPayload := m.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "booked_sessions_details_tap",
	}
	sessionDetailsEvents := sushi.NewClevertapEvents()
	sessionDetailsEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, sessionDetailsEvents)
	button.AddClevertapTrackingItem(trackItem)

	items := []*sushi.FitsoTextSnippetType2SnippetItem{}
	var bookingCount int32
	var totalBookings int32
	for i, val := range m.Response.SportBookingCount {
		if i == 0 {
			title, _ := sushi.NewTextSnippet(fmt.Sprintf("%d", val.Count))
			title.SetAlignment(sushi.TextAlignmentCenter)

			subtitle, _ := sushi.NewTextSnippet(val.SportName)
			subtitle.SetAlignment(sushi.TextAlignmentCenter)

			bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

			item := &sushi.FitsoTextSnippetType2SnippetItem{
				Title:    title,
				Subtitle: subtitle,
				BgColor:  bgColor,
			}
			items = append(items, item)
		} else {
			bookingCount += val.Count
		}
		totalBookings += val.Count
	}
	if len(m.Response.SportBookingCount) > 2 {
		title, _ := sushi.NewTextSnippet(fmt.Sprintf("%d", bookingCount))
		title.SetAlignment(sushi.TextAlignmentCenter)

		subtitle, _ := sushi.NewTextSnippet("Other Games")
		subtitle.SetAlignment(sushi.TextAlignmentCenter)

		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

		item := &sushi.FitsoTextSnippetType2SnippetItem{
			Title:    title,
			Subtitle: subtitle,
			BgColor:  bgColor,
		}
		items = append(items, item)
	} else if len(m.Response.SportBookingCount) == 2 || len(m.Response.SportBookingCount) == 1 {
		title, _ := sushi.NewTextSnippet("0")
		title.SetAlignment(sushi.TextAlignmentCenter)

		subtitle, _ := sushi.NewTextSnippet("Other games")
		subtitle.SetAlignment(sushi.TextAlignmentCenter)

		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

		item := &sushi.FitsoTextSnippetType2SnippetItem{
			Title:    title,
			Subtitle: subtitle,
			BgColor:  bgColor,
		}
		items = append(items, item)
	}

	snippetTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d sessions booked", totalBookings))
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	snippetTitle.SetFont(font)

	snippet := &sushi.FitsoTextSnippetType2Snippet{
		Title:       snippetTitle,
		RightButton: button,
		Items:       items,
	}
	section := &productModel.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType2: snippet,
		SnippetConfig:         snippetConfig,
	}
	m.AddResultSection(section)
}

func (m *MembershipDetails) SetEmptyBookedSessionDetailsSection(ctx context.Context) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	snippetTitle, _ := sushi.NewTextSnippet("0 session booked")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	snippetTitle.SetFont(font)
	snippetTitle.SetColor(color)

	snippetItem := &sushi.SectionHeaderType1Snippet{
		Title: snippetTitle,
	}

	section1 := &productModel.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: snippetItem,
	}

	title, _ := sushi.NewTextSnippet("You have no sessions booked yet!\n Book your first session today")
	titleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	title.SetFont(titleFont)
	title.SetColor(titleColor)
	title.SetAlignment(sushi.TextAlignmentCenter)

	item := sushi.ImageTextSnippetType35SnippetItem{
		Title: title,
	}
	snippet := &sushi.ImageTextSnippetType35Snippet{
		Items: &[]sushi.ImageTextSnippetType35SnippetItem{item},
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	layoutConfig2 := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType35,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	section2 := &productModel.ResultSection{
		LayoutConfig:           layoutConfig2,
		ImageTextSnippetType35: snippet,
		SnippetConfig:          snippetConfig,
	}
	m.AddResultSection(section1)
	m.AddResultSection(section2)
}

func (m *MembershipDetails) SetMembershipDetailsection(ctx context.Context) {
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	vertical_list_items := make([]sushi.VerticalListItem, 0)

	snippetTitle1, _ := sushi.NewTextSnippet("Membership purchased by")
	font300Med, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	color800Grey, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	snippetTitle1.SetFont(font300Med)
	snippetTitle1.SetColor(color800Grey)

	snippetSubtitle1, _ := sushi.NewTextSnippet(m.Response.PurchaseDetails.Name)
	snippetSubtitle1.SetFont(font300Med)
	snippetSubtitle1.SetColor(color800Grey)

	snippetItem1 := &sushi.FitsoTextSnippetType4Item{
		Title:    snippetTitle1,
		Subtitle: snippetSubtitle1,
	}
	section := m.SetSnippetFitsoTextType4(ctx, snippetItem1)
	m.AddResultSection(section)

	snippetTitle2, _ := sushi.NewTextSnippet("Purchased on ")
	snippetTitle2.SetFont(font300Med)
	snippetTitle2.SetColor(color800Grey)

	bookingTimeObj := time.Unix(m.Response.PurchaseDetails.PurchaseDate.Seconds, 0)
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	bookingMonthStr := bookingMonth.String()

	snippetSubtitle2, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
	snippetSubtitle2.SetFont(font300Med)
	snippetSubtitle2.SetColor(color800Grey)

	snippetItem2 := &sushi.FitsoTextSnippetType4Item{
		Title:    snippetTitle2,
		Subtitle: snippetSubtitle2,
	}
	section = m.SetSnippetFitsoTextType4(ctx, snippetItem2)

	separatorColorBottom, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separatorBottom, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColorBottom)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separatorBottom,
	}

	if m.Response.NewProductMembership && m.Response.PurchaseDetails.ActualAmount > 0 {
		m.AddResultSection(section)
		snippetTitle3, _ := sushi.NewTextSnippet("Amount paid")
		snippetTitle3.SetFont(font300Med)
		snippetTitle3.SetColor(color800Grey)

		snippetSubtitle3, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", m.Response.PurchaseDetails.ActualAmount))
		snippetSubtitle3.SetFont(font300Med)
		snippetSubtitle3.SetColor(color800Grey)

		font200Reg, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		color600Grey, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)

		snippetItem3 := &sushi.FitsoTextSnippetType4Item{
			Title:    snippetTitle3,
			Subtitle: snippetSubtitle3,
		}
		if len(m.Response.PaymentMethod) > 0 {
			snippetTitle1, _ := sushi.NewTextSnippet(m.Response.PaymentMethod)
			snippetTitle1.SetFont(font200Reg)
			snippetTitle1.SetColor(color600Grey)
			snippetItem3.Subtitle1 = snippetTitle1
		}
		section = m.SetSnippetFitsoTextType4(ctx, snippetItem3)

		if m.Response.MembershipStatus == SUBSCRIPTION_STATUS_CANCELLED {

			m.AddResultSection(section)
			snippetTitleSublist1, _ := sushi.NewTextSnippet("Grand Total")
			snippetTitleSublist1.SetFont(font200Reg)
			snippetTitleSublist1.SetColor(color600Grey)

			snippetSubtitleSublist1, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", m.Response.PurchaseDetails.ActualAmount))
			snippetSubtitleSublist1.SetFont(font200Reg)
			snippetSubtitleSublist1.SetColor(color600Grey)

			snippetItemSublist1 := &sushi.FitsoTextSnippetType5Item{
				Title:    snippetTitleSublist1,
				Subtitle: snippetSubtitleSublist1,
			}
			vertical_list_item1 := m.SetSnippetFitsoTextType5Sublist(ctx, snippetItemSublist1)
			vertical_list_items = append(vertical_list_items, *vertical_list_item1)

			endDate, _ := ptypes.Timestamp(m.Response.CancellationDetails.CreatedAt)
			startDate, _ := ptypes.Timestamp(m.Response.UserSubscription.StartDate)
			startDate = GetLocalDateTime(startDate)
			endDate = GetLocalDateTime(endDate)
			endDate = Bod(endDate)
			startDate = Bod(startDate)
			days := endDate.Sub(startDate).Hours() / 24
			var remainingDays string

			if days >= 1 {
				days = days + 1
				remainingDays = fmt.Sprintf("Cost of service used (%d days)", int(days))
			} else if days >= 0 {
				remainingDays = fmt.Sprintf("Cost of service used (%d day)", 1)
			} else {
				remainingDays = fmt.Sprintf("Cost of service used (%d days)", 0)
			}
			snippetTitleSublist2, _ := sushi.NewTextSnippet(remainingDays)
			snippetTitleSublist2.SetFont(font200Reg)
			snippetTitleSublist2.SetColor(color600Grey)
			var amount string
			_, floatDigits := math.Modf(float64(m.Response.PurchaseDetails.ActualAmount - m.Response.CancellationDetails.Amount))
			if floatDigits != 0 {
				amount = fmt.Sprintf("₹%.2f", m.Response.PurchaseDetails.ActualAmount-m.Response.CancellationDetails.Amount)
			} else {
				amount = fmt.Sprintf("₹%.f", m.Response.PurchaseDetails.ActualAmount-m.Response.CancellationDetails.Amount)
			}
			snippetSubtitleSublist2, _ := sushi.NewTextSnippet(amount)
			snippetSubtitleSublist2.SetFont(font200Reg)
			snippetSubtitleSublist2.SetColor(color600Grey)

			separatorColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint200)
			if isNewColorSupported {
				separatorColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint200)
			}
			separator := &sushi.TextSnippet{
				Type:  "line",
				Color: separatorColor,
			}

			snippetItemSublist2 := &sushi.FitsoTextSnippetType5Item{
				Title:               snippetTitleSublist2,
				Subtitle:            snippetSubtitleSublist2,
				BottomSeparatorData: separator,
			}
			vertical_list_item2 := m.SetSnippetFitsoTextType5Sublist(ctx, snippetItemSublist2)
			vertical_list_items = append(vertical_list_items, *vertical_list_item2)

			snippetTitleSublist3, _ := sushi.NewTextSnippet("Total Refund")
			font200Med, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			color900Grey, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

			snippetTitleSublist3.SetFont(font200Med)
			snippetTitleSublist3.SetColor(color900Grey)
			var refundTotal string
			_, float := math.Modf(float64(m.Response.CancellationDetails.Amount))
			if float != 0 {
				refundTotal = fmt.Sprintf("₹%.2f", m.Response.CancellationDetails.Amount)
			} else {
				refundTotal = fmt.Sprintf("₹%.f", m.Response.CancellationDetails.Amount)
			}
			snippetSubtitleSublist3, _ := sushi.NewTextSnippet(refundTotal)
			snippetSubtitleSublist3.SetFont(font200Med)
			snippetSubtitleSublist3.SetColor(color900Grey)

			snippetItemSublist3 := &sushi.FitsoTextSnippetType5Item{
				Title:    snippetTitleSublist3,
				Subtitle: snippetSubtitleSublist3,
			}
			vertical_list_item3 := m.SetSnippetFitsoTextType5Sublist(ctx, snippetItemSublist3)
			vertical_list_items = append(vertical_list_items, *vertical_list_item3)

			vertical_list_border, _ := sushi.NewBorderSnippet()
			vertical_list_border_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
			if isNewColorSupported {
				vertical_list_border_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
			}
			vertical_list_border.SetBorderColor(vertical_list_border_color)
			vertical_list_border.SetBorderType(sushi.BorderTypeRounded)
			vertical_list_border.SetBorderRadius(sushi.BorderRadius8)

			vertical_list_bg_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint50)
			if isNewColorSupported {
				vertical_list_bg_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint50)
			}
			vertical_list_type_1 := &sushi.VerticalListType1{
				Items:      vertical_list_items,
				BgColor:    vertical_list_bg_color,
				BorderData: vertical_list_border,
			}

			vertical_list_type_1_layout_config := &sushi.LayoutConfig{
				SnippetType: sushi.VerticalListSnipppetType1,
				LayoutType:  sushi.LayoutTypeGrid,
			}
			vertical_title_subtitle := &sushi.FitsoBottomSheetType1ResultSection{
				LayoutConfig:      vertical_list_type_1_layout_config,
				VerticalListType1: vertical_list_type_1,
			}

			snippetItem6 := &sushi.FitsoTextSnippetType4Item{}
			snippetTitle6, _ := sushi.NewTextSnippet("Refund subtotal")
			snippetTitle6.SetFont(font300Med)
			snippetTitle6.SetColor(color800Grey)

			snippetSubtitle6, _ := sushi.NewTextSnippet(refundTotal)
			snippetSubtitle6.SetFont(font300Med)
			snippetSubtitle6.SetColor(color800Grey)

			verticalExpandedSubtitles := make([]*sushi.TextSnippet, 0)
			bookingTimeObj := time.Unix(m.Response.CancellationDetails.CreatedAt.Seconds, 0)
			bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
			bookingMonthStr := bookingMonth.String()

			snippetSubtitle8, _ := sushi.NewTextSnippet(fmt.Sprintf("Cancelled on %d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
			snippetSubtitle8.SetFont(font200Reg)
			color700Grey, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			snippetSubtitle8.SetColor(color700Grey)
			verticalExpandedSubtitles = append(verticalExpandedSubtitles, snippetSubtitle8)

			bookingTimeObj = time.Unix(m.Response.CancellationDetails.CompletedAt.Seconds, 0)
			bookingYear, bookingMonth, bookingDay = bookingTimeObj.Date()
			bookingMonthStr = bookingMonth.String()
			snippetSubtitle9, _ := sushi.NewTextSnippet(fmt.Sprintf("Refund intitated on %d %s %d", bookingDay, bookingMonthStr[:3], bookingYear))
			snippetSubtitle9.SetFont(font200Reg)
			snippetSubtitle9.SetColor(color700Grey)
			verticalExpandedSubtitles = append(verticalExpandedSubtitles, snippetSubtitle9)

			snippetSubtitle10, _ := sushi.NewTextSnippet("It may take upto 48 hrs (from the refund initiated date) to reflect on your account. In case the payment does not reflect in your account, please check with your bank for updates.")
			snippetColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			snippetSubtitle10.SetFont(font200Reg)
			snippetSubtitle10.SetColor(snippetColor)
			verticalExpandedSubtitles = append(verticalExpandedSubtitles, snippetSubtitle10)

			snippetSubtitle7, _ := sushi.NewTextSnippet("")
			if m.Response.CancellationDetails.Status == 3 {
				snippetSubtitle7, _ = sushi.NewTextSnippet("Refund successfully Initiated by bank")
				titleColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
				if isNewColorSupported {
					titleColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
				}
				snippetSubtitle7.SetFont(font200Reg)
				snippetSubtitle7.SetColor(titleColor)

			} else {
				snippetSubtitle7, _ = sushi.NewTextSnippet("Refund under process")
				font200Medium, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				snippetSubtitle7.SetFont(font200Medium)
				snippetSubtitle7.SetColor(titleColor)
			}

			snippetItem6 = &sushi.FitsoTextSnippetType4Item{
				Title:        snippetTitle6,
				Subtitle:     snippetSubtitle6,
				Subtitle1:    snippetSubtitle7,
				ExpandedData: vertical_title_subtitle,
				IsExpanded:   false,
			}

			if m.Response.CancellationDetails.Status == 3 {
				snippetItem6.VerticalExpandedSubtitles = verticalExpandedSubtitles
			}

			if len(m.Response.PaymentMethod) > 0 {
				snippetTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Refund initiated to %s", m.Response.PaymentMethod))
				snippetTitle.SetFont(font200Reg)
				snippetTitle.SetColor(color600Grey)
				snippetItem6.Subtitle2 = snippetTitle
			}

			section = m.SetSnippetFitsoTextType4(ctx, snippetItem6)

			section.SnippetConfig = snippetConfig
			m.AddResultSection(section)

		} else {
			section.SnippetConfig = snippetConfig
			m.AddResultSection(section)
		}
	} else {
		section.SnippetConfig = snippetConfig
		m.AddResultSection(section)
	}

}

func (m *MembershipDetails) SetSnippetFitsoTextType4(ctx context.Context, snippetItem *sushi.FitsoTextSnippetType4Item) *productModel.ResultSection {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType4,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	fitso_text_snippet_type_4_items := make([]*sushi.FitsoTextSnippetType4Item, 0)
	fitso_text_snippet_type_4_items = append(fitso_text_snippet_type_4_items, snippetItem)
	fitso_text_snippet_type_4 := &sushi.FitsoTextType4{
		Items: fitso_text_snippet_type_4_items,
	}
	section := &productModel.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType4: fitso_text_snippet_type_4,
		//SnippetConfig:         snippetConfig,
	}
	return section
}

func (m *MembershipDetails) SetSnippetFitsoTextType5Sublist(ctx context.Context, snippetItem *sushi.FitsoTextSnippetType5Item) *sushi.VerticalListItem {
	fitso_text_snippet_type_5_items_sublist := make([]*sushi.FitsoTextSnippetType5Item, 0)
	fitso_text_snippet_type_5_items_sublist = append(fitso_text_snippet_type_5_items_sublist, snippetItem)
	fitso_text_snippet_type_5_sublist := &sushi.FitsoTextType5{
		Items: fitso_text_snippet_type_5_items_sublist,
	}

	vl_layout_config1 := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType5,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	vertical_list_item := &sushi.VerticalListItem{
		LayoutConfig:          vl_layout_config1,
		FitsoTextSnippetType5: fitso_text_snippet_type_5_sublist,
	}
	return vertical_list_item
}

func (m *MembershipDetails) SetFitsoBenefitsSection(ctx context.Context) {

	snippetTitle, _ := sushi.NewTextSnippet("Benefits of cult")
	snippetTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	snippetTitle.SetFont(snippetTitleFont)

	pointerItems := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint100)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)

	for _, val := range m.Response.FitsoBenifits {
		title, _ := sushi.NewTextSnippet(val.Title)
		title.SetColor(color)
		title.SetFont(font)

		image, _ := sushi.NewImage(val.Image)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(20)
		image.SetWidth(20)

		item := sushi.ImageTextSnippetType30SnippetItem{
			Image: image,
			Title: title,
		}
		pointerItems = append(pointerItems, item)
	}

	pointerSnippet := &sushi.ImageTextSnippetType30Snippet{
		Items: &pointerItems,
	}

	pointerLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	pointerLayout := &sushi.AccordionSnippetType4SnippetItem{
		LayoutConfig:           pointerLayoutConfig,
		ImageTextSnippetType30: pointerSnippet,
	}

	snippet := &sushi.AccordionSnippetType4Snippet{
		Title: snippetTitle,
		Items: []*sushi.AccordionSnippetType4SnippetItem{pointerLayout},
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.AccordionSnippetType4,
	}

	section := &productModel.ResultSection{
		LayoutConfig:                 layoutConfig,
		AccordionSnippetType4Snippet: snippet,
		SnippetConfig:                snippetConfig,
	}
	m.AddResultSection(section)
}

func (m *MembershipDetails) SetPurchaseHistorySection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	title, _ := sushi.NewTextSnippet("Purchase history")
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetFont(font)
	title.SetColor(color)
	bookingTimeObj := time.Unix(m.Response.MemberSince.Seconds, 0)
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	bookingMonthStr := bookingMonth.String()
	var text string
	if bookingTimeObj.Sub(time.Now()).Seconds() < 0 {
		text = fmt.Sprintf("Member since %d %s %d", bookingDay, bookingMonthStr[:3], bookingYear)
	} else {
		text = fmt.Sprintf("Membership starts %d %s %d", bookingDay, bookingMonthStr[:3], bookingYear)
	}

	subtitle, _ := sushi.NewTextSnippet(text)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	subtitle.SetFont(font)
	subtitle.SetColor(color)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("See history")
	button.SetSize(sushi.ButtonSizeSmall)
	clickAction := sushi.GetClickAction()

	payload := structs.MembershipDetailsRequest{
		ProductId: m.ProductId,
		UserId:    m.UserId,
	}
	params, _ := json.Marshal(payload)
	deeplink := &sushi.Deeplink{
		URL:            util.GetPurchaseHistoryDeeplink(),
		PostbackParams: string(params),
	}
	clickAction.SetDeeplink(deeplink)
	button.SetClickAction(clickAction)

	tapPayload := m.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "purchase_history_tap",
	}
	seeHistoryEvents := sushi.NewClevertapEvents()
	seeHistoryEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, seeHistoryEvents)
	button.AddClevertapTrackingItem(trackItem)

	item := &sushi.FitsoTextSnippetType3SnippetItem{
		Title:       title,
		Subtitle:    subtitle,
		RightButton: button,
	}
	snippet := &sushi.FitsoTextSnippetType3Snippet{
		Items: []*sushi.FitsoTextSnippetType3SnippetItem{item},
	}
	section := &productModel.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType3: snippet,
		SnippetConfig:         snippetConfig,
	}
	m.AddResultSection(section)
}

func (m *MembershipDetails) SetAcademyFacilitiesSessions(ctx context.Context) {
	for _, facilitySessionDetails := range m.Response.FacilitySessionDetails {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoTextSnippetType3,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
		snippetConfig := &sushi.SnippetConfig{
			BottomSeparator: separator,
		}
		titleText := fmt.Sprintf("Center - %s", facilitySessionDetails.FacilityDetails.SportName)
		if m.Response.ProductCategoryId == common.SummerCampCategoryID {
			titleText = fmt.Sprintf("%s · %s", facilitySessionDetails.FacilityDetails.SportName, facilitySessionDetails.SessionTiming)
		}

		title, _ := sushi.NewTextSnippet(titleText)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		title.SetFont(font)
		title.SetColor(color)

		subtitle, _ := sushi.NewTextSnippet(facilitySessionDetails.FacilityDetails.DisplayName)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle.SetFont(font)
		subtitle.SetColor(color)

		button, _ := sushi.NewButton(sushi.ButtontypeText)
		button.SetText("Center details")
		button.SetSize(sushi.ButtonSizeSmall)

		headerCityId := util.GetCityIDFromContext(ctx)
		clickAction := sushi.GetClickAction()
		if facilitySessionDetails.FacilityDetails.CityIdV2 == headerCityId {
			deeplink := &sushi.Deeplink{
				URL: facilitySessionDetails.FacilityDetails.Deeplink,
			}
			clickAction.SetDeeplink(deeplink)
		} else {
			customAlert := util.CustomAlertForCityChange(ctx)
			clickAction.SetCustomAlertAction(customAlert)
		}
		button.SetClickAction(clickAction)

		item := &sushi.FitsoTextSnippetType3SnippetItem{
			Title:       title,
			Subtitle:    subtitle,
			RightButton: button,
		}
		snippet := &sushi.FitsoTextSnippetType3Snippet{
			Items: []*sushi.FitsoTextSnippetType3SnippetItem{item},
		}
		section := &productModel.ResultSection{
			LayoutConfig:          layoutConfig,
			FitsoTextSnippetType3: snippet,
			SnippetConfig:         snippetConfig,
		}
		m.AddResultSection(section)
	}
}

func (m *MembershipDetails) SetPreferredCenterSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	title, _ := sushi.NewTextSnippet("Preferred center")
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetFont(font)
	title.SetColor(color)

	subtitle, _ := sushi.NewTextSnippet(m.Response.PreferredFacility.DisplayName)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	subtitle.SetFont(font)
	subtitle.SetColor(color)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Center details")
	button.SetSize(sushi.ButtonSizeSmall)

	header_city_id := util.GetCityIDFromContext(ctx)
	clickAction := sushi.GetClickAction()
	if m.Response.PreferredFacility.CityIdV2 == header_city_id {
		deeplink := &sushi.Deeplink{
			URL: m.Response.PreferredFacility.Deeplink,
		}
		clickAction.SetDeeplink(deeplink)
	} else {
		customAlert := util.CustomAlertForCityChange(ctx)
		clickAction.SetCustomAlertAction(customAlert)
	}
	button.SetClickAction(clickAction)

	tapPayload := m.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "preferred_center_tap",
	}
	centerDetailsEvents := sushi.NewClevertapEvents()
	centerDetailsEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, centerDetailsEvents)
	button.AddClevertapTrackingItem(trackItem)

	item := &sushi.FitsoTextSnippetType3SnippetItem{
		Title:       title,
		Subtitle:    subtitle,
		RightButton: button,
	}
	snippet := &sushi.FitsoTextSnippetType3Snippet{
		Items: []*sushi.FitsoTextSnippetType3SnippetItem{item},
	}
	section := &productModel.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType3: snippet,
		SnippetConfig:         snippetConfig,
	}
	m.AddResultSection(section)
}

func (m *MembershipDetails) SetFooterButtonsSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType10,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	items := make([]sushi.V2ImageTextSnippetType10SnippetItem, 0)
	endDate, _ := ptypes.Timestamp(m.Response.UserSubscription.SubscriptionEndDate)
	endDate = GetLocalDateTime(endDate)
	checkLastDay := Bod(endDate).Sub(Bod(time.Now())).Hours() / 24

	today := time.Now()
	today = Bod(today)
	startDate, _ := ptypes.Timestamp(m.Response.UserSubscription.StartDate)
	startDate = GetLocalDateTime(startDate)
	usedDays := today.Sub(startDate).Hours() / 24

	var enable30DayRefundPolicy int64 = 1635851753 // 2 nov 2021
	var enable7DayRefundPolicy int64 = 1640044740  // 20 dec 2021 23:59:00

	var lastRefundOffering int64 = 1641493740

	purchaseDate := m.Response.PurchaseDetails.PurchaseDate.Seconds
	var showCancelMembership bool
	if purchaseDate <= enable30DayRefundPolicy {
		showCancelMembership = false
	} else if purchaseDate > enable30DayRefundPolicy && purchaseDate <= enable7DayRefundPolicy && usedDays <= 30 {
		showCancelMembership = false
	} else if purchaseDate > enable7DayRefundPolicy && usedDays <= 7 && purchaseDate < lastRefundOffering {
		showCancelMembership = true
	} else {
		showCancelMembership = false
	}

	if m.Response.NewProductMembership &&
		checkLastDay != 0 &&
		m.Response.MembershipStatus != SUBSCRIPTION_STATUS_CANCELLED &&
		m.Response.MembershipStatus != SUBSCRIPTION_STATUS_EXPIRED &&
		m.Response.ProductCategoryId != common.AcademyCategoryID &&
		m.Response.ProductCategoryId != common.SummerCampCategoryID &&
		showCancelMembership {
		// for cancel Membership
		if button := m.GetCancelMembershipButtonSnippet(ctx); button != nil {
			items = append(items, *button)
		}
	}
	// for need help
	if button := m.GetNeedHelpButtonSnippet(ctx); button != nil {
		items = append(items, *button)
	}

	snippet := &sushi.V2ImageTextSnippetType10Snippet{
		Items: &items,
	}
	section := &productModel.ResultSection{
		LayoutConfig:         layoutConfig,
		V2ImageSnippetType10: snippet,
	}
	m.AddResultSection(section)
}

func (m *MembershipDetails) GetCancelMembershipButtonSnippet(ctx context.Context) *sushi.V2ImageTextSnippetType10SnippetItem {
	title, _ := sushi.NewTextSnippet("Cancel membership")
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetColor(color)
	title.SetFont(font)

	icon, _ := sushi.NewIcon(sushi.RightIcon, color)
	icon.SetSize(sushi.IconSize18)

	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: util.GetCancelMembershipDeeplink(m.ProductId, m.UserId),
	}
	clickAction.SetDeeplink(deeplink)
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)

	tapPayload := m.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "cancel_membership_tap",
	}
	needHelpEvents := sushi.NewClevertapEvents()
	needHelpEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, needHelpEvents)

	item := &sushi.V2ImageTextSnippetType10SnippetItem{
		Title:             title,
		RightIcon:         icon,
		ClickAction:       clickAction,
		BottomSeparator:   separator,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	return item
}

func (m *MembershipDetails) GetNeedHelpButtonSnippet(ctx context.Context) *sushi.V2ImageTextSnippetType10SnippetItem {
	title, _ := sushi.NewTextSnippet("Membership related queries")
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetColor(color)
	title.SetFont(font)

	icon, _ := sushi.NewIcon(sushi.RightIcon, color)
	icon.SetSize(sushi.IconSize18)

	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: util.GetCustomerSupportDeeplink(0),
	}
	clickAction.SetDeeplink(deeplink)

	tapPayload := m.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "need_help_tap",
	}
	needHelpEvents := sushi.NewClevertapEvents()
	needHelpEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, needHelpEvents)

	item := &sushi.V2ImageTextSnippetType10SnippetItem{
		Title:             title,
		RightIcon:         icon,
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	return item
}

func (m *MembershipDetails) SetClevertapTracking(ctx context.Context) {
	commonPayload := m.GetCommonPayloadForTracking(ctx)
	landingEName := &sushi.EnameData{
		Ename: "membership_details_landing",
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	dismissEName := &sushi.EnameData{
		Ename: "membership_details_back_button_tap",
	}
	pageDismissEvent := sushi.NewClevertapEvents()
	pageDismissEvent.SetPageDismiss(dismissEName)
	pageDismissTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageDismissEvent)

	m.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem, pageDismissTrackItem}
}

func (m *MembershipDetails) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["source"] = "membership_details"
	commonPayload["membership_type"] = m.GetMembershipTypeForTrackingPayload()
	commonPayload["member"] = m.UserId
	return commonPayload
}

func (m *MembershipDetails) GetMembershipTypeForTrackingPayload() string {
	switch m.Response.MembershipStatus {
	case SUBSCRIPTION_STATUS_CANCELLED:
		return "cancelled"
	case SUBSCRIPTION_STATUS_CURRENT:
		return "active"
	case SUBSCRIPTION_STATUS_OLDENTRY:
		return "expired"
	case SUBSCRIPTION_STATUS_RENEWED:
		return "active"
	case SUBSCRIPTION_STATUS_UPCOMING:
		return "yet to start"
	case SUBSCRIPTION_STATUS_EXPIRED:
		return "expired"
	default:
		return ""
	}
}

func GetLocalDateTime(date time.Time) time.Time {
	location, _ := time.LoadLocation("Asia/Kolkata") //IST
	return date.In(location)
}

func SubscriptionDaysRemaining(days int) string {
	if days >= 2 {
		return fmt.Sprintf("Expiring in %d days", days)
	} else if days >= 1 {
		return "Expiring tomorrow"
	} else {
		return "Expiring today"
	}
}

func Bod(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, t.Location())
}
