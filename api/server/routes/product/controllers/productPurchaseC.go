package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	productModels "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type PurchasePageeData struct {
	ReferralApplied          bool
	ReferralDiscountedAmount int32
	ShowNewColorScheme       bool
	ShowNewBgImage           bool
	HorizontalOrientation    bool
}

type PurchasePageTemplate struct {
	Sections          []*productModels.ResultSection  `json:"results,omitempty"`
	BackgroundImage   *sushi.Image                    `json:"bg_image,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout `json:"footer,omitempty"`
	EmptyView         *sushi.EmptyViewType1Layout     `json:"empty_view,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem          `json:"clever_tap_tracking,omitempty"`
	PageData          PurchasePageeData               `json:"-"`
	JumboTracking     []*jumbo.Item                   `json:"jumbo_tracking,omitempty"`
	FloatingButton    *sushi.Button                   `json:"floating_button,omitempty"`
}

const (
	holiSaleDateUnix    = 1647455401
	holiEndSaleDateUnix = 1647800999
)

func GetPurchasePageDetailsC(c *gin.Context) {
	productService := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetPurchasePageDetailsRequest)
	var templateResponse PurchasePageTemplate
	purchasePageDetails, err := productService.GetPurchasePageDetails(ctx, &productPB.GetPurchasPageDetailsRequest{
		IsProAvail:       false,
		IsNoCostEmiAvail: false,
		ProductId:        json.ProductId,
	})
	if err != nil {
		log.Println("Error in controller GetPurchasePageDetailsC while getting purchase page details, Error: ", err)
		c.JSON(
			http.StatusOK,
			models.StatusFailure(apiCommon.UNEXPECTED_ERROR),
		)
		return
	} else if purchasePageDetails.Status != nil && purchasePageDetails.Status.Status == apiCommon.FAILED {
		log.Println("Failed in controller GetPurchasePageDetailsC while getting purchase page details, Error: ", err)
		c.JSON(
			http.StatusOK,
			purchasePageDetails.Status,
		)
		return
	} else {
		totalProducts := 0
		for _, productList := range purchasePageDetails.ProductMap {
			totalProducts = totalProducts + len(productList.Products)
		}
		if totalProducts > 0 {
			templateResponse.SetColorSchemeStatus(ctx)
			templateResponse.SetHeaderSection(ctx, purchasePageDetails)
			templateResponse.SetMembershipBenefitsSection(c, purchasePageDetails)
			if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
				templateResponse.SetMultipleProducts(ctx, purchasePageDetails)
				//templateResponse.SetFooterImage(ctx) to be set after image update from product design team.
				templateResponse.SetBgImage(ctx, purchasePageDetails)
				if featuresupport.SupportSingleKeyProduct(ctx) {
					templateResponse.SetFloatingButton(ctx)
				}

			} else {
				templateResponse.SetLinksSection(purchasePageDetails)
				templateResponse.SetBgImage(ctx, purchasePageDetails)
				templateResponse.SetFooterSection(ctx, purchasePageDetails)
			}
		} else {
			templateResponse.SetEmptyView(purchasePageDetails)
		}
		templateResponse.SetClevertapTracking(ctx)
		templateResponse.SetJumboTracking(ctx)
	}

	c.JSON(
		http.StatusOK,
		templateResponse,
	)
}

func (p *PurchasePageTemplate) SetColorSchemeStatus(ctx context.Context) {
	cl := util.GetUserServiceClient()
	loggedInUser := util.GetUserIDFromContext(ctx)
	p.PageData.ShowNewColorScheme = false
	p.PageData.ShowNewBgImage = false

	userCategories, err := cl.GetUserCategoriesFromCacheForMasterKeySuggestionBanners(ctx, &userPB.Empty{})
	if err != nil {
		log.Println("Error in getting userCategoryIds from cache for userId:", loggedInUser)
		return
	}

	if featuresupport.SupportsMultipleFeaturedProducts(ctx) && len(userCategories.UserCategoryIds) > 0 {
		for _, val := range userCategories.UserCategoryIds {
			if val == 11 || val == 6 || val == 22 || val == 2 || val == 7 || val == 8 || val == 5 || val == 9 {
				p.PageData.ShowNewColorScheme = true
				break
			}
		}
	}
	if p.PageData.ShowNewColorScheme && featuresupport.SupportsPurchasePageDynamicBgImage(ctx) {
		p.PageData.ShowNewBgImage = true
	}

}

func (p *PurchasePageTemplate) SetHeaderSection(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	loggedInUser := util.GetUserIDFromContext(ctx)

	fitsoHeaderSnippetType1Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoHeaderSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	title, _ := sushi.NewTextSnippet(purchasePageData.ProductName)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetFont(font)
	title.SetKerning(2)

	if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		title.SetKerning(3)
		color, _ := sushi.NewColor(sushi.ColorTypeCider, sushi.ColorTint500)
		if featuresupport.SupportSingleKeyProduct(ctx) {
			color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		}
		title.SetColor(color)
	}

	image, _ := sushi.NewImage(purchasePageData.FitsoLogo)
	image.SetAspectRatio(2.21)

	subtitle1, _ := sushi.NewTextSnippet(purchasePageData.MinAgeSubtitle)
	subtitle2, _ := sushi.NewTextSnippet(purchasePageData.ProductDetails)

	if featuresupport.SupportSingleKeyProduct(ctx) {
		color, _ := sushi.NewColor(sushi.ALERT_DARK_THEME, sushi.ColorTint500)
		subtitle1.SetColor(color)
	}

	pcl := util.GetProductClient()

	if loggedInUser > 0 {
		reqData := &productPB.Empty{}
		response, err := pcl.GetReferralEligiblity(ctx, reqData)
		if err == nil {
			if response.EligibleForReferral && response.ReferralUserId > 0 {
				discountAmtReq := &productPB.ApplyVoucherRequest{
					CustomAmount: purchasePageData.ProductRetailPrice,
				}
				discountAmtResp, err := pcl.GetDiscountAmountForReferral(ctx, discountAmtReq)
				if err != nil {
					log.Println("func:SetHeaderSection, error in getting discounted amount: ", err)
				} else {
					discountedAmount := int32(purchasePageData.ProductRetailPrice) - discountAmtResp.DiscountAmount
					subtitle2, _ = sushi.NewTextSnippet(fmt.Sprintf("{yellow-500|<regular-100|Referral applied>}\n<regular-400|~~₹%d~~>\n₹%d for 3 month membership", int32(purchasePageData.ProductRetailPrice), discountedAmount))
					subtitle2.SetAlignment(sushi.TextAlignmentCenter)
					subtitle2.SetIsMarkdown(1)

					p.PageData.ReferralApplied = true
					p.PageData.ReferralDiscountedAmount = discountedAmount
				}
			}
		}
	}

	tag, _ := sushi.NewTag(purchasePageData.CityValidity)
	if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		tag, _ = sushi.NewTag(purchasePageData.CityName) //setting for right tag
		tag.Size = "small"
	}
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	tagTextColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	tag.Title.SetColor(tagTextColor)
	tag.SetBgColor(tagBgColor)

	fitsoHeaderSnippetType1 := &sushi.FitsoHeaderSnippetType1Snippet{
		//Title:     title,
		Image:     image,
		Subtitle1: subtitle1,
	}

	if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		//fitsoHeaderSnippetType1.RightTag = tag
		if !p.PageData.ShowNewBgImage {
			fitsoHeaderSnippetType1.Image = image
			//fitsoHeaderSnippetType1.RightTag = tag
		}
	} else {
		fitsoHeaderSnippetType1.Tag = tag
		fitsoHeaderSnippetType1.Image = image
		fitsoHeaderSnippetType1.Subtitle2 = subtitle2
	}

	if purchasePageData.EmiText != "" && !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		buttonColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonIcon, _ := sushi.NewIcon(sushi.RightArrowOutlineIcon, buttonColor)
		button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
		button.SetText(purchasePageData.EmiText)
		button.SetColor(buttonColor)
		button.SetSuffixIcon(buttonIcon)
		fitsoHeaderSnippetType1.Button = button
	}

	section := &productModels.ResultSection{
		LayoutConfig:            fitsoHeaderSnippetType1Layout,
		FitsoHeaderSnippetType1: fitsoHeaderSnippetType1,
	}
	p.AddSection(section)
}

func (p *PurchasePageTemplate) SetMembershipBenefitsSection(c *gin.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	ctx := util.PrepareGRPCMetaData(c)
	layoutConfig := &sushi.LayoutConfig{
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
	}
	if featuresupport.SupportsMultipleFeaturedProducts(c) {
		layoutConfig.SnippetType = sushi.FitsoTextSnippetSnippetType19
		layoutConfig.SectionCount = 1
	} else if featuresupport.SupportsNewPurchaseBenefitsSnippet(c) {
		layoutConfig.SnippetType = sushi.V2ImageTextSnippetType43
	} else {
		layoutConfig.SnippetType = sushi.ImageTextSnippetType41
	}

	var items []sushi.ImageTextSnippetType41SnippetItem
	var newItems []sushi.V2ImageTextSnippetType43SnippetItem
	var newItems1 []sushi.V2ImageTextType14SnippetItem

	for _, benefit := range purchasePageData.Benefits {

		benefitTitle, _ := sushi.NewTextSnippet(benefit.BenefitTitle)
		benefitFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		benefitTitle.SetFont(benefitFont)
		if featuresupport.SupportSingleKeyProduct(c) {
			benefitColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			benefitTitle.SetColor(benefitColor)
		}

		benefitImage, _ := sushi.NewImage(benefit.Image)
		benefitImage.SetHeight(40)
		benefitImage.SetWidth(40)
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			benefitImage.Type = sushi.ImageTypeCircle
			benefitImage.SetHeight(32)
			benefitImage.SetWidth(32)
			item := sushi.V2ImageTextType14SnippetItem{
				Title: benefitTitle,
				Image: benefitImage,
			}
			if benefit.BenefitSubtitle1 != "" {
				suffixColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				icon, _ := sushi.NewIcon("e805", suffixColor)
				benefitTitle.SetSuffixIcon(icon)
				item.Title = benefitTitle
				clickAction := sushi.GetClickAction()
				titleSnippet, _ := sushi.NewTextSnippet(benefit.BenefitSubtitle1)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				titleSnippet.SetColor(titleColor)
				titleSnippet.SetFont(titleFont)
				subtitleSnippet, _ := sushi.NewTextSnippet("OK")
				subtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				subtitleSnippet.SetColor(subtitleColor)
				subtitleSnippet.SetFont(subtitleFont)
				bgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
				showToolTip := &sushi.ShowTooltip{
					Title:    titleSnippet,
					Subtitle: subtitleSnippet,
					BgColor:  bgColor,
				}
				clickAction.SetClickActionType(sushi.ClickActionShowToolTip)
				clickAction.SetShowTooltip(showToolTip)
				item.ClickAction = clickAction
				trackingItem := p.GetToolTipTrackingItem(ctx, benefit.BenefitTitle)
				item.ClevertapTracking = []*sushi.ClevertapItem{trackingItem}
			}
			newItems1 = append(newItems1, item)
		} else {
			benefitSubtitle1, _ := sushi.NewTextSnippet(benefit.BenefitSubtitle1)
			benefitSubtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			benefitSubtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			benefitSubtitle1.SetFont(benefitSubtitle1Font)
			benefitSubtitle1.SetColor(benefitSubtitle1Color)
			benefitImage.SetAspectRatio(1)
			if featuresupport.SupportsNewPurchaseBenefitsSnippet(c) {
				item := sushi.V2ImageTextSnippetType43SnippetItem{
					Title:     benefitTitle,
					SubTitle1: benefitSubtitle1,
					Image:     benefitImage,
				}
				newItems = append(newItems, item)
			} else {
				item := sushi.ImageTextSnippetType41SnippetItem{
					Title:     benefitTitle,
					SubTitle1: benefitSubtitle1,
					Image:     benefitImage,
				}
				items = append(items, item)
			}
		}
	}

	section := &productModels.ResultSection{
		LayoutConfig: layoutConfig,
	}
	if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		snippetV2 := &sushi.V2ImageTextType14Snippet{
			Item: &newItems1,
		}
		layoutConfigV2 := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType14,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 2,
		}
		layoutV2 := sushi.V2ImageTextSnippetType14Layout{
			LayoutConfig:             layoutConfigV2,
			V2ImageTextSnippetType14: snippetV2,
		}
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		snippetTitle, _ := sushi.NewTextSnippet("Benefits of cultpass PLAY")
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		snippetTitle.SetFont(font)
		snippetTitle.SetColor(color)
		snippetTitle.Alignment = sushi.TextAlignmentCenter
		snippet := &sushi.FitsoImageTextSnippetType19Snippet{
			CornerRadius: 12,
			BorderColor:  borderColor,
			Title:        snippetTitle,
			Items:        &[]sushi.V2ImageTextSnippetType14Layout{layoutV2},
		}
		if featuresupport.SupportSingleKeyProduct(ctx) {
			gradient := sushi.Gradient{}
			color1, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint100)
			color1.Transparency = 1
			color2, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			color3, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			color4, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			color5, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			color6, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			gradient.Colors = &[]sushi.Color{*color1, *color2, *color3, *color4, *color5, *color6}
			snippet.Gradient = &gradient
			snippet.BorderWidth = 0
			snippet.CornerRadius = 0

			borderColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint800)
			snippet.BorderColor = borderColor

			snippetTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			snippetTitle.SetColor(snippetTitleColor)
		}

		section.FitsoImageTextSnippetType19Snippet = snippet
	} else if featuresupport.SupportsNewPurchaseBenefitsSnippet(c) {
		snippet := &sushi.V2ImageTextSnippetType43Snippet{
			Items: &newItems,
		}
		section.V2ImageTextSnippetType43 = snippet
	} else {
		snippet := &sushi.ImageTextSnippetType41Snippet{
			Items: &items,
		}
		section.ImageTextSnippetType41 = snippet
	}

	p.AddSection(section)
}

func getProductOfferings(ctx context.Context, productId int32) []string {
	pcl := util.GetProductClient()
	productOfferingsReq := &productPB.GetProductOfferingsReq{
		ProductId: productId,
	}
	response, err := pcl.GetProductOfferings(ctx, productOfferingsReq)
	if err != nil {
		log.Println("func:getProductOfferings, error in getting product offerings for product id: %d, err: ", productId, err)
	} else {
		return response.ProductOfferings
	}
	return []string{}
}

func (p *PurchasePageTemplate) SetLinksSection(purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	var buttonDataItems []sushi.FitsoTextSnippetType1ButtonItem

	rightArrowIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	rightArrowIcon, _ := sushi.NewIcon(sushi.RightArrowOutlineIcon, rightArrowIconColor)

	faqClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	faqClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: purchasePageData.FaqUrl.Text,
		URL:   purchasePageData.FaqUrl.Path,
	})
	faqButtonDataItem := sushi.FitsoTextSnippetType1ButtonItem{
		Type:        sushi.FitsoTextSnippetType1TypeText,
		Text:        purchasePageData.FaqUrl.Text,
		Size:        sushi.FitsoTextSnippetType1ButtonSizeMedium,
		SuffixIcon:  rightArrowIcon,
		ClickAction: faqClickAction,
	}
	buttonDataItems = append(buttonDataItems, faqButtonDataItem)

	tncClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	tncClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: purchasePageData.TncUrl.Text,
		URL:   purchasePageData.TncUrl.Path,
	})
	tncButtonDataItem := sushi.FitsoTextSnippetType1ButtonItem{
		Type:        sushi.FitsoTextSnippetType1TypeText,
		Text:        purchasePageData.TncUrl.Text,
		Size:        sushi.FitsoTextSnippetType1ButtonSizeMedium,
		SuffixIcon:  rightArrowIcon,
		ClickAction: tncClickAction,
	}
	buttonDataItems = append(buttonDataItems, tncButtonDataItem)

	buttonData := &sushi.FitsoTextSnippetType1SnippetButton{
		Orientation: sushi.FitsoTextSnippetType1OrientationHorizontal,
		Items:       &buttonDataItems,
	}
	snippet := &sushi.FitsoTextSnippetType1Snippet{
		ButtonData: buttonData,
	}
	section := &productModels.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType1: snippet,
	}
	p.AddSection(section)
}

func (p *PurchasePageTemplate) SetMultipleProducts(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	loggedInUser := util.GetUserIDFromContext(ctx)
	isNoCostEmiSupported := featuresupport.SupportsNoCostEMI(ctx)
	productMap := purchasePageData.ProductMap
	layout_config := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoPurchaseSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	productCategories := make([]int, 0, len(productMap))
	for k := range productMap {
		productCategories = append(productCategories, int(k))
	}
	sort.Ints(productCategories)

	if featuresupport.SupportsMultipleFeaturedProductsHorizontalLayout(ctx) {
		layout_config.SnippetType = sushi.FitsoPurchaseSnippetType2
	}
	if featuresupport.SupportSingleKeyProduct(ctx) {
		layout_config.SnippetType = sushi.FitsoTextSnippetSnippetType19
	}

	for _, productCategory := range productCategories {
		productCategory := int32(productCategory)
		productList := productMap[productCategory]
		if len(productList.Products) == 0 {
			continue
		}
		singleProduct := false

		if len(productList.Products) == 1 {
			singleProduct = true
		}
		titleText := "cultpass PLAY"
		color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

		if productCategory == 3 {
			if !featuresupport.SupportSingleKeyProduct(ctx) {
				titleText = titleText + " Lite"
			}
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		}

		title, _ := sushi.NewTextSnippet(titleText)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)

		if featuresupport.SupportSingleKeyProduct(ctx) {
			color1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			color2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
			if productCategory == apiCommon.SinglekeyCategoryID {
				color1, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint500)
				color2, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			}
			titleGradient := sushi.Gradient{}
			titleGradient.Colors = &[]sushi.Color{*color1, *color2}
			titleGradient.Direction = sushi.HorizontalDirection
			title.Gradient = &titleGradient
		}

		title.SetFont(font)
		title.SetColor(color)
		title.SetAlignment(sushi.TextAlignmentCenter)
		title.SetKerning(3)

		subtitleText := ""
		if productCategory == apiCommon.MasterkeyCategoryID {
			subtitleText = "Get access to {blue-400|all sports} at all centers"
		} else if productCategory == apiCommon.SinglekeyCategoryID {
			subtitleText = "{blue-400|Badminton only} access at all centers"
		}
		subtitle, _ := sushi.NewTextSnippet(subtitleText)
		color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)

		if featuresupport.SupportSingleKeyProduct(ctx) {
			if productCategory == apiCommon.MasterkeyCategoryID {
				color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			} else if productCategory == apiCommon.SinglekeyCategoryID {
				color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			}
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		}

		subtitle.SetFont(font)
		subtitle.SetColor(color)
		subtitle.SetAlignment(sushi.TextAlignmentCenter)
		subtitle.SetIsMarkdown(1)

		items := make([]*sushi.FitsoPurchaseSnippetType1SnippetItem, 0)
		for _, product := range productList.Products {
			item := &sushi.FitsoPurchaseSnippetType1SnippetItem{
				Id:           product.PId,
				CornerRadius: int32(12),
			}

			title, _ := sushi.NewTextSnippet(strconv.Itoa(int(product.Duration)))
			titleFont, _ := sushi.NewFont(sushi.FontBold, sushi.FontSize900)
			title.SetFont(titleFont)
			item.Title = title

			durationText := product.DurationUnit
			if product.DurationUnit == "month" {
				if product.Duration == 1 {
					durationText = "Month"
				} else {
					durationText = "Months"
				}
			}
			subtitle, _ := sushi.NewTextSnippet(durationText)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			if isNoCostEmiSupported {
				if product.Popularity == 1 {
					titleGradient := sushi.Gradient{}
					color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
					color2, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint400)
					titleGradient.Colors = &[]sushi.Color{*color1, *color2}
					title.Gradient = &titleGradient
					subtitle.Gradient = &titleGradient
				}
			}
			titleTextColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			title.SetColor(titleTextColor)
			subtitle.SetColor(titleTextColor)
			subtitle.SetFont(subtitleFont)
			item.Subtitle = subtitle

			//price
			subtitle1, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(product.Price)))
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			fontType := sushi.FontMedium
			if isNoCostEmiSupported {
				fontType = sushi.FontRegular
				color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			}
			font, _ = sushi.NewFont(fontType, sushi.FontSize200)
			subtitle1.SetFont(font)
			subtitle.SetColor(titleTextColor)

			perMonthPrice := int32(product.Price)
			if product.Duration > 0 {
				perMonthPrice = int32(product.Price) / product.Duration
			}

			if product.Price > product.RetailPrice {
				perMonthPrice = int32(product.RetailPrice) / product.Duration
			}

			subtitle2, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(product.RetailPrice)))
			if isNoCostEmiSupported {
				subtitle2Text := fmt.Sprintf("₹%s{black-500|<semibold-200|/month*>}", strconv.Itoa(int(perMonthPrice)))
				subtitle2, _ = sushi.NewTextSnippet(subtitle2Text)
				subtitle2Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
				subtitle2.SetFont(subtitle2Font)
				subtitle2.SetIsMarkdown(1)
				subtitle2.SetAlignment(sushi.TextAlignmentRight)
			} else {
				font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
				subtitle2.SetFont(font)
			}
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			subtitle2.SetColor(subtitle2Color)

			if product.Price > product.RetailPrice {
				subtitle1.Strikethrough = true
				item.Subtitle2 = subtitle2
			}
			item.Subtitle1 = subtitle1

			perMonthText := "Just ₹" + strconv.Itoa(int(perMonthPrice)) + "/mo"
			if singleProduct {
				perMonthText = " At Just ₹" + strconv.Itoa(int(perMonthPrice)) + "/mo"
			}
			subtitle3, _ := sushi.NewTextSnippet(perMonthText)
			var firstPurchaseDiscountAmount int32
			firstPurchaseExtensionDays := 10
			if productCategory == apiCommon.MasterkeyCategoryID {
				if product.DurationInDays == 365 {
					firstPurchaseDiscountAmount = 1000
				} else if product.DurationInDays == 180 {
					firstPurchaseDiscountAmount = 750
				} else if product.DurationInDays == 90 {
					firstPurchaseDiscountAmount = 500
				}
			} else if productCategory == apiCommon.SinglekeyCategoryID {
				if product.DurationInDays == 90 {
					firstPurchaseDiscountAmount = 100
				}
			}
			if (time.Now().Unix() > apiCommon.SaleStartDateUnix || util.Contains(apiCommon.TestUsersAutoPromoCode, loggedInUser)) && time.Now().Unix() < apiCommon.SaleEndDateUnix {
				if productCategory == apiCommon.MasterkeyCategoryID {
					if product.DurationInDays == 365 {
						firstPurchaseDiscountAmount = 1500
						firstPurchaseExtensionDays = 30
					} else if product.DurationInDays == 180 {
						firstPurchaseDiscountAmount = 1000
						firstPurchaseExtensionDays = 20
					} else if product.DurationInDays == 90 {
						firstPurchaseDiscountAmount = 750
					}
				} else if productCategory == apiCommon.SinglekeyCategoryID {
					if product.DurationInDays == 90 {
						firstPurchaseDiscountAmount = 250
					}
				}
			}
			if !isNoCostEmiSupported {
				s3Text := ""
				if productCategory == apiCommon.MasterkeyCategoryID {
					s3Text = fmt.Sprintf("%s\n\n - Get Flat Rs %d Off on your 1st purchase \n - No Cost EMI Available", perMonthText, firstPurchaseDiscountAmount)
					if product.DurationInDays > 90 {
						s3Text = fmt.Sprintf("%s\n\n - Get Flat Rs %d Off on your 1st purchase \n - Or Get FREE %d Days extension on your 1st purchase\n - No Cost EMI Available", perMonthText, firstPurchaseDiscountAmount, firstPurchaseExtensionDays)
					}
				} else if productCategory == apiCommon.SinglekeyCategoryID {
					s3Text = fmt.Sprintf("%s\n\n - Get Flat Rs %d Off on your 1st purchase\n - 2 complimentry sessions of other sports per month\n - No Cost EMI Available", perMonthText, firstPurchaseDiscountAmount)
				}

				if (time.Now().Unix() > apiCommon.SaleStartDateUnix || util.Contains(apiCommon.TestUsersAutoPromoCode, loggedInUser)) && time.Now().Unix() < apiCommon.SaleEndDateUnix {
					if productCategory == apiCommon.MasterkeyCategoryID {
						s3Text = fmt.Sprintf("%s\n\n - Additional INR. %d Off \n - Additional %d Days extension \n - No Cost EMI Available", perMonthText, firstPurchaseDiscountAmount, firstPurchaseExtensionDays)
						if firstPurchaseExtensionDays == 30 {
							s3Text = fmt.Sprintf("%s\n\n - Additional INR. %d Off \n - Additional 1 month extension \n - No Cost EMI Available", perMonthText, firstPurchaseDiscountAmount)
						}
					} else if productCategory == apiCommon.SinglekeyCategoryID {
						s3Text = fmt.Sprintf("%s\n\n - Additional INR. %d Off \n - 2 complimentry sessions of other sports per month\n - No Cost EMI Available", perMonthText, firstPurchaseDiscountAmount, firstPurchaseExtensionDays)
					}
				}
				subtitle3, _ = sushi.NewTextSnippet(s3Text)
				s3Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint800)
				s3Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
				subtitle3.SetFont(s3Font)
				subtitle3.SetColor(s3Color)
				subtitle3.SetAlignment(sushi.TextAlignmentLeft)
				item.Subtitle3 = subtitle3
			}

			percentDiscount := int((product.Price - product.RetailPrice) * 100 / product.Price)
			percentDiscountText := strconv.Itoa(int(percentDiscount)) + "% off"
			tagTitle, _ := sushi.NewTextSnippet(percentDiscountText)
			tagColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
			tagTitle.SetColor(tagColor)
			if percentDiscount > 0 && !isNoCostEmiSupported {
				item.Tag1 = &sushi.Tag{
					Title: tagTitle,
					Type:  "small",
				}
			}
			if isNoCostEmiSupported {
				rightTitle, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(product.RetailPrice)))
				rightTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
				rightTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
				rightTitle.SetFont(rightTitleFont)
				rightTitle.SetColor(rightTitleColor)
				item.RightTitle = rightTitle

				var subtitleList []*sushi.FitsoTextSnippetType7SnippetSubtitleItem
				subtitleListItemTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
				subtitleListItemTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
				subtitleListItemIcon, _ := sushi.NewIcon(sushi.PointIcon, subtitleListItemTitleColor)

				subtitleListItems := getProductOfferings(ctx, product.PId)

				for _, subtitleItem := range subtitleListItems {
					trimmedSubtitle := strings.TrimSpace(subtitleItem)
					subtitleListItemTitle, _ := sushi.NewTextSnippet(trimmedSubtitle)
					subtitleListItemTitle.SetColor(subtitleListItemTitleColor)
					subtitleListItemTitle.SetFont(subtitleListItemTitleFont)

					subtitleListItem := &sushi.FitsoTextSnippetType7SnippetSubtitleItem{
						Title: subtitleListItemTitle,
						Icon:  subtitleListItemIcon,
					}

					subtitleList = append(subtitleList, subtitleListItem)
				}
				if len(subtitleList) > 0 {
					item.SubtitleList = subtitleList
				}
			}
			whiteColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			blueColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

			if product.Popularity == 1 {
				if !singleProduct && isNoCostEmiSupported {
					tagTitle, _ := sushi.NewTextSnippet("MOST POPULAR")
					tagTitleFont, _ := sushi.NewFont(sushi.FontBold, sushi.FontSize100)
					tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
					tagTitle.SetFont(tagTitleFont)
					tagTitle.SetColor(tagTitleColor)
					tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
					tagColor2, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
					tagGradient := sushi.Gradient{}
					tagGradient.Colors = &[]sushi.Color{*tagColor1, *tagColor2}
					item.Tag = &sushi.Tag{
						Title:    tagTitle,
						Gradient: &tagGradient,
					}
				} else if !singleProduct {
					item.ShowOfferTag = true
					topContainerTitle, _ := sushi.NewTextSnippet("MOST POPULAR")
					topContainerTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize50)
					topContainerTitle.SetFont(topContainerTitleFont)
					topContainerTitle.Kerning = 2
					bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
					topContainer := &sushi.Tag{
						Title:   topContainerTitle,
						BgColor: bgColor,
					}
					item.TopContainer = topContainer
				}

				if !isNoCostEmiSupported {
					bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
					borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)
					if productCategory == apiCommon.SinglekeyCategoryID {
						bgColor, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
						borderColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
					}
					item.BgColor = bgColor
					item.BorderColor = borderColor
				} else {
					bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)
					item.BgColor = bgColor
					borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
					item.BorderColor = borderColor
				}
				if percentDiscount > 0 {
					tagBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
					tagBgColor.Alpha = 0.8
					if item.Tag1 != nil && !isNoCostEmiSupported {
						item.Tag1.BgColor = tagBgColor
					}
				}

				if !isNoCostEmiSupported && productCategory == apiCommon.MasterkeyCategoryID {
					title.SetColor(blueColor)
					subtitle.SetColor(blueColor)
				}

				separatorColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
				if !isNoCostEmiSupported {
					separatorColor, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)
				}
				separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
				item.BottomSeparator = separator
			} else if product.Popularity == 2 {
				//tempColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
				if percentDiscount > 0 && item.Tag1 != nil && !isNoCostEmiSupported {
					item.Tag1.BgColor = whiteColor
				}
				if isNoCostEmiSupported {
					item.BgColor = whiteColor
					borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
					item.BorderColor = borderColor
				} else {
					bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)
					borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
					if productCategory == apiCommon.SinglekeyCategoryID {
						bgColor, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
						borderColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
					}
					item.BgColor = bgColor
					item.BorderColor = borderColor
				}
				//item.BorderColor = tempColor
				separatorColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				if isNoCostEmiSupported {
					separatorColor, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
				}
				separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
				item.BottomSeparator = separator

			} else {
				if percentDiscount > 0 {
					tagBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
					tagBgColor.Alpha = 0.8
					if !isNoCostEmiSupported && item.Tag1 != nil {
						item.Tag1.BgColor = tagBgColor
					}
				}

				if isNoCostEmiSupported {
					item.BgColor = whiteColor
				} else {
					bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)
					borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
					if productCategory == apiCommon.SinglekeyCategoryID {
						bgColor, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
						borderColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
					}
					item.BgColor = bgColor
					item.BorderColor = borderColor
				}
				separatorColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
				if isNoCostEmiSupported {
					separatorColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
					separator, _ = sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
					borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
					item.BorderColor = borderColor
				}
				item.BottomSeparator = separator
			}

			pcl := util.GetProductClient()
			discountAmount := int32(0)
			if loggedInUser > 0 {
				reqData := &productPB.Empty{}
				response, err := pcl.GetReferralEligiblity(ctx, reqData)
				if err == nil {
					if response.EligibleForReferral && response.ReferralUserId > 0 {
						discountAmtReq := &productPB.ApplyVoucherRequest{
							CustomAmount: product.RetailPrice,
						}
						discountAmtResp, err := pcl.GetDiscountAmountForReferral(ctx, discountAmtReq)

						if err != nil {
							log.Println("func:SetMultipleProducts, error in getting discounted amount for referral", err)
						} else {
							discountAmount = discountAmtResp.DiscountAmount
							p.PageData.ReferralApplied = true
						}
					}
				}
			}

			if isNoCostEmiSupported {
				item.BottomContainer = getNewBottomContainer()
			} else {
				item.BottomContainer = getBottomContainer(ctx, discountAmount, product, singleProduct)
			}

			item.ClickAction = p.GetClickActionForBuy(ctx, product)
			trackItem := p.GetTrackingItemForProductClick(ctx, product.PId)
			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

			items = append(items, item)
		}
		trackItem1 := p.GetTrackingItemForProductList(ctx, productList.Products)
		if featuresupport.SupportsMultipleFeaturedProductsHorizontalLayout(ctx) {
			if featuresupport.SupportSingleKeyProduct(ctx) {
				borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				gradient := sushi.Gradient{}

				color1, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
				color2, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				gradient.Colors = &[]sushi.Color{*color1, *color2}

				title.Color = nil

				snippet19 := &sushi.FitsoImageTextSnippetType19Snippet{
					Title:        title,
					Subtitle:     subtitle,
					BorderColor:  borderColor,
					BorderWidth:  1,
					CornerRadius: 12,
					Gradient:     &gradient,
				}

				if productCategory == apiCommon.SinglekeyCategoryID {
					tagTitle, _ := sushi.NewTextSnippet("2 complimentry sessions of other sports per month")
					tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint400)
					tagTitle.SetColor(tagTitleColor)
					tagBgcolor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
					tagBordercolor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)

					tag := &sushi.Tag{
						Title:       tagTitle,
						BgColor:     tagBgcolor,
						BorderColor: tagBordercolor,
						Type:        sushi.TagTypeSmall,
					}
					snippet19.Tag = tag

					tag1Title, _ := sushi.NewTextSnippet("Lite")
					tag1TitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					tag1Title.SetColor(tag1TitleColor)
					tag1Bgcolor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)

					tag1 := &sushi.Tag{
						Title:   tag1Title,
						BgColor: tag1Bgcolor,
						Type:    sushi.TagTypeSmall,
					}

					snippet19.Tag1 = tag1
				}

				snippet2 := sushi.FitsoPurchaseSnippetType2Snippet{
					Items:             items,
					ClevertapTracking: []*sushi.ClevertapItem{trackItem1},
				}
				layoutConfig := &sushi.LayoutConfig{
					SnippetType:  sushi.FitsoPurchaseSnippetType2,
					LayoutType:   sushi.LayoutTypeGrid,
					SectionCount: 1,
				}

				layoutV2 := sushi.V2ImageTextSnippetType14Layout{
					LayoutConfig:              layoutConfig,
					FitsoPurchaseSnippetType2: &snippet2,
				}

				items2 := []sushi.V2ImageTextSnippetType14Layout{layoutV2}
				snippet19.Items = &items2

				section := &productModels.ResultSection{
					LayoutConfig:                       layout_config,
					FitsoImageTextSnippetType19Snippet: snippet19,
				}
				p.AddSection(section)
			} else {
				snippet2 := sushi.FitsoPurchaseSnippetType2Snippet{
					Title:             title,
					Subtitle:          subtitle,
					Items:             items,
					ClevertapTracking: []*sushi.ClevertapItem{trackItem1},
				}
				section := &productModels.ResultSection{
					LayoutConfig:              layout_config,
					FitsoPurchaseSnippetType2: &snippet2,
				}
				p.AddSection(section)
			}
		} else {
			snippet := sushi.FitsoPurchaseSnippetType1Snippet{
				Title:             title,
				Items:             items,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem1},
			}
			section := &productModels.ResultSection{
				LayoutConfig:              layout_config,
				FitsoPurchaseSnippetType1: &snippet,
			}
			p.AddSection(section)
		}
	}
	productService := util.GetProductClient()
	fixedPlandetails, err := productService.GetSelectPackForCity(ctx, &productPB.Empty{})
	if err != nil {
		log.Println("Error in function GetSelectPackForCity while getting purchase page details, Error: ", err)
	}
	if len(fixedPlandetails.Products) != 0 && false {
		productDetails := fixedPlandetails.Products[0]

		titleText := "Cultpass PLAY <semibold-300|LITE>"
		//color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

		title, _ := sushi.NewTextSnippet(titleText)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)

		if featuresupport.SupportSingleKeyProduct(ctx) {
			color1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			color2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
			titleGradient := sushi.Gradient{}
			titleGradient.Colors = &[]sushi.Color{*color1, *color2}
			titleGradient.Direction = sushi.HorizontalDirection
			title.Gradient = &titleGradient
		}

		title.SetFont(font)
		title.SetAlignment(sushi.TextAlignmentCenter)
		title.SetKerning(3)
		title.SetIsMarkdown(1)

		subtitleText := "Get access to {blue-400|all sports} at any {blue-400|one center}"
		subtitle, _ := sushi.NewTextSnippet(subtitleText)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		fontS, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)

		subtitle.SetFont(fontS)
		subtitle.SetColor(color)
		subtitle.SetAlignment(sushi.TextAlignmentCenter)
		subtitle.SetIsMarkdown(1)

		items := make([]*sushi.FitsoPurchaseSnippetType1SnippetItem, 0)

		item := &sushi.FitsoPurchaseSnippetType1SnippetItem{
			Id:           productDetails.PId,
			CornerRadius: int32(12),
		}

		titleT, _ := sushi.NewTextSnippet("Plan starts at")//, strconv.Itoa(int(productDetails.Duration)))
		titleFont, _ := sushi.NewFont(sushi.FontBold, sushi.FontSize900)
		titleT.SetFont(titleFont)
		color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
		color2, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint400)
		titleGradient := sushi.Gradient{}
		titleGradient.Colors = &[]sushi.Color{*color1, *color2}
		titleGradient.Direction = sushi.HorizontalDirection
		titleT.Gradient = &titleGradient
		titleTextColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		titleT.SetColor(titleTextColor)
		item.Title = titleT

		//price
		subtitle1, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(productDetails.Price)))
		fontType := sushi.FontRegular
		font, _ = sushi.NewFont(fontType, sushi.FontSize200)
		subtitle1.SetFont(font)
		subtitle.SetColor(titleTextColor)

		perMonthPrice := int32(productDetails.Price)
		if productDetails.Duration > 0 {
			perMonthPrice = int32(productDetails.Price) / productDetails.Duration
		}

		if productDetails.Price > productDetails.RetailPrice {
			perMonthPrice = int32(productDetails.RetailPrice) / productDetails.Duration
		}

		subtitle2, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(productDetails.RetailPrice)))

		subtitle2Text := fmt.Sprintf("₹%s{black-500|<semibold-200|/month*>}", strconv.Itoa(int(perMonthPrice)))
		subtitle2, _ = sushi.NewTextSnippet(subtitle2Text)
		subtitle2Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		subtitle2.SetFont(subtitle2Font)
		subtitle2.SetIsMarkdown(1)
		subtitle2.SetAlignment(sushi.TextAlignmentRight)

		subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitle2.SetColor(subtitle2Color)

		if productDetails.Price > productDetails.RetailPrice {
			subtitle1.Strikethrough = true
			item.Subtitle2 = subtitle2
		}
		item.Subtitle1 = subtitle1

		percentDiscount := int((productDetails.Price - productDetails.RetailPrice) * 100 / productDetails.Price)
		percentDiscountText := strconv.Itoa(int(percentDiscount)) + "% off"
		tagTitle, _ := sushi.NewTextSnippet(percentDiscountText)
		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
		tagTitle.SetColor(tagColor)
		if percentDiscount > 0 {
			item.Tag1 = &sushi.Tag{
				Title: tagTitle,
				Type:  "small",
			}
		}

		rightTitle, _ := sushi.NewTextSnippet("₹" + strconv.Itoa(int(productDetails.RetailPrice)))
		rightTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		rightTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
		rightTitle.SetFont(rightTitleFont)
		rightTitle.SetColor(rightTitleColor)
		item.RightTitle = rightTitle

		var subtitleList []*sushi.FitsoTextSnippetType7SnippetSubtitleItem
		subtitleListItemTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		subtitleListItemTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		subtitleListItemIcon, _ := sushi.NewIcon(sushi.PointIcon, subtitleListItemTitleColor)

		subtitleListItems := getProductOfferings(ctx, productDetails.PId)

		for _, subtitleItem := range subtitleListItems {
			trimmedSubtitle := strings.TrimSpace(subtitleItem)
			subtitleListItemTitle, _ := sushi.NewTextSnippet(trimmedSubtitle)
			subtitleListItemTitle.SetColor(subtitleListItemTitleColor)
			subtitleListItemTitle.SetFont(subtitleListItemTitleFont)

			subtitleListItem := &sushi.FitsoTextSnippetType7SnippetSubtitleItem{
				Title: subtitleListItemTitle,
				Icon:  subtitleListItemIcon,
			}

			subtitleList = append(subtitleList, subtitleListItem)
		}
		if len(subtitleList) > 0 {
			item.SubtitleList = subtitleList
		}

		bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)
		item.BgColor = bgColor
		borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		item.BorderColor = borderColor
		if percentDiscount > 0 {
			tagBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			tagBgColor.Alpha = 0.8
			if item.Tag1 != nil {
				item.Tag1.BgColor = tagBgColor
			}
		}

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		item.BottomSeparator = separator
		item.BottomContainer = getNewBottomContainer()

		clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
		clickAction.SetOpenWebview(&sushi.OpenWebview{
			Title: "",
			URL:   "https://cure.app.link/GrNrtDIhZyb?force_browser=1",
			InApp: false,
		})
		item.ClickAction = clickAction

		trackItem := p.GetTrackingItemForProductClick(ctx, productDetails.PId)
		item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, item)

		borderColor1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		gradient := sushi.Gradient{}

		color3, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		color4, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		gradient.Colors = &[]sushi.Color{*color3, *color4}
		title.Color = nil

		fitsoHeaderSnippetType1Layout := &sushi.LayoutConfig{
			SnippetType:  sushi.SectionHeaderType1,
		}
	
		titleHead, _ := sushi.NewTextSnippet("cultpass PLAY Lite Plans available on cult app only")
		fontTitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		colorTitle, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		titleHead.SetFont(fontTitle)
		titleHead.SetAlignment(sushi.TextAlignmentCenter)
		titleHead.SetColor(colorTitle)

		headerSnippet := &sushi.SectionHeaderType1Snippet{
			Title: titleHead,
		}
		sectionHeader := sushi.V2ImageTextSnippetType14Layout{
			LayoutConfig:       fitsoHeaderSnippetType1Layout,
			SectionHeaderType1: headerSnippet,
		}

		snippet19 := &sushi.FitsoImageTextSnippetType19Snippet{
			Title:        title,
			Subtitle:     subtitle,
			BorderColor:  borderColor1,
			BorderWidth:  1,
			CornerRadius: 12,
			Gradient:     &gradient,
		}
		trackItem1 := p.GetTrackingItemForProductList(ctx, fixedPlandetails.Products)
		snippet2 := sushi.FitsoPurchaseSnippetType2Snippet{
			Items:             items,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem1},
		}
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoPurchaseSnippetType2,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		layoutV2 := sushi.V2ImageTextSnippetType14Layout{
			LayoutConfig:              layoutConfig,
			FitsoPurchaseSnippetType2: &snippet2,
		}

		items2 := []sushi.V2ImageTextSnippetType14Layout{layoutV2, sectionHeader}
		snippet19.Items = &items2

		section := &productModels.ResultSection{
			LayoutConfig:                       layout_config,
			FitsoImageTextSnippetType19Snippet: snippet19,
		}
		p.AddSection(section)
	}

}
func (p *PurchasePageTemplate) SetFooterImage(ctx context.Context) {
	layout_config := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType19,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}
	image, _ := sushi.NewImage(util.GetCDNLink("uploads/Group218379sports-everyday1635244604.png"))
	image.SetHeight(72)
	image.SetWidth(222)
	image.AspectRatio = 3.08
	item := sushi.ImageTextSnippetType19SnippetItem{
		Image: image,
	}
	items := make([]sushi.ImageTextSnippetType19SnippetItem, 0)
	items = append(items, item)
	snippet := &sushi.ImageTextSnippetType19Snippet{
		Items: &items,
	}
	section := &productModels.ResultSection{
		LayoutConfig:           layout_config,
		ImageTextSnippetType19: snippet,
	}
	p.AddSection(section)
}

func (p *PurchasePageTemplate) GetTrackingItemForProductList(ctx context.Context, products []*productPB.Product) *sushi.ClevertapItem {
	tapPayload := p.GetCommonPayloadForTracking(ctx)
	PIds := make([]string, 0)
	for _, elem := range products {
		PIds = append(PIds, strconv.Itoa(int(elem.PId)))
	}

	tapPayload["product_ids"] = strings.Join(PIds, ",")

	tapEname := &sushi.EnameData{
		Ename: "purchase_landing_product_list_impressions",
	}
	event := sushi.NewClevertapEvents()
	event.SetImpression(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func (p *PurchasePageTemplate) GetTrackingItemForProductClick(ctx context.Context, PId int32) *sushi.ClevertapItem {
	tapPayload := p.GetCommonPayloadForTracking(ctx)
	tapPayload["product_id"] = PId
	tapEname := &sushi.EnameData{
		Ename: "purchase_landing_product_tap",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func (p *PurchasePageTemplate) GetToolTipTrackingItem(ctx context.Context, title string) *sushi.ClevertapItem {
	tapPayload := p.GetCommonPayloadForTracking(ctx)
	temp := strings.Split(strings.ToLower(title), " ")
	tapEname := &sushi.EnameData{
		Ename: strings.Join(temp, "_") + "_tool_tip_tap",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func getBottomContainer(ctx context.Context, discountAmount int32, product *productPB.Product, singleProduct bool) *sushi.Tag {
	if discountAmount == 0 {
		var titleText string = "Buy Now"
		tag := &sushi.Tag{}
		if len(product.PlanTileText) > 0 {
			offerText := product.PlanTileText
			offerTitle, _ := sushi.NewTextSnippet(offerText)
			font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			offerTitle.Alignment = sushi.TextAlignmentLeft
			color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			offerTitle.SetFont(font)
			offerTitle.SetColor(color)
			if !featuresupport.SupportsMultipleFeaturedProductsHorizontalLayout(ctx) {
				offerTitle.Alignment = sushi.TextAlignmentCenter
			}
			tag.Title = offerTitle

		}

		title, _ := sushi.NewTextSnippet(titleText)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
		title.Alignment = sushi.TextAlignmentCenter
		color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		// if product.Popularity == 1 {
		// 	color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		// }
		title.SetFont(font)
		title.SetColor(color)
		icon, _ := sushi.NewIcon("e936", color)
		title.SetSuffixIcon(icon)
		title.Alignment = sushi.TextAlignmentCenter
		if len(product.PlanTileText) > 0 {
			tag.Subtitle = title
		} else {
			tag.Title = title
		}
		return tag

	} else {
		titleText := "Extra <medium-200|₹" + strconv.Itoa(int(discountAmount)) + "> off"
		title, _ := sushi.NewTextSnippet(titleText)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		// if product.Popularity == 1 {
		// 	color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		// }
		title.SetFont(font)
		title.SetColor(color)

		title.Alignment = sushi.TextAlignmentLeft
		title.IsMarkdown = 1
		subtitle, _ := sushi.NewTextSnippet("Apply Referral")
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
		subtitle.SetFont(font)
		subtitle.SetColor(color)
		icon, _ := sushi.NewIcon("e936", color)
		subtitle.SetSuffixIcon(icon)
		tag := &sushi.Tag{
			Title:    title,
			Subtitle: subtitle,
			//RightIcon: icon,
		}
		return tag
	}
}

func getNewBottomContainer() *sushi.Tag {
	var titleText string = "Buy Now"
	title, _ := sushi.NewTextSnippet(titleText)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon("e936", color)
	title.Alignment = sushi.TextAlignmentCenter
	title.SetFont(font)
	title.SetColor(color)
	title.SetSuffixIcon(icon)
	tag := &sushi.Tag{}
	tag.Title = title
	return tag
}

func setAuthClickAction(ctx context.Context, buyingFor string, productId int32) *sushi.Auth {
	auth_title, _ := sushi.NewTextSnippet("Please login to purchase a cultpass PLAY membership. Enter your phone number to login using OTP.")
	payload := map[string]interface{}{
		"post_action": buyingFor,
	}
	if productId > 0 {
		payload["product_id"] = productId
	}

	postback_params, _ := json.Marshal(payload)
	auth := &sushi.Auth{
		Title:          auth_title,
		PostbackParams: string(postback_params),
		Source:         "buy_for_landing",
	}
	return auth
}

func (p *PurchasePageTemplate) GetClickActionForBuy(ctx context.Context, product *productPB.Product) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()
	loggedInUserId := util.GetUserIDFromContext(ctx)

	clickAction.Type = sushi.ClickActionOpenGenericBottomSheet
	headerTitle, _ := sushi.NewTextSnippet("Buying for")
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	headerTitle.SetFont(font)
	headerTitle.SetColor(color)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

	header := &sushi.GenericBottomSheetHeader{
		Title: headerTitle,
	}

	items := make([]*sushi.CustomTextSnippetTypeLayout, 0)

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType35,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	title, _ := sushi.NewTextSnippet("Buy for yourself only")
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetFont(titleFont)
	title.SetColor(color)

	subtitle1, _ := sushi.NewTextSnippet("Quick buy single membership for yourself")
	subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	subtitle1.SetFont(subtitle1Font)
	subtitle1.SetColor(subtitle1Color)

	buyForYourselfImage, _ := sushi.NewImage(util.GetCDNLink("uploads/Group218376buy-for-yourself1634620002.png")) //first setting for yourself
	buyForYourselfImage.SetAspectRatio(1)
	buyForYourselfImage.SetHeight(64)
	buyForYourselfImage.SetWidth(64)
	buyForYourselfImage.SetType(sushi.ImageTypeCircle)

	clickActionBuyForMe := sushi.GetClickAction()
	if featuresupport.SupportsLoginRedirectForMultipleFeaturedProducts(ctx) && loggedInUserId == 0 {
		auth := setAuthClickAction(ctx, apiCommon.POST_ACTION_BUYING_FOR_ME, product.PId)
		clickActionBuyForMe.SetAuth(auth)
	} else {
		clickActionBuyForMe.SetClickActionType("dismiss_page")
		deeplinkBuyForMe := &sushi.Deeplink{
			URL: util.GetBuyingForMeDeeplink(product.PId, false),
		}
		dismissPage := &sushi.DismissPage{
			Type:     "deeplink",
			Deeplink: deeplinkBuyForMe,
		}
		clickActionBuyForMe.DismissPage = dismissPage
	}

	itemBuyForMe := sushi.V2ImageTextSnippetType35SnippetItem{
		Title:             title,
		SubTitle1:         subtitle1,
		Image:             buyForYourselfImage,
		ClickAction:       clickActionBuyForMe,
		ClevertapTracking: []*sushi.ClevertapItem{p.GetTrackingItemForBuyForMe(ctx)},
		JumboTracking:     []*jumbo.Item{getBuyForMeJumboTrackItem()},
	}
	snippetForYourself := &sushi.V2ImageTextSnippetType35Snippet{
		Items:   &[]sushi.V2ImageTextSnippetType35SnippetItem{itemBuyForMe},
		BgColor: bgColor,
	}

	buyForYourselfItem := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:                   layoutConfig,
		Version2ImageTextSnippetType35: snippetForYourself,
	}
	items = append(items, buyForYourselfItem)

	//now setting for buy for others

	fnfTitle, _ := sushi.NewTextSnippet("Buy for friends & family")
	fnfTitle.SetColor(color)
	fnfTitle.SetFont(titleFont)

	fnfSubtitle1, _ := sushi.NewTextSnippet("Multiple memberships for others and/or you")
	fnfSubtitle1.SetColor(subtitle1Color)
	fnfSubtitle1.SetFont(subtitle1Font)

	buyForOthersImage, _ := sushi.NewImage(util.GetCDNLink("uploads/Group218377buy-for-fnf1634620141.png"))
	buyForOthersImage.SetAspectRatio(1)
	buyForOthersImage.SetHeight(64)
	buyForOthersImage.SetWidth(64)
	buyForOthersImage.SetType(sushi.ImageTypeCircle)

	buyForOthersImage.SetType(sushi.ImageTypeCircle)
	clickActionBuyForOthers := sushi.GetClickAction()
	if featuresupport.SupportsLoginRedirectForMultipleFeaturedProducts(ctx) && loggedInUserId == 0 {
		auth := setAuthClickAction(ctx, apiCommon.POST_ACTION_BUYING_FOR_OTHERS, product.PId)
		clickActionBuyForOthers.SetAuth(auth)
	} else {
		clickActionBuyForOthers.SetClickActionType("dismiss_page")
		deeplinkBuyForOthers := &sushi.Deeplink{
			URL: util.GetBuyingForOthersDeeplink(product.PId, false),
		}
		dismissPage := &sushi.DismissPage{
			Type:     "deeplink",
			Deeplink: deeplinkBuyForOthers,
		}
		clickActionBuyForOthers.DismissPage = dismissPage
	}
	itemBuyForOthers := sushi.V2ImageTextSnippetType35SnippetItem{
		Title:             fnfTitle,
		SubTitle1:         fnfSubtitle1,
		Image:             buyForOthersImage,
		ClickAction:       clickActionBuyForOthers,
		ClevertapTracking: []*sushi.ClevertapItem{p.GetTrackingItemForBuyForOthers(ctx)},
		JumboTracking:     []*jumbo.Item{getBuyForOtherJumboTrackItem()},
	}
	snippetForOthers := &sushi.V2ImageTextSnippetType35Snippet{
		Items:   &[]sushi.V2ImageTextSnippetType35SnippetItem{itemBuyForOthers},
		BgColor: bgColor,
	}

	buyForOthersItem := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:                   layoutConfig,
		Version2ImageTextSnippetType35: snippetForOthers,
	}
	items = append(items, buyForOthersItem)

	clickAction.OpenGenericBottomSheet = &sushi.OpenGenericBottomSheet{
		Header: header,
		Items:  items,
	}
	return clickAction
}

func (p *PurchasePageTemplate) SetBgImage(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	bgImage, _ := sushi.NewImage(purchasePageData.HeaderBackground)
	p.BackgroundImage = bgImage
	return
}

func (p *PurchasePageTemplate) GetTrackingItemForBuyForMe(ctx context.Context) *sushi.ClevertapItem {
	tapPayload := p.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "buy_for_me_tap",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func (p *PurchasePageTemplate) GetTrackingItemForBuyForOthers(ctx context.Context) *sushi.ClevertapItem {
	tapPayload := p.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "buy_for_others_tap",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func (p *PurchasePageTemplate) SetFooterSection(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	footerLayoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	var buttonDataItems []sushi.FooterSnippetType2ButtonItem

	loggedInUser := util.GetUserIDFromContext(ctx)
	purchaseUserClickAction := sushi.GetClickAction()
	if loggedInUser > 0 {
		deeplink := sushi.Deeplink{
			URL: purchasePageData.CtaUser.Deeplink,
		}
		purchaseUserClickAction.SetDeeplink(&deeplink)
	} else {
		auth := setAuthClickAction(ctx, apiCommon.POST_ACTION_BUYING_FOR_ME, 0)
		purchaseUserClickAction.SetAuth(auth)
	}

	tapPayload := p.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "buy_for_me_tap",
	}
	buyForMeEvent := sushi.NewClevertapEvents()
	buyForMeEvent.SetTap(tapEname)
	buyForMeTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buyForMeEvent)

	purchaseUserButton := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeSolid,
		Size:              sushi.FooterButtonSizeLarge,
		Text:              purchasePageData.CtaUser.Text,
		ClickAction:       purchaseUserClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{buyForMeTrackItem},
		JumboTracking:     []*jumbo.Item{getBuyForMeJumboTrackItem()},
	}
	buttonDataItems = append(buttonDataItems, purchaseUserButton)

	purchaseMemberClickAction := sushi.GetClickAction()
	if loggedInUser > 0 {
		deeplink := sushi.Deeplink{
			URL: purchasePageData.CtaMembers.Deeplink,
		}
		purchaseMemberClickAction.SetDeeplink(&deeplink)
	} else {
		auth := setAuthClickAction(ctx, apiCommon.POST_ACTION_BUYING_FOR_OTHERS, 0)
		purchaseMemberClickAction.SetAuth(auth)
	}
	tapEname = &sushi.EnameData{
		Ename: "buy_for_others_tap",
	}
	buyForOthersEvent := sushi.NewClevertapEvents()
	buyForOthersEvent.SetTap(tapEname)
	buyForOthersTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buyForOthersEvent)

	purchaseMemberButton := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeOutline,
		Size:              sushi.FooterButtonSizeLarge,
		Text:              purchasePageData.CtaMembers.Text,
		ClickAction:       purchaseMemberClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{buyForOthersTrackItem},
		JumboTracking:     []*jumbo.Item{getBuyForOtherJumboTrackItem()},
	}
	buttonDataItems = append(buttonDataItems, purchaseMemberButton)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &buttonDataItems,
	}
	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       footerLayoutConfig,
		FooterSnippetType2: footerSnippet,
	}
	p.Footer = footer
}

func (p *PurchasePageTemplate) SetFloatingButton(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

	color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ScrollDownIcon, color)

	clickAction := &sushi.ClickAction{
		Type: sushi.ClickActionScrollDown,
	}
	floatingButton := &sushi.Button{
		BgColor:     bgColor,
		ClickAction: clickAction,
	}

	floatingButton.SetSuffixIcon(icon)
	p.FloatingButton = floatingButton
}

func getBuyForOtherJumboTrackItem() *jumbo.Item {
	jumboPayload := make(map[string]interface{})
	jumboPayload["source_page"] = "buy_membership"
	jumboBuyForOthersEvents := jumbo.NewEvents()
	jumboBuyForOthersTapEvent := jumbo.GetEventNameObject(jumbo.BuyForOthersTap)
	jumboBuyForOthersEvents.SetTap(jumboBuyForOthersTapEvent)
	jumboBuyForOtherTrackItem := jumbo.GetJumboTrackItem(jumboPayload, jumboBuyForOthersEvents, jumbo.PurchaseEventsTableName)

	return jumboBuyForOtherTrackItem
}

func getBuyForMeJumboTrackItem() *jumbo.Item {
	jumboPayload := make(map[string]interface{})
	jumboPayload["source_page"] = "buy_membership"
	jumboBuyForMeEvents := jumbo.NewEvents()
	jumboBuyForMeTapEvent := jumbo.GetEventNameObject(jumbo.BuyForMeTap)
	jumboBuyForMeEvents.SetTap(jumboBuyForMeTapEvent)
	jumboBuyForMeTrackItem := jumbo.GetJumboTrackItem(jumboPayload, jumboBuyForMeEvents, jumbo.PurchaseEventsTableName)

	return jumboBuyForMeTrackItem
}

func (p *PurchasePageTemplate) SetEmptyView(purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	bottomButtonItems := make([]sushi.FooterSnippetType2ButtonItem, 0)

	clickAction := sushi.GetClickAction()
	deeplink := sushi.Deeplink{
		URL: util.GetChangeLocationDeeplink(),
	}
	clickAction.SetDeeplink(&deeplink)

	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Type:        sushi.FooterButtonTypeText,
		Text:        purchasePageData.EmptyView.ButtonText,
		ClickAction: clickAction,
	}
	bottomButtonItems = append(bottomButtonItems, buttonItem1)

	bottomButton := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &bottomButtonItems,
	}

	title, _ := sushi.NewTextSnippet(purchasePageData.EmptyView.Title)
	subtitle1, _ := sushi.NewTextSnippet(purchasePageData.EmptyView.Subtitle)
	image, _ := sushi.NewImage(purchasePageData.EmptyView.Image)
	image.SetHeight(220)
	image.SetWidth(258)

	snippet := &sushi.EmptyViewType1Snippet{
		Title:        title,
		Subtitle1:    subtitle1,
		Image:        image,
		BottomButton: bottomButton,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}
	emptyView := &sushi.EmptyViewType1Layout{
		LayoutConfig:          layoutConfig,
		EmptyViewType1Snippet: snippet,
	}
	p.EmptyView = emptyView
}

func (p *PurchasePageTemplate) AddSection(section *productModels.ResultSection) {
	p.Sections = append(p.Sections, section)
}

func (p *PurchasePageTemplate) SetClevertapTracking(ctx context.Context) {
	commonPayload := p.GetCommonPayloadForTracking(ctx)

	if p.PageData.ReferralDiscountedAmount > 0 {
		commonPayload["slashed_price"] = "Y"
	} else {
		commonPayload["slashed_price"] = "N"
	}

	landingEName := &sushi.EnameData{
		Ename: "buy_membership_landing",
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	dismissEName := &sushi.EnameData{
		Ename: "buy_membership_back_button_tap",
	}
	pageDismissEvent := sushi.NewClevertapEvents()
	pageDismissEvent.SetPageDismiss(dismissEName)
	pageDismissTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageDismissEvent)
	p.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem, pageDismissTrackItem}
}

func (p *PurchasePageTemplate) SetJumboTracking(ctx context.Context) {
	jumboPayload := make(map[string]interface{})
	jumboPayload["source_page"] = "buy_membership"
	events := jumbo.NewEvents()
	landingEvent := jumbo.GetEventNameObject(jumbo.BuyMembershipLanding)
	events.SetPageSuccess(landingEvent)
	jumboTrackItem := jumbo.GetJumboTrackItem(jumboPayload, events, jumbo.PurchaseEventsTableName)

	p.JumboTracking = []*jumbo.Item{jumboTrackItem}
}

func (p *PurchasePageTemplate) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "buy_membership"
	return commonPayload
}
