package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"bitbucket.org/jogocoin/go_api/api/common"
	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	model "bitbucket.org/jogocoin/go_api/api/models"
	productModels "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilityPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type BuyingForTemplate struct {
	Title               *sushi.TextSnippet                `json:"title,omitempty"`
	InfoData            []*productModels.InfoData         `json:"info_data,omitempty"`
	UserData            *productModels.UserData           `json:"user_data"`
	ChildUsersData      *productModels.ChildUsersData     `json:"child_users_data,omitempty"`
	BottomButtonStates  *productModels.BottomButtonStates `json:"bottom_button_states,omitempty"`
	TrackingAttributes  *productModels.TrackingAttributes `json:"tracking_attributes,omitempty"`
	IsSingleProductCity bool                              `json:"-"`
}

const (
	PAGE_TYPE_OTHERS     = "others"
	MIN_AGE_FOR_PURCHASE = 18
	PLAN_START_DATE_THRESHOLD_DAYS = 30
)

func GetBuyOrRenewDetailsC(c *gin.Context) {
	productService := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetBuyOrRenewDetailsRequest)
	loggedInUserId := util.GetUserIDFromContext(ctx)

	var templateResponse BuyingForTemplate

	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You need to login first!"),
		)
		return
	}

	reqData := &productPB.GetBuyingOrRenewalRequest{
		IsRenewal: json.IsRenewal,
		Type:      json.Type,
		ProductId: json.ProductId,
	}

	response, err := productService.GetBuyingOrRenewalDetails(ctx, reqData)
	if err != nil {
		log.Printf("Error in controller GetBuyOrRenewDetailsC while getting buying or renewal details, Error: %v", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(apiCommon.UNEXPECTED_ERROR),
		)
		return
	} else if response.Status != nil && response.Status.Status == apiCommon.FAILED {
		log.Println("Error in controller GetBuyOrRenewDetailsC while getting buying or renewal details")
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(apiCommon.UNEXPECTED_ERROR),
		)
		return
	} else {
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			cityId := util.GetCityIDFromContext(ctx)
			if cityId > 0 {
				req := &productPB.GetFeaturedProductsForCityIdReq{
					CityId: cityId,
				}
				res, err := productService.GetFeaturedProductsForCityId(ctx, req)
				if err != nil {
					log.Println("Error in getting Featured Procucts for CityId: ", cityId, err)
					c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
					return
				}
				totalProducts := 0
				for _, productList := range res.ProductMap {
					totalProducts = totalProducts + len(productList.Products)
				}
				if totalProducts == 1 {
					templateResponse.IsSingleProductCity = true
				} else {
					templateResponse.IsSingleProductCity = false
				}
			}
			if json.Type == PAGE_TYPE_OTHERS {
				// Buy for friends and family
				templateResponse.SetTitleV2(response)
				templateResponse.SetInfoDataV2(ctx, response)
			}
			templateResponse.SetUserDataV2(ctx, &json, response, reqData.IsRenewal)
			templateResponse.SetChildUsersData(ctx, &json, response, reqData.IsRenewal)
			templateResponse.SetBottomButtonStates(response)
			templateResponse.SetTrackingAttributes(ctx, json)
		} else {
			templateResponse.SetTitle(response)
			templateResponse.SetInfoData(response)
			templateResponse.SetUserData(ctx, response, reqData.IsRenewal)
			templateResponse.SetChildUsersData(ctx, &json, response, reqData.IsRenewal)
			templateResponse.SetBottomButtonStates(response)
			templateResponse.SetTrackingAttributes(ctx, json)
		}
	}

	c.JSON(
		http.StatusOK,
		templateResponse,
	)
}

func getAgeValidationBottomSheet(ctx context.Context, buyingForMyself bool) *sushi.ClickAction {
	clickAction, _ := sushi.NewTextClickAction("open_age_bottom_sheet")

	headerTitle, _ := sushi.NewTextSnippet("Enter your age")
	header := &sushi.GenericBottomSheetHeader{
		Title: headerTitle,
	}

	age_str := strconv.Itoa(MIN_AGE_FOR_PURCHASE)

	title, _ := sushi.NewTextSnippet("Your age must be " + age_str + " years or above to buy a membership")

	placeholder, _ := sushi.NewTextSnippet("Age (in years)")

	alwaysTitle, _ := sushi.NewTextSnippet("Age must be " + age_str + " years or above")
	always := &sushi.TextSnippetType1Snippet{
		Title: alwaysTitle,
	}
	emptyTitle, _ := sushi.NewTextSnippet("Age cannot be empty")
	empty := &sushi.TextSnippetType1Snippet{
		Title: emptyTitle,
	}
	errorTitle, _ := sushi.NewTextSnippet("Age cannot be less than " + age_str + " years")
	minAge := &sushi.MinAge{
		Age:        MIN_AGE_FOR_PURCHASE - 1,
		ErrorTitle: errorTitle,
	}
	states := &sushi.AgeFieldStates{
		Always: always,
		Empty:  empty,
		MinAge: minAge,
	}
	ageField := &sushi.AgeField{
		Placeholder: placeholder,
		States:      states,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	var clickActionTypeForSaveDetails string
	if featuresupport.SupportsFullPageCart(ctx) {
		clickActionTypeForSaveDetails = sushi.ClickActionOpenCart
	} else {
		clickActionTypeForSaveDetails = sushi.ClickActionProceedToCart
	}

	clickActionForSaveDetails, _ := sushi.NewTextClickAction(clickActionTypeForSaveDetails)
	if !buyingForMyself {
		clickActionForSaveDetails.Type = "dismiss_page"
	}
	trackingItem := getAgeSaveTrackingItem(ctx, buyingForMyself)

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	item := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeSolid,
		Size:              sushi.FooterButtonSizeLarge,
		Text:              "Save details",
		ClickAction:       clickActionForSaveDetails,
		ClevertapTracking: []*sushi.ClevertapItem{trackingItem},
	}
	items = append(items, item)
	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: "horizonatal",
		Items:       &items,
	}
	footer := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footerLayout := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: footer,
	}
	openAgeBottomSheet := &sushi.OpenAgeBottomSheet{
		Title:    title,
		Header:   header,
		AgeField: ageField,
		Footer:   footerLayout,
	}
	clickAction.OpenAgeBottomSheet = openAgeBottomSheet
	return clickAction
}

func (b *BuyingForTemplate) SetTitle(response *productPB.GetBuyingOrRenewalResponse) {
	title, _ := sushi.NewTextSnippet(response.PageTitle)
	b.Title = title
}

func (b *BuyingForTemplate) SetTitleV2(response *productPB.GetBuyingOrRenewalResponse) {
	titleText := "Buying for friends and family"
	if response.PageTitle != "" {
		titleText = response.PageTitle
	}
	title, _ := sushi.NewTextSnippet(titleText)

	b.Title = title
}

func (b *BuyingForTemplate) SetInfoData(response *productPB.GetBuyingOrRenewalResponse) {
	if response.MemberBenefits != nil {
		title, _ := sushi.NewTextSnippet(response.MemberBenefits.Title)
		var benefitItems []*productModels.InfoDataItem
		for _, memberBenefit := range response.MemberBenefits.Benefits {
			benefitText, _ := sushi.NewTextSnippet(memberBenefit)
			iconColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			checkIcon, _ := sushi.NewIcon(sushi.TickMarkIcon, iconColor)
			benefitItem := &productModels.InfoDataItem{
				LeftIcon: checkIcon,
				Title:    benefitText,
			}
			benefitItems = append(benefitItems, benefitItem)
		}
		b.InfoData = append(b.InfoData, &productModels.InfoData{
			Title: title,
			Items: benefitItems,
		})
	}
}

func (b *BuyingForTemplate) SetInfoDataV2(ctx context.Context, response *productPB.GetBuyingOrRenewalResponse) {
	if response.MemberBenefits != nil {
		rightImage, _ := sushi.NewImage(util.GetCDNLink("uploads/but-for-fnf-new-banner-image1634712059.png"))
		rightImage.SetAspectRatio(1)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)

		var benefitItems []*productModels.InfoDataItem
		for _, memberBenefit := range response.MemberBenefits.Benefits {
			benefitText, _ := sushi.NewTextSnippet(memberBenefit)
			iconColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			checkIcon, _ := sushi.NewIcon(sushi.TickMarkIcon, iconColor)
			benefitItem := &productModels.InfoDataItem{
				LeftIcon: checkIcon,
				Title:    benefitText,
			}
			benefitItems = append(benefitItems, benefitItem)
		}

		b.InfoData = append(b.InfoData, &productModels.InfoData{
			RightImage:  rightImage,
			BgColor:     bgColor,
			BorderColor: borderColor,
			Items:       benefitItems,
		})
	}

}

func getClickActionForSelectPreferredCenter(ctx context.Context) *sushi.ClickAction {
	isSingleSportCity, sportId := util.GetSportIdIfSingleSportCity(ctx)
	deeplink := util.GetSportSelectDeeplinkForPurchaseFlow()

	if isSingleSportCity && sportId > 0 {
		deeplink = util.GetPreferredCenterPageDeeplinkWithSportID(sportId, false)
	}

	clickAction := sushi.GetClickAction()
	clickAction.SetClickActionType(sushi.ClickActionDeeplink)
	clickAction.Deeplink = &sushi.Deeplink{
		URL: deeplink,
	}
	return clickAction
}

func (b *BuyingForTemplate) SetUserData(ctx context.Context, response *productPB.GetBuyingOrRenewalResponse, isRenewal bool) {
	userData := response.UserData
	user := userData.User
	membershipStatusText, _ := sushi.NewTextSnippet(user.ExpiryString)

	userDataTitle, _ := sushi.NewTextSnippet(userData.Title)
	userDataTitle.SetKerning(3)

	editButton, _ := sushi.NewTextSnippet(userData.EditButton)
	addDetailsButton, _ := sushi.NewTextSnippet(userData.AddDetailsButton)

	editInfoTitle, _ := sushi.NewTextSnippet(userData.EditInfo.Title)

	b.UserData = &productModels.UserData{
		User: productModels.MembershipUser{
			UserId:               user.UserId,
			Name:                 user.Name,
			Phone:                user.Phone,
			StartDate:            user.StartDate,
			MembershipStatusText: membershipStatusText,
			ProductId:            user.ProductId,
		},
		Title:            userDataTitle,
		EditButton:       editButton,
		AddDetailsButton: addDetailsButton,
		EditInfo: &productModels.MembershipEditInfo{
			Title: editInfoTitle,
			BottomButtonStates: productModels.BottomButtonStates{
				Disabled: &productModels.BottomButtonState{
					Button: GetDisabledButton(userData.EditInfo.BottomButtonStates),
				},
				Completed: &productModels.BottomButtonState{
					Button: GetCompletedButton(userData.EditInfo.BottomButtonStates),
				},
			},
		},
	}

	if user.Age != 0 {
		b.UserData.User.Age = user.Age
	}
	//b.UserData.User.DisplayAge = fmt.Sprintf("${age} yrs")

	preferredFacilityInfo := user.PreferredFacility
	if preferredFacilityInfo != nil && !ValidateSeasonalPoolInfoToShow(preferredFacilityInfo, isRenewal) {
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) && user.PreferredSportId != 0 {
			b.UserData.User.PreferredCenter = GetPreferredCenterAndSportInfo(ctx, preferredFacilityInfo, user.PreferredSportId)
			b.UserData.User.PreferredFacilityId = preferredFacilityInfo.FacilityId
			b.UserData.User.PreferredSportId = user.PreferredSportId
		} else if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			b.UserData.User.PreferredCenter = GetPreferredCenterInfo(ctx, preferredFacilityInfo)
		}
	}

	for _, editInfoSection := range userData.EditInfo.Sections {
		section := GetEditInfoSection(ctx, editInfoSection)
		b.UserData.EditInfo.Sections = append(b.UserData.EditInfo.Sections, section)
	}
}

func (b *BuyingForTemplate) SetUserDataV2(ctx context.Context, req *structs.GetBuyOrRenewDetailsRequest, response *productPB.GetBuyingOrRenewalResponse, isRenewal bool) {

	cityId := util.GetCityIDFromContext(ctx)
	userData := response.UserData
	user := userData.User

	product := response.ProductInfo

	headerTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s, %d years", user.Name, user.Age))
	header := &productModels.UserDataHeader{
		Title: headerTitle,
	}

	duration := ""
	if product.DurationUnit == "month" && product.Duration > 1 {
		duration = "Months"
	} else if product.DurationUnit == "month" {
		duration = "Month"
	}
	productDuration, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s", product.Duration, duration))
	productPrice, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%d", int32(product.RetailPrice)))

	productDetails := &productModels.ProductDetails{
		Duration:  productDuration,
		Price:     productPrice,
		ProductId: product.ProductId,
	}

	planEditInfoTitle, _ := sushi.NewTextSnippet("Plan")
	suffixIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint400)
	suffixIcon := &sushi.Icon{
		Code:  "e900",
		Color: suffixIconColor,
	}

	rightButtonCa := sushi.GetClickAction()
	rightButtondeeplink := &sushi.Deeplink{
		URL: util.GetSelectPlansDeeplink(),
	}
	rightButtonCa.SetDeeplink(rightButtondeeplink)

	productName := "CULTPASS PLAY"

	if product.ProductCategoryId == apiCommon.SinglekeyCategoryID {
		productName = productName + " Lite"
	}
	textColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	rightButton := &sushi.Button{
		Text:  fmt.Sprintf("%s - %d %s", productName, product.Duration, duration),
		Color: textColor,
	}
	rightButton.IsActionDisabled = 1
	if !b.IsSingleProductCity && (cityId != apiCommon.BANGALORE_CITY_ID || featuresupport.SupportSingleKeyProduct(ctx)) {
		rightButton.IsActionDisabled = 0
		rightButton.SuffixIcon = suffixIcon
		rightButton.ClickAction = rightButtonCa
		rightButton.ClevertapTracking = []*sushi.ClevertapItem{getTrackingItemForPlanSelectionTap(ctx, product.PId)}
	}

	plan_edit_info_sections := []*productPB.FormField{
		{
			Type:             "start_date",
			CityIdForProduct: product.LocationIdV2,
			IsOptional:       false,
		},
		{
			Type:             "preferred_center",
			CityIdForProduct: product.LocationIdV2,
			IsOptional:       false,
		},
	}

	title, _ := sushi.NewTextSnippet("YOURSELF")
	title.SetKerning(3)

	editButton, _ := sushi.NewTextSnippet("Edit")
	addDetailsButton, _ := sushi.NewTextSnippet("Add details")

	if req.Type == PAGE_TYPE_OTHERS {
		b.UserData = &productModels.UserData{
			User: productModels.MembershipUser{
				UserId:     user.UserId,
				Name:       user.Name,
				Phone:      user.Phone,
				StartDate:  user.StartDate,
				ProductId:  product.ProductId,
				Age:        user.Age,
				DisplayAge: "${age} yrs",
			},
			Title:            title,
			EditButton:       editButton,
			AddDetailsButton: addDetailsButton,
			ProductDetails:   productDetails,
			PlanEditInfo: &productModels.PlanEditInfo{
				Title:       planEditInfoTitle,
				RightButton: rightButton,
				BottomButtonStates: &productModels.BottomButtonStates{
					Disabled: &productModels.BottomButtonState{
						Button: GetDisabledButton(userData.EditInfo.BottomButtonStates),
					},
					Completed: &productModels.BottomButtonState{
						Button: GetCompletedButtonV2(ctx, userData.EditInfo.BottomButtonStates, req.Type, user.Age),
					},
				},
			},
			DisplayAge: "${age} yrs",
		}

		for _, planEditInfoSection := range plan_edit_info_sections {
			section := GetPlanEditInfoSection(ctx, planEditInfoSection, product.ProductCategoryId)
			b.UserData.PlanEditInfo.Sections = append(b.UserData.PlanEditInfo.Sections, section)
		}
	} else {
		b.UserData = &productModels.UserData{
			User: productModels.MembershipUser{
				UserId:    user.UserId,
				Name:      user.Name,
				Phone:     user.Phone,
				StartDate: user.StartDate,
				ProductId: product.ProductId,
				Age:       user.Age,
			},
			Header:         header,
			ProductDetails: productDetails,
			PlanEditInfo: &productModels.PlanEditInfo{
				Title:       planEditInfoTitle,
				RightButton: rightButton,
				BottomButtonStates: &productModels.BottomButtonStates{
					Disabled: &productModels.BottomButtonState{
						Button: GetDisabledButton(userData.EditInfo.BottomButtonStates),
					},
					Completed: &productModels.BottomButtonState{
						Button: GetCompletedButtonV2(ctx, userData.EditInfo.BottomButtonStates, req.Type, user.Age),
					},
				},
			},
			DisplayAge: "${age} yrs",
		}

		for _, planEditInfoSection := range plan_edit_info_sections {
			section := GetPlanEditInfoSection(ctx, planEditInfoSection, product.ProductCategoryId)
			b.UserData.PlanEditInfo.Sections = append(b.UserData.PlanEditInfo.Sections, section)
		}
	}
}

func (b *BuyingForTemplate) SetChildUsersData(ctx context.Context, req *structs.GetBuyOrRenewDetailsRequest, response *productPB.GetBuyingOrRenewalResponse, isRenewal bool) {
	cityId := util.GetCityIDFromContext(ctx)
	if response.ChildUsersData != nil {
		childUsersData := response.ChildUsersData

		product := response.ProductInfo

		childUsersDataTitle, _ := sushi.NewTextSnippet(childUsersData.Title)
		childUsersDataTitle.SetKerning(3)

		editButton, _ := sushi.NewTextSnippet(childUsersData.EditButton)
		addDetailsButton, _ := sushi.NewTextSnippet(childUsersData.AddDetailsButton)

		defaultSectionTitle, _ := sushi.NewTextSnippet(childUsersData.DefaultSection.Title)
		defaultSectionTitleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		defaultSectionTitle.SetColor(defaultSectionTitleColor)
		defaultSectionSubtitle, _ := sushi.NewTextSnippet(childUsersData.DefaultSection.Subtitle)
		rightIcon, _ := sushi.NewIcon(sushi.PlusCircleIcon, defaultSectionTitleColor)

		editInfoTitle, _ := sushi.NewTextSnippet(childUsersData.EditInfo.Title)

		productDuration, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s", product.Duration, product.DurationUnit))
		productPrice, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%d", int32(product.RetailPrice)))

		durationUnit := "Month"
		if product.DurationUnit == "month" && product.Duration > 1 {
			durationUnit = "Months"
		} else if product.DurationUnit == "month" {
			durationUnit = "Month"
		}

		productDetails := &productModels.ProductDetails{
			Duration:  productDuration,
			Price:     productPrice,
			ProductId: product.ProductId,
		}

		planEditInfoTitle, _ := sushi.NewTextSnippet("Plan")

		suffixIcon := &sushi.Icon{
			Code: "e900",
		}

		rightButtonCa := sushi.GetClickAction()
		rightButtondeeplink := &sushi.Deeplink{
			URL: util.GetSelectPlansDeeplink(),
		}
		rightButtonCa.SetDeeplink(rightButtondeeplink)

		productName := "CULTPASS PLAY"

		if product.ProductCategoryId == apiCommon.SinglekeyCategoryID {
			productName = productName + " Lite"
		}
		rightButton := &sushi.Button{
			Text: fmt.Sprintf("%s - %d %s", productName, product.Duration, durationUnit),
		}
		rightButton.IsActionDisabled = 1
		if !b.IsSingleProductCity && (cityId != apiCommon.BANGALORE_CITY_ID || featuresupport.SupportSingleKeyProduct(ctx)) {
			rightButton.IsActionDisabled = 0
			rightButton.SuffixIcon = suffixIcon
			rightButton.ClickAction = rightButtonCa
			rightButton.ClevertapTracking = []*sushi.ClevertapItem{getTrackingItemForPlanSelectionTap(ctx, product.PId)}
		}

		plan_edit_info_sections := []*productPB.FormField{
			{
				Type:             "start_date",
				CityIdForProduct: product.LocationIdV2,
				IsOptional:       false,
			},
			{
				Type:             "preferred_center",
				CityIdForProduct: product.LocationIdV2,
				IsOptional:       false,
			},
		}

		b.ChildUsersData = &productModels.ChildUsersData{
			Title:            childUsersDataTitle,
			StartDate:        childUsersData.StartDate,
			EditButton:       editButton,
			AddDetailsButton: addDetailsButton,
			DefaultSection: productModels.MembershipDefaultSection{
				Title:     defaultSectionTitle,
				Subtitle:  defaultSectionSubtitle,
				RightIcon: rightIcon,
			},
			EditInfo: productModels.MembershipEditInfo{
				Title: editInfoTitle,
				BottomButtonStates: productModels.BottomButtonStates{
					Disabled: &productModels.BottomButtonState{
						Button: GetDisabledButton(childUsersData.EditInfo.BottomButtonStates),
					},
					Edited: &productModels.BottomButtonState{
						Button: GetEditedButton(childUsersData.EditInfo.BottomButtonStates),
					},
					Completed: &productModels.BottomButtonState{
						Button: GetCompletedButton(childUsersData.EditInfo.BottomButtonStates),
					},
				},
			},
			PlanEditInfo: &productModels.PlanEditInfo{
				Title:       planEditInfoTitle,
				RightButton: rightButton,
				BottomButtonStates: &productModels.BottomButtonStates{
					Disabled: &productModels.BottomButtonState{
						Button: GetDisabledButton(childUsersData.EditInfo.BottomButtonStates),
					},
					Edited: &productModels.BottomButtonState{
						Button: GetEditedButton(childUsersData.EditInfo.BottomButtonStates),
					},
					Completed: &productModels.BottomButtonState{
						Button: GetCompletedButton(childUsersData.EditInfo.BottomButtonStates),
					},
				},
			},
			ProductDetails: productDetails,
			DisplayAge:     "${age} yrs",
		}
		for _, child := range childUsersData.Users {
			childMembershipStatusText, _ := sushi.NewTextSnippet(child.ExpiryString)
			childUserToAppend := &productModels.MembershipUser{
				UserId:               child.UserId,
				Name:                 child.Name,
				Phone:                child.Phone,
				StartDate:            child.StartDate,
				MembershipStatusText: childMembershipStatusText,
				ProductId:            child.ProductId,
			}
			if child.Age != 0 {
				childUserToAppend.Age = child.Age
			}
			childUserToAppend.DisplayAge = fmt.Sprintf("${age} yrs")

			preferredFacilityInfo := child.PreferredFacility
			if preferredFacilityInfo != nil && !ValidateSeasonalPoolInfoToShow(preferredFacilityInfo, isRenewal) {

				if featuresupport.SupportsMultipleFeaturedProducts(ctx) && child.PreferredSportId != 0 {
					childUserToAppend.PreferredCenter = GetPreferredCenterAndSportInfo(ctx, preferredFacilityInfo, child.PreferredSportId)
					childUserToAppend.PreferredFacilityId = preferredFacilityInfo.FacilityId
					childUserToAppend.PreferredSportId = child.PreferredSportId
				} else if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
					childUserToAppend.PreferredCenter = GetPreferredCenterInfo(ctx, preferredFacilityInfo)
				} //else do nothing do not add center.
			}

			b.ChildUsersData.Users = append(b.ChildUsersData.Users, childUserToAppend)
		}

		for _, planEditInfoSection := range plan_edit_info_sections {
			section := GetPlanEditInfoSection(ctx, planEditInfoSection, product.ProductCategoryId)
			b.ChildUsersData.PlanEditInfo.Sections = append(b.ChildUsersData.PlanEditInfo.Sections, section)
		}

		for _, editInfoSection := range childUsersData.EditInfo.Sections {
			if featuresupport.SupportsMultipleFeaturedProducts(ctx) && req.Type == PAGE_TYPE_OTHERS {
				if editInfoSection.Type == "start_date" || editInfoSection.Type == "preferred_center" {
					continue
				}
			}
			section := GetEditInfoSection(ctx, editInfoSection)
			b.ChildUsersData.EditInfo.Sections = append(b.ChildUsersData.EditInfo.Sections, section)
		}
	}
}

func (b *BuyingForTemplate) SetBottomButtonStates(response *productPB.GetBuyingOrRenewalResponse) {
	if response.BottomButtonStates != nil {
		b.BottomButtonStates = &productModels.BottomButtonStates{
			Disabled: &productModels.BottomButtonState{
				Button: GetDisabledButton(response.BottomButtonStates),
			},
			Completed: &productModels.BottomButtonState{
				Button:     GetCompletedButton(response.BottomButtonStates),
				PluralText: response.BottomButtonStates.PluralCompletedText,
			},
		}
	}
}

func (b *BuyingForTemplate) SetTrackingAttributes(ctx context.Context, json structs.GetBuyOrRenewDetailsRequest) {
	contextType := "buy"
	if json.IsRenewal {
		contextType = "renewal"
	}
	b.TrackingAttributes = &productModels.TrackingAttributes{
		Type:        json.Type,
		CityId:      util.GetCityIDFromContext(ctx),
		UserId:      util.GetUserIDFromContext(ctx),
		ProductId:   json.ProductId,
		ContextType: contextType,
	}
}

func GetEditInfoSection(ctx context.Context, editInfoSection *productPB.FormField) *productModels.EditInfoSection {
	section := &productModels.EditInfoSection{
		Type: editInfoSection.Type,
	}
	switch editInfoSection.Type {
	case "start_date":
		placeholder, _ := sushi.NewTextSnippet(editInfoSection.Placeholder)
		section.StartDate = &productModels.EditInfoSectionField{
			Optional:       editInfoSection.IsOptional,
			Placeholder:    placeholder,
			DurationInDays: editInfoSection.MaxAddition,
			States:         GetEditInfoSectionStates(editInfoSection),
		}
		break
	case "preferred_center":
		title, _ := sushi.NewTextSnippet(editInfoSection.Title)
		placeholderContainerTitle, _ := sushi.NewTextSnippet(editInfoSection.PlaceholderContainer)
		placeholderContainerIcon, _ := sushi.NewIcon(sushi.RightIcon, nil)
		section.PreferredCenter = &productModels.EditInfoSectionField{
			Optional: editInfoSection.IsOptional,
			Title:    title,
			PlaceholderContainer: &productModels.EditInfoSectionFieldPlaceholderContainer{
				Title: placeholderContainerTitle,
				Icon:  placeholderContainerIcon,
			},
			States: GetEditInfoSectionStates(editInfoSection),
		}

		cityID := util.GetCityIDFromContext(ctx)

		if cityID != editInfoSection.CityIdForProduct {

			cityChangeClickAction := sushi.GetClickAction()
			customAlert := CustomAlertForCityChange()
			cityChangeClickAction.SetCustomAlertAction(customAlert)
			editButton := &productModels.EditButtonCustom{
				Text:        editInfoSection.EditButton,
				ClickAction: cityChangeClickAction,
			}

			section.PreferredCenter.EditButton = editButton
		} else {
			editButton := &productModels.EditButtonCustom{
				Text: editInfoSection.EditButton,
			}
			section.PreferredCenter.EditButton = editButton
		}

		break
	case "age":
		placeholder, _ := sushi.NewTextSnippet(editInfoSection.Placeholder)
		section.Age = &productModels.EditInfoSectionField{
			Optional:    editInfoSection.IsOptional,
			Placeholder: placeholder,
			States:      GetEditInfoSectionStates(editInfoSection),
		}
		break
	case "name":
		placeholder, _ := sushi.NewTextSnippet(editInfoSection.Placeholder)
		section.Name = &productModels.EditInfoSectionField{
			Optional:    editInfoSection.IsOptional,
			Placeholder: placeholder,
			States:      GetEditInfoSectionStates(editInfoSection),
		}
		break
	case "mobile":
		placeholder, _ := sushi.NewTextSnippet(editInfoSection.Placeholder)
		section.Mobile = &productModels.EditInfoSectionField{
			Optional:    editInfoSection.IsOptional,
			Placeholder: placeholder,
			States:      GetEditInfoSectionStates(editInfoSection),
		}
		break
	}
	return section
}

func GetPlanEditInfoSection(ctx context.Context, planEditInfoSection *productPB.FormField, productCategory int32) *productModels.PlanEditInfoSection {
	section := &productModels.PlanEditInfoSection{
		Type: planEditInfoSection.Type,
	}
	switch planEditInfoSection.Type {
	case "start_date":
		title, _ := sushi.NewTextSnippet("Select your plan start date")
		startDateToolTipText := "You can choose plan start date upto " + strconv.Itoa(PLAN_START_DATE_THRESHOLD_DAYS) +" days from ${start_date}"
		rightIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		rightIcon, _ := sushi.NewIcon(sushi.InfoIcon, rightIconColor)
		buttonTitle, _ := sushi.NewTextSnippet(startDateToolTipText)
		buttonTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonTitle.SetColor(buttonTitleColor)
		buttonTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		buttonTitle.SetFont(buttonTitleFont)
		buttonSubtitle, _ := sushi.NewTextSnippet("OK")
		buttonSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonSubtitle.SetColor(buttonSubtitleColor)
		buttonSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		buttonSubtitle.SetFont(buttonSubtitleFont)
		buttonBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		showTooltip := &sushi.ShowTooltip{
			Title:    buttonTitle,
			Subtitle: buttonSubtitle,
			BgColor:  buttonBgColor,
		}
		clickAction := sushi.GetClickAction()
		clickAction.SetShowTooltip(showTooltip)
		rightIcon.SetClickAction(clickAction)
		trackingItemStartDate := getTrackingItemForToolTips(ctx, "select_date_tooltip_tap")
		rightIcon.ClevertapTracking = []*sushi.ClevertapItem{trackingItemStartDate}

		placeholderContainerTitle, _ := sushi.NewTextSnippet("Choose plan start date")
		placeholderContainerTitleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		placeholderContainerTitle.SetColor(placeholderContainerTitleColor)
		placeholderContainerTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		placeholderContainerTitle.SetFont(placeholderContainerTitleFont)

		placeholderContainerIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		placeholderContainerIcon, _ := sushi.NewIcon(sushi.CalenderIcon, placeholderContainerIconColor)

		section.StartDate = &productModels.SectionItem{
			Title:          title,
			RightIcon:      rightIcon,
			DurationInDays: PLAN_START_DATE_THRESHOLD_DAYS,
			EditButton: &productModels.EditButtonCustom{
				Text: "Change",
			},
			PlaceholderContainer: &productModels.EditInfoSectionFieldPlaceholderContainer{
				Title: placeholderContainerTitle,
				Icon:  placeholderContainerIcon,
			},
			Optional: planEditInfoSection.IsOptional,
		}
		break
	case "preferred_center":
		centerSportText := "Select your Preferred sport & center"
		if productCategory == common.SinglekeyCategoryID {
			centerSportText = "Select your preferred center"
		}
		title, _ := sushi.NewTextSnippet(centerSportText)

		rightIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		rightIcon, _ := sushi.NewIcon(sushi.InfoIcon, rightIconColor)
		buttonTitle, _ := sushi.NewTextSnippet("Get priority while booking your favourite sport at your preferred center.")
		buttonTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonTitle.SetColor(buttonTitleColor)
		buttonTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		buttonTitle.SetFont(buttonTitleFont)
		buttonSubtitle, _ := sushi.NewTextSnippet("OK")
		buttonSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		buttonSubtitle.SetColor(buttonSubtitleColor)
		buttonSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		buttonSubtitle.SetFont(buttonSubtitleFont)
		buttonBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		showTooltip := &sushi.ShowTooltip{
			Title:    buttonTitle,
			Subtitle: buttonSubtitle,
			BgColor:  buttonBgColor,
		}
		clickAction := sushi.GetClickAction()
		clickAction.SetShowTooltip(showTooltip)
		rightIcon.SetClickAction(clickAction)
		trackingItemPreferredCenter := getTrackingItemForToolTips(ctx, "select_preferred_center_tooltip_tap")
		rightIcon.ClevertapTracking = []*sushi.ClevertapItem{trackingItemPreferredCenter}

		placeholderContainerTitle, _ := sushi.NewTextSnippet("Set preference")
		placeholderContainerTitleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		placeholderContainerTitle.SetColor(placeholderContainerTitleColor)
		placeholderContainerTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		placeholderContainerTitle.SetFont(placeholderContainerTitleFont)

		placeholderContainerIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		placeholderContainerIcon, _ := sushi.NewIcon(sushi.RightIcon, placeholderContainerIconColor)

		preferredCenterClickAction := getClickActionForSelectPreferredCenter(ctx)
		if productCategory == common.SinglekeyCategoryID {
			deeplink := util.GetPreferredCenterPageDeeplinkWithSportID(common.BADMINTON_SPORT_ID, true)

			payload := map[string]interface{}{
				"sport_id":             common.BADMINTON_SPORT_ID,
				"is_singlekey_product": true,
			}
			postback_params, _ := json.Marshal(payload)

			preferredCenterClickAction = sushi.GetClickAction()
			preferredCenterClickAction.SetClickActionType(sushi.ClickActionDeeplink)
			preferredCenterClickAction.Deeplink = &sushi.Deeplink{
				URL:            deeplink,
				PostbackParams: string(postback_params),
			}
		}
		section.PreferredCenter = &productModels.SectionItem{
			Title:          title,
			RightIcon:      rightIcon,
			DurationInDays: PLAN_START_DATE_THRESHOLD_DAYS,
			EditButton: &productModels.EditButtonCustom{
				Text:        "Change",
				ClickAction: preferredCenterClickAction,
			},
			PlaceholderContainer: &productModels.EditInfoSectionFieldPlaceholderContainer{
				Title:       placeholderContainerTitle,
				Icon:        placeholderContainerIcon,
				ClickAction: preferredCenterClickAction,
			},
			Optional: planEditInfoSection.IsOptional,
		}

		cityID := util.GetCityIDFromContext(ctx)
		if cityID != planEditInfoSection.CityIdForProduct {

			cityChangeClickAction := sushi.GetClickAction()
			customAlert := CustomAlertForCityChange()
			cityChangeClickAction.SetCustomAlertAction(customAlert)
			editButton := &productModels.EditButtonCustom{
				Text:        "Change",
				ClickAction: cityChangeClickAction,
			}

			section.PreferredCenter.EditButton = editButton
			section.PreferredCenter.PlaceholderContainer.ClickAction = cityChangeClickAction
		} else {
			editButton := &productModels.EditButtonCustom{
				Text: "Change",
			}
			section.PreferredCenter.EditButton = editButton
		}
		break
	}
	return section
}

func GetEditInfoSectionStates(editInfoSection *productPB.FormField) productModels.EditInfoSectionFieldStates {
	var states productModels.EditInfoSectionFieldStates
	editInfoSectionStates := editInfoSection.States
	if editInfoSectionStates.Always != "" {
		alwaysTitle, _ := sushi.NewTextSnippet(editInfoSectionStates.Always)
		states.Always = &productModels.EditInfoSectionFieldStateTitle{
			Title: alwaysTitle,
		}
	}

	if editInfoSectionStates.Empty != "" {
		emptyTitle, _ := sushi.NewTextSnippet(editInfoSectionStates.Empty)
		states.Empty = &productModels.EditInfoSectionFieldStateTitle{
			Title: emptyTitle,
		}
	}

	if editInfoSectionStates.Error != "" {
		errorTitle, _ := sushi.NewTextSnippet(editInfoSectionStates.Error)
		switch editInfoSection.Type {
		case "age":
			states.MinAge = &productModels.EditInfoSectionFieldStateMinAge{
				Age:        editInfoSection.MinValue,
				ErrorTitle: errorTitle,
			}
			break
		case "mobile":
			states.CharLength = &productModels.EditInfoSectionFieldStateCharLength{
				Length:     editInfoSection.MinValue,
				ErrorTitle: errorTitle,
			}
		}
	}

	if editInfoSectionStates.ErrorMinAge != "" {
		errorMinAgeTitle, _ := sushi.NewTextSnippet(editInfoSectionStates.ErrorMinAge)
		states.MinAge = &productModels.EditInfoSectionFieldStateMinAge{
			Age:        editInfoSection.MinAgeValue,
			ErrorTitle: errorMinAgeTitle,
		}
	}

	return states
}

func GetDisabledButton(bottomButtonStates *productPB.BottomButtonStates) *sushi.Button {
	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButtonColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(disabledButtonColor)
	disabledButton.SetActionDisabled()
	disabledButton.SetText(bottomButtonStates.DisabledText)
	return disabledButton
}

func GetEditedButton(bottomButtonStates *productPB.BottomButtonStates) *sushi.Button {
	editedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	editedButtonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	editedButtonClickAction := sushi.GetClickAction()
	editedButtonClickAction.Type = bottomButtonStates.EditedClickAction
	editedButton.SetBgColor(editedButtonColor)
	editedButton.SetText(bottomButtonStates.EditedText)
	editedButton.SetClickAction(editedButtonClickAction)
	return editedButton
}

func GetCompletedButton(bottomButtonStates *productPB.BottomButtonStates) *sushi.Button {
	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButtonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButtonClickAction := sushi.GetClickAction()
	completedButtonClickAction.Type = bottomButtonStates.CompletedClickAction
	completedButton.SetBgColor(completedButtonColor)
	completedButton.SetText(bottomButtonStates.CompletedText)
	completedButton.SetClickAction(completedButtonClickAction)
	return completedButton
}

func GetCompletedButtonV2(ctx context.Context, bottomButtonStates *productPB.BottomButtonStates, reqType string, userAge int32) *sushi.Button {
	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButtonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButtonClickAction := sushi.GetClickAction()
	completedButtonClickAction.Type = bottomButtonStates.CompletedClickAction
	if userAge < MIN_AGE_FOR_PURCHASE {
		if reqType == PAGE_TYPE_OTHERS {
			completedButtonClickAction = getAgeValidationBottomSheet(ctx, false)
		} else {
			completedButtonClickAction = getAgeValidationBottomSheet(ctx, true)
		}
		trackingItem := getTrackingItemForAgeValidationBottomSheet(ctx)
		completedButton.ClevertapTracking = []*sushi.ClevertapItem{trackingItem}
	} else {
		trackingItem := getProceedToBuyMkTrackingItem(ctx)
		completedButton.ClevertapTracking = []*sushi.ClevertapItem{trackingItem}
	}
	completedButton.SetBgColor(completedButtonColor)
	completedButton.SetText(bottomButtonStates.CompletedText)
	completedButton.SetClickAction(completedButtonClickAction)
	return completedButton
}

//returns true preferred center should be shown in renewal else false PS: will deselect old preferred center if seasonal pool and season is mid,close,closing
func ValidateSeasonalPoolInfoToShow(facility *productPB.Facility, isRenewal bool) bool {
	if isRenewal == true && facility.SeasonalPoolInfo != nil && facility.SeasonalPoolInfo.Flag == true && (facility.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_MID_SEASON || facility.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || facility.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSING_SEASON) {
		return true
	}
	return false
}

func GetPreferredCenterInfo(ctx context.Context, preferredFacilityInfo *productPB.Facility) *sushi.V2ImageTextSnippetType31SnippetItem {

	// setting facility title
	facility_title, _ := sushi.NewTextSnippet(preferredFacilityInfo.DisplayName)
	facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	facility_title.SetFont(facility_title_font)
	facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	facility_title.SetColor(facility_title_color)

	// setting facility subtitle
	facility_subtitle, _ := sushi.NewTextSnippet(preferredFacilityInfo.ShortAddress)
	facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
	facility_subtitle.SetFont(facility_subtitle_font)
	facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	facility_subtitle.SetColor(facility_subtitle_color)

	// setting facility tag
	tag := &sushi.Tag{}
	if preferredFacilityInfo.Distance != 0 {
		facility_tag_title, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f km", preferredFacilityInfo.Distance))
		facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		facility_tag_title.SetFont(facility_tag_title_font)
		facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_tag_title.SetColor(facility_tag_title_color)

		// setting facility tag bg color
		facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag = &sushi.Tag{
			Title:   facility_tag_title,
			BgColor: facility_tag_bgcolor,
		}
	}

	// setting facility rating title
	facility_rating_title, _ := sushi.NewTextSnippet(preferredFacilityInfo.Rating)
	facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	facility_rating_title.SetFont(facility_rating_title_font)
	facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
	facility_rating_title.SetColor(facility_rating_title_color)
	facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
	facility_rating_title.SetPrefixIcon(ratingIcon)

	// setting facility rating bg color
	facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		facility_rating_bgcolor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}

	ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

	if preferredFacilityInfo.Rating == apiCommon.DEFAULT_FACILITY_RATING {
		ratingTitle, _ := sushi.NewTextSnippet(apiCommon.NEW_FACILITY_SPORT_TAG)
		ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

		ratingSnippetBlockItem.Title = ratingTitle
		ratingSnippetBlockItem.BgColor = ratingBgColor
		ratingSnippetBlockItem.Size = sushi.RatingSize300

	} else {
		ratingSnippetBlockItem.Title = facility_rating_title
		ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
	}

	rating := ratingSnippetBlockItem

	facility_image := &sushi.Image{
		URL:         preferredFacilityInfo.DisplayPicture,
		AspectRatio: 1,
		Type:        sushi.ImageTypeRounded,
		Height:      64,
		Width:       64,
	}

	facility_obj := &sushi.V2ImageTextSnippetType31SnippetItem{
		Id:       preferredFacilityInfo.FacilityId,
		Title:    facility_title,
		Subtitle: facility_subtitle,
		Tag:      tag,
		Rating:   rating,
		Image:    facility_image,
	}

	return facility_obj
}

func GetPreferredCenterAndSportInfo(ctx context.Context, preferredFacilityInfo *productPB.Facility, preferredSportId int32) *sushi.V2ImageTextSnippetType31SnippetItem {

	// setting facility title
	facility_title, _ := sushi.NewTextSnippet(preferredFacilityInfo.DisplayName)
	facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	facility_title.SetFont(facility_title_font)
	facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	facility_title.SetColor(facility_title_color)

	// setting facility subtitle
	facility_subtitle, _ := sushi.NewTextSnippet(preferredFacilityInfo.ShortAddress)
	facility_subtitle_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	facility_subtitle.SetFont(facility_subtitle_font)
	facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	facility_subtitle.SetColor(facility_subtitle_color)

	sportName := common.SportIdSportNameMap[preferredSportId]
	facility_subtitle1, _ := sushi.NewTextSnippet(sportName)
	facility_subtitle1_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	facility_subtitle1.SetFont(facility_subtitle1_font)
	facility_subtitle1_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	facility_subtitle1.SetColor(facility_subtitle1_color)

	cl := util.GetFacilitySportClient()
	req := facilityPB.SportRequest{
		SportId:     preferredSportId,
		MinimalData: true,
	}
	sportDetails, err := cl.GetSport(ctx, &req)
	if err != nil {
		log.Println("Error getting sport details from GetPreferredCenterAndSportInfo: ", err)
		return nil
	}
	sportImage := sportDetails.Sport[0].Icon
	leftImage, _ := sushi.NewImage(sportImage)
	leftImage.SetHeight(24)
	leftImage.SetWidth(24)

	facility_obj := &sushi.V2ImageTextSnippetType31SnippetItem{
		Id:        preferredFacilityInfo.FacilityId,
		Title:     facility_title,
		Subtitle:  facility_subtitle,
		Subtitle1: facility_subtitle1,
		LeftImage: leftImage,
	}

	return facility_obj
}

func CustomAlertForCityChange() *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("Your city is invalid!")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("Current selected city on the app and the membership city does not match. Please change your location to view preferred centers of the membership city")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)

	pos_click_action := sushi.GetClickAction()
	pos_deep_link := &sushi.Deeplink{
		URL: util.GetChangeLocationDeeplink(),
	}
	pos_click_action.SetDeeplink(pos_deep_link)
	positiveAction := &sushi.Button{
		Type:        "text",
		Text:        "Change location",
		ClickAction: pos_click_action,
	}

	neg_action_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	negativeAction := &sushi.Button{
		Type:  "text",
		Text:  "Do it later",
		Color: neg_action_color,
	}

	custom_alert := &sushi.CustomAlert{
		Title:          title,
		Message:        message,
		PositiveAction: positiveAction,
		NegativeAction: negativeAction,
		DismissAfterAction: true,
	}

	return custom_alert
}

func getTrackingItemForPlanSelectionTap(ctx context.Context, PId int32) *sushi.ClevertapItem {
	tapPayload := getCommonPayloadForTracking(ctx)
	tapPayload["pre_selected_product"] = PId
	tapEname := &sushi.EnameData{
		Ename: "plan_type_change_click",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func getTrackingItemForToolTips(ctx context.Context, ename string) *sushi.ClevertapItem {
	tapPayload := getCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: ename,
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func getTrackingItemForAgeValidationBottomSheet(ctx context.Context) *sushi.ClevertapItem {
	tapPayload := getCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "age_validation_bottomsheet_opened",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func getProceedToBuyMkTrackingItem(ctx context.Context) *sushi.ClevertapItem {
	tapPayload := getCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "proceed_to_buy_masterkey",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func getAgeSaveTrackingItem(ctx context.Context, buyForMe bool) *sushi.ClevertapItem {
	tapPayload := getCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "proceed_to_buy_masterkey_after_age_update",
	}
	if !buyForMe {
		tapEname.Ename = "save_age_details"
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)
	return trackItem
}

func getCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "plan_summary"
	return commonPayload
}
