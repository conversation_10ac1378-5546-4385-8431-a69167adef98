package productController

import (
	"context"
	"fmt"
	"log"
	"net/http"
	//"time"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	//featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func CalculateSummercampCartC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	//ctx, _ = context.WithTimeout(context.Background(), time.Duration(time.Millisecond*8000))
	json := c.MustGet("jsonData").(structs.CalculateAcademyCartRequest)

	var parentUser *productPB.CartUser
	if json.ParentUser != nil {
		user := json.ParentUser
		parentUser = &productPB.CartUser{
			UserId:        user.UserId,
			Name:          user.Name,
			PlanStartDate: user.PlanStartDate,
			ProductId:     user.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range user.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			parentUser.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not selected for a logged in user"))
			return
		}
	}

	var childUsersToSubscribe []*productPB.CartUser
	for _, childUser := range json.ChildUsers {
		childUserToSubscribe := &productPB.CartUser{
			UserId:        childUser.UserId,
			Name:          childUser.Name,
			PlanStartDate: childUser.PlanStartDate,
			ProductId:     childUser.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range childUser.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			childUserToSubscribe.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not selected for a child user"))
			return
		}
		childUsersToSubscribe = append(childUsersToSubscribe, childUserToSubscribe)
	}

	productClient := util.GetProductClient()
	response, err := productClient.CalculateSummercampCart(ctx, &productPB.CalculateCartRequest{
		ChildUsers:   childUsersToSubscribe,
		ReferralCode: json.ReferralCode,
		PromoCode:    json.PromoCode,
		ParentUser:   parentUser,
	})

	if err != nil {
		log.Printf("Error in calculating academy cart: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("Something went wrong!"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.JSON(http.StatusUnauthorized, models.StatusUnauthorized("You are not authorized!"))
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED && !response.ShowPopup {
		c.JSON(http.StatusOK, response.Status)
		return
	}

	cartPage := CalculateCartData{
		promoCode:         json.PromoCode,
		productCategoryId: common.SummerCampCategoryID,
	}
	if response.ShowPopup {
		cartPage.SetCustomAlertCapacityIssueSection(ctx)
		sushiResponseSections := &productModel.CalculateCartResponseV2{
			ActionList:        cartPage.ActionList,
		}
		c.JSON(
			http.StatusOK,
			sushiResponseSections,
		)
		return
	}

	var cartUsers []*structs.CommonCartUser
	for _, user := range response.FinalCartUsers {
		cartUsers = append(cartUsers, &structs.CommonCartUser{
			UserId:           user.UserId,
			Name:             user.Name,
			ProductName:      user.ProductName,
			ProductDuration:  user.ProductDuration,
			ProductFrequency: user.ProductFrequency,
			FormattedAmount:  user.FormattedAmount,
		})
	}

	autoApplyPromoCode := cartPage.promoCode == ""

	cartPage.setCalculateCartData(response)

	eligibleVoucherReq := &productPB.GetEligibleVouchersReq{
		ProductDetails: response.ProductDetails,
		UsersCount:     int32(len(response.FinalCartUsers)),
	}
	eligibleVoucherRes, err := productClient.GetEligibleVouchers(ctx, eligibleVoucherReq)
	if err != nil {
		log.Printf("error in getting eligible vouchers, %v", err)
		c.JSON(
			http.StatusInternalServerError,
			models.StatusFailure("Something went wrong!"),
		)
		return
	}

	cartPage.SetSummercampFullCartPageHeaderSection()
	cartPage.SetFullCartPageTopContainer(ctx, response.FinalCartUsers)
	cartPage.SetAcademyFullCartPageProductSection(ctx, response)
	cartPage.SetApplyPromoCodeSection(ctx, response.FormattedDiscount, response.DiscountAmount, response.VoucherValid, response.VoucherError, autoApplyPromoCode, eligibleVoucherRes)
	cartPage.SetFullCartPageCartValueSection(ctx, response.FormattedTotalCartValue, response.TotalCartValue, response.VoucherValid, response.DiscountAmount)
	cartPage.SetFullCartClevertapTrackingImpression(ctx)
	cartPage.SetFullCartPageFooterSection(ctx, response.FormattedTotalCartValue)
	//cartPage.SetLinksSection(ctx)

	sushiResponseSections := &productModel.CalculateCartResponseV2{
		Header:     cartPage.Header,
		Results:    cartPage.Results,
		Footer:     cartPage.Footer,
		ActionList: cartPage.ActionList,
	}

	c.JSON(
		http.StatusOK,
		sushiResponseSections,
	)
}

func (cc *CalculateCartData) SetCustomAlertCapacityIssueSection(ctx context.Context) {
	log.Println("SetCustomAlertCapacityIssueSection called ")

	customAlert := util.CustomAlertForCapacityIssue(ctx)

	actionListItem := &productModel.ActionListItem{
		Type:        "custom_alert",
		CustomAlert: customAlert,
	}

	cc.ActionList = append(cc.ActionList, actionListItem)
}

func (cc *CalculateCartData) SetSummercampFullCartPageHeaderSection() {

	text := fmt.Sprintf("Summer Camp")
	title, _ := sushi.NewTextSnippet(text)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	cc.Header = &productModel.CalculateCartHeaderSection{
		Title: title,
	}
}


func (cc *CalculateCartData) SetLinksSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	var buttonDataItems []sushi.FitsoTextSnippetType1ButtonItem

	rightArrowIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	rightArrowIcon, _ := sushi.NewIcon(sushi.RightArrowOutlineIcon, rightArrowIconColor)

	faqClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	faqClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: "See FAQs",
		URL:   "https://www.getfitso.com/faq?force_browser=1",
	})

	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapEname := &sushi.EnameData{
		Ename: "summercamp_faq_tap",
	}
	faqTapEvent := sushi.NewClevertapEvents()
	faqTapEvent.SetTap(tapEname)
	faqTapTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, faqTapEvent)

	faqButtonDataItem := sushi.FitsoTextSnippetType1ButtonItem{
		Type:              sushi.FitsoTextSnippetType1TypeText,
		Text:              "See FAQs",
		Size:              sushi.FitsoTextSnippetType1ButtonSizeMedium,
		SuffixIcon:        rightArrowIcon,
		ClickAction:       faqClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{faqTapTrackItem},
	}
	buttonDataItems = append(buttonDataItems, faqButtonDataItem)

	tncClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	tncClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: "Terms & Conditions",
		URL:   "https://www.getfitso.com/terms?force_browser=1",
	})

	tapTncEname := &sushi.EnameData{
		Ename: "summercamp_t&c_tap",
	}
	tcTapEvent := sushi.NewClevertapEvents()
	tcTapEvent.SetTap(tapTncEname)
	tncTapTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, tcTapEvent)

	tncButtonDataItem := sushi.FitsoTextSnippetType1ButtonItem{
		Type:              sushi.FitsoTextSnippetType1TypeText,
		Text:              "Terms & Conditions",
		Size:              sushi.FitsoTextSnippetType1ButtonSizeMedium,
		SuffixIcon:        rightArrowIcon,
		ClickAction:       tncClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{tncTapTrackItem},
	}
	buttonDataItems = append(buttonDataItems, tncButtonDataItem)

	buttonData := &sushi.FitsoTextSnippetType1SnippetButton{
		Orientation: sushi.FitsoTextSnippetType1OrientationVertical,
		Items:       &buttonDataItems,
	}
	snippet := &sushi.FitsoTextSnippetType1Snippet{
		ButtonData: buttonData,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: separator,
	}
	linksSection := &productModel.ProductResult{
		LayoutConfig:          layoutConfig,
		SnippetConfig:         snippetConfig,
		FitsoTextSnippetType1: snippet,
	}
	cc.Results = append(cc.Results, linksSection)
}
