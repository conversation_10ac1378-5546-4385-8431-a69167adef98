// product controller

package productController

import (
	"bitbucket.org/jogocoin/go_api/api/common"

	"context"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strconv"
	"time"

	model "bitbucket.org/jogocoin/go_api/api/models"
	authPB "bitbucket.org/jogocoin/go_api/api/proto/auth"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/render"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/client/selector/shard"
)

var (
	cl productPB.ProductService
)

func GetC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = productPB.NewProductService(serviceList.Product, service.Client())
	// cl = authPB.NewAuthService("product.service.auth", client.DefaultClient)

	var json structs.Product

	json = c.MustGet("jsonData").(structs.Product)

	productData := &productPB.ProductRequest{
		PId:               json.PID,
		DurationInDays:    json.DurationInDays,
		ProductCategoryId: json.ProductCategoryId,
		FsId:              json.FSID,
		IsVisible:         json.IsVisible,
	}

	// session affinity based call
	response, err := cl.ProductGet(context.TODO(), productData, shard.Strategy(string(json.PID)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}
	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateUpdateProductC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.ADD_PRODUCT_PERMISSION, common.EDIT_PRODUCT_PERMISSION}, []int32{common.MANAGE_PRODUCT_TOOL, common.FACILITY_PRODUCT_MAP_TOOL})
	if !authorized {
		log.Println("CreateUpdateProductC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = productPB.NewProductService(serviceList.Product, service.Client())
	// cl = authPB.NewAuthService("product.service.auth", client.DefaultClient)

	var json structs.Product

	json = c.MustGet("jsonData").(structs.Product)

	var fsDataPb []*productPB.FSData
	for _, ele := range json.FsData {

		//today's date by default
		var aDate int64
		aDate = time.Now().Unix()

		fsData := &productPB.FSData{
			FsId:             ele.FSID,
			ActivationDate:   aDate,
			DeactivationDate: ele.DeactivationDate,
		}
		fsDataPb = append(fsDataPb, fsData)
	}
	var productCourseMappings []*productPB.ProductCourseMapping
	if json.ProductCategoryId == common.AcademyCategoryID {
		for _, data := range json.ProductCourseMappings {
			sportData := &productPB.Sport{
				SportId: data.SportId,
			}
			productCourseData := &productPB.ProductCourseMapping{
				Sport:      sportData,
				DaysOfWeek: data.DaysOfWeek,
				Duration:   data.Duration,
			}
			productCourseMappings = append(productCourseMappings, productCourseData)
		}
	}
	productData := &productPB.ProductRequest{
		PId:                         json.PID,
		IsPass:                      json.IsPass,
		FsId:                        json.FSID,
		ProductKey:                  json.ProductKey,
		Price:                       json.Price,
		SessionCount:                json.SessionCount,
		AllSportPass:                json.AllSportPass,
		Duration:                    json.Duration,
		DurationUnit:                json.DurationUnit,
		DurationInDays:              json.DurationInDays,
		AllSlotsFlag:                json.AllSlotsFlag,
		ProductDescription:          json.ProductDescription,
		SlotId:                      json.SlotId,
		PlanDays:                    json.PlanDays,
		TotalSellingCapacity:        json.TotalSellingCapacity,
		RetailPrice:                 json.RetailPrice,
		LocationId:                  json.LocationId,
		AllFacilitySportFlag:        json.AllFacilitySportFlag,
		IsVisible:                   json.IsVisible,
		IsActive:                    json.IsActive,
		IsTrial:                     json.IsTrial,
		ProductCategoryId:           json.ProductCategoryId,
		FeatureType:                 json.FeatureType,
		MaxSportsActiveBookingLimit: json.MaxSportsActiveBookingLimit,
		ActiveBookingCount:          json.ActiveBookingCount,
		PpTokenAmount:               json.PpTokenAmount,
		PpFinalAmount:               json.PpFinalAmount,
		TokenPurchaseEligible:       json.TokenPurchaseEligible,
		CreatedBy:                   json.CreatedBy,
		FsData:                      fsDataPb,
		AllFacilities:               json.AllFacilities,
		AllSports:                   json.AllSports,
		AllSlots:                    json.AllSlots,
		AllLocations:                json.AllLocations,
		CoachingIncluded:            json.CoachingIncluded,
		PfsmProductIds:              sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.PfsmProductIds),
		ClearCache:                  json.ClearCache,
		LocationIdV2:                json.LocationIdV2,
		ProductCourseMappings:       productCourseMappings,
		CourseCategoryId:            json.CourseCategoryId,
	}
	response, err := cl.CreateUpdateProduct(context.TODO(), productData, shard.Strategy(string(json.PID)))

	if err != nil {
		log.Printf("func:CreateUpdateProductC: Error in creating product %d, name %s, err: %v", json.PID, json.ProductDescription, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in saving product"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
}

func GetProductLinkC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = productPB.NewProductService(serviceList.Product, service.Client())
	acl := authPB.NewAuthService(serviceList.Auth, service.Client())

	var json structs.ProductLinkRequest
	json = c.MustGet("jsonData").(structs.ProductLinkRequest)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userID int32
	var fromApp bool
	var authUserID int32

	appType := headers.AppType

	if appType == "sports-ios" || appType == "sports-android" {
		userID = int32(headers.UserId)
		fromApp = true
	} else {
		userID = json.UserID
		fromApp = false
	}

	token := headers.AuthToken

	if token != "" {
		tokenData := &authPB.Token{
			Token:   token,
			AppType: appType,
		}

		response, err := acl.ValidateToken(c, tokenData)

		if err == nil && response.Valid {
			authUserID = response.User.UserId
		} else {
			authUserID = 0
		}
	}

	productLinkData := &productPB.ProductLinkRequest{
		Purpose:             json.Purpose,
		RetailPrice:         json.RetailPrice,
		PId:                 json.PID,
		UserId:              userID,
		Ttl:                 json.TTL,
		CouponApplicable:    json.CouponApplicable,
		MinUsersPerPurchase: json.MinUsersPerPurchase,
		EmployeeCode:        json.EmployeeCode,
		OfferId:             json.OfferId,
		FromApp:             fromApp,
		AppType:             headers.AppType,
		AppVersion:          headers.AppVersion,
		UserEncodeVal:       json.UserEncodeVal,
		PurchaseType:        json.PurchaseType,
		RemainingAmount:     json.RemainingAmount,
		SubscriptionId:      json.SubscriptionId,
		UserIds:             json.UserIds,
		GroupOffer:          json.GroupOffer,
		MaxUsersPerGroup:    json.MaxUsersPerGroup,
		AuthUserId:          authUserID,
	}

	// session affinity based call
	response, err := cl.ProductLinkGet(context.TODO(), productLinkData, shard.Strategy(string(json.PID)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GenerateBulkPurchaseLinkC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL,common.SHOW__TOOL}, []int32{common.BULK_PAYMENT_LINK_TOOL})
	if !authorized {
		log.Println("GenerateBulkPurchaseLinkC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.BulkProductLinkRequest
	json = c.MustGet("jsonData").(structs.BulkProductLinkRequest)
	productLinkData := &productPB.BulkProductLinkRequest{
		Purpose:             json.Purpose,
		PIds:                json.PIDs,
		Ttl:                 json.TTL,
		CouponApplicable:    json.CouponApplicable,
		MinUsersPerPurchase: json.MinUsersPerPurchase,
	}
	response, err := cl.BulkPaymentLinkGet(context.TODO(), productLinkData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func checkDashboardUserAuthorised(toolPermissions interface{}, permissions []string, toolIds []int32) bool {
	if maptoolsPermissions, ok := toolPermissions.(map[int32]*userPB.ListOfPermissions);ok  {
		for j:= 0 ; j <len(toolIds); j++ {
			log.Println("checkDashboardUserAuthorised: ", maptoolsPermissions[toolIds[j]])
			if maptoolsPermissions[toolIds[j]] != nil && len(maptoolsPermissions[toolIds[j]].Permissions) > 0{
				for _,permission := range maptoolsPermissions[toolIds[j]].Permissions {
					for i := 0; i <len(permissions); i++ {
						log.Println("checkDashboardUserAuthorised permissions",permission, permissions[i], maptoolsPermissions[toolIds[j]])
						if permissions[i] == permission {
							log.Println("checkDashboardUserAuthorised permissions granted ",permission, permissions[i],maptoolsPermissions[toolIds[j]])
							return true
						}
					}
				}
			}
		}
	}

	return false
}

func GetSubscriptionC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW_TOOL,common.SHOW__TOOL,common.EDIT_SUBSCRIPTION_PERMISSION}, []int32{common.SUBSCRIPTION_TOOL, common.RECIEPT_GENERATION_TOOL})
	if !authorized {
		log.Println("GetSubscriptionC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}


	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.Subscription

	json = c.MustGet("jsonData").(structs.Subscription)

	subscriptionData := &productPB.Subscription{
		SubscriptionId:             json.SubscriptionID,
		ProductId:                  json.ProductID,
		UserId:                     json.UserID,
		PurchaseId:                 json.PurchaseID,
		SessionCount:               json.SessionCount,
		IsActive:                   json.IsActive,
		FetchProduct:               json.FetchProduct,
		FsId:                       json.FsId,
		SendWithoutSessions:        json.SendWithoutSessions,
		FetchWithoutFacilitySport:  json.FetchWithoutFacilitySport,
		FetchTrialUsers:            json.FetchTrialUsers,
		SubscriptionStatus:         json.SubscriptionStatus,
		Start:                      json.Start,
		Count:                      json.Count,
		Phone:                      json.Phone,
		Email:                      json.Email,
		FetchWithPreferredFacility: json.FetchWithPreferredFacility,
		//FreezesProvided:           json.FreezesProvided,
	}
	log.Println("FethcTrialUsers", json.FetchTrialUsers)
	timeTillContextDeadline := time.Now().Add(20 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := cl.SubscriptionGet(ctxbs, subscriptionData, shard.Strategy(strconv.Itoa(int(json.SubscriptionID))))
	if err != nil {
		fmt.Println(err)
		panic(err)
	}
	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func IsVoucherValidC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.IsVoucherValid

	json = c.MustGet("jsonData").(structs.IsVoucherValid)

	voucherRequestData := &productPB.ApplyVoucherRequest{
		Code:         json.Code,
		ProductId:    json.ProductId,
		VoucherId:    json.VoucherId,
		UsersCount:   json.UsersCount,
		CustomAmount: json.CustomAmount,
		UserId:       json.UserId,
		FacilityId:   json.FacilityId,
		SportId:      json.SportId,
		ReferralId:   json.ReferralId,
	}

	// session affinity based call
	response, err := cl.IsVoucherValid(context.TODO(), voucherRequestData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSubscriptionCV2(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to view Subscription details."))
		return
	}

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.Subscription

	json = c.MustGet("jsonData").(structs.Subscription)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var tabId int32
	if headers.TabId > 0 {
		tabId = int32(headers.TabId)
	} else {
		tabId = json.TabId
	}

	subscriptionData := &productPB.Subscription{
		SubscriptionId:           json.SubscriptionID,
		ProductId:                json.ProductID,
		UserId:                   loggedInUserId,
		PurchaseId:               json.PurchaseID,
		SessionCount:             json.SessionCount,
		IsActive:                 json.IsActive,
		FetchProduct:             json.FetchProduct,
		FsId:                     json.FsId,
		ForBookingSlots:          json.ForBookingSlots,
		FetchActiveBookingsCount: json.FetchActiveBookingsCount,
		TabId:                    tabId,
		AppType:                  headers.AppType,
		AppVersion:               headers.AppVersion,
		SubscriptionsForFreeze:   json.SubscriptionsForFreeze,
	}

	// session affinity based call
	response, err := cl.SubscriptionGetV2(ctx, subscriptionData, shard.Strategy(strconv.Itoa(int(json.SubscriptionID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateSubscriptionC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.EDIT_SUBSCRIPTION_PERMISSION}, []int32{common.SUBSCRIPTION_TOOL, common.RECIEPT_GENERATION_TOOL})
	if !authorized {
		log.Println("GetSubscriptionC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.CreateSubscription

	json = c.MustGet("jsonData").(structs.CreateSubscription)

	planData := &productPB.PlanData{
		Name:          json.PlanData.Name,
		Email:         json.PlanData.Email,
		MemberUserId:  json.PlanData.MemberUserId,
		ActualAmount:  json.PlanData.ActualAmount,
		PlanStartDate: json.PlanData.PlanStartDate,
		//add more here when decided
	}

	purData := &productPB.PurchaseData{
		MemberPurchaseId: json.PurchaseData.MemberPurchaseId,
		Amount:           json.PurchaseData.Amount,
	}

	subscriptionData := &productPB.CreateSubscriptionRequest{
		ProductId:    json.ProductID,
		UserId:       json.UserID,
		PurchaseId:   json.PurchaseID,
		PlanData:     planData,
		PurchaseData: purData,
	}

	// session affinity based call
	response, err := cl.CreateSubscription(context.TODO(), subscriptionData, shard.Strategy(strconv.Itoa(int(json.UserID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSportsForSubscriptionC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetSportsForSubscription

	json = c.MustGet("jsonData").(structs.GetSportsForSubscription)

	subscriptionData := &productPB.GetSportsForSubscriptionRequest{
		SubscriptionId: json.SubscriptionId,
	}

	// session affinity based call
	response, err := cl.GetSportsForSubscription(context.TODO(), subscriptionData, shard.Strategy(strconv.Itoa(int(json.SubscriptionId))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilitiesForSubscriptionC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetFacilitiesForSubscription

	json = c.MustGet("jsonData").(structs.GetFacilitiesForSubscription)

	subscriptionData := &productPB.GetFacilitiesForSubscriptionRequest{
		SubscriptionId: json.SubscriptionId,
	}

	// session affinity based call
	response, err := cl.GetFacilitiesForSubscription(context.TODO(), subscriptionData, shard.Strategy(strconv.Itoa(int(json.SubscriptionId))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateProductFSMappingC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.ADD_PRODUCT_PERMISSION, common.EDIT_PRODUCT_PERMISSION}, []int32{common.MANAGE_PRODUCT_TOOL, common.FACILITY_PRODUCT_MAP_TOOL})
	if !authorized {
		log.Println("CreateProductFSMappingC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.CreateProductFSMapping
	json = c.MustGet("jsonData").(structs.CreateProductFSMapping)

	fmt.Println("Test -- ", json)
	var fsIds []int32
	var fsDataPb []*productPB.FSData
	for _, ele := range json.FsData {

		//today's date by default
		var aDate int64
		aDate = time.Now().Unix()

		fsData := &productPB.FSData{
			FsId:             ele.FSID,
			ActivationDate:   aDate,
			DeactivationDate: ele.DeactivationDate,
		}
		fsDataPb = append(fsDataPb, fsData)
		fsIds = append(fsIds, ele.FSID)
	}

	pfsmData := &productPB.CreateProductFSMappingRequest{
		ProductIds: sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.ProductIds),
		FsIds:      fsIds,
		FsData:     fsDataPb,
	}

	// session affinity based call
	response, err := cl.ProductFSMappingCreate(context.TODO(), pfsmData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UpdateSubscriptionC(c *gin.Context) {
	productClient := util.GetProductClient()

	var json structs.UpdateSubscription
	json = c.MustGet("jsonData").(structs.UpdateSubscription)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.EDIT_SUBSCRIPTION_PERMISSION}, []int32{common.SUBSCRIPTION_TOOL, common.RECIEPT_GENERATION_TOOL})
	if !authorized {
		log.Println("UpdateSubscriptionC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	log.Println("Update Subsciption Request -- ", json)

	upSubPb := &productPB.UpdateSubscriptionRequest{
		UserId:              json.UserId,
		SubscriptionId:      json.SubscriptionId,
		PurchaseId:          json.PurchaseId,
		ProductId:           json.ProductId,
		StartDate:           sharedFunc.GetProtobufTimeFromIntTimestamp(json.StartDate),
		EndDate:             sharedFunc.GetProtobufTimeFromIntTimestamp(json.SubscriptionEndDate),
		SubscriptionEndDate: sharedFunc.GetProtobufTimeFromIntTimestamp(json.SubscriptionEndDate),
		SessionCount:        json.SessionCount,
		FreezesProvided:     json.FreezesProvided,
		IsActive:            json.IsActive,
		DeactivationDate:    sharedFunc.GetProtobufTimeFromIntTimestamp(json.DeactivationDate),
		UpdatedBy:           int32(headers.UserId),
		Reason:              json.Reason,
		PreferredFacilityId: json.PreferredFacilityId,
	}
	if json.PreferredSportId > 0 {
		upSubPb.PreferredSportId = json.PreferredSportId
	}
	// session affinity based call
	response, err := productClient.UpdateSubscription(context.TODO(), upSubPb)
	if err != nil {
		log.Println("func:UpdateSubscriptionC Error in updating subscription details in controller err:", err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetTrialDetailsForUserC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetTrialDetailsForUser
	json = c.MustGet("jsonData").(structs.GetTrialDetailsForUser)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32

	if headers.AppType == "sports-ios" || headers.AppType == "sports-android" {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	if json.CityId == 0 {
		json.CityId = 143
	}

	fmt.Println("GetTrialDetailsForUser Request -- ", json)

	upSubPb := &productPB.TrialDetailsRequest{
		UserId:     userId,
		CityId:     json.CityId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
		Token:      headers.AccessToken,
	}

	// session affinity based call
	response, err := cl.GetTrialDetailsForUser(context.TODO(), upSubPb)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	statusCode := http.StatusOK

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		statusCode = http.StatusUnauthorized
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
	}

	c.JSON(statusCode, response)
	return
}

func GetAboutFitsoDetailsC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.AboutFitso
	json = c.MustGet("jsonData").(structs.AboutFitso)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId > 0 {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	if json.CityId == 0 {
		json.CityId = 143
	}

	aboutFitsoReq := &productPB.AboutFitso{
		UserId:     userId,
		CityId:     json.CityId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	// session affinity based call
	response, err := cl.AboutFitsoDetailsGet(context.TODO(), aboutFitsoReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetHomeGroundDetailsC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.AboutFistoHomeGround
	json = c.MustGet("jsonData").(structs.AboutFistoHomeGround)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId != 0 {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	var cityId int32
	if headers.CityId > 0 {
		cityId = int32(headers.CityId)
	} else {
		if json.CityId == 0 {
			cityId = 143
		} else {
			cityId = json.CityId
		}
	}

	var societyId int32
	if headers.SocietyId > 0 {
		societyId = int32(headers.SocietyId)
	} else {
		societyId = json.SocietyId
	}

	aboutFitsoReq := &productPB.AboutFistoHomeGround{
		UserId:     userId,
		CityId:     cityId,
		SocietyId:  societyId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	// session affinity based call
	response, err := cl.HomeGroundDetailsGet(context.TODO(), aboutFitsoReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetHomeGroundOfferedSportDetailsC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.HomeGroundOfferedSports
	json = c.MustGet("jsonData").(structs.HomeGroundOfferedSports)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId != 0 {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	var cityId int32
	if headers.CityId > 0 {
		cityId = int32(headers.CityId)
	} else {
		if json.CityId == 0 {
			cityId = 143
		} else {
			cityId = json.CityId
		}
	}

	var societyId int32
	if headers.SocietyId > 0 {
		societyId = int32(headers.SocietyId)
	} else {
		societyId = json.SocietyId
	}

	homeOfferedSportReq := &productPB.HomeGroundSportDetailsRequest{
		UserId:     userId,
		CityId:     cityId,
		SportId:    json.SportId,
		SocietyId:  societyId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	// session affinity based call
	response, err := cl.HomeGroundOfferedSportDetailsGet(context.TODO(), homeOfferedSportReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func MarkFreezeC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)
	toolPermissions,_ := c.Get("tool_access")
	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.EDIT_SUBSCRIPTION_PERMISSION}, []int32{common.SUBSCRIPTION_TOOL, common.RECIEPT_GENERATION_TOOL})
	if !authorized {
		log.Println("MarkFreezeC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}
	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var json structs.MarkFreeze
	json = c.MustGet("jsonData").(structs.MarkFreeze)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId > 0 {
		userId = int32(headers.UserId)
	} else {
		userId = json.MarkerUserId
	}

	markFreezeReq := &productPB.MarkFreezeRequest{
		SubscriptionId: json.SubscriptionId,
		MarkerUserId:   userId,
		FreezeReasonId: json.FreezeReasonId,
		FreezeDates:    json.FreezeDates,
		AppType:        headers.AppType,
		AppVersion:     headers.AppVersion,
		FreezeNote:     json.FreezeNote,
		Token:          headers.AccessToken,
	}

	// session affinity based call
	response, err := cl.MarkFreezeForSubscription(context.TODO(), markFreezeReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	if response.Status != nil && response.Status.Message == common.NOT_AUTHORIZED {
		render.AbortWithStatusJSON(c, http.StatusUnauthorized, response.Status.Status, "Please logout and login again to freeze.")
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetPlanStatusForUserC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetPlanStatusForUser
	json = c.MustGet("jsonData").(structs.GetPlanStatusForUser)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId > 0 {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	planStatusReq := &productPB.GetPlanStatusForUserRequest{
		UserId: userId,
	}

	response, err := cl.GetPlanStatusForUser(context.TODO(), planStatusReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func NoShowPenaltyApplyC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	refRequest := &productPB.Empty{}
	response, err := cl.NoShowPenaltyApply(context.TODO(), refRequest)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetDetailsForMarkingFreezeC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)

	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Invalid Request"))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.PreMarkFreeze
	json = c.MustGet("jsonData").(structs.PreMarkFreeze)

	preMarkFreezeData := &productPB.PreMarkFreeze{
		SubscriptionId: json.SubscriptionId,
		UserId:         loggedInUser,
	}

	response, err := cl.GetDetailsForMarkingFreeze(context.TODO(), preMarkFreezeData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateOrUpdateVouchersC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW__TOOL,common.SHOW_TOOL}, []int32{common.ADD_EDIT_COUPON_TOOL})
	if !authorized {
		log.Println("CreateOrUpdateVouchersC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.Vouchers
	json = c.MustGet("jsonData").(structs.Vouchers)

	fmt.Println("Test -- ", json)
	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	var userId int32
	if headers.UserId > 0 {
		userId = int32(headers.UserId)
	} else {
		userId = int32(json.UserId)
	}

	upSubPb := &productPB.Vouchers{
		VoucherId:       json.VoucherId,
		Code:            json.Code,
		Description:     json.Descripton,
		ApplicantType:   json.ApplicantType,
		CouponType:      json.CouponType,
		RewardType:      json.RewardType,
		RewardValue:     json.RewardValue,
		DiscountType:    json.DiscountType,
		DiscountValue:   json.DiscountValue,
		MaxDiscount:     json.MaxDiscount,
		MinPlanPrice:    json.MinPlanPrice,
		MaxPlanPrice:    json.MaxPlanPrice,
		DurationInDays:  json.DurationInDays,
		UsageCount:      json.UsageCount,
		MinUsers:        json.MinUsers,
		CouponTypeValue: json.CouponTypeValues,
		UserId:          userId,
		DeletedFlag:     json.DeletedFlag,
		IsPromoted:      json.IsPromoted,
	}

	if json.ExpiryDate > 0 {
		upSubPb.ExpiryDate = sharedFunc.GetProtobufTimeFromIntTimestamp(json.ExpiryDate)
	}

	response, err := cl.CreateOrUpdateVouchers(context.TODO(), upSubPb)

	if err != nil {
		status := &productPB.Status{
			Status:  "failure",
			Message: fmt.Sprintf("%v", err),
		}
		response = &productPB.CreateUpdateVoucherRes{
			Status: status,
		}
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetVouchersC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())
	var json structs.GetVouchers
	json = c.MustGet("jsonData").(structs.GetVouchers)

	getVouchersReq := &productPB.GetVouchersReq{
		VouchersStatus: json.VouchersStatus,
		ApplicantType:  json.ApplicantType,
		CouponType:     json.CouponType,
	}

	// session affinity based call
	response, err := cl.GetVouchers(context.TODO(), getVouchersReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GenerateOrGetReferralCodeC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW__TOOL,common.SHOW_TOOL}, []int32{common.REFERRAL_REWARDS_TOOL})
	if !authorized {
		log.Println("GenerateOrGetReferralCodeC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.ReferralCodeGenerateOrGet
	json = c.MustGet("jsonData").(structs.ReferralCodeGenerateOrGet)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId > 0 {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	refCodeData := &productPB.ReferralCodeGenerateOrGet{
		UserId:     userId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	response, err := cl.GenerateOrGetReferralCode(context.TODO(), refCodeData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFreezeLogsForSubscriptionC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.FreezeLogGet
	json = c.MustGet("jsonData").(structs.FreezeLogGet)

	// var headers structs.Headers
	// headers = c.MustGet("requestHeaders").(structs.Headers)

	// var userId int32
	// if headers.UserId > 0 {
	// 	userId = int32(headers.UserId)
	// } else {
	// 	uid, _ := strconv.Atoi(json.UserId)
	// 	userId = int32(uid)
	// }

	logData := &productPB.SportsUserFreezeLogRequest{
		SubscriptionId:     json.SubscriptionId,
		UniqueFreezeString: json.UniqueFreezeString,
		FetchUpcoming:      json.FetchUpcoming,
	}

	response, err := cl.GetFreezeLogsForSubscription(context.TODO(), logData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFreezeReasonsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.FreezeReasonGet
	json = c.MustGet("jsonData").(structs.FreezeReasonGet)

	// var headers structs.Headers
	// headers = c.MustGet("requestHeaders").(structs.Headers)

	// var userId int32
	// if headers.UserId > 0 {
	// 	userId = int32(headers.UserId)
	// } else {
	// 	uid, _ := strconv.Atoi(json.UserId)
	// 	userId = int32(uid)
	// }

	reasonGetData := &productPB.SportsFreezeReason{
		FreezeReasonId: json.FreezeReasonId,
	}

	response, err := cl.FreezeReasonsGet(context.TODO(), reasonGetData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetPlanUtilizationC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.PlanUtilizationRequest
	json = c.MustGet("jsonData").(structs.PlanUtilizationRequest)

	reasonGetData := &productPB.PlanUtilizationRequest{
		StartDate:  json.StartDate,
		EndDate:    json.EndDate,
		FsIds:      json.FsIds,
		ProductIds: json.ProductIds,
		IsTrial:    json.IsTrial,
	}

	response, err := cl.PlanUtilizationGet(context.TODO(), reasonGetData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSportsOffersC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.SportsOffersGet
	json = c.MustGet("jsonData").(structs.SportsOffersGet)

	offerGetData := &productPB.GetSportsOffersRequest{
		OfferIds:        sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.OfferIds),
		OfferCampaignId: json.OfferCampaignId,
		ProductIds:      sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.ProductIds),
		IncludeUpcoming: json.IncludeUpcoming,
		IncludeExpired:  json.IncludeExpired,
		CityIds:         sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.CityIds),
		SportIds:        sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.SportIds),
		DurationInDays:  sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.DurationInDays),
		FacilityNameUrl: json.FacilityNameUrl,
	}

	response, err := cl.SportsOffersGet(context.TODO(), offerGetData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UnfreezeSubscriptionC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var json structs.Unfreeze
	json = c.MustGet("jsonData").(structs.Unfreeze)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if headers.UserId > 0 {
		userId = int32(headers.UserId)
	} else {
		userId = json.MarkerUserId
	}

	unfreezeReq := &productPB.UnfreezeRequest{
		SubscriptionId:     json.SubscriptionId,
		MarkerUserId:       userId,
		UniqueFreezeString: json.UniqueFreezeString,
	}

	// session affinity based call
	response, err := cl.UnfreezeSubscription(context.TODO(), unfreezeReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetRenewalCharactersticsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetRenewalRequest
	json = c.MustGet("jsonData").(structs.GetRenewalRequest)

	reasonGetData := &productPB.GetRenewalRequest{
		StartDate:    json.StartDate,
		EndDate:      json.EndDate,
		ProductIds:   json.ProductIds,
		IsTrial:      json.IsTrial,
		AnalysisType: json.AnalysisType,
	}

	timeTillContextDeadline := time.Now().Add(10 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	response, err := cl.RenewalCharactersticsGet(ctxbs, reasonGetData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSportsOfferCampaignsC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.SportsOfferCampaignsGet
	json = c.MustGet("jsonData").(structs.SportsOfferCampaignsGet)

	offerCampaignGetData := &productPB.GetSportsOfferCampaignsRequest{
		OfferCampaignId: json.OfferCampaignId,
		IncludeUpcoming: json.IncludeUpcoming,
		IncludeExpired:  json.IncludeExpired,
	}

	response, err := cl.SportsOfferCampaignsGet(context.TODO(), offerCampaignGetData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateUpdateSportsOfferC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW__TOOL,common.SHOW_TOOL}, []int32{common.SPORT_OFFERS_TOOL})
	if !authorized {
		log.Println("CreateUpdateSportsOfferC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.SportsOffer

	json = c.MustGet("jsonData").(structs.SportsOffer)

	offerData := &productPB.Offer{
		OfferId:          json.OfferId,
		OfferCampaignId:  json.OfferCampaignId,
		OfferName:        json.OfferName,
		ProductId:        json.ProductId,
		OfferedPrice:     json.OfferedPrice,
		CouponApplicable: json.CouponApplicable,
		CouponCode:       json.CouponCode,
		CouponInfoText:   json.CouponInfoText,
		ImageDesktopUrl:  json.ImageDesktopUrl,
		ImageMobileUrl:   json.ImageMobileUrl,
		StartTime:        sharedFunc.GetProtobufTimeFromIntTimestamp(json.StartTime),
		EndTime:          sharedFunc.GetProtobufTimeFromIntTimestamp(json.EndTime),
		Priority:         json.Priority,
		IsVisible:        json.IsVisible,
		DeletedFlag:      json.DeletedFlag,
		CreatedBy:        json.CreatedBy,
		SectionTitle:     json.SectionTitle,
		SpecialFeature:   json.SpecialFeature,
		GroupOffer:       json.GroupOffer,
		MaxUsersPerGroup: json.MaxUsersPerGroup,
	}

	// session affinity based call
	response, err := cl.CreateUpdateSportsOffer(context.TODO(), offerData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateUpdateSportsOfferCampaignC(c *gin.Context) {

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.SHOW__TOOL,common.SHOW_TOOL}, []int32{common.CAMPAIGN_OFFERS_TOOL})
	if !authorized {
		log.Println("CreateUpdateSportsOfferCampaignC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.OfferCampaign

	json = c.MustGet("jsonData").(structs.OfferCampaign)

	offerCampaignData := &productPB.OfferCampaign{
		OfferCampaignId:     json.OfferCampaignId,
		CampaignName:        json.CampaignName,
		CampaignTitle:       json.CampaignTitle,
		CampaignDescription: json.CampaignDescription,
		CampaignType:        json.CampaignType,
		BannerDesktopUrl:    json.BannerImageDesktopUrl,
		BannerMobileUrl:     json.BannerImageMobileUrl,
		StartTime:           sharedFunc.GetProtobufTimeFromIntTimestamp(json.StartTime),
		EndTime:             sharedFunc.GetProtobufTimeFromIntTimestamp(json.EndTime),
		TimerEndDate:        sharedFunc.GetProtobufTimeFromIntTimestamp(json.TimerEndDate),
		DeletedFlag:         json.DeletedFlag,
		CreatedBy:           json.CreatedBy,
	}

	// session affinity based call
	response, err := cl.CreateUpdateSportsOfferCampaign(context.TODO(), offerCampaignData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func PostFreezeOrUnfreezeDetailsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.PostFreezeOrUnfreeze
	json = c.MustGet("jsonData").(structs.PostFreezeOrUnfreeze)

	fData := &productPB.PostFreezeOrUnfreeze{
		SubscriptionId:     json.SubscriptionId,
		FreezeDates:        json.FreezeDates,
		UniqueFreezeString: json.UniqueFreezeString,
		ForFreeze:          json.ForFreeze,
	}

	response, err := cl.PostFreezeOrUnfreezeDetails(context.TODO(), fData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilitiesForProductC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetFacilitiesForProduct

	json = c.MustGet("jsonData").(structs.GetFacilitiesForProduct)

	productData := &productPB.Product{
		ProductId: json.ProductId,
	}

	// session affinity based call
	response, err := cl.GetFacilitiesForProduct(context.TODO(), productData, shard.Strategy(strconv.Itoa(int(json.ProductId))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetProductFacilitySportC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.ProductFacilitySport

	json = c.MustGet("jsonData").(structs.ProductFacilitySport)

	productData := &productPB.Product{
		ProductId: json.PID,
	}

	// session affinity based call
	response, err := cl.GetProductFacilitySport(context.TODO(), productData, shard.Strategy(strconv.Itoa(int(json.PID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UpdateProductFacilitySportC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)
	toolPermissions,_ := c.Get("tool_access")

	authorized := checkDashboardUserAuthorised(toolPermissions, []string{common.ADD_PRODUCT_PERMISSION, common.EDIT_PRODUCT_PERMISSION, common.SHOW_TOOL}, []int32{common.MANAGE_PRODUCT_TOOL, common.FACILITY_PRODUCT_MAP_TOOL})
	if !authorized {
		log.Println("CreateProductFSMappingC: User not authorised ", toolPermissions)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.ProductFacilitySport

	json = c.MustGet("jsonData").(structs.ProductFacilitySport)

	reqData := &productPB.ProductFacilitySport{
		PfsId:     json.PFSID,
		IsVisible: json.IsVisible,
		IsActive:  json.IsActive,
	}

	// session affinity based call
	response, err := cl.UpdateProductFacilitySport(context.TODO(), reqData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetTimelineForSubscriptionC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())
	var json structs.GetTimelineForSubscription
	json = c.MustGet("jsonData").(structs.GetTimelineForSubscription)

	logData := &productPB.SubscriptionTimelineRequest{
		SubscriptionId: json.SubscriptionId,
	}

	response, err := cl.GetTimelineForSubscription(context.TODO(), logData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func OptInSubscriptionC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.OptInSubscription
	json = c.MustGet("jsonData").(structs.OptInSubscription)

	var userId int32
	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	if headers.AppType == "sports-ios" || headers.AppType == "sports-android" {
		userId = int32(headers.UserId)
	} else {
		uid, _ := strconv.Atoi(json.UserId)
		userId = int32(uid)
	}

	reqData := &productPB.OptInSubscriptionRequest{
		SubscriptionId: json.SubscriptionId,
		UserId:         userId,
	}

	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	response, err := cl.OptInSubscription(ctxbs, reqData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetProductCategoriesC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetProductCategories

	json = c.MustGet("jsonData").(structs.GetProductCategories)

	productData := &productPB.ProductCategory{
		ProductCategoryId: json.ProductCategoryId,
	}

	// session affinity based call
	response, err := cl.GetProductCategories(context.TODO(), productData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func InsertHomeGroundLeadC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.HomeGroundLeadData

	json = c.MustGet("jsonData").(structs.HomeGroundLeadData)

	var sportData []*productPB.Sport
	for _, ele := range json.Sport {
		sportObj := &productPB.Sport{
			SportId:   ele.SportID,
			SportName: ele.SportName,
		}
		sportData = append(sportData, sportObj)
	}

	leadData := &productPB.HomeGroundLeadDataRequest{
		UserId:      json.UserID,
		Phone:       json.Phone,
		Name:        json.Name,
		Email:       json.Email,
		Address:     json.Address,
		Sport:       sportData,
		Age:         json.Age,
		HiringFor:   json.HiringFor,
		ProgramType: json.ProgramType,
	}

	// session affinity based call
	response, err := cl.InsertHomeGroundLead(context.TODO(), leadData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}
func SealsUpgradeC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService(
	// uncomment if want services to be served in roundrobin fashion
	// micro.WrapClient(roundrobin.NewClientWrapper()),
	)

	cl = productPB.NewProductService(serviceList.Product, service.Client())
	// cl = authPB.NewAuthService("product.service.auth", client.DefaultClient)

	var json structs.SealsUpgrade

	json = c.MustGet("jsonData").(structs.SealsUpgrade)

	sealsUpgradeRequest := &productPB.SealsUpgradeRequest{

		SwimmingUserPlanId: json.SwimmingUserPlanId,
		UserId:             json.UserId,
	}
	timeTillContextDeadline := time.Now().Add(20 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()
	// session affinity based call
	response, err := cl.SealsUpgrade(ctxbs, sealsUpgradeRequest)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetFacilitiesForSubscriptionV2C(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.GetFacilitiesForSubscription

	json = c.MustGet("jsonData").(structs.GetFacilitiesForSubscription)

	reqData := &productPB.GetFacilitiesForSubscriptionV2Request{
		SubscriptionId: json.SubscriptionId,
		CityId:         json.CityId,
	}
	fmt.Println(shard.Strategy(strconv.Itoa(int(json.SubscriptionId))))
	// session affinity based call
	response, err := cl.GetFacilitiesForSubscriptionV2(context.TODO(), reqData, shard.Strategy(strconv.Itoa(int(json.SubscriptionId))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetDurationsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	// session affinity based call
	response, err := cl.GetDurations(context.TODO(), &productPB.Empty{})

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetReferralParamsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	response, err := cl.GetReferralParams(context.TODO(), &productPB.Empty{})
	if err != nil {
		log.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UploadImageToS3BucketC(c *gin.Context) {
	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = productPB.NewProductService(serviceList.Product, service.Client())

	var json structs.ImageUpload

	json = c.MustGet("jsonData").(structs.ImageUpload)

	header := json.UploadImage
	file, err := header.Open()
	defer file.Close()
	if err != nil {
		log.Println(err)
	}

	bytes, err := ioutil.ReadAll(file)
	if err != nil {
		log.Println(err)
	}

	reqData := &productPB.UploadImageToS3BucketRequest{
		UploadImage: bytes,
		FileName:    header.Filename,
		IsDbEntry:   json.IsDbEntry,
		ImageId:     json.ImageId,
	}

	mimeHeader := header.Header
	types, ok := mimeHeader["Content-Type"]
	if ok {
		for _, x := range types {
			reqData.ContentType = x
		}
	}

	response, err := cl.UploadImageToS3Bucket(context.TODO(), reqData)
	if err != nil {
		log.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func PushLeadToLeadSquaredCaptureC(c *gin.Context) {
	productClient := util.GetProductClient()
	var json structs.LeadSquaredCaptureLead

	json = c.MustGet("jsonData").(structs.LeadSquaredCaptureLead)

	reqData := &productPB.LeadSquaredLead{
		FirstName:      json.FirstName,
		Mobile:         json.Mobile,
		CityName:       json.CityName,
		PoolLocation:   json.PoolLocation,
		LeadSource:     json.LeadSource,
		LeadSubSource:  json.LeadSubSource,
		SourceCampaign: json.SourceCampaign,
		Remark:         json.Remark,
	}

	if len(json.AppSignUpDetails.SubzoneName) > 0 {
		reqData.AppSignUpDetails = &productPB.AppSignUpDetails{
			SubzoneName:      json.AppSignUpDetails.SubzoneName,
			ClosestFacility:  json.AppSignUpDetails.ClosestFacility,
			FacilityDistance: json.AppSignUpDetails.FacilityDistance,
		}
	}

	response, err := productClient.PublishLeadSquaredCaptureMessage(c, reqData)
	if err != nil {
		log.Println("error in publishing lead details: ", err)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			model.StatusFailure(err.Error()),
		)
		return
	}

	c.JSON(
		http.StatusOK,
		response,
	)
}

func GetActiveLocationsC(c *gin.Context) {
	productClient := util.GetProductClient()
	response, err := productClient.GetActiveLocations(c, &productPB.Empty{})

	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func TestClevertapC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	productClient := util.GetProductClient()
	timeTillContextDeadline := time.Now().Add(10 * 60 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(ctx, timeTillContextDeadline)
	defer ctxCancelFuncfs()

	productClient.UpdateClevertapUserProfiles(ctxbs, &productPB.Empty{})
}
