package productController

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/jogocoin/go_api/api/models/sushi"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"

	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type CalculateCartData struct {
	productCategoryId int32
	data              *productPB.CalculateCartResponse
	promoCode         string
	paymentsData      *productModel.PaymentsData
	headerData        *productModel.HeaderData
	topContainer      *productModel.TopContainer
	membershipData    *productModel.MembershipData
	offersData        *productModel.OffersData
	billInfoData      *productModel.BillInfoData
	cartButtonData    *productModel.CartButtonData
	ClevertapTracking []*sushi.ClevertapItem
	JumboTracking     []*jumbo.Item
	PaymentProvider   int32
	Header            *productModel.CalculateCartHeaderSection
	Results           []*productModel.ProductResult
	Footer            *productModel.CalculateCartFooterSection
	ActionList        []*productModel.ActionListItem
}

type MembershipDataItem struct {
	ProductTitle    string
	ProductSubtitle string
	ProductValue    string
	InfoTitle       string
	InfoRightTitle  string
	CouponApplied   int32
}

func (cc *CalculateCartData) setCalculateCartData(data *productPB.CalculateCartResponse) {
	cc.data = data
	cc.promoCode = data.AppliedPromoCode
}

func (cc *CalculateCartData) SetPaymentProvider(ctx context.Context, paymentProvider int32) {
	cc.PaymentProvider = paymentProvider
}

func (cc *CalculateCartData) SetPaymentsData(countryId int32, serviceType string, totalCartValue float32) {
	paymentsData := &productModel.PaymentsData{
		CountryID:          countryId,
		ServiceType:        serviceType,
		Amount:             totalCartValue,
		OnlinePaymentsFlag: int32(1),
	}

	cc.paymentsData = paymentsData
}

func (cc *CalculateCartData) SetCartPageHeader(ctx context.Context, cartUsers []*structs.CommonCartUser) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)

	title, _ := sushi.NewTextSnippet("BUYING FOR")
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	titleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	title.SetColor(titleColor)
	title.SetFont(titleFont)
	title.SetKerning(3)

	subTitleText := ""
	for index, user := range cartUsers {
		subTitleText += user.Name
		if index < len(cartUsers)-1 {
			subTitleText += ", "
		}
	}

	subTitle, _ := sushi.NewTextSnippet(subTitleText)
	subTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	subTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	subTitle.SetColor(subTitleColor)
	subTitle.SetFont(subTitleFont)

	cc.headerData = &productModel.HeaderData{
		Color:    bgColor,
		Title:    title,
		Subtitle: subTitle,
	}

	if cc.data.EnableChangeCta {
		button, _ := sushi.NewButton(sushi.ButtontypeText)
		button.SetText("Add member")
		buttonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		button.SetColor(buttonColor)
		button.SetFont(buttonFont)

		buttonClickAction := sushi.GetClickAction()
		buttonClickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetBuyingForOthersDeeplink(cc.data.SubscriptionProductId, false),
		})

		button.SetClickAction(buttonClickAction)
		cc.headerData.Button = button
	}
}

func (cc *CalculateCartData) setTopContainer() {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	title, _ := sushi.NewTextSnippet("Buy memberships for friends and family, and book sessions together")
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Add members")
	buttonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	button.SetColor(buttonColor)
	button.SetFont(buttonFont)

	disclaimerData := &productModel.TopContainerDisclaimer{
		BgColor:     bgColor,
		BorderColor: borderColor,
		Title:       title,
		Button:      button,
	}

	cc.topContainer = &productModel.TopContainer{
		Disclaimer: disclaimerData,
	}
}

func (cc *CalculateCartData) SetMembershipData(ctx context.Context, data *productPB.CalculateCartResponse) {
	items := make([]*productModel.MembershipDataItem, 0)
	if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		for _, product := range data.ProductDetails {
			membershipDataItem := &MembershipDataItem{}

			productTitle := fmt.Sprintf("%d x %s", product.Frequency, product.Name)
			membershipDataItem.ProductTitle = productTitle

			namesArr := make([]string, 0)
			for _, user := range product.CartUsers {
				namesArr = append(namesArr, user.Name)
			}
			names := strings.Join(namesArr, ", ")

			productSubtitle := fmt.Sprintf("%s | %s, for %s", names, product.Duration, product.LocationName)
			membershipDataItem.ProductSubtitle = productSubtitle

			productValue := fmt.Sprintf("₹%.0f", product.TotalAmountPerProduct)
			productMRP := product.Price * float32(product.Frequency)

			if product.TotalAmountPerProduct < productMRP {
				productValue = fmt.Sprintf("{grey-500|<medium-400|~~₹%.0f~~>} ₹%v", productMRP, product.TotalAmountPerProduct)
			}
			membershipDataItem.ProductValue = productValue

			if product.VoucherResponse != nil && product.VoucherResponse.Status != nil && product.VoucherResponse.Status.Message != "" {
				infoTitle := product.VoucherResponse.Status.Message
				membershipDataItem.InfoTitle = infoTitle
				if product.VoucherResponse.DiscountAmount > 0 {
					membershipDataItem.CouponApplied = 1

					infoRightTitle := fmt.Sprintf("-₹%v", product.VoucherResponse.DiscountAmount)
					membershipDataItem.InfoRightTitle = infoRightTitle
				}
			}

			if len(product.ErrorText) > 0 {
				productSubtitle = fmt.Sprintf("%s\n{red-500|%s}", productSubtitle, product.ErrorText)
			}
			items = append(items, getMembershipDataItem(membershipDataItem))
		}

		cc.membershipData = &productModel.MembershipData{
			Items: items,
		}
	} else {
		membershipDataItem := &MembershipDataItem{}

		membershipDataItem.ProductTitle = data.ProductTitle
		membershipDataItem.ProductSubtitle = data.ProductSubtitle
		membershipDataItem.ProductValue = data.FormattedTotalProductValue
		items = append(items, getMembershipDataItem(membershipDataItem))
	}
	cc.membershipData = &productModel.MembershipData{
		Items: items,
	}
}

func getMembershipDataItem(membershipData *MembershipDataItem) *productModel.MembershipDataItem {
	membershipDataItem := &productModel.MembershipDataItem{}

	title, _ := sushi.NewTextSnippet(membershipData.ProductTitle)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetColor(titleColor)
	title.SetFont(titleFont)
	membershipDataItem.Title = title

	subTitle, _ := sushi.NewTextSnippet(membershipData.ProductSubtitle)
	subTitle.SetIsMarkdown(int32(1))
	subTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	subTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	subTitle.SetColor(subTitleColor)
	subTitle.SetFont(subTitleFont)
	membershipDataItem.Subtitle = subTitle

	rightTitle, _ := sushi.NewTextSnippet(membershipData.ProductValue)
	rightTitle.IsMarkdown = 1
	rightTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	rightTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	rightTitle.SetColor(rightTitleColor)
	rightTitle.SetFont(rightTitleFont)
	membershipDataItem.RightTitle = rightTitle

	if len(membershipData.InfoTitle) > 0 {
		infoTitle, _ := sushi.NewTextSnippet(membershipData.InfoTitle)
		infoTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		if membershipData.CouponApplied == 0 {
			infoTitleColor, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		}
		infoTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		infoTitle.SetColor(infoTitleColor)
		infoTitle.SetFont(infoTitleFont)
		membershipDataItem.InfoTitle = infoTitle
	}
	if len(membershipData.InfoRightTitle) > 0 {
		infoRightTitle, _ := sushi.NewTextSnippet(membershipData.InfoRightTitle)
		infoRightTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		infoRightTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		infoRightTitle.SetColor(infoRightTitleColor)
		infoRightTitle.SetFont(infoRightTitleFont)
		membershipDataItem.InfoRightTitle = infoRightTitle
	}

	return membershipDataItem
}

func (cc *CalculateCartData) SetOffersData(ctx context.Context, formattedDiscount string, discountValue int32, voucherValid bool, voucherError string) {
	loggedInUser := util.GetUserIDFromContext(ctx)
	button, _ := sushi.NewButton(sushi.ButtontypeText)
	if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		button, _ = sushi.NewButton(sushi.ButtonTypeUnderlined)
		buttonUnderlined := &sushi.ButtonUnderline{
			Type: sushi.ButtonUnderlineTypeDashed,
		}
		button.SetUnderline(buttonUnderlined)
	}

	button.SetText("Apply promo code")

	buttonColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	button.SetColor(buttonColor)

	tapPayload := cc.GetCommonPayloadForTracking(ctx)
	eName := "apply_promo_code_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eName = "academy_apply_promo_code_tap"
	}
	tapEname := &sushi.EnameData{
		Ename: eName,
	}
	promoCodeEvent := sushi.NewClevertapEvents()
	promoCodeEvent.SetTap(tapEname)
	applyPromoTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, promoCodeEvent)
	button.AddClevertapTrackingItem(applyPromoTrackItem)

	leftIconColor, _ := sushi.NewColor(sushi.ColorTypePurple, sushi.ColorTint500)
	leftIcon, _ := sushi.NewIcon(sushi.PromoIcon, leftIconColor)

	clickAction := sushi.GetClickAction()

	bottomSheetHeaderTitle, _ := sushi.NewTextSnippet("Promo code")

	bottomSheetHeader := &sushi.OpenInputBottomSheetItemHeader{
		Title: bottomSheetHeaderTitle,
	}

	placeholder, _ := sushi.NewTextSnippet("Enter promo code")
	textField := &sushi.TextField{
		Placeholder: placeholder,
	}

	bottomButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	bottomButton.SetSize(sushi.ButtonSizeLarge)
	bottomButton.SetText("Apply")

	tapPayloadV2 := cc.GetCommonPayloadForTracking(ctx)
	tapPayloadV2["promo_code"] = cc.promoCode
	eName = "promo_code_pop_up_apply_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eName = "academy_promo_code_pop_up_apply_tap"
	}
	tapEnameV2 := &sushi.EnameData{
		Ename: eName,
	}
	applyCodeEvent := sushi.NewClevertapEvents()
	applyCodeEvent.SetTap(tapEnameV2)
	promoApplyTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayloadV2, applyCodeEvent)
	bottomButton.AddClevertapTrackingItem(promoApplyTrackItem)

	bottomSheetItem := &sushi.OpenInputBottomSheetItem{
		OpenInputBottomSheetItemHeader: bottomSheetHeader,
		Textfield:                      textField,
		Button:                         bottomButton,
	}
	clickAction.SetOpenInputBottomSheet(bottomSheetItem)
	button.SetClickAction(clickAction)

	offerItem := &productModel.OffersDataItem{
		Button:   button,
		LeftIcon: leftIcon,
	}

	if len(cc.promoCode) > 0 {
		if voucherValid {
			leftIconColor, _ := sushi.NewColor(sushi.ColorTypePurple, sushi.ColorTint500)
			leftIcon, _ := sushi.NewIcon(sushi.PromoIcon, leftIconColor)

			subtitle1, _ := sushi.NewTextSnippet(fmt.Sprintf("Code %s applied!", cc.promoCode))
			subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			subtitle1Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
			subtitle1.SetFont(subtitle1Font)
			subtitle1.SetColor(subtitle1Color)

			var subtitle2Text string
			textExtension := "\nExtension will be applied post purchase"
			if cc.data.DiscountAmount > 0 {
				subtitle2Text = fmt.Sprintf("You saved %s", formattedDiscount)
				if !isProductCategoryAcademy(cc.productCategoryId) && (time.Now().Unix() > common.SaleStartDateUnix || util.Contains(common.TestUsersAutoPromoCode, loggedInUser)) && time.Now().Unix() < common.SaleEndDateUnix {
					subtitle2Text += textExtension
				}
			} else {
				if len(cc.data.CustomSuccessPromoMessage) > 0 {
					subtitle2Text = cc.data.CustomSuccessPromoMessage
				} else {
					subtitle2Text = "Promo code applied successfully!"
				}
			}
			subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle2.SetFont(subtitle2Font)
			subtitle2.SetColor(subtitle2Color)

			button, _ := sushi.NewButton(sushi.ButtontypeText)
			button.SetText("Try another code")
			buttonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			button.SetColor(buttonColor)
			button.SetFont(buttonFont)

			clickAction := sushi.GetClickAction()
			clickAction.SetOpenInputBottomSheet(bottomSheetItem)
			button.SetClickAction(clickAction)

			offerItem = &productModel.OffersDataItem{
				RightButton: button,
				LeftIcon:    leftIcon,
				Title:       subtitle1,
				Subtitle:    subtitle2,
			}

			if discountValue > 0 {
				rightSubtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Success"))
				rightSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				rightSubtitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
				rightSubtitle.SetFont(rightSubtitleFont)
				rightSubtitle.SetColor(rightSubtitleColor)

				offerItem.RightTitle = rightSubtitle
			}
		} else {
			leftIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
			leftIcon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, leftIconColor)

			subtitle1, _ := sushi.NewTextSnippet("Promo Code invalid")
			subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			subtitle1.SetFont(subtitle1Font)
			subtitle1.SetColor(subtitle1Color)
			var errorMessage string
			if len(voucherError) > 0 {
				errorMessage = voucherError
			} else {
				errorMessage = "This code does not exist"
			}

			subtitle2, _ := sushi.NewTextSnippet(errorMessage)
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle2.SetFont(subtitle2Font)
			subtitle2.SetColor(subtitle2Color)

			button, _ := sushi.NewButton(sushi.ButtontypeText)
			button.SetText("Try another code")
			buttonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			button.SetColor(buttonColor)
			button.SetFont(buttonFont)

			clickAction := sushi.GetClickAction()
			clickAction.SetOpenInputBottomSheet(bottomSheetItem)
			button.SetClickAction(clickAction)

			offerItem = &productModel.OffersDataItem{
				RightButton: button,
				LeftIcon:    leftIcon,
				Title:       subtitle1,
				Subtitle:    subtitle2,
			}
		}
	}

	cc.offersData = &productModel.OffersData{
		Items: []*productModel.OffersDataItem{offerItem},
	}
}

func (cc *CalculateCartData) SetBillInfoData(formattedTotalCartValue string, totalCartValue float32, voucherValid bool, discountAmount int32) {
	title, _ := sushi.NewTextSnippet("Total cost")
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	subTitle, _ := sushi.NewTextSnippet(formattedTotalCartValue)
	subTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	subTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	subTitle.SetColor(subTitleColor)
	subTitle.SetFont(subTitleFont)

	billInfoDataItem := &productModel.BillInfoDataItem{
		Title:      title,
		RightTitle: subTitle,
	}

	cc.billInfoData = &productModel.BillInfoData{
		TotalCost: totalCartValue,
		Discount:  int32(0),
		Items:     []*productModel.BillInfoDataItem{billInfoDataItem},
	}

	if voucherValid {
		cc.billInfoData.Discount = discountAmount
	}
}

func (cc *CalculateCartData) getTrackingItemsForCartButtons(ctx context.Context, impressionEventName string, tapEventName string) []*sushi.ClevertapItem {
	commonPayload := cc.GetCommonPayloadForTracking(ctx)
	trackingItems := make([]*sushi.ClevertapItem, 0)
	if impressionEventName != "" {

		impressionEname := &sushi.EnameData{
			Ename: impressionEventName,
		}

		impressionEvent := sushi.NewClevertapEvents()
		impressionEvent.SetImpression(impressionEname)
		impressionItem := sushi.GetClevertapTrackItem(ctx, commonPayload, impressionEvent)
		trackingItems = append(trackingItems, impressionItem)
	}

	if tapEventName != "" {

		tapEname := &sushi.EnameData{
			Ename: tapEventName,
		}

		tapEvent := sushi.NewClevertapEvents()
		tapEvent.SetTap(tapEname)
		tapItem := sushi.GetClevertapTrackItem(ctx, commonPayload, tapEvent)
		trackingItems = append(trackingItems, tapItem)
	}

	return trackingItems

}

func (cc *CalculateCartData) SetCartButtonData(ctx context.Context, formattedTotalCartValue string) {
	buttonTitle, _ := sushi.NewTextSnippet(formattedTotalCartValue)

	buttonSubtitle1, _ := sushi.NewTextSnippet("TOTAL")

	buttonSubtitle2, _ := sushi.NewTextSnippet("Buy now")
	cartButton := &productModel.CartButton{
		Title:      buttonTitle,
		SubTitle:   buttonSubtitle1,
		SubTitle2:  buttonSubtitle2,
		IsDisabled: false,
	}

	eventName := "cart_buy_now_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eventName = "academy_cart_buy_now_tap"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eventName = "summercamp_cart_buy_now_tap"
	}
	trackingItems := cc.getTrackingItemsForCartButtons(ctx, "", eventName)
	cartButton.ClevertapTracking = trackingItems

	if cc.data.CartError {
		cartButton.IsDisabled = true
	}

	title, _ := sushi.NewTextSnippet(fmt.Sprintf("Paying %s", formattedTotalCartValue))

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("CANCEL")
	button.SetSize(sushi.ButtonSizeMedium)
	eventName = "cart_cancel_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eventName = "academy_cart_cancel_tap"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eventName = "summercamp_cart_cancel_tap"
	}
	button.ClevertapTracking = cc.getTrackingItemsForCartButtons(ctx, "", eventName)

	confirmationData := &productModel.ConfirmationData{
		Title:  title,
		Button: button,
		Time:   "3",
	}

	cc.cartButtonData = &productModel.CartButtonData{
		Button:           cartButton,
		ConfirmationData: confirmationData,
	}
}

func (cc *CalculateCartData) SetClevertapTracking(ctx context.Context) {
	commonPayload := cc.GetCommonPayloadForTracking(ctx)

	eName := "cart_card_impression"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eName = "academy_cart_card_impression"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eName = "summercamp_cart_card_impression"
	}
	landingEName := &sushi.EnameData{
		Ename: eName,
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	eName = "card_card_close_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eName = "academy_card_card_close_tap"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eName = "summercamp_card_card_close_tap"
	}
	dismissEName := &sushi.EnameData{
		Ename: eName,
	}
	pageDismissEvent := sushi.NewClevertapEvents()
	pageDismissEvent.SetPageDismiss(dismissEName)
	pageDismissTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageDismissEvent)

	cc.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem, pageDismissTrackItem}
}

func (cc *CalculateCartData) SetJumboTracking(ctx context.Context) {
	jumboPayload := make(map[string]interface{})
	jumboPayload["source_page"] = "cart_card"

	events := jumbo.NewEvents()
	cartCardImpEvent := jumbo.GetEventNameObject(jumbo.CartCardImpression)
	events.SetPageSuccess(cartCardImpEvent)
	jumboTrackItem := jumbo.GetJumboTrackItem(jumboPayload, events, jumbo.PurchaseEventsTableName)

	cc.JumboTracking = []*jumbo.Item{jumboTrackItem}
}

func (cc *CalculateCartData) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "cart_card"
	return commonPayload
}

func isProductCategoryAcademy(productCategoryId int32) bool {
	return productCategoryId == common.AcademyCategoryID
}

func isProductCategorySummercamp(productCategoryId int32) bool {
	return productCategoryId == common.SummerCampCategoryID
}

//////// Full Cart Page Data //////////
func (cc *CalculateCartData) AddResultSection(section *productModel.ProductResult) {
	cc.Results = append(cc.Results, section)
}

// Header section in Full Cart Page
func (cc *CalculateCartData) SetFullCartPageHeaderSection(ctx context.Context, location string) {

	text := fmt.Sprintf("Sports Membership (%s)", location)
	title, _ := sushi.NewTextSnippet(text)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	cc.Header = &productModel.CalculateCartHeaderSection{
		Title: title,
	}
}

// Top container in results section with names of all users in cart
func (cc *CalculateCartData) SetFullCartPageTopContainer(ctx context.Context, finalUsers []*productPB.CartUser) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	title, _ := sushi.NewTextSnippet("BUYING FOR")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(3)

	namesArr := make([]string, 0)
	for _, user := range finalUsers {
		namesArr = append(namesArr, user.Name)
	}
	names := strings.Join(namesArr, ", ")

	subtitle, _ := sushi.NewTextSnippet(names)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	subtitle.SetFont(font)
	subtitle.SetColor(color)

	// TODO: Add a custom alert button to change location

	item := &sushi.FitsoTextSnippetType3SnippetItem{
		Title:    title,
		Subtitle: subtitle,
	}
	snippet := &sushi.FitsoTextSnippetType3Snippet{
		Items: []*sushi.FitsoTextSnippetType3SnippetItem{item},
	}
	section := &productModel.ProductResult{
		LayoutConfig:          layoutConfig,
		SnippetConfig:         snippetConfig,
		FitsoTextSnippetType3: snippet,
	}

	cc.AddResultSection(section)
}

// Voucher response snippet below each product detail
func getVoucherResponseSnippet(ctx context.Context, voucherResponse *productPB.ProductVoucherResponse) *sushi.FitsoTextType4 {

	item := &sushi.FitsoTextSnippetType4Item{}

	voucherResponseText := voucherResponse.Status.Message
	voucherResponseSnippet, _ := sushi.NewTextSnippet(voucherResponseText)
	colorType := sushi.ColorTypeOrange
	if featuresupport.SupportsNewColor(ctx) {
		colorType = sushi.ALERT_LIGHT_THEME
	}
	voucherResponseColor, _ := sushi.NewColor(colorType, sushi.ColorTint500)
	voucherResponseFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

	if voucherResponse.DiscountAmount > 0 {
		discountText := fmt.Sprintf("-₹%d", voucherResponse.DiscountAmount)
		voucherDiscount, _ := sushi.NewTextSnippet(discountText)
		voucherResponseColor, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		voucherDiscountFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		voucherDiscount.SetFont(voucherDiscountFont)
		voucherDiscount.SetColor(voucherResponseColor)
		item.Subtitle = voucherDiscount

		voucherAppliedIcon, _ := sushi.NewIcon(sushi.CheckCircleIcon, voucherResponseColor)
		voucherResponseSnippet.SetPrefixIcon(voucherAppliedIcon)
	}

	voucherResponseSnippet.SetFont(voucherResponseFont)
	voucherResponseSnippet.SetColor(voucherResponseColor)
	item.Title = voucherResponseSnippet

	snippet := &sushi.FitsoTextType4{
		Items: []*sushi.FitsoTextSnippetType4Item{item},
	}

	return snippet
}

// Middle container in results section with all product details in cart
func (cc *CalculateCartData) SetFullCartPageProductSection(ctx context.Context, data *productPB.CalculateCartResponse) {

	for i, product := range data.ProductDetails {

		productLayoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.FitsoTextSnippetType11,
			LayoutType:  sushi.LayoutTypeGrid,
		}

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		if i == len(data.ProductDetails)-1 {
			separator, _ = sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
		}
		snippetConfig := &sushi.SnippetConfig{
			BottomSeparator: separator,
		}

		productTitle := fmt.Sprintf("%d x %s", product.Frequency, product.Name)
		title, _ := sushi.NewTextSnippet(productTitle)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		title.SetFont(font)
		title.SetColor(color)

		namesArr := make([]string, 0)
		for _, user := range product.CartUsers {
			namesArr = append(namesArr, user.Name)
		}
		names := strings.Join(namesArr, ", ")

		productSubtitle := fmt.Sprintf("%s, for %s", product.Duration, product.LocationName)
		subtitle, _ := sushi.NewTextSnippet(productSubtitle)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle.SetFont(font)
		subtitle.SetColor(color)

		productSubtitle2 := fmt.Sprintf("%s", names)
		subtitle2, _ := sushi.NewTextSnippet(productSubtitle2)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle2.SetFont(font)
		subtitle2.SetColor(color)

		productValue := fmt.Sprintf("₹%.0f", product.TotalAmountPerProduct)
		rightSubtitle, _ := sushi.NewTextSnippet(productValue)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		rightSubtitle.SetFont(font)
		rightSubtitle.SetColor(color)

		productMRP := product.Price * float32(product.Frequency)
		productPrice := fmt.Sprintf("~~₹%.0f~~", productMRP)
		rightSubtitle2, _ := sushi.NewTextSnippet(productPrice)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		rightSubtitle2.SetFont(font)
		rightSubtitle2.SetColor(color)
		rightSubtitle2.SetIsMarkdown(int32(1))

		productSnippet := &sushi.FitsoTextSnippetType11Snippet{
			Title:          title,
			Subtitle:       subtitle,
			Subtitle2:      subtitle2,
			RightSubtitle:  rightSubtitle,
			RightSubtitle2: rightSubtitle2,
		}

		productSection := &productModel.ProductResult{
			LayoutConfig:           productLayoutConfig,
			FitsoTextSnippetType11: productSnippet,
		}

		if product.VoucherResponse == nil || product.VoucherResponse.Status == nil || product.VoucherResponse.Status.Message == "" {
			productSection.SnippetConfig = snippetConfig
			cc.Results = append(cc.Results, productSection)
		} else {
			voucherResponseSnippet := getVoucherResponseSnippet(ctx, product.VoucherResponse)

			voucherLayoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.FitsoTextSnippetType4,
				LayoutType:  sushi.LayoutTypeGrid,
			}
			voucherSection := &productModel.ProductResult{
				LayoutConfig:          voucherLayoutConfig,
				SnippetConfig:         snippetConfig,
				FitsoTextSnippetType4: voucherResponseSnippet,
			}
			cc.Results = append(cc.Results, productSection)
			cc.Results = append(cc.Results, voucherSection)
		}
	}
}

// Apply Promo code or Promo Code response section
func (cc *CalculateCartData) SetApplyPromoCodeSection(ctx context.Context, formattedDiscount string, discountValue int32, voucherValid bool, voucherError string, autoApplyPromoCode bool, eligibleVoucherRes *productPB.GetVouchersRes) {
	loggedInUser := util.GetUserIDFromContext(ctx)
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoImageTextSnippetType24,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	snippet := &sushi.FitsoImageTextSnippetType24Snippet{}

	title, _ := sushi.NewTextSnippet("Promo Code")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetFont(font)
	title.SetColor(color)

	header := &sushi.CouponTrayHeader{
		Title: title,
	}

	placeholder, _ := sushi.NewTextSnippet("Enter promo code")

	dismissClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDismiss)
	dismissClickAction.DismissPage = &sushi.DismissPage{
		Type: "apply_custom_promo_code",
	}

	rightButton := &sushi.Button{
		Type: sushi.ButtontypeText,
		Text: "Apply",
		Color: &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorTypeRed,
		},
		ClickAction: dismissClickAction,
	}

	sectionItem := &sushi.CouponTraySectionItem{
		Type: "text",
		Text: &sushi.CouponTrayTextInput{
			Optional:    true,
			Id:          "coupon",
			Placeholder: placeholder,
			RightButton: rightButton,
		},
	}

	sections := make([]*sushi.CouponTraySectionItem, 0)
	sections = append(sections, sectionItem)

	results := []*sushi.CouponTrayResultItem{}
	if len(eligibleVoucherRes.Vouchers) > 0 {
		for _, voucher := range eligibleVoucherRes.Vouchers {
			layoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.FitsoTextSnippetType3,
				LayoutType:  sushi.LayoutTypeGrid,
			}
			separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
			separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
			snippetConfig := &sushi.SnippetConfig{
				BottomSeparator: separator,
			}

			title, _ := sushi.NewTextSnippet(voucher.Code)
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
			title.SetFont(font)
			title.SetColor(color)

			bgColorTint := sushi.ColorTint100
			if featuresupport.SupportsNewColor(ctx) {
				bgColorTint = sushi.ColorTint50
			}
			bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)

			borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)

			topLeftTag := &sushi.Tag{
				Title:       title,
				BgColor:     bgColor,
				BorderColor: borderColor,
			}

			title, _ = sushi.NewTextSnippet(voucher.Description)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
			title.SetFont(font)
			title.SetColor(color)

			// TODO: Add eligibility conditions

			subtitle2, _ := sushi.NewTextSnippet(fmt.Sprintf("You will save ₹%d on your order with this code", voucher.DiscountAmount))
			if voucher.DiscountAmount <= 0 {
				subtitle2, _ = sushi.NewTextSnippet("You will get extension with this code")
			}
			color, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint600)
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle2.SetFont(font)
			subtitle2.SetColor(color)

			rightButton, _ := sushi.NewButton(sushi.ButtontypeText)
			rightButton.SetText("Apply")
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDismiss)
			applyPromoCodeAction := &sushi.ApplyPromoCode{
				PromoCode: voucher.Code,
			}
			clickAction.DismissPage = &sushi.DismissPage{
				Type:           "apply_promo_code",
				ApplyPromoCode: applyPromoCodeAction,
			}
			rightButton.SetClickAction(clickAction)

			tapPayload := cc.GetCommonPayloadForTracking(ctx)
			tapPayload["promo_code"] = voucher.Code
			eName := "promo_code_pop_up_individual_apply_tap"
			if isProductCategoryAcademy(cc.productCategoryId) {
				eName = "academy_promo_code_pop_up_individual_apply_tap"
			}
			tapEname := &sushi.EnameData{
				Ename: eName,
			}
			applyPromoCodeEvent := sushi.NewClevertapEvents()
			applyPromoCodeEvent.SetTap(tapEname)
			applyPromoTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, applyPromoCodeEvent)
			rightButton.AddClevertapTrackingItem(applyPromoTrackItem)

			item := &sushi.FitsoTextSnippetType3SnippetItem{
				TopLeftTag:  topLeftTag,
				Title:       title,
				Subtitle2:   subtitle2,
				RightButton: rightButton,
			}
			items := []*sushi.FitsoTextSnippetType3SnippetItem{item}

			snippet := &sushi.FitsoTextSnippetType3Snippet{
				Items: items,
			}

			resultItem := &sushi.CouponTrayResultItem{
				LayoutConfig:          layoutConfig,
				SnippetConfig:         snippetConfig,
				FitsoTextSnippetType3: snippet,
			}
			results = append(results, resultItem)
		}

	}

	OpenPromoTray := &sushi.OpenPromoTray{
		Header:   header,
		Sections: sections,
		Results:  results,
	}

	clickAction := sushi.GetClickAction()
	clickAction.SetOpenPromoTray(OpenPromoTray)

	if len(cc.promoCode) > 0 {
		if voucherValid {

			title, _ := sushi.NewTextSnippet(fmt.Sprintf("Code %s applied!", cc.promoCode))
			titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			title.SetFont(titleFont)
			title.SetColor(titleColor)
			snippet.Title = title

			var subtitleText string
			if cc.data.DiscountAmount > 0 {
				textExtension := "\nExtension will be applied post purchase"
				subtitleText = fmt.Sprintf("You saved %s", formattedDiscount)
				if !isProductCategoryAcademy(cc.productCategoryId) && (time.Now().Unix() > common.SaleStartDateUnix || util.Contains(common.TestUsersAutoPromoCode, loggedInUser)) && time.Now().Unix() < common.SaleEndDateUnix {
					subtitleText += textExtension
				}
			} else {
				if len(cc.data.CustomSuccessPromoMessage) > 0 {
					subtitleText = cc.data.CustomSuccessPromoMessage
				} else {
					subtitleText = "Promo code applied successfully!"
				}
			}
			subtitle, _ := sushi.NewTextSnippet(subtitleText)
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle.SetFont(subtitleFont)
			subtitle.SetColor(subtitleColor)
			snippet.Subtitle = subtitle

			leftIconColor, _ := sushi.NewColor(sushi.ColorTypePurple, sushi.ColorTint500)
			leftIcon, _ := sushi.NewIcon(sushi.ReferEarnIcon, leftIconColor)
			snippet.LeftIcon = leftIcon

			rightTitle, _ := sushi.NewTextSnippet("Success!")
			rightTitleColor, _ := sushi.NewColor(sushi.ColorTypePurple, sushi.ColorTint500)
			rightTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			rightTitle.SetFont(rightTitleFont)
			rightTitle.SetColor(rightTitleColor)
			snippet.RightTitle = rightTitle

			if !autoApplyPromoCode {
				trackingPayload := cc.GetCommonPayloadForTracking(ctx)
				trackingPayload["application"] = "success"
				trackingEname := "promo_application_impression"
				impressionEname := &sushi.EnameData{
					Ename: trackingEname,
				}
				impressionEvent := sushi.NewClevertapEvents()
				impressionEvent.SetImpression(impressionEname)
				trackItemImpression := sushi.GetClevertapTrackItem(ctx, trackingPayload, impressionEvent)

				successCustomAlert := util.CustomAlertForCouponApplied(ctx, formattedDiscount, cc.promoCode, trackItemImpression)

				actionListItem := &productModel.ActionListItem{
					Type:        "custom_alert",
					CustomAlert: successCustomAlert,
				}

				cc.ActionList = append(cc.ActionList, actionListItem)
			}

		} else {
			title, _ := sushi.NewTextSnippet("Promo Code invalid")
			titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			title.SetFont(titleFont)
			title.SetColor(titleColor)
			snippet.Title = title

			var errorMessage string
			if len(voucherError) > 0 {
				errorMessage = voucherError
			} else {
				errorMessage = "This code does not exist"
			}

			subtitle, _ := sushi.NewTextSnippet(errorMessage)
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle.SetFont(subtitleFont)
			subtitle.SetColor(subtitleColor)
			snippet.Subtitle = subtitle

			leftIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			leftIcon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, leftIconColor)
			snippet.LeftIcon = leftIcon

		}

		rightAction, _ := sushi.NewButton(sushi.ButtontypeText)
		rightAction.SetText("Try another code")
		rightActionColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		rightActionFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		rightAction.SetColor(rightActionColor)
		rightAction.SetFont(rightActionFont)

		rightAction.SetClickAction(clickAction)

		tapPayload := cc.GetCommonPayloadForTracking(ctx)
		eName := "try_another_code_click"
		if isProductCategoryAcademy(cc.productCategoryId) {
			eName = "academy_try_another_code_click"
		}
		tapEname := &sushi.EnameData{
			Ename: eName,
		}
		tryAnotherCodeEvent := sushi.NewClevertapEvents()
		tryAnotherCodeEvent.SetTap(tapEname)
		tryAnotherCodeTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tryAnotherCodeEvent)
		rightAction.AddClevertapTrackingItem(tryAnotherCodeTrackItem)

		snippet.RightAction = rightAction

	} else {
		title, _ := sushi.NewTextSnippet(fmt.Sprintf("Apply Promo Code"))
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title.SetColor(color)
		title.SetFont(font)
		snippet.Title = title

		leftIconColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		leftIcon, _ := sushi.NewIcon(sushi.PromoIcon, leftIconColor)
		snippet.LeftIcon = leftIcon

		rightIconColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		rightIcon, _ := sushi.NewIcon(sushi.ChevronRightIcon, rightIconColor)
		snippet.RightIcon = rightIcon

		bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)
		snippet.BackgroundColor = bgColor

		snippet.ClickAction = clickAction

	}

	section := &productModel.ProductResult{
		LayoutConfig:                layoutConfig,
		SnippetConfig:               snippetConfig,
		FitsoImageTextSnippetType24: snippet,
	}

	cc.Results = append(cc.Results, section)
}

// Total Bill Section
func (cc *CalculateCartData) SetFullCartPageCartValueSection(ctx context.Context, formattedTotalCartValue string, totalCartValue float32, voucherValid bool, discountAmount int32) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoTextSnippetType11,
	}
	isNoCostEmiSupported := featuresupport.SupportsNoCostEMI(ctx)
	title, _ := sushi.NewTextSnippet("Total cost")
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	subtitle, _ := sushi.NewTextSnippet("Total price is inclusive of all taxes\n{blue-500|<medium-300|No cost EMI plans available on next step>}")
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	subtitle.SetColor(subtitleColor)
	subtitle.SetFont(subtitleFont)

	if isNoCostEmiSupported {
		subtitle, _ = sushi.NewTextSnippet("No cost EMI plans available on next step")
		subtitleColor, _ = sushi.NewColor(sushi.NEUTRAL_LIGHT_THEME, sushi.ColorTint500)
		subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subtitle.SetColor(subtitleColor)
		subtitle.SetFont(subtitleFont)
	} else {
		subtitle.SetIsMarkdown(int32(1))
	}

	rightSubtitle, _ := sushi.NewTextSnippet(formattedTotalCartValue)
	rightSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	rightSubtitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	rightSubtitle.SetColor(rightSubtitleColor)
	rightSubtitle.SetFont(rightSubtitleFont)

	snippet := &sushi.FitsoTextSnippetType11Snippet{
		Title:         title,
		Subtitle:      subtitle,
		RightSubtitle: rightSubtitle,
	}

	if isNoCostEmiSupported {
		rightSubtitle3Text := fmt.Sprintf("(Inclusive of all taxes)")
		rightSubtitle3, _ := sushi.NewTextSnippet(rightSubtitle3Text)
		rightSubtitle3Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		rightSubtitle3Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		rightSubtitle3.SetColor(rightSubtitle3Color)
		rightSubtitle3.SetFont(rightSubtitle3Font)
		rightSubtitle3.SetIsMarkdown(1)
		snippet.RightSubtitle3 = rightSubtitle3
	}

	section := &productModel.ProductResult{
		LayoutConfig:           layoutConfig,
		FitsoTextSnippetType11: snippet,
	}

	cc.Results = append(cc.Results, section)
}

// Cart Footer Section
func (cc *CalculateCartData) SetFullCartPageFooterSection(ctx context.Context, formattedTotalCartValue string) {
	buttonTitle, _ := sushi.NewTextSnippet(formattedTotalCartValue)

	buttonSubtitle1, _ := sushi.NewTextSnippet("TOTAL")

	buttonSubtitle2, _ := sushi.NewTextSnippet("Buy now")
	iconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint000)
	icon, _ := sushi.NewIcon(sushi.RightArrowIcon, iconColor)
	buttonSubtitle2.SetSuffixIcon(icon)

	cartButton := &productModel.CartButton{
		Title:      buttonTitle,
		SubTitle:   buttonSubtitle1,
		SubTitle2:  buttonSubtitle2,
		IsDisabled: false,
	}

	eventName := "cart_buy_now_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eventName = "academy_cart_buy_now_tap"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eventName = "summercamp_cart_buy_now_tap"
	}
	trackingItems := cc.getTrackingItemsForCartButtons(ctx, "", eventName)
	cartButton.ClevertapTracking = trackingItems

	if cc.data.CartError {
		cartButton.IsDisabled = true
	}

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("CANCEL")
	button.SetSize(sushi.ButtonSizeMedium)
	eventName = "cart_cancel_tap"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eventName = "academy_cart_cancel_tap"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eventName = "summercamp_cart_buy_now_tap"
	}
	button.ClevertapTracking = cc.getTrackingItemsForCartButtons(ctx, "", eventName)

	cc.Footer = &productModel.CalculateCartFooterSection{
		RightButton: cartButton,
	}
}

func (cc *CalculateCartData) SetCustomAlertSection(ctx context.Context, errorMessage string) {
	log.Println("SetCustomAlertSection called with productCategoryId: ", cc.productCategoryId, cc)
	if len(errorMessage) <= 0 || isProductCategoryAcademy(cc.productCategoryId) || isProductCategorySummercamp(cc.productCategoryId) {
		return
	}

	phoneDuplicationCustomAlert := util.CustomAlertForPhoneDuplication(ctx, errorMessage)

	actionListItem := &productModel.ActionListItem{
		Type:        "custom_alert",
		CustomAlert: phoneDuplicationCustomAlert,
	}

	cc.ActionList = append(cc.ActionList, actionListItem)
}

func (cc *CalculateCartData) SetFullCartClevertapTrackingImpression(ctx context.Context) {
	commonPayload := cc.GetCommonPayloadForTracking(ctx)

	eName := "cart_card_impression"
	if isProductCategoryAcademy(cc.productCategoryId) {
		eName = "academy_cart_card_impression"
	} else if isProductCategorySummercamp(cc.productCategoryId) {
		eName = "summercamp_cart_card_impression"
	}
	landingEName := &sushi.EnameData{
		Ename: eName,
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	cc.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem}
}

// CalculateCartC handles calculate cart request
func CalculateCartC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(productModel.CalculateCartRequest)

	var parentUser *productPB.CartUser
	if json.ParentUser != nil {
		user := json.ParentUser
		parentUser = &productPB.CartUser{
			Phone:               user.Phone,
			PlanStartDate:       user.PlanStartDate,
			Name:                user.Name,
			PreferredFacilityId: user.PreferredFacilityId,
			PreferredSportId:    user.PreferredSportId,
			Age:                 user.Age,
			ProductId:           user.ProductId,
		}
	}

	var childUsersToSubscribe []*productPB.CartUser
	for _, user := range json.ChildUsers {
		childUsersToSubscribe = append(childUsersToSubscribe, &productPB.CartUser{
			Phone:               user.Phone,
			PlanStartDate:       user.PlanStartDate,
			Name:                user.Name,
			PreferredFacilityId: user.PreferredFacilityId,
			PreferredSportId:    user.PreferredSportId,
			Age:                 user.Age,
			ProductId:           user.ProductId,
			UserId:              user.UserId,
		})
	}

	var productId int32
	var err error
	if !featuresupport.SupportsMultipleFeaturedProducts(ctx) {
		productId, err = util.GetInt32FromInterface(json.ProductId)
		if err != nil {
			log.Printf("product id value is not correct: %v", err.Error())
			c.JSON(
				http.StatusBadRequest,
				models.StatusFailure("Something went wrong!"),
			)
			return
		}
	}

	pcl := util.GetProductClient()

	if len(json.PromoCode) == 0 {
		reqData := &productPB.Empty{}
		referralEligiblity, err := pcl.GetReferralEligiblity(ctx, reqData)
		if err == nil {
			if referralEligiblity.EligibleForReferral && referralEligiblity.ReferralUserId > 0 {
				refCodeData := &productPB.ReferralCodeGenerateOrGet{
					UserId: referralEligiblity.ReferralUserId,
				}
				referralCodeRes, err := pcl.GenerateOrGetReferralCode(ctx, refCodeData)
				if err != nil && referralCodeRes.Status.Status != common.SUCCESS && len(referralCodeRes.Code) == 0 {
					log.Printf("func:CalculateCartC, error in getting referral code, err:%v", err)
					c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusUnauthorized(common.UNEXPECTED_ERROR))
					return
				}
				json.PromoCode = referralCodeRes.Code
			}
		}
	}

	cartRequest := &productPB.CalculateCartRequest{
		ProductId:         productId,
		ChildUsers:        childUsersToSubscribe,
		ReferralCode:      json.ReferralCode,
		PromoCode:         json.PromoCode,
		ParentUser:        parentUser,
		ProductCategoryId: json.ProductCategoryId,
	}
	log.Println("cartRequest: ", cartRequest)

	response, err := pcl.CalculateCart(ctx, cartRequest)

	if err != nil {
		log.Printf("error in calculating cart, %v", err)
		c.JSON(
			http.StatusInternalServerError,
			models.StatusFailure("Something went wrong!"),
		)
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.JSON(
			http.StatusUnauthorized,
			models.StatusUnauthorized("You are not authorized!"),
		)
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		c.JSON(
			http.StatusOK,
			models.StatusFailure("Something went wrong!"),
		)
		return
	}
	if featuresupport.IsWebRequestV2(c) {
		c.JSON(
			http.StatusOK,
			response,
		)
		return
	}

	cartPage := CalculateCartData{
		promoCode: json.PromoCode,
	}

	var cartUsers []*structs.CommonCartUser
	for _, user := range response.FinalCartUsers {
		cartUsers = append(cartUsers, &structs.CommonCartUser{
			Name: user.Name,
		})
	}

	autoApplyPromoCode := cartPage.promoCode == ""

	cartPage.setCalculateCartData(response)
	if featuresupport.SupportsFullPageCart(ctx) {

		eligibleVoucherReq := &productPB.GetEligibleVouchersReq{
			ProductDetails: response.ProductDetails,
			UsersCount:     int32(len(response.FinalCartUsers)),
		}
		eligibleVoucherRes, err := pcl.GetEligibleVouchers(ctx, eligibleVoucherReq)
		if err != nil {
			log.Printf("error in getting eligible vouchers, %v", err)
			c.JSON(
				http.StatusInternalServerError,
				models.StatusFailure("Something went wrong!"),
			)
			return
		}

		log.Println("response: ", response)
		cartPage.SetFullCartPageHeaderSection(ctx, response.ProductDetails[0].LocationName)
		cartPage.SetFullCartPageTopContainer(ctx, response.FinalCartUsers)
		cartPage.SetFullCartPageProductSection(ctx, response)
		cartPage.SetApplyPromoCodeSection(ctx, response.FormattedDiscount, response.DiscountAmount, response.VoucherValid, response.VoucherError, autoApplyPromoCode, eligibleVoucherRes)
		cartPage.SetFullCartPageCartValueSection(ctx, response.FormattedTotalCartValue, response.TotalCartValue, response.VoucherValid, response.DiscountAmount)
		cartPage.SetFullCartClevertapTrackingImpression(ctx)
		cartPage.SetFullCartPageFooterSection(ctx, response.FormattedTotalCartValue)
		if response.CartError {
			cartPage.SetCustomAlertSection(ctx, response.CartErrorMessage)
		}

		sushiResponseSections := &productModel.CalculateCartResponseV2{
			Header:            cartPage.Header,
			Results:           cartPage.Results,
			Footer:            cartPage.Footer,
			ActionList:        cartPage.ActionList,
			ClevertapTracking: cartPage.ClevertapTracking,
		}

		c.JSON(
			http.StatusOK,
			sushiResponseSections,
		)
	} else {

		cartPage.SetPaymentsData(response.CountryId, response.ServiceType, response.TotalCartValue)
		cartPage.SetCartPageHeader(ctx, cartUsers)
		cartPage.setTopContainer()
		cartPage.SetMembershipData(ctx, response)
		cartPage.SetOffersData(ctx, response.FormattedDiscount, response.DiscountAmount, response.VoucherValid, response.VoucherError)
		cartPage.SetBillInfoData(response.FormattedTotalCartValue, response.TotalCartValue, response.VoucherValid, response.DiscountAmount)
		cartPage.SetCartButtonData(ctx, response.FormattedTotalCartValue)
		cartPage.SetClevertapTracking(ctx)
		cartPage.SetJumboTracking(ctx)
		cartPage.SetPaymentProvider(ctx, response.PaymentProvider)

		sushiResponseSections := &productModel.CalculateCartResultSection{
			PaymentsData:    cartPage.paymentsData,
			HeaderData:      cartPage.headerData,
			MembershipData:  cartPage.membershipData,
			BillInfoData:    cartPage.billInfoData,
			OffersData:      cartPage.offersData,
			CartButtonData:  cartPage.cartButtonData,
			PaymentProvider: cartPage.PaymentProvider,
		}

		c.JSON(
			http.StatusOK,
			&productModel.CalculateCartResponse{
				Status:            common.SUCCESS,
				ResultSections:    sushiResponseSections,
				ClevertapTracking: cartPage.ClevertapTracking,
				JumboTracking:     cartPage.JumboTracking,
			},
		)
	}
}
