package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/jogocoin/go_api/api/common"
	// "golang.org/x/text/language"
	// "golang.org/x/text/message"

	"bitbucket.org/jogocoin/go_api/api/models"
	productModels "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	// featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type SummerCampRecommendedCoursePageData struct {
	RecommendedPlanData *productPB.GetSummerCampPreferredPlanResponse
}

type SummerCampRecommendedPlanPostbackParams struct {
	UserId            int32                     `json:"user_id"`
	PlanStartDate     int64                     `json:"plan_start_date"`
	SummercampSlotIds []int32                   `json:"summercamp_slot_ids"`
	SummercampSlots   map[int32]*SummerCampSlot `json:"summercamp_slots"`
}
type SummerCampSlot struct {
	FsId                       int32
	SlotId                     int32
	SummercampProductMappingId int32
}
type SummerCampCourseTemplate struct {
	Header             *sushi.Header                                    `json:"header,omitempty"`
	BottomButtonStates *sushi.BottomButtonStates                        `json:"bottom_button_states,omitempty"`
	RecommendedPlan    *productModels.AcademyRecommendedPlan            `json:"recommended_plan,omitempty"`
	Items              []*productModels.AcademyRecommendedPlanItem      `json:"items,omitempty"`
	ItemsConfig        *productModels.AcademyRecommendedPlanItemsConfig `json:"items_config,omitempty"`
	PageRequest        *productPB.GetPreferredPlanRequest               `json:"-"`
	PageData           *SummerCampRecommendedCoursePageData             `json:"-"`
	PostbackData       *SummerCampRecommendedPlanPostbackParams         `json:"-"`
	CourseCategoryPostback []productPB.PurchaseSelectedUser                 `json:"-"`
}

func SummerCampRecommendedPlansC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	jsonReq := c.MustGet("jsonData").(structs.GetAcademyRecommendedCourse)
	log.Printf("AcademyRecommendedCourseC: Error Provider : City Id - %d", util.GetCityIDFromContext(ctx))
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("You need to login first!"))
		return
	}

	template := &SummerCampCourseTemplate{}

	reqData := &productPB.GetPreferredPlanRequest{
		UserId:            jsonReq.UserId,
		ProductCategoryId: jsonReq.ProductCategoryId,
	}

	if jsonReq.CourseCategoryPostback != "" {
		log.Println("SummerCampRecommendedPlansC: json data",jsonReq.CourseCategoryPostback)
		var courseCategoryPostback []productPB.PurchaseSelectedUser
		err := json.Unmarshal([]byte(jsonReq.CourseCategoryPostback), &courseCategoryPostback)
		if err != nil {
			log.Println("SummerCampRecommendedPlansC: json marshal error", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		log.Println("SummerCampRecommendedPlansC: postbackData: ",courseCategoryPostback)
		template.CourseCategoryPostback = courseCategoryPostback
	}

	if jsonReq.PostbackParams != "" {
		var postbackData SummerCampRecommendedPlanPostbackParams
		template.PostbackData = &postbackData
		err := json.Unmarshal([]byte(jsonReq.PostbackParams), &postbackData)
		if err != nil {
			log.Println("json marshal error", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		log.Println("postbackData: ", postbackData, postbackData.SummercampSlotIds)

		summercampProductMappingId, summercampSlotsMap, err := template.GetPostBackDataForSummercampSlots(ctx, postbackData.SummercampSlotIds)
		if err != nil {
			log.Println("GetCourseCategoryForAcademySlots", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}

		if jsonReq.SummercampProductMappingId > 0 {
			if summercampProductMappingId == jsonReq.SummercampProductMappingId {
				postbackData.SummercampSlots = summercampSlotsMap
				template.PostbackData = &postbackData
				reqData.SummercampProductMappingId = summercampProductMappingId
			} else {
				reqData.SummercampProductMappingId = jsonReq.SummercampProductMappingId
			}
		} else if summercampProductMappingId > 0 {
			postbackData.SummercampSlots = summercampSlotsMap
			reqData.SummercampProductMappingId = summercampProductMappingId
			template.PostbackData = &postbackData
		}
	} else {
		reqData.SummercampProductMappingId = jsonReq.SummercampProductMappingId
	}

	productService := util.GetProductClient()
	response, err := productService.GetSummerCampRecommendedCourseForUser(ctx, reqData)
	if err != nil {
		log.Println("GetAcademyRecommendedCourseForUser", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil {
		if response.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, models.StatusFailure("You are not authorised"))
			return
		}
		if response.Status.Status == common.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
			return
		}
		if response.Status.Status == common.FAILED {
			c.JSON(http.StatusOK, models.StatusFailure(response.Status.Message))
			return
		}
	}

	template.PageRequest = reqData
	template.PageData = &SummerCampRecommendedCoursePageData{
		RecommendedPlanData: response,
	}

	template.SetHeader(ctx)
	template.SetBottomButtonStates(ctx)
	template.SetRecommendedCourse(ctx)
	template.AddPersonalInfoInput(ctx)
	template.AddStartDateInput(ctx)
	template.AddCenterTimeInput(ctx)
	template.SetItemsConfig(ctx)
	c.JSON(http.StatusOK, template)
}

func (a *SummerCampCourseTemplate) GetPostBackDataForSummercampSlots(ctx context.Context, summercampSlotIds []int32) (int32, map[int32]*SummerCampSlot, error) {
	summercampSlotsMap := make(map[int32]*SummerCampSlot)
	if len(summercampSlotIds) == 0 {
		return 0, summercampSlotsMap, fmt.Errorf("No Summer camp slots provided")
	}

	bookingClient := util.GetBookingClient()
	getSlotReq := &bookingPB.SummercampSlot{
		Id:       summercampSlotIds[0],
		IsActive: true,
	}
	getSlotRes, err := bookingClient.GetSummercampSlots(ctx, getSlotReq)
	if err != nil {
		log.Println(err)
		return 0, summercampSlotsMap, err
	}
	if len(getSlotRes.SummercampSlots) == 0 {
		return 0, summercampSlotsMap, fmt.Errorf("No active summer camp slot found")
	}

	slot := getSlotRes.SummercampSlots[0]
	summercampSlotsMap[slot.Id] = &SummerCampSlot{
		FsId:                       slot.FsId,
		SlotId:                     slot.SlotId,
		SummercampProductMappingId: slot.SummercampProductMappingId,
	}

	return slot.SummercampProductMappingId, summercampSlotsMap, nil
}

func (a *SummerCampCourseTemplate) SetHeader(ctx context.Context) {
	if a.PageData.RecommendedPlanData.User == nil {
		return
	}

	user := a.PageData.RecommendedPlanData.User
	title, _ := sushi.NewTextSnippet(fmt.Sprintf("%s, %d years", user.Name, user.Age))
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(font)
	title.SetColor(color)
	header := &sushi.Header{
		Title: title,
	}
	a.Header = header
}

func (a *SummerCampCourseTemplate) SetBottomButtonStates(ctx context.Context) {
	disabledButton := &sushi.Button{
		Type: sushi.ButtonTypeSolid,
		Text: "Save details to add member",
		BgColor: &sushi.Color{
			Tint: sushi.ColorTint400,
			Type: sushi.ColorTypeGrey,
		},
		IsActionDisabled: 1,
	}

	clickAction, _ := sushi.NewTextClickAction("save_purchase_details")
	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "summercamp_save_member_details_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	completedButton := &sushi.Button{
		Type: sushi.ButtonTypeSolid,
		Text: "Save details to add member",
		BgColor: &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorTypeRed,
		},
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button: completedButton,
		},
	}
	a.BottomButtonStates = bottomButtonStates
}

func (a *SummerCampCourseTemplate) SetRecommendedCourse(ctx context.Context) {
	if len(a.PageData.RecommendedPlanData.SportProducts) == 0 {
		return
	}

	sportProduct := a.PageData.RecommendedPlanData.SportProducts[0]

	if len(sportProduct.Products) == 0 {
		return
	}
	product := sportProduct.Products[0]

	title, _ := sushi.NewTextSnippet("Recommended plan")
	fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetFont(fontSemi400)

	clickAction := sushi.GetClickAction()
	userData := &sushi.UserData{
		UserId:                     a.PageRequest.UserId,
		ProductCategoryId:          a.PageRequest.ProductCategoryId,
		SummercampProductMappingId: a.PageRequest.SummercampProductMappingId,
	}
	/*if len(a.SelectedUsers) > 0 {
		params, _ := json.Marshal(a.SelectedUsers)
		userData.CourseCategoryPostback = string(params)
	}*/
	preferredPlan := &sushi.OpenAcademyPreferredCourse{
		UserData:                  userData,
		IsRecommendedCourseOpened: true,
	}
	clickAction.SetOpenAcademyPreferredCourse(preferredPlan)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Change")
	button.SetClickAction(clickAction)

	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapPayload["summercamp_product_mapping_id"] = product.SummercampProductMappingId
	tapEname := &sushi.EnameData{
		Ename: "summercamp_change_course_plan_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	button.AddClevertapTrackingItem(trackItem)

	header := &sushi.Header{
		Title:      title,
		EditButton: button,
	}

	snippetTitleText := fmt.Sprintf("%d %s", product.Duration, product.DurationUnit)
	snippetTitle, _ := sushi.NewTextSnippet(snippetTitleText)
	colorBlack900, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	snippetTitle.SetFont(fontSemi400)
	snippetTitle.SetColor(colorBlack900)

	daysInt, daysStr := util.GetProductDaysOfWeekSummercamp(product.DaysOfWeek)
	subtitle2Text := fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|>}", daysStr)
	if len(daysInt) == 2 {
		subtitle2Text = fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|| Weekends>}", daysStr)
	} else if len(daysInt) == 0 || len(daysInt) == 1 || len(daysInt) > 5 {
		subtitle2Text = fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|}", daysStr)
	} else {
		subtitle2Text = fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|| Weekdays>}", daysStr)
	}
	subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle2.SetFont(fontMed100)
	subtitle2.SetColor(colorGrey900)
	subtitle2.SetIsMarkdown(1)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	subtitle1Text := fmt.Sprintf("%d hr/ session", product.SessionDuration/60)
	subtitle1, _ := sushi.NewTextSnippet(subtitle1Text)
	fontRegular100, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	colorGrey700, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle1.SetFont(fontRegular100)
	subtitle1.SetColor(colorGrey700)
	subtitle1.SetIsMarkdown(1)

	rightTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", product.RetailPrice))
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	rightTitle.SetFont(fontMed400)
	rightTitle.SetColor(colorBlack900)

	rightSubtitleText := fmt.Sprintf("₹%.f", product.Price)
	rightSubtitle, _ := sushi.NewTextSnippet(rightSubtitleText)
	fontMedium100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	colorGrey600, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	rightSubtitle.SetFont(fontMedium100)
	rightSubtitle.SetColor(colorGrey600)
	rightSubtitle.Strikethrough = true

	image := &sushi.Image{
		URL:         sportProduct.SportIcon,
		AspectRatio: 1,
		Type:        sushi.ImageTypeCircle,
		Height:      48,
		Width:       48,
	}

	var sportBgColor *sushi.Color
	if val, ok := common.SportIdBackgroundColorMap[product.SportId]; ok {
		sportBgColor, _ = sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
	}

	if len(sportBgColor.Type) > 0 {
		image.BgColor = sportBgColor
	}

	snippet := &sushi.FitsoTextSnippetType8Snippet{
		Id:          product.SummercampProductMappingId,
		Title:       snippetTitle,
		BgColor:     bgColor,
		BorderColor: borderColor,
		// Subtitle1:     subtitle1,
		RightTitle:    rightTitle,
		RightSubtitle: rightSubtitle,
		IsSelectable:  true,
		IsSelected:    true,
		Image:         image,
	}
	if len(product.DaysOfWeek) > 0 {
		snippet.Subtitle2 = subtitle2
	}

	if len(product.ProductOfferings) > 0 {
		subtitle3, _ := sushi.NewTextSnippet(strings.Join(product.ProductOfferings[:], "\n"))
		fontMedium100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		colorGrey700, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		subtitle3.SetFont(fontMedium100)
		subtitle3.SetColor(colorGrey700)

		snippet.Subtitle1 = subtitle3
	}

	a.RecommendedPlan = &productModels.AcademyRecommendedPlan{
		Header:  header,
		Snippet: snippet,
	}
}

func (p *SummerCampCourseTemplate) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["p_user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "summercamp_plan_details_page"
	return commonPayload
}

func (a *SummerCampCourseTemplate) AddInputItem(item *productModels.AcademyRecommendedPlanItem) {
	a.Items = append(a.Items, item)
}

func (a *SummerCampCourseTemplate) AddPersonalInfoInput(ctx context.Context) {
	user := a.PageData.RecommendedPlanData.User
	nonEditableDetails := &productModels.NonEditableUserDetails{
		Name:  user.Name,
		Phone: user.Phone,
	}

	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButton.SetText("Save details")
	colorGrey400, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(colorGrey400)
	disabledButton.SetActionDisabled()

	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButton.SetText("Save details")
	colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButton.SetBgColor(colorRed500)

	m := map[string]interface{}{
		"user_id":   a.PageRequest.UserId,
		"flow_type": common.ACADEMY_PURCHASE_RECOMMENDED_FLOW,
		"name":      user.Name,
	}
	payload, _ := json.Marshal(m)

	ageEditClickAction := sushi.GetClickAction()
	apiCall := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/academy/edit-member",
		Body:        string(payload),
	}
	ageEditClickAction.SetApiCallAction(apiCall)
	completedButton.SetClickAction(ageEditClickAction)

	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button: completedButton,
		},
	}

	inputAgeAlwaysTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("age must be between %d-%d years", common.SUMMER_CAMP_MIN_AGE, common.SUMMER_CAMP_MAX_AGE))
	inputAgeEmptyTitle, _ := sushi.NewTextSnippet("Age cannot be empty")
	minAgeErrorTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age cannot be less than %d years", common.SUMMER_CAMP_MIN_AGE))
	maxAgeErrorTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age cannot be more than %d years", common.SUMMER_CAMP_MAX_AGE))
	agePlaceholderTitle, _ := sushi.NewTextSnippet("Age (in years)")

	inputFieldStates := &sushi.InputFieldStates{
		Always: &sushi.InputFieldStateTitle{
			Title: inputAgeAlwaysTitle,
		},
		Empty: &sushi.InputFieldStateTitle{
			Title: inputAgeEmptyTitle,
		},
		MinAge: &sushi.InputFieldStateAge{
			Age:        common.SUMMER_CAMP_MIN_AGE,
			ErrorTitle: minAgeErrorTitle,
		},
		MaxAge: &sushi.InputFieldStateAge{
			Age:        common.SUMMER_CAMP_MAX_AGE,
			ErrorTitle: maxAgeErrorTitle,
		},
	}
	ageInput := &sushi.InputField{
		Optional:    false,
		Placeholder: agePlaceholderTitle,
		States:      inputFieldStates,
	}
	ageInputSection := &productModels.AcademyRecommendedPlanInputSection{
		Type: "age",
		Age:  ageInput,
	}

	editInfo := &productModels.AcademyRecommendedPlanEditInfo{
		BottomButtonStates: bottomButtonStates,
		Sections:           []*productModels.AcademyRecommendedPlanInputSection{ageInputSection},
	}
	editInfoData := &productModels.AcademyRecommendedPlanPersonalInfoEdit{
		NonEditableUserDetails: nonEditableDetails,
		EditInfo:               editInfo,
	}

	expandedHeaderTitle, _ := sushi.NewTextSnippet("Personal Details")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontSemi300, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	expandedHeaderTitle.SetColor(colorGrey900)
	expandedHeaderTitle.SetFont(fontSemi300)

	expandedState := &productModels.AcademyRecommendedPlanInputExpandedState{
		Title:        expandedHeaderTitle,
		EditInfoData: editInfoData,
	}

	collapsedHeaderTitle, _ := sushi.NewTextSnippet("Personal Details")
	textColor, _ := sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	collapsedHeaderTitle.SetColor(textColor)
	collapsedHeaderTitle.SetFont(fontMed100)

	var collapsedHeaderSubtitle *sushi.TextSnippet
	if user.Age > 0 {
		collapsedHeaderSubtitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%s, %d years", user.Name, user.Age))
	} else {
		collapsedHeaderSubtitle, _ = sushi.NewTextSnippet(user.Name)
	}
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	collapsedHeaderSubtitle.SetColor(colorGrey900)
	collapsedHeaderSubtitle.SetFont(fontMed400)

	userClient := util.GetUserServiceClient()
	isEditableRes, err := userClient.GetAcademyUserEditableBehaviour(ctx, &userPB.CheckAcademyUserEditableRequest{UserId: a.PageRequest.UserId})
	if err != nil {
		log.Println(err)
		return
	}

	editButton, _ := sushi.NewButton(sushi.ButtontypeText)
	editButton.SetText("Edit")
	if !isEditableRes.IsEditable {
		editButton.SetActionDisabled()
		editButton.SetColor(colorGrey400)
	}

	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "academy_edit_personal_info_tap",
	}
	editButtonEvents := sushi.NewClevertapEvents()
	editButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, editButtonEvents)
	editButton.AddClevertapTrackingItem(trackItem)

	closedState := &productModels.AcademyRecommendedPlanInputClosedState{
		Title:      collapsedHeaderTitle,
		Subtitle:   collapsedHeaderSubtitle,
		EditButton: editButton,
	}

	personalInfoInputItem := &productModels.AcademyRecommendedPlanItem{
		Type: "personal_info",
		PersonalInfo: &productModels.AcademyRecommendedPlanInputItem{
			ClosedState:   closedState,
			ExpandedState: expandedState,
		},
	}

	a.AddInputItem(personalInfoInputItem)
}

func (a *SummerCampCourseTemplate) AddStartDateInput(ctx context.Context) {
	cityId := util.GetCityIDFromContext(ctx)
	planStartDate := time.Now().Unix()
	productStartDate := time.Now().Unix()
	loc, _ := time.LoadLocation("Asia/Kolkata")
	sellPlanStartDate := time.Date(2024, time.Month(4), 1, 0, 0, 0, 0, loc).Unix()
	if cityId == common.HYDERABAD_CITY_ID {
		sellPlanStartDate = time.Date(2024, time.Month(4), 20, 0, 0, 0, 0, loc).Unix()
	}
	if cityId == common.CITY_ID_DELHI_NCR {
		sellPlanStartDate = time.Date(2024, time.Month(5), 20, 0, 0, 0, 0, loc).Unix()
	}
	if planStartDate < sellPlanStartDate {
		planStartDate = sellPlanStartDate
	}

	if a.PostbackData != nil && a.PostbackData.PlanStartDate > 0 {
		planStartDate = a.PostbackData.PlanStartDate
	}
	productStartDate = planStartDate
	expandedTitle, _ := sushi.NewTextSnippet("Select your plan start date")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontSemi300, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	expandedTitle.SetColor(colorGrey900)
	expandedTitle.SetFont(fontSemi300)

	tooltipIcon := a.GetStartDateTooltipIcon(ctx)

	inputDateAlwaysTitle, _ := sushi.NewTextSnippet("you can choose plan start date upto 2 weeks from today")
	inputFieldStates := &sushi.InputFieldStates{
		Always: &sushi.InputFieldStateTitle{
			Title: inputDateAlwaysTitle,
		},
	}

	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButton.SetText("Save and proceed")
	colorGrey400, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(colorGrey400)
	disabledButton.SetActionDisabled()

	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButton.SetText("Save and proceed")
	colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButton.SetBgColor(colorRed500)

	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapSaveEname := &sushi.EnameData{
		Ename: "academy_save_plan_start_date_tap",
	}
	saveButtonEvents := sushi.NewClevertapEvents()
	saveButtonEvents.SetTap(tapSaveEname)
	tracksItem := sushi.GetClevertapTrackItem(ctx, tapPayload, saveButtonEvents)
	completedButton.AddClevertapTrackingItem(tracksItem)

	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button: completedButton,
		},
	}

	timeObj := time.Unix(planStartDate, 0)
	timeString, _ := sushi.NewTextSnippet(timeObj.Format("02 Jan 2006"))
	timeString.SetColor(colorGrey900)
	timeString.SetFont(fontSemi300)

	expandedEditButton, _ := sushi.NewButton(sushi.ButtontypeText)
	expandedEditButton.SetText("Change")

	placeholder := &productModels.AcademyRecommendedPlanPlaceholder{
		Title:      timeString,
		EditButton: expandedEditButton,
	}

	expandedState := &productModels.AcademyRecommendedPlanInputExpandedState{
		Title:                expandedTitle,
		Icon:                 tooltipIcon,
		PlaceholderContainer: placeholder,
		States:               inputFieldStates,
		BottomButtonStates:   bottomButtonStates,
		DurationInDays:       int32(14),
		StartDate:            planStartDate,
		ProductStartDate:     productStartDate,
	}
	if util.IsIOS(ctx) && cityId == common.CITY_ID_DELHI_NCR && false {
		expandedState.DurationInDays = int32(25) //change this
	}

	collapsedHeaderTitle, _ := sushi.NewTextSnippet("Plan start date")
	textColor, _ := sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	collapsedHeaderTitle.SetColor(textColor)
	collapsedHeaderTitle.SetFont(fontMed100)

	collapsedHeaderSubtitle, _ := sushi.NewTextSnippet(timeObj.Format("02 Jan, 2006"))
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	collapsedHeaderSubtitle.SetColor(colorGrey900)
	collapsedHeaderSubtitle.SetFont(fontMed400)

	collapsedEditButton, _ := sushi.NewButton(sushi.ButtontypeText)
	collapsedEditButton.SetText("Edit")
	tapEname := &sushi.EnameData{
		Ename: "academy_edit_plan_start_date_tap",
	}
	editButtonEvents := sushi.NewClevertapEvents()
	editButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, editButtonEvents)
	collapsedEditButton.AddClevertapTrackingItem(trackItem)

	closedState := &productModels.AcademyRecommendedPlanInputClosedState{
		Title:      collapsedHeaderTitle,
		Subtitle:   collapsedHeaderSubtitle,
		EditButton: collapsedEditButton,
	}

	startDateInputItem := &productModels.AcademyRecommendedPlanItem{
		Type: "start_date",
		StartDate: &productModels.AcademyRecommendedPlanInputItem{
			ClosedState:   closedState,
			ExpandedState: expandedState,
		},
	}

	a.AddInputItem(startDateInputItem)
}

func (a *SummerCampCourseTemplate) GetFacilitySnippetForSummercampSlot(
	ctx context.Context,
	summercampSlotId int32,
	summercampSlot *SummerCampSlot,
) (*sushi.V2ImageTextSnippetType31SnippetItem, error) {

	facilityClient := util.GetFacilitySportClient()
	fsDataReq := &facilitySportPB.FacilitySportsByFsIdRequest{
		FsIds: []int32{summercampSlot.FsId},
	}
	fsDataRes, err := facilityClient.GetFacilitySportsDetailsByFsID(ctx, fsDataReq)
	if err != nil {
		return nil, err
	}
	if len(fsDataRes.FacilitySports) == 0 {
		return nil, fmt.Errorf("Unable to fetch fs details")
	}

	slotIds := []int32{summercampSlot.SlotId}

	slotDataReq := &facilitySportPB.BatchSlotGetRequest{
		SlotIds: slotIds,
	}
	slotDataRes, err := facilityClient.BatchSlotGet(ctx, slotDataReq)
	if err != nil {
		return nil, err
	}
	if len(slotDataRes.SlotsMap) == 0 {
		return nil, fmt.Errorf("Unable to fetch slots details")
	}

	fsData := fsDataRes.FacilitySports[0]
	facility := fsData.Facility[0]

	var timings []string
	for _, v := range slotIds {
		if v == 12 {
			timings = append(timings, "4 - 5 PM")
		} else if v == 13 {
			timings = append(timings, "5 - 6 PM")
		} else {
			timings = append(timings, slotDataRes.SlotsMap[v].Timing)
		}
	}
	timingStr := strings.Join(timings, " & ")

	title, _ := sushi.NewTextSnippet(facility.DisplayName)
	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetFont(fontSemi400)
	title.SetColor(colorBlack500)

	subtitle, _ := sushi.NewTextSnippet(facility.ShortAddress)
	colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	subtitle.SetColor(colorGrey500)
	subtitle.SetFont(fontMed200)

	ratingTitle, _ := sushi.NewTextSnippet(facility.Rating)
	colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	prefixIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
	ratingTitle.SetColor(colorWhite900)
	ratingTitle.SetFont(fontMed100)
	ratingTitle.SetPrefixIcon(prefixIcon)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)

	ratingObj := &sushi.RatingSnippetBlockItem{
		Title:   ratingTitle,
		BgColor: bgColor,
	}

	image, _ := sushi.NewImage(facility.DisplayPicture)
	image.SetHeight(64)
	image.SetWidth(64)
	image.SetAspectRatio(1)
	image.SetType(sushi.ImageTypeRounded)

	bottomTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s", timingStr))
	bottomTitle.SetFont(fontMed200)
	bottomTitle.SetColor(colorBlack500)
	bottomContainer := &sushi.BottomContainerSnippetItem{
		Title: bottomTitle,
	}

	snippet := &sushi.V2ImageTextSnippetType31SnippetItem{
		Id:              summercampSlotId,
		Title:           title,
		Subtitle:        subtitle,
		Rating:          ratingObj,
		Image:           image,
		BottomContainer: bottomContainer,
	}
	return snippet, nil
}

func (a *SummerCampCourseTemplate) GetStartDateTooltipIcon(ctx context.Context) *sushi.Icon {
	title, _ := sushi.NewTextSnippet("You can choose plan start date upto 2 weeks from today.")
	colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fontMed300, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetColor(colorWhite500)
	title.SetFont(fontMed300)

	subtitle, _ := sushi.NewTextSnippet("OK")
	subtitle.SetColor(colorWhite500)
	subtitle.SetFont(fontMed300)

	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

	showTooltip := &sushi.ShowTooltip{
		Title:    title,
		Subtitle: subtitle,
		BgColor:  colorBlack500,
	}
	clickAction := sushi.GetClickAction()
	clickAction.SetShowTooltip(showTooltip)

	icon, _ := sushi.NewIcon(sushi.InfoIcon, nil)
	icon.SetClickAction(clickAction)

	return icon
}

func (a *SummerCampCourseTemplate) AddCenterTimeInput(ctx context.Context) {
	expandedHeaderTitle, _ := sushi.NewTextSnippet("Select your center")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontSemi300, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	expandedHeaderTitle.SetColor(colorGrey900)
	expandedHeaderTitle.SetFont(fontSemi300)

	placeholderTitle, _ := sushi.NewTextSnippet("Select center")
	colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	placeholderTitle.SetColor(colorRed500)
	placeholderTitle.SetFont(fontMed400)
	icon, _ := sushi.NewIcon(sushi.RightIcon, colorRed500)

	planStartDate := time.Now().Unix()
	productStartDate := time.Now().Unix()
	loc, _ := time.LoadLocation("Asia/Kolkata")
	sellPlanStartDate := time.Date(2024, time.Month(4), 1, 0, 0, 0, 0, loc).Unix()

	cityId := util.GetCityIDFromContext(ctx)
	if cityId == common.HYDERABAD_CITY_ID {
		sellPlanStartDate = time.Date(2024, time.Month(4), 20, 0, 0, 0, 0, loc).Unix()
	}
	if cityId == common.CITY_ID_DELHI_NCR {
		sellPlanStartDate = time.Date(2024, time.Month(5), 20, 0, 0, 0, 0, loc).Unix()
	}
	if planStartDate < sellPlanStartDate {
		planStartDate = sellPlanStartDate
	}
	if a.PostbackData != nil && a.PostbackData.PlanStartDate > 0 {
		planStartDate = a.PostbackData.PlanStartDate
	}
	productStartDate = planStartDate
	payload := map[string]interface{}{
		"product_category_id":           a.PageRequest.ProductCategoryId,
		"summercamp_product_mapping_id": a.PageRequest.SummercampProductMappingId,
		"plan_start_date":               planStartDate,
		"product_start_date":            productStartDate,
		"user_id":                       a.PageRequest.UserId,
		"product_id":                    a.PageData.RecommendedPlanData.SportProducts[0].Products[0].ProductId,
		"course_category_postback":      a.CourseCategoryPostback,
	}

	params, _ := json.Marshal(payload)

	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL:            util.GetAcademyPurchaseFacilitiesDeeplink(),
		PostbackParams: string(params),
	}
	clickAction.SetDeeplink(deeplink)

	container := &productModels.AcademyRecommendedPlanPlaceholder{
		Title:       placeholderTitle,
		Icon:        icon,
		ClickAction: clickAction,
	}

	expandedState := &productModels.AcademyRecommendedPlanInputExpandedState{
		Title:                expandedHeaderTitle,
		PlaceholderContainer: container,
	}

	collapsedHeaderTitle, _ := sushi.NewTextSnippet("Center & slot")
	textColor, _ := sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	collapsedHeaderTitle.SetColor(textColor)
	collapsedHeaderTitle.SetFont(fontMed100)

	collapsedEditButton, _ := sushi.NewButton(sushi.ButtontypeText)
	collapsedEditButton.SetText("Edit")
	collapsedEditButton.SetClickAction(clickAction)

	var snippet *sushi.V2ImageTextSnippetType31SnippetItem
	if a.PostbackData != nil && len(a.PostbackData.SummercampSlots) > 0 {
		for summercampSlotId, slot := range a.PostbackData.SummercampSlots {
			snippetX, err := a.GetFacilitySnippetForSummercampSlot(ctx, int32(summercampSlotId), slot)
			if err != nil {
				log.Printf("Summercamp RecommendedPlan, AddCenterTimeInput: error in fetching facility snippet for summercampSlotId: %d, err: %v", summercampSlotId, err)
				return
			}
			snippet = snippetX
			break
		}
	}

	closedState := &productModels.AcademyRecommendedPlanInputClosedState{
		Title:      collapsedHeaderTitle,
		EditButton: collapsedEditButton,
		Snippet:    snippet,
	}

	centerSlotInputItem := &productModels.AcademyRecommendedPlanItem{
		Type: "center_time",
		CenterTime: &productModels.AcademyRecommendedPlanInputItem{
			ExpandedState: expandedState,
			ClosedState:   closedState,
		},
	}

	a.AddInputItem(centerSlotInputItem)
}
func (a *SummerCampCourseTemplate) SetItemsConfig(ctx context.Context) {
	iconColor, _ := sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)

	completeIcon, _ := sushi.NewIcon(sushi.TickMarkIconV2, iconColor)
	incompleteIcon, _ := sushi.NewIcon(sushi.EmptyCircleIcon, iconColor)

	itemConfig := &productModels.AcademyRecommendedPlanItemsConfig{
		IncompleteIcon:  incompleteIcon,
		CompleteIcon:    completeIcon,
		DashedLineColor: iconColor,
	}
	a.ItemsConfig = itemConfig
}
