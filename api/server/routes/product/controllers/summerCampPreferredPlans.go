package productController

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"encoding/json"
	"sort"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	// featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
)

type SummerCampPlansTemplate struct {
	Header                    *sushi.Header                        `json:"header,omitempty"`
	Results                   *[]sushi.CustomTextSnippetTypeLayout `json:"results,omitempty"`
	Footer                    *sushi.FooterSnippetType2Layout      `json:"footer,omitempty"`
	SportProducts             []*productPB.SummerCampSportProduct  `json:"-"`
	UserId                    int32                                `json:"-"`
	ComboAvailable            bool                                 `json:"-"`
	IsRecommendedCourseOpened bool                                 `json:"-"`
	SelectedUsers 			  []productPB.PurchaseSelectedUser    `json:"-"`
}

func SummerCampPreferredPlansC(c *gin.Context) {
	productService := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetAcademyPreferredPlan)

	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("You need to login first!"))
		return
	}

	var selectedUsers []productPB.PurchaseSelectedUser
	for _, user := range json.SelectedUsers {
		selectedUser := productPB.PurchaseSelectedUser{
			UserId:        user.UserId,
			PlanStartDate: user.PlanStartDate,
			ProductId:     user.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range user.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				SummercampSlotId: facilitySlot.SummercampSlotId,
			})
		}
		if len(facilitySlots) > 0 {
			selectedUser.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not present for selected user"))
			return
		}
		selectedUsers = append(selectedUsers, selectedUser)
	}
	if len(json.SelectedUsers) > 0 {
		log.Println("SummerCampPreferredPlansC: test payload",selectedUsers)
		log.Println("SummerCampPreferredPlansC: json test payload",json)
	}
	log.Println("SummerCampPreferredPlansC: preferred plan payload",json)
	reqData := &productPB.GetPreferredPlanRequest{
		UserId:            json.UserId,
		ProductCategoryId: json.ProductCategoryId,
	}

	response, err := productService.GetSummerCampPlansForUser(ctx, reqData)
	if err != nil {
		log.Printf("Error in controller SummerCampPreferredPlansC while getting plans for user %d, Error: %v", json.UserId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.FAILED {
		log.Printf("Error in controller SummerCampPreferredPlansC while getting plans for user %d", json.UserId)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.BAD_REQUEST {
		log.Printf("Error in controller SummerCampPreferredPlansC while getting plans for user %d", json.UserId)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure(apiCommon.NOT_AUTHORIZED))
		return
	}

	templateResponse := SummerCampPlansTemplate{
		SportProducts:             response.SportProducts,
		UserId:                    json.UserId,
		IsRecommendedCourseOpened: json.IsRecommendedCourseOpened,
		SelectedUsers:			   selectedUsers,
	}
	templateResponse.SetHeader()
	templateResponse.SetResultSection(ctx)
	templateResponse.SetFooter()
	c.JSON(http.StatusOK, templateResponse)
}

func (p *SummerCampPlansTemplate) SetHeader() {

	title, _ := sushi.NewTextSnippet("Select preferred plan")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(font)
	title.SetColor(color)

	header := &sushi.Header{
		Title: title,
	}
	p.Header = header
}

func (p *SummerCampPlansTemplate) SetResultSection(ctx context.Context) {
	var items []sushi.CustomTextSnippetTypeLayout
	var showSwimmingInfo bool
	for _, sportProductsList := range p.SportProducts {
		if len(sportProductsList.Products) == 0 {
			continue
		}
		layoutConfig := &sushi.LayoutConfig{ 
			SnippetType: sushi.TextSnippetType1,
		}
		title, _ := sushi.NewTextSnippet(sportProductsList.SportName)
		fontSemi600, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		title.SetFont(fontSemi600)
		title.SetColor(colorBlack500)

		snippet := &sushi.TextSnippetType1Snippet{
			Title: title,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:            layoutConfig,
			TextSnippetType1Snippet: snippet,
		}

		items = append(items, *item)
		sort.SliceStable(sportProductsList.Products, func(i, j int) bool {
			return len(sportProductsList.Products[i].DaysOfWeek) < len(sportProductsList.Products[j].DaysOfWeek)
		})
		sort.SliceStable(sportProductsList.Products, func(i, j int) bool {
			return sportProductsList.Products[i].Price > sportProductsList.Products[j].Price
		})
		for _, product := range sportProductsList.Products {
			productLayoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.FitsoTextSnippetType8,
			}

			titleText := fmt.Sprintf("%d %s", product.Duration, product.DurationUnit)
			title, _ := sushi.NewTextSnippet(titleText)
			fontSemi600, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
			colorBlack900, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
			title.SetFont(fontSemi600)
			title.SetColor(colorBlack900)

			bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

			daysInt, daysStr := util.GetProductDaysOfWeekSummercamp(product.DaysOfWeek)
			subtitle2Text := fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|>}", daysStr)
			if len(daysInt) == 2 {
				subtitle2Text = fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|| Weekends>}", daysStr)
			} else if len(daysInt) == 0 || len(daysInt) == 1 || len(daysInt) > 5 {
				subtitle2Text = fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|}", daysStr)
			} else {
				subtitle2Text = fmt.Sprintf("{orange-500|<regular-100|%s >}{grey-900|<medium-100|| Weekdays>}", daysStr)
			}
			subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
			fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			subtitle2.SetFont(fontMed100)
			subtitle2.SetColor(colorGrey900)
			subtitle2.SetIsMarkdown(1)

			subtitle1Text := fmt.Sprintf("%d hr/ session", product.SessionDuration/60)
			subtitle1, _ := sushi.NewTextSnippet(subtitle1Text)
			fontRegular100, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
			colorGrey600, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			subtitle1.SetFont(fontRegular100)
			subtitle1.SetColor(colorGrey600)

			rightTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", product.RetailPrice))
			fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
			rightTitle.SetFont(fontMed400)
			rightTitle.SetColor(colorBlack900)

			rightSubtitleText := fmt.Sprintf("₹%.f", product.Price)
			rightSubtitle, _ := sushi.NewTextSnippet(rightSubtitleText)
			fontRegular200, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
			rightSubtitle.SetFont(fontRegular200)
			rightSubtitle.SetColor(colorBlack900)
			rightSubtitle.Strikethrough = true

			image := &sushi.Image{
				URL:         sportProductsList.SportIcon,
				AspectRatio: 1,
				Type:        sushi.ImageTypeCircle,
				Height:      48,
			}

			var sportBgColor *sushi.Color
			if val, ok := apiCommon.SportIdBackgroundColorMap[product.SportId]; ok {
				sportBgColor, _ = sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
			}

			if len(sportBgColor.Type) > 0 {
				image.BgColor = sportBgColor
			}

			snippet := &sushi.FitsoTextSnippetType8Snippet{
				Id:            product.SummercampProductMappingId,
				Title:         title,
				BgColor:       bgColor,
				BorderColor:   borderColor,
				// Subtitle1:     subtitle1,
				RightTitle:    rightTitle,
				RightSubtitle: rightSubtitle,
				IsSelectable:  true,
				Image:         image,
			}
			if len(product.DaysOfWeek) > 0 {
				snippet.Subtitle2 = subtitle2
			}

			if len(product.ProductOfferings) > 0 {
				subtitle3, _ := sushi.NewTextSnippet(strings.Join(product.ProductOfferings[:], "\n"))
				fontMedium100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
				colorGrey700, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
				subtitle3.SetFont(fontMedium100)
				subtitle3.SetColor(colorGrey700)

				snippet.Subtitle1 = subtitle3
			}

			buttonClickAction := sushi.GetClickAction()

			if !p.IsRecommendedCourseOpened {
				userData := &sushi.UserData{
					UserId:                     p.UserId,
					ProductId:                  product.ProductId,
					ProductCategoryId:          apiCommon.SummerCampCategoryID,
					SummercampProductMappingId: product.SummercampProductMappingId,
				}
				if len(p.SelectedUsers) > 0 {
					params, _ := json.Marshal(p.SelectedUsers)
					userData.CourseCategoryPostback = string(params)
				}
				selectedCourse := &sushi.OpenAcademyRecommendedCourse{
					UserData: userData,
				}
				dismissPage := &sushi.DismissPage{
					Type:                         sushi.ClickActionOpenAcademyRecommendedCourse,
					OpenAcademyRecommendedCourse: selectedCourse,
				}
				buttonClickAction.SetPageDismiss(dismissPage)
			} else {
				refreshPageAction := &sushi.RefreshDetailsPage{
					UserId:                     p.UserId,
					ProductId:                  product.ProductId,
					ProductCategoryId:          apiCommon.SummerCampCategoryID,
					SummercampProductMappingId: product.SummercampProductMappingId,
				}
				if len(p.SelectedUsers) > 0 {
					params, _ := json.Marshal(p.SelectedUsers)
					refreshPageAction.CourseCategoryPostback = string(params)
				}
				dismissPage := &sushi.DismissPage{
					Type:               sushi.ClickActionRefreshDetailsPage,
					RefreshDetailsPage: refreshPageAction,
				}
				buttonClickAction.SetPageDismiss(dismissPage)
			}

			button := &sushi.Button{
				Type:        sushi.ButtonTypeSolid,
				Text:        "Proceed with selected plan",
				ID:          "bottom_button_id1",
				ClickAction: buttonClickAction,
			}
			change_bottom_button := &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id1",
				Button:   button,
			}
			clickAction := &sushi.ClickAction{
				Type:               sushi.ClickActionChangeBottomButton,
				ChangeBottomButton: change_bottom_button,
			}
			snippet.ClickAction = clickAction

			productItem := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:          productLayoutConfig,
				FitsoTextSnippetType8: snippet,
			}
			items = append(items, *productItem)
		}

		if sportProductsList.SportId == apiCommon.SWIMMING_SPORT_ID {
			showSwimmingInfo = true
		}
	}
	if showSwimmingInfo {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.TextSnippetType1,
		}
		title, _ := sushi.NewTextSnippet("Note: Swimming pool closes once a weekday for maintenance")
		fontRegular200, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		title.SetFont(fontRegular200)
		title.SetColor(colorRed500)

		snippet := &sushi.TextSnippetType1Snippet{
			Title: title,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:            layoutConfig,
			TextSnippetType1Snippet: snippet,
		}

		items = append(items, *item)
	}
	p.Results = &items
}

func (p *SummerCampPlansTemplate) SetFooter() {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItemText string
	buttonItemText = "Select a plan to continue"
	buttonItemFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItemBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:             "solid",
		Text:             buttonItemText,
		Font:             buttonItemFont,
		Id:               "bottom_button_id1",
		BgColor:          buttonItemBgColor,
		IsActionDisabled: 1,
	}
	items = append(items, buttonItem)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	p.Footer = footer
}
