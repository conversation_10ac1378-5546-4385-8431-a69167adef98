package productController

import (
	"context"
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func (cc *CalculateCartData) SetAcademyFullCartPageHeaderSection() {

	text := fmt.Sprintf("Academy")
	title, _ := sushi.NewTextSnippet(text)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	cc.Header = &productModel.CalculateCartHeaderSection{
		Title: title,
	}
}

func (cc *CalculateCartData) SetAcademyFullCartPageProductSection(ctx context.Context, data *productPB.CalculateCartResponse) {

	for i, detail := range data.ProductDetails {

		productLayoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.FitsoTextSnippetType11,
			LayoutType:  sushi.LayoutTypeGrid,
		}

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		if i == len(data.ProductDetails)-1 {
			separator, _ = sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
		}
		snippetConfig := &sushi.SnippetConfig{
			BottomSeparator: separator,
		}

		title, _ := sushi.NewTextSnippet(detail.ProductName)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		title.SetFont(font)
		title.SetColor(color)
		subTitle := ""
		if isProductCategorySummercamp(cc.productCategoryId) {
			subTitle = fmt.Sprintf("%s | %s", detail.Name, detail.Duration)
			_, daysStr := util.GetProductDaysOfWeekSummercamp(detail.DaysOfWeek)
			if len(detail.DaysOfWeek) > 0 {
				subTitle = fmt.Sprintf("%s | %s | %s", detail.Name, detail.Duration, daysStr)
			}
		} else {
			subTitle = fmt.Sprintf("%s | %s | %s", detail.Name, detail.Duration, detail.ProductFrequency)
		}
		subtitle, _ := sushi.NewTextSnippet(subTitle)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle.SetFont(font)
		subtitle.SetColor(color)

		productValue := fmt.Sprintf("₹%.0f", detail.TotalAmountPerProduct)
		rightSubtitle, _ := sushi.NewTextSnippet(productValue)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		rightSubtitle.SetFont(font)
		rightSubtitle.SetColor(color)

		productPrice := fmt.Sprintf("~~₹%.0f~~", detail.Price)
		rightSubtitle2, _ := sushi.NewTextSnippet(productPrice)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		rightSubtitle2.SetFont(font)
		rightSubtitle2.SetColor(color)
		rightSubtitle2.SetIsMarkdown(int32(1))

		productSnippet := &sushi.FitsoTextSnippetType11Snippet{
			Title:          title,
			Subtitle:       subtitle,
			RightSubtitle:  rightSubtitle,
			RightSubtitle2: rightSubtitle2,
		}

		productSection := &productModel.ProductResult{
			LayoutConfig:           productLayoutConfig,
			FitsoTextSnippetType11: productSnippet,
		}

		if detail.VoucherResponse == nil {
			productSection.SnippetConfig = snippetConfig
			cc.Results = append(cc.Results, productSection)
		} else {
			voucherResponseSnippet := getVoucherResponseSnippet(ctx, detail.VoucherResponse)

			voucherLayoutConfig := &sushi.LayoutConfig{
				SnippetType: sushi.FitsoTextSnippetType4,
				LayoutType:  sushi.LayoutTypeGrid,
			}
			voucherSection := &productModel.ProductResult{
				LayoutConfig:          voucherLayoutConfig,
				SnippetConfig:         snippetConfig,
				FitsoTextSnippetType4: voucherResponseSnippet,
			}
			cc.Results = append(cc.Results, productSection)
			cc.Results = append(cc.Results, voucherSection)
		}
	}
}

func CalculateAcademyCartC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.CalculateAcademyCartRequest)

	var parentUser *productPB.CartUser
	if json.ParentUser != nil {
		user := json.ParentUser
		parentUser = &productPB.CartUser{
			UserId:        user.UserId,
			Name:          user.Name,
			PlanStartDate: user.PlanStartDate,
			ProductId:     user.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range user.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				AcademySlotId: facilitySlot.AcademySlotId,
			})
		}
		if len(facilitySlots) > 0 {
			parentUser.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not selected for a logged in user"))
			return
		}
	}

	var childUsersToSubscribe []*productPB.CartUser
	for _, childUser := range json.ChildUsers {
		childUserToSubscribe := &productPB.CartUser{
			UserId:        childUser.UserId,
			Name:          childUser.Name,
			PlanStartDate: childUser.PlanStartDate,
			ProductId:     childUser.ProductId,
		}
		var facilitySlots []*productPB.FacilitySlot
		for _, facilitySlot := range childUser.FacilitySlots {
			facilitySlots = append(facilitySlots, &productPB.FacilitySlot{
				AcademySlotId: facilitySlot.AcademySlotId,
			})
		}
		if len(facilitySlots) > 0 {
			childUserToSubscribe.FacilitySlots = facilitySlots
		} else {
			c.JSON(http.StatusBadRequest, models.StatusFailure("Facility and slot not selected for a child user"))
			return
		}
		childUsersToSubscribe = append(childUsersToSubscribe, childUserToSubscribe)
	}

	productClient := util.GetProductClient()
	response, err := productClient.CalculateAcademyCart(ctx, &productPB.CalculateCartRequest{
		ChildUsers:   childUsersToSubscribe,
		ReferralCode: json.ReferralCode,
		PromoCode:    json.PromoCode,
		ParentUser:   parentUser,
	})

	if err != nil {
		log.Printf("Error in calculating academy cart: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure("Something went wrong!"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.JSON(http.StatusUnauthorized, models.StatusUnauthorized("You are not authorized!"))
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED && !response.ShowPopup {
		c.JSON(http.StatusOK, response.Status)
		return
	}

	cartPage := CalculateCartData{
		promoCode:         json.PromoCode,
		productCategoryId: common.AcademyCategoryID,
	}
	if response.ShowPopup {
		cartPage.SetCustomAlertCapacityIssueSection(ctx)
		sushiResponseSections := &productModel.CalculateCartResponseV2{
			ActionList:        cartPage.ActionList,
		}
		c.JSON(
			http.StatusOK,
			sushiResponseSections,
		)
		return
	}
	var cartUsers []*structs.CommonCartUser
	for _, user := range response.FinalCartUsers {
		cartUsers = append(cartUsers, &structs.CommonCartUser{
			UserId:           user.UserId,
			Name:             user.Name,
			ProductName:      user.ProductName,
			ProductDuration:  user.ProductDuration,
			ProductFrequency: user.ProductFrequency,
			FormattedAmount:  user.FormattedAmount,
		})
	}

	autoApplyPromoCode := cartPage.promoCode == ""

	cartPage.setCalculateCartData(response)

	if featuresupport.SupportsFullPageCart(ctx) {

		eligibleVoucherReq := &productPB.GetEligibleVouchersReq{
			ProductDetails: response.ProductDetails,
			UsersCount:     int32(len(response.FinalCartUsers)),
		}
		eligibleVoucherRes, err := productClient.GetEligibleVouchers(ctx, eligibleVoucherReq)
		if err != nil {
			log.Printf("error in getting eligible vouchers, %v", err)
			c.JSON(
				http.StatusInternalServerError,
				models.StatusFailure("Something went wrong!"),
			)
			return
		}

		cartPage.SetAcademyFullCartPageHeaderSection()
		cartPage.SetFullCartPageTopContainer(ctx, response.FinalCartUsers)
		cartPage.SetAcademyFullCartPageProductSection(ctx, response)
		cartPage.SetApplyPromoCodeSection(ctx, response.FormattedDiscount, response.DiscountAmount, response.VoucherValid, response.VoucherError, autoApplyPromoCode, eligibleVoucherRes)
		cartPage.SetFullCartPageCartValueSection(ctx, response.FormattedTotalCartValue, response.TotalCartValue, response.VoucherValid, response.DiscountAmount)
		cartPage.SetFullCartClevertapTrackingImpression(ctx)
		cartPage.SetFullCartPageFooterSection(ctx, response.FormattedTotalCartValue)

		sushiResponseSections := &productModel.CalculateCartResponseV2{
			Header:     cartPage.Header,
			Results:    cartPage.Results,
			Footer:     cartPage.Footer,
			ActionList: cartPage.ActionList,
		}

		c.JSON(
			http.StatusOK,
			sushiResponseSections,
		)
	} else {
		cartPage.SetPaymentsData(response.CountryId, response.ServiceType, response.TotalCartValue)
		cartPage.SetCartPageHeader(ctx, cartUsers)
		cartPage.setTopContainer()
		cartPage.SetAcademyMembershipData(response)
		cartPage.SetOffersData(ctx, response.FormattedDiscount, response.DiscountAmount, response.VoucherValid, response.VoucherError)
		cartPage.SetBillInfoData(response.FormattedTotalCartValue, response.TotalCartValue, response.VoucherValid, response.DiscountAmount)
		cartPage.SetCartButtonData(ctx, response.FormattedTotalCartValue)
		cartPage.SetClevertapTracking(ctx)
		cartPage.SetPaymentProvider(ctx, response.PaymentProvider)

		sushiResponseSections := &productModel.CalculateCartResultSection{
			PaymentsData:    cartPage.paymentsData,
			HeaderData:      cartPage.headerData,
			MembershipData:  cartPage.membershipData,
			BillInfoData:    cartPage.billInfoData,
			OffersData:      cartPage.offersData,
			CartButtonData:  cartPage.cartButtonData,
			PaymentProvider: cartPage.PaymentProvider,
		}

		c.JSON(
			http.StatusOK,
			&productModel.CalculateCartResponse{
				Status:            "success",
				ResultSections:    sushiResponseSections,
				ClevertapTracking: cartPage.ClevertapTracking,
			},
		)
	}
}

func (cc *CalculateCartData) SetAcademyMembershipData(data *productPB.CalculateCartResponse) {
	membershipItems := []*productModel.MembershipDataItem{}
	for _, detail := range data.ProductDetails {
		membershipDataItem := &productModel.MembershipDataItem{}

		title, _ := sushi.NewTextSnippet(detail.ProductName)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		title.SetColor(titleColor)
		title.SetFont(titleFont)
		membershipDataItem.Title = title

		subTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s | %s | %s", detail.Name, detail.Duration, detail.ProductFrequency))
		subTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		subTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subTitle.SetColor(subTitleColor)
		subTitle.SetFont(subTitleFont)
		membershipDataItem.Subtitle = subTitle

		productValue := fmt.Sprintf("₹%.0f", detail.TotalAmountPerProduct)
		if detail.TotalAmountPerProduct < detail.Price {
			productValue = fmt.Sprintf("{grey-500|<medium-400|~~₹%.0f~~>} ₹%v", detail.Price, detail.TotalAmountPerProduct)
		}

		rightTitle, _ := sushi.NewTextSnippet(productValue)
		rightTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		rightTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		rightTitle.SetColor(rightTitleColor)
		rightTitle.SetFont(rightTitleFont)
		rightTitle.SetIsMarkdown(int32(1))
		membershipDataItem.RightTitle = rightTitle

		if detail.VoucherResponse != nil && detail.VoucherResponse.Status != nil && detail.VoucherResponse.Status.Message != "" {
			message := detail.VoucherResponse.Status.Message
			infoTitle, _ := sushi.NewTextSnippet(message)
			infoTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			infoTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

			if detail.VoucherResponse.DiscountAmount > 0 {
				infoTitleColor, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

				discountAmount := fmt.Sprintf("-₹%v", detail.VoucherResponse.DiscountAmount)
				infoRightTitle, _ := sushi.NewTextSnippet(discountAmount)
				infoRightTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
				infoRightTitle.SetColor(infoTitleColor)
				infoRightTitle.SetFont(infoRightTitleFont)
				membershipDataItem.InfoRightTitle = infoRightTitle
			}
			infoTitle.SetColor(infoTitleColor)
			infoTitle.SetFont(infoTitleFont)
			membershipDataItem.InfoTitle = infoTitle
		}
		membershipItems = append(membershipItems, membershipDataItem)
	}
	cc.membershipData = &productModel.MembershipData{
		Items: membershipItems,
	}
}
