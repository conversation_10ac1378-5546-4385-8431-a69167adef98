package productController

import (
	"context"
	"fmt"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type ReferAndEarnPageData struct {
	CouponCode       string
	MembersPurchased int32
	ExtensionDays    int32
	ReferralLink     string
	ReferralParams   *productPB.ReferralParams
}

type ReferAndEarnPageTemplate struct {
	Header            *sushi.FitsoHeaderSnippetType1Snippet `json:"header,omitempty"`
	Results           *[]sushi.CustomTextSnippetTypeLayout  `json:"results,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                `json:"clever_tap_tracking,omitempty"`
	PageData          ReferAndEarnPageData                  `json:"-"`
}

func ReferAndEarnDetailsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	template := ReferAndEarnPageTemplate{}

	pcl := util.GetProductClient()
	refCodeData := &productPB.ReferralCodeGenerateOrGet{
		UserId: util.GetUserIDFromContext(ctx),
	}
	referralCodeRes, err := pcl.GenerateOrGetReferralCode(ctx, refCodeData)
	if err != nil && referralCodeRes.Status.Status != common.SUCCESS && len(referralCodeRes.Code) == 0 {
		log.Printf("func:ReferAndEarnDetailsC, error in getting referral code, err:%v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	template.PageData.CouponCode = referralCodeRes.Code

	reqData := &productPB.User{
		UserId: util.GetUserIDFromContext(ctx),
	}
	response, err := pcl.GetUserReferralEarnings(ctx, reqData)
	if err != nil {
		log.Printf("func:ReferAndEarnDetailsC, error in getting referral earnings, err:%v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	template.PageData.MembersPurchased = response.MembersPurchased
	template.PageData.ExtensionDays = response.ExtensionDays

	resp, err := pcl.GenerateOneLinkForReferral(ctx, reqData)
	if err != nil {
		log.Printf("func:ReferAndEarnDetailsC, error in getting referral link, err:%v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	template.PageData.ReferralLink = resp.ReferralLink

	referralParams, err := pcl.GetReferralParams(ctx, &productPB.Empty{})
	if err != nil {
		log.Printf("func:ReferAndEarnDetailsC, error in getting referral params, err:%v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	template.PageData.ReferralParams = referralParams.ReferralParams

	template.setHeaderSection(ctx)
	template.setResultSection(ctx)
	template.SetPageTracking(ctx)

	c.JSON(http.StatusOK, template)
}

func (r *ReferAndEarnPageTemplate) setHeaderSection(ctx context.Context) {

	title, _ := sushi.NewTextSnippet("Refer & Earn")
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetFont(titleFont)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)

	header := &sushi.FitsoHeaderSnippetType1Snippet{
		Title:   title,
		BgColor: bgColor,
	}
	r.Header = header
}

func (r *ReferAndEarnPageTemplate) setResultSection(ctx context.Context) {
	referralRewardInfo := r.getReferralRewardInfo(ctx)
	userReferralCodeInfo := r.getUserReferralCodeDetails(ctx)
	workingDetailsOfReferral := r.getWorkingDetailsOfReferral(ctx)
	earningInfo := r.getEarningsSection(ctx)
	tncInfo := r.getTncOfReferral(ctx)
	items := []sushi.CustomTextSnippetTypeLayout{
		*referralRewardInfo,
		*userReferralCodeInfo,
		*workingDetailsOfReferral,
		*earningInfo,
		*tncInfo,
	}
	r.Results = &items
}

func (r *ReferAndEarnPageTemplate) getReferralRewardInfo(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {

	layOutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType18,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	referrer_reward := r.PageData.ReferralParams.ReferralDetails["referral_reward_value"]
	referee_discount := r.PageData.ReferralParams.ReferralDetails["discount_value"]
	discount_type := r.PageData.ReferralParams.ReferralDetails["discount_type"]
	if discount_type == "1" {
		referee_discount = "₹" + referee_discount
	} else if discount_type == "2" {
		referee_discount += "%"
	}

	titleText := fmt.Sprintf("You’ll get <semibold-300|Free %s days>\n<semibold-300|+>\nYour friend will get\n<semibold-300|%s off>", referrer_reward, referee_discount)

	title, _ := sushi.NewTextSnippet(titleText)
	title.SetAlignment(sushi.TextAlignmentCenter)
	titleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	title.SetFont(titleFont)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)
	title.SetIsMarkdown(1)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint50)

	image, _ := sushi.NewImage(util.GetCDNLink("uploads/<EMAIL>"))
	image.SetAspectRatio(1.2)
	image.SetHeight(142)

	fitsoImageTextSnippetType18Item := &sushi.FitsoImageTextSnippetType18SnippetItem{
		Title:   title,
		Image:   image,
		BgColor: bgColor,
	}

	fitsoImageTextSnippetType18 := &sushi.FitsoImageTextSnippetType18Snippet{
		Items: []*sushi.FitsoImageTextSnippetType18SnippetItem{fitsoImageTextSnippetType18Item},
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:                layOutConfig,
		FitsoImageTextSnippetType18: fitsoImageTextSnippetType18,
	}
	return item

}

func (r *ReferAndEarnPageTemplate) getUserReferralCodeDetails(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {

	referee_discount := r.PageData.ReferralParams.ReferralDetails["discount_value"]
	discount_type := r.PageData.ReferralParams.ReferralDetails["discount_type"]
	if discount_type == "1" {
		referee_discount = "₹" + referee_discount
	} else if discount_type == "2" {
		referee_discount += "%"
	}

	layOutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType9,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	fitsoTextSnippetType9Title, _ := sushi.NewTextSnippet(r.PageData.CouponCode)
	fitsoTextSnippetType9TitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	fitsoTextSnippetType9Title.SetFont(fitsoTextSnippetType9TitleFont)
	fitsoTextSnippetType9TitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fitsoTextSnippetType9Title.SetColor(fitsoTextSnippetType9TitleColor)

	fitsoTextSnippetType9RightButton, _ := sushi.NewButton(sushi.ButtontypeText)
	fitsoTextSnippetType9RightButton.SetText("Copy code")
	fitsoTextSnippetType9RightButtonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	fitsoTextSnippetType9RightButton.SetFont(fitsoTextSnippetType9RightButtonFont)
	fitsoTextSnippetType9RightButtonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	fitsoTextSnippetType9RightButton.SetColor(fitsoTextSnippetType9RightButtonColor)
	fitsoTextSnippetType9RightButtonIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	fitsoTextSnippetType9RightButtonIcon, _ := sushi.NewIcon(sushi.CopyIconCode, fitsoTextSnippetType9RightButtonIconColor)
	fitsoTextSnippetType9RightButton.SetPrefixIcon(fitsoTextSnippetType9RightButtonIcon)
	fitsoTextSnippetType9RightButtonCa := sushi.GetClickAction()
	clipboardContent := &sushi.CopyToClipboard{
		Text: r.PageData.CouponCode,
	}
	fitsoTextSnippetType9RightButtonCa.SetCopyToClipboard(clipboardContent)
	fitsoTextSnippetType9RightButton.SetClickAction(fitsoTextSnippetType9RightButtonCa)

	tapPayload := r.getClevertapTrackingDefaultPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "copy_referral_code_cta_click",
	}
	tabEvents := sushi.NewClevertapEvents()
	tabEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tabEvents)

	fitsoTextSnippetType9RightButton.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	fitsoTextSnippetType9BottomButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	fitsoTextSnippetType9BottomButton.SetText("Share code")
	fitsoTextSnippetType9BottomButton.SetAlignment(sushi.ButtonAlignmentCenter)
	fitsoTextSnippetType9BottomButtonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	fitsoTextSnippetType9BottomButton.SetFont(fitsoTextSnippetType9BottomButtonFont)
	fitsoTextSnippetType9BottomButtonColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fitsoTextSnippetType9BottomButton.SetColor(fitsoTextSnippetType9BottomButtonColor)
	fitsoTextSnippetType9BottomButton.SetSize(sushi.ButtonSizeLarge)
	fitsoTextSnippetType9BottomButtonIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fitsoTextSnippetType9BottomButtonIcon, _ := sushi.NewIcon(sushi.ShareIcon, fitsoTextSnippetType9BottomButtonIconColor)
	fitsoTextSnippetType9BottomButton.SetPrefixIcon(fitsoTextSnippetType9BottomButtonIcon)
	fitsoTextSnippetType9BottomButtonCa := sushi.GetClickAction()

	shareContentText := "Join me on cult to play sports at a facility near you!\nWith cult ACADEMY, enjoy:\n1. Unlimited Sessions\n2. Premium & Sanitised Facilities\n3. 100% guarantee of a Playing Partner\n4. Guidance by Certified Coaches\n\nUse my code : " + r.PageData.CouponCode + " to get flat " + referee_discount + " off on your first membership purchase.\n\nClick here to download the app"
	shareContent := &sushi.Share{
		Text: shareContentText,
		URL:  r.PageData.ReferralLink,
	}
	fitsoTextSnippetType9BottomButtonCa.SetShare(shareContent)
	fitsoTextSnippetType9BottomButton.SetClickAction(fitsoTextSnippetType9BottomButtonCa)

	shareTapPayload := r.getClevertapTrackingDefaultPayload(ctx)
	shareTapPayload["referral_link"] = r.PageData.ReferralLink
	shareTapEname := &sushi.EnameData{
		Ename: "share_referral_code_cta_click",
	}
	shareTabEvents := sushi.NewClevertapEvents()
	shareTabEvents.SetTap(shareTapEname)
	shareTrackItem := sushi.GetClevertapTrackItem(ctx, shareTapPayload, shareTabEvents)
	fitsoTextSnippetType9BottomButton.ClevertapTracking = []*sushi.ClevertapItem{shareTrackItem}

	fitsoTextSnippetType9BgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fitsoTextSnippetType9BorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	fitsoTextSnippetType9Item := &sushi.FitsoTextSnippetType9SnippetItem{
		Title:        fitsoTextSnippetType9Title,
		RightButton:  fitsoTextSnippetType9RightButton,
		BottomButton: fitsoTextSnippetType9BottomButton,
		BgColor:      fitsoTextSnippetType9BgColor,
		BorderColor:  fitsoTextSnippetType9BorderColor,
		CornerRadius: 12,
	}

	fitsoTextSnippetType9 := &sushi.FitsoTextSnippetType9Snippet{
		Items: []*sushi.FitsoTextSnippetType9SnippetItem{fitsoTextSnippetType9Item},
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:          layOutConfig,
		SnippetConfig:         snippetConfig,
		FitsoTextSnippetType9: fitsoTextSnippetType9,
	}
	return item
}

func (r *ReferAndEarnPageTemplate) getWorkingDetailsOfReferral(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {
	layOutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.AccordionSnippetType4,
		IsExpanded:  true,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	accordionSnippetType4Title, _ := sushi.NewTextSnippet("How it works")
	accordionSnippetType4TitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	accordionSnippetType4Title.SetFont(accordionSnippetType4TitleFont)
	accordionSnippetType4TitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	accordionSnippetType4Title.SetColor(accordionSnippetType4TitleColor)

	accordionSnippetType4ItemLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType36,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	pointers := []string{
		"Share your referral code with your friends and family",
		"If your buddies purchase their first membership with your referral code, they will get the benefit during purchase",
		"You’ll get the above mentioned benefit once your buddy purchases the membership with your referral code",
		"In case your membership expires before the extension reward, the extension reward will be applicable on your next purchase",
	}

	pointerItems := make([]sushi.V2ImageTextSnippetType36SnippetItem, 0)
	for _, pointer := range pointers {

		imagePathStr := "uploads/Ellipse2271617607635.png"

		image, _ := sushi.NewImage(util.GetCDNLink(imagePathStr))
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(6)
		image.SetWidth(6)

		title, _ := sushi.NewTextSnippet(pointer)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
		title.SetColor(color)
		title.SetFont(font)
		title.SetNumberOfLines(-1)

		item := sushi.V2ImageTextSnippetType36SnippetItem{
			Image: image,
			Title: title,
		}
		pointerItems = append(pointerItems, item)
	}

	v2ImageTextSnippetType36 := &sushi.V2ImageTextSnippetType36Snippet{
		Items: &pointerItems,
	}

	accordionSnippetType4Item := &sushi.AccordionSnippetType4SnippetItem{
		LayoutConfig:             accordionSnippetType4ItemLayoutConfig,
		V2ImageTextSnippetType36: v2ImageTextSnippetType36,
	}
	accordionSnippetType4Items := []*sushi.AccordionSnippetType4SnippetItem{accordionSnippetType4Item}

	accordionSnippetType4 := &sushi.AccordionSnippetType4Snippet{
		Title:    accordionSnippetType4Title,
		Items:    accordionSnippetType4Items,
		Expanded: 1,
	}

	if r.PageData.ExtensionDays > 0 {
		accordionSnippetType4.Expanded = 0
		layOutConfig.IsExpanded = false
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:          layOutConfig,
		SnippetConfig:         snippetConfig,
		AccordionSnippetType4: accordionSnippetType4,
	}
	return item
}

func (r *ReferAndEarnPageTemplate) getEarningsSection(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {

	if r.PageData.MembersPurchased > 0 {
		return r.getUserEarningSection(ctx)
	} else {
		return r.getNoEarningsSection(ctx)
	}
}

func (r *ReferAndEarnPageTemplate) getUserEarningSection(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {
	layOutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoAccordionSnippetType2,
	}

	if r.PageData.ExtensionDays > 0 {
		layOutConfig.IsExpanded = true
	}

	fitsoTextSnippetType2SnippetItem2 := &sushi.FitsoTextSnippetType2SnippetItem{}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	fitsoAccordionSnippetType2Title, _ := sushi.NewTextSnippet("Your earnings")
	fitsoAccordionSnippetType2TitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	fitsoAccordionSnippetType2Title.SetFont(fitsoAccordionSnippetType2TitleFont)
	fitsoAccordionSnippetType2TitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fitsoAccordionSnippetType2Title.SetColor(fitsoAccordionSnippetType2TitleColor)

	fitsoAccordionSnippetType2IconColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	fitsoAccordionSnippetType2Icon, _ := sushi.NewIcon(sushi.ChevronDownIcon, fitsoAccordionSnippetType2IconColor)

	fitsoAccordionSnippetType2Items := make([]*sushi.CustomTextSnippetTypeLayout, 0)

	fitsoAccordionSnippetType2ItemLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType2,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
	}

	items := []*sushi.FitsoTextSnippetType2SnippetItem{}

	fitsoTextSnippetType2SnippetItem1Title, _ := sushi.NewTextSnippet(fmt.Sprintf("%d", r.PageData.MembersPurchased))
	fitsoTextSnippetType2SnippetItem1Title.SetAlignment(sushi.TextAlignmentCenter)
	fitsoTextSnippetType2SnippetItem1TitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	fitsoTextSnippetType2SnippetItem1Title.SetFont(fitsoTextSnippetType2SnippetItem1TitleFont)
	fitsoTextSnippetType2SnippetItem1TitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fitsoTextSnippetType2SnippetItem1Title.SetColor(fitsoTextSnippetType2SnippetItem1TitleColor)

	fitsoTextSnippetType2SnippetItem1SubTitle, _ := sushi.NewTextSnippet("Members purchased")
	fitsoTextSnippetType2SnippetItem1SubTitle.SetAlignment(sushi.TextAlignmentCenter)
	fitsoTextSnippetType2SnippetItem1SubTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	fitsoTextSnippetType2SnippetItem1SubTitle.SetFont(fitsoTextSnippetType2SnippetItem1SubTitleFont)
	fitsoTextSnippetType2SnippetItem1SubTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	fitsoTextSnippetType2SnippetItem1SubTitle.SetColor(fitsoTextSnippetType2SnippetItem1SubTitleColor)

	fitsoTextSnippetType2SnippetItem1BgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

	fitsoTextSnippetType2SnippetItem1 := &sushi.FitsoTextSnippetType2SnippetItem{
		Title:        fitsoTextSnippetType2SnippetItem1Title,
		Subtitle:     fitsoTextSnippetType2SnippetItem1SubTitle,
		BgColor:      fitsoTextSnippetType2SnippetItem1BgColor,
		CornerRadius: 4,
	}
	items = append(items, fitsoTextSnippetType2SnippetItem1)

	fitsoTextSnippetType2SnippetItem2Title, _ := sushi.NewTextSnippet(fmt.Sprintf("%d", r.PageData.ExtensionDays))
	fitsoTextSnippetType2SnippetItem2Title.SetAlignment(sushi.TextAlignmentCenter)
	fitsoTextSnippetType2SnippetItem2TitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	fitsoTextSnippetType2SnippetItem2Title.SetFont(fitsoTextSnippetType2SnippetItem2TitleFont)
	fitsoTextSnippetType2SnippetItem2TitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fitsoTextSnippetType2SnippetItem2Title.SetColor(fitsoTextSnippetType2SnippetItem2TitleColor)

	fitsoTextSnippetType2SnippetItem2SubTitle, _ := sushi.NewTextSnippet("Days extended")
	fitsoTextSnippetType2SnippetItem2SubTitle.SetAlignment(sushi.TextAlignmentCenter)
	fitsoTextSnippetType2SnippetItem2SubTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	fitsoTextSnippetType2SnippetItem2SubTitle.SetFont(fitsoTextSnippetType2SnippetItem2SubTitleFont)
	fitsoTextSnippetType2SnippetItem2SubTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	fitsoTextSnippetType2SnippetItem2SubTitle.SetColor(fitsoTextSnippetType2SnippetItem2SubTitleColor)

	fitsoTextSnippetType2SnippetItem2BgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

	if r.PageData.ExtensionDays > 0 {
		fitsoTextSnippetType2SnippetItem2SubTitleIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		fitsoTextSnippetType2SnippetItem2SubTitleIcon, _ := sushi.NewIcon(sushi.RightArrowOutlineIcon, fitsoTextSnippetType2SnippetItem2SubTitleIconColor)
		fitsoTextSnippetType2SnippetItem2SubTitle.SetSuffixIcon(fitsoTextSnippetType2SnippetItem2SubTitleIcon)

		fitsoTextSnippetType2SnippetItem2Ca := sushi.GetClickAction()
		deeplink := &sushi.Deeplink{
			URL: util.GetYourMembershipsDeeplink(common.MasterkeyCategoryID),
		}
		fitsoTextSnippetType2SnippetItem2Ca.SetDeeplink(deeplink)
		fitsoTextSnippetType2SnippetItem2.ClickAction = fitsoTextSnippetType2SnippetItem2Ca

		tapPayload := r.getClevertapTrackingDefaultPayload(ctx)
		tapEname := &sushi.EnameData{
			Ename: "days_extension_click",
		}
		tabEvents := sushi.NewClevertapEvents()
		tabEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tabEvents)
		fitsoTextSnippetType2SnippetItem2.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
	}

	fitsoTextSnippetType2SnippetItem2.Title = fitsoTextSnippetType2SnippetItem2Title
	fitsoTextSnippetType2SnippetItem2.Subtitle = fitsoTextSnippetType2SnippetItem2SubTitle
	fitsoTextSnippetType2SnippetItem2.BgColor = fitsoTextSnippetType2SnippetItem2BgColor
	fitsoTextSnippetType2SnippetItem2.CornerRadius = 4

	items = append(items, fitsoTextSnippetType2SnippetItem2)

	fitsoTextSnippetType2 := &sushi.FitsoTextSnippetType2Snippet{
		Items: items,
	}

	fitsoAccordionSnippetType2Item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:          fitsoAccordionSnippetType2ItemLayout,
		FitsoTextSnippetType2: fitsoTextSnippetType2,
	}

	fitsoAccordionSnippetType2Items = append(fitsoAccordionSnippetType2Items, fitsoAccordionSnippetType2Item)

	fitsoAccordionSnippetType2 := &sushi.FitsoAccordianType2Snippet{
		Title: fitsoAccordionSnippetType2Title,
		Icon:  fitsoAccordionSnippetType2Icon,
		Items: fitsoAccordionSnippetType2Items,
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:               layOutConfig,
		SnippetConfig:              snippetConfig,
		FitsoAccordionSnippetType2: fitsoAccordionSnippetType2,
	}
	return item
}

func (r *ReferAndEarnPageTemplate) getNoEarningsSection(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {
	layOutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.AccordionSnippetType4,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	accordionSnippetType4Title, _ := sushi.NewTextSnippet("Your earnings")
	accordionSnippetType4TitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	accordionSnippetType4Title.SetFont(accordionSnippetType4TitleFont)

	accordionSnippetType4ItemLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType35,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	imageTextSnippetType35Items := make([]sushi.ImageTextSnippetType35SnippetItem, 0)

	imageTextSnippetType35ItemTitle, _ := sushi.NewTextSnippet("You have no earnings yet!\n Refer now to enjoy referral benefits.")
	imageTextSnippetType35ItemTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	imageTextSnippetType35ItemTitle.SetFont(imageTextSnippetType35ItemTitleFont)
	imageTextSnippetType35ItemTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	imageTextSnippetType35ItemTitle.SetColor(imageTextSnippetType35ItemTitleColor)
	imageTextSnippetType35ItemTitle.SetAlignment(sushi.TextAlignmentCenter)

	imageTextSnippetType10Item := &sushi.ImageTextSnippetType35SnippetItem{
		Title: imageTextSnippetType35ItemTitle,
	}

	imageTextSnippetType35Items = append(imageTextSnippetType35Items, *imageTextSnippetType10Item)

	imageTextSnippetType35 := &sushi.ImageTextSnippetType35Snippet{
		Items: &imageTextSnippetType35Items,
	}

	accordionSnippetType4Item := &sushi.AccordionSnippetType4SnippetItem{
		LayoutConfig:           accordionSnippetType4ItemLayoutConfig,
		ImageTextSnippetType35: imageTextSnippetType35,
	}

	accordionSnippetType4Items := []*sushi.AccordionSnippetType4SnippetItem{accordionSnippetType4Item}

	accordionSnippetType4 := &sushi.AccordionSnippetType4Snippet{
		Title: accordionSnippetType4Title,
		Items: accordionSnippetType4Items,
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:          layOutConfig,
		SnippetConfig:         snippetConfig,
		AccordionSnippetType4: accordionSnippetType4,
	}
	return item
}

func (r *ReferAndEarnPageTemplate) getTncOfReferral(ctx context.Context) *sushi.CustomTextSnippetTypeLayout {
	layOutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType10,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	v2ImageTextSnippetType10Items := make([]sushi.V2ImageTextSnippetType10SnippetItem, 0)

	v2ImageTextSnippetType10ItemTitle, _ := sushi.NewTextSnippet("Terms and Conditions")
	v2ImageTextSnippetType10ItemTitleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	v2ImageTextSnippetType10ItemTitle.SetColor(v2ImageTextSnippetType10ItemTitleColor)
	v2ImageTextSnippetType10ItemTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	v2ImageTextSnippetType10ItemTitle.SetFont(v2ImageTextSnippetType10ItemTitleFont)

	v2ImageTextSnippetType10ItemIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	v2ImageTextSnippetType10ItemIcon, _ := sushi.NewIcon(sushi.RightIcon, v2ImageTextSnippetType10ItemIconColor)
	v2ImageTextSnippetType10ItemIcon.SetSize(sushi.IconSize18)

	v2ImageTextSnippetType10ItemCa := sushi.GetClickAction()
	openWebview := &sushi.OpenWebview{
		Title: "Terms & Conditions",
		URL:   "https://www.getfitso.com/terms?tnc_id=term-10",
		InApp: true,
	}
	v2ImageTextSnippetType10ItemCa.SetOpenWebview(openWebview)

	tapPayload := r.getClevertapTrackingDefaultPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "referral_tnc_click",
	}
	tabEvents := sushi.NewClevertapEvents()
	tabEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tabEvents)

	v2ImageTextSnippetType10Item := &sushi.V2ImageTextSnippetType10SnippetItem{
		Title:             v2ImageTextSnippetType10ItemTitle,
		RightIcon:         v2ImageTextSnippetType10ItemIcon,
		ClickAction:       v2ImageTextSnippetType10ItemCa,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	v2ImageTextSnippetType10Items = append(v2ImageTextSnippetType10Items, *v2ImageTextSnippetType10Item)

	v2ImageTextSnippetType10 := &sushi.V2ImageTextSnippetType10Snippet{
		Items: &v2ImageTextSnippetType10Items,
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:             layOutConfig,
		V2ImageTextSnippetType10: v2ImageTextSnippetType10,
	}
	return item
}

func (r *ReferAndEarnPageTemplate) getClevertapTrackingDefaultPayload(ctx context.Context) map[string]interface{} {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["referral_code"] = r.PageData.CouponCode
	payload["city_id"] = util.GetCityIDFromContext(ctx)
	payload["members_purchases"] = r.PageData.MembersPurchased
	payload["extension_days"] = r.PageData.ExtensionDays
	payload["source"] = "refer_and_earn"

	return payload
}

func (r *ReferAndEarnPageTemplate) SetPageTracking(ctx context.Context) {
	payload := r.getClevertapTrackingDefaultPayload(ctx)
	landingEname := &sushi.EnameData{
		Ename: "refer_and_earn_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	r.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}
