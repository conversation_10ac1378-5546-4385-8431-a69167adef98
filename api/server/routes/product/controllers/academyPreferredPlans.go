package productController

import (
	"context"
	"fmt"
	"log"
	"net/http"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"

	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

type AcademyPlansTemplate struct {
	Header                    *sushi.Header                        `json:"header,omitempty"`
	Results                   *[]sushi.CustomTextSnippetTypeLayout `json:"results,omitempty"`
	Footer                    *sushi.FooterSnippetType2Layout      `json:"footer,omitempty"`
	Products                  []*productPB.AcademyProductDetails   `json:"-"`
	UserId                    int32                                `json:"-"`
	ComboAvailable            bool                                 `json:"-"`
	IsRecommendedCourseOpened bool                                 `json:"-"`
}

func AcademyPreferredPlansC(c *gin.Context) {
	productService := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetAcademyPreferredPlan)

	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("You need to login first!"))
		return
	}

	reqData := &productPB.GetPreferredPlanRequest{
		UserId:           json.UserId,
		CourseCategoryId: json.CourseCategoryId,
	}

	response, err := productService.GetPreferredPlanForUser(ctx, reqData)
	if err != nil {
		log.Printf("Error in controller AcademyPreferredPlansC while getting plans for user %d, Error: %v", json.UserId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.FAILED {
		log.Printf("Error in controller AcademyPreferredPlansC while getting plans for user %d", json.UserId)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.BAD_REQUEST {
		log.Printf("Error in controller AcademyPreferredPlansC while getting plans for user %d", json.UserId)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == apiCommon.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusFailure(apiCommon.NOT_AUTHORIZED))
		return
	}

	templateResponse := AcademyPlansTemplate{
		Products:                  response.Products,
		UserId:                    json.UserId,
		IsRecommendedCourseOpened: json.IsRecommendedCourseOpened,
	}
	templateResponse.SetHeader()
	templateResponse.SetResultSection(ctx)
	templateResponse.SetFooter()
	c.JSON(http.StatusOK, templateResponse)
}

func (p *AcademyPlansTemplate) SetHeader() {

	title, _ := sushi.NewTextSnippet("Select preferred plan")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(font)
	title.SetColor(color)

	header := &sushi.Header{
		Title: title,
	}
	p.Header = header
}

func (p *AcademyPlansTemplate) SetResultSection(ctx context.Context) {
	var items []sushi.CustomTextSnippetTypeLayout
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	isNoCostEMISupported := featuresupport.SupportsNoCostEMI(ctx)
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	subtitle, _ := sushi.NewTextSnippet("All the plans are based on assessment done during trial at center. {grey-800|<semibold-200|Sports without assessment can only be purchased for beginner level.>}")
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle.SetFont(font)
	subtitle.SetColor(color)
	subtitle.SetIsMarkdown(1)
	snippet := &sushi.SectionHeaderType1Snippet{
		Subtitle: subtitle,
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:              layoutConfig,
		SectionHeaderType1Snippet: snippet,
	}
	items = append(items, *item)

	if isNoCostEMISupported {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.TextSnippetType1,
		}
		title, _ := sushi.NewTextSnippet("All plans are valid for a period of <semibold-300|3 months>")
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		titleColor, _ := sushi.NewColor(sushi.NEUTRAL_LIGHT_THEME, sushi.ColorTint500)
		title.SetFont(titleFont)
		title.SetColor(titleColor)
		title.SetIsMarkdown(1)
		snippet := &sushi.TextSnippetType1Snippet{
			Title: title,
		}
		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:            layoutConfig,
			TextSnippetType1Snippet: snippet,
		}
		items = append(items, *item)
	}

	for _, data := range p.Products {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.FitsoTextSnippetType8,
		}
		title, _ := sushi.NewTextSnippet(data.CourseName)
		fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		colorBlack900, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
		title.SetFont(fontSemi400)
		title.SetColor(colorBlack900)

		rightTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.f", data.RetailPrice))
		fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		rightTitle.SetFont(fontMed400)
		rightTitle.SetColor(colorBlack900)

		rightSubtitleText := data.Duration
		if !isNoCostEMISupported {
			rightSubtitleText += "\n{grey-900|<medium-100|No Cost EMI>}"
		}
		rightSubtitle, _ := sushi.NewTextSnippet(rightSubtitleText)
		fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		colorGrey600, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		rightSubtitle.SetFont(fontMed100)
		rightSubtitle.SetColor(colorGrey600)
		rightSubtitle.SetIsMarkdown(1)

		bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

		image := &sushi.Image{
			URL:         data.Image,
			AspectRatio: 1,
			Type:        sushi.ImageTypeRounded,
			Height:      48,
		}

		tagTitle, _ := sushi.NewTextSnippet(data.CourseCategory)
		tagTitle.SetColor(bgColor)
		tagColors, _ := sushi.NewColor(sushi.ColorType(data.BgColor), sushi.ColorTint400)
		if isNewColorSupported {
			tagColors, _ = sushi.NewColor(sushi.ColorType(data.BgColor), sushi.ColorTint700)
		}

		tag := &sushi.Tag{
			Title:       tagTitle,
			BgColor:     tagColors,
			BorderColor: tagColors,
		}

		snippet := &sushi.FitsoTextSnippetType8Snippet{
			Title:        title,
			Image:        image,
			IsSelected:   data.IsSelected,
			IsSelectable: true,
			BgColor:      bgColor,
			BorderColor:  borderColor,
			Tag:          tag,
			RightTitle:   rightTitle,
		}

		if isNoCostEMISupported {
			rightTagTitle, _ := sushi.NewTextSnippet("No cost EMI")
			rightTagColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			rightTagIcon, _ := sushi.NewIcon(sushi.PromoIcon, rightTagColor)
			rightTagTitle.SetColor(bgColor)
			rightTagTitle.SetPrefixIcon(rightTagIcon)
			rightTagBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			rightTagBorderColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

			rightTag := &sushi.Tag{
				Title:       rightTagTitle,
				BgColor:     rightTagBgColor,
				BorderColor: rightTagBorderColor,
			}
			snippet.RightTag = rightTag
		} else {
			snippet.RightSubtitle = rightSubtitle
		}

		if !data.AssessmentTaken {
			subtitle3, _ := sushi.NewTextSnippet("Trial not taken")
			colorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_LIGHT_THEME
			}
			color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
			subtitle3.SetColor(color)
			subtitle3.SetFont(fontMed100)
			snippet.Subtitle3 = subtitle3
		}

		if data.AssessmentAwaiting {
			infoText, _ := sushi.NewTextSnippet(fmt.Sprintf("%s plans will be populated once we have your assessment. It ususally takes upto 24 hours after your trial.", data.CourseName))
			colorType := sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_LIGHT_THEME
			}
			color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
			infoText.SetColor(color)
			infoText.SetFont(fontMed100)
			snippet.InfoText = infoText
			snippet.IsSelectable = false
			snippet.IsSelectable = false
		}

		subtitle1, _ := sushi.NewTextSnippet(data.DaysSlotsText)
		colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle1.SetColor(colorGrey900)
		subtitle1.SetFont(fontMed100)
		subtitle1.SetIsMarkdown(1)
		snippet.Subtitle1 = subtitle1

		if data.CourseCategoryId == 3 {

			toolTipTitle, _ := sushi.NewTextSnippet("For advanced players 2 hours training sessions are scheduled twice a day")
			colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			fontMed300, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			toolTipTitle.SetFont(fontMed300)
			toolTipTitle.SetColor(colorWhite500)

			toolTipSubtitle, _ := sushi.NewTextSnippet("OK")
			toolTipSubtitle.SetColor(colorWhite500)
			toolTipSubtitle.SetFont(fontMed300)

			toolTipBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

			showTooltip := &sushi.ShowTooltip{
				Title:    toolTipTitle,
				Subtitle: toolTipSubtitle,
				BgColor:  toolTipBgColor,
			}
			clickAction := sushi.GetClickAction()
			clickAction.SetShowTooltip(showTooltip)

			icon, _ := sushi.NewIcon(sushi.ExtraInfoIcon, colorGrey900)
			icon.SetClickAction(clickAction)
			snippet.InfoIcon = icon
		}

		buttonClickAction := sushi.GetClickAction()
		if !p.IsRecommendedCourseOpened {
			userData := &sushi.UserData{
				UserId:           p.UserId,
				CourseCategoryId: data.CourseCategoryId,
			}
			selectedCourse := &sushi.OpenAcademyRecommendedCourse{
				UserData: userData,
			}
			dismissPage := &sushi.DismissPage{
				Type:                         sushi.ClickActionOpenAcademyRecommendedCourse,
				OpenAcademyRecommendedCourse: selectedCourse,
			}
			buttonClickAction.SetPageDismiss(dismissPage)
		} else {
			refreshPageAction := &sushi.RefreshDetailsPage{
				CourseCategoryId: data.CourseCategoryId,
				UserId:           p.UserId,
			}
			dismissPage := &sushi.DismissPage{
				Type:               sushi.ClickActionRefreshDetailsPage,
				RefreshDetailsPage: refreshPageAction,
			}
			buttonClickAction.SetPageDismiss(dismissPage)
		}

		button := &sushi.Button{
			Type:        sushi.ButtonTypeSolid,
			Text:        "Proceed with selected plan",
			ID:          "bottom_button_id1",
			ClickAction: buttonClickAction,
		}
		change_bottom_button := &sushi.ChangeBottomButton{
			ButtonId: "bottom_button_id1",
			Button:   button,
		}
		clickAction := &sushi.ClickAction{
			Type:               sushi.ClickActionChangeBottomButton,
			ChangeBottomButton: change_bottom_button,
		}
		snippet.ClickAction = clickAction

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:          layoutConfig,
			FitsoTextSnippetType8: snippet,
		}
		items = append(items, *item)
	}
	p.Results = &items
}

func (p *AcademyPlansTemplate) SetFooter() {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItemText string
	buttonItemText = "Select a plan to continue"
	buttonItemFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItemBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:             "solid",
		Text:             buttonItemText,
		Font:             buttonItemFont,
		Id:               "bottom_button_id1",
		BgColor:          buttonItemBgColor,
		IsActionDisabled: 1,
	}
	items = append(items, buttonItem)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	p.Footer = footer
}
