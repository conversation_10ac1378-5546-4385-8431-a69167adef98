package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	productModels "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

const (
	summerCampBenefits = "Benefits of Cult Summer Camp"
)

type SummerCampPurchasePageTemplate struct {
	Sections          []*productModels.ResultSection  `json:"results,omitempty"`
	BackgroundImage   *sushi.Image                    `json:"bg_image,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout `json:"footer,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem          `json:"clever_tap_tracking,omitempty"`
}

func GetSummerCampPurchasePageC(c *gin.Context) {
	productService := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)
	var templateResponse SummerCampPurchasePageTemplate

	purchasePageDetails, err := productService.GetSummerCampPurchasePageDetails(ctx, &productPB.Empty{})
	if err != nil {
		log.Println("Error in controller GetSummerCampPurchasePageC while getting purchase page details, Error: %v", err)
		c.JSON(http.StatusOK, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	} else if purchasePageDetails.Status != nil && purchasePageDetails.Status.Status == apiCommon.FAILED {
		log.Println("Failed in controller GetSummerCampPurchasePageC while getting purchase page details, Error: %v", err)
		c.JSON(http.StatusOK, purchasePageDetails.Status)
		return
	} else {
		if purchasePageDetails.Benefits != nil && len(purchasePageDetails.Benefits) > 0 {
			templateResponse.SetHeaderSection(ctx, purchasePageDetails)
			templateResponse.SetMembershipBenefitsSection(c, purchasePageDetails)
			templateResponse.SetLinksSection(ctx, purchasePageDetails)
			templateResponse.SetBgImage(purchasePageDetails)
			templateResponse.SetFooterSection(ctx, purchasePageDetails)
			templateResponse.SetClevertapTracking(ctx)
		}
	}

	c.JSON(http.StatusOK, templateResponse)
}

func (p *SummerCampPurchasePageTemplate) SetHeaderSection(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	fitsoHeaderSnippetType1Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoHeaderSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	isNoCostEMISupported := featuresupport.SupportsNoCostEMI(ctx)

	title, _ := sushi.NewTextSnippet("CULT SUMMER CAMP")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeCider, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)

	image, _ := sushi.NewImage(purchasePageData.FitsoLogo)
	image.SetAspectRatio(2.21)

	subtitle1, _ := sushi.NewTextSnippet(fmt.Sprintf("for %d - %d years of age", apiCommon.SUMMER_CAMP_MIN_AGE, apiCommon.SUMMER_CAMP_MAX_AGE))

	fitsoHeaderSnippetType1 := &sushi.FitsoHeaderSnippetType1Snippet{
		//Title:     title,
		Image:     image,
		Subtitle1: subtitle1,
	}

	noCostEMIBenifit := getNoCostEMIBenifit(purchasePageData)

	if isNoCostEMISupported && noCostEMIBenifit != nil {
		tagTitle, _ := sushi.NewTextSnippet(noCostEMIBenifit.BenefitTitle)
		tagColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		tagBgColor.Transparency = 1
		tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		suffixColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		icon, _ := sushi.NewIcon("e805", suffixColor)
		tagTitle.SetSuffixIcon(icon)

		clickAction := sushi.GetClickAction()
		titleSnippet, _ := sushi.NewTextSnippet(noCostEMIBenifit.BenefitSubtitle1)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		titleSnippet.SetColor(titleColor)
		titleSnippet.SetFont(titleFont)
		subtitleSnippet, _ := sushi.NewTextSnippet("OK")
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		subtitleSnippet.SetColor(subtitleColor)
		subtitleSnippet.SetFont(subtitleFont)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		showToolTip := &sushi.ShowTooltip{
			Title:    titleSnippet,
			Subtitle: subtitleSnippet,
			BgColor:  bgColor,
		}
		clickAction.SetClickActionType(sushi.ClickActionShowToolTip)
		clickAction.SetShowTooltip(showToolTip)

		bottomTag := &sushi.Tag{
			Title:       tagTitle,
			BgColor:     tagBgColor,
			BorderColor: tagBorderColor,
			ClickAction: clickAction,
		}
		fitsoHeaderSnippetType1.BottomTag = bottomTag
	}

	section := &productModels.ResultSection{
		LayoutConfig:            fitsoHeaderSnippetType1Layout,
		FitsoHeaderSnippetType1: fitsoHeaderSnippetType1,
	}
	p.AddSection(section)
}

func (p *SummerCampPurchasePageTemplate) SetMembershipBenefitsSection(c *gin.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	ctx := util.PrepareGRPCMetaData(c)
	rootLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetSnippetType19,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	isNoCostEMISupported := featuresupport.SupportsNoCostEMI(ctx)
	noCostEMIBenifit := getNoCostEMIBenifit(purchasePageData)

	var newItems []sushi.V2ImageTextType14SnippetItem
	for _, benefit := range purchasePageData.Benefits {
		if isNoCostEMISupported && noCostEMIBenifit != nil && noCostEMIBenifit.BenefitTitle == benefit.BenefitTitle {
			continue
		}
		benefitTitle, _ := sushi.NewTextSnippet(benefit.BenefitTitle)
		benefitFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		benefitTitle.SetFont(benefitFont)

		benefitImage, _ := sushi.NewImage(benefit.Image)
		benefitImage.SetType(sushi.ImageTypeCircle)
		benefitImage.SetHeight(32)
		benefitImage.SetWidth(32)

		item := sushi.V2ImageTextType14SnippetItem{
			Title: benefitTitle,
			Image: benefitImage,
		}
		if benefit.BenefitSubtitle1 != "" && false {
			suffixColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			icon, _ := sushi.NewIcon("e805", suffixColor)
			benefitTitle.SetSuffixIcon(icon)
			item.Title = benefitTitle
			clickAction := sushi.GetClickAction()
			titleSnippet, _ := sushi.NewTextSnippet(benefit.BenefitSubtitle1)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			titleSnippet.SetColor(titleColor)
			titleSnippet.SetFont(titleFont)
			subtitleSnippet, _ := sushi.NewTextSnippet("OK")
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitleSnippet.SetColor(subtitleColor)
			subtitleSnippet.SetFont(subtitleFont)
			bgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			showToolTip := &sushi.ShowTooltip{
				Title:    titleSnippet,
				Subtitle: subtitleSnippet,
				BgColor:  bgColor,
			}
			clickAction.SetClickActionType(sushi.ClickActionShowToolTip)
			clickAction.SetShowTooltip(showToolTip)
			item.ClickAction = clickAction
			trackingItem := p.GetToolTipTrackingItem(ctx, benefit.BenefitTitle)
			item.ClevertapTracking = []*sushi.ClevertapItem{trackingItem}
		}
		newItems = append(newItems, item)
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType14,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
	}
	snippet := &sushi.V2ImageTextType14Snippet{
		Item: &newItems,
	}

	itemLayoutSnippet := sushi.V2ImageTextSnippetType14Layout{
		LayoutConfig:             layoutConfig,
		V2ImageTextSnippetType14: snippet,
	}

	rootItemSnippet := []sushi.V2ImageTextSnippetType14Layout{itemLayoutSnippet}

	benefitHeadTitle, _ := sushi.NewTextSnippet(summerCampBenefits)
	benefitHeadFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	benefitHeadColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

	benefitHeadTitle.SetFont(benefitHeadFont)
	benefitHeadTitle.SetColor(benefitHeadColor)
	benefitHeadTitle.SetAlignment(sushi.TextAlignmentCenter)

	borderColorGrey, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	rootSnippet := &sushi.FitsoImageTextSnippetType19Snippet{
		CornerRadius: int32(12),
		Title:        benefitHeadTitle,
		BorderColor:  borderColorGrey,
		Items:        &rootItemSnippet,
	}

	section := &productModels.ResultSection{
		LayoutConfig:                       rootLayoutConfig,
		FitsoImageTextSnippetType19Snippet: rootSnippet,
	}

	p.AddSection(section)
}

func (p *SummerCampPurchasePageTemplate) GetToolTipTrackingItem(ctx context.Context, title string) *sushi.ClevertapItem {
	tapPayload := p.GetCommonPayloadForTracking(ctx)
	temp := strings.Split(strings.ToLower(title), " ")
	tapEname := &sushi.EnameData{
		Ename: strings.Join(temp, "_") + "_tool_tip_tap",
	}
	event := sushi.NewClevertapEvents()
	event.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, event)

	return trackItem
}

func (p *SummerCampPurchasePageTemplate) SetLinksSection(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	var buttonDataItems []sushi.FitsoTextSnippetType1ButtonItem

	rightArrowIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	rightArrowIcon, _ := sushi.NewIcon(sushi.RightArrowOutlineIcon, rightArrowIconColor)

	faqClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	faqClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: purchasePageData.FaqUrl.Text,
		URL:   purchasePageData.FaqUrl.Path,
	})

	tapPayload := p.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "summercamp_faq_tap",
	}
	faqTapEvent := sushi.NewClevertapEvents()
	faqTapEvent.SetTap(tapEname)
	faqTapTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, faqTapEvent)

	faqButtonDataItem := sushi.FitsoTextSnippetType1ButtonItem{
		Type:              sushi.FitsoTextSnippetType1TypeText,
		Text:              purchasePageData.FaqUrl.Text,
		Size:              sushi.FitsoTextSnippetType1ButtonSizeMedium,
		SuffixIcon:        rightArrowIcon,
		ClickAction:       faqClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{faqTapTrackItem},
	}
	buttonDataItems = append(buttonDataItems, faqButtonDataItem)

	tncClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	tncClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: purchasePageData.TncUrl.Text,
		URL:   purchasePageData.TncUrl.Path,
	})

	tapTncEname := &sushi.EnameData{
		Ename: "summercamp_t&c_tap",
	}
	tcTapEvent := sushi.NewClevertapEvents()
	tcTapEvent.SetTap(tapTncEname)
	tncTapTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tcTapEvent)

	tncButtonDataItem := sushi.FitsoTextSnippetType1ButtonItem{
		Type:              sushi.FitsoTextSnippetType1TypeText,
		Text:              purchasePageData.TncUrl.Text,
		Size:              sushi.FitsoTextSnippetType1ButtonSizeMedium,
		SuffixIcon:        rightArrowIcon,
		ClickAction:       tncClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{tncTapTrackItem},
	}
	buttonDataItems = append(buttonDataItems, tncButtonDataItem)

	buttonData := &sushi.FitsoTextSnippetType1SnippetButton{
		Orientation: sushi.FitsoTextSnippetType1OrientationVertical,
		Items:       &buttonDataItems,
	}
	snippet := &sushi.FitsoTextSnippetType1Snippet{
		ButtonData: buttonData,
	}
	section := &productModels.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType1: snippet,
	}
	p.AddSection(section)
}

func (p *SummerCampPurchasePageTemplate) SetBgImage(purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	bgImage, _ := sushi.NewImage(purchasePageData.HeaderBackground)
	p.BackgroundImage = bgImage
}

func (p *SummerCampPurchasePageTemplate) SetFooterSection(ctx context.Context, purchasePageData *productPB.GetPurchasPageDetailsResponse) {
	footerLayoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	var buttonDataItems []sushi.FooterSnippetType2ButtonItem

	loggedInUser := util.GetUserIDFromContext(ctx)
	purchaseUserClickAction := sushi.GetClickAction()
	if loggedInUser > 0 {
		deeplink := sushi.Deeplink{
			URL: util.GetSummerCampPurchaseMembersDeeplink(),
		}
		purchaseUserClickAction.SetDeeplink(&deeplink)
	} else {
		auth_title, _ := sushi.NewTextSnippet("Please login to purchase a cult SUMMER CAMP membership. Enter your phone number to login using OTP.")
		payload := map[string]interface{}{
			"post_action": "buy_academy",
			"product_category_id": 13,
		}

		postback_params, _ := json.Marshal(payload)
		auth := &sushi.Auth{
			Title:          auth_title,
			PostbackParams: string(postback_params),
			Source:         "product_details",
		}
		purchaseUserClickAction.SetAuth(auth)
	}

	tapPayload := p.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "summercamp_proceed_buy_membership_tap",
	}
	buyTapEvent := sushi.NewClevertapEvents()
	buyTapEvent.SetTap(tapEname)
	buyTapTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buyTapEvent)

	purchaseUserButton := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeSolid,
		Size:              sushi.FooterButtonSizeLarge,
		Text:              "Proceed to buy membership",
		ClickAction:       purchaseUserClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{buyTapTrackItem},
	}
	buttonDataItems = append(buttonDataItems, purchaseUserButton)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &buttonDataItems,
	}
	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       footerLayoutConfig,
		FooterSnippetType2: footerSnippet,
	}
	p.Footer = footer
}

func (p *SummerCampPurchasePageTemplate) AddSection(section *productModels.ResultSection) {
	p.Sections = append(p.Sections, section)
}

func (p *SummerCampPurchasePageTemplate) SetClevertapTracking(ctx context.Context) {
	commonPayload := p.GetCommonPayloadForTracking(ctx)

	landingEName := &sushi.EnameData{
		Ename: "summercamp_buy_membership_landing",
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	dismissEName := &sushi.EnameData{
		Ename: "summercamp_buy_membership_back_button_tap",
	}
	pageDismissEvent := sushi.NewClevertapEvents()
	pageDismissEvent.SetPageDismiss(dismissEName)
	pageDismissTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageDismissEvent)
	p.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem, pageDismissTrackItem}
}

func (p *SummerCampPurchasePageTemplate) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "summercamp_buy_membership"
	return commonPayload
}
