package productController

import (
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	productModel "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	structs "bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// CancelMembershipRequestTemplate represents the response structure
type CancelMembershipRequestTemplate struct {
	Status            string                              `json:"status,omitempty"`
	ActionList        []*productModel.CancelBookingAction `json:"action_list,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem              `json:"clever_tap_tracking,omitempty"`
}

// CancelMembershipC is the controller layer which raised the user cancel membership request
func CancelMembershipC(c *gin.Context) {
	cl := util.GetProductClient()
	ctx := util.PrepareGRPCMetaData(c)

	template := CancelMembershipRequestTemplate{}

	loggedInUser := util.GetUserIDFromContext(c)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	reqData := prepareCancellationMembershipRequest(c)
	response, err := cl.CancelMembership(ctx, reqData)
	if err != nil {
		log.Println("Could not create User Query for Cancel Membership---", err)
		c.JSON(http.StatusOK, template.failedResponse(c, common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		message := response.Status.Message
		c.JSON(http.StatusOK, template.failedResponse(c, message))
		return
	}

	c.JSON(http.StatusOK, template.successResponse(c, response.Status.Message))
	return
}

func prepareCancellationMembershipRequest(c *gin.Context) *productPB.CancelMembershipReq {

	var json structs.CancelMembershipReq
	json = c.MustGet("jsonData").(structs.CancelMembershipReq)

	var subscription_ids []int32
	for _, membershipInfo := range json.MembershipsInfo {
		subscription_ids = append(subscription_ids, membershipInfo.SubscriptionId)
	}
	subscription_ids = util.DeduplicateSlice(subscription_ids)

	var membershipsInfo []*productPB.MembershipInfo
	for _, subscription_id := range subscription_ids {
		membershipObj := &productPB.MembershipInfo{
			SubscriptionId: subscription_id,
		}
		membershipsInfo = append(membershipsInfo, membershipObj)
	}

	var cancellation_reason_ids []int32
	for _, reason := range json.CancellationReasons {
		cancellation_reason_ids = append(cancellation_reason_ids, reason.ReasonId)
	}

	cancellation_reason_ids = util.DeduplicateSlice(cancellation_reason_ids)

	return &productPB.CancelMembershipReq{
		MembershipsInfo:       membershipsInfo,
		CancellationReasonIds: cancellation_reason_ids,
	}
}

func NewCancelMembershipRequestTemplate() *CancelMembershipRequestTemplate {
	return &CancelMembershipRequestTemplate{}
}

func (s *CancelMembershipRequestTemplate) getTitle(text string) *sushi.TextSnippet {
	title, _ := sushi.NewTextSnippet(text)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	title.SetColor(color)
	title.SetFont(font)
	return title
}

func (s *CancelMembershipRequestTemplate) getMessage(text string) *sushi.TextSnippet {
	message, _ := sushi.NewTextSnippet(text)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	message.SetFont(font)
	return message
}

func (s *CancelMembershipRequestTemplate) getImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (s *CancelMembershipRequestTemplate) setStatus(status string) {
	s.Status = status
}

func (s *CancelMembershipRequestTemplate) failedResponse(c *gin.Context, errorMessage string) *CancelMembershipRequestTemplate {
	response := NewCancelMembershipRequestTemplate()
	response.setStatus(common.FAILURE)

	clevertapTrackingItems := getClevertapTrackingItems(c, common.FAILURE)

	customAlert := &sushi.CustomAlert{
		Title:   s.getTitle("Membership Cancellation Failed"),
		Message: s.getMessage(errorMessage),
		Image:   s.getImage(util.GetFailedLoty()),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		ClevertapTracking: clevertapTrackingItems,
		DismissAfterAction: true,
	}

	customAlertAction := &productModel.CancelBookingAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	response.ActionList = append(response.ActionList, customAlertAction)
	return response
}

func (s *CancelMembershipRequestTemplate) successResponse(c *gin.Context, succMessage string) *CancelMembershipRequestTemplate {
	response := NewCancelMembershipRequestTemplate()
	response.setStatus(common.SUCCESS)

	clevertapTrackingItems := getClevertapTrackingItems(c, common.SUCCESS)

	customAlert := &sushi.CustomAlert{
		Title:      s.getTitle("Membership cancelled"),
		Message:    s.getMessage(succMessage),
		IsBlocking: true,
		Image:      s.getImage(util.GetSuccessLoty()),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		ClevertapTracking: clevertapTrackingItems,
		DismissAfterAction: true,
	}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	bottomSheetDismissAction := &productModel.CancelBookingAction{
		Type:                  sushi.ActionTypeCancelMembership,
		CancelMembershipEvent: action,
	}
	response.ActionList = append(response.ActionList, bottomSheetDismissAction)
	return response
}

func getClevertapTrackingItems(c *gin.Context, apiResponseStatus string) []*sushi.ClevertapItem {
	json := c.MustGet("jsonData").(structs.CancelMembershipReq)
	ctx := util.PrepareGRPCMetaData(c)

	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(c)
	commonPayload["status"] = apiResponseStatus

	apiResponseEname := &sushi.EnameData{
		Ename: "membership_cancel_response",
	}
	apiResponseEvent := sushi.NewClevertapEvents()
	apiResponseEvent.SetImpression(apiResponseEname)

	var clevertapTrackingItems []*sushi.ClevertapItem
	for _, membershipInfo := range json.MembershipsInfo {
		for _, reason := range json.CancellationReasons {
			commonPayload["reason"] = reason.ReasonId
			commonPayload["subscription_id"] = membershipInfo.SubscriptionId
			trackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, apiResponseEvent)
			clevertapTrackingItems = append(clevertapTrackingItems, trackItem)
		}
	}

	return clevertapTrackingItems
}
