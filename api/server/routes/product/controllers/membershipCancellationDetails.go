package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	"bitbucket.org/jogocoin/go_api/api/render"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	structs "bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// MembershipCancellationDetailTemplate represents the response structure of membership cancellation detail page
type MembershipCancellationDetailTemplate struct {
	Header            *sushi.FitsoBottomSheetType1Header          `json:"header,omitempty"`
	Results           []*sushi.FitsoBottomSheetType1ResultSection `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout             `json:"footer,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                      `json:"clever_tap_tracking,omitempty"`
	UserId            int32                                       `json:"-"`
}

func MembershipCancellationDetailsC(c *gin.Context) {
	cl := util.GetProductClient()

	ctx := util.PrepareGRPCMetaData(c)
	var json structs.GetMembershipsEligibleForRefundReq
	json = c.MustGet("jsonData").(structs.GetMembershipsEligibleForRefundReq)

	loggedInUserID := util.GetUserIDFromContext(ctx)

	if loggedInUserID == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("You are not authorized"))
		return
	}

	reqData := &productPB.GetMembershipsEligibleForRefundReq{
		ProductId: json.ProductId,
		UserId:    json.UserId,
	}
	membershipResponse, err := cl.GetMembershipsEligibleForRefund(ctx, reqData)
	if err != nil {
		log.Println(err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if membershipResponse.Status != nil && membershipResponse.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("You are not authorized"))
		return
	}

	fcl := util.GetFacilitySportClient()
	fitsoBenifits, err := fcl.GetFitsoBenefits(ctx, &facilitySportPB.FitsoBenefitReq{ExtraBenefits: true})
	if err != nil {
		log.Println(err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	template := MembershipCancellationDetailTemplate{
		UserId: reqData.UserId,
	}
	template.SetHeaderSection()
	template.SetResultSection(fitsoBenifits, membershipResponse)
	template.SetFooterSection(ctx, membershipResponse)
	template.SetClevertapTracking(ctx)
	render.Render(c, gin.H{"payload": template}, "index.html")
}

func (s *MembershipCancellationDetailTemplate) SetHeaderSection() {
	title, _ := sushi.NewTextSnippet("Are you sure want to cancel?")
	header := &sushi.FitsoBottomSheetType1Header{
		Title: title,
	}
	s.Header = header
}

func (s *MembershipCancellationDetailTemplate) SetResultSection(fitsoBenifits *facilitySportPB.Benefit, membershipResponse *productPB.GetMembershipsEligibleForRefundRes) {
	items := make([]*sushi.FitsoBottomSheetType1ResultSection, 0)

	vertical_list_items := make([]sushi.VerticalListItem, 0)
	vl_layout_config1 := &sushi.LayoutConfig{
		SnippetType:  sushi.SectionHeaderType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	sectionHeaderType1Title, _ := sushi.NewTextSnippet("You’ll lose all the benefits of a cultpass PLAY membership")
	sectionHeaderType1TitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	sectionHeaderType1Title.SetFont(sectionHeaderType1TitleFont)

	vertical_section_header_type_1 := &sushi.SectionHeaderType1Snippet{
		Title: sectionHeaderType1Title,
	}
	vertical_list_item1 := &sushi.VerticalListItem{
		LayoutConfig:       vl_layout_config1,
		SectionHeaderType1: vertical_section_header_type_1,
	}

	vertical_list_items = append(vertical_list_items, *vertical_list_item1)

	for _, itm := range fitsoBenifits.BenefitDescription {
		vl_layout_config2 := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoImageTextSnippetType6,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		fitso_image_text_snippet_type_6_title, _ := sushi.NewTextSnippet(itm.Title)
		fitso_image_text_snippet_type_6_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		fitso_image_text_snippet_type_6_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		fitso_image_text_snippet_type_6_title.SetFont(fitso_image_text_snippet_type_6_font)
		fitso_image_text_snippet_type_6_title.SetColor(fitso_image_text_snippet_type_6_color)

		fitso_image_text_snippet_type_6_img_url := itm.Image1
		fitso_image_text_snippet_type_6_image, _ := sushi.NewImage(fitso_image_text_snippet_type_6_img_url)
		fitso_image_text_snippet_type_6_image.SetType(sushi.ImageTypeCircle)
		fitso_image_text_snippet_type_6_image.SetHeight(24)
		fitso_image_text_snippet_type_6_image.SetAspectRatio(1)
		fitso_image_text_snippet_type_6 := &sushi.FitsoImageTextSnippetType6Snippet{
			Image: fitso_image_text_snippet_type_6_image,
			Title: fitso_image_text_snippet_type_6_title,
		}
		vertical_list_item2 := &sushi.VerticalListItem{
			LayoutConfig:               vl_layout_config2,
			FitsoImageTextSnippetType6: fitso_image_text_snippet_type_6,
		}
		vertical_list_items = append(vertical_list_items, *vertical_list_item2)
	}

	vertical_list_border, _ := sushi.NewBorderSnippet()
	vertical_list_border_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	vertical_list_border.SetBorderColor(vertical_list_border_color)
	vertical_list_border.SetBorderType(sushi.BorderTypeRounded)
	vertical_list_border.SetBorderRadius(sushi.BorderRadius8)

	vertical_list_bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	vertical_list_type_1 := &sushi.VerticalListType1{
		Items:      vertical_list_items,
		BgColor:    vertical_list_bg_color,
		BorderData: vertical_list_border,
	}

	layoutConfig1 := &sushi.LayoutConfig{
		SnippetType: sushi.VerticalListSnipppetType1,
		LayoutType:  sushi.LayoutTypeGrid,
	}
	section1 := &sushi.FitsoBottomSheetType1ResultSection{
		LayoutConfig:      layoutConfig1,
		VerticalListType1: vertical_list_type_1,
	}
	items = append(items, section1)

	section_header_type1_title, _ := sushi.NewTextSnippet("We are sad to see you go! What made you cancel ? 😞")
	section_header_type1_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	section_header_type1_title.SetFont(section_header_type1_title_font)

	section_header_type_1 := &sushi.SectionHeaderType1Snippet{
		Title: section_header_type1_title,
	}

	layoutConfig2 := &sushi.LayoutConfig{
		SnippetType:  sushi.SectionHeaderType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	section2 := &sushi.FitsoBottomSheetType1ResultSection{
		LayoutConfig:       layoutConfig2,
		SectionHeaderType1: section_header_type_1,
	}
	items = append(items, section2)

	p_items := make([]*sushi.FitsoPillsType1Item, 0)
	for _, item := range membershipResponse.CancellationReasons {
		title, _ := sushi.NewTextSnippet(item.Reason)
		title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		title.SetFont(title_font)

		out := map[string]interface{}{
			"reason_id": item.ReasonId,
		}

		payload, err := json.Marshal(out)
		if err != nil {
			log.Println("error in marshalling the payload data")
		}

		fitso_pills_type1_item := &sushi.FitsoPillsType1Item{
			PostbackParams: string(payload),
			Title:          title,
			IsSelected:     false,
		}
		p_items = append(p_items, fitso_pills_type1_item)
	}

	//shuffling cancellation reasons
	rand.Shuffle(len(p_items), func(i, j int) {
		p_items[i], p_items[j] = p_items[j], p_items[i]
	})

	selected_config_text_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	selected_config_bg_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	selected_config_border_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	selected_config := &sushi.FitsoPillsType1ConfigDetail{
		TextColor:   selected_config_text_color,
		BgColor:     selected_config_bg_color,
		BorderColor: selected_config_border_color,
	}

	unselected_config_text_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	unselected_config_bg_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	unselected_config_border_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	unselected_config := &sushi.FitsoPillsType1ConfigDetail{
		TextColor:   unselected_config_text_color,
		BgColor:     unselected_config_bg_color,
		BorderColor: unselected_config_border_color,
	}
	fitso_pills_type_1_config := &sushi.FitsoPillsType1Config{
		Selected:   selected_config,
		Unselected: unselected_config,
	}
	fitso_pills_type_1 := &sushi.FitsoPillsType1{
		Items:  p_items,
		Config: fitso_pills_type_1_config,
	}

	layoutConfig3 := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoPillsSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	section3 := &sushi.FitsoBottomSheetType1ResultSection{
		LayoutConfig:    layoutConfig3,
		FitsoPillsType1: fitso_pills_type_1,
	}
	items = append(items, section3)
	s.Results = items
}

func (s *MembershipCancellationDetailTemplate) SetFooterSection(ctx context.Context, response *productPB.GetMembershipsEligibleForRefundRes) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItem1Text string
	buttonItem1Text = "Go back"
	buttonItem1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

	click_action1 := &sushi.ClickAction{
		Type: "dismiss_page",
	}
	tapPayload := s.GetCommonPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "cancel_membership_go_back_tap",
	}
	goBackButtonEvents := sushi.NewClevertapEvents()
	goBackButtonEvents.SetTap(tapEname)
	goBackTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, goBackButtonEvents)
	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Id:                "bottom_button_id_1",
		Type:              "outline",
		Text:              buttonItem1Text,
		Font:              buttonItem1Font,
		ClickAction:       click_action1,
		ClevertapTracking: []*sushi.ClevertapItem{goBackTrackItem},
	}
	items = append(items, buttonItem1)

	var buttonItem2Text string
	buttonItem2Text = "Proceed"
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

	click_action2 := &sushi.ClickAction{
		Type:                  "fitso_bottom_sheet_type_1",
		FitsoBottomSheetType1: RefundMembershipDetails(ctx, s.UserId, response),
	}

	tapEname = &sushi.EnameData{
		Ename: "cancel_membership_proceed_tap",
	}
	proceedButtonEvents := sushi.NewClevertapEvents()
	proceedButtonEvents.SetTap(tapEname)
	proceedTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, proceedButtonEvents)
	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Id:                "bottom_button_id_2",
		Type:              "solid",
		Text:              buttonItem2Text,
		Font:              buttonItem2Font,
		IsActionDisabled:  1,
		ClickAction:       click_action2,
		ClevertapTracking: []*sushi.ClevertapItem{proceedTrackItem},
	}
	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}

	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func RefundMembershipDetails(ctx context.Context, userId int32, response *productPB.GetMembershipsEligibleForRefundRes) *sushi.FitsoBottomSheetType1 {
	result := &sushi.FitsoBottomSheetType1{}
	if len(response.Memberships) > 0 {
		refund_membership_details := MembershipCancellationDetailTemplate{
			UserId: userId,
		}
		refund_membership_details.SetCartHeaderSection(response)
		refund_membership_details.SetCartResultSection(ctx, response)
		refund_membership_details.SetCartFooterSection(ctx, response)
		refund_membership_details.SetCancellationConfirmClevertapTracking(ctx)
		result.Header = refund_membership_details.Header
		result.Results = refund_membership_details.Results
		result.Footer = refund_membership_details.Footer
		result.ClevertapTracking = refund_membership_details.ClevertapTracking
	}
	return result
}

func (s *MembershipCancellationDetailTemplate) SetCartHeaderSection(response *productPB.GetMembershipsEligibleForRefundRes) {
	var title_text string
	if len(response.Memberships) > 1 {
		title_text = "Select memberships to cancel"
	} else {
		title_text = "Are you sure want to cancel?"
	}
	title, _ := sushi.NewTextSnippet(title_text)
	header := &sushi.FitsoBottomSheetType1Header{
		Title: title,
	}
	s.Header = header
}

func (s *MembershipCancellationDetailTemplate) SetCartResultSection(ctx context.Context, response *productPB.GetMembershipsEligibleForRefundRes) {
	items := make([]*sushi.FitsoBottomSheetType1ResultSection, 0)

	text_snippet_type_1_title, _ := sushi.NewTextSnippet("Your refund amount is calculated on a pro-rata basis")
	text_snippet_type_1_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint800)
	text_snippet_type_1_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	text_snippet_type_1_title.SetColor(text_snippet_type_1_title_color)
	text_snippet_type_1_title.SetFont(text_snippet_type_1_title_font)

	text_snippet_type_1_subtitle1, _ := sushi.NewTextSnippet("The balance amount you receive will be proportionate to the unused duration of the membership.")
	text_snippet_type_1_subtitle1_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	text_snippet_type_1_subtitle1_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	text_snippet_type_1_subtitle1.SetColor(text_snippet_type_1_subtitle1_color)
	text_snippet_type_1_subtitle1.SetFont(text_snippet_type_1_subtitle1_font)
	text_snippet_type_1 := &sushi.TextSnippetType1Snippet{
		Title:     text_snippet_type_1_title,
		Subtitle1: text_snippet_type_1_subtitle1,
	}

	layoutConfig1 := &sushi.LayoutConfig{
		SnippetType: sushi.TextSnippetType1,
		LayoutType:  sushi.LayoutTypeGrid,
	}
	section1 := &sushi.FitsoBottomSheetType1ResultSection{
		LayoutConfig:     layoutConfig1,
		TextSnippetType1: text_snippet_type_1,
	}
	items = append(items, section1)

	for _, itm := range response.Memberships {
		radio_container_type_1_title, _ := sushi.NewTextSnippet(itm.ProductTitle)
		radio_container_type_1_title.SetIsMarkdown(1)
		radio_container_type_1_subtitle, _ := sushi.NewTextSnippet(itm.ProductSubtitle)
		radio_container_type_1_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if featuresupport.SupportsNewColor(ctx) {
			radio_container_type_1_subtitle_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}
		radio_container_type_1_subtitle.SetColor(radio_container_type_1_subtitle_color)

		vertical_list_items := make([]sushi.VerticalListItem, 0)

		// Discounted Grand Total
		fitso_text_snippet_type_5_item_title1, _ := sushi.NewTextSnippet("Discounted Grand Total")
		fitso_text_snippet_type_5_item_title_font1, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		fitso_text_snippet_type_5_item_title1.SetFont(fitso_text_snippet_type_5_item_title_font1)
		fitso_text_snippet_type_5_item_subtitle1, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.1f", itm.PaidAmt))
		fitso_text_snippet_type_5_item_subtitle_color1, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fitso_text_snippet_type_5_item_subtitle_font1, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		fitso_text_snippet_type_5_item_subtitle1.SetFont(fitso_text_snippet_type_5_item_subtitle_font1)
		fitso_text_snippet_type_5_item_subtitle1.SetColor(fitso_text_snippet_type_5_item_subtitle_color1)
		fitso_text_snippet_type_5_item1 := &sushi.FitsoTextSnippetType5Item{
			Title:    fitso_text_snippet_type_5_item_title1,
			Subtitle: fitso_text_snippet_type_5_item_subtitle1,
		}

		fitso_text_snippet_type_obj_1 := &sushi.FitsoTextType5{
			Items: []*sushi.FitsoTextSnippetType5Item{fitso_text_snippet_type_5_item1},
		}

		vl_layout_config1 := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoTextSnippetType5,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		vertical_list_item1 := &sushi.VerticalListItem{
			LayoutConfig:          vl_layout_config1,
			FitsoTextSnippetType5: fitso_text_snippet_type_obj_1,
		}
		vertical_list_items = append(vertical_list_items, *vertical_list_item1)

		// Cost of service used
		fitso_text_snippet_type_5_item_title2, _ := sushi.NewTextSnippet(fmt.Sprintf("Cost of service used (%d days)", itm.DaysConsumed))
		fitso_text_snippet_type_5_item_title_font2, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		fitso_text_snippet_type_5_item_title2.SetFont(fitso_text_snippet_type_5_item_title_font2)
		fitso_text_snippet_type_5_item_subtitle2, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.1f", itm.UsedAmt))
		fitso_text_snippet_type_5_item_subtitle_color2, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fitso_text_snippet_type_5_item_subtitle_font2, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		fitso_text_snippet_type_5_item_subtitle2.SetFont(fitso_text_snippet_type_5_item_subtitle_font2)
		fitso_text_snippet_type_5_item_subtitle2.SetColor(fitso_text_snippet_type_5_item_subtitle_color2)
		fitso_text_snippet_type_5_item_bottom_color2, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint200)
		if featuresupport.SupportsNewColor(ctx) {
			fitso_text_snippet_type_5_item_bottom_color2, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint200)
		}
		fitso_text_snippet_type_5_item_bottom2 := &sushi.TextSnippet{
			Type:  "line",
			Color: fitso_text_snippet_type_5_item_bottom_color2,
		}
		fitso_text_snippet_type_5_item2 := &sushi.FitsoTextSnippetType5Item{
			Title:               fitso_text_snippet_type_5_item_title2,
			Subtitle:            fitso_text_snippet_type_5_item_subtitle2,
			BottomSeparatorData: fitso_text_snippet_type_5_item_bottom2,
		}

		fitso_text_snippet_type_obj_2 := &sushi.FitsoTextType5{
			Items: []*sushi.FitsoTextSnippetType5Item{fitso_text_snippet_type_5_item2},
		}

		vl_layout_config2 := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoTextSnippetType5,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		vertical_list_item2 := &sushi.VerticalListItem{
			LayoutConfig:          vl_layout_config2,
			FitsoTextSnippetType5: fitso_text_snippet_type_obj_2,
		}
		vertical_list_items = append(vertical_list_items, *vertical_list_item2)

		// Total Refund
		fitso_text_snippet_type_5_item_title3, _ := sushi.NewTextSnippet("Total Refund")
		fitso_text_snippet_type_5_item_title_font3, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		fitso_text_snippet_type_5_item_title3.SetFont(fitso_text_snippet_type_5_item_title_font3)
		fitso_text_snippet_type_5_item_subtitle3, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%.1f", itm.RefundAmt))
		fitso_text_snippet_type_5_item_subtitle_color3, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fitso_text_snippet_type_5_item_subtitle_font3, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		fitso_text_snippet_type_5_item_subtitle3.SetFont(fitso_text_snippet_type_5_item_subtitle_font3)
		fitso_text_snippet_type_5_item_subtitle3.SetColor(fitso_text_snippet_type_5_item_subtitle_color3)
		fitso_text_snippet_type_5_item3 := &sushi.FitsoTextSnippetType5Item{
			Title:    fitso_text_snippet_type_5_item_title3,
			Subtitle: fitso_text_snippet_type_5_item_subtitle3,
		}

		fitso_text_snippet_type_obj_3 := &sushi.FitsoTextType5{
			Items: []*sushi.FitsoTextSnippetType5Item{fitso_text_snippet_type_5_item3},
		}

		vl_layout_config3 := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoTextSnippetType5,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		vertical_list_item3 := &sushi.VerticalListItem{
			LayoutConfig:          vl_layout_config3,
			FitsoTextSnippetType5: fitso_text_snippet_type_obj_3,
		}
		vertical_list_items = append(vertical_list_items, *vertical_list_item3)

		vertical_list_border, _ := sushi.NewBorderSnippet()
		vertical_list_border_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint200)
		if featuresupport.SupportsNewColor(ctx) {
			vertical_list_border_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint200)
		}
		vertical_list_border.SetBorderColor(vertical_list_border_color)
		vertical_list_border.SetBorderType(sushi.BorderTypeRounded)
		vertical_list_border.SetBorderRadius(sushi.BorderRadius8)

		vertical_list_bg_color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint50)
		if featuresupport.SupportsNewColor(ctx) {
			vertical_list_bg_color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint50)
		}
		vertical_list_type_1 := &sushi.VerticalListType1{
			Items:      vertical_list_items,
			BgColor:    vertical_list_bg_color,
			BorderData: vertical_list_border,
		}

		vertical_list_type_1_layout_config := &sushi.LayoutConfig{
			SnippetType: sushi.VerticalListSnipppetType1,
			LayoutType:  sushi.LayoutTypeGrid,
		}
		vertical_title_subtitle := &sushi.FitsoBottomSheetType1ResultSection{
			LayoutConfig:      vertical_list_type_1_layout_config,
			VerticalListType1: vertical_list_type_1,
		}

		var is_checkbox_disabled bool
		if len(response.Memberships) == 1 {
			is_checkbox_disabled = true
		} else {
			is_checkbox_disabled = false
		}

		out := map[string]interface{}{
			"subscription_id": itm.SubscriptionId,
		}

		payload, err := json.Marshal(out)
		if err != nil {
			log.Println("error in marshalling the payload data")
		}

		radio_container_type_1 := &sushi.RadioContainerType1{
			PostbackParams:        string(payload),
			Title:                 radio_container_type_1_title,
			Subtitle:              radio_container_type_1_subtitle,
			VerticalTitleSubtitle: vertical_title_subtitle,
			IsCheckboxDisabled:    is_checkbox_disabled,
		}

		layoutConfig2 := &sushi.LayoutConfig{
			SnippetType: sushi.RadioContainerSnippetType1,
			LayoutType:  sushi.LayoutTypeGrid,
		}
		section2 := &sushi.FitsoBottomSheetType1ResultSection{
			LayoutConfig:        layoutConfig2,
			RadioContainerType1: radio_container_type_1,
		}
		items = append(items, section2)
	}

	s.Results = items
}

func (s *MembershipCancellationDetailTemplate) SetCartFooterSection(ctx context.Context, response *productPB.GetMembershipsEligibleForRefundRes) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItem1Text string
	buttonItem1Text = "Don't cancel"
	buttonItem1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

	click_action1 := &sushi.ClickAction{
		Type: "dismiss_page",
	}
	tapPayload := s.GetCommonPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "cancel_membership_dont_cancel_tap",
	}
	dontCancelButtonEvents := sushi.NewClevertapEvents()
	dontCancelButtonEvents.SetTap(tapEname)
	dontCancelTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, dontCancelButtonEvents)
	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Id:                "bottom_button_id_1",
		Type:              "outline",
		Text:              buttonItem1Text,
		Font:              buttonItem1Font,
		ClickAction:       click_action1,
		ClevertapTracking: []*sushi.ClevertapItem{dontCancelTrackItem},
	}
	items = append(items, buttonItem1)

	var buttonItem2Text string
	buttonItem2Text = "Cancel Now"
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

	click_action2 := sushi.GetClickAction()
	apiCall := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/product/cancel/membership",
	}
	click_action2.SetApiCallAction(apiCall)

	tapEname = &sushi.EnameData{
		Ename: "cancel_membership_cancel_now_tap",
	}
	cancelButtonEvents := sushi.NewClevertapEvents()
	cancelButtonEvents.SetTap(tapEname)
	cancelTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, cancelButtonEvents)

	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Id:                "bottom_button_id_2",
		Type:              "solid",
		Text:              buttonItem2Text,
		Font:              buttonItem2Font,
		IsActionDisabled:  1,
		ClickAction:       click_action2,
		ClevertapTracking: []*sushi.ClevertapItem{cancelTrackItem},
	}

	if len(response.Memberships) > 1 {
		buttonItem2.IsActionDisabled = 1
	}
	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}

	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func (s *MembershipCancellationDetailTemplate) SetClevertapTracking(ctx context.Context) {
	commonPayload := s.GetCommonPayload(ctx)
	landingEName := &sushi.EnameData{
		Ename: "cancel_membership_landing",
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	s.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem}
}

func (s *MembershipCancellationDetailTemplate) SetCancellationConfirmClevertapTracking(ctx context.Context) {
	commonPayload := s.GetCommonPayload(ctx)
	landingEName := &sushi.EnameData{
		Ename: "cancel_confirmation_landing",
	}
	pageSuccessEvent := sushi.NewClevertapEvents()
	pageSuccessEvent.SetPageSuccess(landingEName)
	pageSuccessTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, pageSuccessEvent)

	s.ClevertapTracking = []*sushi.ClevertapItem{pageSuccessTrackItem}
}

func (s *MembershipCancellationDetailTemplate) GetCommonPayload(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["member"] = s.UserId
	commonPayload["source"] = "cancel_membership"
	return commonPayload
}
