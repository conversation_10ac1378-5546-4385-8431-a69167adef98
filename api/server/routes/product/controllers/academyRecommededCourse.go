package productController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/jogocoin/go_api/api/common"
	"golang.org/x/text/language"
	"golang.org/x/text/message"

	"bitbucket.org/jogocoin/go_api/api/models"
	productModels "bitbucket.org/jogocoin/go_api/api/models/product"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type AcademyRecommendedCoursePageData struct {
	RecommendedCourseData *productPB.GetPreferredPlanResponse
}

type AcademyRecommendedCoursePostbackParams struct {
	UserId         int32                   `json:"user_id"`
	PlanStartDate  int64                   `json:"plan_start_date"`
	AcademySlotIds []int32                 `json:"academy_slot_ids"`
	AcademySlots   map[int32]*FacilitySlot `json:"-"`
}

type FacilitySlot struct {
	FsId     int32
	SlotId1  int32
	SlotId2  int32
	DaysText string
}

type AcademyRecommendedCourseTemplate struct {
	Header             *sushi.Header                                    `json:"header,omitempty"`
	BottomButtonStates *sushi.BottomButtonStates                        `json:"bottom_button_states,omitempty"`
	RecommendedPlan    *productModels.AcademyRecommendedPlan            `json:"recommended_plan,omitempty"`
	Items              []*productModels.AcademyRecommendedPlanItem      `json:"items,omitempty"`
	ItemsConfig        *productModels.AcademyRecommendedPlanItemsConfig `json:"items_config,omitempty"`
	PageRequest        *productPB.GetPreferredPlanRequest               `json:"-"`
	PageData           *AcademyRecommendedCoursePageData                `json:"-"`
	PostbackData       *AcademyRecommendedCoursePostbackParams          `json:"-"`
}

func AcademyRecommendedCourseC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	jsonReq := c.MustGet("jsonData").(structs.GetAcademyRecommendedCourse)
	log.Printf("AcademyRecommendedCourseC: Error Provider : City Id - %d",util.GetCityIDFromContext(ctx))
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId <= 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("You need to login first!"))
		return
	}

	template := &AcademyRecommendedCourseTemplate{}

	reqData := &productPB.GetPreferredPlanRequest{
		UserId: jsonReq.UserId,
	}

	if jsonReq.PostbackParams != "" {
		var postbackData AcademyRecommendedCoursePostbackParams
		err := json.Unmarshal([]byte(jsonReq.PostbackParams), &postbackData)
		if err != nil {
			log.Println("json marshal error", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		template.PostbackData = &postbackData

		courseCategoryId, err := template.GetCourseCategoryForAcademySlots(ctx, postbackData.AcademySlotIds)
		if err != nil {
			log.Println("GetCourseCategoryForAcademySlots", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		reqData.CourseCategoryId = courseCategoryId
	} else {
		reqData.CourseCategoryId = jsonReq.CourseCategoryId
	}

	productService := util.GetProductClient()
	response, err := productService.GetAcademyRecommendedCourseForUser(ctx, reqData)
	if err != nil {
		log.Println("GetAcademyRecommendedCourseForUser", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil {
		if response.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, models.StatusFailure("You are not authorised"))
			return
		}
		if response.Status.Status == common.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, models.StatusFailure(common.BAD_REQUEST))
			return
		}
		if response.Status.Status == common.FAILED {
			c.JSON(http.StatusOK, models.StatusFailure(response.Status.Message))
			return
		}
	}

	template.PageRequest = reqData
	template.PageData = &AcademyRecommendedCoursePageData{
		RecommendedCourseData: response,
	}

	template.SetHeader(ctx)
	template.SetBottomButtonStates(ctx)
	template.SetRecommendedCourse(ctx)
	template.AddPersonalInfoInput(ctx)
	template.AddStartDateInput(ctx)
	template.AddCenterTimeInput(ctx)
	template.SetItemsConfig(ctx)
	c.JSON(http.StatusOK, template)
}

func (a *AcademyRecommendedCourseTemplate) GetCourseCategoryForAcademySlots(ctx context.Context, academySlotIds []int32) (int32, error) {
	if len(academySlotIds) == 0 {
		return 0, fmt.Errorf("No academy slots provided")
	}

	academySlotsMap := make(map[int32]*FacilitySlot)

	bookingClient := util.GetBookingClient()
	getAcademySlotReq := &bookingPB.AcademySlot{
		Id:       academySlotIds[0],
		IsActive: true,
	}
	getAcademySlotRes, err := bookingClient.GetAcademySlots(ctx, getAcademySlotReq)
	if err != nil {
		log.Println(err)
		return 0, err
	}
	if len(getAcademySlotRes.AcademySlots) == 0 {
		return 0, fmt.Errorf("No active academy slot found")
	}

	academySlot := getAcademySlotRes.AcademySlots[0]
	academySlotsMap[academySlot.Id] = &FacilitySlot{
		FsId:    academySlot.FsId,
		SlotId1: academySlot.SlotId1,
		SlotId2: academySlot.SlotId2,
	}

	productClient := util.GetProductClient()
	getPcmReq := &productPB.ProductCourseDetailsRequest{
		ProductCourseMappingIds: []int32{academySlot.ProductCourseMappingId},
	}
	getPcmRes, err := productClient.GetProductCourseMappingsDetails(ctx, getPcmReq)
	if err != nil {
		log.Println(err)
		return 0, err
	}
	pcmData := getPcmRes.ProductCourseMappingsData[academySlot.ProductCourseMappingId]

	academySlotsMap[academySlot.Id].DaysText = pcmData.DaysText
	a.PostbackData.AcademySlots = academySlotsMap

	return pcmData.CourseCategoryId, nil
}

func (a *AcademyRecommendedCourseTemplate) SetHeader(ctx context.Context) {
	if a.PageData.RecommendedCourseData.User == nil {
		return
	}

	user := a.PageData.RecommendedCourseData.User
	title, _ := sushi.NewTextSnippet(fmt.Sprintf("%s, %d years", user.Name, user.Age))
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(font)
	title.SetColor(color)
	header := &sushi.Header{
		Title: title,
	}
	a.Header = header
}

func (a *AcademyRecommendedCourseTemplate) SetBottomButtonStates(ctx context.Context) {
	disabledButton := &sushi.Button{
		Type: sushi.ButtonTypeSolid,
		Text: "Save details to add member",
		BgColor: &sushi.Color{
			Tint: sushi.ColorTint400,
			Type: sushi.ColorTypeGrey,
		},
		IsActionDisabled: 1,
	}

	clickAction, _ := sushi.NewTextClickAction("save_purchase_details")
	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "academy_save_member_details_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	completedButton := &sushi.Button{
		Type: sushi.ButtonTypeSolid,
		Text: "Save details to add member",
		BgColor: &sushi.Color{
			Tint: sushi.ColorTint500,
			Type: sushi.ColorTypeRed,
		},
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button: completedButton,
		},
	}
	a.BottomButtonStates = bottomButtonStates
}

func (a *AcademyRecommendedCourseTemplate) SetRecommendedCourse(ctx context.Context) {
	if len(a.PageData.RecommendedCourseData.Products) == 0 {
		return
	}

	product := a.PageData.RecommendedCourseData.Products[0]

	if len(product.ProductCourseMappings) > 1 || len(product.ProductCourseMappings) == 0 {
		return
	}
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	title, _ := sushi.NewTextSnippet("Recommended plan")
	fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetFont(fontSemi400)

	clickAction := sushi.GetClickAction()
	userData := &sushi.UserData{
		UserId:           a.PageRequest.UserId,
		CourseCategoryId: a.PageRequest.CourseCategoryId,
	}
	preferredCourse := &sushi.OpenAcademyPreferredCourse{
		UserData:                  userData,
		IsRecommendedCourseOpened: true,
	}
	clickAction.SetOpenAcademyPreferredCourse(preferredCourse)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Change")
	button.SetClickAction(clickAction)

	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapPayload["course"] = product.CourseName
	tapPayload["course_category"] = product.CourseCategory
	tapEname := &sushi.EnameData{
		Ename: "academy_change_course_plan_tap",
	}
	shareButtonEvents := sushi.NewClevertapEvents()
	shareButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
	button.AddClevertapTrackingItem(trackItem)

	header := &sushi.Header{
		Title:      title,
		EditButton: button,
	}

	courseImage, _ := sushi.NewImage(util.GetCDNLink(product.Image))
	courseImage.SetHeight(48)
	courseImage.SetWidth(48)
	courseImage.SetAspectRatio(1)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	tagTitle, _ := sushi.NewTextSnippet(strings.ToUpper(product.CourseCategory))
	tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	tagTitle.SetColor(tagTitleColor)
	tagColor, _ := sushi.NewColor(sushi.ColorType(product.BgColor), sushi.ColorTint400)
	if isNewColorSupported {
		tagColor, _ = sushi.NewColor(sushi.ColorType(product.BgColor), sushi.ColorTint700)
	}

	tag := &sushi.Tag{
		Title:       tagTitle,
		BgColor:     tagColor,
		BorderColor: tagColor,
	}

	snippetTitle, _ := sushi.NewTextSnippet(product.CourseName)
	colorBlack900, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	snippetTitle.SetFont(fontSemi400)
	snippetTitle.SetColor(colorBlack900)

	printer := message.NewPrinter(language.Hindi)
	rightTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("₹%s", printer.Sprintf("%d", int32(product.RetailPrice))))
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	rightTitle.SetFont(fontMed400)
	rightTitle.SetColor(colorBlack900)

	rightSubtitle, _ := sushi.NewTextSnippet(product.Duration)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	colorGrey600, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	rightSubtitle.SetFont(fontMed100)
	rightSubtitle.SetColor(colorGrey600)

	snippet := &sushi.FitsoTextSnippetType8Snippet{
		IsSelected:    true,
		IsSelectable:  true,
		Image:         courseImage,
		BgColor:       bgColor,
		BorderColor:   borderColor,
		Tag:           tag,
		Title:         snippetTitle,
		RightTitle:    rightTitle,
		RightSubtitle: rightSubtitle,
	}

	var slotIdsMap map[string]int8

	var subtitle1TextArray []string
	for _, product := range a.PageData.RecommendedCourseData.Products {
		productCourseData := product.ProductCourseMappings[0]
		var slotTimings []string
		slotIdsMap = make(map[string]int8)
		for _, slot := range productCourseData.Slots {
			key := fmt.Sprintf("%d_%d", slot.SlotId1, slot.SlotId2)
			if _, ok := slotIdsMap[key]; !ok {
				if slot.SlotId2 > 0 {
					slotTimings = append(slotTimings, fmt.Sprintf("%s & %s", slot.SlotTiming1, slot.SlotTiming2))
				} else {
					slotTimings = append(slotTimings, slot.SlotTiming1)
				}
				slotIdsMap[key] = 1
			}
		}
		subtitle1TextArray = append(subtitle1TextArray, fmt.Sprintf("%s | {grey-600|<medium-100|%s>}", productCourseData.DaysText, strings.Join(slotTimings, " & ")))
	}
	subtitle1, _ := sushi.NewTextSnippet(strings.Join(subtitle1TextArray, "\n"))
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle1.SetColor(colorGrey900)
	subtitle1.SetFont(fontMed100)
	subtitle1.SetIsMarkdown(1)
	snippet.Subtitle1 = subtitle1

	a.RecommendedPlan = &productModels.AcademyRecommendedPlan{
		Header:  header,
		Snippet: snippet,
	}
}

func (a *AcademyRecommendedCourseTemplate) AddInputItem(item *productModels.AcademyRecommendedPlanItem) {
	a.Items = append(a.Items, item)
}

func (a *AcademyRecommendedCourseTemplate) AddPersonalInfoInput(ctx context.Context) {
	user := a.PageData.RecommendedCourseData.User
	nonEditableDetails := &productModels.NonEditableUserDetails{
		Name:  user.Name,
		Phone: user.Phone,
	}

	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButton.SetText("Save details")
	colorGrey400, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(colorGrey400)
	disabledButton.SetActionDisabled()

	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButton.SetText("Save details")
	colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButton.SetBgColor(colorRed500)

	m := map[string]interface{}{
		"user_id":   a.PageRequest.UserId,
		"flow_type": common.ACADEMY_PURCHASE_RECOMMENDED_FLOW,
		"name":      user.Name,
	}
	payload, _ := json.Marshal(m)

	ageEditClickAction := sushi.GetClickAction()
	apiCall := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/academy/edit-member",
		Body:        string(payload),
	}
	ageEditClickAction.SetApiCallAction(apiCall)
	completedButton.SetClickAction(ageEditClickAction)

	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button: completedButton,
		},
	}

	inputAgeAlwaysTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("age must be between %d-%d years", common.ACADEMY_MIN_AGE, common.ACADEMY_MAX_AGE))
	inputAgeEmptyTitle, _ := sushi.NewTextSnippet("Age cannot be empty")
	minAgeErrorTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age cannot be less than %d years", common.ACADEMY_MIN_AGE))
	maxAgeErrorTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age cannot be more than %d years", common.ACADEMY_MAX_AGE))
	agePlaceholderTitle, _ := sushi.NewTextSnippet("Age (in years)")

	inputFieldStates := &sushi.InputFieldStates{
		Always: &sushi.InputFieldStateTitle{
			Title: inputAgeAlwaysTitle,
		},
		Empty: &sushi.InputFieldStateTitle{
			Title: inputAgeEmptyTitle,
		},
		MinAge: &sushi.InputFieldStateAge{
			Age:        common.ACADEMY_MIN_AGE,
			ErrorTitle: minAgeErrorTitle,
		},
		MaxAge: &sushi.InputFieldStateAge{
			Age:        common.ACADEMY_MAX_AGE,
			ErrorTitle: maxAgeErrorTitle,
		},
	}
	ageInput := &sushi.InputField{
		Optional:    false,
		Placeholder: agePlaceholderTitle,
		States:      inputFieldStates,
	}
	ageInputSection := &productModels.AcademyRecommendedPlanInputSection{
		Type: "age",
		Age:  ageInput,
	}

	editInfo := &productModels.AcademyRecommendedPlanEditInfo{
		BottomButtonStates: bottomButtonStates,
		Sections:           []*productModels.AcademyRecommendedPlanInputSection{ageInputSection},
	}
	editInfoData := &productModels.AcademyRecommendedPlanPersonalInfoEdit{
		NonEditableUserDetails: nonEditableDetails,
		EditInfo:               editInfo,
	}

	expandedHeaderTitle, _ := sushi.NewTextSnippet("Personal Details")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontSemi300, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	expandedHeaderTitle.SetColor(colorGrey900)
	expandedHeaderTitle.SetFont(fontSemi300)

	expandedState := &productModels.AcademyRecommendedPlanInputExpandedState{
		Title:        expandedHeaderTitle,
		EditInfoData: editInfoData,
	}

	collapsedHeaderTitle, _ := sushi.NewTextSnippet("Personal Details")
	textColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint700)
	if featuresupport.SupportsNewColor(ctx) {
		textColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	collapsedHeaderTitle.SetColor(textColor)
	collapsedHeaderTitle.SetFont(fontMed100)

	var collapsedHeaderSubtitle *sushi.TextSnippet
	if user.Age > 0 {
		collapsedHeaderSubtitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%s, %d years", user.Name, user.Age))
	} else {
		collapsedHeaderSubtitle, _ = sushi.NewTextSnippet(user.Name)
	}
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	collapsedHeaderSubtitle.SetColor(colorGrey900)
	collapsedHeaderSubtitle.SetFont(fontMed400)

	userClient := util.GetUserServiceClient()
	isEditableRes, err := userClient.GetAcademyUserEditableBehaviour(ctx, &userPB.CheckAcademyUserEditableRequest{UserId: a.PageRequest.UserId})
	if err != nil {
		log.Println(err)
		return
	}

	editButton, _ := sushi.NewButton(sushi.ButtontypeText)
	editButton.SetText("Edit")
	if !isEditableRes.IsEditable {
		editButton.SetActionDisabled()
		editButton.SetColor(colorGrey400)
	}

	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapEname := &sushi.EnameData{
		Ename: "academy_edit_personal_info_tap",
	}
	editButtonEvents := sushi.NewClevertapEvents()
	editButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, editButtonEvents)
	editButton.AddClevertapTrackingItem(trackItem)

	closedState := &productModels.AcademyRecommendedPlanInputClosedState{
		Title:      collapsedHeaderTitle,
		Subtitle:   collapsedHeaderSubtitle,
		EditButton: editButton,
	}

	personalInfoInputItem := &productModels.AcademyRecommendedPlanItem{
		Type: "personal_info",
		PersonalInfo: &productModels.AcademyRecommendedPlanInputItem{
			ClosedState:   closedState,
			ExpandedState: expandedState,
		},
	}

	a.AddInputItem(personalInfoInputItem)
}

func (a *AcademyRecommendedCourseTemplate) AddStartDateInput(ctx context.Context) {
	planStartDate := time.Now().Unix()
	if a.PostbackData != nil && a.PostbackData.PlanStartDate > 0 {
		planStartDate = a.PostbackData.PlanStartDate
	}

	expandedTitle, _ := sushi.NewTextSnippet("Select your plan start date")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontSemi300, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	expandedTitle.SetColor(colorGrey900)
	expandedTitle.SetFont(fontSemi300)

	tooltipIcon := a.GetStartDateTooltipIcon(ctx)

	inputDateAlwaysTitle, _ := sushi.NewTextSnippet("you can choose plan start date upto 2 weeks from today")
	inputFieldStates := &sushi.InputFieldStates{
		Always: &sushi.InputFieldStateTitle{
			Title: inputDateAlwaysTitle,
		},
	}

	disabledButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	disabledButton.SetText("Save and proceed")
	colorGrey400, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabledButton.SetBgColor(colorGrey400)
	disabledButton.SetActionDisabled()

	completedButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	completedButton.SetText("Save and proceed")
	colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	completedButton.SetBgColor(colorRed500)

	tapPayload := a.GetCommonPayloadForTracking(ctx)
	tapSaveEname := &sushi.EnameData{
		Ename: "academy_save_plan_start_date_tap",
	}
	saveButtonEvents := sushi.NewClevertapEvents()
	saveButtonEvents.SetTap(tapSaveEname)
	tracksItem := sushi.GetClevertapTrackItem(ctx, tapPayload, saveButtonEvents)
	completedButton.AddClevertapTrackingItem(tracksItem)

	bottomButtonStates := &sushi.BottomButtonStates{
		Disabled: &sushi.BottomButtonState{
			Button: disabledButton,
		},
		Completed: &sushi.BottomButtonState{
			Button: completedButton,
		},
	}

	timeObj := time.Unix(planStartDate, 0)
	timeString, _ := sushi.NewTextSnippet(timeObj.Format("02 Jan 2006"))
	timeString.SetColor(colorGrey900)
	timeString.SetFont(fontSemi300)

	expandedEditButton, _ := sushi.NewButton(sushi.ButtontypeText)
	expandedEditButton.SetText("Change")

	placeholder := &productModels.AcademyRecommendedPlanPlaceholder{
		Title:      timeString,
		EditButton: expandedEditButton,
	}
	productStartDate := time.Now().Unix()

	expandedState := &productModels.AcademyRecommendedPlanInputExpandedState{
		Title:                expandedTitle,
		Icon:                 tooltipIcon,
		PlaceholderContainer: placeholder,
		States:               inputFieldStates,
		BottomButtonStates:   bottomButtonStates,
		DurationInDays:       int32(14),
		StartDate:            planStartDate,
		ProductStartDate:     productStartDate,
	}

	collapsedHeaderTitle, _ := sushi.NewTextSnippet("Plan start date")
	textColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint700)
	if featuresupport.SupportsNewColor(ctx) {
		textColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	collapsedHeaderTitle.SetColor(textColor)
	collapsedHeaderTitle.SetFont(fontMed100)

	collapsedHeaderSubtitle, _ := sushi.NewTextSnippet(timeObj.Format("02 Jan, 2006"))
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	collapsedHeaderSubtitle.SetColor(colorGrey900)
	collapsedHeaderSubtitle.SetFont(fontMed400)

	collapsedEditButton, _ := sushi.NewButton(sushi.ButtontypeText)
	collapsedEditButton.SetText("Edit")
	tapEname := &sushi.EnameData{
		Ename: "academy_edit_plan_start_date_tap",
	}
	editButtonEvents := sushi.NewClevertapEvents()
	editButtonEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, editButtonEvents)
	collapsedEditButton.AddClevertapTrackingItem(trackItem)

	closedState := &productModels.AcademyRecommendedPlanInputClosedState{
		Title:      collapsedHeaderTitle,
		Subtitle:   collapsedHeaderSubtitle,
		EditButton: collapsedEditButton,
	}

	startDateInputItem := &productModels.AcademyRecommendedPlanItem{
		Type: "start_date",
		StartDate: &productModels.AcademyRecommendedPlanInputItem{
			ClosedState:   closedState,
			ExpandedState: expandedState,
		},
	}

	a.AddInputItem(startDateInputItem)
}

func (a *AcademyRecommendedCourseTemplate) AddCenterTimeInput(ctx context.Context) {
	expandedHeaderTitle, _ := sushi.NewTextSnippet("Select your center")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontSemi300, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	expandedHeaderTitle.SetColor(colorGrey900)
	expandedHeaderTitle.SetFont(fontSemi300)

	placeholderTitle, _ := sushi.NewTextSnippet("Select center")
	colorRed500, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	placeholderTitle.SetColor(colorRed500)
	placeholderTitle.SetFont(fontMed400)
	icon, _ := sushi.NewIcon(sushi.RightIcon, colorRed500)

	planStartDate := time.Now().Unix()
	if a.PostbackData != nil && a.PostbackData.PlanStartDate > 0 {
		planStartDate = a.PostbackData.PlanStartDate
	}
	productStartDate := time.Now().Unix()
	payload := map[string]interface{}{
		"course_category_id": a.PageRequest.CourseCategoryId,
		"plan_start_date":    planStartDate,
		"product_start_date": productStartDate,
		"user_id":            a.PageRequest.UserId,
	}
	params, _ := json.Marshal(payload)

	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL:            util.GetAcademyPurchaseFacilitiesDeeplink(),
		PostbackParams: string(params),
	}
	clickAction.SetDeeplink(deeplink)

	container := &productModels.AcademyRecommendedPlanPlaceholder{
		Title:       placeholderTitle,
		Icon:        icon,
		ClickAction: clickAction,
	}

	expandedState := &productModels.AcademyRecommendedPlanInputExpandedState{
		Title:                expandedHeaderTitle,
		PlaceholderContainer: container,
	}

	collapsedHeaderTitle, _ := sushi.NewTextSnippet("Center & slot")
	textColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint700)
	if featuresupport.SupportsNewColor(ctx) {
		textColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	collapsedHeaderTitle.SetColor(textColor)
	collapsedHeaderTitle.SetFont(fontMed100)

	collapsedEditButton, _ := sushi.NewButton(sushi.ButtontypeText)
	collapsedEditButton.SetText("Edit")
	collapsedEditButton.SetClickAction(clickAction)

	var snippet *sushi.V2ImageTextSnippetType31SnippetItem
	if a.PostbackData != nil && len(a.PostbackData.AcademySlots) > 0 {
		for academySlotId, facilitySlot := range a.PostbackData.AcademySlots {
			snippetX, err := a.GetFacilitySnippetForAcademySlot(ctx, academySlotId, facilitySlot)
			if err != nil {
				return
			}
			snippet = snippetX
			break
		}
	}

	closedState := &productModels.AcademyRecommendedPlanInputClosedState{
		Title:      collapsedHeaderTitle,
		EditButton: collapsedEditButton,
		Snippet:    snippet,
	}

	centerSlotInputItem := &productModels.AcademyRecommendedPlanItem{
		Type: "center_time",
		CenterTime: &productModels.AcademyRecommendedPlanInputItem{
			ExpandedState: expandedState,
			ClosedState:   closedState,
		},
	}

	a.AddInputItem(centerSlotInputItem)
}

func (a *AcademyRecommendedCourseTemplate) SetItemsConfig(ctx context.Context) {
	iconColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		iconColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}
	completeIcon, _ := sushi.NewIcon(sushi.TickMarkIconV2, iconColor)
	incompleteIcon, _ := sushi.NewIcon(sushi.EmptyCircleIcon, iconColor)

	itemConfig := &productModels.AcademyRecommendedPlanItemsConfig{
		IncompleteIcon:  incompleteIcon,
		CompleteIcon:    completeIcon,
		DashedLineColor: iconColor,
	}
	a.ItemsConfig = itemConfig
}

func (a *AcademyRecommendedCourseTemplate) GetStartDateTooltipIcon(ctx context.Context) *sushi.Icon {
	title, _ := sushi.NewTextSnippet("You can choose plan start date upto 2 weeks from today.")
	colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fontMed300, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetColor(colorWhite500)
	title.SetFont(fontMed300)

	subtitle, _ := sushi.NewTextSnippet("OK")
	subtitle.SetColor(colorWhite500)
	subtitle.SetFont(fontMed300)

	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

	showTooltip := &sushi.ShowTooltip{
		Title:    title,
		Subtitle: subtitle,
		BgColor:  colorBlack500,
	}
	clickAction := sushi.GetClickAction()
	clickAction.SetShowTooltip(showTooltip)

	icon, _ := sushi.NewIcon(sushi.InfoIcon, nil)
	icon.SetClickAction(clickAction)

	return icon
}

func (a *AcademyRecommendedCourseTemplate) GetFacilitySnippetForAcademySlot(
	ctx context.Context,
	academySlotId int32,
	facilitySlot *FacilitySlot,
) (*sushi.V2ImageTextSnippetType31SnippetItem, error) {

	facilityClient := util.GetFacilitySportClient()
	fsDataReq := &facilitySportPB.FacilitySportsByFsIdRequest{
		FsIds: []int32{facilitySlot.FsId},
	}
	fsDataRes, err := facilityClient.GetFacilitySportsDetailsByFsID(ctx, fsDataReq)
	if err != nil {
		return nil, err
	}
	if len(fsDataRes.FacilitySports) == 0 {
		return nil, fmt.Errorf("Unable to fetch fs details")
	}

	slotIds := []int32{facilitySlot.SlotId1}
	if facilitySlot.SlotId2 > 0 {
		slotIds = append(slotIds, facilitySlot.SlotId2)
	}
	slotDataReq := &facilitySportPB.BatchSlotGetRequest{
		SlotIds: slotIds,
	}
	slotDataRes, err := facilityClient.BatchSlotGet(ctx, slotDataReq)
	if err != nil {
		return nil, err
	}
	if len(slotDataRes.SlotsMap) == 0 {
		return nil, fmt.Errorf("Unable to fetch slots details")
	}

	fsData := fsDataRes.FacilitySports[0]
	facility := fsData.Facility[0]

	var timings []string
	for _, v := range slotIds {
		if v == 12 {
			timings = append(timings, "4 - 5 PM")
		} else if v == 13 {
			timings = append(timings, "5 - 6 PM")
		} else {
			timings = append(timings, slotDataRes.SlotsMap[v].Timing)
		}
	}
	timingStr := strings.Join(timings, " & ")

	title, _ := sushi.NewTextSnippet(facility.DisplayName)
	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetFont(fontSemi400)
	title.SetColor(colorBlack500)

	subtitle, _ := sushi.NewTextSnippet(facility.ShortAddress)
	colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	subtitle.SetColor(colorGrey500)
	subtitle.SetFont(fontMed200)

	ratingTitle, _ := sushi.NewTextSnippet(facility.Rating)
	colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	prefixIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
	ratingTitle.SetColor(colorWhite900)
	ratingTitle.SetFont(fontMed100)
	ratingTitle.SetPrefixIcon(prefixIcon)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}

	ratingObj := &sushi.RatingSnippetBlockItem{
		Title:   ratingTitle,
		BgColor: bgColor,
	}

	image, _ := sushi.NewImage(facility.DisplayPicture)
	image.SetHeight(64)
	image.SetWidth(64)
	image.SetAspectRatio(1)
	image.SetType(sushi.ImageTypeRounded)

	bottomTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s at %s", facilitySlot.DaysText, timingStr))
	bottomTitle.SetFont(fontMed200)
	bottomTitle.SetColor(colorBlack500)
	bottomContainer := &sushi.BottomContainerSnippetItem{
		Title: bottomTitle,
	}

	snippet := &sushi.V2ImageTextSnippetType31SnippetItem{
		Id:              academySlotId,
		Title:           title,
		Subtitle:        subtitle,
		Rating:          ratingObj,
		Image:           image,
		BottomContainer: bottomContainer,
	}
	return snippet, nil
}

func (p *AcademyRecommendedCourseTemplate) GetCommonPayloadForTracking(ctx context.Context) map[string]interface{} {
	commonPayload := make(map[string]interface{})
	commonPayload["p_user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
	commonPayload["source"] = "academy_plan_details_page"
	return commonPayload
}
