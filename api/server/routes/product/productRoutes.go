// productRoutes.go

package product

import (
	commonController "bitbucket.org/jogocoin/go_api/api/server/routes/product/commonController"
	productController "bitbucket.org/jogocoin/go_api/api/server/routes/product/controllers"
	productResource "bitbucket.org/jogocoin/go_api/api/server/routes/product/resources"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func ProductRouteV1(routerVersion *gin.RouterGroup) {
	// Group product related routes together

	productRoutes := routerVersion.Group("/product")
	{
		productRoutes.GET("/get", sharedFunc.ValidateDashboardUser, productResource.GetR, productController.GetC)
		productRoutes.POST("/createUpdateProduct", sharedFunc.ValidateDashboardUser, productResource.CreateUpdateProductR, productController.CreateUpdateProductC)
		productRoutes.GET("/getProductCategories", productResource.GetProductCategoriesR, productController.GetProductCategoriesC)
		productRoutes.GET("/getProductLink", productResource.GetProductLinkR, productController.GetProductLinkC)
		productRoutes.POST("/generateBulkPurchaseLink", sharedFunc.ValidateDashboardUser, productResource.GenerateBulkPurchaseLinkR, productController.GenerateBulkPurchaseLinkC)
		productRoutes.GET("/getSubscription", sharedFunc.ValidateDashboardUser, productResource.GetSubscriptionR, productController.GetSubscriptionC)
		productRoutes.POST("/isVoucherValid", productResource.IsVoucherValidR, productController.IsVoucherValidC)
		productRoutes.POST("/createSubscription", sharedFunc.ValidateDashboardUser, productResource.CreateSubscriptionR, productController.CreateSubscriptionC)
		productRoutes.GET("/getSportsForSubscription", productResource.GetSportsForSubscriptionR, productController.GetSportsForSubscriptionC)
		productRoutes.GET("/getFacilitiesForSubscription", productResource.GetFacilitiesForSubscriptionR, productController.GetFacilitiesForSubscriptionC)
		productRoutes.POST("/createProductFSMapping", sharedFunc.ValidateDashboardUser, productResource.CreateProductFSMappingR, productController.CreateProductFSMappingC)
		productRoutes.POST("/updateSubscription", sharedFunc.ValidateDashboardUser, productResource.UpdateSubscriptionR, productController.UpdateSubscriptionC)
		productRoutes.GET("/getTrialDetailsForUser", productResource.GetTrialDetailsForUserR, productController.GetTrialDetailsForUserC)
		productRoutes.GET("/getAboutFitsoDetails", productResource.GetAboutFitsoDetailsR, productController.GetAboutFitsoDetailsC)
		productRoutes.GET("/getHomeGroundDetails", productResource.GetHomeGroundDetailsR, productController.GetHomeGroundDetailsC)
		productRoutes.GET("/getHomeGroundOfferedSportDetails", productResource.GetHomeGroundOfferedSportDetailsR, productController.GetHomeGroundOfferedSportDetailsC)
		productRoutes.POST("/markFreezeForSubscription", sharedFunc.ValidateDashboardUser, productResource.MarkFreezeR, productController.MarkFreezeC)
		productRoutes.GET("/getPlanStatusForUser", productResource.GetPlanStatusForUserR, productController.GetPlanStatusForUserC)
		productRoutes.GET("/getDetailsForMarkingFreeze", productResource.GetDetailsForMarkingFreezeR, sharedFunc.SetUserIDInContextFromToken, productController.GetDetailsForMarkingFreezeC)
		productRoutes.POST("/createOrUpdateVouchers", sharedFunc.ValidateDashboardUser, productResource.CreateOrUpdateVouchersR, productController.CreateOrUpdateVouchersC)
		productRoutes.POST("/generateOrGetReferralCode", sharedFunc.ValidateDashboardUser, productResource.GenerateOrGetReferralCodeR, productController.GenerateOrGetReferralCodeC)
		productRoutes.GET("/getFreezeLogsForSubscription", productResource.GetFreezeLogsForSubscriptionR, productController.GetFreezeLogsForSubscriptionC)
		productRoutes.GET("/getFreezeReasons", productResource.GetFreezeReasonsR, productController.GetFreezeReasonsC)
		productRoutes.GET("/getPlanUtilizationDetails", productResource.GetPlanUtilizationR, productController.GetPlanUtilizationC)
		productRoutes.GET("/getSportsOffers", productResource.GetSportsOffersR, productController.GetSportsOffersC)
		productRoutes.GET("/getSportsOfferCampaigns", productResource.GetSportsOfferCampaignsR, productController.GetSportsOfferCampaignsC)
		productRoutes.POST("/createUpdateSportsOffer", sharedFunc.ValidateDashboardUser, productResource.CreateUpdateSportsOfferR, productController.CreateUpdateSportsOfferC)
		productRoutes.POST("/createUpdateSportsOfferCampaign", sharedFunc.ValidateDashboardUser, productResource.CreateUpdateSportsOfferCampaignR, productController.CreateUpdateSportsOfferCampaignC)
		productRoutes.GET("/getVouchers", productResource.GetVouchersR, productController.GetVouchersC)
		productRoutes.POST("/unfreezeSubscription", productResource.UnfreezeSubscriptionR, productController.UnfreezeSubscriptionC)
		productRoutes.GET("/getRenewalCharacterstics", productResource.GetRenewalCharactersticsR, productController.GetRenewalCharactersticsC)
		productRoutes.POST("/postFreezeOrUnfreezeDetails", productResource.PostFreezeOrUnfreezeDetailsR, productController.PostFreezeOrUnfreezeDetailsC)
		productRoutes.GET("/getFacilitiesForProduct", productResource.GetFacilitiesForProductR, productController.GetFacilitiesForProductC)
		productRoutes.GET("/getProductFacilitySport", productResource.GetProductFacilitySportR, productController.GetProductFacilitySportC)
		productRoutes.POST("/updateProductFacilitySport", sharedFunc.ValidateDashboardUser, productResource.UpdateProductFacilitySportR, productController.UpdateProductFacilitySportC)
		productRoutes.GET("/getTimelineForSubscription", sharedFunc.ValidateDashboardUser, productResource.GetTimelineForSubscriptionR, productController.GetTimelineForSubscriptionC)
		productRoutes.POST("/optInSubscription", productResource.OptInSubscriptionR, productController.OptInSubscriptionC)
		productRoutes.POST("/insertHomeGroundLead", productResource.InsertHomeGroundLeadR, productController.InsertHomeGroundLeadC)
		productRoutes.POST("/noShowPenaltyApply", productController.NoShowPenaltyApplyC)
		productRoutes.POST("/sealsUpgrade", productResource.SealsUpgradeR, productController.SealsUpgradeC)
		productRoutes.GET("/getDurations", productResource.GetDurationsR, productController.GetDurationsC)
		productRoutes.GET("/getReferralParams", productController.GetReferralParamsC)
		productRoutes.POST("/uploadImageToS3Bucket", sharedFunc.ValidateDashboardUser, productResource.UploadImageToS3BucketR, productController.UploadImageToS3BucketC)
		productRoutes.GET("/locations/active", productController.GetActiveLocationsC)
		productRoutes.POST(
			"/lead-squared/lead/push",
			sharedFunc.ValidateTokenShared,
			productResource.PushLeadToLeadSquaredCaptureR,
			productController.PushLeadToLeadSquaredCaptureC,
		)
		productRoutes.GET(
			"/getPurchasePageDetails",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productResource.GetPurchasePageDetailsR,
			commonController.PurchasePageMKandAcademyC,
		)
		productRoutes.POST(
			"/calculateCart",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productResource.CalculateCartR,
			productController.CalculateCartC,
		)
		productRoutes.GET(
			"/getBuyOrRenewDetails",
			productResource.GetBuyOrRenewDetailsR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productController.GetBuyOrRenewDetailsC,
		)
		productRoutes.GET(
			"/getMembershipDetails",
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productResource.GetMembershipDetailsR,
			sharedFunc.SetUserIDInContextFromToken,
			productController.GetMembershipDetails,
		)
		productRoutes.POST(
			"/cancel/membership",
			productResource.CancelMembershipR,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetUserIDInContextFromToken,
			productController.CancelMembershipC,
		)
		productRoutes.GET(
			"/membership/cancellation/details",
			productResource.MembershipCancellationDetailsR,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetUserIDInContextFromToken,
			productController.MembershipCancellationDetailsC,
		)
		productRoutes.GET(
			"/academy/preferredPlans",
			productResource.AcademyPreferredPlansR,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetUserIDInContextFromToken,
			commonController.PreferredPlansC,
		)
		productRoutes.POST(
			"/academy/recommended-course",
			productResource.AcademyRecommendedCourseR,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetUserIDInContextFromToken,
			commonController.RecommendedCourseC,
		)
		productRoutes.POST(
			"/academy/calculate-cart",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productResource.CalculateAcademyCartR,
			commonController.CalculateCartCommonC,
		)
		productRoutes.POST(
			"/refer-and-earn",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productController.ReferAndEarnDetailsC,
		)
		productRoutes.GET("/clevertap",
			productController.TestClevertapC,
		)
		productRoutes.GET("/featured/list",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			productController.GetFeaturedProductsForCityIdC,
		)
	}
}

func ProductRouteV2(routerVersion *gin.RouterGroup) {
	// Group product related routes together

	productRoutes := routerVersion.Group("/product")
	{
		productRoutes.GET("/getSubscription", productResource.GetSubscriptionR, sharedFunc.SetUserIDInContextFromToken, productController.GetSubscriptionCV2)
		productRoutes.GET("/getFacilitiesForSubscription", productResource.GetFacilitiesForSubscriptionV2R, productController.GetFacilitiesForSubscriptionV2C)
		productRoutes.POST(
			"/academy/preferredPlans",
			productResource.AcademyPreferredPlansV2R,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetUserIDInContextFromToken,
			commonController.PreferredPlansC,
		)
	}
}
