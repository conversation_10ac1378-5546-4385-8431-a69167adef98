package bookingResource

import (
	"fmt"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	validator "gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func GetBookingDetailsR(c *gin.Context) {
	validate = validator.New()

	var json structs.Booking

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetBookingDetailsV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.Booking

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func PublishBookingR(c *gin.Context) {
	validate = validator.New()

	fmt.Println("---------")

	var json structs.PublishBooking

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	fmt.Println("jsonData", json)
	c.Set("jsonData", json)

	c.Next()
}

func PublishBookingV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.PublishBookingV2

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func ListBookingBuddiesR(c *gin.Context) {
	validate = validator.New()

	var json structs.ListBookingBuddiesRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func PublishLiveSessionBookingR(c *gin.Context) {
	validate = validator.New()

	fmt.Println("---------")

	var json structs.PublishBooking

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	fmt.Println("jsonData", json)
	c.Set("jsonData", json)

	c.Next()
}
func GetDataForBookingCreationR(c *gin.Context) {
	validate = validator.New()

	var json structs.BookingDatesGet

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetBookingSlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.BookingSlots

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CancelBookingR(c *gin.Context) {
	validate = validator.New()

	var json structs.CancelBooking

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CancelBookingV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.BookingCancelRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("error in binding cancel request struct : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Printf("error in validating cancel booking struct %v: ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func ModifyBookingR(c *gin.Context) {
	validate = validator.New()

	var json structs.ModifyBooking

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetBookingSlotsV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.BookingSlotsV2

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func BookingFeedbackOptionsR(c *gin.Context) {
	validate = validator.New()

	var json structs.BookingFeedbackOptions

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func SaveBookingFeedbackR(c *gin.Context) {
	validate = validator.New()

	var json structs.SaveBookingFeedback

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func SaveBookingFeedbackV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.SaveBookingFeedback

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func BlockInventoryR(c *gin.Context) {
	validate = validator.New()

	var json structs.BlockInventory

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetBlockedInventoryR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetBlockedInventory

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func FeedbackListingR(c *gin.Context) {
	validate = validator.New()

	var json structs.FeedbackListing

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func BookingSlotsDetailsR(c *gin.Context) {
	validate = validator.New()
	log.Println("InResource")
	var json structs.BookingSlotsDetails

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func BookingSlotsDetailsV2R(c *gin.Context) {
	validate = validator.New()

	var json structs.BookingSlotsDetailsV2

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func MarkAttendanceStatusR(c *gin.Context) {
	validate = validator.New()

	var json structs.MarkAttendance

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func UnblockBlockedInventoryR(c *gin.Context) {

	validate = validator.New()

	var json structs.UnblockInventoryRequest

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()

}

func UpsertSportsInactiveDaysR(c *gin.Context) {

	validate = validator.New()

	var json structs.SportsInactiveDayRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("UpsertSportsInactiveDaysR: error in binding data into json : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("UpsertSportsInactiveDaysR:  error in validating data into json : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()

}

func GetInactiveDaysR(c *gin.Context) {

	validate = validator.New()

	var json structs.GetInactiveDaysRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Printf("GetInactiveDaysR: error in binding query into json : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("GetInactiveDaysR:  error in validating data into json : %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()

}

func GetStreamDetailsR(c *gin.Context) {

	validate = validator.New()

	var json structs.StreamDetailsRequest

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()

}

func GetRecommendedSlotsR(c *gin.Context) {

	validate = validator.New()

	var json structs.GetRecommendedSlots

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()

}

func MarkOnlineSessionAttendanceR(c *gin.Context) {

	validate = validator.New()

	var json structs.OnlineSessionAttendance

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func AutoPopulateSlotR(c *gin.Context) {

	c.Next()
}

func GetNoShowPenaltyRecordR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetNoShowPenaltyRecord

	if err := c.ShouldBindQuery(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}
func NoShowRevertR(c *gin.Context) {
	validate = validator.New()

	var json structs.NoShowRevertRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err - Bind : ", err)
	}

	fmt.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err - Validation : ", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetActiveSlotsR(c *gin.Context) {
	validate = validator.New()
	var json structs.ActiveSlots

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}
	c.Set("jsonData", json)
	c.Next()
}

func UserQueryNoShowRevertR(c *gin.Context) {
	validate = validator.New()
	var json structs.UserQuery

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
	}
	c.Set("jsonData", json)
	c.Next()
}

func UserQueryNoShowRevertV2R(c *gin.Context) {
	validate = validator.New()
	var json structs.UserQuery

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetSlotPopulationStatusR(c *gin.Context) {
	c.Next()
}

func GetBookingSlotsV3R(c *gin.Context) {
	validate = validator.New()
	var json structs.BookingSlotsV3

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetBookingFeedbackOptionsV2R(c *gin.Context) {
	validate = validator.New()
	var json structs.BookingFeedbackOptions

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	log.Println("query data: ", json)

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetFitsoCentresForSlotBookingR(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	if util.GetAppTypeFromContext(ctx) == featuresupport.Android {
		var json structs.GetCentresForSlotBookingReqV2
		validate = validator.New()

		if err := c.ShouldBindJSON(&json); err != nil {
			log.Println("err - Bind : ", err)
			c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
			return
		}

		if err := validate.Struct(&json); err != nil {
			log.Println("err - Validation : ", err)
			c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
			return
		}

		c.Set("jsonData", json)
		c.Next()
	} else {
		var json structs.GetCentresForSlotBookingReq
		validate = validator.New()

		if err := c.ShouldBindJSON(&json); err != nil {
			log.Println("err - Bind : ", err)
			c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
			return
		}

		if err := validate.Struct(&json); err != nil {
			log.Println("err - Validation : ", err)
			c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
			return
		}

		c.Set("jsonData", json)
		c.Next()
	}
}

func GetUserBookingsR(c *gin.Context) {
	validate = validator.New()
	var json structs.UserBookingsRequest

	log.Println("Func:GetUserBookingsR, Query data", json)
	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Error binding in GetUserBookingsR, Error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error validating in GetUserBookingsR, Error: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
	}

	if json.ProductCategoryId == common.AcademyCategoryID {
		requestHeaders := c.Value("requestHeaders").(structs.Headers)
		requestHeaders.FeatureType = common.Academy
		c.Set(util.RequestHeadersCtx, requestHeaders)
	}

	c.Set("jsonData", json)
	c.Next()
}

func AddEditCapacitySlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.PlayArenaCapacitySlotRequest

	if err := c.ShouldBind(&json); err != nil {
		log.Println("err - Bind : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Println("err - Validation : ", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetSessionCountR(c *gin.Context) {
	validate = validator.New()
	var json structs.SessionCountRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Function:GetSessionCountR, Error:%v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	log.Println("Func:GetSessionCountR, Query data", json)

	if err := validate.Struct(&json); err != nil {
		log.Printf("Function:GetSessionCountR, Error:%v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func AssignCoachToBookingR(c *gin.Context) {
	validate = validator.New()
	var json structs.AssignCoachToBookingRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Function:AssignCoachToBookingR, Error:%v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	log.Println("Func:AssignCoachToBookingR, Query data", json)

	if err := validate.Struct(&json); err != nil {
		log.Printf("Function:AssignCoachToBookingR, Error:%v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func UpsertFMRemarkR(c *gin.Context) {
	validate = validator.New()
	var json structs.UpsertFMRemarkRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Function:UpsertFMRemarkR, Error:%v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	log.Println("Func:UpsertFMRemarkR, Query data", json)

	if err := validate.Struct(&json); err != nil {
		log.Printf("Function:UpsertFMRemarkR, Error:%v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetAcademyProductCentersR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetCentersForProductCourseReq

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Error in binding json for GetAcademyProductCentersR: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error in validating json for GetAcademyProductCentersR: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetAcademyProductSlotsR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetSlotsForProductCourseReq

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Error in binding json for GetAcademyProductSlotsR: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error in validating json for GetAcademyProductSlotsR: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetFacilitiesSlotsCapacityR(c *gin.Context) {
	validate = validator.New()
	var json structs.GetFacilitiesSlotsCapacityRequest

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Printf("Error in binding request in GetFacilitiesSlotsCapacityR: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("Error in validating request in GetFacilitiesSlotsCapacityR: %v", err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)
	c.Next()
}

func GetAcademySlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.Booking

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Printf("func:GetAcademySlotsR: Error in Academy Slots for FSID %d err: ", json.FSID, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:GetAcademySlotsR:  Error in Academy Slots for FSID %d err: ", json.FSID, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func AddEditAcademySlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.AddEditAcademySlots

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("func:AddEditAcademySlotsR:  Error in Add/Edit Academy Slots for FsId %d err: ", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:AddEditAcademySlotsR:  Error in Add/Edit Academy Slots for FSID %d err: ", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetRelatedAcademySlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetRelatedAcademySlotsByProductAndFsReq

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Printf("func:GetRelatedAcademySlotsR: Error in binding request params for Academy Slots for FSID %d, SPORTID %d, PRODUCTID %d, err: %v", json.FsId, json.SportId, json.ProductId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:GetRelatedAcademySlotsR: Error in validating request params for Academy Slots for FSID %d, SPORTID %d, PRODUCTID %d, err: %v", json.FsId, json.SportId, json.ProductId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func GetSummerCampSlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.GetSummerCampSlotsByProductAndFsReq

	if err := c.ShouldBindQuery(&json); err != nil {
		log.Printf("func:GetSummerCampSlotsR: Error in binding request params for summer camp Slots for FSID %d, SPORTID %d, PRODUCTID %d, err: %v", json.FsId, json.SportId, json.ProductId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:GetSummerCampSlotsR: Error in validating request params for summer camp Slots for FSID %d, SPORTID %d, PRODUCTID %d, err: %v", json.FsId, json.SportId, json.ProductId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func AddEditSummerCampSlotsR(c *gin.Context) {
	validate = validator.New()

	var json structs.AddEditSummerCampSlots

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("func:AddEditAcademySlotsR:  Error in Add/Edit Summercamp Slots for FsId %d err: ", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:AddEditAcademySlotsR:  Error in Add/Edit Summercamp Slots for FSID %d err: ", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func AcademyBookingCreationCultR(c *gin.Context) {
	validate = validator.New()

	var json structs.AcademySessionNotification

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("func:AcademyBookingCreationCultR:  Error in sending notification academy for userId %d err: ", json.UserId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:AcademyBookingCreationCultR:  Error in sending notification academy for userId %d err: ", json.UserId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}

func CorrectAcademySlotCapacityR(c *gin.Context) {
	validate = validator.New()

	var json structs.CorrectAcademySlots

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("func:CorrectAcademySlotCapacityR:  Error in correct Slots for FsId %d err: ", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if err := validate.Struct(&json); err != nil {
		log.Printf("func:CorrectAcademySlotCapacityR:  Error in correct Slots for FSID %d err: ", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	c.Set("jsonData", json)

	c.Next()
}