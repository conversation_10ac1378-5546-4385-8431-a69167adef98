package commonController

import (
	"bitbucket.org/jogocoin/go_api/api/common"
	bookingController "bitbucket.org/jogocoin/go_api/api/server/routes/booking/controllers"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func CentersAcademyAndMasterkeyC(c *gin.Context) {
	var productCategegoryId int32
	ctx := util.PrepareGRPCMetaData(c)
	if util.GetAppTypeFromContext(ctx) == featuresupport.Android {
		json := c.MustGet("jsonData").(structs.GetCentresForSlotBookingReqV2)
		productCategegoryId = json.ProductCategoryId
	} else {
		json := c.MustGet("jsonData").(structs.GetCentresForSlotBookingReq)
		productCategegoryId = json.ProductCategoryId
	}
	if productCategegoryId == common.AcademyCategoryID {
		bookingController.GetAcademyCentresForSlotBookingC(c)
	} else if productCategegoryId == common.SummerCampCategoryID {
		bookingController.GetSummerCampCentersForBookingC(c)
	} else {
		bookingController.GetFitsoCentresForSlotBookingC(c)
	}
}
