package commonController

import (
	bookingController "bitbucket.org/jogocoin/go_api/api/server/routes/booking/controllers"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func SlotsAcademyAndMasterkeyC(c *gin.Context) {
	var json structs.BookingSlotsV3
	json = c.MustGet("jsonData").(structs.BookingSlotsV3)
	if json.ProductCategoryId == common.AcademyCategoryID {
		bookingController.GetAcademyTrialSlotsC(c)
	} else if json.ProductCategoryId == common.SummerCampCategoryID {
		bookingController.GetSummerCampSlotsC(c)
	} else {
		bookingController.GetBookingSlotsV3C(c)
	}
}
