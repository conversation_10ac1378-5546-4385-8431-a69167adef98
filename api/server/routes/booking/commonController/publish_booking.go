package commonController

import (
	common "bitbucket.org/jogocoin/go_api/api/common"
	bookingController "bitbucket.org/jogocoin/go_api/api/server/routes/booking/controllers"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func PublishBookingCommonC(c *gin.Context) {
	json := c.MustGet("jsonData").(structs.PublishBookingV2)
	if json.ProductCategoryId == common.AcademyCategoryID {
		bookingController.AcademyPublishBookingC(c)
	} else if json.ProductCategoryId == common.SummerCampCategoryID {
		bookingController.SummerCampPublishBookingC(c)
	} else {
		bookingController.PublishBookingV2C(c)
	}
}
