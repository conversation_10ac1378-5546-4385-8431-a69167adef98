package commonController

import (
	bookingController "bitbucket.org/jogocoin/go_api/api/server/routes/booking/controllers"
	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func GetProductCentersC(c *gin.Context) {
	var json structs.GetCentersForProductCourseReq
	json = c.MustGet("jsonData").(structs.GetCentersForProductCourseReq)
	if json.ProductCategoryId == common.SummerCampCategoryID {
		bookingController.GetSummercampProductCentersC(c)
	} else {
		bookingController.GetAcademyProductCentersC(c)
	}
}
