// bookingRoutes.go

package booking

import (
	commonController "bitbucket.org/jogocoin/go_api/api/server/routes/booking/commonController"
	bookingController "bitbucket.org/jogocoin/go_api/api/server/routes/booking/controllers"
	bookingResource "bitbucket.org/jogocoin/go_api/api/server/routes/booking/resources"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func BookingRoute(routerVersion *gin.RouterGroup) {
	// Group auth related routes together

	bookingRoutes := routerVersion.Group("/booking")
	{
		bookingRoutes.POST("/publishBooking", bookingResource.PublishBookingR, sharedFunc.SetUserIDInContextFromToken, bookingController.PublishBookingC)
		bookingRoutes.POST("/publishLiveSessionBooking", bookingResource.PublishLiveSessionBookingR, bookingController.PublishLiveSessionBookingC)
		bookingRoutes.GET("/getDataForBookingCreation", bookingResource.GetDataForBookingCreationR, bookingController.GetDataForBookingCreationC)
		bookingRoutes.GET("/getBookingSlots", bookingResource.GetBookingSlotsR, bookingController.GetBookingSlotsC)
		bookingRoutes.POST("/cancelBooking", bookingResource.CancelBookingR, sharedFunc.ValidateTokenShared, sharedFunc.SetUserIDInContextFromToken, bookingController.CancelBookingC)
		bookingRoutes.POST("/modifyBooking", bookingResource.ModifyBookingR, bookingController.ModifyBookingC)
		bookingRoutes.GET("/getBookingFeedbackOptions", bookingResource.BookingFeedbackOptionsR, bookingController.BookingFeedbackOptionsC)
		bookingRoutes.POST("/saveBookingFeedback", bookingResource.SaveBookingFeedbackR, sharedFunc.SetUserIDInContextFromToken, bookingController.SaveBookingFeedbackC)
		bookingRoutes.POST("/blockInventory", sharedFunc.ValidateDashboardUser, bookingResource.BlockInventoryR, bookingController.BlockInventoryC)
		bookingRoutes.GET("/getBlockedInventory", sharedFunc.ValidateDashboardUser, bookingResource.GetBlockedInventoryR, bookingController.GetBlockedInventoryC)
		bookingRoutes.POST("/unblockBlockedInventory", sharedFunc.ValidateDashboardUser, bookingResource.UnblockBlockedInventoryR, bookingController.UnblockBlockedInventoryC)
		bookingRoutes.POST("/sportsInactiveDays/upsert", sharedFunc.ValidateDashboardUser, bookingResource.UpsertSportsInactiveDaysR, bookingController.UpsertSportsInactiveDaysC)
		bookingRoutes.GET("/sportsInactiveDays/get", sharedFunc.ValidateDashboardUser, bookingResource.GetInactiveDaysR, bookingController.GetInactiveDaysC)
		bookingRoutes.GET("/getFeedbackListings", bookingResource.FeedbackListingR, bookingController.FeedbackListingC)
		bookingRoutes.GET("/getBookingSlotsDetails", bookingResource.BookingSlotsDetailsR, bookingController.BookingSlotsDetailsC)
		bookingRoutes.POST("/markAttendanceStatus", bookingResource.MarkAttendanceStatusR, bookingController.MarkAttendanceStatusC)
		bookingRoutes.GET("/getStreamDetailsForOnlineSession", bookingResource.GetStreamDetailsR, bookingController.GetStreamDetailsC)
		bookingRoutes.GET("/getRecommendedSlots", bookingResource.GetRecommendedSlotsR, bookingController.GetRecommendedSlotsC)
		bookingRoutes.POST("/markOnlineSessionAttendance", bookingResource.MarkOnlineSessionAttendanceR, bookingController.MarkOnlineSessionAttendanceC)
		bookingRoutes.GET("/autoPopulateSlot", sharedFunc.ValidateDashboardUser, bookingResource.AutoPopulateSlotR, bookingController.AutoPopulateSlotC)
		bookingRoutes.GET("/getNoShowPenaltyRecord", sharedFunc.ValidateDashboardUser, bookingResource.GetNoShowPenaltyRecordR, bookingController.GetNoShowPenaltyRecordC)
		bookingRoutes.POST("/noShowRevert", sharedFunc.ValidateDashboardUser, bookingResource.NoShowRevertR, bookingController.NoShowRevertC)
		bookingRoutes.GET("/getActiveSlots", sharedFunc.ValidateDashboardUser, bookingResource.GetActiveSlotsR, bookingController.GetActiveSlotsC)
		bookingRoutes.POST("/userQueryNoShowRevert", bookingResource.UserQueryNoShowRevertR, bookingController.UserQueryNoShowRevertC)
		bookingRoutes.GET("/getSlotPopulationStatus", sharedFunc.ValidateDashboardUser, bookingResource.GetSlotPopulationStatusR, bookingController.GetSlotPopulationStatusC)
		bookingRoutes.POST("/sessionCount", sharedFunc.ValidateDashboardUser, bookingResource.GetSessionCountR, bookingController.GetSessionCountC)
		bookingRoutes.POST("/assignCoach", sharedFunc.ValidateDashboardUser, bookingResource.AssignCoachToBookingR, bookingController.AssignCoachToBookingC)
		bookingRoutes.POST("/FMRemark/upsert", sharedFunc.ValidateDashboardUser, bookingResource.UpsertFMRemarkR, bookingController.UpsertFMRemarkC)
		bookingRoutes.GET("/academy/getRelatedAcademySlots", sharedFunc.ValidateDashboardUser, bookingResource.GetRelatedAcademySlotsR, bookingController.GetRelatedAcademySlotsC)
		bookingRoutes.POST(
			"/userBookings",
			bookingResource.GetUserBookingsR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			bookingController.GetUserBookingsC,
		)
		bookingRoutes.GET(
			"/getBookingDetails",
			bookingResource.GetBookingDetailsR,
			sharedFunc.ValidateTokenShared,
			sharedFunc.SetUserIDInContextFromToken,
			bookingController.GetBookingDetailsC,
		)
		bookingRoutes.POST(
			"/addEditCapacitySlots",
			bookingResource.AddEditCapacitySlotsR,
			sharedFunc.ValidateDashboardUser,
			bookingController.AddEditCapacitySlotsC,
		)
		bookingRoutes.POST(
			"/academy/product-centers",
			bookingResource.GetAcademyProductCentersR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			commonController.GetProductCentersC,
		)
		bookingRoutes.POST(
			"/academy/product-slots",
			bookingResource.GetAcademyProductSlotsR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			bookingController.GetAcademyProductSlotsC,
		)
		bookingRoutes.GET(
			"/academy/getAcademySlots",
			sharedFunc.ValidateDashboardUser,
			bookingResource.GetAcademySlotsR,
			bookingController.GetAcademySlotsC,
		)
		bookingRoutes.POST(
			"/academy/addEditAcademySlots",
			sharedFunc.ValidateDashboardUser,
			bookingResource.AddEditAcademySlotsR,
			bookingController.AddEditAcademySlotsC,
		)
		bookingRoutes.GET(
			"/academy/product-centres-slots",
			sharedFunc.ValidateDashboardUser,
			bookingResource.GetFacilitiesSlotsCapacityR,
			bookingController.GetAcademyFacilitiesSlotsCapacityC,
		)
		bookingRoutes.GET(
			"/summer-camp/getSlotsByFsId",
			sharedFunc.ValidateDashboardUser,
			bookingResource.GetSummerCampSlotsR,
			bookingController.GetSummerCampSlotsByFsIdC,
		)
		bookingRoutes.GET(
			"/summer-camp/getRelatedSlots",
			sharedFunc.ValidateDashboardUser,
			bookingResource.GetSummerCampSlotsR,
			bookingController.GetRelatedSummerCampSlotsC,
		)
		bookingRoutes.POST(
			"/summer-camp/addEditSlots",
			sharedFunc.ValidateDashboardUser,
			bookingResource.AddEditSummerCampSlotsR,
			bookingController.AddEditSummerCampSlotsC,
		)
		bookingRoutes.POST(
			"/academy/sendNotification",
			bookingResource.AcademyBookingCreationCultR,
			bookingController.AcademyBookingCreationCultC,
		)
		bookingRoutes.GET(
			"/summer-camp/getFacilitiesSlotCapacity",
			sharedFunc.ValidateDashboardUser,
			bookingResource.GetFacilitiesSlotsCapacityR,
			bookingController.GetSummerCampFacilitiesSlotsCapacityC,
		)
		bookingRoutes.POST(
			"/academy/correctAcademyCapacity",
			bookingResource.CorrectAcademySlotCapacityR,
			bookingController.CorrectAcademySlotCapacityC,
		)
	}
}

func BookingRouteV2(routerVersion *gin.RouterGroup) {
	// Group product related routes together

	bookingRoutes := routerVersion.Group("/booking")
	{
		bookingRoutes.GET("/getBookingSlotsV2", bookingResource.GetBookingSlotsV2R, bookingController.GetBookingSlotsV2C)
		bookingRoutes.GET("/getLiveSessionBookingSlotsV2", bookingResource.GetBookingSlotsV2R, bookingController.GetLiveSessionBookingSlotsV2C)
		bookingRoutes.GET("/getBookingDetailsV2", sharedFunc.ValidateDashboardUser, bookingResource.GetBookingDetailsV2R, bookingController.GetBookingDetailsV2C)
		bookingRoutes.GET("/getBookingSlotsDetailsV2", bookingResource.BookingSlotsDetailsV2R, bookingController.BookingSlotsDetailsV2C)
		bookingRoutes.POST(
			"/saveBookingFeedbackV2",
			sharedFunc.SetUserIDInContextFromToken,
			bookingResource.SaveBookingFeedbackV2R,
			bookingController.SaveBookingFeedbackV2C,
		)
		bookingRoutes.POST(
			"/publish",
			bookingResource.PublishBookingV2R,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			commonController.PublishBookingCommonC,
		)

		bookingRoutes.POST(
			"/getBookingSlotsV3",
			bookingResource.GetBookingSlotsV3R,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			commonController.SlotsAcademyAndMasterkeyC,
		)

		bookingRoutes.POST(
			"/getAvailableCentresForBooking",
			bookingResource.GetFitsoCentresForSlotBookingR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			commonController.CentersAcademyAndMasterkeyC,
		)

		bookingRoutes.POST(
			"/buddies/list",
			bookingResource.ListBookingBuddiesR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			bookingController.ListBookingBuddiesC,
		)

		bookingRoutes.POST(
			"/noshow/revert-request",
			bookingResource.UserQueryNoShowRevertV2R,
			sharedFunc.SetUserIDInContextFromToken,
			bookingController.UserQueryNoShowRevertV2C,
		)

		bookingRoutes.GET(
			"/booking_details/:reference_number",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			bookingController.GetBookingDetailsV3C,
		)

		bookingRoutes.POST(
			"/cancel-booking",
			bookingResource.CancelBookingV2R,
			sharedFunc.SetUserIDInContextFromToken,
			bookingController.CancelBookingV2C,
		)
		bookingRoutes.GET(
			"/getFeedbackOptions",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			bookingResource.GetBookingFeedbackOptionsV2R,
			bookingController.GetBookingFeedbackOptionsV2C,
		)
	}
}
