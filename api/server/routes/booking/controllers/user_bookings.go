package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models/booking/users"
	sushi "bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	structs "bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

const (
	NO_SHOW_TAG                string = "NO-SHOW"
	LATE_ENTRY_TAG             string = "LATE ENTRY"
	CANCELLED_TAG              string = "CANCELLED"
	MODIFIED_TAG               string = "MODIFIED"
	ABSENT_TAG                 string = "ABSENT"
	PAST_ALL_BOOKING_FILTER_ID string = "filter_past_all_bookings"
	PAST_NO_SHOW_FILTER_ID     string = "filter_past_no_show"
	PAST_CANCELLED_FILTER_ID   string = "filter_past_cancelled"
)

type UserBookingsPageData struct {
	UpcomingBookings    []*bookingPB.Booking                         `json:"-"`
	PastBookings        []*bookingPB.Booking                         `json:"-"`
	Users               []*bookingPB.User                            `json:"-"`
	ReferenceNumbers    []string                                     `json:"-"`
	SubscriptionDetails *productPB.GetUserSubscriptionStatusResponse `json:"-"`
	Filters             []*bookingPB.BookingFilter                   `json:"-"`
	HasMore             bool                                         `json:"-"`
	PostbackParams      map[string]interface{}                       `json:"-"`
}

type GetUserBookingsTemplate struct {
	PageRequest      structs.UserBookingsRequest `json:"-"`
	PageData         UserBookingsPageData        `json:"-"`
	PageHeader       *models.PageHeader          `json:"header,omitempty"`
	EmptyView        *models.ResultSection       `json:"empty_view,omitempty"`
	Filters          []*models.Filter            `json:"filters,omitempty"`
	Footer           *models.ResultSection       `json:"footer,omitempty"`
	UpcomingBookings *models.Bookings            `json:"upcoming_bookings,omitempty"`
	PastBookings     *models.Bookings            `json:"past_bookings,omitempty"`
	PostbackParams   string                      `json:"postback_params,omitempty"`
	HasMore          bool                        `json:"has_more,omitempty"`
}

func GetUserBookingsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	requestData := c.MustGet("jsonData").(structs.UserBookingsRequest)
	if requestData.ProductCategoryId == 0 {
		requestData.ProductCategoryId = common.MasterkeyCategoryID
	}
	var requestPostbackParams map[string]interface{}
	json.Unmarshal([]byte(requestData.PostbackParams), &requestPostbackParams)

	pastAllReferenceNumbers := make([]string, 0)
	if referenceNumbers, ok := requestPostbackParams["past_all"]; ok {
		referenceNumbersInterface, _ := referenceNumbers.([]interface{})
		for _, val := range referenceNumbersInterface {
			pastAllReferenceNumbers = append(pastAllReferenceNumbers, val.(string))
		}
	}

	upcomingReferenceNumbers := make([]string, 0)
	pastNoshowReferenceNumbers := make([]string, 0)
	pastCancelledReferenceNumbers := make([]string, 0)
	if !isProductCategoryAcademy(requestData.ProductCategoryId) {
		if referenceNumbers, ok := requestPostbackParams["upcoming"]; ok {
			referenceNumbersInterface, _ := referenceNumbers.([]interface{})
			for _, val := range referenceNumbersInterface {
				upcomingReferenceNumbers = append(upcomingReferenceNumbers, val.(string))
			}
		}
		if referenceNumbers, ok := requestPostbackParams["past_noshow"]; ok {
			referenceNumbersInterface, _ := referenceNumbers.([]interface{})
			for _, val := range referenceNumbersInterface {
				pastNoshowReferenceNumbers = append(pastNoshowReferenceNumbers, val.(string))
			}
		}

		if referenceNumbers, ok := requestPostbackParams["past_cancelled"]; ok {
			referenceNumbersInterface, _ := referenceNumbers.([]interface{})
			for _, val := range referenceNumbersInterface {
				pastCancelledReferenceNumbers = append(pastCancelledReferenceNumbers, val.(string))
			}
		}
	}

	requestData.UnmarshalledReferenceNumbers = &structs.UserBookingPostbackParams{
		UpcomingReferenceNumbers:       upcomingReferenceNumbers,
		PastAllSessionReferenceNumbers: pastAllReferenceNumbers,
		PastNoshowReferenceNumbers:     pastNoshowReferenceNumbers,
		PastCancelledReferenceNumbers:  pastCancelledReferenceNumbers,
	}

	template := GetUserBookingsTemplate{
		PageRequest: requestData,
		PageData:    UserBookingsPageData{},
	}
	template.GetSubscriptionStatusDetails(ctx)
	template.SetBookingsPageData(ctx)

	if template.PageRequest.PostbackParams == "" { // first api call
		if len(template.PageData.UpcomingBookings) == 0 &&
			len(template.PageData.PastBookings) == 0 &&
			(template.PageRequest.FilterId == PAST_ALL_BOOKING_FILTER_ID || template.PageRequest.FilterId == "") {
			template.SetPageHeader(ctx)
			template.SetEmptyPageSection(ctx)
		} else {
			template.SetPageHeader(ctx)
			template.SetUpcomingBookingsSection(ctx)
			template.SetPastBookingsSection(c)
			template.SetPostbackParams(ctx)
		}
	} else { // load more call
		template.SetLoadMorePastBookingsSection(c)
		template.SetPostbackParams(ctx)
	}

	c.JSON(http.StatusOK, template)
}

func (b *GetUserBookingsTemplate) SetBookingsPageData(ctx context.Context) {
	var referenceNumbers []string
	switch b.PageRequest.FilterId {
	case PAST_ALL_BOOKING_FILTER_ID:
		referenceNumbers = b.PageRequest.UnmarshalledReferenceNumbers.PastAllSessionReferenceNumbers

	case PAST_NO_SHOW_FILTER_ID:
		referenceNumbers = b.PageRequest.UnmarshalledReferenceNumbers.PastNoshowReferenceNumbers

	case PAST_CANCELLED_FILTER_ID:
		referenceNumbers = b.PageRequest.UnmarshalledReferenceNumbers.PastCancelledReferenceNumbers

	default:
		referenceNumbers = b.PageRequest.UnmarshalledReferenceNumbers.PastAllSessionReferenceNumbers
	}

	req := &bookingPB.UserBookingsRequest{
		FilterId:          b.PageRequest.FilterId,
		Count:             b.PageRequest.Count,
		ReferenceNumbers:  referenceNumbers,
		PostbackParams:    b.PageRequest.PostbackParams,
		ProductCategoryId: b.PageRequest.ProductCategoryId,
	}
	bookingClient := util.GetBookingClient()
	res, err := bookingClient.GetUserBookings(ctx, req)

	if err != nil {
		log.Printf("Function: SetBookingsPageData, Error: %v", err)
		b.PageData.UpcomingBookings = make([]*bookingPB.Booking, 0)
		b.PageData.PastBookings = make([]*bookingPB.Booking, 0)
		return
	}

	if res.Status != nil && (res.Status.Status == common.BAD_REQUEST || res.Status.Status == common.FAILED) {
		log.Printf("Function: SetBookingsPageData, Error: %v", res.Status.Message)
		b.PageData.UpcomingBookings = make([]*bookingPB.Booking, 0)
		b.PageData.PastBookings = make([]*bookingPB.Booking, 0)
		return
	}

	if val, ok := res.Bookings["upcoming"]; ok {
		b.PageData.UpcomingBookings = val.Booking
	} else {
		b.PageData.UpcomingBookings = make([]*bookingPB.Booking, 0)
	}

	if val, ok := res.Bookings["past"]; ok {
		b.PageData.PastBookings = val.Booking
	} else {
		b.PageData.PastBookings = make([]*bookingPB.Booking, 0)
	}

	b.PageData.Users = res.Users
	b.PageData.Filters = res.Filters
	b.PageData.ReferenceNumbers = res.ReferenceNumbers
	b.PageData.HasMore = res.HasMore
	if isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		b.PostbackParams = res.PostbackParams
	}
}

func (b *GetUserBookingsTemplate) GetSubscriptionStatusDetails(ctx context.Context) *productPB.GetUserSubscriptionStatusResponse {
	if b.PageData.SubscriptionDetails != nil {
		return b.PageData.SubscriptionDetails
	}
	productClient := util.GetProductClient()
	footerDetailsReq := &productPB.GetUserSubscriptionStatusRequest{
		UserId:                    util.GetUserIDFromContext(ctx),
		ProductSuggestionRequired: true,
		CheckChildSub:             true,
	}
	footerDetailsResponse, err := productClient.GetUserSubscriptionStatus(ctx, footerDetailsReq)
	if err != nil {
		log.Printf("Api: user bookings, function: GetSubscriptionStatusDetails, Error: %v", err)
		return nil
	}
	b.PageData.SubscriptionDetails = footerDetailsResponse
	return b.PageData.SubscriptionDetails
}

func (b *GetUserBookingsTemplate) AddUpcomingBookingResultItem(result *models.ResultSection) {
	b.UpcomingBookings.Results = append(b.UpcomingBookings.Results, result)
}

func (b *GetUserBookingsTemplate) AddPastBookingResultItem(result *models.ResultSection) {
	b.PastBookings.Results = append(b.PastBookings.Results, result)
}

func (b *GetUserBookingsTemplate) SetPageHeader(ctx context.Context) {
	titleStr := "Your bookings"
	if isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		titleStr = "Your Sessions"
	}
	title, _ := sushi.NewTextSnippet(titleStr)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetColor(color)
	title.SetFont(font)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)

	b.PageHeader = &models.PageHeader{
		Title:   title,
		BgColor: bgColor,
	}
}

func (b *GetUserBookingsTemplate) SetEmptyPageSection(ctx context.Context) {
	emptyLayout := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}
	emptyTitle, _ := sushi.NewTextSnippet("You have no upcoming sessions")
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	emptyTitle.SetFont(font)
	emptyTitle.SetColor(color)

	image, _ := sushi.NewImage(util.GetCDNLink("uploads/Group23303.png"))
	image.SetHeight(220)
	image.SetWidth(258)
	emptyViewSnippet := &sushi.EmptyViewType1Snippet{
		Title: emptyTitle,
		Image: image,
	}
	b.EmptyView = &models.ResultSection{
		LayoutConfig:   emptyLayout,
		EmptyViewType1: emptyViewSnippet,
	}

	subscription := b.GetSubscriptionStatusDetails(ctx)
	var status productPB.GetUserSubscriptionStatusResponse_SubscriptionStatus

	if subscription == nil {
		status = productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL
	} else {
		status = b.PageData.SubscriptionDetails.SubscriptionStatus
	}

	var productSubtext string
	if len(subscription.SuggestedProducts) > 0 {
		subscriptionProduct := subscription.SuggestedProducts[0]
		productSubtext = fmt.Sprintf("₹%d for %d %ss", int32(subscriptionProduct.RetailPrice), subscriptionProduct.Duration, subscriptionProduct.DurationUnit)
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			perMonthPrice := int32(subscriptionProduct.RetailPrice)
			if subscriptionProduct.Duration > 0 {
				perMonthPrice = int32(subscriptionProduct.RetailPrice) / subscriptionProduct.Duration
			}
			productSubtext = fmt.Sprintf("starts from ₹%d per month", int32(perMonthPrice))
		}
	}

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	clickActionPurchase := sushi.GetClickAction()
	deeplinkPurchase := sushi.Deeplink{
		URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
	}
	clickActionPurchase.SetDeeplink(&deeplinkPurchase)

	switch status {
	case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:        "solid",
			Text:        "Become a cultpass PLAY member",
			Subtext:     productSubtext,
			ClickAction: clickActionPurchase,
		}
		items = append(items, buttonItem1)

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: util.GetTrialSportsPageDeeplink(),
		}
		clickAction.SetDeeplink(&deeplink)

		buttonItem2 := sushi.FooterSnippetType2ButtonItem{
			Type:        "text",
			Text:        "or book a free trial session",
			ClickAction: clickAction,
		}
		items = append(items, buttonItem2)
		break

	case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
		fallthrough
	case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:        "solid",
			Text:        "Become a cultpass PLAY member",
			Subtext:     productSubtext,
			ClickAction: clickActionPurchase,
		}
		items = append(items, buttonItem1)
		break
	}

	if len(items) > 0 {
		footerLayout := &sushi.LayoutConfig{
			SnippetType: sushi.FooterSnippetType2,
		}
		buttonData := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}
		footerSnippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: buttonData,
		}
		b.Footer = &models.ResultSection{
			LayoutConfig: footerLayout,
			FooterType2:  footerSnippet,
		}
	}
}

func (b *GetUserBookingsTemplate) SetUpcomingBookingsSection(ctx context.Context) {
	sectionBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)

	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	headerTitle, _ := sushi.NewTextSnippet("UPCOMING SESSIONS")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	headerTitle.SetColor(color)
	headerTitle.SetFont(font)
	headerTitle.SetKerning(3)

	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: headerTitle,
	}
	sectionHeader := &models.ResultSection{
		LayoutConfig:       headerLayout,
		SectionHeaderType1: headerSnippet,
	}

	b.UpcomingBookings = &models.Bookings{
		BgColor: sectionBgColor,
		Header:  sectionHeader,
		Results: make([]*models.ResultSection, 0),
	}

	if len(b.PageData.UpcomingBookings) > 0 {
		bookingLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType34,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		for _, val := range b.PageData.UpcomingBookings {
			items := make([]sushi.V2ImageTextSnippetType34SnippetItem, 0)
			item := b.GetUpcomingBookingItem(val)
			items = append(items, item)

			bookingSnippet := &sushi.V2ImageTextSnippetType34Snippet{
				Items: &items,
			}
			section := &models.ResultSection{
				LayoutConfig:             bookingLayout,
				V2ImageTextSnippetType34: bookingSnippet,
			}
			b.AddUpcomingBookingResultItem(section)
		}
	} else {
		emptyLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType35,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		emptyTitle, _ := sushi.NewTextSnippet("You have no upcoming sessions")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
		emptyTitle.SetColor(color)
		emptyTitle.SetFont(font)
		emptyTitle.SetAlignment(sushi.TextAlignmentCenter)

		item := sushi.ImageTextSnippetType35SnippetItem{
			Title: emptyTitle,
		}
		items := []sushi.ImageTextSnippetType35SnippetItem{item}

		emptyViewSnippet := &sushi.ImageTextSnippetType35Snippet{
			Items: &items,
		}
		section := &models.ResultSection{
			LayoutConfig:           emptyLayout,
			ImageTextSnippetType35: emptyViewSnippet,
		}
		b.AddUpcomingBookingResultItem(section)
	}
}

func (b *GetUserBookingsTemplate) GetUpcomingBookingItem(booking *bookingPB.Booking) sushi.V2ImageTextSnippetType34SnippetItem {
	item := sushi.V2ImageTextSnippetType34SnippetItem{}

	sportImage, _ := sushi.NewImage(booking.Sport.Icon)
	sportImage.SetAspectRatio(1)
	sportImage.SetType(sushi.ImageTypeRounded)
	sportImage.SetHeight(80)
	sportImage.SetWidth(80)

	var sportBgColor *sushi.Color
	if val, ok := common.SportIdBackgroundColorMap[booking.Sport.SportId]; ok {
		sportBgColor, _ = sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
	} else {
		sportBgColor, _ = sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint200)
	}
	sportImage.SetColor(sportBgColor)
	item.Image = sportImage

	title, _ := sushi.NewTextSnippet(booking.Sport.SportName)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title.SetColor(color)
	title.SetFont(font)
	item.Title = title

	var subtitlePrefix string
	currentTimeObj := time.Now()
	bookingTimeObj := time.Unix(booking.BookingTime.Seconds, 0)

	currentYear, currentMonth, currentDay := currentTimeObj.Date()
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	currentMonthStr := currentMonth.String()
	bookingMonthStr := bookingMonth.String()
	timeStr := bookingTimeObj.Format("03:04 pm")

	if bookingMonthStr == currentMonthStr && currentYear == bookingYear {
		if currentDay == bookingDay {
			subtitlePrefix = "Today, "
		} else if bookingDay-currentDay == 1 {
			subtitlePrefix = "Tomorrow, "
		}
	}

	subtitleStr := fmt.Sprintf("%s%d %s at %s", subtitlePrefix, bookingDay, bookingMonthStr[:3], timeStr)
	subtitle, _ := sushi.NewTextSnippet(subtitleStr)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitle.SetColor(color)
	subtitle.SetFont(font)
	item.Subtitle = subtitle

	if isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		item.Subtitle.Text = booking.Sport.SportName
		if booking.IsTrial {
			item.Subtitle.Text = fmt.Sprintf("Trial: %s", booking.Sport.SportName)
		}
		if booking.SessionNo > 0 {
			item.Subtitle.Text = fmt.Sprintf("Session %d: %s", booking.SessionNo, booking.Sport.SportName)
		}
		item.Title.Text = subtitleStr
	}

	if booking.Tag != "" {
		title, _ = sushi.NewTextSnippet(booking.Tag)
		color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		title.SetColor(color)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
		tag := &sushi.Tag{
			Title:   title,
			BgColor: tagBgColor,
		}
		item.Tag = tag
	}

	userPhotos := make(map[int32]string)
	for _, val := range b.PageData.Users {
		userPhotos[val.UserId] = val.ProfilePicture
	}
	linkedPhotos := make([]*sushi.Image, 0)
	for _, id := range booking.LinkedUserIds {
		profileImage, _ := sushi.NewImage(userPhotos[id])
		profileImage.SetType(sushi.ImageTypeCircle)
		profileImage.SetHeight(24)
		profileImage.SetWidth(24)
		linkedPhotos = append(linkedPhotos, profileImage)
	}
	item.Images = linkedPhotos

	subzoneName := booking.Facility.SubzoneName
	if isProductCategorySummerCamp(b.PageRequest.ProductCategoryId) && len(booking.Facility.SubzoneName) == 0 {
		subzoneName = "-"
	}

	bottomTitle, _ := sushi.NewTextSnippet(subzoneName)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	bottomTitle.SetColor(color)
	bottomTitle.SetFont(font)

	// get directions
	clickAction := sushi.GetClickAction()
	openMap := sushi.OpenMap{
		Latitude:  fmt.Sprintf("%f", booking.Facility.Latitude),
		Longitude: fmt.Sprintf("%f", booking.Facility.Longitude),
	}
	clickAction.SetOpenMap(&openMap)
	directionColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	directionIcon, _ := sushi.NewIcon(sushi.DirectionIcon, directionColor)
	directionFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	directionButton, _ := sushi.NewButton(sushi.ButtontypeText)
	directionButton.SetText("Get directions")
	directionButton.SetSize(sushi.ButtonSizeMedium)
	directionButton.SetFont(directionFont)
	directionButton.SetPrefixIcon(directionIcon)
	directionButton.SetColor(directionColor)
	directionButton.SetClickAction(clickAction)

	bottomItem := &sushi.BottomContainerSnippetItem{
		Title:       bottomTitle,
		RightButton: directionButton,
	}
	item.BottomContainer = bottomItem

	cardClickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: booking.Deeplink,
	}
	cardClickAction.SetDeeplink(deeplink)
	item.ClickAction = cardClickAction

	return item
}

func (b *GetUserBookingsTemplate) SetFiltersSection(ctx context.Context) {
	for index, val := range b.PageData.Filters {
		hasMore := true
		if index == 0 {
			hasMore = b.PageData.HasMore
		}
		filter := &models.Filter{
			Id:      val.Id,
			HasMore: hasMore,
		}
		b.Filters = append(b.Filters, filter)
	}
}

func (b *GetUserBookingsTemplate) SetPastBookingsSection(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	b.SetFiltersSection(ctx)
	b.setPastBookingHeader(ctx)
	if !isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		b.setPastBookingFilterRail(ctx)
	}

	if len(b.PageData.PastBookings) == 0 {
		b.setEmptyPastBookingSnippet(ctx)
	} else {
		bookingLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType34,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		for _, val := range b.PageData.PastBookings {
			items := make([]sushi.V2ImageTextSnippetType34SnippetItem, 0)
			item := b.GetPastBookingItem(c, val)
			items = append(items, item)
			bookingSnippet := &sushi.V2ImageTextSnippetType34Snippet{
				Items: &items,
			}
			section := &models.ResultSection{
				LayoutConfig:             bookingLayout,
				V2ImageTextSnippetType34: bookingSnippet,
			}
			b.AddPastBookingResultItem(section)
		}
	}
}

func (b *GetUserBookingsTemplate) setPastBookingFilterRail(ctx context.Context) {
	filterLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.FilterRailType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	selectedBorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	defaultBorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	selectedBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	defaultBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	selectedTextColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	defaultTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)

	filterConfig := &sushi.FilterRailType1SnippetConfig{
		BorderColor:          selectedBorderColor,
		DefaultBorderColor:   defaultBorderColor,
		SelectedBgColor:      selectedBgColor,
		DefaultBgColor:       defaultBgColor,
		SelectedTextColor:    selectedTextColor,
		DefaultSelectedColor: defaultTextColor,
	}

	var tabOptions []sushi.FilterRailType1SnippetItem

	for _, val := range b.PageData.Filters {
		isSelected := false

		if (b.PageRequest.FilterId == "" && val.Id == PAST_ALL_BOOKING_FILTER_ID) ||
			b.PageRequest.FilterId == val.Id {
			isSelected = true
		}

		title, _ := sushi.NewTextSnippet(val.Name)
		tabSnippetType1Item := sushi.FilterRailType1SnippetItem{
			ID:         val.Id,
			Title:      title,
			IsSelected: isSelected,
		}
		tabOptions = append(tabOptions, tabSnippetType1Item)
	}
	filterSnippet := &sushi.FilterRailType1Snippet{
		Config: filterConfig,
		Items:  &tabOptions,
	}
	filterSection := &models.ResultSection{
		LayoutConfig:    filterLayout,
		FilterRailType1: filterSnippet,
	}
	b.AddPastBookingResultItem(filterSection)
}

func (b *GetUserBookingsTemplate) setPastBookingHeader(ctx context.Context) {
	sectionBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)

	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	headerTitle, _ := sushi.NewTextSnippet("PAST SESSIONS")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	headerTitle.SetColor(color)
	headerTitle.SetFont(font)
	headerTitle.SetKerning(3)

	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: headerTitle,
	}
	sectionHeader := &models.ResultSection{
		LayoutConfig:       headerLayout,
		SectionHeaderType1: headerSnippet,
	}

	b.PastBookings = &models.Bookings{
		BgColor: sectionBgColor,
		Header:  sectionHeader,
		Results: make([]*models.ResultSection, 0),
	}
}

func (b *GetUserBookingsTemplate) setEmptyPastBookingSnippet(ctx context.Context) {
	emptyLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType35,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	var emptyText string
	if b.PageRequest.FilterId == PAST_ALL_BOOKING_FILTER_ID && len(b.PageRequest.UnmarshalledReferenceNumbers.PastAllSessionReferenceNumbers) == 0 {
		emptyText = "You have no sessions"
	} else if b.PageRequest.FilterId == PAST_NO_SHOW_FILTER_ID && len(b.PageRequest.UnmarshalledReferenceNumbers.PastNoshowReferenceNumbers) == 0 {
		emptyText = "Great going! You don’t have any applied penalties yet"
	} else if b.PageRequest.FilterId == PAST_CANCELLED_FILTER_ID && len(b.PageRequest.UnmarshalledReferenceNumbers.PastCancelledReferenceNumbers) == 0 {
		emptyText = "You have no cancelled sessions"
	} else if isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		emptyText = "You have no sessions"
	}

	if len(emptyText) == 0 {
		return
	}

	emptyTitle, _ := sushi.NewTextSnippet(emptyText)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	emptyTitle.SetColor(color)
	emptyTitle.SetFont(font)
	emptyTitle.SetAlignment(sushi.TextAlignmentCenter)

	item := sushi.ImageTextSnippetType35SnippetItem{
		Title: emptyTitle,
	}
	items := []sushi.ImageTextSnippetType35SnippetItem{item}

	emptyViewSnippet := &sushi.ImageTextSnippetType35Snippet{
		Items: &items,
	}
	section := &models.ResultSection{
		LayoutConfig:           emptyLayout,
		ImageTextSnippetType35: emptyViewSnippet,
	}
	b.AddPastBookingResultItem(section)
}

func (b *GetUserBookingsTemplate) SetLoadMorePastBookingsSection(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	b.SetHasMore(ctx)

	if len(b.PageData.PastBookings) == 0 {
		b.setPastBookingHeader(ctx)
		b.setEmptyPastBookingSnippet(ctx)

	} else {
		b.PastBookings = &models.Bookings{
			Results: make([]*models.ResultSection, 0),
		}

		bookingLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType34,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		for _, val := range b.PageData.PastBookings {
			items := make([]sushi.V2ImageTextSnippetType34SnippetItem, 0)
			item := b.GetPastBookingItem(c, val)
			items = append(items, item)

			bookingSnippet := &sushi.V2ImageTextSnippetType34Snippet{
				Items: &items,
			}
			section := &models.ResultSection{
				LayoutConfig:             bookingLayout,
				V2ImageTextSnippetType34: bookingSnippet,
			}
			b.AddPastBookingResultItem(section)
		}
	}
}

func (b *GetUserBookingsTemplate) GetPastBookingItem(c *gin.Context, booking *bookingPB.Booking) sushi.V2ImageTextSnippetType34SnippetItem {
	source := "your_bookings"

	ctx := util.PrepareGRPCMetaData(c)
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	item := sushi.V2ImageTextSnippetType34SnippetItem{}

	sportImage, _ := sushi.NewImage(booking.Sport.Icon)
	sportImage.SetAspectRatio(1)
	sportImage.SetType(sushi.ImageTypeRounded)
	sportImage.SetHeight(80)
	sportImage.SetWidth(80)

	var sportBgColor *sushi.Color
	if val, ok := common.SportIdBackgroundColorMap[booking.Sport.SportId]; ok {
		sportBgColor, _ = sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
	} else {
		sportBgColor, _ = sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint200)
	}
	sportImage.SetColor(sportBgColor)
	item.Image = sportImage

	title, _ := sushi.NewTextSnippet(booking.Sport.SportName)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	title.SetColor(color)
	title.SetFont(font)
	item.Title = title

	var subtitlePrefix string
	currentTimeObj := time.Now()
	bookingTimeObj := time.Unix(booking.BookingTime.Seconds, 0)

	currentYear, currentMonth, currentDay := currentTimeObj.Date()
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	currentMonthStr := currentMonth.String()
	bookingMonthStr := bookingMonth.String()
	timeStr := bookingTimeObj.Format("03:04 pm")

	if bookingMonthStr == currentMonthStr && currentYear == bookingYear {
		if currentDay == bookingDay {
			subtitlePrefix = "Today, "
		} else if currentDay-bookingDay == 1 {
			subtitlePrefix = "Yesterday, "
		} else if currentDay-bookingDay <= 3 && currentDay-bookingDay > 1 {
			subtitlePrefix = fmt.Sprintf("%d days ago, ", currentDay-bookingDay)
		}
	}

	subtitleStr := fmt.Sprintf("%s%d %s at %s", subtitlePrefix, bookingDay, bookingMonthStr[:3], timeStr)
	subtitle, _ := sushi.NewTextSnippet(subtitleStr)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	subtitle.SetColor(color)
	subtitle.SetFont(font)
	item.Subtitle = subtitle

	if isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		item.Subtitle.Text = booking.Sport.SportName
		if booking.IsTrial {
			item.Subtitle.Text = fmt.Sprintf("Trial: %s", booking.Sport.SportName)
		}
		if booking.SessionNo > 0 {
			item.Subtitle.Text = fmt.Sprintf("Session %d: %s", booking.SessionNo, booking.Sport.SportName)
		}
		item.Title.Text = subtitleStr
	}

	if booking.Tag != "" {
		title, _ = sushi.NewTextSnippet(booking.Tag)
		color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		title.SetColor(color)

		var colorType sushi.ColorType
		if booking.Tag == LATE_ENTRY_TAG || booking.Tag == NO_SHOW_TAG {
			colorType = sushi.ColorTypeOrange
			if isNewColorSupported {
				colorType = sushi.ALERT_DARK_THEME
			}
		} else if booking.Tag == CANCELLED_TAG {
			colorType = sushi.ColorTypeGrey
		} else if booking.Tag == ABSENT_TAG {
			colorType = sushi.ColorTypeRed
		}
		tagBgColor, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		tag := &sushi.Tag{
			Title:   title,
			BgColor: tagBgColor,
		}
		item.Tag = tag
	}

	footerButtons := make([]*sushi.Button, 0)
	bookAgainButton, _ := sushi.NewButton(sushi.ButtontypeText)
	bookAgainButton.SetText("Book again")
	bookAgainButton.SetSize(sushi.ButtonSizeSmall)
	bookAgainButton.SetAlignment(sushi.ButtonAlignmentLeft)

	loggedInUser := util.GetUserIDFromContext(ctx)
	loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)

	var status productPB.GetUserSubscriptionStatusResponse_SubscriptionStatus
	var hasActiveChildSubscriptions bool
	if b.PageData.SubscriptionDetails == nil {
		status = productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL
	} else {
		status = b.PageData.SubscriptionDetails.SubscriptionStatus
		hasActiveChildSubscriptions = b.PageData.SubscriptionDetails.ActiveChildSubscriptions
	}

	bookAgainClickAction := sushi.GetClickAction()
	header_city_id := util.GetCityIDFromContext(ctx)
	tomorrowSubscription := b.PageData.SubscriptionDetails.TomorrowParentSubscription || b.PageData.SubscriptionDetails.TomorrowChildSubscription
	if booking.Facility.CityIdV2 == header_city_id {
		if status == productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED ||
			(status == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION && !tomorrowSubscription) {
			customAlert := util.CustomAlertForExploreSlots(ctx, booking.FsId, b.PageData.SubscriptionDetails, source)
			bookAgainClickAction.SetCustomAlertAction(customAlert)
		} else {
			var openBuddyList bool
			if hasActiveChildSubscriptions {
				openBuddyList = true
			} else {
				openBuddyList = false
			}

			addAgeDeeplink, addBookingFlowDeeplink, addMedicalFormDeeplink := false, false, false
			isTrial := status == productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL || status == productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE

			if openBuddyList {
				addBookingFlowDeeplink = true
			} else { // single user
				if !featuresupport.SupportsMedicalForm(ctx) {
					if loggedInUserAge >= util.GetMinAgeForBooking(isTrial, b.PageRequest.ProductCategoryId) {
						addBookingFlowDeeplink = true
					} else {
						addAgeDeeplink = true
					}
				} else { // supports medical form UI
					if util.NeedMedicalDetails(ctx, loggedInUser) {
						addMedicalFormDeeplink = true
					} else if loggedInUserAge < util.GetMinAgeForBooking(isTrial, b.PageRequest.ProductCategoryId) {
						addAgeDeeplink = true
					} else {
						addBookingFlowDeeplink = true
					}
				}
			}

			if addBookingFlowDeeplink {
				deeplink, buddyListOpen := util.GetBookingFlowDeepLink(ctx, booking.FsId, openBuddyList, source)
				if !buddyListOpen && b.PageData.SubscriptionDetails.ProductCategoryId == common.SinglekeyCategoryID && booking.Sport.SportId != common.BADMINTON_SPORT_ID && b.PageData.SubscriptionDetails.SinglekeyTrialNotEligible {
					customAlert := util.GetTrialUsedupPopup(ctx, b.PageData.SubscriptionDetails)
					bookAgainClickAction.SetCustomAlertAction(customAlert)
				} else {
					bookAgainClickAction.SetDeeplink(&deeplink)
				}
			} else if addAgeDeeplink {
				obj := map[string]interface{}{
					"fs_id":        booking.FsId,
					"bottom_sheet": 1,
					"source":       source,
				}
				post_params, _ := json.Marshal(obj)
				payload := map[string]interface{}{
					"post_action":     "slots_get_my_bookings",
					"user_id":         loggedInUser,
					"postback_params": string(post_params),
				}
				postback_params, _ := json.Marshal(payload)
				bookAgainClickAction.Type = sushi.ClickActionOpenAgeBottomSheet
				bookAgainClickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, isTrial, b.PageRequest.ProductCategoryId)
			} else if addMedicalFormDeeplink {
				payload := map[string]interface{}{
					"page_type":    common.PAGE_TYPE_MY_BOOKINGS,
					"user_id":      loggedInUser,
					"fs_id":        booking.FsId,
					"bottom_sheet": 1,
					"source":       source,
				}
				postbackParams, _ := json.Marshal(payload)
				alert := util.GetCompleteMedicalDetailsAlert(ctx, loggedInUser, string(postbackParams))
				bookAgainClickAction.SetCustomAlertAction(alert)
			}
		}
	} else {
		customAlert := util.CustomAlertForCityChange(ctx)
		bookAgainClickAction.SetCustomAlertAction(customAlert)
	}

	if booking.Facility.OperationStatus == 0 {
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		bookAgainButton.SetActionDisabled()
	} else {
		color, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		bookAgainButton.SetClickAction(bookAgainClickAction)
	}
	bookAgainButton.SetColor(color)
	bookAgainIcon, _ := sushi.NewIcon(sushi.RepeatIcon, color)
	bookAgainButton.SetPrefixIcon(bookAgainIcon)
	if !(isProductCategoryAcademy(b.PageRequest.ProductCategoryId) || isProductCategorySummerCamp(b.PageRequest.ProductCategoryId)) {
		footerButtons = append(footerButtons, bookAgainButton)
	}

	if booking.RatingId == -1 {
		footerButton2, _ := sushi.NewButton(sushi.ButtontypeText)
		footerButton2.SetText("Share feedback")
		footerButton2.SetSize(sushi.ButtonSizeSmall)
		footerButton2.SetAlignment(sushi.ButtonAlignmentRight)
		color, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		feedbackIcon, _ := sushi.NewIcon(sushi.StarIcon, color)
		footerButton2.SetPrefixIcon(feedbackIcon)

		feedbackClickAction := sushi.GetClickAction()
		deeplink := &sushi.Deeplink{
			URL: util.GetFeedbackDeeplink(booking.BookingId, booking.BookingReferenceNumber, int32(0)),
		}
		feedbackClickAction.SetDeeplink(deeplink)
		footerButton2.SetClickAction(feedbackClickAction)

		feedbackPagePayload := make(map[string]interface{})
		feedbackPagePayload["user_id"] = util.GetUserIDFromContext(ctx)
		feedbackPagePayload["source"] = source
		feedbackPagePayload["booking_id"] = booking.BookingId
		feedbackPagePayload["fs_id"] = booking.FsId
		feedbackPagePayload["booking_time"] = booking.BookingTime.Seconds
		feedbackPagePayload["facility_id"] = booking.Facility.FacilityId
		feedbackPagePayload["sport_id"] = booking.Sport.SportId
		impressionEname := &sushi.EnameData{
			Ename: "share_feedback_button_tap",
		}
		feedbackPageEvents := sushi.NewClevertapEvents()
		feedbackPageEvents.SetTap(impressionEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, feedbackPagePayload, feedbackPageEvents)
		footerButton2.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		footerButtons = append(footerButtons, footerButton2)
	} else if booking.RatingId > 0 {
		footerButton2, _ := sushi.NewButton(sushi.ButtontypeText)
		footerButton2.SetText("Rated")
		footerButton2.SetSize(sushi.ButtonSizeSmall)
		footerButton2.SetActionDisabled()
		footerButton2.SetAlignment(sushi.ButtonAlignmentRight)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		footerButton2.SetColor(color)
		footerButtons = append(footerButtons, footerButton2)

		ratingText := fmt.Sprintf("%.1f", float32(booking.RatingId))
		ratingTitle, _ := sushi.NewTextSnippet(ratingText)
		ratingFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		ratingColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

		ratingTitle.SetFont(ratingFont)
		ratingTitle.SetColor(ratingColor)
		ratingTitle.SetPrefixIcon(ratingIcon)

		ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if isNewColorSupported {
			ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}

		item.Rating = &sushi.RatingSnippetBlockItem{
			Title:   ratingTitle,
			BgColor: ratingBgColor,
		}
	}
	if len(footerButtons) > 0 {
		item.ButtonData = &sushi.V2ImageTextSnippetType34SnippetButtonData{
			Items: footerButtons,
		}
	}

	cardClickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: booking.Deeplink,
	}
	cardClickAction.SetDeeplink(deeplink)
	item.ClickAction = cardClickAction

	return item
}

func (b *GetUserBookingsTemplate) SetPostbackParams(ctx context.Context) {
	b.PageData.PostbackParams = make(map[string]interface{})
	b.PageData.PostbackParams["upcoming"] = b.PageRequest.UnmarshalledReferenceNumbers.UpcomingReferenceNumbers
	b.PageData.PostbackParams["past_all"] = b.PageRequest.UnmarshalledReferenceNumbers.PastAllSessionReferenceNumbers
	b.PageData.PostbackParams["past_noshow"] = b.PageRequest.UnmarshalledReferenceNumbers.PastNoshowReferenceNumbers
	b.PageData.PostbackParams["past_cancelled"] = b.PageRequest.UnmarshalledReferenceNumbers.PastCancelledReferenceNumbers

	if b.PageRequest.FilterId == "" { // first api call which involves upcoming and past all sessions
		b.PageData.PostbackParams["upcoming"] = b.PageData.ReferenceNumbers
		b.PageData.PostbackParams["past_all"] = b.PageData.ReferenceNumbers
	}
	if b.PageRequest.FilterId == PAST_ALL_BOOKING_FILTER_ID {
		b.PageData.PostbackParams["past_all"] = b.PageData.ReferenceNumbers
	}
	if b.PageRequest.FilterId == PAST_NO_SHOW_FILTER_ID {
		b.PageData.PostbackParams["past_noshow"] = b.PageData.ReferenceNumbers
	}
	if b.PageRequest.FilterId == PAST_CANCELLED_FILTER_ID {
		b.PageData.PostbackParams["past_cancelled"] = b.PageData.ReferenceNumbers
	}

	serializedParams, _ := json.Marshal(b.PageData.PostbackParams)
	if !isProductCategoryAcademy(b.PageRequest.ProductCategoryId) {
		b.PostbackParams = string(serializedParams)
	}
}

func (b *GetUserBookingsTemplate) SetHasMore(ctx context.Context) {
	b.HasMore = b.PageData.HasMore
}

func isProductCategoryAcademy(productCategoryId int32) bool {
	return productCategoryId == common.AcademyCategoryID
}

func isProductCategorySummerCamp(productCategoryId int32) bool {
	return productCategoryId == common.SummerCampCategoryID
}
