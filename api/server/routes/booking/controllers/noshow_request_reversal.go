package bookingController

import (
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	bookingModel "bitbucket.org/jogocoin/go_api/api/models/booking"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// NoShowReversalRequestTemplate represents the response structure
type NoShowReversalRequestTemplate struct {
	Status     string                                `json:"status,omitempty"`
	ActionList []*bookingModel.ReversalRequestAction `json:"action_list,omitempty"`
}

// UserQueryNoShowRevertV2C is the controller layer which raised the user no show penalty reveral request
func UserQueryNoShowRevertV2C(c *gin.Context) {
	cl = util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	template := NoShowReversalRequestTemplate{}

	loggedInUser := util.GetUserIDFromContext(c)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	reversalRequest := prepareNoShowReversalRequest(c)
	response, err := cl.UserQueryNoShowRevert(ctx, reversalRequest)
	if err != nil {
		log.Println("Could not Create User Query for Noshow---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILURE {
		message := response.Status.Message
		resTemplate := template.failedResponse(c, message, reversalRequest)
		c.JSON(http.StatusOK, resTemplate)
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		log.Println("Could not Create User Query for Noshow---", err)
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	var json structs.UserQuery
	json = c.MustGet("jsonData").(structs.UserQuery)
	c.JSON(http.StatusOK, template.successResponse(c, json.BookingReferenceNumber, reversalRequest))
}

func prepareNoShowReversalRequest(c *gin.Context) *bookingPB.UserBookingRequestQueries {

	var json structs.UserQuery
	json = c.MustGet("jsonData").(structs.UserQuery)

	return &bookingPB.UserBookingRequestQueries{
		BookingId:              json.BookingId,
		ReasonId:               json.ReasonId,
		QueryTypeId:            json.QueryTypeId,
		OtherReason:            json.OtherReason,
		BookingReferenceNumber: json.BookingReferenceNumber,
	}
}

func NewNoShowReversalRequestTemplate() *NoShowReversalRequestTemplate {
	return &NoShowReversalRequestTemplate{}
}

func (s *NoShowReversalRequestTemplate) setTitle(text string) *sushi.TextSnippet {
	title, _ := sushi.NewTextSnippet(text)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	title.SetColor(color)
	title.SetFont(font)
	return title
}

func (s *NoShowReversalRequestTemplate) setMessage(text string) *sushi.TextSnippet {
	message, _ := sushi.NewTextSnippet(text)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	message.SetFont(font)
	return message
}

func (s *NoShowReversalRequestTemplate) setPositiveAction(text string, bookingReferenceNumber string) *sushi.Button {
	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText(text)
	clickAction := &sushi.ClickAction{
		Type: sushi.ClickActionDeeplink,
	}
	clickAction.SetDeeplink(&sushi.Deeplink{URL: util.GetBookingDetailsDeeplink(bookingReferenceNumber)})
	button.ClickAction = clickAction
	return button
}

func (s *NoShowReversalRequestTemplate) setImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (s *NoShowReversalRequestTemplate) setStatus(status string) {
	s.Status = status
}

func (s *NoShowReversalRequestTemplate) failedResponse(c *gin.Context, errorMessage string, data *bookingPB.UserBookingRequestQueries) *NoShowReversalRequestTemplate {
	response := NewNoShowReversalRequestTemplate()
	response.setStatus("failure")
	ctx := util.PrepareGRPCMetaData(c)

	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Reversal request declined"),
		Message: s.setMessage(errorMessage),
		Image:   s.setImage("https://fitso-images.curefit.co/file_assets/failure_image.json"),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		DismissAfterAction: true,
	}

	noshowPayload := s.GetCleverTapCommonPayload(c, data)
	noshowPayload["noshow_request_status"] = "failed"
	impressionEname := &sushi.EnameData{
		Ename: "noshow_reversal_request_status",
	}

	noshowEvents := sushi.NewClevertapEvents()
	noshowEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, noshowPayload, noshowEvents)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	customAlertAction := &bookingModel.ReversalRequestAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	response.ActionList = append(response.ActionList, customAlertAction)
	return response
}

func (s *NoShowReversalRequestTemplate) successResponse(c *gin.Context, bookingReferenceNumber string, data *bookingPB.UserBookingRequestQueries) *NoShowReversalRequestTemplate {
	response := NewNoShowReversalRequestTemplate()
	response.setStatus(common.SUCCESS)
	ctx := util.PrepareGRPCMetaData(c)

	customAlert := &sushi.CustomAlert{
		Title:          s.setTitle("Reversal request raised"),
		IsBlocking:     true,
		PositiveAction: s.setPositiveAction("See details", bookingReferenceNumber),
		Image:          s.setImage("https://fitso-images.curefit.co/file_assets/success_image.json"),
		AutoDismissData: &sushi.AutoDismissData{
			Time:              3,
			DismissActionType: sushi.ActionTypePositive,
		},
		DismissAfterAction: true,
	}

	noshowPayload := s.GetCleverTapCommonPayload(c, data)
	noshowPayload["noshow_request_status"] = "success"
	impressionEname := &sushi.EnameData{
		Ename: "noshow_reversal_request_status",
	}

	noshowEvents := sushi.NewClevertapEvents()
	noshowEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, noshowPayload, noshowEvents)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	bottomSheetDismissAction := &bookingModel.ReversalRequestAction{
		Type:               sushi.ActionTypeBottomSheetDismiss,
		BottomSheetDismiss: action,
	}
	response.ActionList = append(response.ActionList, bottomSheetDismissAction)

	refreshPageItems := &[]bookingModel.RefreshItem{
		bookingModel.RefreshItem{
			Id: "booking_details",
		},
	}
	refreshPages := &bookingModel.ReversalRequestAction{
		Type:         sushi.ActionTypeRefreshPages,
		RefreshPages: refreshPageItems,
	}
	response.ActionList = append(response.ActionList, refreshPages)
	return response
}

func (s *NoShowReversalRequestTemplate) GetCleverTapCommonPayload(c *gin.Context, payload *bookingPB.UserBookingRequestQueries) map[string]interface{} {

	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(c)
	commonPayload["booking_id"] = payload.BookingId
	commonPayload["reason_id"] = payload.ReasonId
	commonPayload["query_type_id"] = payload.QueryTypeId
	commonPayload["other_reason"] = payload.OtherReason
	commonPayload["booking_reference_number"] = payload.BookingReferenceNumber
	commonPayload["source"] = "booking_details"
	return commonPayload
}
