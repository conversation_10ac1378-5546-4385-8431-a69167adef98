package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"
	"log"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	centreModel "bitbucket.org/jogocoin/go_api/api/models/booking/centres"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"

	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"

	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type SummercampProductCentersTemplate struct {
	CustomHeader   *centreModel.CustomHeader                          `json:"custom_header,omitempty"`
	EmptyView      *sushi.EmptyViewType1Layout                        `json:"empty_view,omitempty"`
	Results        []*sushi.FitsoImageTextSnippetType22Layout         `json:"results,omitempty"`
	Footer         *sushi.FooterSnippetType2Layout                    `json:"footer,omitempty"`
	HasMore        bool                                               `json:"has_more"`
	PostbackParams string                                             `json:"postback_params,omitempty"`
	PageData       *productPB.GetSummercampCentersForPurchaseResponse `json:"-"`
	PageDataFacilities       *productPB.GetSummercampCentersForPurchaseResponse `json:"-"`
	CourseCategoryPostback []*productPB.PurchaseSelectedUser                 `json:"-"`
}

func GetSummercampProductCentersC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var jsonReq structs.GetCentersForProductCourseReq
	jsonReq = c.MustGet("jsonData").(structs.GetCentersForProductCourseReq)

	productClient := util.GetProductClient()
	previousFacilityIds := getFacilityRequestPostbackParams(jsonReq.PostbackParams)

	var courseCategoryPostback []*productPB.PurchaseSelectedUser
	if jsonReq.CourseCategoryPostback != "" {
		log.Println("GetSummercampProductCentersC: json data",jsonReq.CourseCategoryPostback)
		err := json.Unmarshal([]byte(jsonReq.CourseCategoryPostback), &courseCategoryPostback)
		if err != nil {
			log.Println("GetSummercampProductCentersC: json marshal error", err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
			return
		}
		log.Println("GetSummercampProductCentersC: postbackData: ",courseCategoryPostback)
	}
	//filledSlotData := getAcademySlotsPostbackParams(json.PostbackParams)
	reqData := &productPB.GetSummercampCentersForPurchaseRequest{
		StartDate:           jsonReq.PlanStartDate,
		PreviousFacilityIds: previousFacilityIds,
		//AcademySlotFilledData: 		filledSlotData,
		UserId:                     jsonReq.UserId,
		SummercampProductMappingId: jsonReq.SummercampProductMappingId,
		ProductId:                  jsonReq.ProductId,
		PurchaseSelectedUsers: 		courseCategoryPostback,
	}

	var template *SummercampProductCentersTemplate
	response, err := productClient.GetSummercampCentersForPurchase(ctx, reqData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	responseClosedFacilities, err := productClient.GetSummercampFacilitesForNext15Days(ctx, reqData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	template = &SummercampProductCentersTemplate{
		PageData: 			response,
		PageDataFacilities: responseClosedFacilities,
	}
	template.CourseCategoryPostback = courseCategoryPostback

	template.SetCustomHeaderSection(ctx)
	template.SetFooterSection(ctx)

	if len(response.SummercampCenters) > 0 {
		template.SetResultSection(ctx)
	} else if jsonReq.PostbackParams == "" { // to ensure we don't send empty view for load more
		template.SetEmptyView(ctx)
	}

	template.SetHasMore(ctx)
	template.SetPostbackParams(ctx, previousFacilityIds)
	c.JSON(http.StatusOK, template)
}

func (c *SummercampProductCentersTemplate) SetPostbackParams(ctx context.Context, previousFacilityIds []int32) {

	for _, facilityId := range c.PageData.FacilityIds {
		previousFacilityIds = append(previousFacilityIds, facilityId)
	}

	var allFacilityIdsStringArray []int32
	for _, element := range previousFacilityIds {
		allFacilityIdsStringArray = append(allFacilityIdsStringArray, element)
	}

	payload := map[string][]int32{
		"previous_facility_ids": allFacilityIdsStringArray,
	}

	postbackParams, _ := json.Marshal(payload)
	c.PostbackParams = string(postbackParams)
}

func (c *SummercampProductCentersTemplate) SetHasMore(ctx context.Context) {
	c.HasMore = c.PageData.HasMore
}

func (c *SummercampProductCentersTemplate) SetCustomHeaderSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Select center & slot")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetColor(colorGrey900)
	title.SetFont(fontMed400)

	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.DismissIcon, colorBlack500)

	c.CustomHeader = &centreModel.CustomHeader{
		Type: centreModel.CustomHeaderType1Val,
		CustomHeaderType1: &centreModel.CustomHeaderType1{
			Title: title,
			Icon:  icon,
		},
	}
}

func (c *SummercampProductCentersTemplate) SetFooterSection(ctx context.Context) {
	facilityId := int32(84)

	facilityTitle, _ := sushi.NewTextSnippet("Workout Studio")
	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	facilityTitle.SetColor(colorBlack500)
	facilityTitle.SetFont(fontSemi400)

	facilitySubtitle, _ := sushi.NewTextSnippet("Malviya Nagar, New Delhi")
	colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	facilitySubtitle.SetColor(colorGrey500)
	facilitySubtitle.SetFont(fontMed200)

	ratingTitle, _ := sushi.NewTextSnippet("4.7")
	colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
	colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	ratingTitle.SetColor(colorWhite900)
	ratingTitle.SetFont(fontMed100)
	ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
	ratingTitle.SetPrefixIcon(ratingIcon)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}
	rating := &sushi.RatingSnippetBlockItem{
		Title:   ratingTitle,
		BgColor: bgColor,
	}

	facilityImage, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/GymAtWorkoutStudio01617731389.jpg")
	facilityImage.SetAspectRatio(1)
	facilityImage.SetType(sushi.ImageTypeRounded)
	facilityImage.SetHeight(64)
	facilityImage.SetWidth(64)

	bottomContainerTitle, _ := sushi.NewTextSnippet("Mon, Wed & Fri at 05:00 - 06:00 PM")
	bottomContainerTitle.SetColor(colorBlack500)
	bottomContainerTitle.SetFont(fontMed200)
	bottomContainer := &sushi.BottomContainerSnippetItem{Title: bottomContainerTitle}

	academySnippetData := &sushi.AcademySnippetData{
		Id:              facilityId,
		Title:           facilityTitle,
		Subtitle:        facilitySubtitle,
		Rating:          rating,
		Image:           facilityImage,
		BottomContainer: bottomContainer,
	}
	updateAcademySlotPurchaseDetails := &sushi.UpdateAcademySlotPurchaseDetails{
		//AcademySlotId:      int32(123),
		AcademySnippetData: academySnippetData,
		SummercampSlotId:   int32(123),
	}

	updateAcademySlotPurchaseDetailsClickAction := sushi.GetClickAction()
	updateAcademySlotPurchaseDetailsClickAction.SetAcademySlotPurchaseDetails(updateAcademySlotPurchaseDetails)

	fontMed500, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:             sushi.FooterButtonTypeSolid,
		Text:             "Select a center to proceed",
		Font:             fontMed500,
		Id:               "bottom_button_id1",
		BgColor:          colorGrey500,
		IsActionDisabled: 1,
		ClickAction:      updateAcademySlotPurchaseDetailsClickAction,
	}

	items := []sushi.FooterSnippetType2ButtonItem{buttonItem}

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	c.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
}

func (c *SummercampProductCentersTemplate) SetEmptyView(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("No facility currently available for selected summer camp sports")
	colorGrey700, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	fontReg400, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	title.SetColor(colorGrey700)
	title.SetFont(fontReg400)

	image, _ := sushi.NewImage(util.GetCDNLink("uploads/yourmemberships1619098531.png"))
	image.SetHeight(158)
	image.SetWidth(160)
	snippet := &sushi.EmptyViewType1Snippet{
		Title: title,
		Image: image,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}
	c.EmptyView = &sushi.EmptyViewType1Layout{
		LayoutConfig:          layoutConfig,
		EmptyViewType1Snippet: snippet,
	}
}

func (c *SummercampProductCentersTemplate) SetResultSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoImageTextSnippetType22,
		LayoutType:  sushi.LayoutTypeGrid,
	}
	facilityIdMap := make(map[int32]bool)
	for _, facilityData := range c.PageData.SummercampCenters {

		facilityId := facilityData.FacilityId

		if len(facilityData.DisplayName) == 0 || len(facilityData.SubzoneName) == 0 || len(facilityData.DisplayPicture) == 0 || len(facilityData.SportName) == 0 {
			continue
		}

		facilityTitle, _ := sushi.NewTextSnippet(facilityData.DisplayName)
		colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		facilityTitle.SetColor(colorBlack500)
		facilityTitle.SetFont(fontSemi400)

		facilitySubtitle, _ := sushi.NewTextSnippet(facilityData.SubzoneName)
		colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		facilitySubtitle.SetColor(colorGrey500)
		facilitySubtitle.SetFont(fontMed200)

		distanceTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facilityData.Distance))
		fontSemi50, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		distanceTitle.SetColor(colorBlack500)
		distanceTitle.SetFont(fontSemi50)

		colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag := &sushi.Tag{
			Title:   distanceTitle,
			BgColor: colorWhite500,
		}

		ratingTitle, _ := sushi.NewTextSnippet(facilityData.Rating)
		colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
		fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		ratingTitle.SetColor(colorWhite900)
		ratingTitle.SetFont(fontMed100)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
		ratingTitle.SetPrefixIcon(ratingIcon)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		isNewColorSupported := featuresupport.SupportsNewColor(ctx)
		if isNewColorSupported {
			bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}
		rating := &sushi.RatingSnippetBlockItem{
			Title:   ratingTitle,
			BgColor: bgColor,
		}

		facilityImage, _ := sushi.NewImage(facilityData.DisplayPicture)
		facilityImage.SetAspectRatio(1)
		facilityImage.SetType(sushi.ImageTypeRounded)
		facilityImage.SetHeight(64)
		facilityImage.SetWidth(64)

		subtitleText2 := facilityData.SportName
		if c.PageData.SportDetails.SportId == common.SWIMMING_SPORT_ID {
			pool := "Outdoor Pool"
			if util.Contains(common.IndoorPools,facilityData.FsId) {
				pool = "Indoor Pool"
			}
			subtitleText2 = fmt.Sprintf("%s {teal-500|- %s} ", c.PageData.SportDetails.SportName, pool)
			if len(facilityData.NonOperationalDays) > 0 {
				dayStrings := make([]string, 0)
				for _, day := range facilityData.NonOperationalDays {
					dayStrings = append(dayStrings, time.Weekday(day).String())
				}
				
				var subtitle2Text string
				if len(dayStrings) == 1 {
					subtitle2Text = fmt.Sprintf("Swimming closes on %s for maintenance", dayStrings[0])
				} else if len(dayStrings) != 0 {
					subtitle2Text = fmt.Sprintf(
						"Swimming closes on %s and %s for maintenance",
						strings.Join(dayStrings[0:len(dayStrings)-1], ", "),
						dayStrings[len(dayStrings)-1],
					)
				}
				subtitleText2 += fmt.Sprintf("\n<regular-200 |{red-500|%s} >", subtitle2Text)
			} else if common.FsIdNonOperationalDay[facilityData.FsId] > 0 {
				dayStrings := make([]string, 0)
				dayStrings = append(dayStrings, time.Weekday(common.FsIdNonOperationalDay[facilityData.FsId]).String())
				var subtitle2Text string
				if len(dayStrings) == 1 {
					subtitle2Text = fmt.Sprintf("Swimming closes on %s for maintenance", dayStrings[0])
				} else if len(dayStrings) != 0 {
					subtitle2Text = fmt.Sprintf(
						"Swimming closes on %s and %s for maintenance",
						strings.Join(dayStrings[0:len(dayStrings)-1], ", "),
						dayStrings[len(dayStrings)-1],
					)
				}
				subtitleText2 += fmt.Sprintf("\n<regular-200 |{red-500|%s} >", subtitle2Text)
			}
		} else if c.PageData.SportDetails.SportId == common.BADMINTON_SPORT_ID && len(facilityData.NonOperationalDays) > 0 {
			subtitleText2 = fmt.Sprintf("%s ", c.PageData.SportDetails.SportName)
				dayStrings := make([]string, 0)
				for _, day := range facilityData.NonOperationalDays {
					dayStrings = append(dayStrings, time.Weekday(day).String())
				}
				
				var subtitle2Text string
				if len(dayStrings) == 1 {
					subtitle2Text = fmt.Sprintf("Facility closes on %s for maintenance", dayStrings[0])
				} else if len(dayStrings) != 0 {
					subtitle2Text = fmt.Sprintf(
						"Facility closes on %s and %s for maintenance",
						strings.Join(dayStrings[0:len(dayStrings)-1], ", "),
						dayStrings[len(dayStrings)-1],
					)
				}
				subtitleText2 += fmt.Sprintf("\n<regular-200 |{red-500|%s} >", subtitle2Text)
		}
		subtitle2, _ := sushi.NewTextSnippet(subtitleText2)
		subtitle2.SetIsMarkdown(1)
		subtitle2.SetColor(colorBlack500)
		fontSemiBold200, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
		subtitle2.SetFont(fontSemiBold200)

		topContainer := &sushi.TopContainer{
			Id:        facilityId,
			Title:     facilityTitle,
			SubTitle:  facilitySubtitle,
			SubTitle2: subtitle2,
			Rating:    rating,
			Tag:       tag,
			Image:     facilityImage,
		}
		facilityIdMap[facilityId] = true

		var bottomContainer []*sushi.BottomContainerSnippetItem
		slotsCount := 0
		soldOutSlotsCount := 0
		for _, summercampProductMapping := range facilityData.SummercampProductMapping {
			var slots []*sushi.Slot
			slotsCount += len(summercampProductMapping.SummercampSlots)
			for _, facilitySlot := range summercampProductMapping.SummercampSlots {
				if facilitySlot.Capacity == 0 {
					soldOutSlotsCount++
				}
				timingString := facilitySlot.SlotTiming1
				slotTitle, _ := sushi.NewTextSnippet(timingString)

				button, _ := sushi.NewButton(sushi.ButtonTypeSolid)
				button.SetText("Proceed with selected slot")

				commonPayload := make(map[string]interface{})
				commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
				commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
				commonPayload["source"] = "summercamp_slot_center_page"
				tapEname := &sushi.EnameData{
					Ename: "summercamp_proceed_with_slot_tap",
				}
				tapEvent := sushi.NewClevertapEvents()
				tapEvent.SetTap(tapEname)
				tapTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, tapEvent)
				button.AddClevertapTrackingItem(tapTrackItem)

				colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
				fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

				facilityTitleItem, _ := sushi.NewTextSnippet(facilityData.DisplayName)
				facilityTitleItem.SetColor(colorGrey900)
				facilityTitleItem.SetFont(fontMed100)

				dowTitleItem, _ := sushi.NewTextSnippet(c.PageData.SportDetails.SportName)
				dowTitleItem.SetColor(colorGrey900)
				dowTitleItem.SetFont(fontMed100)
				
				sportImage, _ := sushi.NewImage(c.PageData.SportDetails.Icon)//(util.GetCDNLink(common.AllSummercampCourse[c.PageData.SportDetails.SportId].SportIconPath))
				sportImage.SetAspectRatio(1)
				sportImage.SetType(sushi.ImageTypeCircle)
				sportImage.SetHeight(12)
				sportImage.SetWidth(12)

				slotInfo := make([]*sushi.SubtitleItem, 0)
				slotInfo = append(slotInfo, &sushi.SubtitleItem{
					Type: "facility",
					Item: &sushi.Item{Text: facilityTitleItem},
				})
				slotInfo = append(slotInfo, &sushi.SubtitleItem{
					Type: "sport",
					Item: &sushi.Item{Text: dowTitleItem, Image: sportImage},
				})
				daysText := fmt.Sprintf("Everyday at %s", timingString)
				slotTitleItem, _ := sushi.NewTextSnippet(timingString)
				_, daysStr := util.GetProductDaysOfWeekSummercamp(facilityData.DaysOfWeek)
				if len(facilityData.DaysOfWeek) > 0 {
					daysText = fmt.Sprintf("%s at %s", daysStr, timingString)
					slotTitleItem, _ = sushi.NewTextSnippet(daysText)
				}
				slotTitleItem.SetColor(colorGrey900)
				slotTitleItem.SetFont(fontMed100)
				slotInfo = append(slotInfo, &sushi.SubtitleItem{
					Type: "time",
					Item: &sushi.Item{Text: slotTitleItem},
				})
				
				contextualData := &sushi.ContextualData{
					SnippetId:           facilityId,
					TitleText:           facilityData.DisplayName,
					SubtitleText:        facilityData.SubzoneName,
					RatingText:          facilityData.Rating,
					ImageURL:            facilityData.DisplayPicture,
					BottomContainerText: daysText,
					SummercampSlotId:    facilitySlot.SummercampSlotId,
					ProductId:           summercampProductMapping.ProductId,
					SlotInfo:            slotInfo,
					ProductCategoryId:   13,
				}

				slotFacilityContextualData := sushi.UpdateSlotFacilityContextualData{
					ButtonId:       "bottom_button_id1",
					Button:         button,
					ContextualData: contextualData,
				}

				updateSlotFacilityContextualDataClickAction := sushi.GetClickAction()
				updateSlotFacilityContextualDataClickAction.SetSlotFacilityContextualData(&slotFacilityContextualData)

				slot := &sushi.Slot{
					Title:      slotTitle,
					SlotId:     facilitySlot.SummercampSlotId,
					IsDisabled: facilitySlot.Disabled,
				}
				if !facilitySlot.Disabled {
					slot.ClickAction = updateSlotFacilityContextualDataClickAction
				} else {
					slotSubTitle, _ := sushi.NewTextSnippet("slot sold out")
					iconColorType := sushi.ColorTypeOrange
					if isNewColorSupported {
						iconColorType = sushi.ALERT_LIGHT_THEME
					}
					iconColor, _ := sushi.NewColor(iconColorType, sushi.ColorTint500)
					soldOutIcon, _ := sushi.NewIcon(sushi.ThunderIcon, iconColor)
					slotSubTitle.SetPrefixIcon(soldOutIcon)
					slot.SubTitle = slotSubTitle
				}

				slots = append(slots, slot)
			}
			sectionTitle, _ := sushi.NewTextSnippet(summercampProductMapping.DaysText)
			colorGrey600, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			fontRegular100, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
			sectionTitle.SetColor(colorGrey600)
			sectionTitle.SetFont(fontRegular100)
			sectionTitle.SetKerning(3)

			sectionHeader := &sushi.SectionHeaderType1Snippet{
				Title: sectionTitle,
			}

			colorGrey200, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
			colorGrey50, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
			colorGrey400, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
			fontSemi100, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)

			slotConfig := &sushi.ItemConfig{
				SelectedItemColor:      colorBlack500,
				UnselectedItemColor:    colorGrey200,
				EnabledItemColor:       colorWhite500,
				DisabledItemColor:      colorGrey50,
				DisabledItemTitleColor: colorGrey400,
				DisabledItemTitleFont:  fontSemi100,
			}

			slotElement := &sushi.SlotSection{
				SectionHeader: sectionHeader,
				Slots:         slots,
			}
			bottomContainerElement := &sushi.BottomContainerSnippetItem{
				Type:        "slots",
				SlotSection: slotElement,
				SlotConfig:  slotConfig,
			}
			bottomContainer = append(bottomContainer, bottomContainerElement)
		}

		tagString, tagNumber := getFacilityTag(soldOutSlotsCount, slotsCount)

		var facilityTag *sushi.Tag
		if tagNumber != 3 { 
			facilityTagTitle, _ := sushi.NewTextSnippet(tagString)
			fontSemi50, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
			colorWhite100, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
			facilityTagTitle.SetColor(colorWhite100)
			facilityTagTitle.SetFont(fontSemi50)

			facilityTag = &sushi.Tag{
				Title:   facilityTagTitle,
				BgColor: colorBlack500,
			}
		}

		expandSnippetClickAction := sushi.GetClickAction() 
		if tagNumber != 1 {
			expandSnippetClickAction.SetExpandSnippet()
		}

		item := &sushi.FitsoImageTextSnippetType22Item{
			Tag:          facilityTag,
			TopContainer: topContainer,
			ClickAction:  expandSnippetClickAction,
			IsSelectable: true,
		}
		commonPayload := make(map[string]interface{})
		commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
		commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
		commonPayload["facility_id"] = facilityId
		commonPayload["source"] = "summercamp_slot_center_page"

		if tagNumber == 1 {
			item.IsSelectable = false
		} else {
			item.BottomContainer = bottomContainer
		}
		tapEname := &sushi.EnameData{
			Ename: "summercamp_preferred_center_tap",
		}
		tapEvent := sushi.NewClevertapEvents()
		tapEvent.SetTap(tapEname)
		tapTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, tapEvent)
		item.ClevertapTracking = []*sushi.ClevertapItem{tapTrackItem}

		items := []*sushi.FitsoImageTextSnippetType22Item{item}
		itemsSnippet := &sushi.FitsoImageTextSnippetType22Snippet{
			Items: items,
		}
		resultItem := &sushi.FitsoImageTextSnippetType22Layout{
			LayoutConfig:                       layoutConfig,
			FitsoImageTextSnippetType22Snippet: itemsSnippet,
		}

		c.Results = append(c.Results, resultItem)
	}

	for _, facilityData := range c.PageDataFacilities.SummercampCenters {

		facilityId := facilityData.FacilityId
		if facilityIdMap[facilityId] {
			continue
		}
		if len(facilityData.DisplayName) == 0 || len(facilityData.SubzoneName) == 0 || len(facilityData.DisplayPicture) == 0 || len(facilityData.SportName) == 0 {
			continue
		}

		facilityTitle, _ := sushi.NewTextSnippet(facilityData.DisplayName)
		colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		facilityTitle.SetColor(colorBlack500)
		facilityTitle.SetFont(fontSemi400)

		facilitySubtitle, _ := sushi.NewTextSnippet(facilityData.SubzoneName)
		colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		facilitySubtitle.SetColor(colorGrey500)
		facilitySubtitle.SetFont(fontMed200)

		distanceTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facilityData.Distance))
		fontSemi50, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		distanceTitle.SetColor(colorBlack500)
		distanceTitle.SetFont(fontSemi50)

		colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag := &sushi.Tag{
			Title:   distanceTitle,
			BgColor: colorWhite500,
		}

		ratingTitle, _ := sushi.NewTextSnippet(facilityData.Rating)
		colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
		fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		ratingTitle.SetColor(colorWhite900)
		ratingTitle.SetFont(fontMed100)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
		ratingTitle.SetPrefixIcon(ratingIcon)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		isNewColorSupported := featuresupport.SupportsNewColor(ctx)
		if isNewColorSupported {
			bgColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		}
		rating := &sushi.RatingSnippetBlockItem{
			Title:   ratingTitle,
			BgColor: bgColor,
		}

		facilityImage, _ := sushi.NewImage(facilityData.DisplayPicture)
		facilityImage.SetAspectRatio(1)
		facilityImage.SetType(sushi.ImageTypeRounded)
		facilityImage.SetHeight(64)
		facilityImage.SetWidth(64)
		greyFilter, _ := sushi.NewFilter(sushi.FilterTypeGrayScale)
		greyFilter.Data = "0"
		facilityImage.Filter = greyFilter

		subtitleText2 := facilityData.SportName
		if facilityData.ShowSubtitleSummercamp {
			subtitleText2 += fmt.Sprintf("\n<regular-200 |{grey-500|Change start date to %s onwards to buy for this centre} >", facilityData.Tag)
		}
		subtitle2, _ := sushi.NewTextSnippet(subtitleText2)
		subtitle2.SetIsMarkdown(1)
		subtitle2.SetColor(colorBlack500)
		fontSemiBold200, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
		subtitle2.SetFont(fontSemiBold200)

		facilityTagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Starts %v", facilityData.Tag))
		colorBlack100, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint100)
		facilityTagTitle.SetColor(colorBlack100)
		facilityTagTitle.SetFont(fontSemi50)
		colorYellow500, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)

		facilityTag := &sushi.Tag{
			Title:   facilityTagTitle,
			BgColor: colorYellow500,
		}
		topContainer := &sushi.TopContainer{
			Id:        facilityId,
			Title:     facilityTitle,
			SubTitle:  facilitySubtitle,
			SubTitle2: subtitle2,
			Rating:    rating,
			Tag:       tag,
			Image:     facilityImage,
		}

		item := &sushi.FitsoImageTextSnippetType22Item{
			Tag:          facilityTag,
			TopContainer: topContainer,
			IsSelectable: false,
		}

		items := []*sushi.FitsoImageTextSnippetType22Item{item}
		itemsSnippet := &sushi.FitsoImageTextSnippetType22Snippet{
			Items: items,
		}
		resultItem := &sushi.FitsoImageTextSnippetType22Layout{
			LayoutConfig:                       layoutConfig,
			FitsoImageTextSnippetType22Snippet: itemsSnippet,
		}

		c.Results = append(c.Results, resultItem)
	}
}
