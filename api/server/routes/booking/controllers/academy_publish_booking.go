package bookingController

import (
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

func AcademyPublishBookingC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	bookingClient := util.GetBookingClient()

	loggedInUser := util.GetUserIDFromContext(c)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusFailure(common.NOT_AUTHORIZED))
		return
	}

	publishBookingRequest := preparePublishBookingRequest(c)
	preBookingResponse, err := bookingClient.AcademyPrebookingConditions(ctx, publishBookingRequest)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if preBookingResponse.Status != nil && preBookingResponse.Status.Status == common.FAILED {
		message := preBookingResponse.Status.Message
		resTemplate := failedResponse(ctx, publishBookingRequest, message)
		c.JSON(http.StatusOK, resTemplate)
		return
	}

	response, err := bookingClient.AcademyPublishBooking(ctx, publishBookingRequest)
	if err != nil {
		log.Printf("Unable to publish bookings | loggedIn userId: %d | Error: %v", loggedInUser, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		message := response.Status.Message
		resTemplate := failedResponse(ctx, publishBookingRequest, message)
		c.JSON(http.StatusOK, resTemplate)
		return
	}
	c.JSON(http.StatusOK, successResponse(ctx, publishBookingRequest, response))
}
