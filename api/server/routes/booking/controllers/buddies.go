package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models/booking/buddies"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// ListBookingBuddiesTemplate represents the response structure of booking for page
type ListBookingBuddiesTemplate struct {
	Header       *model.Header                        `json:"header,omitempty"`
	Footer       *sushi.FooterSnippetType2Layout      `json:"footer,omitempty"`
	ItemConfig   *sushi.ItemConfig                    `json:"item_config,omitempty"`
	BuddyList    []model.Buddy                        `json:"buddy_list,omitempty"`
	AddGuestData AddGuestDataTemplate                 `json:"add_guest_data,omitempty"`
	EmptyView    *sushi.EmptyViewType1Layout          `json:"empty_view,omitempty"`
	Results      *[]sushi.CustomTextSnippetTypeLayout `json:"results,omitempty"`
}

type AddGuestDataTemplate struct {
	GuestData *sushi.FitsoImageTextSnippetType23Layout `json:"guest_data,omitempty"`
	ErrorData *sushi.TextSnippet                       `json:"error_data,omitempty"`
}

func NewListBookingBuddiesTemplate() *ListBookingBuddiesTemplate {
	return &ListBookingBuddiesTemplate{}
}

// ListBookingBuddiesC is the controller layer which calls booking service
func ListBookingBuddiesC(c *gin.Context) {
	cl = util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)
	failedStatus := failedListBookingBuddiesStatus("")

	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(
			http.StatusUnauthorized,
			failedListBookingBuddiesStatus("You are not authorized"),
		)
		return
	}
	var json structs.ListBookingBuddiesRequest
	json = c.MustGet("jsonData").(structs.ListBookingBuddiesRequest)

	listBuddiesRequest := &bookingPB.ListBookingBuddiesRequest{
		FsId:    json.FSID,
		SportId: json.SportId,
	}

	var listBuddiesResponse *bookingPB.ListBookingBuddiesResponse
	var err error
	if json.ProductCategoryId == common.SummerCampCategoryID {
		if listBuddiesRequest.FsId <= 0 {
			c.AbortWithStatusJSON(http.StatusBadRequest, failedStatus)
			return
		}
		listBuddiesResponse, err = cl.ListSummerCampBookingBuddies(ctx, listBuddiesRequest)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, failedStatus)
			return
		}
	} else {
		listBuddiesResponse, err = cl.ListBookingBuddies(ctx, listBuddiesRequest)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, failedStatus)
			return
		}
	}
	response := NewListBookingBuddiesTemplate()
	response.setHeader()
	if len(listBuddiesResponse.BuddyList) > 0 {
		response.setFooter()
		response.setItemConfig()
		err := response.setBuddyList(ctx, listBuddiesResponse.BuddyList, &json)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, failedStatus)
			return
		}
		if featuresupport.SupportsBringAGuest(ctx) && listBuddiesResponse.GuestMode {
			err = response.setGuestList(ctx, listBuddiesResponse.BuddyList, &json)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusInternalServerError, failedStatus)
				return
			}
		}
	} else {
		response.setEmptyView()
	}
	c.JSON(http.StatusOK, response)

}

func (t *ListBookingBuddiesTemplate) setEmptyView() {
	emptyLayout := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}

	emptyTitle, _ := sushi.NewTextSnippet("Oops! No member found.")
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	emptyTitle.SetFont(font)
	emptyTitle.SetColor(color)

	image, _ := sushi.NewImage(util.GetCDNLink("uploads/yourmemberships1619098531.png"))
	emptyViewSnippet := &sushi.EmptyViewType1Snippet{
		Title: emptyTitle,
		Image: image,
	}
	t.EmptyView = &sushi.EmptyViewType1Layout{
		LayoutConfig:          emptyLayout,
		EmptyViewType1Snippet: emptyViewSnippet,
	}
}

func (t *ListBookingBuddiesTemplate) setHeader() {
	title, _ := sushi.NewTextSnippet("Booking for")
	t.Header = &model.Header{
		Title: title,
	}
}

func (t *ListBookingBuddiesTemplate) setItemConfig() {
	selectedItemColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	unselectedItemColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	selectedColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	selectedIcon, _ := sushi.NewIcon(sushi.TickMarkIcon, selectedColor)
	itemConfig := &sushi.ItemConfig{
		SelectedItemColor:   selectedItemColor,
		UnselectedItemColor: unselectedItemColor,
		SelectedIcon:        selectedIcon,
	}
	t.ItemConfig = itemConfig
}

func (t *ListBookingBuddiesTemplate) setFooter() {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type: "solid",
		Text: "Proceed with selected contacts",
		ClickAction: &sushi.ClickAction{
			Type: "select_slots",
		},
	}
	items = append(items, buttonItem)

	buttonData := &sushi.FooterSnippetType2Button{
		Items: &items,
	}

	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	t.Footer = footer
}

func (t *ListBookingBuddiesTemplate) setBuddyList(
	ctx context.Context,
	buddyList []*bookingPB.Buddy,
	reqData *structs.ListBookingBuddiesRequest,
) error {
	var buddies []model.Buddy

	for _, buddy := range buddyList {
		if buddy.IsGuest {
			continue
		}
		image, _ := sushi.NewImage(buddy.ImageUrl)
		image.SetType(sushi.ImageTypeCircle)
		title, _ := sushi.NewTextSnippet(buddy.Name)
		title.SetIsMarkdown(1)
		user := struct {
			UserID int32 `json:"user_id"`
		}{
			UserID: buddy.Id,
		}
		param, err := json.Marshal(user)
		if err != nil {
			fmt.Println(err)
			return err
		}

		b := model.Buddy{
			ID:            buddy.Id,
			Image:         image,
			Title:         title,
			IsSelected:    buddy.IsSelected,
			IsSelectable:  true,
			PostbackParam: string(param),
		}

		if featuresupport.SupportsMedicalForm(ctx) && util.NeedMedicalDetails(ctx, buddy.Id) {
			subtitle, _ := sushi.NewTextSnippet("Update safety info to book")
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			subtitle.SetColor(subtitleColor)
			b.Subtitle = subtitle
			b.IsSelected = false
			b.IsSelectable = false
			b.RightButton = getMedicalFormUpdateButton(buddy.Id, reqData.FSID)
		} else if buddy.Age < util.GetMinAgeForBooking(buddy.IsTrial, reqData.ProductCategoryId) || !util.IsMaxAgeCheckPassed(reqData.ProductCategoryId, buddy.Age){
			// Age validation check
			b.IsSelected = false
			b.IsSelectable = false
			b.RightButton = getRightButton(buddy.Id, buddy.IsTrial, reqData.ProductCategoryId, util.IsMaxAgeCheckPassed(reqData.ProductCategoryId, buddy.Age))
		} else if buddy.SinglekeyTrial {
			showText := fmt.Sprintf("%d complimentry sessions available", buddy.TrialLeft)
			if buddy.TrialLeft == 1 {
				showText = "1 complimentry session available"
			}
			subtitle, _ := sushi.NewTextSnippet(showText)
			subtitleColor, _ := sushi.NewColor(sushi.NEUTRAL_LIGHT_THEME, sushi.ColorTint100)
			fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle.SetColor(subtitleColor)
			subtitle.SetFont(fontMed100)
			b.Subtitle = subtitle
			title, _ := sushi.NewTextSnippet(buddy.Name)
			title.SetIsMarkdown(1)
			b.Title = title
		} else if buddy.SinglekeyTrialNotEligible {
			subtitle, _ := sushi.NewTextSnippet("Your 2 complimentary sessions for the month are exhausted.")
			subtitleColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
			fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			subtitle.SetColor(subtitleColor)
			subtitle.SetFont(fontMed100)
			b.Subtitle = subtitle
			b.IsSelectable = false
			/*rbClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			deeplink := &sushi.Deeplink{
				URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
			}
			rbClickAction.Deeplink = deeplink

			trackingPayload := getCommonTrackingPayload(ctx, reqData)
			tapEname := &sushi.EnameData{
				Ename: "Singlekey_upgade_tap",
			}
			tapEvent := sushi.NewClevertapEvents()
			tapEvent.SetTap(tapEname)
			trackItemTap := sushi.GetClevertapTrackItem(ctx, trackingPayload, tapEvent)

			rightButton := &sushi.Button{
				Type:              sushi.ButtontypeText,
				Size:              sushi.ButtonSizeMedium,
				Text:              "Upgrade",
				ClickAction:       rbClickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItemTap},
			}
			b.RightButton = rightButton*/

			title, _ := sushi.NewTextSnippet(buddy.Name)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			title.SetColor(titleColor)
			title.SetIsMarkdown(1)
			b.Title = title
			greyFilter, _ := sushi.NewFilter(sushi.FilterTypeGrayScale)
			b.Image.Filter = greyFilter
		}

		buddies = append(buddies, b)
	}
	t.BuddyList = buddies
	return nil
}

func (t *ListBookingBuddiesTemplate) setGuestList(
	ctx context.Context,
	buddyList []*bookingPB.Buddy,
	reqData *structs.ListBookingBuddiesRequest,
) error {
	var guests []sushi.FitsoImageTextSnippetType23SnippetItem

	for _, buddy := range buddyList {
		if !buddy.IsGuest {
			continue
		}
		image, _ := sushi.NewImage(buddy.ImageUrl)
		image.SetType(sushi.ImageTypeCircle)
		title, _ := sushi.NewTextSnippet(buddy.Name)
		user := struct {
			UserID int32 `json:"user_id"`
		}{
			UserID: buddy.Id,
		}
		param, err := json.Marshal(user)
		if err != nil {
			log.Println("Error in marshalling userID to params: ", err)
			return err
		}
		b := sushi.FitsoImageTextSnippetType23SnippetItem{
			ID:            buddy.Id,
			Image:         image,
			Title:         title,
			IsSelected:    buddy.IsSelected,
			IsSelectable:  true,
			PostbackParam: string(param),
		}

		guests = append(guests, b)
	}
	if len(guests) > 0 {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoImageTextSnippetType23,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		title, _ := sushi.NewTextSnippet("Bring a guest")
		titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title.SetFont(titleFont)
		title.SetColor(titleColor)

		payload := structs.BringGuestReq{
			FsId:    reqData.FSID,
			Source:  reqData.Source,
			SportId: reqData.SportId,
		}
		postbackParams, err := json.Marshal(payload)
		if err != nil {
			log.Printf("func:SetBringAGuestBookingForBanner, error marshalling, err: %v", err)
		}

		rbClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		deeplink := &sushi.Deeplink{
			URL:            util.GetBringAGuestDeepLink(),
			PostbackParams: string(postbackParams),
		}
		rbClickAction.Deeplink = deeplink

		trackingPayload := getCommonTrackingPayload(ctx, reqData)
		tapEnameHIW := &sushi.EnameData{
			Ename: "bring_a_guest_how_it_works_tap",
		}
		tapEventHIW := sushi.NewClevertapEvents()
		tapEventHIW.SetTap(tapEnameHIW)
		trackItemTapHIW := sushi.GetClevertapTrackItem(ctx, trackingPayload, tapEventHIW)

		rightButton := &sushi.Button{
			Type:              sushi.ButtontypeText,
			Size:              sushi.ButtonSizeMedium,
			Text:              "How it works?",
			ClickAction:       rbClickAction,
			ClevertapTracking: []*sushi.ClevertapItem{trackItemTapHIW},
		}

		tagTitle, _ := sushi.NewTextSnippet("NEW")
		tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
		tagTitle.SetFont(tagTitleFont)
		tagTitle.SetColor(tagTitleColor)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		tag := &sushi.Tag{
			Type:    sushi.TagTypeSmall,
			Title:   tagTitle,
			BgColor: tagBgColor,
		}

		checkboxTitle, _ := sushi.NewTextSnippet("Don’t show this again")
		cbTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		cbTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		checkboxTitle.SetFont(cbTitleFont)
		checkboxTitle.SetColor(cbTitleColor)

		tapEname := &sushi.EnameData{
			Ename: "bring_a_guest_dont_show_again_tap",
		}
		tapEvent := sushi.NewClevertapEvents()
		tapEvent.SetImpression(tapEname)
		trackItemTap := sushi.GetClevertapTrackItem(ctx, trackingPayload, tapEvent)

		checkbox := &sushi.Checkbox{
			IsChecked:         false,
			Title:             checkboxTitle,
			ClevertapTracking: []*sushi.ClevertapItem{trackItemTap},
		}

		guestSnippet := &sushi.FitsoImageTextSnippetType23Snippet{
			CornerRadius: 12,
			Title:        title,
			BgColor:      bgColor,
			BorderColor:  borderColor,
			RightButton:  rightButton,
			Tag:          tag,
			Checkbox:     checkbox,
			Items:        &guests,
		}
		guestLayout := &sushi.FitsoImageTextSnippetType23Layout{
			LayoutConfig:                       layoutConfig,
			FitsoImageTextSnippetType23Snippet: guestSnippet,
		}

		errorData, _ := sushi.NewTextSnippet("Select atleast one existing member to select guest")
		errorColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint600)
		errorFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		errorData.SetFont(errorFont)
		errorData.SetColor(errorColor)

		t.AddGuestData = AddGuestDataTemplate{
			GuestData: guestLayout,
			ErrorData: errorData,
		}

	} else {
		t.SetBringAGuestBookingForBanner(ctx, reqData)
	}

	return nil
}

func (t *ListBookingBuddiesTemplate) SetBringAGuestBookingForBanner(ctx context.Context, reqData *structs.ListBookingBuddiesRequest) {
	var items []sushi.CustomTextSnippetTypeLayout
	layOutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType13,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	seperatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeperator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, seperatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeperator,
	}
	image, _ := sushi.NewImage(util.GetCDNLink("uploads/<EMAIL>"))
	image.SetHeight(110)
	image.SetWidth(351)
	image.AspectRatio = 3.19

	payload := structs.BringGuestReq{
		FsId:   reqData.FSID,
		Source: reqData.Source,
	}
	postbackParams, err := json.Marshal(payload)
	if err != nil {
		log.Printf("func:SetBringAGuestBookingForBanner, error marshalling, err: %v", err)
	}

	clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	deeplink := &sushi.Deeplink{
		URL:            util.GetBringAGuestDeepLink(),
		PostbackParams: string(postbackParams),
	}
	clickAction.Deeplink = deeplink

	trackingPayload := getCommonTrackingPayload(ctx, reqData)

	impressionEname := &sushi.EnameData{
		Ename: "bring_a_guest_booking_for_banner_impression",
	}
	impressionEvent := sushi.NewClevertapEvents()
	impressionEvent.SetImpression(impressionEname)
	trackItemImpression := sushi.GetClevertapTrackItem(ctx, trackingPayload, impressionEvent)

	tapEname := &sushi.EnameData{
		Ename: "bring_a_guest_booking_for_banner_tap",
	}
	tapEvent := sushi.NewClevertapEvents()
	tapEvent.SetTap(tapEname)
	trackItemTap := sushi.GetClevertapTrackItem(ctx, trackingPayload, tapEvent)

	imageTextSnippetType13SnippetItem := &sushi.ImageTextSnippetType13SnippetItem{
		Image:             image,
		CornerRadius:      12,
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItemImpression, trackItemTap},
	}

	imageTextSnippetType13Snippet := &sushi.ImageTextSnippetType13Snippet{
		Items: []*sushi.ImageTextSnippetType13SnippetItem{imageTextSnippetType13SnippetItem},
	}

	item := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:           layOutConfig,
		SnippetConfig:          snippetConfig,
		ImageTextSnippetType13: imageTextSnippetType13Snippet,
	}
	items = append(items, *item)
	t.Results = &items
}

func getMedicalFormUpdateButton(userId int32, fsId int32) *sushi.Button {
	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Update")

	clickAction := sushi.GetClickAction()
	payload := map[string]interface{}{
		"user_id":   userId,
		"fs_id":     fsId,
		"page_type": common.PAGE_TYPE_BUDDY_LIST,
	}
	serailaizedData, _ := json.Marshal(payload)
	deeplink := &sushi.Deeplink{
		URL:            util.GetMedicalDetailsDeeplink(userId),
		PostbackParams: string(serailaizedData),
	}
	clickAction.SetDeeplink(deeplink)
	button.SetClickAction(clickAction)

	return button
}

func getRightButton(userId int32, isTrial bool, productCategoryId int32, isMaxAgeCheckPassed bool) *sushi.Button {
	right_button, _ := sushi.NewButton(sushi.ButtontypeText)
	right_button.Text = "Add age"

	right_button_ca := sushi.GetClickAction()
	right_button_ca.Type = sushi.ClickActionOpenAgeBottomSheet

	payload := map[string]interface{}{
		"post_action": "buddy_list",
		"user_id":     userId,
	}
	postback_params, _ := json.Marshal(payload)
	var open_age_bottom_sheet *sushi.OpenAgeBottomSheet
	if !isMaxAgeCheckPassed {
		open_age_bottom_sheet = util.GetOpenAgeBottomSheetWithMaxAgeCheck(string(postback_params), false, isTrial, productCategoryId)
	} else {
		open_age_bottom_sheet = util.GetOpenAgeBottomSheet(string(postback_params), false, isTrial, productCategoryId)
	}

	right_button_ca.OpenAgeBottomSheet = open_age_bottom_sheet
	right_button.ClickAction = right_button_ca

	return right_button
}

func failedListBookingBuddiesStatus(text string) *bookingPB.ListBookingBuddiesResponse {
	if text == "" {
		text = "Something went wrong. Please try again later!"
	}
	return &bookingPB.ListBookingBuddiesResponse{
		Status: &bookingPB.Status{
			Status:  "failed",
			Message: text,
		},
	}
}

func getCommonTrackingPayload(ctx context.Context, reqData *structs.ListBookingBuddiesRequest) map[string]interface{} {
	trackingPayload := make(map[string]interface{})
	trackingPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackingPayload["source"] = reqData.Source

	return trackingPayload
}
