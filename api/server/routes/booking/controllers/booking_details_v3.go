package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models/booking/details"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

var No_Show_Other_Reason_Id int32 = 13

const (
	WHAT_TO_EXPECT_TITLE_ID           int32 = 1
	WHAT_TO_BRING_WEAR_TITLE_ID       int32 = 2
	WHAT_TO_BRING_TITLE_ID            int32 = 9
	CANCELLATION_AND_NO_SHOW_TITLE_ID int32 = 11
)

type booking struct {
	ReferenceNumber string `uri:"reference_number" binding:"required"`
}

type bookingDetailsTemplate struct {
	Response             *bookingPB.BookingDetailsResponse `json:"-"`
	AllBookingsCancelled bool                              `json:"-"`
	ReferenceNumber      string                            `json:"-"`
	PageHeader           *models.Header                    `json:"header,omitempty"`
	FloatingView         *models.FloatingView              `json:"floating_view,omitempty"`
	Results              []*models.ResultSection           `json:"results,omitempty"`
	HasUpcommingBooking  bool                              `json:"-"`
	HasAnyPenalty        bool                              `json:"-"`
	ContainsGuestBooking bool                              `json:"-"`
}

func GetBookingDetailsV3C(c *gin.Context) {
	var b booking

	if err := c.ShouldBindUri(&b); err != nil {
		c.JSON(400, gin.H{"msg": err})
		log.Printf("Api: booking details, function: GetBookingDetailsV3C, Error: %v", err)
		return
	}

	ctx := util.PrepareGRPCMetaData(c)

	bookingClient := util.GetBookingClient()
	req := &bookingPB.BookingDetailsRequest{
		BookingReferenceNumber: b.ReferenceNumber,
	}

	response, err := bookingClient.GetUserBookingDetail(ctx, req)
	if err != nil {
		log.Printf("Api: Booking details, Failed request, Error: %v", err)
		panic(err)
	}

	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Status == common.FAILED {
		statusCode = http.StatusInternalServerError
		log.Printf("Api: Booking details, Failed request, Error: %v", response.Status.Message)
		c.JSON(statusCode, response)
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
		log.Printf("Api: Booking details, Bad request, Error: %v", response.Status.Message)
		c.JSON(statusCode, response)
		return
	}

	template := bookingDetailsTemplate{
		Response:        response,
		ReferenceNumber: b.ReferenceNumber,
	}
	if featuresupport.SupportsBringAGuest(ctx) {
		template.SetGuestBokingStatus(ctx)
	}

	template.SetHasUpcommingBooking(ctx)
	template.SetHeaders(ctx)
	template.SetFacilityInfoSnippet(ctx)

	if featuresupport.SupportsNewBookingDetailsPage(ctx) {
		if !isProductCategoryAcademy(template.Response.ProductCategoryId) && !isProductCategorySummerCamp(template.Response.ProductCategoryId) {
			template.SetFloatingView(ctx)
		}
		template.SetInviteSection(ctx)
		template.SetWhatBringSection(ctx)
		template.SetUsersSection(ctx)
		template.SetSharingCourtSection(ctx)
	} else {
		template.SetUsersSection(ctx)
		template.SetSharingCourtSection(ctx)
		template.SetWhatBringSection(ctx)
	}
	if !isProductCategoryAcademy(template.Response.ProductCategoryId) && !isProductCategorySummerCamp(template.Response.ProductCategoryId) {
		template.SetCancellationPolicySection(ctx)
	}
	template.SetFooterButtonsSection(ctx)

	c.JSON(statusCode, template)
}

func (b *bookingDetailsTemplate) SetGuestBokingStatus(ctx context.Context) {
	b.ContainsGuestBooking = false
	for _, val := range b.Response.Bookings {
		if !val.BookingCancelled && val.BookingCategory == GUEST_BOOKING_TYPE {
			b.ContainsGuestBooking = true
			break
		}
	}
}

func (b *bookingDetailsTemplate) AddResultSection(section *models.ResultSection) {
	b.Results = append(b.Results, section)
}

func (b *bookingDetailsTemplate) SetFloatingView(ctx context.Context) {
	isTrial := b.isLoggedInUserTrial(ctx)
	if isTrial || !b.HasUpcommingBooking {
		return
	}
	title, _ := sushi.NewTextSnippet("Cancellation & no-show policy")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	border_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	rightButton, _ := sushi.NewButton(sushi.ButtontypeText)
	cancelIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	rightIcon, _ := sushi.NewIcon(sushi.CancelIcon, cancelIconColor)
	rightButton.SetPrefixIcon(rightIcon)
	rightButton.Size = sushi.ButtonSizeMedium
	tapPayload := b.GetCleverTapCommonPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "cancellation_no_show_aerobar_tap",
	}
	tapEvent := sushi.NewClevertapEvents()
	tapEvent.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tapEvent)
	rightButton.AddClevertapTrackingItem(trackItem)

	impressionEname := &sushi.EnameData{
		Ename: "cancellation_no_show_aerobar_impression",
	}
	impressionEvent := sushi.NewClevertapEvents()
	impressionEvent.SetImpression(impressionEname)
	impressionTrackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, impressionEvent)

	b.FloatingView = &models.FloatingView{
		Id:                "211",
		Identifier:        b.Response.Sport.SportName,
		Title:             title,
		BgColor:           bg_color,
		BorderColor:       border_color,
		Button:            rightButton,
		ClevertapTracking: []*sushi.ClevertapItem{impressionTrackItem},
	}
}

func (b *bookingDetailsTemplate) SetInviteSection(ctx context.Context) {
	isTrial := b.isLoggedInUserTrial(ctx)
	if isTrial {
		return
	}

	hasUpcoming := b.HasUpcommingBooking
	if !hasUpcoming {
		return
	}
	title, _ := sushi.NewTextSnippet("Invite a friend")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	bg_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	border_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
	rightIcon, _ := sushi.NewIcon(sushi.ShareIcon, color)

	shareClickAction := sushi.GetClickAction()
	share := &sushi.Share{
		URL: b.Response.Facility.WebUrl,
	}
	if b.Response.ProductCategoryId == common.AcademyCategoryID {
		share.Text = b.GetAcademyInviteText(ctx)
	} else {
		share.Text = b.GetInviteText(ctx)
	}
	shareClickAction.SetShare(share)
	tapPayload := b.GetCleverTapCommonPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "invite_tap_for_member_user",
	}
	inviteEvents := sushi.NewClevertapEvents()
	inviteEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, inviteEvents)

	item := &sushi.ImageTextSnippetType16SnippetItem{
		Title:             title,
		BorderColor:       border_color,
		BgColor:           bg_color,
		RightIcon:         rightIcon,
		CornerRadius:      12,
		ClickAction:       shareClickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	items := []*sushi.ImageTextSnippetType16SnippetItem{item}
	snippet := &sushi.ImageTextSnippetType16Snippet{
		Items: items,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	snippetLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType16,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	section := &models.ResultSection{
		Id:                            "201",
		LayoutConfig:                  snippetLayout,
		SnippetConfig:                 snippetConfig,
		ImageTextSnippetType16Snippet: snippet,
	}
	b.AddResultSection(section)
}

func (b *bookingDetailsTemplate) SetHeaders(ctx context.Context) {
	if !featuresupport.SupportsNewBookingDetailsPage(ctx) {
		headerImage, _ := sushi.NewImage(b.Response.Facility.DisplayPicture)
		headerImage.SetType(sushi.ImageTypeRectangle)
		b.PageHeader = &models.Header{Image: headerImage}
		return
	}
	hasUpcoming := b.HasUpcommingBooking

	title, _ := sushi.NewTextSnippet("Your booking")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	b.PageHeader = &models.Header{
		Title: title,
	}

	isTrial := b.isLoggedInUserTrial(ctx)

	if isTrial && hasUpcoming {
		shareIcon, _ := sushi.NewIcon(sushi.ShareIcon, color)
		shareClickAction := sushi.GetClickAction()
		share := &sushi.Share{
			URL: b.Response.Facility.WebUrl,
		}
		if b.Response.ProductCategoryId == common.AcademyCategoryID {
			share.Text = b.GetAcademyInviteText(ctx)
		} else {
			share.Text = b.GetInviteText(ctx)
		}
		shareClickAction.SetShare(share)
		shareIcon.SetClickAction(shareClickAction)

		tapPayload := b.GetCleverTapCommonPayload(ctx)
		tapEname := &sushi.EnameData{
			Ename: "invite_tap_for_trial_user",
		}
		shareButtonEvents := sushi.NewClevertapEvents()
		shareButtonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, shareButtonEvents)
		shareIcon.AddClevertapTrackingItem(trackItem)

		b.PageHeader.RightIcons = append(b.PageHeader.RightIcons, shareIcon)
	}
}

func (b *bookingDetailsTemplate) SetFacilityInfoSnippet(ctx context.Context) {
	layout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType2,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	item := sushi.FitsoImageTextSnippetType2SnippetItem{}
	item.TopContainer = b.SetTopContainer(ctx)
	item.CenterContainer = b.SetCenterContainer(ctx)

	hasUpcoming := b.HasUpcommingBooking

	if hasUpcoming {
		showInfoContainerSection := b.checkInfoContainerShow(ctx)
		if showInfoContainerSection {
			item.InfoContainer = b.SetInfoContainerSection(ctx)
		}
	}

	item.BottomContainer = b.SetBottomContainer(ctx)
	item.AlertContainer = b.SetCancelledAlert(ctx)
	item.RatingContainer = b.SetRatingContainer(ctx)

	// hide direction when all bookings are cancelled
	if b.AllBookingsCancelled {
		item.TopContainer.Button = nil
	}

	items := []sushi.FitsoImageTextSnippetType2SnippetItem{item}
	snippet := &sushi.FitsoImageTextSnippetType2Snippet{
		Items: &items,
	}
	section := &models.ResultSection{
		Id:                  "200",
		LayoutConfig:        layout,
		FitsoImageTextType2: snippet,
	}
	b.AddResultSection(section)
}

func (b *bookingDetailsTemplate) SetTopContainer(ctx context.Context) *sushi.FitsoImageTextSnippetType2TopContainer {
	sportImage, _ := sushi.NewImage(b.Response.Sport.Icon)
	sportImage.SetType(sushi.ImageTypeRounded)

	loggedInUserId := util.GetUserIDFromContext(ctx)

	var sportBgColor *sushi.Color
	if val, ok := common.SportIdBackgroundColorMap[b.Response.Sport.SportId]; ok {
		sportBgColor, _ = sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
	} else {
		sportBgColor, _ = sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint200)
	}
	sportImage.SetColor(sportBgColor)

	title, _ := sushi.NewTextSnippet(b.Response.Sport.SportName)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	title.SetColor(color)
	title.SetFont(font)

	var booking *bookingPB.Booking
	for _, val := range b.Response.Bookings {
		booking = val
		break
	}
	bookingTimeObj := time.Unix(booking.BookingTime.Seconds, 0)
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	bookingMonthStr := bookingMonth.String()
	timeStr := bookingTimeObj.Format("03:04 PM")

	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d, %s", bookingDay, bookingMonthStr[:3], bookingYear, timeStr))
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)

	if b.Response.UserDetails[loggedInUserId].BookingStatus == "cancelled" {
		grey_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
		subtitle.SetColor(grey_color)
	} else {
		subtitle.SetColor(color)
	}
	subtitle.SetFont(font)

	topContainer := &sushi.FitsoImageTextSnippetType2TopContainer{
		Image:    sportImage,
		Title:    title,
		Subtitle: subtitle,
	}

	if b.Response.ProductArenaCategoryId > 0 {
		tag := b.SetSessionTypeTagInfo(ctx)
		if tag != nil {
			topContainer.Tag = tag
		}
	}

	for userID := range b.Response.Bookings {
		userDetail := b.Response.UserDetails[userID]
		if userDetail.BookingStatus == "upcoming" {
			// get directions
			clickAction := sushi.GetClickAction()
			openMap := sushi.OpenMap{
				Latitude:  fmt.Sprintf("%.10f", b.Response.Facility.Latitude),
				Longitude: fmt.Sprintf("%.10f", b.Response.Facility.Longitude),
			}

			clickAction.SetOpenMap(&openMap)
			directionIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			directionIcon, _ := sushi.NewIcon(sushi.DirectionIcon, directionIconColor)
			directionColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			directionButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
			directionButton.SetPrefixIcon(directionIcon)
			directionButton.SetBgColor(directionColor)
			directionButton.SetClickAction(clickAction)

			payload := b.GetCleverTapCommonPayload(ctx)
			tapEname := &sushi.EnameData{
				Ename: "get_directions_tap",
			}

			tapEvents := sushi.NewClevertapEvents()
			tapEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, payload, tapEvents)
			directionButton.AddClevertapTrackingItem(trackItem)

			//add tracking for this button
			topContainer.Button = directionButton
			break
		}
	}

	return topContainer
}

func (b *bookingDetailsTemplate) SetSessionTypeTagInfo(ctx context.Context) *sushi.Tag {

	sessionType := b.Response.ProductArenaCategoryId

	var sessionInfo SessionInfo

	if sessionType != sessionTypeBuddyHour {
		if _, ok := sessionTypes[sessionType]; !ok {
			return nil
		}

		if !sessionTypes[sessionType].CoachingAvailable {
			return nil
		}
		sessionInfo = sessionTypes[sessionType]
	} else {
		if b.Response.Sport.SportId == common.SWIMMING_SPORT_ID || b.Response.Sport.SportId == common.BADMINTON_SPORT_ID {
			sessionInfo = buddyHourSessionTypes[b.Response.Sport.SportId]
		}
	}

	if len(sessionInfo.Name) == 0 {
		return nil
	}

	tagTitleText := sessionInfo.Name
	tagTitle, _ := sushi.NewTextSnippet(tagTitleText)
	tagTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	tagTitle.SetColor(tagTitleColor)
	tagTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
	tagTitle.SetFont(tagTitleFont)

	var tagBgColor sushi.ColorType
	var tagBorderColor sushi.ColorType
	if sessionType == sessionTypeGuided || sessionType == sessionTypeBuddyHour {
		tagBgColor = sushi.ColorTypeGrey
		tagBorderColor = sushi.ColorTypeGrey
	} else if sessionType == sessionTypeSkillup || sessionType == sessionTypeSummerCamp {
		tagBgColor = sushi.ColorTypeBlue
		tagBorderColor = sushi.ColorTypeBlue
	} else {
		return nil
	}

	tagBgcolor, _ := sushi.NewColor(tagBgColor, sushi.ColorTint100)
	tagBordercolor, _ := sushi.NewColor(tagBorderColor, sushi.ColorTint200)

	if sessionType == sessionTypeSummerCamp {
		tagBgcolor, _ = sushi.NewColor(tagBgColor, sushi.ColorTint50)
		tagBordercolor, _ = sushi.NewColor(tagBorderColor, sushi.ColorTint100)
	}

	imageLink := sessionInfo.Icon2
	image, _ := sushi.NewImage(imageLink)
	image.SetHeight(14)
	image.SetWidth(14)
	image.SetType(sushi.ImageTypeCircle)
	image.SetAspectRatio(1)

	tag := &sushi.Tag{
		Title:       tagTitle,
		BgColor:     tagBgcolor,
		BorderColor: tagBordercolor,
		Image:       image,
	}

	return tag
}

func (b *bookingDetailsTemplate) SetCenterContainer(ctx context.Context) *sushi.FitsoImageTextSnippetType2CenterContainer {
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)

	title, _ := sushi.NewTextSnippet(b.Response.Facility.DisplayName)
	title.SetColor(color)
	title.SetFont(font)

	subtitle, _ := sushi.NewTextSnippet(b.Response.Facility.SubzoneName)
	subtitle.SetColor(color)
	subtitle.SetFont(font)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("Center details")
	button.SetSize(sushi.ButtonSizeMedium)
	button.SetAlignment(sushi.ButtonAlignmentLeft)

	// disable center details cta in case of closed facility
	if b.Response.Facility.OperationStatus == 0 {
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		button.SetActionDisabled()
		button.SetColor(color)
	} else {
		header_city_id := util.GetCityIDFromContext(ctx)
		clickAction := sushi.GetClickAction()
		if b.Response.Facility.CityIdV2 == header_city_id {
			deeplink := &sushi.Deeplink{
				URL: b.Response.Facility.Deeplink,
			}
			clickAction.SetDeeplink(deeplink)
		} else {
			customAlert := util.CustomAlertForCityChange(ctx)
			clickAction.SetCustomAlertAction(customAlert)
		}
		button.SetClickAction(clickAction)
	}

	centerContainer := &sushi.FitsoImageTextSnippetType2CenterContainer{
		Title:    title,
		Subtitle: subtitle,
		Button:   button,
	}

	return centerContainer
}

func (s *bookingDetailsTemplate) SetInfoContainerSection(ctx context.Context) *sushi.FitsoImageTextSnippetType2InfoContainer {
	var titleText, subTitleText, imageLink string

	titleText = "Monsoon Alert"
	subTitleText = "Please note that this is an open facility. In case it rains, the center may be closed."
	imageLink = util.GetCDNLink("uploads/Group240401627311075.png")

	if s.checkGoldenFinnSwim(ctx) {
		titleText = "Double vaccination mandatory"
		subTitleText = "Please get Aarogya Setu vaccination status checked at the center"
		imageLink = util.GetCDNLink("uploads/MicrosoftTeams-image(5)1627286620.png")
	}

	if isProductCategorySummerCamp(s.Response.ProductCategoryId) {
		titleText = "Alert"
		subTitleText = "Entry will not be allowed 5 minutes post the session start time."
	}

	title, _ := sushi.NewTextSnippet(titleText)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetFont(titleFont)
	subTitle, _ := sushi.NewTextSnippet(subTitleText)
	subTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subTitle.SetColor(subTitleColor)
	subTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	subTitle.SetFont(subTitleFont)
	image, _ := sushi.NewImage(imageLink)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	infoContainer := &sushi.FitsoImageTextSnippetType2InfoContainer{
		Title:        title,
		SubTitle:     subTitle,
		BgColor:      bgColor,
		BorderColor:  borderColor,
		CornerRadius: int32(12),
	}
	if !isProductCategorySummerCamp(s.Response.ProductCategoryId) {
		infoContainer.Image = image
	}
	return infoContainer
}

func (b *bookingDetailsTemplate) SetBottomContainer(ctx context.Context) *sushi.FitsoImageTextSnippetType2BottomContainer {
	var booking *bookingPB.Booking
	for _, val := range b.Response.Bookings {
		booking = val
	}
	createdBy := booking.CreatedBy
	referenceNumber := booking.BookingReferenceNumber

	title2, _ := sushi.NewTextSnippet("Booking ID")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	title2.SetColor(color)
	title2.SetFont(font)

	subtitle2, _ := sushi.NewTextSnippet(referenceNumber)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	subtitle2.SetColor(color)
	subtitle2.SetFont(font)
	subtitle2.SetIsCopyingEnabled(true)

	bottomContainer := &sushi.FitsoImageTextSnippetType2BottomContainer{
		Title2:    title2,
		Subtitle2: subtitle2,
	}

	var user *bookingPB.User
	for _, val := range b.Response.UserDetails {
		if val.UserId == createdBy {
			user = val
			break
		}
	}
	if user != nil {
		title1, _ := sushi.NewTextSnippet("Booked by")
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		title1.SetColor(color)
		title1.SetFont(font)

		subtitle1, _ := sushi.NewTextSnippet(user.Name)
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		subtitle1.SetColor(color)
		subtitle1.SetFont(font)

		bottomContainer.Title1 = title1
		bottomContainer.Subtitle1 = subtitle1
	}
	return bottomContainer
}

func (b *bookingDetailsTemplate) SetCancelledAlert(ctx context.Context) *sushi.FitsoImageTextSnippetType2Alert {
	loggedInUserId := util.GetUserIDFromContext(ctx)

	loggedInUserDetails, ok := b.Response.UserDetails[loggedInUserId]
	if !ok {
		return nil
	}

	var alertTitle *sushi.TextSnippet
	if !loggedInUserDetails.IsMaster {
		if loggedInUserDetails.BookingStatus != "cancelled" {
			return nil
		}
		booking, ok := b.Response.Bookings[loggedInUserId]
		if !ok {
			return nil
		}

		if loggedInUserId == booking.CancelBy {
			alertTitle, _ = sushi.NewTextSnippet("You have cancelled this booking")
		} else {
			alertTitle, _ = sushi.NewTextSnippet("Your booking has been cancelled")
		}
	}
	if loggedInUserDetails.IsMaster {
		cancelledBookingCount := 0
		cancelledByLoginUserCount := 0
		for _, val := range b.Response.UserDetails {
			if val.BookingStatus == "cancelled" {
				cancelledBookingCount++

				booking, ok := b.Response.Bookings[val.UserId]
				if !ok {
					continue
				}
				if booking.CancelBy == loggedInUserId {
					cancelledByLoginUserCount++
				}
			}
		}
		if cancelledBookingCount != len(b.Response.Bookings) {
			return nil
		}
		if cancelledByLoginUserCount == len(b.Response.Bookings) {
			alertTitle, _ = sushi.NewTextSnippet("You have cancelled this booking")
		} else {
			alertTitle, _ = sushi.NewTextSnippet("This booking has been cancelled")
		}
	}
	b.AllBookingsCancelled = true

	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint200)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)

	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	alertTitle.SetColor(color)
	alertTitle.SetFont(font)

	alertImage, _ := sushi.NewImage(util.GetCDNLink("uploads/Group235971617613291.png"))
	alertImage.SetType(sushi.ImageTypeRounded)
	alertImage.SetHeight(20)
	alertImage.SetWidth(20)

	alert := &sushi.FitsoImageTextSnippetType2Alert{
		BorderColor: borderColor,
		BgColor:     bgColor,
		Image:       alertImage,
		Title:       alertTitle,
	}
	return alert
}

func (b *bookingDetailsTemplate) SetRatingContainer(ctx context.Context) *sushi.FitsoImageTextSnippetType2RatingContainer {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	loggedInUserDetails, ok := b.Response.UserDetails[loggedInUserId]
	if !ok {
		return nil
	}
	booking := &bookingPB.Booking{}
	if !loggedInUserDetails.IsRatingEligible {
		for userID, data := range b.Response.UserDetails {
			childBooking, ok := b.Response.Bookings[userID]
			if !ok || !data.IsRatingEligible {
				continue
			}
			booking = childBooking
			break
		}
	} else {
		booking, ok = b.Response.Bookings[loggedInUserId]
		if !ok {
			return nil
		}
	}

	if booking.BookingId == 0 {
		return nil
	}

	isRated := false
	var ratingBottomContainer *sushi.FitsoImageTextSnippetType2RatingBottomContainer
	var borderColor *sushi.Color
	var bgColor *sushi.Color
	var snippetTitle *sushi.TextSnippet
	var snippetSubtitle *sushi.TextSnippet
	var ratingStars []*sushi.Button

	if b.Response.UserFeedback != nil { // has rated previously
		isRated = true
		ratingId := b.Response.UserFeedback.RatingId
		tagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f", float32(ratingId)))
		tagColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagIcon, _ := sushi.NewIcon(sushi.RatingIconCode, tagColor)
		tagTitle.SetColor(tagColor)
		tagTitle.SetPrefixIcon(tagIcon)

		var tagBgColorCode sushi.ColorType
		var tagTintCode sushi.ColorTint
		if float32(ratingId) > 4.0 {
			tagBgColorCode = sushi.ColorTypeGreen
			tagTintCode = sushi.ColorTint800
		} else if float32(ratingId) > 3.0 {
			tagBgColorCode = sushi.ColorTypeGreen
			tagTintCode = sushi.ColorTint600
		} else if float32(ratingId) > 2.0 {
			tagBgColorCode = sushi.ColorTypeYellow
			tagTintCode = sushi.ColorTint600
		} else if float32(ratingId) > 1.0 {
			tagBgColorCode = sushi.ColorTypeRed
			tagTintCode = sushi.ColorTint400
		} else {
			tagBgColorCode = sushi.ColorTypeRed
			tagTintCode = sushi.ColorTint500
		}
		tagBgColor, _ := sushi.NewColor(tagBgColorCode, tagTintCode)

		tag := &sushi.Tag{
			Title:   tagTitle,
			BgColor: tagBgColor,
		}
		ratingTitle, _ := sushi.NewTextSnippet(b.Response.UserFeedback.HighlightText)
		ratingTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		ratingTitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
		ratingTitle.SetColor(ratingTitleColor)
		ratingTitle.SetFont(ratingTitleFont)

		editButton, _ := sushi.NewButton(sushi.ButtontypeText)
		editButton.SetText("Edit")
		editButton.SetSize(sushi.ButtonSizeMedium)
		clickAction := sushi.GetClickAction()
		editDeeplink := &sushi.Deeplink{
			URL: util.GetFeedbackDeeplink(booking.BookingId, booking.BookingReferenceNumber, int32(0)),
		}
		clickAction.SetDeeplink(editDeeplink)
		editButton.SetClickAction(clickAction)

		editFeedbackPayload := b.GetCleverTapCommonPayload(ctx)
		editFeedbackPayload["booking_id"] = booking.BookingId
		editFeedbackPayload["rating_id"] = ratingId
		tapEname := &sushi.EnameData{
			Ename: "edit_feedback_tap",
		}

		feedbackEvents := sushi.NewClevertapEvents()
		feedbackEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, editFeedbackPayload, feedbackEvents)
		editButton.AddClevertapTrackingItem(trackItem)

		ratingBottomContainer = &sushi.FitsoImageTextSnippetType2RatingBottomContainer{
			Tag:         tag,
			Title:       ratingTitle,
			RightButton: editButton,
		}

		borderColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		bgColor, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint200)

		snippetTitle, _ = sushi.NewTextSnippet("Session completed")
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		titleIconColor, _ := sushi.NewColor(sushi.ColorTypeGreen, sushi.ColorTint500)
		titleIcon, _ := sushi.NewIcon(sushi.CheckCircleIcon, titleIconColor)
		snippetTitle.SetColor(color)
		snippetTitle.SetFont(font)
		snippetTitle.SetSuffixIcon(titleIcon)
		snippetTitle.SetAlignment(sushi.TextAlignmentLeft)
		userDetail := b.Response.UserDetails[b.Response.UserFeedback.CreatedBy]
		if b.Response.UserFeedback.CreatedBy != loggedInUserId && userDetail != nil {
			snippetSubtitle, _ = sushi.NewTextSnippet(userDetail.Name + " has rated your session")
		} else {
			snippetSubtitle, _ = sushi.NewTextSnippet("You’ve rated your session")
		}
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		snippetSubtitle.SetColor(color)
		snippetSubtitle.SetFont(font)
		snippetSubtitle.SetAlignment(sushi.TextAlignmentLeft)
	} else { // has never rated
		borderColor, _ = sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint200)
		bgColor, _ = sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint50)
		if featuresupport.SupportsNewColor(ctx) {
			borderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint200)
			bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint50)
		}

		snippetTitle, _ = sushi.NewTextSnippet("Session completed")
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		titleIconColor, _ := sushi.NewColor(sushi.ColorTypeGreen, sushi.ColorTint500)
		titleIcon, _ := sushi.NewIcon(sushi.CheckCircleIcon, titleIconColor)
		snippetTitle.SetColor(color)
		snippetTitle.SetFont(font)
		snippetTitle.SetSuffixIcon(titleIcon)
		snippetTitle.SetAlignment(sushi.TextAlignmentCenter)

		snippetSubtitle, _ = sushi.NewTextSnippet("How did you like your session?")
		color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
		snippetSubtitle.SetColor(color)
		snippetSubtitle.SetFont(font)
		snippetSubtitle.SetAlignment(sushi.TextAlignmentCenter)

		for i := 1; i <= 5; i++ {
			button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
			font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
			icon, _ := sushi.NewIcon(sushi.RatingIconCode, color)
			starBorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
			clickAction := sushi.GetClickAction()
			deeplink := &sushi.Deeplink{
				URL: util.GetFeedbackDeeplink(booking.BookingId, booking.BookingReferenceNumber, int32(i)),
			}
			clickAction.SetDeeplink(deeplink)

			feedbackStarPayload := b.GetCleverTapCommonPayload(ctx)
			feedbackStarPayload["booking_id"] = booking.BookingId
			feedbackStarPayload["rating_id"] = i
			tapEname := &sushi.EnameData{
				Ename: "feedback_star_click",
			}

			feedbackEvents := sushi.NewClevertapEvents()
			feedbackEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, feedbackStarPayload, feedbackEvents)
			button.AddClevertapTrackingItem(trackItem)

			button.SetText(strconv.FormatInt(int64(i), 10))
			button.SetColor(color)
			button.SetFont(font)
			button.SetSuffixIcon(icon)
			button.SetBorderColor(starBorderColor)
			button.SetClickAction(clickAction)
			ratingStars = append(ratingStars, button)
		}
	}
	ratingContainer := &sushi.FitsoImageTextSnippetType2RatingContainer{
		IsRated:         isRated,
		BottomContainer: ratingBottomContainer,
		BorderColor:     borderColor,
		BgColor:         bgColor,
		Title:           snippetTitle,
		Subtitle:        snippetSubtitle,
		RatingStars:     ratingStars,
	}
	return ratingContainer
}

func (b *bookingDetailsTemplate) SetUsersSection(ctx context.Context) {
	snippetLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoAccordionSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	nonCancelledUserIds := make([]int32, 0)
	cancelledUserIds := make([]int32, 0)

	for userID := range b.Response.Bookings {
		if b.Response.UserDetails[userID].BookingStatus == "cancelled" {
			cancelledUserIds = append(cancelledUserIds, userID)
		} else {
			nonCancelledUserIds = append(nonCancelledUserIds, userID)
		}
	}

	var snippetTitle *sushi.TextSnippet
	userImages := make([]*sushi.Image, 0)

	if len(cancelledUserIds) == len(b.Response.Bookings) {
		snippetTitle, _ = sushi.NewTextSnippet("Booking has been cancelled")

		for _, userID := range cancelledUserIds {
			userData := b.Response.UserDetails[userID]
			image, _ := sushi.NewImage(userData.ProfilePicture)
			image.SetType(sushi.ImageTypeCircle)
			image.SetHeight(24)
			image.SetWidth(24)
			image.SetIsGrey()
			userImages = append(userImages, image)
		}
	} else {
		if len(nonCancelledUserIds) == 1 {
			snippetTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("Session booked for %d person", len(nonCancelledUserIds)))
		} else {
			snippetTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("Session booked for %d people", len(nonCancelledUserIds)))
		}
		for _, userID := range nonCancelledUserIds {
			userData := b.Response.UserDetails[userID]
			image, _ := sushi.NewImage(userData.ProfilePicture)
			image.SetType(sushi.ImageTypeCircle)
			image.SetHeight(24)
			image.SetWidth(24)
			userImages = append(userImages, image)
		}
	}
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	snippetTitle.SetFont(font)
	snippetTitle.SetColor(color)

	collapsedListIcon, _ := sushi.NewIcon(sushi.ChevronDownIcon, color)

	snippet := &sushi.FitsoAccordianType1Snippet{
		Title:  snippetTitle,
		Icon:   collapsedListIcon,
		Images: userImages,
	}

	// show expanded view if its 1 booking only
	if len(b.Response.Bookings) == 1 {
		snippet.Expanded = 1
	}

	// invite a friend
	loggedInUserId := util.GetUserIDFromContext(ctx)
	loggedInUserDetails, ok := b.Response.UserDetails[loggedInUserId]
	if ok {
		hasUpcoming := false
		if !loggedInUserDetails.IsMaster {
			if loggedInUserDetails.BookingStatus == "upcoming" {
				hasUpcoming = true
			}
		}
		if loggedInUserDetails.IsMaster {
			for _, val := range b.Response.UserDetails {
				if val.BookingStatus == "upcoming" {
					hasUpcoming = true
					break
				}
			}
		}
		if hasUpcoming && !featuresupport.SupportsNewBookingDetailsPage(ctx) {
			inviteButton, _ := sushi.NewButton(sushi.ButtontypeText)
			inviteButton.SetText("Invite a friend")
			inviteButton.SetSize(sushi.ButtonSizeMedium)
			inviteButton.SetAlignment(sushi.ButtonAlignmentLeft)
			iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			inviteIcon, _ := sushi.NewIcon(sushi.PlusCircleIcon, iconColor)
			inviteButton.SetPrefixIcon(inviteIcon)

			shareClickAction := sushi.GetClickAction()
			share := &sushi.Share{
				Text: b.GetInviteText(ctx),
				URL:  b.Response.Facility.WebUrl,
			}
			shareClickAction.SetShare(share)
			inviteButton.SetClickAction(shareClickAction)

			snippet.BottomButton = inviteButton
		}
	}
	if !featuresupport.SupportsNewBookingDetailsPage(ctx) {
		snippet.Items = b.SetUserListWithTags(ctx)
		penaltyList := b.SetPenaltyDetails(ctx)
		if len(penaltyList) > 0 && util.GetAppTypeFromContext(ctx) == featuresupport.Ios {
			layoutConfig := &sushi.LayoutConfig{
				SnippetType:  sushi.FitsoImageTextSnippetType3,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
			}
			items := []*sushi.FitsoImageTextSnippetType3SnippetItem{}
			for _, elem := range penaltyList {
				items = append(items, elem.FitsoImageTextSnippetType3.Items[0])
			}

			snippet1 := &sushi.FitsoImageTextSnippetType3Snippet{
				Items: items,
			}

			customLayout := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:               layoutConfig,
				FitsoImageTextSnippetType3: snippet1,
			}

			customLayout1 := make([]*sushi.CustomTextSnippetTypeLayout, 0)
			customLayout1 = append(customLayout1, customLayout)
			snippet.PenaltyList = customLayout1
		} else {
			snippet.PenaltyList = penaltyList
		}

	} else {
		snippet.Items = b.SetUserListWithPenaltyDetails(ctx)
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &models.ResultSection{
		Id:                  "209",
		LayoutConfig:        snippetLayout,
		FitsoAccordianType1: snippet,
	}
	passSeperator := true
	if featuresupport.SupportsNewBookingDetailsPage(ctx) && loggedInUserDetails.BookingStatus == "upcoming" && b.Response.Sport.SharedCourtFlag {
		passSeperator = false
	}
	if passSeperator {
		section.SnippetConfig = snippetConfig
	}

	b.AddResultSection(section)
}

func (b *bookingDetailsTemplate) SetUserListWithPenaltyDetails(ctx context.Context) []*sushi.CustomTextSnippetTypeLayout {
	customLayout := make([]*sushi.CustomTextSnippetTypeLayout, 0)
	for userID, booking := range b.Response.Bookings {
		userItem := b.GetUserItemLayout(ctx, userID)
		penaltyItem := b.GetPenaltyItemLayout(ctx, userID, booking)
		customLayout = append(customLayout, userItem)
		if penaltyItem != nil {
			customLayout = append(customLayout, penaltyItem)
		}
	}
	return customLayout
}

func (b *bookingDetailsTemplate) SetUserListWithTags(ctx context.Context) []*sushi.CustomTextSnippetTypeLayout {
	userItems := make([]*sushi.CustomTextSnippetTypeLayout, 0)
	nonCancelledLayout := make([]*sushi.CustomTextSnippetTypeLayout, 0)
	cancelledLayout := make([]*sushi.CustomTextSnippetTypeLayout, 0)
	for userID := range b.Response.Bookings {
		userItem := b.GetUserItemLayout(ctx, userID)
		userDetail := b.Response.UserDetails[userID]
		if userDetail.BookingStatus == "cancelled" {
			cancelledLayout = append(cancelledLayout, userItem)
		} else {
			nonCancelledLayout = append(nonCancelledLayout, userItem)
		}
	}

	if len(cancelledLayout) > 0 && len(nonCancelledLayout) > 0 {
		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		snippetConfig := &sushi.SnippetConfig{
			BottomSeparator: separator,
		}
		index := len(nonCancelledLayout) - 1
		nonCancelledLayout[index].SnippetConfig = snippetConfig
	}
	userItems = append(userItems, nonCancelledLayout...)
	userItems = append(userItems, cancelledLayout...)

	return userItems
}

func (b *bookingDetailsTemplate) GetUserItemLayout(ctx context.Context, userID int32) *sushi.CustomTextSnippetTypeLayout {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	userDetail := b.Response.UserDetails[userID]
	userName := userDetail.Name
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType4,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	if userID == loggedInUserId {
		userName = fmt.Sprintf("%s (You)", userName)
	}
	title, _ := sushi.NewTextSnippet(userName)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	image, _ := sushi.NewImage(userDetail.ProfilePicture)
	image.SetType(sushi.ImageTypeCircle)
	image.SetHeight(40)
	image.SetWidth(40)

	item := &sushi.FitsoImageTextSnippetType4SnippetItem{
		Title: title,
		Image: image,
	}
	if userDetail.BookingStatus == "cancelled" {
		tagTitle, _ := sushi.NewTextSnippet("CANCELLED")
		tagColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		tag := &sushi.Tag{
			Title:   tagTitle,
			BgColor: tagBgColor,
		}
		item.Tag = tag
		item.IsGrey = true
	}

	items := []*sushi.FitsoImageTextSnippetType4SnippetItem{item}
	userSnippet := &sushi.FitsoImageTextSnippetType4Snippet{
		Items: items,
	}
	customLayout := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:               layoutConfig,
		FitsoImageTextSnippetType4: userSnippet,
	}
	return customLayout
}

func (b *bookingDetailsTemplate) SetPenaltyDetails(ctx context.Context) []*sushi.CustomTextSnippetTypeLayout {
	penaltyItems := make([]*sushi.CustomTextSnippetTypeLayout, 0)
	for userID, booking := range b.Response.Bookings {
		itemLayout := b.GetPenaltyItemLayout(ctx, userID, booking)
		if itemLayout != nil {
			penaltyItems = append(penaltyItems, itemLayout)
		}
	}
	return penaltyItems
}

func (b *bookingDetailsTemplate) GetPenaltyItemLayout(ctx context.Context, userID int32, booking *bookingPB.Booking) *sushi.CustomTextSnippetTypeLayout {
	userDetails := b.Response.UserDetails[userID]
	if userDetails.BookingStatus != "late_entry" &&
		userDetails.BookingStatus != "no_show" &&
		userDetails.BookingStatus != "completed" &&
		userDetails.BookingStatus != "others" {
		return nil
	}
	noShowDetails, noShowOk := b.Response.NoShowDetails[userID]
	if !noShowOk {
		return nil
	}

	if (userDetails.BookingStatus == "late_entry" ||
		userDetails.BookingStatus == "no_show") &&
		!b.HasAnyPenalty { // making sure that this condition is evaluated once only
		b.HasAnyPenalty = true
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	userQuery, ok := b.Response.UserQuery[userID]
	loggedInUserId := util.GetUserIDFromContext(ctx)
	title, _ := sushi.NewTextSnippet(userDetails.Name)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	image, _ := sushi.NewImage(userDetails.ProfilePicture)
	image.SetAspectRatio(1)
	image.SetType(sushi.ImageTypeCircle)
	image.SetHeight(24)
	image.SetWidth(24)

	var borderColor *sushi.Color
	var bgColor *sushi.Color
	var subtitle1 *sushi.TextSnippet
	var subtitle2_text string
	var subtitle2 *sushi.TextSnippet
	var subtitle3_text string
	var subtitle3 *sushi.TextSnippet
	var subtitle4_text string
	var subtitle4 *sushi.TextSnippet
	var button *sushi.Button
	var tag *sushi.Tag
	var tagTitle *sushi.TextSnippet
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	if noShowDetails.IsReverted { // no show reverted
		borderColor, _ = sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint200)
		bgColor, _ = sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint50)
		if isNewColorSupported {
			borderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint200)
			bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint50)
		}

		subtitle1, _ = sushi.NewTextSnippet("No Show penalty reversed")
		subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGreen, sushi.ColorTint500)
		subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		subtitle1.SetColor(subtitle1Color)
		subtitle1.SetFont(subtitle1Font)

		if userID == loggedInUserId {
			subtitle2_text = "1 day added back to your membership"
		} else {
			subtitle2_text = "1 day added back to " + userDetails.Name + " membership"
		}
		subtitle2, _ = sushi.NewTextSnippet(subtitle2_text)
		subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle2Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle2.SetColor(subtitle2Color)
		subtitle2.SetFont(subtitle2Font)

		subtitle3, _ = sushi.NewTextSnippet("Please cancel your booking atleast 30 mins in advance if you don't plan to attend and avoid the penalty in future")
		subtitle3Color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		subtitle3Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle3.SetColor(subtitle3Color)
		subtitle3.SetFont(subtitle3Font)
	} else {
		if !ok { // no request
			borderColorType := sushi.ColorTypeOrange
			borderColor, _ = sushi.NewColor(borderColorType, sushi.ColorTint200)
			bgColor, _ = sushi.NewColor(borderColorType, sushi.ColorTint50)

			if userDetails.BookingStatus == "no_show" {
				subtitle1, _ = sushi.NewTextSnippet("Attendance not found at facility")
			} else if userDetails.BookingStatus == "late_entry" {
				subtitle1, _ = sushi.NewTextSnippet("Attendance marked after 15 mins of session start time")
			}
			subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
			subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitle1.SetColor(subtitle1Color)
			subtitle1.SetFont(subtitle1Font)

			if userID == loggedInUserId {
				subtitle2_text = "1 day deducted from your membership"
			} else {
				subtitle2_text = "1 day deducted from " + userDetails.Name + " membership"
			}
			subtitle2, _ = sushi.NewTextSnippet(subtitle2_text)
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			subtitle2Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
			subtitle2.SetColor(subtitle2Color)
			subtitle2.SetFont(subtitle2Font)

			noShowPenaltyApplicationTime := time.Unix(noShowDetails.CreatedAt.Seconds, 0)
			if noShowPenaltyApplicationTime.Add(72 * time.Hour).After(time.Now()) {
				return nil
				button, _ = sushi.NewButton(sushi.ButtontypeText)
				button.SetText("Request a reversal")
				button.SetSize(sushi.ButtonSizeSmall)
				buttonColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				buttonIcon, _ := sushi.NewIcon(sushi.RightIcon, buttonColor)
				button.SetSuffixIcon(buttonIcon)

				noShowRequestPayload := b.GetCleverTapCommonPayload(ctx)
				noShowRequestPayload["booking_id"] = booking.BookingId
				noShowRequestPayload["penalty"] = userDetails.BookingStatus
				tapEname := &sushi.EnameData{
					Ename: "request_for_reversal_click",
				}

				noShowEvents := sushi.NewClevertapEvents()
				noShowEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, noShowRequestPayload, noShowEvents)
				button.AddClevertapTrackingItem(trackItem)

				// reasons bottom sheet click action
				reasons, ok := b.Response.PossibleReasons[userID]
				if ok {
					clickAction := b.SetRequestReversalBottomSheet(ctx, reasons, booking)
					button.SetClickAction(clickAction)
				}
			}
		} else if userQuery.Status == 0 { // request pending
			borderColorType := sushi.ColorTypeOrange
			borderColor, _ = sushi.NewColor(borderColorType, sushi.ColorTint200)
			bgColor, _ = sushi.NewColor(borderColorType, sushi.ColorTint50)

			subtitle1, _ = sushi.NewTextSnippet("Reversal request pending")
			subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if isNewColorSupported {
				subtitle1Color, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			}
			subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitle1.SetColor(subtitle1Color)
			subtitle1.SetFont(subtitle1Font)

			if userDetails.BookingStatus == "late_entry" {
				subtitle2, _ = sushi.NewTextSnippet("Attendance marked 15 mins after session start time")
			} else {
				subtitle2, _ = sushi.NewTextSnippet("Attendance not found at the facility")
			}
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
			subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitle2.SetColor(subtitle2Color)
			subtitle2.SetFont(subtitle2Font)

			if userID == loggedInUserId {
				subtitle3_text = "1 day deducted from your membership"
				subtitle4_text = "It may take upto 48 hrs to update the request"
			} else {
				subtitle3_text = "1 day deducted from " + userDetails.Name + " membership"
				subtitle4_text = "It may take upto 48 hrs to update the request"
			}
			subtitle3, _ = sushi.NewTextSnippet(subtitle3_text)
			subtitle3Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			subtitle3Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
			subtitle3.SetColor(subtitle3Color)
			subtitle3.SetFont(subtitle3Font)

			subtitle4, _ = sushi.NewTextSnippet(subtitle4_text)
			subtitle4Color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			subtitle4Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
			subtitle4.SetColor(subtitle4Color)
			subtitle4.SetFont(subtitle4Font)
		} else if userQuery.Status == 1 { // request processed and declined
			borderColorType := sushi.ColorTypeOrange
			borderColor, _ = sushi.NewColor(borderColorType, sushi.ColorTint200)
			bgColor, _ = sushi.NewColor(borderColorType, sushi.ColorTint50)

			subtitle1, _ = sushi.NewTextSnippet("Reversal request declined")
			subtitle1Color, _ := sushi.NewColor(borderColorType, sushi.ColorTint500)
			subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitle1.SetColor(subtitle1Color)
			subtitle1.SetFont(subtitle1Font)

			var subtitle2Reason string
			if userQuery.ReasonId == 13 {
				subtitle2Reason = userQuery.OtherReason
			} else {
				subtitle2Reason = userQuery.Description
			}
			subtitle2, _ = sushi.NewTextSnippet(fmt.Sprintf("Penalty cannot be reversed for the reason: %s", subtitle2Reason))
			subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
			subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			subtitle2.SetColor(subtitle2Color)
			subtitle2.SetFont(subtitle2Font)

			if userID == loggedInUserId {
				subtitle3_text = "1 day stays deducted from your membership"
			} else {
				subtitle3_text = "1 day stays deducted from " + userDetails.Name + " membership"
			}

			subtitle3, _ = sushi.NewTextSnippet(subtitle3_text)
			subtitle3Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			subtitle3Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
			subtitle3.SetColor(subtitle3Color)
			subtitle3.SetFont(subtitle3Font)
			subtitle4Text := "Please cancel your booking atleast 30 mins in advance if you don't plan to attend and avoid the penalty in future"
			if userDetails.BookingStatus == "late_entry" {
				subtitle4Text = "Please mark your attendance within 15 mins of session start time and avoid the penalty in future"
			}
			subtitle4, _ = sushi.NewTextSnippet(subtitle4Text)
			subtitle4Color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			subtitle4Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
			subtitle4.SetColor(subtitle4Color)
			subtitle4.SetFont(subtitle4Font)
		}
	}
	if userDetails.BookingStatus == "no_show" {
		tagTitle, _ = sushi.NewTextSnippet("No Show")
	} else if userDetails.BookingStatus == "late_entry" {
		tagTitle, _ = sushi.NewTextSnippet("Late Entry")
	}
	if tagTitle != nil {
		tagColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)
		borderColorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			borderColorType = sushi.ALERT_LIGHT_THEME
		}
		tagBgColor, _ := sushi.NewColor(borderColorType, sushi.ColorTint400)
		tag = &sushi.Tag{
			Title:   tagTitle,
			BgColor: tagBgColor,
		}
	}

	item := &sushi.FitsoImageTextSnippetType3SnippetItem{
		BorderColor: borderColor,
		BgColor:     bgColor,
		Subtitle1:   subtitle1,
		Subtitle2:   subtitle2,
		Subtitle3:   subtitle3,
		Subtitle4:   subtitle4,
		Tag:         tag,
		Button:      button,
	}
	if !featuresupport.SupportsNewBookingDetailsPage(ctx) {
		item.Title = title
		item.Image = image
	}
	items := []*sushi.FitsoImageTextSnippetType3SnippetItem{item}
	snippet := &sushi.FitsoImageTextSnippetType3Snippet{
		Items: items,
	}

	customLayout := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:               layoutConfig,
		FitsoImageTextSnippetType3: snippet,
	}
	return customLayout
}

func (b *bookingDetailsTemplate) SetRequestReversalBottomSheet(ctx context.Context, reasons *bookingPB.UserBookingRequestQueries, booking *bookingPB.Booking) *sushi.ClickAction {
	apiClickAction := sushi.GetClickAction()
	reversePenaltyPayload := map[string]interface{}{
		"booking_id":               booking.BookingId,
		"booking_reference_number": booking.BookingReferenceNumber,
		"query_type_id":            1,
	}
	serialized, err := json.Marshal(reversePenaltyPayload)
	if err == nil {
		apiCall := &sushi.APICallAction{
			RequestType: sushi.POSTRequestType,
			URL:         "v2/booking/noshow/revert-request",
			Body:        string(serialized),
		}
		apiClickAction.SetApiCallAction(apiCall)
	}

	bottomSheetTitle, _ := sushi.NewTextSnippet("State a reason")
	bottomSheetHeader := &sushi.OpenInputBottomSheetItemHeader{
		Title: bottomSheetTitle,
	}
	bottomSheetButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	bottomSheetButton.SetText("Submit reversal request")
	bottomSheetButton.SetSize(sushi.ButtonSizeLarge)
	bottomSheetButton.SetClickAction(apiClickAction)

	noShowReasonPayload := b.GetCleverTapCommonPayload(ctx)
	noShowReasonPayload["booking_id"] = booking.BookingId
	noShowReasonPayload["reason_id"] = No_Show_Other_Reason_Id
	tapEname := &sushi.EnameData{
		Ename: "submit_reversal_request",
	}

	noShowEvents := sushi.NewClevertapEvents()
	noShowEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, noShowReasonPayload, noShowEvents)
	bottomSheetButton.AddClevertapTrackingItem(trackItem)

	textViewPlaceholder := &sushi.TextSnippet{
		Placeholder: "Leave a description here",
	}
	textViewMinLength := &sushi.MinLength{
		Length:    10,
		ErrorText: "Minimum 10 characters are mandatory",
	}
	textView := &sushi.TextView{
		Placeholder: textViewPlaceholder,
		MinLength:   textViewMinLength,
	}
	inputBottomSheetItem := &sushi.OpenInputBottomSheetItem{
		OpenInputBottomSheetItemHeader: bottomSheetHeader,
		TextView:                       textView,
		Button:                         bottomSheetButton,
	}
	inputSheetClickAction := sushi.GetClickAction()
	inputSheetClickAction.SetOpenInputBottomSheet(inputBottomSheetItem)

	reasonsList := make([]*sushi.Reason, 0)

	for index, val := range reasons.Reasons {
		text, _ := sushi.NewTextSnippet(val.Description)
		reason := &sushi.Reason{
			Id:         val.ReasonId,
			Title:      text,
			IsSelected: index == 0,
		}
		if val.ReasonId == 13 { // others - custom reason
			continue
		}
		reasonsList = append(reasonsList, reason)
	}

	//shuffling reasons
	rand.Shuffle(len(reasonsList), func(i, j int) {
		reasonsList[i], reasonsList[j] = reasonsList[j], reasonsList[i]
	})

	for index, val := range reasons.Reasons {
		if val.ReasonId == 13 { // others - custom reason
			text, _ := sushi.NewTextSnippet(val.Description)
			reason := &sushi.Reason{
				Id:         val.ReasonId,
				Title:      text,
				IsSelected: index == 0,
			}
			reason.ClickAction = inputSheetClickAction
			reasonsList = append(reasonsList, reason)
		} else {
			continue
		}
	}

	footerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:        sushi.FooterButtonTypeSolid,
		Text:        "Request a reversal",
		ClickAction: apiClickAction,
		Size:        sushi.FooterButtonSizeLarge,
	}

	noShowReasonPayload = b.GetCleverTapCommonPayload(ctx)
	noShowReasonPayload["booking_id"] = booking.BookingId
	tapEname = &sushi.EnameData{
		Ename: "submit_reversal_request",
	}
	noShowEvent := sushi.NewClevertapEvents()
	noShowEvent.SetTap(tapEname)
	trackItems := sushi.GetClevertapTrackItem(ctx, noShowReasonPayload, noShowEvent)

	buttonItem.ClevertapTracking = []*sushi.ClevertapItem{trackItems}

	buttonItems := []sushi.FooterSnippetType2ButtonItem{buttonItem}
	buttonData := &sushi.FooterSnippetType2Button{
		Items: &buttonItems,
	}
	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:       footerLayout,
		FooterSnippetType2: footerSnippet,
	}

	reasonsSheetTitle, _ := sushi.NewTextSnippet("Select a reason")
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	reasonsSheetTitle.SetColor(color)
	reasonsSheetTitle.SetFont(font)

	openPickerSheet := &sushi.OpenReasonPickerSheet{
		Title:  reasonsSheetTitle,
		Footer: footer,
		Items:  reasonsList,
	}
	openSheet := &sushi.OpenSheet{
		Type:                  sushi.ClickActionOpenReasonPickerSheet,
		OpenReasonPickerSheet: openPickerSheet,
	}
	openSheetClickAction := sushi.GetClickAction()
	openSheetClickAction.SetOpenSheet(openSheet)

	return openSheetClickAction
}

func (b *bookingDetailsTemplate) GetCleverTapCommonPayload(ctx context.Context) map[string]interface{} {

	var iBooking *bookingPB.Booking
	for _, val := range b.Response.Bookings {
		iBooking = val
		break
	}

	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["sport_id"] = b.Response.Sport.SportId
	commonPayload["facility_id"] = b.Response.Facility.FacilityId
	commonPayload["booking_time"] = iBooking.BookingTime.Seconds
	commonPayload["booking_reference_number"] = iBooking.BookingReferenceNumber
	if b.Response.ProductCategoryId == common.AcademyCategoryID {
		commonPayload["source"] = "academy_booking_details"
	} else {
		commonPayload["source"] = "booking_details"
	}
	return commonPayload
}

func (b *bookingDetailsTemplate) SetSharingCourtSection(ctx context.Context) {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	loggedInUserDetails, ok := b.Response.UserDetails[loggedInUserId]
	if !ok {
		return
	}
	if loggedInUserDetails.BookingStatus != "upcoming" || !b.Response.Sport.SharedCourtFlag {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.SectionHeaderType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	var pointerItem string
	for _, val := range b.Response.FooterDetails {
		if val.TitleId == WHAT_TO_EXPECT_TITLE_ID && len(val.Pointers) > 0 {
			pointerItem = val.Pointers[0].Text
			break
		}
	}

	if pointerItem == "" || isProductCategoryAcademy(b.Response.ProductCategoryId) || isProductCategorySummerCamp(b.Response.ProductCategoryId) {
		return
	}

	if featuresupport.SupportsNewBookingDetailsPage(ctx) {
		title, _ := sushi.NewTextSnippet(pointerItem)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		title.SetFont(font)
		title.SetColor(color)

		snippet := &sushi.TextSnippetType3Snippet{
			Title: title,
		}

		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
		snippetConfig := &sushi.SnippetConfig{
			BottomSeparator: separator,
		}

		snippetLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.TextSnippetType3,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		section := &models.ResultSection{
			Id:               "210",
			LayoutConfig:     snippetLayout,
			SnippetConfig:    snippetConfig,
			TextSnippetType3: snippet,
		}
		b.AddResultSection(section)
	} else {
		title, _ := sushi.NewTextSnippet(pointerItem)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		title.SetFont(font)

		snippet := &sushi.SectionHeaderType1Snippet{
			Title: title,
		}
		section := &models.ResultSection{
			LayoutConfig:       layoutConfig,
			SectionHeaderType1: snippet,
		}
		b.AddResultSection(section)
	}
}

func (b *bookingDetailsTemplate) SetWhatBringSection(ctx context.Context) {
	hasUpcoming := false
	for _, val := range b.Response.UserDetails {
		if val.BookingStatus == "upcoming" {
			hasUpcoming = true
			break
		}
	}
	if !hasUpcoming {
		return
	}

	items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	items2 := make([]*sushi.FitsoImageTextSnippetType17SnippetItem, 0)

	// Govt. Id card mandatory in what to bring for Apeejay pools
	if b.Response.Facility.FacilityId == 3 || b.Response.Facility.FacilityId == 4 || b.Response.Facility.FacilityId == 5 {
		image, _ := sushi.NewImage(util.GetCDNLink("uploads/id-card1678907254.png"))
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(28)
		image.SetWidth(28)
		image.BgColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

		title, _ := sushi.NewTextSnippet("Govt. Id card is mandatory to enter in premises")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		title.SetColor(color)
		title.SetFont(font)

		newItem := &sushi.FitsoImageTextSnippetType17SnippetItem{
			Image: image,
			Title: title,
		}
		items2 = append(items2, newItem)
	}

	// Mask mandatory in what to bring for Delhi-NCR facilities
	if b.Response.Facility.CityIdV2 == 1 {
		image, _ := sushi.NewImage(util.GetCDNLink("uploads/Screenshot2023-04-20at22910PM1682065740.png"))
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(28)
		image.SetWidth(28)
		image.BgColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)

		title, _ := sushi.NewTextSnippet("Users will be allowed inside the facility with mask only")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		title.SetColor(color)
		title.SetFont(font)

		newItem := &sushi.FitsoImageTextSnippetType17SnippetItem{
			Image: image,
			Title: title,
		}
		items2 = append(items2, newItem)
	}

	for _, val := range b.Response.FooterDetails {
		if val.TitleId != WHAT_TO_BRING_WEAR_TITLE_ID && val.TitleId != WHAT_TO_BRING_TITLE_ID {
			continue
		}
		for _, pointer := range val.Pointers {
			imagePathStr := "uploads/Equipments-black.png"
			if pointer.Src != "" {
				imagePathStr = pointer.Src
			}

			image, _ := sushi.NewImage(util.GetCDNLink(imagePathStr))
			image.SetAspectRatio(1)
			image.SetType(sushi.ImageTypeCircle)
			image.SetHeight(24)
			if featuresupport.SupportsNewBookingDetailsPage(ctx) {
				image.SetHeight(28)
				image.SetWidth(28)
				image.BgColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
			}
			if isProductCategoryAcademy(b.Response.ProductCategoryId) || isProductCategorySummerCamp(b.Response.ProductCategoryId) {
				pointer.Text = strings.Replace(pointer.Text, "Users", "Students", -1)
			}

			title, _ := sushi.NewTextSnippet(pointer.Text)
			color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
			font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			title.SetColor(color)
			title.SetFont(font)

			if featuresupport.SupportsNewBookingDetailsPage(ctx) {
				newItem := &sushi.FitsoImageTextSnippetType17SnippetItem{
					Image: image,
					Title: title,
				}
				if len(pointer.TextSubBody) > 0 {
					collapsed, _ := sushi.NewButton(sushi.ButtontypeText)
					collapsed.SetText("More Info")
					collapsed.Alignment = sushi.ButtonAlignmentLeft
					font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
					color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
					collapsed.SetColor(color)
					collapsed.SetFont(font)
					suffixIcon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, color)
					collapsed.SuffixIcon = suffixIcon
					collapsed.SetSize(sushi.ButtonSizeMedium)
					tapPayload := b.GetCleverTapCommonPayload(ctx)
					moreInfoEname := &sushi.EnameData{
						Ename: "more_info_tap_for_what_to_bring",
					}
					moreInfoEvents := sushi.NewClevertapEvents()
					moreInfoEvents.SetTap(moreInfoEname)
					trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, moreInfoEvents)
					collapsed.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

					expanded, _ := sushi.NewButton(sushi.ButtontypeText)
					expanded.SetText("Less Info")
					expanded.Alignment = sushi.ButtonAlignmentLeft
					expanded.SetColor(color)
					expanded.SetFont(font)
					suffixIcon, _ = sushi.NewIcon("e873", color)
					expanded.SuffixIcon = suffixIcon
					expanded.SetSize(sushi.ButtonSizeMedium)
					lessInfoEname := &sushi.EnameData{
						Ename: "less_info_tap_for_what_to_bring",
					}
					lessInfoEvents := sushi.NewClevertapEvents()
					lessInfoEvents.SetTap(lessInfoEname)
					trackItem1 := sushi.GetClevertapTrackItem(ctx, tapPayload, lessInfoEvents)
					expanded.ClevertapTracking = []*sushi.ClevertapItem{trackItem1}

					newItem.Buttons = &sushi.CollapseExpand{
						Collapsed: collapsed,
						Expanded:  expanded,
					}
					newItem.IsExpanded = false
					layoutConfig := &sushi.LayoutConfig{
						SnippetType: sushi.V2ImageTextSnippetType36,
						LayoutType:  sushi.LayoutTypeGrid,
					}
					innerItems := []sushi.V2ImageTextSnippetType36Layout{}

					layout := sushi.V2ImageTextSnippetType36Layout{
						LayoutConfig: layoutConfig,
					}
					innerSnippet := []sushi.V2ImageTextSnippetType36SnippetItem{}
					for _, subTextPointer := range pointer.TextSubBody {
						image, _ := sushi.NewImage(util.GetCDNLink("/uploads/Ellipse2271617607606.png"))
						image.SetHeight(3)
						image.SetWidth(3)
						image.SetAspectRatio(1.0)
						image.Type = sushi.ImageTypeCircle

						title, _ := sushi.NewTextSnippet(subTextPointer)
						color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
						font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
						title.SetColor(color)
						title.SetFont(font)
						title.IsMarkdown = 1
						title.NumberOfLines = -1

						item := sushi.V2ImageTextSnippetType36SnippetItem{
							Image: image,
							Title: title,
						}
						innerSnippet = append(innerSnippet, item)
					}
					layout.V2ImageTextSnippetType36 = &sushi.V2ImageTextSnippetType36Snippet{
						Items: &innerSnippet,
					}
					innerItems = append(innerItems, layout)
					bg_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
					border_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint200)
					newItem.ExpandedData = &sushi.ExpandedData{
						BgColor:      bg_color,
						BorderColor:  border_color,
						CornerRadius: 8,
						Items:        &innerItems,
					}
				}
				items2 = append(items2, newItem)
			} else {
				item := sushi.ImageTextSnippetType30SnippetItem{
					Image: image,
					Title: title,
				}
				items = append(items, item)
			}
		}
	}

	if len(items) == 0 && len(items2) == 0 {
		return
	}

	layout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	sectionTitle, _ := sushi.NewTextSnippet("WHAT TO BRING")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	sectionTitle.SetColor(color)
	sectionTitle.SetFont(font)
	if !featuresupport.SupportsNewBookingDetailsPage(ctx) {
		sectionTitle.SetKerning(2)
	}

	snippet := &sushi.ImageTextSnippetType30Snippet{
		Title: sectionTitle,
		Items: &items,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &models.ResultSection{
		LayoutConfig:       layout,
		SnippetConfig:      snippetConfig,
		ImageSnippetType30: snippet,
	}
	if featuresupport.SupportsNewBookingDetailsPage(ctx) {

		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		sectionTitle.SetColor(color)
		sectionTitle.SetFont(font)

		impressionPayload := b.GetCleverTapCommonPayload(ctx)
		impressionEname := &sushi.EnameData{
			Ename: "what_to_bring_impression",
		}
		impressionEvent := sushi.NewClevertapEvents()
		impressionEvent.SetImpression(impressionEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, impressionPayload, impressionEvent)

		sectionHeader := sushi.SectionHeaderType1Snippet{
			Title:             sectionTitle,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		layout := &sushi.LayoutConfig{
			SnippetType:  sushi.SectionHeaderType1,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
			ShouldResize: false,
		}
		section = &models.ResultSection{
			Id:                 "202",
			LayoutConfig:       layout,
			SectionHeaderType1: &sectionHeader,
		}
		b.AddResultSection(section)

		itemslayout := &sushi.LayoutConfig{
			SnippetType: sushi.FitsoImageTextSnippetType17,
			LayoutType:  sushi.LayoutTypeGrid,
		}

		newSection := &models.ResultSection{
			Id:            "203",
			LayoutConfig:  itemslayout,
			SnippetConfig: snippetConfig,
			FitsoImageTextSnippetType17: &sushi.FitsoImageTextSnippetType17Snippet{
				Items:             items2,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			},
		}
		b.AddResultSection(newSection)
	} else {
		b.AddResultSection(section)
	}

}

func (b *bookingDetailsTemplate) isLoggedInUserTrial(ctx context.Context) bool {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	for _, booking := range b.Response.Bookings {
		if booking.UserId == loggedInUserId && booking.SubscriptionId == 0 {
			return true
		}
	}
	return false
}

func (b *bookingDetailsTemplate) SetCancellationPolicySection(ctx context.Context) {
	// skip for completed booking
	for _, val := range b.Response.UserDetails {
		if val.BookingStatus == "completed" {
			return
		}
	}

	pointerItems := make([]sushi.V2ImageTextSnippetType36SnippetItem, 0)
	for _, val := range b.Response.FooterDetails {
		if val.TitleId != CANCELLATION_AND_NO_SHOW_TITLE_ID {
			continue
		}
		if b.ContainsGuestBooking {
			guestPointers := []string{
				"No show & late entry penalties for Guest users will be applied on the Member who created the booking",
				"Booking and not attending/ not marking attendance results in a No Show Penalty",
				"Attendance marked later than 15 mins after the session starts results in Late Entry Penalty",
				"Booking (if not attending) should be canceled at least 30 mins in advance to avoid No Show Penalty",
				"Penalty reversal can only be requested within 72 hours of penalty application",
			}
			for _, pointer := range guestPointers {

				imagePathStr := "uploads/Ellipse2271617607635.png"

				image, _ := sushi.NewImage(util.GetCDNLink(imagePathStr))
				image.SetAspectRatio(1)
				image.SetType(sushi.ImageTypeCircle)
				image.SetHeight(6)
				image.SetWidth(6)

				title, _ := sushi.NewTextSnippet(pointer)
				color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				title.SetColor(color)
				title.SetFont(font)
				title.NumberOfLines = -1

				item := sushi.V2ImageTextSnippetType36SnippetItem{
					Image: image,
					Title: title,
				}
				pointerItems = append(pointerItems, item)
			}

		} else if isProductCategorySummerCamp(b.Response.ProductCategoryId) {
			summerCampPointers := []string{
				"Session cancellation is allowed till 30 mins before session start time",
			}
			for _, pointer := range summerCampPointers {

				imagePathStr := "uploads/Ellipse2271617607635.png"

				image, _ := sushi.NewImage(util.GetCDNLink(imagePathStr))
				image.SetAspectRatio(1)
				image.SetType(sushi.ImageTypeCircle)
				image.SetHeight(6)
				image.SetWidth(6)

				title, _ := sushi.NewTextSnippet(pointer)
				color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				title.SetColor(color)
				title.SetFont(font)
				title.NumberOfLines = -1

				item := sushi.V2ImageTextSnippetType36SnippetItem{
					Image: image,
					Title: title,
				}
				pointerItems = append(pointerItems, item)
			}
		} else {
			pointers := val.Pointers
			for _, pointer := range pointers {

				imagePathStr := "uploads/Ellipse2271617607635.png"
				if pointer.Src != "" {
					imagePathStr = pointer.Src
				}

				image, _ := sushi.NewImage(util.GetCDNLink(imagePathStr))
				image.SetAspectRatio(1)
				image.SetType(sushi.ImageTypeCircle)
				image.SetHeight(6)
				image.SetWidth(6)

				title, _ := sushi.NewTextSnippet(pointer.Text)
				color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
				title.SetColor(color)
				title.SetFont(font)
				title.NumberOfLines = -1

				item := sushi.V2ImageTextSnippetType36SnippetItem{
					Image: image,
					Title: title,
				}
				pointerItems = append(pointerItems, item)
			}
		}
	}

	if len(pointerItems) == 0 {
		return
	}

	snippetLayout := &sushi.LayoutConfig{
		SnippetType: sushi.AccordionSnippetType4,
	}
	if !b.isLoggedInUserTrial(ctx) && (b.HasAnyPenalty || b.HasUpcommingBooking) {
		snippetLayout.IsExpanded = true
	}

	snippetTitleText := "Cancellation & No Show policy"
	if isProductCategorySummerCamp(b.Response.ProductCategoryId) {
		snippetTitleText = "Cancellation policy"
	}
	snippetTitle, _ := sushi.NewTextSnippet(snippetTitleText)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	snippetTitle.SetFont(font)

	pointerLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType36,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	pointerSnippet := &sushi.V2ImageTextSnippetType36Snippet{
		Items: &pointerItems,
	}

	pointerLayout := &sushi.AccordionSnippetType4SnippetItem{
		LayoutConfig:             pointerLayoutConfig,
		V2ImageTextSnippetType36: pointerSnippet,
	}
	tapPayload := b.GetCleverTapCommonPayload(ctx)
	impressionEname := &sushi.EnameData{
		Ename: "cancellation_no_show_impression",
	}
	cancellationEvents := sushi.NewClevertapEvents()
	cancellationEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, cancellationEvents)
	snippet := &sushi.AccordionSnippetType4Snippet{
		Expanded:          0,
		Title:             snippetTitle,
		Items:             []*sushi.AccordionSnippetType4SnippetItem{pointerLayout},
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	if !b.isLoggedInUserTrial(ctx) && (b.HasAnyPenalty || b.HasUpcommingBooking) {
		snippet.Expanded = 1
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	separator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		BottomSeparator: separator,
	}

	section := &models.ResultSection{
		Id:             "211",
		LayoutConfig:   snippetLayout,
		SnippetConfig:  snippetConfig,
		AccordionType4: snippet,
	}
	b.AddResultSection(section)
}

func (b *bookingDetailsTemplate) SetFooterButtonsSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType10,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	items := make([]sushi.V2ImageTextSnippetType10SnippetItem, 0)

	// for cancellation
	if b.Response.ProductArenaCategoryId != common.ACADEMY_PRODUCT_ARENA_CATEGORY && b.Response.ProductArenaCategoryId != common.SUMMERCAMP_PRODUCT_ARENA_CATEGORY {
		if button := b.GetCancelledButtonSnippet(ctx); button != nil {
			items = append(items, *button)
		}
	}

	// for need help
	if button := b.GetNeedHelpButtonSnippet(ctx); button != nil {
		items = append(items, *button)
	}

	// adding bottom separator in V2ImageTextSnippetType10SnippetItem but not only for last one
	if len(items) > 1 {
		separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		separator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
		for i, val := range items {
			if i != len(items)-1 {
				val.BottomSeparator = separator
			}
		}
	}

	snippet := &sushi.V2ImageTextSnippetType10Snippet{
		Items: &items,
	}
	section := &models.ResultSection{
		Id:                   "212",
		LayoutConfig:         layoutConfig,
		V2ImageSnippetType10: snippet,
	}
	b.AddResultSection(section)
}

func (b *bookingDetailsTemplate) GetCancelledButtonSnippet(ctx context.Context) *sushi.V2ImageTextSnippetType10SnippetItem {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	loggedInUserDetails, ok := b.Response.UserDetails[loggedInUserId]
	if !ok {
		return nil
	}

	if !loggedInUserDetails.IsMaster {
		if loggedInUserDetails.BookingStatus != "upcoming" {
			return nil
		}
	}
	if loggedInUserDetails.IsMaster {
		hasUpcoming := false
		for _, val := range b.Response.UserDetails {
			if val.BookingStatus == "upcoming" {
				hasUpcoming = true
				break
			}
		}
		if !hasUpcoming {
			return nil
		}
	}

	footerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Text: "Proceed to cancel the booking",
	}

	proceedCancelBtnPayload := b.GetCleverTapCommonPayload(ctx)
	Ename := &sushi.EnameData{
		Ename: "proceed_cancel_booking_click",
	}

	proceedCancelBookingEvents := sushi.NewClevertapEvents()
	proceedCancelBookingEvents.SetTap(Ename)
	trackItem1 := sushi.GetClevertapTrackItem(ctx, proceedCancelBtnPayload, proceedCancelBookingEvents)
	buttonItem.ClevertapTracking = []*sushi.ClevertapItem{trackItem1}

	buttonItems := []sushi.FooterSnippetType2ButtonItem{buttonItem}
	buttonData := &sushi.FooterSnippetType2Button{
		Items: &buttonItems,
	}
	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:       footerLayout,
		FooterSnippetType2: footerSnippet,
	}

	selectedItemColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	unselectedItemColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	selectedIcon, _ := sushi.NewIcon(sushi.TickMarkIcon, selectedItemColor)
	itemConfig := &sushi.CancelledItemConfig{
		SelectedItemColor:   selectedItemColor,
		UnselectedItemColor: unselectedItemColor,
		SelectedIcon:        selectedIcon,
	}

	var createdBy int32
	userList := make([]*sushi.CancelledUserItem, 0)
	guestList := make([]*sushi.CancelledUserItem, 0)

	for userID, booking := range b.Response.Bookings {
		userDetails := b.Response.UserDetails[userID]
		if userDetails.BookingStatus != "upcoming" {
			continue
		}

		createdBy = booking.CreatedBy
		if createdBy != loggedInUserId && loggedInUserId != userID {
			continue
		}
		image, _ := sushi.NewImage(userDetails.ProfilePicture)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(32)
		image.SetWidth(32)

		titleText := userDetails.Name
		if userID == loggedInUserId {
			titleText = fmt.Sprintf("%s (You)", titleText)
		}
		title, _ := sushi.NewTextSnippet(titleText)

		userItem := &sushi.CancelledUserItem{
			Id:    strconv.FormatInt(int64(booking.BookingId), 10),
			Image: image,
			Title: title,
		}

		if userID == booking.CreatedBy {
			userItem.BookingOwner = true
		}

		cancelBookingUserItemPayload := b.GetCleverTapCommonPayload(ctx)
		cancelBookingUserItemPayload["booking_id"] = booking.BookingId
		tapEname := &sushi.EnameData{
			Ename: "cancel_booking_member_checkbox_tap",
		}

		cancelBookingEvents := sushi.NewClevertapEvents()
		cancelBookingEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, cancelBookingUserItemPayload, cancelBookingEvents)
		userItem.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
		if featuresupport.SupportsBringAGuest(ctx) {
			if booking.BookingCategory == GUEST_BOOKING_TYPE {
				if userID == loggedInUserId {
					userList = append(userList, userItem)
				} else {
					guestList = append(guestList, userItem)
				}
			} else {
				userList = append(userList, userItem)
			}
		} else {
			userList = append(userList, userItem)
		}
	}

	openCancelSheet := &sushi.OpenCancelBottomSheet{}
	bookingIds := make([]string, 0)
	images := make([]*sushi.Image, 0)

	for _, val := range userList {
		bookingIds = append(bookingIds, val.Id)
		images = append(images, val.Image)
	}

	for _, val := range guestList {
		bookingIds = append(bookingIds, val.Id)
		images = append(images, val.Image)
	}

	if len(userList) > 1 || (featuresupport.SupportsBringAGuest(ctx) && len(guestList) > 0) {

		multiHeaderTitle, _ := sushi.NewTextSnippet("Cancelling for")
		multiUserHeader := &sushi.CancelUserHeader{
			Title: multiHeaderTitle,
		}

		title, _ := sushi.NewTextSnippet("Cancel All")
		cancelAll := &sushi.CancelledUserItem{
			Id:     strings.Join(bookingIds, ","),
			Images: images,
			Title:  title,
		}

		openCancelSheet = &sushi.OpenCancelBottomSheet{
			Header:     multiUserHeader,
			Footer:     footer,
			ItemConfig: itemConfig,
			UserList:   userList,
			CancelAll:  cancelAll,
		}
		if len(guestList) > 0 {
			guestData, err := getGuestDataForCancellation(ctx, guestList)
			if err != nil {
				log.Println("Error in getGuestDataForCancellation for bookingPRN: ")
			} else {
				openCancelSheet.AddGuestData = guestData
			}
		}
	} else {
		singleHeaderTitle, _ := sushi.NewTextSnippet("Cancel Booking")
		singleUserHeader := &sushi.CancelUserHeader{
			Title: singleHeaderTitle,
		}

		footerLayout := &sushi.LayoutConfig{
			SnippetType: sushi.FooterSnippetType2,
		}
		buttonItem := sushi.FooterSnippetType2ButtonItem{
			Type: sushi.FooterButtonTypeSolid,
			Text: "Proceed to cancel the booking",
		}

		proceedCancelBtnPayload := b.GetCleverTapCommonPayload(ctx)
		Ename := &sushi.EnameData{
			Ename: "proceed_cancel_booking_click",
		}

		proceedCancelBookingEvents := sushi.NewClevertapEvents()
		proceedCancelBookingEvents.SetTap(Ename)
		trackItem1 := sushi.GetClevertapTrackItem(ctx, proceedCancelBtnPayload, proceedCancelBookingEvents)
		buttonItem.ClevertapTracking = []*sushi.ClevertapItem{trackItem1}

		buttonItems := []sushi.FooterSnippetType2ButtonItem{buttonItem}
		buttonData := &sushi.FooterSnippetType2Button{
			Items: &buttonItems,
		}
		footerSnippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: buttonData,
		}
		singleUserFooter := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:       footerLayout,
			FooterSnippetType2: footerSnippet,
		}

		out := map[string]interface{}{
			"booking_ids": strings.Join(bookingIds, ","),
		}

		payload, err := json.Marshal(out)
		if err != nil {
			log.Println("error in marshaling the payload data")
			return nil
		}
		sectionHeaderLayout := &sushi.LayoutConfig{
			SnippetType: sushi.SectionHeaderType1,
		}
		title, _ := sushi.NewTextSnippet("Are you sure you want to cancel this session?")
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetColor(color)
		title.SetFont(font)

		sectionHeaderTitle := &sushi.SectionHeaderType1Snippet{
			Title: title,
		}
		item1 := &sushi.V2ImageTextSnippetType36Layout{
			LayoutConfig:       sectionHeaderLayout,
			SectionHeaderType1: sectionHeaderTitle,
		}
		V2ImageTextSnippetType36Layout := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType36,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		image, _ := sushi.NewImage(b.Response.Sport.Icon)
		image.SetType(sushi.ImageTypeRounded)
		image.SetHeight(42)
		image.SetWidth(42)
		image.SetAspectRatio(1)
		border, _ := sushi.NewBorderSnippet()
		border_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
		colorArray := &[]sushi.Color{*border_color}
		border.Colors = colorArray
		border.SetBorderWidth(0.5)
		image.SetBorder(border)
		imageColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		image.SetColor(imageColor)

		V2ImageTextSnippetType36Title, _ := sushi.NewTextSnippet(b.Response.Sport.SportName)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		V2ImageTextSnippetType36Title.SetColor(titleColor)
		titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
		V2ImageTextSnippetType36Title.SetFont(titleFont)

		var booking *bookingPB.Booking
		for _, val := range b.Response.Bookings {
			booking = val
			break
		}
		bookingTimeObj := time.Unix(booking.BookingTime.Seconds, 0)
		bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
		bookingMonthStr := bookingMonth.String()
		timeStr := bookingTimeObj.Format("03:04 PM")

		V2ImageTextSnippetType36Subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d, %s", bookingDay, bookingMonthStr[:3], bookingYear, timeStr))
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		V2ImageTextSnippetType36Subtitle.SetColor(subtitleColor)
		subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		V2ImageTextSnippetType36Subtitle.SetFont(subtitleFont)

		V2ImageTextSnippetType36Item := &sushi.V2ImageTextSnippetType36SnippetItem{
			Image:     image,
			Title:     V2ImageTextSnippetType36Title,
			Subtitle1: V2ImageTextSnippetType36Subtitle,
		}
		ItemArray := &[]sushi.V2ImageTextSnippetType36SnippetItem{*V2ImageTextSnippetType36Item}
		items := &sushi.V2ImageTextSnippetType36Snippet{
			Items: ItemArray,
		}
		item2 := &sushi.V2ImageTextSnippetType36Layout{
			LayoutConfig:             V2ImageTextSnippetType36Layout,
			V2ImageTextSnippetType36: items,
		}
		bottomSheetCancelItems := []*sushi.V2ImageTextSnippetType36Layout{item1, item2}
		openCancelSheet = &sushi.OpenCancelBottomSheet{
			Header:   singleUserHeader,
			Footer:   singleUserFooter,
			PostBody: string(payload),
			Results:  bottomSheetCancelItems,
		}
	}

	cancelTitle, _ := sushi.NewTextSnippet("Cancel this booking")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)

	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	clickAction := sushi.GetClickAction()
	if b.Response.Cancellation == nil || b.Response.Cancellation.IsEnabled == false {
		color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)

		alertTitle, _ := sushi.NewTextSnippet("Cannot cancel!")
		alertTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		alertTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		alertTitle.SetColor(alertTitleColor)
		alertTitle.SetFont(alertTitleFont)

		message, _ := sushi.NewTextSnippet("Session cancellation allowed till 30 mins before session start time")
		messageColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		messageFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		message.SetColor(messageColor)
		message.SetFont(messageFont)

		actionButton, _ := sushi.NewButton(sushi.ButtontypeText)
		actionButton.SetText("Okay")

		customAlert := &sushi.CustomAlert{
			Title:              alertTitle,
			Message:            message,
			PositiveAction:     actionButton,
			DismissAfterAction: true,
		}

		clickAction.SetCustomAlertAction(customAlert)
	} else {
		clickAction.SetOpenCancelBottomSheet(openCancelSheet)
	}

	cancelTitle.SetColor(color)
	cancelTitle.SetFont(font)

	cancelIcon, _ := sushi.NewIcon(sushi.RightIcon, color)
	cancelIcon.SetSize(sushi.IconSize18)

	item := &sushi.V2ImageTextSnippetType10SnippetItem{
		Title:       cancelTitle,
		RightIcon:   cancelIcon,
		ClickAction: clickAction,
	}

	cancelBookingPayload := b.GetCleverTapCommonPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "cancel_booking_click",
	}

	cancelBookingEvents := sushi.NewClevertapEvents()
	cancelBookingEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, cancelBookingPayload, cancelBookingEvents)
	item.AddClevertapTrackingItem(trackItem)

	return item
}

func (b *bookingDetailsTemplate) GetNeedHelpButtonSnippet(ctx context.Context) *sushi.V2ImageTextSnippetType10SnippetItem {
	title, _ := sushi.NewTextSnippet("Need help?")
	color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetColor(color)
	title.SetFont(font)

	icon, _ := sushi.NewIcon(sushi.RightIcon, color)
	icon.SetSize(sushi.IconSize20)

	loggedInUserId := util.GetUserIDFromContext(ctx)
	booking, ok := b.Response.Bookings[loggedInUserId]
	if !ok {
		return nil
	}
	clickAction := sushi.GetClickAction()
	deeplink := &sushi.Deeplink{
		URL: util.GetCustomerSupportDeeplink(booking.BookingId),
	}
	clickAction.SetDeeplink(deeplink)

	item := &sushi.V2ImageTextSnippetType10SnippetItem{
		Title:       title,
		RightIcon:   icon,
		ClickAction: clickAction,
	}
	return item
}

func (b *bookingDetailsTemplate) GetAcademyInviteText(ctx context.Context) string {
	var booking *bookingPB.Booking
	for _, val := range b.Response.Bookings {
		booking = val
		break
	}

	bookingDate := time.Unix(booking.BookingTime.Seconds, 0).Format("02/01/06")
	inviteText := fmt.Sprintf("Let’s learn together at cult!\n\n")
	inviteText += fmt.Sprintf("Sport - %s\n", b.Response.Sport.SportName)
	inviteText += fmt.Sprintf("Centre - %s, %s\n", b.Response.Facility.DisplayName, b.Response.Facility.ShortAddress)
	inviteText += fmt.Sprintf("Time Slot - %s, %s\n\n", bookingDate, b.Response.SlotTiming)
	inviteText += fmt.Sprintf("Let’s get our assessment and grow together! Click here to book your free trial at center near you")
	return inviteText
}

func (b *bookingDetailsTemplate) GetInviteText(ctx context.Context) string {
	facilityName := b.Response.Facility.DisplayName
	facilityAddress := b.Response.Facility.ShortAddress
	sportName := b.Response.Sport.SportName

	var booking *bookingPB.Booking
	for _, val := range b.Response.Bookings {
		booking = val
		break
	}

	slotTimingForBooking := b.Response.SlotTiming

	bookingTimeObj := time.Unix(booking.BookingTime.Seconds, 0)

	currentDay := time.Now().Day()
	_, bookingMonth, bookingDay := bookingTimeObj.Date()
	bookingMonthStr := bookingMonth.String()

	var timeStr string
	if bookingDay == currentDay {
		timeStr = fmt.Sprintf("at %s today", slotTimingForBooking)
	} else {
		timeStr = fmt.Sprintf("on %d %s at %s", bookingDay, bookingMonthStr, slotTimingForBooking)
	}

	inviteText := fmt.Sprintf("It’s Game on at cult!\n\n")
	if isProductCategorySummerCamp(b.Response.ProductCategoryId) {
		inviteText = fmt.Sprintf("Did you hear about the fun filled Kids Summer Camp at cult Academy?\n\n")
	}

	if b.Response.Sport.IsSessionBased {
		inviteText += fmt.Sprintf("Join me for a session of %s at %s, %s %s.\n", sportName, facilityName, facilityAddress, timeStr)
	} else {
		inviteText += fmt.Sprintf("Join me for a game of %s at %s, %s %s.\n", sportName, facilityName, facilityAddress, timeStr)
	}

	if b.Response.AvailableSportsCount < 5 {
		inviteText += fmt.Sprintf("Get ready for a showdown! Click here to book your free trial and get access to multiple centers near you")
	} else {
		inviteText += fmt.Sprintf("Get ready for a showdown! Click here to book your free trial and get access to 5+ Sports at multiple centers near you")
	}

	return inviteText
}

func (b *bookingDetailsTemplate) checkInfoContainerShow(ctx context.Context) bool {
	if b.checkGoldenFinnSwim(ctx) || (b.SummerCampAlert(ctx) && isProductCategorySummerCamp(b.Response.ProductCategoryId)) {
		return true
	}
	hasOpenCourt := b.Response.HasOpenCourt
	if hasOpenCourt != int32(1) {
		return false
	}
	var booking *bookingPB.Booking
	for _, val := range b.Response.Bookings {
		booking = val
		break
	}
	bookingTimeObj := time.Unix(booking.BookingTime.Seconds+3600, 0)
	showTimeObj := util.GetMonsoonAlertLastDate()
	if bookingTimeObj.After(showTimeObj) {
		return false
	}
	if bookingTimeObj.Before(time.Now()) {
		return false
	}
	if showTimeObj.Before(time.Now()) {
		return false
	}
	return true
}

func (b *bookingDetailsTemplate) checkGoldenFinnSwim(ctx context.Context) bool {
	if b.Response.Facility.FacilityId == 108 && b.Response.Sport.SportId == 3 {
		return true
	}
	return false
}

func (b *bookingDetailsTemplate) SummerCampAlert(ctx context.Context) bool {
	if b.Response.Sport.SportId == 8 || b.Response.Sport.SportId == 42 || b.Response.Sport.SportId == 43 {
		return true
	}
	return false
}

func (b *bookingDetailsTemplate) SetHasUpcommingBooking(ctx context.Context) {

	hasUpcoming := false
	for _, val := range b.Response.UserDetails {
		if val.BookingStatus == "upcoming" {
			hasUpcoming = true
			break
		}
	}
	b.HasUpcommingBooking = hasUpcoming
}

func getGuestDataForCancellation(ctx context.Context, guestUserList []*sushi.CancelledUserItem) (*sushi.GuestData, error) {
	guestData := &sushi.GuestData{}

	var guests []sushi.FitsoImageTextSnippetType23SnippetItem
	for _, user := range guestUserList {

		id, err := strconv.ParseInt(user.Id, 10, 32)
		if err != nil {
			log.Println("error in converting to int from string in getGuestDataForCancellation: ", user.Id, err)
			return guestData, err
		}
		b := sushi.FitsoImageTextSnippetType23SnippetItem{
			ID:    int32(id),
			Image: user.Image,
			Title: user.Title,
		}

		guests = append(guests, b)
	}
	if len(guests) > 0 {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoImageTextSnippetType23,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		title, _ := sushi.NewTextSnippet("Your guests")
		titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title.SetFont(titleFont)
		title.SetColor(titleColor)

		guestSnippet := &sushi.FitsoImageTextSnippetType23Snippet{
			CornerRadius: 12,
			Title:        title,
			BgColor:      bgColor,
			BorderColor:  borderColor,
			Items:        &guests,
		}
		guestLayout := &sushi.FitsoImageTextSnippetType23Layout{
			LayoutConfig:                       layoutConfig,
			FitsoImageTextSnippetType23Snippet: guestSnippet,
		}

		errorData, _ := sushi.NewTextSnippet("Selecting the host will cancel bookings for guest as well")
		errorColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint600)
		errorFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		errorData.SetFont(errorFont)
		errorData.SetColor(errorColor)

		guestData.GuestData = guestLayout
		guestData.ErrorData = errorData
		return guestData, nil
	}

	return guestData, nil
}
