package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	centreModel "bitbucket.org/jogocoin/go_api/api/models/booking/centres"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/render"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type SummerCampCentersPageData struct {
	SubscriptionDetails *productPB.GetUserSubscriptionStatusResponse
	TrialDetails        *userPB.TrialDetailsResponse
	IsPremiumUser       bool
	IsTrialUser         bool
	ContainsGuestUsers  bool
}

func GetSummerCampCentersForBookingC(c *gin.Context) { 
	cl := util.GetFacilitySportClient()

	var json structs.GetCentresForSlotBookingReq
	ctx := util.PrepareGRPCMetaData(c)

	if util.GetAppTypeFromContext(ctx) == featuresupport.Android {
		jsonV2 := c.MustGet("jsonData").(structs.GetCentresForSlotBookingReqV2)
		if len(jsonV2.BookingUsers) == 0 {
			c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("Empty user list"))
			return
		}
		var bookingUsers []structs.BookingUser
		for _, itm := range jsonV2.BookingUsers {
			obj := structs.BookingUser{
				UserID: int32(itm.UserID),
			}
			bookingUsers = append(bookingUsers, obj)
		}

		json = structs.GetCentresForSlotBookingReq{
			SportId: jsonV2.SportId,
			BookingUsers:	bookingUsers,
		}
	} else {
		json = c.MustGet("jsonData").(structs.GetCentresForSlotBookingReq)
	}
	if len(json.BookingUsers) == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("Empty user list"))
		return
	}

	reqData := &facilitySportPB.SummerCampFacilitiesReq{
		SportId: json.SportId,
	}

	if json.SportId == 0 {
		reqData.AllSports = true
	}

	centreForBooking, err := cl.GetSummerCampFacilitiesForSport(ctx, reqData)
	if err != nil {
		log.Println("func:GetSummerCampCentersForBookingC, could not fetch booking centers details ---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if centreForBooking.Status != nil {
		if centreForBooking.Status.Status == common.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, model.StatusFailure(centreForBooking.Status.Message))
			return
		}
		if centreForBooking.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, model.StatusFailure(centreForBooking.Status.Message))
			return
		}
	}

	template := AvailableCentreTemplate{}
	template.PageData.SportId = json.SportId
	//template.SetChildUserSummerCampSubscriptionStatus(ctx)
	if json.BottomSheet != 1 {
		template.SetSummerCampHeaderSection(ctx, &json)
		template.SetProgressBarSection(ctx)
	}
	template.ResultSectionSummerCamp(ctx, &json, centreForBooking)
	template.SetSummerCampFooterSection(&json, centreForBooking)
	template.SetSummerCampPageTracking(ctx, json.Source, centreForBooking)

	render.Render(c, gin.H{"payload": template}, "index.html")
}
/*
func (s *AvailableCentreTemplate) SetChildUserSummerCampSubscriptionStatus(ctx context.Context) {
	if s.PageData.ChildSummerCampStatus != nil {
		return
	}
	userClient := util.GetUserServiceClient()
	status, err := userClient.CheckIfChildHasActiveSummerCampSubscription(ctx, &userPB.Empty{})
	if err != nil {
		log.Printf("Api: get centers for summer camp, function: CheckIfChildHasActiveSummerCampSubscription, Error: %v", err)
		return
	}
	s.PageData.ChildSummerCampStatus = status
}*/

func (s *AvailableCentreTemplate) SetSummerCampHeaderSection(ctx context.Context, req *structs.GetCentresForSlotBookingReq) {

	/*bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &centreModel.Header{
		BgColor: bgColor,
	}

	title, _ := sushi.NewTextSnippet("Book free trial")
	title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetFont(title_font)
	title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(title_color)

	header.Title = title
	s.Header = header*/

	title, _ := sushi.NewTextSnippet("Book free trial")
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(title_font)

	title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetColor(title_color)
	trialText := "1 free trial available for each sport"

	tag, _ := sushi.NewTag(trialText)
	tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
	tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
	if featuresupport.SupportsNewColor(ctx) {
		tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
		tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
	}

	tag.Title.SetFont(tagFont)
	tag.Title.SetColor(tagColor)
	tag.SetBgColor(tagBgColor)
	tag.SetBorderColor(tagBorderColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &centreModel.Header{
		Title:   title,
		Tag:     tag,
		BgColor: bgColor,
	}
	s.Header = header
}

func (s *AvailableCentreTemplate) SetSummerCampFooterSection(req *structs.GetCentresForSlotBookingReq, response *facilitySportPB.SummerCampFacilitiesRes) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	var buttonWeight []int32

	var buttonItem2Text string
	buttonItem2Text = "Select a center to proceed"
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItem2BgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Type:             "solid",
		Text:             buttonItem2Text,
		Font:             buttonItem2Font,
		Id:               "bottom_button_id2",
		BgColor:          buttonItem2BgColor,
		IsActionDisabled: 1,
	}
	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData:   buttonData,
		ButtonWeight: buttonWeight,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func (s *AvailableCentreTemplate) ResultSectionSummerCamp(ctx context.Context, req *structs.GetCentresForSlotBookingReq, response *facilitySportPB.SummerCampFacilitiesRes) {
	var items []sushi.CustomTextSnippetTypeLayout
	loggedInUser := util.GetUserIDFromContext(ctx)
	for i, elem := range response.Centers {
		// setting facility title
		facility_title, _ := sushi.NewTextSnippet(elem.DisplayName)
		facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		facility_title.SetFont(facility_title_font)
		facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_title.SetColor(facility_title_color)

		facility_subtitle, _ := sushi.NewTextSnippet(elem.ShortAddress)
		facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		facility_subtitle.SetFont(facility_subtitle_font)
		facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		facility_subtitle.SetColor(facility_subtitle_color)

		// setting facility tag
		facility_distance_tag_title := "-"
		if elem.Distance != 0 {
			facility_distance_tag_title = fmt.Sprintf("%.1f km", elem.Distance)
		}
		facility_tag_title, _ := sushi.NewTextSnippet(facility_distance_tag_title)
		facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		facility_tag_title.SetFont(facility_tag_title_font)
		facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		facility_tag_title.SetColor(facility_tag_title_color)

		// setting facility tag bg color
		facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag := &sushi.Tag{
			Title:   facility_tag_title,
			BgColor: facility_tag_bgcolor,
		}

		// setting facility rating title
		facility_rating_title, _ := sushi.NewTextSnippet(elem.Rating)
		facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		facility_rating_title.SetFont(facility_rating_title_font)
		facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
		facility_rating_title.SetColor(facility_rating_title_color)
		facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
		facility_rating_title.SetPrefixIcon(ratingIcon)

		// setting facility rating bg color
		facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)

		ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

		tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, elem.Tag) {
			ratingTitle, _ := sushi.NewTextSnippet(elem.Tag)
			ratingSnippetBlockItem.Title = ratingTitle
			if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				ratingSnippetBlockItem.BgColor = ratingBgColor
			} else {
				// gradient for new tag
				tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
				gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
				ratingSnippetBlockItem.Gradient = gradient
			}

		} else {
			ratingSnippetBlockItem.Title = facility_rating_title
			ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
		}
		facility_image := &sushi.Image{
			URL:         elem.DisplayPicture,
			AspectRatio: 1,
			Type:        sushi.ImageTypeRounded,
			Height:      64,
			Width:       64,
		}

		var button_text string
		var tapEname *sushi.EnameData

		footerButtonPayload := make(map[string]interface{})
		footerButtonPayload["fs_id"] = elem.FsId
		footerButtonPayload["sport_id"] = req.SportId
		footerButtonPayload["facility_id"] = elem.FacilityId
		footerButtonPayload["source"] = req.Source
		footerButtonPayload["user_id"] = util.GetUserIDFromContext(ctx)

		bottom_button_click_action := sushi.GetClickAction()

		var bookingUsers []structs.BookingUser
		for _, itm := range req.BookingUsers {
			obj := structs.BookingUser{
				UserID: itm.UserID,
			}
			bookingUsers = append(bookingUsers, obj)
		}

		button_text = "Proceed to select Slot"
		params := map[string]interface{}{
			"fs_id":               elem.FsId,
			"sport_id":            req.SportId,
			"product_category_id": common.SummerCampCategoryID,
			"user_id":             loggedInUser,
			"source":              req.Source,
			"booking_users":	   req.BookingUsers,
		}
		payload, err := json.Marshal(params)
		if err != nil {
			log.Printf("json marshalling error: %v", err)
		}
		deeplink := sushi.Deeplink{
			URL:            util.GetSlotsDeeplink() + "?is_bottom_sheet=0",
			PostbackParams: string(payload),
		}
		/*if s.PageData.ChildSummerCampStatus != nil && s.PageData.ChildSummerCampStatus.HasActiveChildSummerCampSubscription {
			deeplink.URL = util.GetBuddiesSelectionDeeplink() + "?is_bottom_sheet=0"
		}*/
		bottom_button_click_action.SetDeeplink(&deeplink)
		//change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
		tapEname = &sushi.EnameData{
			Ename: "select_center",
		}

		button := &sushi.Button{
			Type:        "solid",
			Text:        button_text,
			ClickAction: bottom_button_click_action,
		}

		if tapEname != nil {
			footerButtonEvents := sushi.NewClevertapEvents()
			footerButtonEvents.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, footerButtonPayload, footerButtonEvents)
			button.AddClevertapTrackingItem(trackItem)
		}

		var change_bottom_button *sushi.ChangeBottomButton
		change_bottom_button = &sushi.ChangeBottomButton{
			ButtonId: "bottom_button_id2",
			Button:   button,
		}

		facility_click_action, _ := sushi.NewTextClickAction("change_bottom_button")
		facility_click_action.ChangeBottomButton = change_bottom_button

		facility_obj := sushi.V2ImageTextSnippetType31SnippetItem{
			Title:       facility_title,
			Subtitle:    facility_subtitle,
			Tag:         tag,
			Rating:      ratingSnippetBlockItem,
			Image:       facility_image,
			ClickAction: facility_click_action,
		}

		if elem.Sport != nil && len(elem.Sport) > 0 {
			var sportNames []string
			for _, sportDetails := range elem.Sport {
				if sportDetails.SportId == s.PageData.SportId {
					sportNames = append(sportNames, fmt.Sprintf("<semibold-200|%s>", sportDetails.SportName))
				} else {
					sportNames = append(sportNames, sportDetails.SportName)
				}
			}
			if len(sportNames) > 0 {
				facility_subtitle2, _ := sushi.NewTextSnippet(strings.Join(sportNames, ","))
				facility_subtitle2_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
				facility_subtitle2.SetFont(facility_subtitle2_font)
				facility_subtitle2.SetColor(facility_title_color)
				facility_subtitle2.IsMarkdown = 1
				facility_obj.Subtitle2 = facility_subtitle2
			}
		}

		facility_obj.ClickAction = facility_click_action

		title, _ := sushi.NewTextSnippet("Select your center")
		snippet := &sushi.V2ImageTextSnippetType31Snippet{
			Items: &[]sushi.V2ImageTextSnippetType31SnippetItem{facility_obj},
			Id:    "lockdown",
		}

		if i == 0 {
			snippet.Title = title
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType31,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType31: snippet,
		}

		items = append(items, *item)
	}
	s.Results = &items
}

func (s *AvailableCentreTemplate) SetSummerCampPageTracking(ctx context.Context, source string, response *facilitySportPB.SummerCampFacilitiesRes) {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	if response.Centers != nil && len(response.Centers) > 0 {
		payload["nearest_distance"] = response.Centers[0].Distance
		payload["total_centers"] = len(response.Centers)
	}
	landingEname := &sushi.EnameData{
		Ename: "summer_camp_center_selection_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}
