package bookingController

import (
	"context"
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	bookingModel "bitbucket.org/jogocoin/go_api/api/models/booking"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// SaveFeedbackRequestTemplate represents the response structure
type SaveFeedbackRequestTemplate struct {
	Status     string                             `json:"status,omitempty"`
	ActionList []*bookingModel.SaveFeedbackAction `json:"action_list,omitempty"`
}

func SaveBookingFeedbackV2C(c *gin.Context) {

	bookingClient := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.SaveBookingFeedback
	json = c.MustGet("jsonData").(structs.SaveBookingFeedback)

	template := SaveFeedbackRequestTemplate{}

	feedbackData := &bookingPB.SaveBookingFeedbackRequest{
		RatingId:               json.RatingID,
		BookingReferenceNumber: json.BookingReferenceNumber,
		BookingId:              json.BookingId,
		Note:                   json.Note,
		CategoryOptions:        json.CategoryOptions,
	}
	response, err := bookingClient.SaveBookingFeedbackV2(ctx, feedbackData)
	if err != nil {
		log.Println("Error in inserting booking feedback", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		message := response.Status.Message
		c.JSON(http.StatusUnauthorized, template.failedResponse(ctx, message, response))
		return
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		message := response.Status.Message
		c.JSON(http.StatusBadRequest, template.failedResponse(ctx, message, response))
		return
	}
	c.JSON(http.StatusOK, template.successResponse(ctx, response))
	return
}

func SaveFeedbackTemplate() *SaveFeedbackRequestTemplate {
	return &SaveFeedbackRequestTemplate{}
}

func (s *SaveFeedbackRequestTemplate) setTitle(text string) *sushi.TextSnippet {
	title, _ := sushi.NewTextSnippet(text)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	title.SetColor(color)
	title.SetFont(font)
	return title
}

func (s *SaveFeedbackRequestTemplate) setMessage(text string) *sushi.TextSnippet {
	message, _ := sushi.NewTextSnippet(text)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	message.SetFont(font)
	message.SetColor(color)
	return message
}

func (s *SaveFeedbackRequestTemplate) setImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (s *SaveFeedbackRequestTemplate) setStatus(status string) {
	s.Status = status
}

func (s *SaveFeedbackRequestTemplate) failedResponse(ctx context.Context, errorMessage string, res *bookingPB.SaveBookingFeedbackResponse) *SaveFeedbackRequestTemplate {
	response := SaveFeedbackTemplate()
	response.setStatus(common.FAILURE)

	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Feedback cannot be submitted!"),
		Message: s.setMessage(errorMessage),
		Image:   s.setImage("https://fitso-images.curefit.co/file_assets/failure_image.json"),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		DismissAfterAction: true,
	}

	feedbackBookingPayload := s.GetCleverTapCommonPayload(res.CleverTapPayload)
	feedbackBookingPayload["feedback_status"] = "failed"
	impressionEname := &sushi.EnameData{
		Ename: "feedback_submission_status",
	}

	feedbackBookingEvents := sushi.NewClevertapEvents()
	feedbackBookingEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, feedbackBookingPayload, feedbackBookingEvents)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}

	feedbackDismissAction := &bookingModel.SaveFeedbackAction{
		Type:            sushi.ActionTypeFeedbackDismiss,
		FeedbackDismiss: action,
	}
	response.ActionList = append(response.ActionList, feedbackDismissAction)
	return response
}

func (s *SaveFeedbackRequestTemplate) successResponse(ctx context.Context, res *bookingPB.SaveBookingFeedbackResponse) *SaveFeedbackRequestTemplate {
	response := SaveFeedbackTemplate()
	response.setStatus(common.SUCCESS)

	customAlert := &sushi.CustomAlert{
		Title:   s.setTitle("Feedback Submitted!"),
		Message: s.setMessage("Thanks for helping us improve."),
		Image:   s.setImage("https://fitso-images.curefit.co/file_assets/success_image.json"),
		AutoDismissData: &sushi.AutoDismissData{
			Time:              3,
			DismissActionType: "positive",
		},
		DismissAfterAction: true,
	}

	feedbackBookingPayload := s.GetCleverTapCommonPayload(res.CleverTapPayload)
	feedbackBookingPayload["feedback_status"] = "success"
	impressionEname := &sushi.EnameData{
		Ename: "feedback_submission_status",
	}

	feedbackBookingEvents := sushi.NewClevertapEvents()
	feedbackBookingEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, feedbackBookingPayload, feedbackBookingEvents)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	feedbackDismissAction := &bookingModel.SaveFeedbackAction{
		Type:            sushi.ActionTypeFeedbackDismiss,
		FeedbackDismiss: action,
	}
	response.ActionList = append(response.ActionList, feedbackDismissAction)
	return response
}

func (s *SaveFeedbackRequestTemplate) GetCleverTapCommonPayload(payload *bookingPB.CleverTapPayload) map[string]interface{} {

	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = payload.CreatedBy
	commonPayload["rating_id"] = payload.RatingId
	commonPayload["note"] = payload.Note
	commonPayload["booking_reference_number"] = payload.BookingReferenceNumber
	commonPayload["source"] = "feedback"
	return commonPayload
}
