package bookingController

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"context"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	slotModel "bitbucket.org/jogocoin/go_api/api/models/booking/slots"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// SlotResponse represents the response structure of slots page
type SummerCampSlotPageTemplate struct {
	Header            *slotModel.Header                       `json:"header,omitempty"`
	ProgressBarData   *slotModel.ProgressBarStruct            `json:"progress_bar_data,omitempty"`
	CustomHeader      *slotModel.CustomHeader                 `json:"custom_header,omitempty"`
	SelectionTitle    *sushi.TextSnippet                      `json:"selection_title"`
	Footer            *sushi.FooterSnippetType2Layout         `json:"footer"`
	DetailsContainer  *slotModel.DetailsContainer             `json:"details_container"`
	InfoContainer     *slotModel.InfoContainer                `json:"info_container"`
	SupportContainer  *sushi.FitsoImageTextSnippetType7Layout `json:"support_container,omitempty"`
	Config            *slotModel.SlotConfig                   `json:"config"`
	Items             *[]slotModel.SlotItem                   `json:"items"`
	SessionsInfo      *[]slotModel.SessionsInfo               `json:"sessions_info,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                  `json:"clever_tap_tracking,omitempty"`
}

func GetSummerCampSlotsC(c *gin.Context) { 
	cl = util.GetBookingClient()

	var json structs.BookingSlotsV3
	json = c.MustGet("jsonData").(structs.BookingSlotsV3)
	ctx := util.PrepareGRPCMetaData(c)
	loggedInUser := util.GetUserIDFromContext(ctx)
	if len(json.BookingUsers) == 0 && loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("Empty user list"))
		return
	}
	if len(json.BookingUsers) == 0 {
		json.BookingUsers = append(json.BookingUsers, structs.BookingUser{
			UserID: loggedInUser,
		})
	}

	reqData := &bookingPB.BookingSlotsV3Req{
		FsId:         json.FsId,
		SportId:      json.SportId,
		BookingCount: int32(len(json.BookingUsers)),
	}
	for _, bookingUser := range json.BookingUsers {
		reqData.BookingUserIds = append(reqData.BookingUserIds, bookingUser.UserID)
	}

	bookingSlotResponse, err := cl.GetSummerCampSlots(ctx, reqData) 
	if err != nil {
		log.Println("Could not fetch summer camp slots details ---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if bookingSlotResponse.Status != nil {
		if bookingSlotResponse.Status.Status == common.FAILED {
			c.JSON(http.StatusOK, model.StatusFailure(bookingSlotResponse.Status.Message))
			return
		}
		if bookingSlotResponse.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, model.StatusFailure(bookingSlotResponse.Status.Message))
			return
		}
	}

	template := SummerCampSlotPageTemplate{}
	if json.BottomSheet != 1 {
		template.SetHeaderSection(ctx, bookingSlotResponse)
		template.SetProgressBarSection(ctx)
		template.SetSupportContainerSection(ctx)
	} else {
		template.SetCustomHeaderSection(ctx, bookingSlotResponse)
	}
	template.SetSelectionTitleSection(&json, bookingSlotResponse)
	template.SetFooterSection(&json, bookingSlotResponse)

	template.SetConfigSection()
	template.SetSlotItemSection(ctx, &json, bookingSlotResponse)
	template.GetSessionInfoSnippet(ctx)
	template.SetPageTracking(ctx, bookingSlotResponse)

	c.JSON(http.StatusOK, template)
	return
}

func (s *SummerCampSlotPageTemplate) SetProgressBarSection(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	colorType := sushi.ColorTypeTeal
	if featuresupport.SupportsNewColor(ctx) {
		colorType = sushi.ColorTypeCyan
	}
	progressbarColor := &[]sushi.Color{
		sushi.Color{
			Type: colorType,
			Tint: sushi.ColorTint500,
		},
	}
	progressBarDetails := &slotModel.ProgressBarStruct{
		Progress:       100,
		BgColor:        bgColor,
		ProgressColors: progressbarColor,
	}
	s.ProgressBarData = progressBarDetails
}


func (s *SummerCampSlotPageTemplate) SetHeaderSection(ctx context.Context, response *bookingPB.BookingSlotsV3Res) {
	title, _ := sushi.NewTextSnippet(response.SportName)
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(title_font)

	title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetColor(title_color)
	trialText := response.FacilityName
	tag, _ := sushi.NewTag(trialText)
	tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
	tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
	if featuresupport.SupportsNewColor(ctx) {
		tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
		tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
	}

	tag.Title.SetFont(tagFont)
	tag.Title.SetColor(tagColor)
	tag.SetBgColor(tagBgColor)
	tag.SetBorderColor(tagBorderColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &slotModel.Header{
		Title:   title,
		Tag:     tag,
		BgColor: bgColor,
	}
	s.Header = header
}

func (s *SummerCampSlotPageTemplate) SetCustomHeaderSection(ctx context.Context, response *bookingPB.BookingSlotsV3Res) {
	title, _ := sushi.NewTextSnippet(response.FacilityName)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	subtitle, _ := sushi.NewTextSnippet(response.SubzoneName)
	subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle.SetFont(subtitleFont)
	subtitle.SetColor(subtitleColor)

	bgColorTint := sushi.ColorTint100
	if featuresupport.SupportsNewColor(ctx) {
		bgColorTint = sushi.ColorTint50
	}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)
	customHeaderType1Obj := &slotModel.CustomHeaderType1{
		Title:    title,
		SubTitle: subtitle,
		BgColor:  bgColor,
	}

	customHeaderDetails := &slotModel.CustomHeader{
		Type:              slotModel.CustomHeaderType1Val,
		CustomHeaderType1: customHeaderType1Obj,
	}
	s.CustomHeader = customHeaderDetails
}

func (s *SummerCampSlotPageTemplate) SetSelectionTitleSection(req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	var selection_title_text string

	if req.BottomSheet != 1 {
		selection_title_text = "Select your slot"
	} else {
		selection_title_text = "Booking slot for " + response.SportName
	}
	title, _ := sushi.NewTextSnippet(selection_title_text)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	s.SelectionTitle = title
}

func (s *SummerCampSlotPageTemplate) SetFooterSection(req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItem2Text string
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Font: buttonItem2Font,
		Id:   "bottom_button_id2",
	}
	buttonItem2Text = "Select a slot to book"
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem2.Text = buttonItem2Text
	buttonItem2.BgColor = color
	buttonItem2.IsActionDisabled = 1

	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}

	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func (s *SummerCampSlotPageTemplate) SetSupportContainerSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Contact <EMAIL>.")

	rightButton, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)

	rightButton.SetText("Call")
	rightButton.SetPrefixIcon(callIcon)
	rightButton.SetClickAction(clickAction)

	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["source"] = "summer_camp_slots"
	tapEname := &sushi.EnameData{
		Ename: "summer_camp_call_click",
	}

	callEvents := sushi.NewClevertapEvents()
	callEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, callEvents)
	rightButton.AddClevertapTrackingItem(trackItem)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{
		BgColor:     bgColor,
		Title:       title,
		//RightButton: rightButton,
		BorderColor: borderColor,
	}
	rightButton1, _ := sushi.NewButton(sushi.ButtontypeText)
	rightButton1.SetText(" ")
	item.RightButton = rightButton1

	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}

	supportContainerData := &sushi.FitsoImageTextSnippetType7Layout{
		LayoutConfig:        layoutConfig,
		FitsoImageTextType7: snippet,
	}

	s.SupportContainer = supportContainerData
}

func (s *SummerCampSlotPageTemplate) SetConfigSection() {
	selected_title_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	selected_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	unselected_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	unselected_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)

	headerConfigDetails := &slotModel.HeaderConfig{
		SelectedTitleColor:      selected_title_color,
		SelectedSubtitleColor:   selected_subtitle_color,
		UnselectedTitleColor:    unselected_title_color,
		UnselectedSubtitleColor: unselected_subtitle_color,
	}

	selected_item_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	unselected_item_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	enabled_item_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	disabled_item_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	enabled_item_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabled_item_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)

	itemConfigDetails := &slotModel.ItemConfig{
		SelectedItemColor:     selected_item_color,
		UnselectedItemColor:   unselected_item_color,
		EnabledItemColor:      enabled_item_color,
		DisabledItemColor:     disabled_item_color,
		EnabledItemTitleColor: enabled_item_title_color,
		DisabledItemTitleFont: disabled_item_title_font,
	}

	configDetails := &slotModel.SlotConfig{
		HeaderConfig: headerConfigDetails,
		ItemConfig:   itemConfigDetails,
	}
	s.Config = configDetails
}

func (s *SummerCampSlotPageTemplate) SetSlotItemSection(ctx context.Context, req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	var slotItems []slotModel.SlotItem

	var closed_message string
	if len(response.BookingSlots) > 0 {
		for _, item := range response.BookingSlots {
			var title_text string
			var dayInterval int32
			if item.IsToday == true {
				title_text = "Today"
				dayInterval = 0
			} else if item.IsTomorrow == true {
				title_text = "Tomorrow"
				dayInterval = 1
			} else {
				title_text = item.WeekDay
				dayInterval = 2
			}
			title, _ := sushi.NewTextSnippet(title_text)
			subtitle, _ := sushi.NewTextSnippet(item.Date)
			if !item.IsClosed {
				var morningSlots []slotModel.SlotStruct
				var eveningSlots []slotModel.SlotStruct
				s.GetSlotContent(ctx, req, item.MorningSlots, &morningSlots, response, dayInterval)
				s.GetSlotContent(ctx, req, item.EveningSlots, &eveningSlots, response, dayInterval)

				var slotSections []slotModel.SlotSection
				if len(morningSlots) > 0 {
					morning_title, _ := sushi.NewTextSnippet("MORNING")
					morning_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					morning_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					morning_title.SetFont(morning_title_font)
					morning_title.SetColor(morning_title_color)
					morning_title.SetKerning(3)
					slotText := "(" + strconv.Itoa(len(morningSlots)) + " slots)"
					if len(morningSlots) == 1 {
						slotText = "(" + strconv.Itoa(len(morningSlots)) + " slot)"
					}

					morning_subtitle, _ := sushi.NewTextSnippet(slotText)
					morning_subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					morning_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					morning_subtitle.SetFont(morning_subtitle_font)
					morning_subtitle.SetColor(morning_subtitle_color)

					morning_slot_section_header := &slotModel.SlotSectionHeader{
						Title:    morning_title,
						SubTitle: morning_subtitle,
					}
					morning_slot_section := &slotModel.SlotSection{
						SectionHeader: morning_slot_section_header,
						Slots:         morningSlots,
					}
					slotSections = append(slotSections, *morning_slot_section)
				}

				if len(eveningSlots) > 0 {
					evening_title, _ := sushi.NewTextSnippet("EVENING")
					evening_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					evening_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					evening_title.SetFont(evening_title_font)
					evening_title.SetColor(evening_title_color)
					evening_title.SetKerning(3)
					slotText := "(" + strconv.Itoa(len(eveningSlots)) + " slots)"
					if len(eveningSlots) == 1 {
						slotText = "(" + strconv.Itoa(len(eveningSlots)) + " slot)"
					}

					evening_subtitle, _ := sushi.NewTextSnippet(slotText)
					evening_subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					evening_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					evening_subtitle.SetFont(evening_subtitle_font)
					evening_subtitle.SetColor(evening_subtitle_color)

					evening_slot_section_header := &slotModel.SlotSectionHeader{
						Title:    evening_title,
						SubTitle: evening_subtitle,
					}
					evening_slot_section := &slotModel.SlotSection{
						SectionHeader: evening_slot_section_header,
						Slots:         eveningSlots,
					}
					slotSections = append(slotSections, *evening_slot_section)
				}

				tapPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
				tapPayload["day"] = dayInterval
				tapEname := &sushi.EnameData{
					Ename: "summer_camp_slots_page_day_tab_click",
				}
				tabEvents := sushi.NewClevertapEvents()
				tabEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tabEvents)

				obj := &slotModel.SlotItem{
					Title:             title,
					SubTitle:          subtitle,
					Sections:          slotSections,
					ClevertapTracking: []*sushi.ClevertapItem{trackItem},
				}
				slotItems = append(slotItems, *obj)
			} else {
				closed_image_url := util.GetCDNLink("uploads/Closed1619501106.jpg")
				closed_image, _ := sushi.NewImage(closed_image_url)
				closed_image.SetAspectRatio(1.0)
				closed_message = "Closed due to maintenance"
				closed_title, _ := sushi.NewTextSnippet(closed_message)
				error := &slotModel.SlotErrorStruct{
					Title: closed_title,
					Image: closed_image,
				}
				obj := &slotModel.SlotItem{
					Title:    title,
					SubTitle: subtitle,
					Error:    error,
				}
				slotItems = append(slotItems, *obj)
			}
		}
		s.Items = &slotItems
	} else {
		closed_image_url := util.GetCDNLink("uploads/Closed1619501106.jpg")
		closed_image, _ := sushi.NewImage(closed_image_url)
		closed_image.SetAspectRatio(1.0)
		closed_message = "Closed due to maintenance"
		closed_title, _ := sushi.NewTextSnippet(closed_message)
		error := &slotModel.SlotErrorStruct{
			Title: closed_title,
			Image: closed_image,
		}

		title, _ := sushi.NewTextSnippet("Today")
		t := time.Now()
		str_date := t.Format(time.RFC1123)
		words := strings.Fields(str_date)
		date := words[0] + " " + words[1] + " " + words[2]
		subtitle, _ := sushi.NewTextSnippet(date)
		obj := &slotModel.SlotItem{
			Title:    title,
			SubTitle: subtitle,
			Error:    error,
		}
		slotItems = append(slotItems, *obj)
		s.Items = &slotItems
	}
}

func (s *SummerCampSlotPageTemplate) GetSlotContent(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	slotsData []*bookingPB.BookingSlot,
	slotsTemplateData *[]slotModel.SlotStruct,
	response *bookingPB.BookingSlotsV3Res,
	dayInterval int32,
) error {
	var slots_template_data []slotModel.SlotStruct
	if len(slotsData) == 0 {
		*slotsTemplateData = slots_template_data
		return nil
	}
	for _, itm := range slotsData {
		slot_title := itm.Timing
		if !itm.BookingAllowed && itm.IsSoldOut {
			if len(itm.AvailablityText) > 0 {
				slot_title += " " + itm.AvailablityText
			}
		}

		title, _ := sushi.NewTextSnippet(slot_title)
		slot_obj := &slotModel.SlotStruct{
			Title:  title,
			SlotId: itm.SlotId,
		}

		if !itm.BookingAllowed {
			slot_obj.IsDisabled = true

			if itm.IsSoldOut {
				title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
				title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
				slot_obj.Title.SetFont(title_font)
				slot_obj.Title.SetColor(title_color)
			}

			if len(itm.AvailablityText) > 0 && !itm.IsSoldOut {
				subtitle, _ := sushi.NewTextSnippet(itm.AvailablityText)
				subtitle_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
				subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				subtitle.SetFont(subtitle_font)
				subtitle.SetColor(subtitle_color)

				slot_obj.SubTitle = subtitle
			}
		} else if !itm.ShowSummerCampPopUp {

			out := map[string]interface{}{
				"fs_id":                     response.FsId,
				"slot_id":                   itm.SlotId,
				"booking_time":              itm.SlotDatetime.Seconds,
				"booking_users":             req.BookingUsers,
				"product_arena_category_id": itm.SlotTypes[0].ProductArenaCategoryId,
				"product_category_id":       common.SummerCampCategoryID,
			}

			payload, err := json.Marshal(out)
			if err != nil {
				log.Println("error in marshalling the payload data")
			}

			var tapEname *sushi.EnameData
			slotFooterPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
			slotFooterPayload["slot_id"] = itm.SlotId
			slotFooterPayload["day"] = dayInterval

			change_bottom_button_click_action := sushi.GetClickAction()
			api_call_multi_action := &sushi.APICallAction{
				RequestType: sushi.POSTRequestType,
				URL:         "v2/booking/publish",
				Body:        string(payload),
			}

			buttonText := "Book selected summer camp slot"
			change_bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
			change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
			tapEname = &sushi.EnameData{
				Ename: "summer_camp_confirm_booking",
			}

			var images []*sushi.Image
			image_url := sessionTypes[sessionTypeSummerCamp].Icon2
			image, _ := sushi.NewImage(image_url)
			image.SetHeight(20)
			image.SetWidth(20)
			image.SetType(sushi.ImageTypeCircle)
			image.Id = sessionTypes[sessionTypeSummerCamp].Id
			images = append(images, image)
			slot_obj.Images = images

			button := &sushi.Button{
				Type:        sushi.ButtonTypeSolid,
				Text:        buttonText,
				ClickAction: change_bottom_button_click_action,
			}

			if tapEname != nil {
				footerButtonEvents := sushi.NewClevertapEvents()
				footerButtonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, slotFooterPayload, footerButtonEvents)
				button.AddClevertapTrackingItem(trackItem)
			}

			change_bottom_button := &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id2",
				Button:   button,
			}

			click_action, _ := sushi.NewTextClickAction("change_bottom_button")
			click_action.ChangeBottomButton = change_bottom_button
			slot_obj.ClickAction = click_action

		} else {
			var images []*sushi.Image
			image_url := sessionTypes[sessionTypeSummerCamp].Icon2
			image, _ := sushi.NewImage(image_url)
			image.SetHeight(20)
			image.SetWidth(20)
			image.SetType(sushi.ImageTypeCircle)
			image.Id = sessionTypes[sessionTypeSummerCamp].Id
			images = append(images, image)
			slot_obj.Images = images
			ageText := "5 - 9 years"
			if itm.SlotTypes[0].ProductArenaCategoryId == common.PLAY_ARENA_SUMMER_CAMP_AGE_GREATER_THAN_10 {
				ageText = "10 - 15 years"
			}

			customAlert := GetSummerCampSlotPopUp(ctx, ageText)
			clickAction := sushi.GetClickAction()
			clickAction.SetCustomAlertAction(customAlert)
			slot_obj.ClickAction = clickAction
		}
		slots_template_data = append(slots_template_data, *slot_obj)
	}
	*slotsTemplateData = slots_template_data
	return nil
}

func (s *SummerCampSlotPageTemplate) SetPageTracking(ctx context.Context, res *bookingPB.BookingSlotsV3Res) {
	payload := s.GetClevertapTrackingDefaultPayload(ctx, res)
	landingEname := &sushi.EnameData{
		Ename: "summer_camp_slot_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}

func (s *SummerCampSlotPageTemplate) GetClevertapTrackingDefaultPayload(ctx context.Context, res *bookingPB.BookingSlotsV3Res) map[string]interface{} {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["sport_id"] = res.SportId
	payload["facility_id"] = res.FacilityId
	payload["source"] = "summer_camp_slots"

	return payload
}

func GetSummerCampSlotPopUp(ctx context.Context, agetext string) *sushi.CustomAlert {
	alertTitle, _ := sushi.NewTextSnippet("Slot not selectable")
	alertTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	alertTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	alertTitle.SetColor(alertTitleColor)
	alertTitle.SetFont(alertTitleFont)

	message, _ := sushi.NewTextSnippet(fmt.Sprintf("Please select all users in the age group %s", agetext))
	messageColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	messageFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message.SetColor(messageColor)
	message.SetFont(messageFont)

	animation, _ := sushi.NewAnimation("https://fitso-images.curefit.co/file_assets/failure_image.json")
	animation.SetRepeatCount(1)
	image, _ := sushi.NewImageWithAnimation(animation)
	customAlert := &sushi.CustomAlert{
		Title:   alertTitle,
		Message: message,
		Image:   image,
		DismissAfterAction: true,
	}
	return customAlert
}

func (s *SummerCampSlotPageTemplate) GetSessionInfoSnippet(ctx context.Context) {
	sessionInfo := &slotModel.SessionsInfo{}
	var sessionsInfo []slotModel.SessionsInfo
	session := sessionTypes[sessionTypeSummerCamp]

	imageLink := session.Icon2
	titleText := session.Name
	buttonTitleText := strings.Join(session.Description, ". ")

	image, _ := sushi.NewImage(imageLink)
	image.SetHeight(24)
	image.SetWidth(24)
	image.SetType(sushi.ImageTypeCircle)

	title, _ := sushi.NewTextSnippet(titleText)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetFont(titleFont)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("what’s this?")
	buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
	button.SetFont(buttonFont)

	buttonTitle, _ := sushi.NewTextSnippet(buttonTitleText)
	buttonTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	buttonTitle.SetColor(buttonTitleColor)
	buttonTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	buttonTitle.SetFont(buttonTitleFont)

	buttonSubtitle, _ := sushi.NewTextSnippet("OK")
	buttonSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	buttonSubtitle.SetColor(buttonSubtitleColor)
	buttonSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	buttonSubtitle.SetFont(buttonSubtitleFont)

	buttonBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

	showTooltip := &sushi.ShowTooltip{
		Title:    buttonTitle,
		Subtitle: buttonSubtitle,
		BgColor:  buttonBgColor,
	}
	clickAction := sushi.GetClickAction()
	clickAction.SetShowTooltip(showTooltip)

	sessionInfo.Image = image
	sessionInfo.Title = title
	sessionInfo.BottomButton = button
	sessionInfo.ClickAction = clickAction
	sessionsInfo = append(sessionsInfo, *sessionInfo)
	s.SessionsInfo = &sessionsInfo
	return
}
