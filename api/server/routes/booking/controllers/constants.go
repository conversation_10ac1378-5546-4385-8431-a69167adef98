package bookingController

import (
	common "bitbucket.org/jogocoin/go_api/api/common"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
)

type SessionInfo struct {
	Id                int32
	Name              string
	Description       []string
	Icon              string
	Icon2             string
	CoachingAvailable bool
}

var (
	sessionTypeRegular      int32 = 1
	sessionTypeGuided       int32 = 2
	sessionTypeSkillup      int32 = 3
	PlayArenaAcademySkillUp int32 = 4
	sessionTypeBuddyHour    int32 = 6
	sessionTypeSummerCamp   int32 = 7
)

var sessionTypes = map[int32]SessionInfo{
	sessionTypeRegular: SessionInfo{
		Id:                233,
		Name:              "Regular session",
		Description:       REGULAR_SESSION_DESCRIPTION,
		Icon:              util.GetCDNLink("uploads/Group249881631018622.png"),
		Icon2:             util.GetCDNLink("uploads/Group249881631018622.png"),
		CoachingAvailable: false,
	},
	sessionTypeGuided: SessionInfo{
		Id:                234,
		Name:              "Guided session",
		Description:       GUIDED_SESSION_DESCRIPTION,
		Icon:              util.GetCDNLink("uploads/Group22726-.png"),
		Icon2:             util.GetCDNLink("uploads/<EMAIL>"),
		CoachingAvailable: true,
	},
	sessionTypeSkillup: SessionInfo{
		Id:                235,
		Name:              "Skill up session",
		Description:       SKILLUP_SESSION_DESCRIPTION,
		Icon:              util.GetCDNLink("uploads/Group244071629192862.png"),
		Icon2:             util.GetCDNLink("uploads/<EMAIL>"),
		CoachingAvailable: true,
	},
	sessionTypeSummerCamp: SessionInfo{
		Id:                236,
		Name:              "Summer Camp Session",
		Description:       SUMMERCAMP_SESSION,
		Icon:              util.GetCDNLink("uploads/brightness1648547319.png"),
		Icon2:             util.GetCDNLink("uploads/<EMAIL>"),
		CoachingAvailable: true,
	},
}

var buddyHourSessionTypes = map[int32]SessionInfo{
	common.SWIMMING_SPORT_ID: SessionInfo{
		Name:              "Leisure Swim",
		Description:       BUDDY_HOUR_SWIMMING_SESSION_DESCRIPTION,
		Icon:              util.GetCDNLink("uploads/LeisureSwimIcon1647490236.png"),
		Icon2:             util.GetCDNLink("uploads/LeisureSwimIcon1647490236.png"),
		CoachingAvailable: false,
	},
	common.BADMINTON_SPORT_ID: SessionInfo{
		Name:              "Buddy Hour",
		Description:       BUDDY_HOUR_BADMINTON_SESSION_DESCRIPTION,
		Icon:              util.GetCDNLink("uploads/BuddyHourIcon1647490154.png"),
		Icon2:             util.GetCDNLink("uploads/BuddyHourIcon1647490154.png"),
		CoachingAvailable: false,
	},
}

var BUDDY_HOUR_SWIMMING_SESSION_DESCRIPTION = []string{"Bring upto 1 buddy to swim with for free", "Only lifeguards will be present ( coaching not available)"}
var BUDDY_HOUR_BADMINTON_SESSION_DESCRIPTION = []string{"Bring upto 1 buddy to play with for free", "Partner matching & Coaching will not be provided"}
var REGULAR_SESSION_DESCRIPTION = []string{"Enjoy your personal game without any coach assistance"}
var GUIDED_SESSION_DESCRIPTION = []string{"Coach will be available for guidance and/or as a playing partner"}
var SKILLUP_SESSION_DESCRIPTION = []string{"Improve your game with skill focussed training session with our coaches"}
var SUMMERCAMP_SESSION = []string{"Coaches will be available for guidance in summer camp slots"}

const GUEST_BOOKING_TYPE = 3
const PRODUCT_ARENA_CATEGORGY_ID_ACADEMY_SESSION = 5
const PRODUCT_ARENA_CATEGORGY_ID_SUMMER_CAMP_SESSION = 11

func Contains(arr []int32, val int32) bool {
	for _, a := range arr {
		if a == val {
			return true
		}
	}
	return false
}
