package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	slotModel "bitbucket.org/jogocoin/go_api/api/models/booking/slots"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilityPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/render"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type SlotPageData struct {
	SubscriptionDetails         *productPB.GetUserSubscriptionStatusResponse
	TrialDetails                *userPB.TrialDetailsResponse
	IsPremiumUser               bool
	IsTrialUser                 bool
	FetchSlotsForSports         bool
	FetchSlotsForFacilitySports bool
	ContainsGuestUsers          bool
}

// SlotResponse represents the response structure of slots page
type SlotPageTemplate struct {
	Header                      *slotModel.Header                       `json:"header,omitempty"`
	ProgressBarData             *slotModel.ProgressBarStruct            `json:"progress_bar_data,omitempty"`
	CustomHeader                *slotModel.CustomHeader                 `json:"custom_header,omitempty"`
	SelectionTitle              *sushi.TextSnippet                      `json:"selection_title,omitempty"`
	Footer                      *sushi.FooterSnippetType2Layout         `json:"footer,omitempty"`
	DisabledFooter              *sushi.FooterSnippetType2Layout         `json:"disabled_footer,omitempty"`
	DetailsContainer            *slotModel.DetailsContainer             `json:"details_container,omitempty"`
	InfoContainer               *slotModel.InfoContainer                `json:"info_container,omitempty"`
	SupportContainer            *sushi.FitsoImageTextSnippetType7Layout `json:"support_container,omitempty"`
	Config                      *slotModel.SlotConfig                   `json:"config"`
	Items                       *[]slotModel.SlotItem                   `json:"items"`
	ClevertapTracking           []*sushi.ClevertapItem                  `json:"clever_tap_tracking,omitempty"`
	JumboTracking               []*jumbo.Item                           `json:"jumbo_tracking,omitempty"`
	SessionsInfo                *[]slotModel.SessionsInfo               `json:"sessions_info,omitempty"`
	TransitionFrequency         int32                                   `json:"transition_frequency,omitempty"`
	PageData                    SlotPageData                            `json:"-"`
	TotalOpenSlots              int32                                   `json:"-"`
	TotalVisibleSlots           int32                                   `json:"-"`
	OpenSlotIDs                 []int32                                 `json:"-"`
	PageProductArenaCategoryIDs []int32                                 `json:"-"`
}

func GetBookingSlotsV3C(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.BookingSlotsV3
	json = c.MustGet("jsonData").(structs.BookingSlotsV3)
	ctx := util.PrepareGRPCMetaData(c)

	var bookingCount int32
	if len(json.BookingUsers) > 0 {
		bookingCount = int32(len(json.BookingUsers))
	} else {
		bookingCount = 1
	}

	reqData := &bookingPB.BookingSlotsV3Req{
		FsId:          json.FsId,
		SportId:       json.SportId,
		BookingCount:  bookingCount,
		HideGuestMode: json.HideGuestMode,
	}

	if len(json.BookingUsers) > 0 {
		for _, bookingUser := range json.BookingUsers {
			reqData.BookingUserIds = append(reqData.BookingUserIds, bookingUser.UserID)
		}
	}

	template := SlotPageTemplate{}
	template.SetUserSubscriptionStatus(ctx, json.ProductCategoryId)
	template.PageData.ContainsGuestUsers = SetContainsGuestForTracking(ctx, reqData.BookingUserIds, bookingCount, template.PageData.IsTrialUser)
	if json.SportId > 0 {
		template.PageData.FetchSlotsForSports = true
	} else if json.FsId > 0 {
		template.PageData.FetchSlotsForFacilitySports = true
	} else {
		log.Println("func:GetBookingSlotsV3C, bad request")
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if template.PageData.IsPremiumUser {
		reqData.IsPremiumUser = true
	} else if template.PageData.IsTrialUser {
		reqData.IsTrial = 1
		template.SetUserTrialDetails(ctx)
	}

	bookingSlotResponse, err := cl.GetBookingSlotsV3(ctx, reqData)
	if err != nil {
		log.Println("func:GetBookingSlotsV3C, could not fetch bookingslots details ---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if bookingSlotResponse.Status != nil {
		if bookingSlotResponse.Status.Status == common.FAILED {
			c.JSON(http.StatusOK, model.StatusFailure(bookingSlotResponse.Status.Message))
			return
		}
		if bookingSlotResponse.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, model.StatusFailure(bookingSlotResponse.Status.Message))
			return
		}
	}

	if json.BottomSheet != 1 {
		template.SetHeaderSection(ctx, &json, bookingSlotResponse)

		if template.PageData.IsTrialUser {
			template.SetProgressBarSection(ctx)
			template.SetSupportContainerSection(ctx)
		}
	} else {
		template.SetCustomHeaderSection(ctx, &json, bookingSlotResponse)
	}

	template.SetSelectionTitleSection(ctx, &json, bookingSlotResponse)
	template.SetFooterSection(&json, bookingSlotResponse)

	showInfoContainerSection := template.checkInfoContainerShow(bookingSlotResponse)
	if showInfoContainerSection {
		template.SetInfoContainerSection(ctx, bookingSlotResponse)
	}
	template.SetConfigSection()
	template.SetSlotItemSection(ctx, &json, bookingSlotResponse)

	if bookingSlotResponse.CoachingAvailable {
		if featuresupport.SupportsPlayArenaCategories(ctx) && template.PageData.IsPremiumUser {
			template.SetSessionsInfoSection(ctx, bookingSlotResponse)
		} else {
			template.SetDetailsContainerSection(bookingSlotResponse)
		}
	} else if featuresupport.SupportsPlayArenaCategories(ctx) && bookingSlotResponse.BuddyHourSessionAvailable {
		template.SetSessionsInfoSection(ctx, bookingSlotResponse)
	}
	template.SetPageProductArenaCategoryIDs(ctx, bookingSlotResponse)
	template.SetPageTracking(ctx, json.Source, bookingSlotResponse)
	template.TransitionFrequency = 1

	render.Render(c, gin.H{"payload": template}, "index.html")
}

func SetContainsGuestForTracking(ctx context.Context, userIds []int32, bookingCount int32, isTrialUser bool) bool {
	containsGuestUsers := false
	if bookingCount == 1 || isTrialUser {
		return containsGuestUsers
	}

	var allowedUserIDs []int32
	if featuresupport.SupportsBringAGuest(ctx) && len(userIds) > 1 { // individual guest user booking not possible
		userClient := util.GetUserServiceClient()
		guestUsersResponse, err := userClient.GetGuestUsersEligibleForBooking(ctx, &userPB.Empty{})
		if err != nil {
			log.Printf("Error in fetching eligible guest users: user_id: %d, err: %v", util.GetUserIDFromContext(ctx), err)
			return containsGuestUsers
		}

		for _, user := range guestUsersResponse.GuestUsers {
			allowedUserIDs = append(allowedUserIDs, user.UserId)
		}
	}

	for _, bookingUser := range userIds {
		if Contains(allowedUserIDs, bookingUser) {
			containsGuestUsers = true
			return containsGuestUsers
		}
	}
	return containsGuestUsers
}

func (s *SlotPageTemplate) SetHeaderSection(ctx context.Context, req *structs.BookingSlotsV3, res *bookingPB.BookingSlotsV3Res) {

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &slotModel.Header{
		BgColor: bgColor,
	}

	var title_text string
	if s.PageData.IsTrialUser {
		title_text = "Book free trial"
		var trialText string
		trialLeft := s.PageData.TrialDetails.TrialDetails.TrialSessionsLeft
		if trialLeft == 1 {
			trialText = fmt.Sprintf("%d free trial available", trialLeft)
		} else {
			trialText = fmt.Sprintf("%d free trials available", trialLeft)
		}

		tag, _ := sushi.NewTag(trialText)
		tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
		tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
		if featuresupport.SupportsNewColor(ctx) {
			tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
			tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
		}

		tag.Title.SetFont(tagFont)
		tag.Title.SetColor(tagColor)
		tag.SetBgColor(tagBgColor)
		tag.SetBorderColor(tagBorderColor)

		header.Tag = tag
	} else {
		title_text = res.SportName
	}

	title, _ := sushi.NewTextSnippet(title_text)
	title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetFont(title_font)
	title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(title_color)

	header.Title = title
	s.Header = header
}

func (s *SlotPageTemplate) SetProgressBarSection(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	colorType := sushi.ColorTypeTeal
	if featuresupport.SupportsNewColor(ctx) {
		colorType = sushi.ColorTypeCyan
	}
	progressbarColor := &[]sushi.Color{
		sushi.Color{
			Type: colorType,
			Tint: sushi.ColorTint500,
		},
	}
	progressBarDetails := &slotModel.ProgressBarStruct{
		Progress:       66,
		BgColor:        bgColor,
		ProgressColors: progressbarColor,
	}
	s.ProgressBarData = progressBarDetails
}

func (s *SlotPageTemplate) SetCustomHeaderSection(ctx context.Context, req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	bgColorTint := sushi.ColorTint100
	if featuresupport.SupportsNewColor(ctx) {
		bgColorTint = sushi.ColorTint50
	}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)
	customHeaderType1Obj := &slotModel.CustomHeaderType1{
		BgColor: bgColor,
	}

	var title_text string
	if s.PageData.FetchSlotsForFacilitySports {
		title_text = response.FacilityName

		subtitle, _ := sushi.NewTextSnippet(response.SubzoneName)
		subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
		subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		subtitle.SetFont(subtitle_font)
		subtitle.SetColor(subtitle_color)

		customHeaderType1Obj.SubTitle = subtitle
	} else if s.PageData.FetchSlotsForSports {
		title_text = "Booking slot for " + response.SportName
	}

	title, _ := sushi.NewTextSnippet(title_text)
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(title_font)
	title.SetColor(title_color)

	customHeaderType1Obj.Title = title

	customHeaderDetails := &slotModel.CustomHeader{
		Type:              slotModel.CustomHeaderType1Val,
		CustomHeaderType1: customHeaderType1Obj,
	}
	s.CustomHeader = customHeaderDetails
}

func (s *SlotPageTemplate) SetSelectionTitleSection(ctx context.Context, req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	if req.BottomSheet == 1 && s.PageData.FetchSlotsForSports {
		return
	}
	var selection_title_text string
	selection_title_text = "Booking slot for " + response.SportName
	if req.BottomSheet != 1 && s.PageData.IsTrialUser {
		selection_title_text = "Select your slot"
	}

	title, _ := sushi.NewTextSnippet(selection_title_text)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	s.SelectionTitle = title
}

func (s *SlotPageTemplate) SetFooterSection(req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	var buttonWeight []int32

	var buttonItem2Text string
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Type: "solid",
		Text: buttonItem2Text,
		Font: buttonItem2Font,
		Id:   "bottom_button_id2",
	}
	status := s.PageData.SubscriptionDetails.SubscriptionStatus
	tomorrowSubscription := s.PageData.SubscriptionDetails.TomorrowParentSubscription || s.PageData.SubscriptionDetails.TomorrowChildSubscription
	if status == productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED {
		buttonItem2Text := "Buy membership to proceed"
		color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		buttonItem2.Text = buttonItem2Text
		buttonItem2.BgColor = color

		click_action := sushi.GetClickAction()
		buy_for_me_deep_link := &sushi.Deeplink{
			URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID), //sending to purchase landing page till dec 2021
			//URL: util.GetBuyingForMeDeeplink(s.PageData.SubscriptionDetails.ProductId, false),
		}
		click_action.SetDeeplink(buy_for_me_deep_link)
		buttonItem2.ClickAction = click_action
	} else if status == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION && !tomorrowSubscription {
		buttonItem2Text := "return to home page"
		color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		buttonItem2.Text = buttonItem2Text
		buttonItem2.BgColor = color

		click_action := sushi.GetClickAction()
		deep_link := &sushi.Deeplink{
			URL: util.GetHomeDeeplink(),
		}
		click_action.SetDeeplink(deep_link)
		buttonItem2.ClickAction = click_action
	} else {
		buttonItem2Text := "Select a slot to proceed"
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		buttonItem2.Text = buttonItem2Text
		buttonItem2.BgColor = color
		buttonItem2.IsActionDisabled = 1
	}

	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}

	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData:   buttonData,
		ButtonWeight: buttonWeight,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
	s.DisabledFooter = footer
}

func (s *SlotPageTemplate) SetDetailsContainerSection(response *bookingPB.BookingSlotsV3Res) {

	title, _ := sushi.NewTextSnippet("Coach available in these slots")
	image_url := util.GetCDNLink("uploads/Group22726-.png")
	image, _ := sushi.NewImage(image_url)
	detailsContainerData := &slotModel.DetailsContainer{
		Title: title,
		Image: image,
	}
	if response.CoachingSport {
		subtitle, _ := sushi.NewTextSnippet("Our coaches also double up as your playing partners in case you don't have a playing partner.")
		detailsContainerData.SubTitle = subtitle
	}
	s.DetailsContainer = detailsContainerData
}

func (s *SlotPageTemplate) SetInfoContainerSection(ctx context.Context, response *bookingPB.BookingSlotsV3Res) {
	var titleText, subTitleText, imageLink string

	imageLink = util.GetCDNLink("uploads/Group240401627311075.png")

	if Contains([]int32{111, 85, 124, 91, 88}, response.FsId) {
		imageLink = util.GetCDNLink("uploads/MicrosoftTeams-image(5)1627286620.png")
		titleText = "Safety Guidelines"
		subTitleText = "Complete vaccination is mandatory to enter the facility for your safety"
	}
	image, _ := sushi.NewImage(imageLink)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)

	infoContainerData := &slotModel.InfoContainer{
		Image:        image,
		BgColor:      bgColor,
		BorderColor:  borderColor,
		CornerRadius: int32(12),
	}

	if featuresupport.SupportsPlayArenaCategories(ctx) {
		titleText = "In case it rains, the center may be closed."
		subTitleText = ""
	} else {
		titleText = "Monsoon Alert"
		subTitleText = "Please note that this is an open facility. In case it rains, the center may be closed."
	}

	if response.FsId == 166 {
		titleText = "Double vaccination mandatory"
		subTitleText = "Please get Aarogya Setu vaccination status checked at the center."
	}

	if Contains([]int32{111, 85, 124, 91, 88}, response.FsId) {
		titleText = "Complete vaccination is mandatory to enter the facility for your safety"
		subTitleText = ""
	}

	title, _ := sushi.NewTextSnippet(titleText)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetFont(titleFont)

	infoContainerData.Title = title

	if len(subTitleText) > 0 {
		subTitle, _ := sushi.NewTextSnippet(subTitleText)
		subTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subTitle.SetColor(subTitleColor)
		subTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subTitle.SetFont(subTitleFont)

		infoContainerData.SubTitle = subTitle
	}

	s.InfoContainer = infoContainerData
}

func (s *SlotPageTemplate) SetSupportContainerSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Contact <EMAIL>.")

	rightButton, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)

	rightButton.SetText("Call")
	rightButton.SetPrefixIcon(callIcon)
	rightButton.SetClickAction(clickAction)

	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["is_trial"] = 1
	tapEname := &sushi.EnameData{
		Ename: "call_click",
	}

	callEvents := sushi.NewClevertapEvents()
	callEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, callEvents)
	rightButton.AddClevertapTrackingItem(trackItem)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{
		BgColor:     bgColor,
		Title:       title,
		//RightButton: rightButton,
		BorderColor: borderColor,
	}
	rightButton1, _ := sushi.NewButton(sushi.ButtontypeText)
	rightButton1.SetText(" ")
	item.RightButton = rightButton1

	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}

	supportContainerData := &sushi.FitsoImageTextSnippetType7Layout{
		LayoutConfig:        layoutConfig,
		FitsoImageTextType7: snippet,
	}

	s.SupportContainer = supportContainerData
}

func (s *SlotPageTemplate) SetConfigSection() {
	selected_title_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	selected_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	unselected_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	unselected_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)

	headerConfigDetails := &slotModel.HeaderConfig{
		SelectedTitleColor:      selected_title_color,
		SelectedSubtitleColor:   selected_subtitle_color,
		UnselectedTitleColor:    unselected_title_color,
		UnselectedSubtitleColor: unselected_subtitle_color,
	}

	selected_item_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	unselected_item_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	enabled_item_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	disabled_item_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	enabled_item_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabled_item_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)

	itemConfigDetails := &slotModel.ItemConfig{
		SelectedItemColor:     selected_item_color,
		UnselectedItemColor:   unselected_item_color,
		EnabledItemColor:      enabled_item_color,
		DisabledItemColor:     disabled_item_color,
		EnabledItemTitleColor: enabled_item_title_color,
		DisabledItemTitleFont: disabled_item_title_font,
	}

	configDetails := &slotModel.SlotConfig{
		HeaderConfig: headerConfigDetails,
		ItemConfig:   itemConfigDetails,
	}
	s.Config = configDetails
}

func isSeasonalPoolAndInClosingSeason(ctx context.Context, facilityId int32) bool {
	facilityClient := util.GetFacilitySportClient()
	req := facilityPB.GetFacilityDetailsRequest{
		FacilityId: facilityId,
		SportId:    3,
	}
	res, err := facilityClient.GetFacilityDetails(ctx, &req)
	if err != nil {
		log.Println("Cannot find seasonal pool data for facilityId: ", facilityId, " ERROR: ", err)
		return true
	}
	if res.SeasonalPoolInfo != nil && res.SeasonalPoolInfo.Flag == true && res.SeasonalPoolInfo.CurrentSeason != common.SWIMMING_OPEN_SEASON && res.SeasonalPoolInfo.CurrentSeason != common.SWIMMING_MID_SEASON {
		return true
	}
	return false
}

func (s *SlotPageTemplate) SetSlotItemSection(ctx context.Context, req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	var tomorrowSubscription bool
	if s.PageData.SubscriptionDetails.TomorrowParentSubscription {
		tomorrowSubscription = true
	} else if s.PageData.SubscriptionDetails.TomorrowChildSubscription && len(req.BookingUsers) > 1 {
		tomorrowSubscription = true
	}
	var slotItems []slotModel.SlotItem
	if len(response.BookingSlots) > 0 {
		loader_title, _ := sushi.NewTextSnippet("Loading slots for next 2 days")
		loader_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
		loader_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		loader_title.SetFont(loader_title_font)
		loader_title.SetColor(loader_title_color)
		loader := &slotModel.SlotLoaderStruct{
			Title:           loader_title,
			IsLoaderPresent: true,
		}

		for index, item := range response.BookingSlots {
			var title_text string
			var dayInterval int32
			if item.IsToday == true {
				title_text = "Today"
				dayInterval = 0
			} else if item.IsTomorrow == true {
				title_text = "Tomorrow"
				dayInterval = 1
			} else {
				title_text = item.WeekDay
				dayInterval = 2
			}
			title, _ := sushi.NewTextSnippet(title_text)
			subtitle, _ := sushi.NewTextSnippet(item.Date)
			if util.IsHighPeakSlotFacilitySport(ctx, response.FsId) && s.PageData.SubscriptionDetails.SubscriptionStatus != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
				closed_image_url := util.GetCDNLink("uploads/Group244601630308904.png")
				closed_image, _ := sushi.NewImage(closed_image_url)
				closed_image.SetAspectRatio(1.0)
				closed_image.SetHeight(200)
				closed_image.SetWidth(200)

				closed_title, _ := sushi.NewTextSnippet("No slots available for booking!")
				error := &slotModel.SlotErrorStruct{
					Title: closed_title,
					Image: closed_image,
				}

				obj := &slotModel.SlotItem{
					Title:      title,
					SubTitle:   subtitle,
					Error:      error,
					IsDisabled: item.IsDisabled,
				}

				if item.IsDisabled {
					obj.Loader = loader
				}
				slotItems = append(slotItems, *obj)
				continue
			}
			if s.PageData.FetchSlotsForFacilitySports && response.SportId == common.SWIMMING_SPORT_ID && isSeasonalPoolAndInClosingSeason(ctx, response.FacilityId) && s.PageData.SubscriptionDetails.SubscriptionStatus != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
				closed_image_url := util.GetCDNLink("uploads/Group244601630308904.png")
				closed_image, _ := sushi.NewImage(closed_image_url)
				closed_image.SetAspectRatio(1.0)
				closed_image.SetHeight(200)
				closed_image.SetWidth(200)

				closed_title, _ := sushi.NewTextSnippet("No slots available for booking!")
				error := &slotModel.SlotErrorStruct{
					Title: closed_title,
					Image: closed_image,
				}

				obj := &slotModel.SlotItem{
					Title:      title,
					SubTitle:   subtitle,
					Error:      error,
					IsDisabled: item.IsDisabled,
				}
				if item.IsDisabled {
					obj.Loader = loader
				}
				slotItems = append(slotItems, *obj)
				continue
			}
			if item.IsClosed == false && !(item.IsToday && tomorrowSubscription) {
				var morningSlots []slotModel.SlotStruct
				var eveningSlots []slotModel.SlotStruct
				s.GetSlotContent(ctx, req, item.MorningSlots, &morningSlots, response, dayInterval)
				s.GetSlotContent(ctx, req, item.EveningSlots, &eveningSlots, response, dayInterval)

				var slotSections []slotModel.SlotSection
				if len(morningSlots) > 0 {
					morning_title, _ := sushi.NewTextSnippet("MORNING")
					morning_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					morning_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					morning_title.SetFont(morning_title_font)
					morning_title.SetColor(morning_title_color)
					morning_title.SetKerning(3)

					morning_subtitle, _ := sushi.NewTextSnippet("(" + strconv.Itoa(len(morningSlots)) + " slots)")
					morning_subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					morning_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					morning_subtitle.SetFont(morning_subtitle_font)
					morning_subtitle.SetColor(morning_subtitle_color)

					morning_slot_section_header := &slotModel.SlotSectionHeader{
						Title:    morning_title,
						SubTitle: morning_subtitle,
					}
					morning_slot_section := &slotModel.SlotSection{
						SectionHeader: morning_slot_section_header,
						Slots:         morningSlots,
					}
					slotSections = append(slotSections, *morning_slot_section)
				}

				if len(eveningSlots) > 0 {
					evening_title, _ := sushi.NewTextSnippet("EVENING")
					evening_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					evening_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					evening_title.SetFont(evening_title_font)
					evening_title.SetColor(evening_title_color)
					evening_title.SetKerning(3)

					evening_subtitle, _ := sushi.NewTextSnippet("(" + strconv.Itoa(len(eveningSlots)) + " slots)")
					evening_subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					evening_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					evening_subtitle.SetFont(evening_subtitle_font)
					evening_subtitle.SetColor(evening_subtitle_color)

					evening_slot_section_header := &slotModel.SlotSectionHeader{
						Title:    evening_title,
						SubTitle: evening_subtitle,
					}
					evening_slot_section := &slotModel.SlotSection{
						SectionHeader: evening_slot_section_header,
						Slots:         eveningSlots,
					}
					slotSections = append(slotSections, *evening_slot_section)
				}

				tapPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
				tapPayload["day"] = dayInterval
				tapEname := &sushi.EnameData{
					Ename: "slots_page_day_tab_click",
				}
				tabEvents := sushi.NewClevertapEvents()
				tabEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tabEvents)

				obj := &slotModel.SlotItem{
					Title:      title,
					SubTitle:   subtitle,
					IsDisabled: item.IsDisabled,
				}

				if item.IsDisabled && util.GetAppTypeFromContext(ctx) != common.AppTypeWebFrontend && featuresupport.SupportsThirdDaySlotsTransition(ctx) {
					obj.Loader = loader
				} else {
					obj.Sections = slotSections
					obj.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
					obj.JumboTracking = []*jumbo.Item{s.getSlotTabJumboTrackItem(ctx, index, req.Source)}

					if req.BottomSheet == 1 && req.FsId > 0 && item.ShowSportSlotsOption && featuresupport.SupportsFullPageSlot(ctx) && s.PageData.IsPremiumUser {
						additionalInfoItem := s.getAdditionalInfoItem(ctx, req, response, dayInterval)
						obj.AdditionalInfo = []*sushi.TextButtonSnippetType3Layout{additionalInfoItem}
					}
				}

				slotItems = append(slotItems, *obj)
			} else {
				error := getClosedFacilitySnippet(ctx, response, item.ClosureInfo)
				obj := &slotModel.SlotItem{
					Title:      title,
					SubTitle:   subtitle,
					Error:      error,
					IsDisabled: item.IsDisabled,
				}

				if item.IsDisabled {
					obj.Loader = loader
				}
				slotItems = append(slotItems, *obj)
			}
		}
	}
	s.Items = &slotItems
}

func (s *SlotPageTemplate) getAdditionalInfoItem(ctx context.Context, req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res, dayInterval int32) *sushi.TextButtonSnippetType3Layout {
	text_button_snippet_type_3_title, _ := sushi.NewTextSnippet("Can’t find your preferred slot?")
	text_button_snippet_type_3_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	text_button_snippet_type_3_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	text_button_snippet_type_3_title.SetFont(text_button_snippet_type_3_title_font)
	text_button_snippet_type_3_title.SetColor(text_button_snippet_type_3_title_color)

	text_button_snippet_type_3_button, _ := sushi.NewButton(sushi.ButtontypeText)
	text_button_snippet_type_3_button.SetSize(sushi.ButtonSizeMedium)
	text_button_snippet_type_3_button.SetText(fmt.Sprintf("See all %s slots", response.SportName))
	text_button_snippet_type_3_button.SetAlignment(sushi.ButtonAlignmentCenter)

	text_button_snippet_type_3_button_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	text_button_snippet_type_3_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	text_button_snippet_type_3_button.SetFont(text_button_snippet_type_3_button_font)
	text_button_snippet_type_3_button.SetColor(text_button_snippet_type_3_button_color)

	text_button_snippet_type_3_button_ca := sushi.GetClickAction()

	out := map[string]interface{}{
		"sport_id":      response.SportId,
		"booking_users": req.BookingUsers,
		"source":        "center_slot",
	}

	payload, err := json.Marshal(out)
	if err != nil {
		log.Println("error in marshalling the payload data")
	}

	text_button_snippet_type_3_button_deeplink := &sushi.Deeplink{
		URL:            util.GetFullPageSlotsDeeplink(),
		PostbackParams: string(payload),
	}
	text_button_snippet_type_3_button_ca.SetDeeplink(text_button_snippet_type_3_button_deeplink)

	text_button_snippet_type_3_button.ClickAction = text_button_snippet_type_3_button_ca

	// clevertap tracking
	seeAllSportSlotsPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
	seeAllSportSlotsPayload["day"] = dayInterval
	seeAllSportSlotsPayload["source"] = req.Source
	seeAllSportSlotsTapEname := &sushi.EnameData{
		Ename: "see_all_sport_slots_button_tap",
	}
	seeAllSportSlotsEvents := sushi.NewClevertapEvents()
	seeAllSportSlotsEvents.SetTap(seeAllSportSlotsTapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, seeAllSportSlotsPayload, seeAllSportSlotsEvents)
	text_button_snippet_type_3_button.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	text_button_snippet_type_3 := &sushi.TextButtonSnippetType3Snippet{
		Title:  text_button_snippet_type_3_title,
		Button: text_button_snippet_type_3_button,
	}

	text_button_snippet_type_3_layout := &sushi.LayoutConfig{
		SnippetType:  sushi.TextButtonSnippetType3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	additionalInfoItem := &sushi.TextButtonSnippetType3Layout{
		LayoutConfig:           text_button_snippet_type_3_layout,
		TextButtonSnippetType3: text_button_snippet_type_3,
	}

	return additionalInfoItem
}

func (s *SlotPageTemplate) getSlotTabJumboTrackItem(ctx context.Context, slotTabIndex int, source string) *jumbo.Item {
	payload := make(map[string]interface{})

	payload["source_page"] = source

	if s.PageData.IsTrialUser {
		payload["is_trial"] = true
	}
	payload["selected_slot_tab_index"] = slotTabIndex

	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.SlotPageDayTabChange)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func getClosedFacilitySnippet(ctx context.Context, response *bookingPB.BookingSlotsV3Res, closureInfo *bookingPB.ClosureInfo) *slotModel.SlotErrorStruct {
	var closed_message, closed_image_url string

	// default case
	if closureInfo == nil {
		closed_image_url = util.GetCDNLink("uploads/Group244601630308904.png")
		closed_message = "No slots available for booking!"
	} else {
		closed_image_url = closureInfo.DisplayImageUrl
		closed_message = closureInfo.DisplayText
	}

	closed_image, _ := sushi.NewImage(closed_image_url)
	closed_image.SetAspectRatio(1.0)
	closed_image.SetHeight(200)
	closed_image.SetWidth(200)

	closed_title, _ := sushi.NewTextSnippet(closed_message)
	error := &slotModel.SlotErrorStruct{
		Title: closed_title,
		Image: closed_image,
	}

	return error
}

func (s *SlotPageTemplate) GetSlotContent(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	slotsData []*bookingPB.BookingSlot,
	slotsTemplateData *[]slotModel.SlotStruct,
	response *bookingPB.BookingSlotsV3Res,
	dayInterval int32,
) error {
	var slots_template_data []slotModel.SlotStruct
	if len(slotsData) > 0 {
		for _, itm := range slotsData {
			s.TotalVisibleSlots += 1
			slot_title := itm.Timing
			if itm.BookingAllowed != true && len(itm.AvailablityText) > 0 {
				slot_title += " " + itm.AvailablityText
			}

			title, _ := sushi.NewTextSnippet(slot_title)
			slot_obj := &slotModel.SlotStruct{
				Title:  title,
				SlotId: itm.SlotId,
			}

			if itm.BookingAllowed != true {
				slot_obj.IsDisabled = true

				if itm.AlreadyBooked == true {
					subtitle, _ := sushi.NewTextSnippet("booked")
					subtitle_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
					subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					subtitle.SetFont(subtitle_font)
					subtitle.SetColor(subtitle_color)

					slot_obj.SubTitle = subtitle
				} else if itm.IsSoldOut == true {
					title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
					title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
					slot_obj.Title.SetFont(title_font)
					slot_obj.Title.SetColor(title_color)

					// see other venues
					if req.BottomSheet == 1 && itm.OtherCentresAvailable == true {
						subtitle, _ := sushi.NewTextSnippet("see other centers")
						slot_obj.SubTitle = subtitle
						slot_obj.ClickAction = s.GetSoldOutSlotClickAction(ctx, req, response, itm)

						otherVenuePayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
						otherVenuePayload["day"] = dayInterval
						otherVenuePayload["slot_id"] = itm.SlotId
						otherVenuePayload["source"] = req.Source
						otherVenueTapEname := &sushi.EnameData{
							Ename: "slot_other_venues_tap",
						}
						otherVenueEvents := sushi.NewClevertapEvents()
						otherVenueEvents.SetTap(otherVenueTapEname)
						trackItem := sushi.GetClevertapTrackItem(ctx, otherVenuePayload, otherVenueEvents)
						slot_obj.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
					}
				}
			} else {
				s.TotalOpenSlots += 1
				s.OpenSlotIDs = append(s.OpenSlotIDs, itm.SlotId)
				var images []*sushi.Image
				if itm.CoachingFlag {
					if featuresupport.SupportsPlayArenaCategories(ctx) {
						for _, i := range itm.SlotTypes {
							val := i.ProductArenaCategoryId
							showIcon := false

							if !featuresupport.SupportsSingleImageOnSlot(ctx) && util.GetAppTypeFromContext(ctx) == featuresupport.Android {
								showIcon = true
							} else {
								if val != sessionTypeRegular {
									showIcon = true
								} else {
									showIcon = false
								}
							}
							if showIcon {
								image_url := sessionTypes[val].Icon
								image, _ := sushi.NewImage(image_url)
								image.SetHeight(20)
								image.SetWidth(20)
								image.SetType(sushi.ImageTypeCircle)
								image.Id = sessionTypes[val].Id
								images = append(images, image)
							}
						}

						if len(itm.SlotTypes) > 1 {

							slot_obj.ClickAction = s.GetMultipleCoachingSlotClickAction(ctx, req, response, itm, dayInterval, itm.SlotTypes)
						} else {
							slot_obj.ClickAction = s.GetAllowedSlotClickAction(ctx, req, response, itm, dayInterval, sessionTypeGuided, false)
						}

						slot_obj.Images = images
					} else {
						slot_obj.ClickAction = s.GetAllowedSlotClickAction(ctx, req, response, itm, dayInterval, sessionTypeGuided, false)
						image_url := sessionTypes[sessionTypeGuided].Icon
						image, _ := sushi.NewImage(image_url)
						slot_obj.Image = image
					}
				} else {
					if isSlotTypesBuddyHour(itm.SlotTypes) {
						for _, element := range itm.SlotTypes {
							showIcon := element.ProductArenaCategoryId == sessionTypeBuddyHour
							if showIcon {
								if response.SportId == common.SWIMMING_SPORT_ID {
									image_url := buddyHourSessionTypes[common.SWIMMING_SPORT_ID].Icon
									image, _ := sushi.NewImage(image_url)
									image.SetHeight(20)
									image.SetWidth(20)
									image.SetType(sushi.ImageTypeCircle)
									images = append(images, image)
									slot_obj.Images = images
								} else {
									image_url := buddyHourSessionTypes[common.BADMINTON_SPORT_ID].Icon
									image, _ := sushi.NewImage(image_url)
									image.SetHeight(20)
									image.SetWidth(20)
									image.SetType(sushi.ImageTypeCircle)
									images = append(images, image)
									slot_obj.Images = images
								}
							}
						}
						slot_obj.ClickAction = s.GetAllowedBuddyHourSlotClickAction(ctx, req, response, itm, dayInterval)
					} else {
						slot_obj.ClickAction = s.GetAllowedSlotClickAction(ctx, req, response, itm, dayInterval, sessionTypeRegular, false)
					}
				}

				if s.PageData.FetchSlotsForFacilitySports {
					if itm.ShowRemainingSpots {
						var remainingSpots int32
						for _, i := range itm.SlotTypes {
							remainingSpots += i.RemainingCapacity
						}

						var bottom_container_title_text string
						if remainingSpots == 1 {
							bottom_container_title_text = fmt.Sprintf("%d spot left", remainingSpots)
						} else {
							bottom_container_title_text = fmt.Sprintf("%d spots left", remainingSpots)
						}

						containerColorType := sushi.ColorTypeOrange

						bottom_container_title, _ := sushi.NewTextSnippet(bottom_container_title_text)
						bottom_container_title_color, _ := sushi.NewColor(containerColorType, sushi.ColorTint600)
						bottom_container_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
						bottom_container_title.SetColor(bottom_container_title_color)
						bottom_container_title.SetFont(bottom_container_title_font)
						prefix_icon_color, _ := sushi.NewColor(containerColorType, sushi.ColorTint400)
						prefix_icon, _ := sushi.NewIcon(sushi.ThunderIcon, prefix_icon_color)
						bottom_container_title.SetPrefixIcon(prefix_icon)
						bottom_container_title.SetAlignment(sushi.TextAlignmentCenter)

						bottom_container_bg_color, _ := sushi.NewColor(containerColorType, sushi.ColorTint100)

						bottom_container := &slotModel.BottomContainer{
							Title:   bottom_container_title,
							BgColor: bottom_container_bg_color,
						}
						slot_obj.BottomContainer = bottom_container
					}
				}
			}
			slot_obj.JumboTracking = []*jumbo.Item{s.getSlotButtonJumboTrackItem(ctx, req.Source, itm, dayInterval, response)}

			slots_template_data = append(slots_template_data, *slot_obj)
		}
	}
	*slotsTemplateData = slots_template_data
	return nil
}

func isSlotTypesBuddyHour(slotTypes []*bookingPB.SlotTypeInfo) bool {
	if slotTypes == nil {
		return false
	}
	for _, slotType := range slotTypes {
		if slotType.ProductArenaCategoryId == sessionTypeBuddyHour {
			return true
		}
	}
	return false
}

func (s *SlotPageTemplate) getSlotButtonJumboTrackItem(
	ctx context.Context,
	source string,
	slot *bookingPB.BookingSlot,
	dayInterval int32,
	res *bookingPB.BookingSlotsV3Res) *jumbo.Item {

	payload := make(map[string]interface{})

	var slotProductArenaCategoryIDs []int32

	for _, slotType := range slot.SlotTypes {
		slotProductArenaCategoryIDs = append(slotProductArenaCategoryIDs, slotType.ProductArenaCategoryId)
	}

	payload["source_page"] = source

	if s.PageData.IsTrialUser {
		payload["is_trial"] = true
	}
	payload["product_arena_category_ids"] = slotProductArenaCategoryIDs
	payload["selected_slot_tab_index"] = dayInterval
	payload["slot_id"] = slot.SlotId
	payload["facility_id"] = res.FacilityId
	payload["sport_id"] = res.SportId

	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.SlotSelectClick)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (s *SlotPageTemplate) GetAllowedBuddyHourSlotClickAction(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	response *bookingPB.BookingSlotsV3Res,
	itm *bookingPB.BookingSlot,
	dayInterval int32,
) *sushi.ClickAction {

	out := map[string]interface{}{
		"fs_id":                     response.FsId,
		"slot_id":                   itm.SlotId,
		"booking_time":              itm.SlotDatetime.Seconds,
		"booking_users":             req.BookingUsers,
		"product_arena_category_id": sessionTypeBuddyHour,
		"source":                    req.Source,
	}

	payload, err := json.Marshal(out)
	if err != nil {
		log.Println("error in marshalling the payload data")
	}
	var tapEname *sushi.EnameData
	slotFooterPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
	slotFooterPayload["slot_id"] = itm.SlotId
	slotFooterPayload["day"] = dayInterval
	slotFooterPayload["session_type"] = sessionTypeBuddyHour
	slotFooterPayload["source"] = req.Source
	slotFooterPayload["product_arena_category_id"] = sessionTypeBuddyHour

	var jumboTrackItem *jumbo.Item

	change_bottom_button_click_action := sushi.GetClickAction()
	positive_action_click_action := sushi.GetClickAction()
	custom_alert_click_action, _ := sushi.NewTextClickAction("custom_alert")

	title, _ := sushi.NewTextSnippet("Buddy Hour")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	var messageText, subtitle2Text, imageUrl string
	if response.SportId == common.SWIMMING_SPORT_ID || response.SportId == common.BADMINTON_SPORT_ID {
		sessionType := buddyHourSessionTypes[response.SportId]
		imageUrl = sessionType.Icon
		messageText = sessionType.Description[0]
		subtitle2Text = "Note: " + sessionType.Description[1]
	}

	image, _ := sushi.NewImage(imageUrl)
	image.SetHeight(40)
	image.SetWidth(40)

	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)

	subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
	subtitle2_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	subtitle2_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	subtitle2.SetFont(subtitle2_font)
	subtitle2.SetColor(subtitle2_color)

	positiveAction := &sushi.Button{
		Type: sushi.ButtonTypeSolid,
		Size: sushi.ButtonSizeLarge,
	}

	var buttonText, bottomButtonText string
	if s.PageData.FetchSlotsForSports {
		payload := structs.GetCentresForSlotBookingReq{
			SportId:                req.SportId,
			SlotId:                 itm.SlotId,
			BookingDate:            itm.SlotDatetime.Seconds,
			Source:                 req.Source,
			BookingUsers:           req.BookingUsers,
			ProductArenaCategoryId: sessionTypeBuddyHour,
		}

		params, _ := json.Marshal(payload)

		var deep_link_url string
		if s.PageData.IsTrialUser && req.BottomSheet != 1 {
			deep_link_url = util.GetTrialCentersPageDeeplink()
		} else {
			if req.BottomSheet == 1 {
				deep_link_url = util.GetCentersDeeplink()
			} else {
				deep_link_url = util.GetFullPageCentersDeeplink()
			}
		}
		deep_link := &sushi.Deeplink{
			URL:            deep_link_url,
			PostbackParams: string(params),
		}
		buttonText = "Proceed to select center"
		bottomButtonText = buttonText
		positive_action_click_action.SetDeeplink(deep_link)
	} else if s.PageData.FetchSlotsForFacilitySports {
		loggedInUser := util.GetUserIDFromContext(ctx)
		if loggedInUser > 0 {
			api_call_multi_action := &sushi.APICallAction{
				RequestType: sushi.POSTRequestType,
				URL:         "v2/booking/publish",
				Body:        string(payload),
			}

			status := s.PageData.SubscriptionDetails.SubscriptionStatus
			tomorrowSubscription := s.PageData.SubscriptionDetails.TomorrowParentSubscription || s.PageData.SubscriptionDetails.TomorrowChildSubscription
			jumboTrackItem = s.getSelectCenterJumboTrackItem(ctx, req.Source, itm, sessionTypeBuddyHour, dayInterval, response)

			switch status {
			case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
				buttonText = "Confirm Booking"
				positive_action_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				positive_action_click_action.ApiCallMultiAction = api_call_multi_action
				slotFooterPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				if len(req.BookingUsers) > 1 {
					if response.SportId == common.SWIMMING_SPORT_ID {
						bottomButtonText = fmt.Sprintf("Book Leisure Swim slot for %d people", len(req.BookingUsers))
					} else {
						bottomButtonText = fmt.Sprintf("Book Buddy Hour slot for %d people", len(req.BookingUsers))
					}
				} else {
					if response.SportId == common.SWIMMING_SPORT_ID {
						bottomButtonText = "Book Leisure Swim Slot"
					} else {
						bottomButtonText = "Book Buddy Hour Slot"
					}
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
				if tomorrowSubscription {
					buttonText = "Confirm Booking"
					change_bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
					change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
					slotFooterPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
					if response.SportId == common.SWIMMING_SPORT_ID {
						bottomButtonText = "Book Leisure Swim Slot"
					} else {
						bottomButtonText = "Book Buddy Hour Slot"
					}
					tapEname = &sushi.EnameData{
						Ename: "confirm_booking",
					}
				} else {
					buttonText = "return to home page"
					bottomButtonText = buttonText
					deep_link := &sushi.Deeplink{
						URL: util.GetHomeDeeplink(),
					}
					positive_action_click_action.SetDeeplink(deep_link)
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
				fallthrough
			case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
				fallthrough
			case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
				buttonText = "Buy membership to proceed"
				bottomButtonText = buttonText
				buy_for_me_deep_link := &sushi.Deeplink{
					URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID), //sending to purchase landing page till dec 2021
				}
				positive_action_click_action.SetDeeplink(buy_for_me_deep_link)
				break
			}

			jumboTrackItem = s.getSelectCenterJumboTrackItem(ctx, req.Source, itm, sessionTypeBuddyHour, dayInterval, response)
		} else {
			return nil
		}
	}

	positiveAction.SetText(buttonText)
	positiveAction.ClickAction = positive_action_click_action
	if jumboTrackItem != nil {
		positiveAction.AddJumboTrackingItem(jumboTrackItem)
	}

	if tapEname != nil {
		footerButtonEvents := sushi.NewClevertapEvents()
		footerButtonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, slotFooterPayload, footerButtonEvents)
		positiveAction.AddClevertapTrackingItem(trackItem)
	}

	custom_alert := &sushi.CustomAlert{
		Title:          title,
		Image:          image,
		Message:        message,
		PositiveAction: positiveAction,
		Subtitle2:      subtitle2,
		DismissAfterAction: true,
	}

	custom_alert_click_action.SetCustomAlertAction(custom_alert)

	if len(bottomButtonText) == 0 {
		bottomButtonText = "Book selected slot"
	}
	button := &sushi.Button{
		Type:        "solid",
		Text:        bottomButtonText,
		ClickAction: custom_alert_click_action,
	}

	if tapEname != nil {
		footerButtonEvents := sushi.NewClevertapEvents()
		footerButtonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, slotFooterPayload, footerButtonEvents)
		button.AddClevertapTrackingItem(trackItem)
	}

	button.AddJumboTrackingItem(jumboTrackItem)

	change_bottom_button := &sushi.ChangeBottomButton{
		ButtonId: "bottom_button_id2",
		Button:   button,
	}

	click_action, _ := sushi.NewTextClickAction("change_bottom_button")
	click_action.ChangeBottomButton = change_bottom_button

	return click_action
}

func (s *SlotPageTemplate) GetAllowedSlotClickAction(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	response *bookingPB.BookingSlotsV3Res,
	itm *bookingPB.BookingSlot,
	dayInterval int32,
	sessionType int32,
	dismissType bool,
) *sushi.ClickAction {

	out := map[string]interface{}{
		"fs_id":                     response.FsId,
		"slot_id":                   itm.SlotId,
		"booking_time":              itm.SlotDatetime.Seconds,
		"booking_users":             req.BookingUsers,
		"product_arena_category_id": sessionType,
		"source":                    req.Source,
	}

	payload, err := json.Marshal(out)
	if err != nil {
		log.Println("error in marshalling the payload data")
	}
	var tapEname *sushi.EnameData
	slotFooterPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
	slotFooterPayload["slot_id"] = itm.SlotId
	slotFooterPayload["day"] = dayInterval
	slotFooterPayload["session_type"] = sessionType
	slotFooterPayload["source"] = req.Source
	slotFooterPayload["product_arena_category_id"] = sessionType

	var jumboTrackItem *jumbo.Item

	change_bottom_button_click_action := sushi.GetClickAction()
	var buttonText string
	if s.PageData.FetchSlotsForSports {
		payload := structs.GetCentresForSlotBookingReq{
			SportId:                req.SportId,
			SlotId:                 itm.SlotId,
			BookingDate:            itm.SlotDatetime.Seconds,
			Source:                 req.Source,
			BottomSheet:            req.BottomSheet,
			BookingUsers:           req.BookingUsers,
			ProductArenaCategoryId: sessionType,
		}

		params, _ := json.Marshal(payload)

		var deep_link_url string
		if s.PageData.IsTrialUser && req.BottomSheet != 1 {
			deep_link_url = util.GetTrialCentersPageDeeplink()
		} else {
			if req.BottomSheet == 1 {
				deep_link_url = util.GetCentersDeeplink()
			} else {
				deep_link_url = util.GetFullPageCentersDeeplink()
			}
		}
		deep_link := &sushi.Deeplink{
			URL:            deep_link_url,
			PostbackParams: string(params),
		}

		if dismissType {
			dismissPage := &sushi.DismissPage{
				Type:     sushi.ClickActionDeeplink,
				Deeplink: deep_link,
			}
			change_bottom_button_click_action.Type = sushi.ClickActionDismiss
			change_bottom_button_click_action.DismissPage = dismissPage
		} else {
			change_bottom_button_click_action.SetDeeplink(deep_link)
		}

		buttonText = "Proceed to select center"

		slotFooterPayload["is_trial"] = 1
		tapEname = &sushi.EnameData{
			Ename: "proceed_to_select_center_click",
		}

		jumboTrackItem = s.getSelectCenterJumboTrackItem(ctx, req.Source, itm, sessionType, dayInterval, response)
	} else if s.PageData.FetchSlotsForFacilitySports {
		loggedInUser := util.GetUserIDFromContext(ctx)
		if loggedInUser > 0 {
			api_call_multi_action := &sushi.APICallAction{
				RequestType: sushi.POSTRequestType,
				URL:         "v2/booking/publish",
				Body:        string(payload),
			}

			status := s.PageData.SubscriptionDetails.SubscriptionStatus
			tomorrowSubscription := s.PageData.SubscriptionDetails.TomorrowParentSubscription || s.PageData.SubscriptionDetails.TomorrowChildSubscription
			switch status {
			case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
				if len(req.BookingUsers) > 1 {
					if sessionType == sessionTypeGuided {
						buttonText = fmt.Sprintf("Book guided slot for %d people", len(req.BookingUsers))
					} else if sessionType == sessionTypeSkillup {
						buttonText = fmt.Sprintf("Book skill up slot for %d people", len(req.BookingUsers))
					} else {
						buttonText = fmt.Sprintf("Book slot for %d people", len(req.BookingUsers))
					}
				} else {
					if sessionType == sessionTypeGuided {
						buttonText = "Book guided slot"
					} else if sessionType == sessionTypeSkillup {
						buttonText = "Book skill up slot"
					} else {
						buttonText = "Book selected slot"
					}
				}
				change_bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
				slotFooterPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				if s.PageData.ContainsGuestUsers {
					slotFooterPayload["contains_guests"] = 1
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
				if tomorrowSubscription {
					if len(req.BookingUsers) > 1 {
						if sessionType == sessionTypeGuided {
							buttonText = fmt.Sprintf("Book guided slot for %d people", len(req.BookingUsers))
						} else if sessionType == sessionTypeSkillup {
							buttonText = fmt.Sprintf("Book skill up slot for %d people", len(req.BookingUsers))
						} else {
							buttonText = fmt.Sprintf("Book slot for %d people", len(req.BookingUsers))
						}
					} else {
						if sessionType == sessionTypeGuided {
							buttonText = "Book guided slot"
						} else if sessionType == sessionTypeSkillup {
							buttonText = "Book skill up slot"
						} else {
							buttonText = "Book selected slot"
						}
					}
					change_bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
					change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
					slotFooterPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
					tapEname = &sushi.EnameData{
						Ename: "confirm_booking",
					}
				} else {
					buttonText = "return to home page"
					deep_link := &sushi.Deeplink{
						URL: util.GetHomeDeeplink(),
					}
					change_bottom_button_click_action.SetDeeplink(deep_link)
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
				buttonText = "Book free trial slot"
				change_bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
				slotFooterPayload["membership"] = "TAKE_TRIAL"
				slotFooterPayload["is_trial"] = 1
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				break

			case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
				fallthrough
			case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
				buttonText = "Buy membership to proceed"
				buy_for_me_deep_link := &sushi.Deeplink{
					URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID), //sending to purchase landing page till dec 2021
					//URL: util.GetBuyingForMeDeeplink(s.PageData.SubscriptionDetails.ProductId, false),
				}
				change_bottom_button_click_action.SetDeeplink(buy_for_me_deep_link)
				break
			}

			jumboTrackItem = s.getSelectCenterJumboTrackItem(ctx, req.Source, itm, sessionType, dayInterval, response)
		} else {
			auth_title, _ := sushi.NewTextSnippet("Please login to book your free trial. Enter your phone number to login using OTP.")
			buttonText = "Book free trial slot"
			payload := map[string]interface{}{
				"post_action":  "publish_booking",
				"fs_id":        response.FsId,
				"booking_time": itm.SlotDatetime.Seconds,
				"slot_id":      itm.SlotId,
				"source":       req.Source,
			}

			postback_params, _ := json.Marshal(payload)
			auth := &sushi.Auth{
				Title:          auth_title,
				PostbackParams: string(postback_params),
				Source:         "slots",
			}
			change_bottom_button_click_action.SetAuth(auth)
		}
	}

	if len(buttonText) == 0 {
		buttonText = "Book selected slot"
	}

	button := &sushi.Button{
		Type:        "solid",
		Text:        buttonText,
		ClickAction: change_bottom_button_click_action,
	}

	if tapEname != nil {
		footerButtonEvents := sushi.NewClevertapEvents()
		footerButtonEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, slotFooterPayload, footerButtonEvents)
		button.AddClevertapTrackingItem(trackItem)
	}

	button.AddJumboTrackingItem(jumboTrackItem)

	change_bottom_button := &sushi.ChangeBottomButton{
		ButtonId: "bottom_button_id2",
		Button:   button,
	}

	click_action, _ := sushi.NewTextClickAction("change_bottom_button")
	click_action.ChangeBottomButton = change_bottom_button

	return click_action
}

func (s *SlotPageTemplate) getSelectCenterJumboTrackItem(
	ctx context.Context,
	source string,
	itm *bookingPB.BookingSlot,
	productArenaCategoryID int32,
	dayInterval int32,
	res *bookingPB.BookingSlotsV3Res) *jumbo.Item {

	payload := make(map[string]interface{})

	payload["source_page"] = source

	if s.PageData.IsTrialUser {
		payload["is_trial"] = true
	}
	payload["selected_product_arena_category_id"] = productArenaCategoryID
	payload["selected_slot_tab_index"] = dayInterval
	payload["slot_id"] = itm.SlotId
	payload["facility_id"] = res.FacilityId
	payload["sport_id"] = res.SportId

	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.SlotSelectCenterButtonClick)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (s *SlotPageTemplate) GetMultipleCoachingSlotClickAction(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	response *bookingPB.BookingSlotsV3Res,
	itm *bookingPB.BookingSlot,
	dayInterval int32,
	slotTypes []*bookingPB.SlotTypeInfo,
) *sushi.ClickAction {

	var items []sushi.ImageTextSnippetType33SnippetItem
	for _, i := range slotTypes {
		sessionType := i.ProductArenaCategoryId
		val := sessionTypes[sessionType]

		item_left_image_url := val.Icon
		item_left_image, _ := sushi.NewImage(item_left_image_url)
		item_left_image.SetAspectRatio(1)
		item_left_image.SetType(sushi.ImageTypeCircle)
		item_left_image.SetHeight(42)

		item_left_image_bg_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		item_left_image.SetColor(item_left_image_bg_color)

		item_title, _ := sushi.NewTextSnippet(val.Name)
		item_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		item_title.SetColor(item_title_color)
		item_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		item_title.SetFont(item_title_font)

		description := strings.Join(val.Description, ". ")
		item_subtitle1, _ := sushi.NewTextSnippet(description)
		item_subtitle1_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		item_subtitle1.SetColor(item_subtitle1_color)
		item_subtitle1_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		item_subtitle1.SetFont(item_subtitle1_font)

		item := &sushi.ImageTextSnippetType33SnippetItem{
			Id:        val.Id,
			LeftImage: item_left_image,
			Title:     item_title,
			SubTitle1: item_subtitle1,
		}

		if i.SoldOut {

			// gray out image
			filter := &sushi.Filter{
				Type:  sushi.FilterTypeGrayScale,
				Value: 0,
			}
			item_left_image.SetFilter(filter)

			image_bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
			item_left_image.SetColor(image_bg_color)

			// gray out title and subtitle
			disabled_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
			item_title.SetColor(disabled_color)
			item_subtitle1.SetColor(disabled_color)

			colorTag, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
			fontTag, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			titleTag, _ := sushi.NewTextSnippet("SOLD OUT")
			titleTag.SetColor(colorTag)
			titleTag.SetFont(fontTag)

			bgColorTag, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
			tag := &sushi.Tag{
				Title:   titleTag,
				BgColor: bgColorTag,
			}
			item.Tag1 = tag

			bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
			item.BgColor = bgColor
			item.IsInactive = true

			// ideally there's no need to add click action but this is for backward compatibility on android
			buttonBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			button := &sushi.Button{
				Type:             "solid",
				Text:             "Sold Out",
				IsActionDisabled: 1,
				BgColor:          buttonBgColor,
			}
			change_bottom_button := &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id2",
				Button:   button,
			}

			click_action, _ := sushi.NewTextClickAction("change_bottom_button")
			click_action.ChangeBottomButton = change_bottom_button
			item.ClickAction = click_action
		} else {

			var subtitle2_text string
			if i.RemainingCapacity == 1 {
				subtitle2_text = fmt.Sprintf("%d spot left", i.RemainingCapacity)
			} else {
				subtitle2_text = fmt.Sprintf("%d spots left", i.RemainingCapacity)
			}

			subtitleColorType := sushi.ColorTypeOrange
			if featuresupport.SupportsNewColor(ctx) {
				subtitleColorType = sushi.ALERT_LIGHT_THEME
			}

			subtitle2_color, _ := sushi.NewColor(subtitleColorType, sushi.ColorTint600)
			subtitle2_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)

			prefix_icon_color, _ := sushi.NewColor(subtitleColorType, sushi.ColorTint400)
			prefix_icon, _ := sushi.NewIcon(sushi.ThunderIcon, prefix_icon_color)

			if s.PageData.FetchSlotsForFacilitySports && (itm.ShowRemainingSpots || sessionType == sessionTypeSkillup) {
				if featuresupport.SupportsRemainingSpotsFeature(ctx) {
					subtitle2, _ := sushi.NewTextSnippet(subtitle2_text)
					subtitle2.SetColor(subtitle2_color)
					subtitle2.SetFont(subtitle2_font)
					subtitle2.SetPrefixIcon(prefix_icon)

					item.SubTitle2 = subtitle2
				} else {
					bottom_button := &sushi.Button{
						Type:             sushi.ButtontypeText,
						Text:             subtitle2_text,
						Color:            subtitle2_color,
						Font:             subtitle2_font,
						IsActionDisabled: 1,
						PrefixIcon:       prefix_icon,
					}

					item.BottomButton = bottom_button
				}
			}
			item_click_action := s.GetAllowedSlotClickAction(ctx, req, response, itm, dayInterval, sessionType, true)
			item.ClickAction = item_click_action
		}
		items = append(items, *item)
	}

	title, _ := sushi.NewTextSnippet("Choose your session type")
	title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(title_color)
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(title_font)

	image_text_snippet_type_33 := &sushi.ImageTextSnippetType33Snippet{
		Items: &items,
	}

	layoutConfig1 := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	sectionHeaderType1Snippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}

	custom_selection_popup_item1 := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:              layoutConfig1,
		SectionHeaderType1Snippet: sectionHeaderType1Snippet,
	}

	layoutConfig2 := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType33,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	custom_selection_popup_item2 := &sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:           layoutConfig2,
		ImageTextSnippetType33: image_text_snippet_type_33,
	}
	custom_selection_popup_items := []*sushi.CustomTextSnippetTypeLayout{custom_selection_popup_item1, custom_selection_popup_item2}

	footerButtonBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	footerButtonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	footerButtonDataItem := sushi.FooterSnippetType2ButtonItem{
		Id:               "bottom_button_id2",
		Type:             sushi.FooterButtonTypeSolid,
		Text:             "Select slot type",
		IsActionDisabled: 1,
		BgColor:          footerButtonBgColor,
		Font:             footerButtonFont,
	}

	footerButtonDataItems := []sushi.FooterSnippetType2ButtonItem{footerButtonDataItem}
	footerButtonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &footerButtonDataItems,
	}

	footerLayoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footerSnippetType2 := &sushi.FooterSnippetType2Snippet{
		ButtonData: footerButtonData,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       footerLayoutConfig,
		FooterSnippetType2: footerSnippetType2,
	}
	custom_selection_popup := &sushi.CustomSelectionPopup{
		Results: custom_selection_popup_items,
		Footer:  footer,
	}

	click_action, _ := sushi.NewTextClickAction("custom_selection_popup")
	click_action.CustomSelectionPopup = custom_selection_popup
	return click_action
}

func (s *SlotPageTemplate) GetSoldOutSlotClickAction(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	response *bookingPB.BookingSlotsV3Res,
	itm *bookingPB.BookingSlot,
) *sushi.ClickAction {

	var bookingUsers []structs.BookingUser
	for _, itm := range req.BookingUsers {
		obj := structs.BookingUser{
			UserID: itm.UserID,
		}
		bookingUsers = append(bookingUsers, obj)
	}

	payload := structs.GetCentresForSlotBookingReq{
		SportId:      response.SportId,
		SlotId:       itm.SlotId,
		BookingDate:  itm.SlotDatetime.Seconds,
		BottomSheet:  req.BottomSheet,
		BookingUsers: req.BookingUsers,
		Source:       req.Source,
	}
	params, _ := json.Marshal(payload)
	deep_link := &sushi.Deeplink{
		URL:            util.GetCentersDeeplink(),
		PostbackParams: string(params),
	}

	click_action := sushi.GetClickAction()
	click_action.SetDeeplink(deep_link)
	return click_action
}

func (s *SlotPageTemplate) SetUserSubscriptionStatus(ctx context.Context, productCategoryId int32) *productPB.GetUserSubscriptionStatusResponse {
	if s.PageData.SubscriptionDetails != nil {
		return s.PageData.SubscriptionDetails
	}
	productClient := util.GetProductClient()
	subReqData := &productPB.GetUserSubscriptionStatusRequest{
		UserId:                    util.GetUserIDFromContext(ctx),
		ProductSuggestionRequired: true,
		CheckChildSub:             true,
		ProductCategoryId:         productCategoryId,
	}
	subResponse, err := productClient.GetUserSubscriptionStatus(ctx, subReqData)
	if err != nil {
		log.Printf("Api: slots get, function: GetUserSubscriptionStatus, Error: %v", err)
		return nil
	}
	if subResponse.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
		s.PageData.IsPremiumUser = true
	}

	if subResponse.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL {
		s.PageData.IsTrialUser = true
	}

	s.PageData.SubscriptionDetails = subResponse
	return s.PageData.SubscriptionDetails
}

func (s *SlotPageTemplate) SetUserTrialDetails(ctx context.Context) *userPB.TrialDetailsResponse {
	if s.PageData.TrialDetails != nil {
		return s.PageData.TrialDetails
	}
	userClient := util.GetUserServiceClient()
	userId := util.GetUserIDFromContext(ctx)

	trialDetails, err := userClient.GetTrialDetails(ctx, &userPB.UserRequest{UserId: userId})
	if err != nil {
		log.Printf("Api: slots get, function: GetUserTrialDetails, Error: %v", err)
		return nil
	}
	s.PageData.TrialDetails = trialDetails
	return s.PageData.TrialDetails
}

func (s *SlotPageTemplate) SetPageTracking(ctx context.Context, source string, res *bookingPB.BookingSlotsV3Res) {
	payload := s.GetClevertapTrackingDefaultPayload(ctx, res)
	landingEname := &sushi.EnameData{
		Ename: "slot_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}

	jumboPayload := make(map[string]interface{})
	jumboPayload["source_page"] = source

	if s.PageData.IsTrialUser {
		jumboPayload["is_trial"] = true

		if s.PageData.TrialDetails != nil {
			jumboPayload["user_trial_available_count"] = s.PageData.TrialDetails.TrialDetails.TrialSessionsLeft
		}
	}

	jumboPayload["total_slots"] = s.TotalVisibleSlots
	jumboPayload["total_open_slots"] = s.TotalOpenSlots
	jumboPayload["selected_slot_tab_index"] = 0

	if s.InfoContainer != nil {
		jumboPayload["slot_alert"] = s.InfoContainer.Title
	}

	jumboPayload["open_slot_ids"] = s.OpenSlotIDs
	jumboPayload["product_arena_category_ids"] = s.PageProductArenaCategoryIDs
	jumboPayload["facility_id"] = res.FacilityId
	jumboPayload["sport_id"] = res.SportId

	jumboEvents := jumbo.NewEvents()
	jumboImpressionEvent := jumbo.GetEventNameObject(jumbo.SlotsPageView)
	jumboEvents.SetImpression(jumboImpressionEvent)
	jumboEvent := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	jumboEvent.SetPayload(jumboPayload)
	jumboEvent.SetEventNames(jumboEvents)

	s.JumboTracking = []*jumbo.Item{jumboEvent}
}

func (s *SlotPageTemplate) SetPageProductArenaCategoryIDs(ctx context.Context, res *bookingPB.BookingSlotsV3Res) {
	if res.SkillupSessionAvailable {
		s.PageProductArenaCategoryIDs = append(s.PageProductArenaCategoryIDs, sessionTypeSkillup)
	}

	if res.GuidedSessionAvailable {
		s.PageProductArenaCategoryIDs = append(s.PageProductArenaCategoryIDs, sessionTypeGuided)
	}

	if res.BuddyHourSessionAvailable {
		s.PageProductArenaCategoryIDs = append(s.PageProductArenaCategoryIDs, sessionTypeBuddyHour)
	}
}

func (s *SlotPageTemplate) GetClevertapTrackingDefaultPayload(ctx context.Context, res *bookingPB.BookingSlotsV3Res) map[string]interface{} {
	tomorrowSubscription := s.PageData.SubscriptionDetails.TomorrowParentSubscription || s.PageData.SubscriptionDetails.TomorrowChildSubscription
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["sport_id"] = res.SportId
	payload["facility_id"] = res.FacilityId

	if s.PageData.SubscriptionDetails != nil {
		switch s.PageData.SubscriptionDetails.SubscriptionStatus {
		case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
			payload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"

		case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
			payload["membership"] = "FUTURE_PREMIUM_SUBSCRIPTION"
			if tomorrowSubscription {
				payload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
			}

		case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
			payload["membership"] = "TAKE_TRIAL"

		case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
			payload["membership"] = "TRIAL_SUBSCRIPTION_ACTIVE"

		case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
			payload["membership"] = "ALL_SUBSCRIPTION_EXPIRED"
		}
	}
	return payload
}

func (s *SlotPageTemplate) checkInfoContainerShow(bookingSlotResponse *bookingPB.BookingSlotsV3Res) bool {
	if Contains([]int32{111, 85, 124, 91, 88}, bookingSlotResponse.FsId) {
		return true
	}

	if len(bookingSlotResponse.BookingSlots) == 0 {
		return false
	}
	if bookingSlotResponse.FsId == 166 {
		return true
	}
	if bookingSlotResponse.HasOpenCourt != int32(1) {
		return false
	}
	showTimeObj := util.GetMonsoonAlertLastDate()
	if showTimeObj.Before(time.Now()) {
		return false
	}
	return true
}

func (s *SlotPageTemplate) SetSessionsInfoSection(ctx context.Context, response *bookingPB.BookingSlotsV3Res) {

	var sessionsInfo []slotModel.SessionsInfo
	if response.GuidedSessionAvailable {
		guidedSessionInfo := s.GetSessionInfoSnippet(ctx, sessionTypeGuided, response)
		sessionsInfo = append(sessionsInfo, *guidedSessionInfo)
	}

	if response.SkillupSessionAvailable {
		skillSessionInfo := s.GetSessionInfoSnippet(ctx, sessionTypeSkillup, response)
		sessionsInfo = append(sessionsInfo, *skillSessionInfo)
	}

	if response.BuddyHourSessionAvailable {
		buddyHourSessionInfo := s.GetSessionInfoSnippet(ctx, sessionTypeBuddyHour, response)
		sessionsInfo = append(sessionsInfo, *buddyHourSessionInfo)
	}
	s.SessionsInfo = &sessionsInfo
}

func (s *SlotPageTemplate) GetSessionInfoSnippet(ctx context.Context, sessionType int32, response *bookingPB.BookingSlotsV3Res) *slotModel.SessionsInfo {
	sessionInfo := &slotModel.SessionsInfo{}

	var session SessionInfo
	if sessionType == sessionTypeBuddyHour {
		if response.SportId == common.SWIMMING_SPORT_ID {
			session = buddyHourSessionTypes[common.SWIMMING_SPORT_ID]
		} else if response.SportId == common.BADMINTON_SPORT_ID {
			session = buddyHourSessionTypes[common.BADMINTON_SPORT_ID]
		}
	} else {
		session = sessionTypes[sessionType]
	}

	imageLink := session.Icon
	titleText := session.Name
	buttonTitleText := strings.Join(session.Description, ". ")

	image, _ := sushi.NewImage(imageLink)
	image.SetHeight(24)
	image.SetWidth(24)
	image.SetType(sushi.ImageTypeCircle)

	title, _ := sushi.NewTextSnippet(titleText)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetFont(titleFont)

	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText("what’s this?")
	buttonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
	button.SetFont(buttonFont)

	buttonTitle, _ := sushi.NewTextSnippet(buttonTitleText)
	buttonTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	buttonTitle.SetColor(buttonTitleColor)
	buttonTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	buttonTitle.SetFont(buttonTitleFont)

	buttonSubtitle, _ := sushi.NewTextSnippet("OK")
	buttonSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	buttonSubtitle.SetColor(buttonSubtitleColor)
	buttonSubtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	buttonSubtitle.SetFont(buttonSubtitleFont)

	buttonBgColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)

	showTooltip := &sushi.ShowTooltip{
		Title:    buttonTitle,
		Subtitle: buttonSubtitle,
		BgColor:  buttonBgColor,
	}
	clickAction := sushi.GetClickAction()
	clickAction.SetShowTooltip(showTooltip)

	var tapEname *sushi.EnameData
	tapEname = &sushi.EnameData{
		Ename: "legend_click",
	}
	payload := s.GetClevertapTrackingDefaultPayload(ctx, response)
	payload["session_type"] = sessionType
	events := sushi.NewClevertapEvents()
	events.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, events)

	sessionInfo.Image = image
	sessionInfo.Title = title
	sessionInfo.BottomButton = button
	sessionInfo.ClickAction = clickAction
	sessionInfo.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	return sessionInfo
}
