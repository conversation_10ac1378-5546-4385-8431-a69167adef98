package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	centreModel "bitbucket.org/jogocoin/go_api/api/models/booking/centres"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	"bitbucket.org/jogocoin/go_api/api/render"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// AvailableAcademyCentreTemplate represents the response structure of available centres page
type AvailableAcademyCentreTemplate struct {
	Header            *centreModel.Header                  `json:"header,omitempty"`
	CustomHeader      *centreModel.CustomHeader            `json:"custom_header,omitempty"`
	ProgressBarData   *centreModel.ProgressBarStruct       `json:"progress_bar_data,omitempty"`
	Results           *[]sushi.CustomTextSnippetTypeLayout `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout      `json:"footer"`
	PageData          CentrePageData                       `json:"-"`
	ClevertapTracking []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
}

func GetAcademyCentresForSlotBookingC(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.GetCentresForSlotBookingReq
	ctx := util.PrepareGRPCMetaData(c)

	if util.GetAppTypeFromContext(ctx) == featuresupport.Android {
		jsonV2 := c.MustGet("jsonData").(structs.GetCentresForSlotBookingReqV2)

		var bookingUsers []structs.BookingUser
		for _, elem := range jsonV2.BookingUsers {
			bookingUser := structs.BookingUser{
				UserID: int32(elem.UserID),
			}
			bookingUsers = append(bookingUsers, bookingUser)
		}

		json = structs.GetCentresForSlotBookingReq{
			SportId:                jsonV2.SportId,
			SlotId:                 jsonV2.SlotId,
			BookingDate:            jsonV2.BookingDate,
			BottomSheet:            jsonV2.BottomSheet,
			BookingUsers:           bookingUsers,
			Source:                 jsonV2.Source,
			ProductArenaCategoryId: jsonV2.ProductArenaCategoryId,
		}
	} else {
		json = c.MustGet("jsonData").(structs.GetCentresForSlotBookingReq)
	}

	var bookingCount int32
	var userIds []int32
	for _, data := range json.BookingUsers {
		if data.UserID > 0 {
			userIds = append(userIds, data.UserID)
		}
	}
	if len(userIds) > 0 {
		bookingCount = int32(len(userIds))
	}

	reqData := &bookingPB.CentresForSlotBookingReq{
		SportId:      json.SportId,
		SlotId:       json.SlotId,
		BookingCount: bookingCount,
		BookingDate:  json.BookingDate,
		UserIds:      userIds,
	}

	centreForSlotBooking, err := cl.GetAcademyCentresForSlotBooking(ctx, reqData)
	if err != nil {
		log.Println("Could not fetch academy trial center details ---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if centreForSlotBooking.Status != nil {
		if centreForSlotBooking.Status.Status == common.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, model.StatusFailure(centreForSlotBooking.Status.Message))
			return
		}
		if centreForSlotBooking.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, model.StatusFailure(centreForSlotBooking.Status.Message))
			return
		}
	}

	template := AvailableAcademyCentreTemplate{}
	if json.BottomSheet != 1 {
		template.SetHeaderSection(ctx)
		template.SetProgressBarSection(ctx)
	} else {
		template.SetCustomHeaderSection(centreForSlotBooking, bookingCount)
	}
	template.ResultSection(ctx, &json, centreForSlotBooking)
	template.SetFooterSection(&json, centreForSlotBooking)
	template.SetPageTracking(ctx, centreForSlotBooking)

	render.Render(c, gin.H{"payload": template}, "index.html")
}

func (s *AvailableAcademyCentreTemplate) SetHeaderSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Book free trial class")
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(title_font)

	title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetColor(title_color)

	trialText := "1 free trial available for each sport"
	facilityClient := util.GetFacilitySportClient()
	response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Printf("Error in getting city based sports %v", err)
		return
	}
	if len(response.AcademySportIds) == 1 {
		trialText = "One free trial available"
	}
	tag, _ := sushi.NewTag(trialText)
	tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
	tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
	if featuresupport.SupportsNewColor(ctx) {
		tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
		tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
	}

	tag.Title.SetFont(tagFont)
	tag.Title.SetColor(tagColor)
	tag.SetBgColor(tagBgColor)
	tag.SetBorderColor(tagBorderColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &centreModel.Header{
		Title:   title,
		Tag:     tag,
		BgColor: bgColor,
	}
	s.Header = header
}

func (s *AvailableAcademyCentreTemplate) SetProgressBarSection(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	colorType := sushi.ColorTypeTeal
	if featuresupport.SupportsNewColor(ctx) {
		colorType = sushi.ColorTypeCyan
	}
	progressbarColor := &[]sushi.Color{
		sushi.Color{
			Type: colorType,
			Tint: sushi.ColorTint500,
		},
	}

	progressBarDetails := &centreModel.ProgressBarStruct{
		Progress:       100,
		BgColor:        bgColor,
		ProgressColors: progressbarColor,
	}
	s.ProgressBarData = progressBarDetails
}

func (s *AvailableAcademyCentreTemplate) SetCustomHeaderSection(response *bookingPB.CentresForSlotBookingRes, bookingCount int32) {
	items := make([]sushi.TextSnippet, 0)

	sport_title, _ := sushi.NewTextSnippet(response.SportDetails.SportName)
	items = append(items, *sport_title)

	slot_title, _ := sushi.NewTextSnippet(response.SlotDetails.Timing)
	items = append(items, *slot_title)

	booking_count_title, _ := sushi.NewTextSnippet(fmt.Sprintf("%d person", bookingCount))
	items = append(items, *booking_count_title)

	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)

	leftImage, _ := sushi.NewImage(response.SportDetails.Icon)
	customHeaderType2 := &centreModel.CustomHeaderType2{
		Items:     &items,
		Color:     color,
		Font:      font,
		BgColor:   bgColor,
		LeftImage: leftImage,
	}

	customHeaderDetails := &centreModel.CustomHeader{
		Type:              centreModel.CustomHeaderType2Val,
		CustomHeaderType2: customHeaderType2,
	}
	s.CustomHeader = customHeaderDetails
}

func (s *AvailableAcademyCentreTemplate) SetFooterSection(req *structs.GetCentresForSlotBookingReq, response *bookingPB.CentresForSlotBookingRes) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	var buttonWeight []int32

	var buttonItem2Text string
	buttonItem2Text = "Select a center to proceed"
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItem2BgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)

	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Type:             sushi.FooterButtonTypeSolid,
		Text:             buttonItem2Text,
		Font:             buttonItem2Font,
		Id:               "bottom_button_id2",
		BgColor:          buttonItem2BgColor,
		IsActionDisabled: 1,
	}
	if response != nil && response.SportDetails != nil {
		clickAction := s.SetConfirmBookingBottomSheet(req, response)
		buttonItem2.ClickAction = clickAction
	}
	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData:   buttonData,
		ButtonWeight: buttonWeight,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func (s *AvailableAcademyCentreTemplate) SetConfirmBookingBottomSheet(req *structs.GetCentresForSlotBookingReq, res *bookingPB.CentresForSlotBookingRes) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()

	title, _ := sushi.NewTextSnippet("Confirm free trial booking")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetColor(color)
	title.SetFont(font)

	bottomSheetHeader := &sushi.ConfirmBottomSheetHeader{
		Title: title,
	}

	items := make([]*sushi.OpenConfirmationBottomSheetResult, 0)

	title, _ = sushi.NewTextSnippet(res.SportDetails.SportName)
	font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(color)
	title.SetFont(font)

	bookingTimeObj := time.Unix(req.BookingDate, 0)
	_, bookingMonth, bookingDay := bookingTimeObj.Date()
	day := "Tomorrow"
	if bookingDay == time.Now().Day() {
		day = "Today"
	}
	bookingMonthStr := bookingMonth.String()
	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%s, %d %s at %s", day, bookingDay, bookingMonthStr[:3], bookingTimeObj.Format("03:04 PM")))
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle.SetColor(color)
	subtitle.SetFont(font)

	subtitle1, _ := sushi.NewTextSnippet("Dummy name")
	subtitle1.SetColor(color)
	subtitle1.SetFont(font)

	image, _ := sushi.NewImage(res.SportDetails.Icon)
	image.SetHeight(60)
	image.SetWidth(60)
	image.SetType(sushi.ImageTypeCircle)
	image.SetAspectRatio(float32(1))
	color, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
	image.SetColor(color)

	snippetType13 := &sushi.FitsoImageTextSnippetType13Snippet{
		Title:     title,
		Subtitle:  subtitle,
		Subtitle1: subtitle1,
		Image:     image,
	}

	title, _ = sushi.NewTextSnippet("Monsoon Alert")
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(color)
	title.SetFont(font)

	subtitle, _ = sushi.NewTextSnippet("Please note that this is an open facility. In case it rains, the center may be closed.")
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle.SetColor(color)
	subtitle.SetFont(font)

	image, _ = sushi.NewImage(util.GetCDNLink("uploads/Group240401627311075.png"))
	bgcolor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	bordercolor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)

	topContainer := &sushi.FitsoImageTextSnippetType13TopContainer{
		Title:        title,
		Subtitle:     subtitle,
		Image:        image,
		BgColor:      bgcolor,
		BorderColor:  bordercolor,
		CornerRadius: 12,
	}
	snippetType13.TopContainer = topContainer
	if res.DisplayNames == "" {
		res.DisplayNames = "Booking a session for {grey-800|<semibold-200| - >}"
	}
	title, _ = sushi.NewTextSnippet(res.DisplayNames)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	title.SetColor(color)
	title.SetFont(font)
	title.SetIsMarkdown(1)

	bgcolor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)

	bottomContainer := &sushi.FitsoImageTextSnippetType13BottomContainer{
		Title:   title,
		BgColor: bgcolor,
	}
	snippetType13.BottomContainer = bottomContainer

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType13,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	fitsoImageSnippet13 := &sushi.OpenConfirmationBottomSheetResult{
		LayoutConfig:                layoutConfig,
		FitsoImageTextSnippetType13: snippetType13,
	}
	items = append(items, fitsoImageSnippet13)

	layoutConfig = &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}

	title, _ = sushi.NewTextSnippet("You can purchase the membership at the center post your assessment.")
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	color, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint600)
	title.SetColor(color)
	title.SetFont(font)

	headerSnippet1 := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}

	sectionHeader1 := &sushi.OpenConfirmationBottomSheetResult{
		LayoutConfig:       layoutConfig,
		HeaderSnippetType1: headerSnippet1,
	}
	items = append(items, sectionHeader1)

	confirmBootomSheet := &sushi.OpenConfirmationBottomSheet{
		Header:  bottomSheetHeader,
		Results: items,
	}
	clickAction.SetConfirmationBottomSheet(confirmBootomSheet)
	return clickAction
}

func (s *AvailableAcademyCentreTemplate) ResultSection(ctx context.Context, req *structs.GetCentresForSlotBookingReq, response *bookingPB.CentresForSlotBookingRes) {
	var items []sushi.CustomTextSnippetTypeLayout

	if response.CentresAvailable == true {
		for i, elem := range response.Centres {
			// setting facility title
			facility_title, _ := sushi.NewTextSnippet(elem.DisplayName)
			facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			facility_title.SetFont(facility_title_font)
			facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			facility_title.SetColor(facility_title_color)

			// setting facility subtitle
			facility_subtitle, _ := sushi.NewTextSnippet(elem.SubzoneName)
			facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			facility_subtitle.SetFont(facility_subtitle_font)
			facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			facility_subtitle.SetColor(facility_subtitle_color)

			// setting facility tag
			facility_distance_tag_title := "-"
			if elem.Distance != 0 {
				facility_distance_tag_title = fmt.Sprintf("%.1f km", elem.Distance)
			}
			facility_tag_title, _ := sushi.NewTextSnippet(facility_distance_tag_title)
			facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
			facility_tag_title.SetFont(facility_tag_title_font)
			facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			facility_tag_title.SetColor(facility_tag_title_color)

			// setting facility tag bg color
			facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

			tag := &sushi.Tag{
				Title:   facility_tag_title,
				BgColor: facility_tag_bgcolor,
			}

			// setting facility rating title
			facility_rating_title, _ := sushi.NewTextSnippet(elem.Rating)
			facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			facility_rating_title.SetFont(facility_rating_title_font)
			facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
			facility_rating_title.SetColor(facility_rating_title_color)
			facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
			facility_rating_title.SetPrefixIcon(ratingIcon)

			// setting facility rating bg color
			facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if featuresupport.SupportsNewColor(ctx) {
				facility_rating_bgcolor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			}

			ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

			tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
			if sharedFunc.ContainsString(tags, elem.Tag) {
				ratingTitle, _ := sushi.NewTextSnippet(elem.Tag)

				if !featuresupport.SupportsNewColor(ctx) {
					color := sushi.ColorTypeBlue
					if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
						color = sushi.ColorTypeTeal
					} else if elem.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
						color = sushi.ColorTypeOrange
					}
					ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else {
					if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
						ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
						ratingSnippetBlockItem.BgColor = ratingBgColor
					} else if elem.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
						ratingBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
						ratingSnippetBlockItem.BgColor = ratingBgColor
					} else {
						// gradient for new tag
						tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
						tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
						gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
						ratingSnippetBlockItem.Gradient = gradient
					}
				}

				ratingSnippetBlockItem.Title = ratingTitle
				ratingSnippetBlockItem.Size = sushi.RatingSize300

			} else {
				ratingSnippetBlockItem.Title = facility_rating_title
				ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
			}

			rating := ratingSnippetBlockItem

			facility_image := &sushi.Image{
				URL:         elem.DisplayPicture,
				AspectRatio: 1,
				Type:        sushi.ImageTypeRounded,
				Height:      64,
				Width:       64,
			}

			var tapEname *sushi.EnameData

			footerButtonPayload := make(map[string]interface{})
			footerButtonPayload["fs_id"] = elem.FsId
			footerButtonPayload["sport_id"] = req.SportId
			footerButtonPayload["facility_id"] = elem.FacilityId
			footerButtonPayload["slot_id"] = req.SlotId
			footerButtonPayload["booking_date"] = req.BookingDate

			button_text := "Book selected center"
			tapEname = &sushi.EnameData{
				Ename: "academy_confirm_booking",
			}

			button := &sushi.Button{
				Type: sushi.ButtonTypeSolid,
				Text: button_text,
			}

			if tapEname != nil {
				footerButtonEvents := sushi.NewClevertapEvents()
				footerButtonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, footerButtonPayload, footerButtonEvents)
				button.AddClevertapTrackingItem(trackItem)
			}

			contextualData := &sushi.ContextualData{
				FacilityInfoText:       elem.DisplayName,
				FacilityId:             elem.FacilityId,
				ShouldShowWeatherAlert: false,
			}
			showTimeObj := util.GetMonsoonAlertLastDate()
			if elem.HasOpenCourt == 1 && !showTimeObj.Before(time.Now()) {
				contextualData.ShouldShowWeatherAlert = true
			}

			layoutConfig1 := &sushi.LayoutConfig{
				SnippetType: sushi.FooterSnippetType2,
			}

			buttonItems := make([]sushi.FooterSnippetType2ButtonItem, 0)
			font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

			out := map[string]interface{}{
				"fs_id":                     elem.FsId,
				"slot_id":                   req.SlotId,
				"booking_time":              req.BookingDate,
				"booking_users":             req.BookingUsers,
				"product_arena_category_id": PlayArenaAcademySkillUp,
				"product_category_id":       common.AcademyCategoryID,
			}

			payload, err := json.Marshal(out)
			if err != nil {
				log.Println("error in marshaling the payload data")
			}

			api_call_multi_action := &sushi.APICallAction{
				RequestType: sushi.POSTRequestType,
				URL:         "v2/booking/publish",
				Body:        string(payload),
			}
			clickActionButton2 := sushi.GetClickAction()
			clickActionButton2.SetApiCallAction(api_call_multi_action)
			bookingTapEname := &sushi.EnameData{
				Ename: "academy_confirm_booking",
			}
			footerButtonEvent := sushi.NewClevertapEvents()
			footerButtonEvent.SetTap(bookingTapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, footerButtonPayload, footerButtonEvent)
			buttonItem2 := sushi.FooterSnippetType2ButtonItem{
				Type:              sushi.FooterButtonTypeSolid,
				Text:              "Confirm booking",
				Font:              font,
				ClickAction:       clickActionButton2,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			buttonItems = append(buttonItems, buttonItem2)

			buttonData := &sushi.FooterSnippetType2Button{
				Orientation: sushi.FooterButtonOrientationHorizontal,
				Items:       &buttonItems,
			}
			snippetFooter := &sushi.FooterSnippetType2Snippet{
				ButtonData: buttonData,
			}

			footer := &sushi.FooterSnippetType2Layout{
				LayoutConfig:       layoutConfig1,
				FooterSnippetType2: snippetFooter,
			}

			change_bottom_button := &sushi.UpdateBookingContextualDataButton{
				ButtonId:       "bottom_button_id2",
				Button:         button,
				ContextualData: contextualData,
				Footer:         footer,
			}

			clickActionButton := sushi.GetClickAction()
			clickActionButton.SetClickActionType("update_booking_confirmation_contexual_data")
			clickActionButton.UpdateBookingConfirmContextData = change_bottom_button

			facility_obj := sushi.V2ImageTextSnippetType31SnippetItem{
				Title:       facility_title,
				Subtitle:    facility_subtitle,
				Tag:         tag,
				Rating:      rating,
				Image:       facility_image,
				ClickAction: clickActionButton,
			}

			title, _ := sushi.NewTextSnippet("Select your center")
			snippet := &sushi.V2ImageTextSnippetType31Snippet{
				Items: &[]sushi.V2ImageTextSnippetType31SnippetItem{facility_obj},
				Id:    "lockdown",
			}

			if i == 0 {
				snippet.Title = title
			}

			layoutConfig := &sushi.LayoutConfig{
				SnippetType:  sushi.V2ImageTextSnippetType31,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
			}

			item := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:             layoutConfig,
				V2ImageTextSnippetType31: snippet,
			}

			items = append(items, *item)
		}
	} else {
		title, _ := sushi.NewTextSnippet("Sorry! You just missed our last available centre for this time slot. Please select another slot to make a booking.")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)

		title.SetColor(color)
		title.SetFont(font)
		title.SetAlignment(sushi.TextAlignmentCenter)
		title.SetNumberOfLines(3)

		empty_obj := sushi.V2ImageTextSnippetType35SnippetItem{
			Title: title,
		}
		snippet := &sushi.V2ImageTextSnippetType35Snippet{
			Items: &[]sushi.V2ImageTextSnippetType35SnippetItem{empty_obj},
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType35,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType35: snippet,
		}

		items = append(items, *item)
	}

	s.Results = &items
}

func (s *AvailableAcademyCentreTemplate) SetPageTracking(ctx context.Context, response *bookingPB.CentresForSlotBookingRes) {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	if response.Centres != nil && len(response.Centres) > 0 {
		payload["nearest_distance"] = response.Centres[0].Distance
		payload["total_centers"] = len(response.Centres)
	}

	landingEname := &sushi.EnameData{
		Ename: "academy_center_selection_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}
