// facilitySport controller

package bookingController

import (
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"

	model "bitbucket.org/jogocoin/go_api/api/models"
	authPB "bitbucket.org/jogocoin/go_api/api/proto/auth"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/structs"
	ptypes "github.com/golang/protobuf/ptypes"

	"bitbucket.org/jogocoin/go_api/api/server/routes/util"

	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/client/selector/shard"

	common "bitbucket.org/jogocoin/go_api/api/common"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	micro "github.com/micro/go-micro"
)

var (
	cl bookingPB.BookingService
)

func Ordinal(num int) string {
	var ordinalDictionary = map[int]string{
		0: "th",
		1: "st",
		2: "nd",
		3: "rd",
		4: "th",
		5: "th",
		6: "th",
		7: "th",
		8: "th",
		9: "th",
	}

	floatNum := math.Abs(float64(num))
	positiveNum := int(floatNum)

	if ((positiveNum % 100) >= 11) && ((positiveNum % 100) <= 13) {
		return "th"
	}
	return ordinalDictionary[positiveNum%10]
}

func GetBookingDetailsC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)

	loggedInUser := util.GetUserIDFromContext(ctx)

	headers := c.MustGet("requestHeaders").(structs.Headers)

	if loggedInUser == 0 && headers.AppType != common.USER_AUTH_WEB_APP_TYPE {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to view booking details."))
		return
	}

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	json := c.MustGet("jsonData").(structs.Booking)

	bookedUserId := loggedInUser

	bookingData := &bookingPB.Booking{
		BookingId:              json.BookingID,
		UserId:                 bookedUserId,
		SubscriptionId:         json.SubscriptionID,
		ProductId:              json.ProductID,
		AttendanceFlag:         json.AttendanceFlag,
		BookingCancelled:       json.BookingCancelled,
		RescheduledFrom:        json.RescheduledFrom,
		SessionCount:           json.SessionCount,
		SlotId:                 json.SlotID,
		PaId:                   json.PAID,
		ChargeLevied:           json.ChargeLevied,
		FsId:                   json.FSID,
		BookingType:            json.BookingType,
		BookingCapacity:        json.BookingCapacity,
		BookingStatus:          json.BookingStatus,
		Timeline:               json.Timeline,
		Start:                  json.Start,
		Count:                  json.Count,
		BookingReferenceNumber: json.BookingReferenceNumber,
		FsIds:                  json.FsIds,
		SlotIds:                json.SlotIds,
		BookingStartDate:       json.BookingStartDate,
		BookingEndDate:         json.BookingEndDate,
		FetchSlotBookingsCount: json.FetchSlotBookingsCount,
		TabId:                  json.TabId,
		NoShowApplicable:       json.NoShowApplicable,
		AppType:                headers.AppType,
		AppVersion:             headers.AppVersion,
		FacilityId:             json.FacilityId,
		IsTrial:                json.IsTrial,
	}

	//sports dashboard
	if (headers.AppType == "web" || headers.AppType == "partner-facility") && len(json.UserID) <= 0 {
		bookingData.UserId = 0
	}

	// session affinity based call
	response, err := cl.GetBookingDetails(ctx, bookingData, shard.Strategy(strconv.Itoa(int(json.BookingID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetBookingDetailsV2C(c *gin.Context) {
	bookingClient := util.GetBookingClient()

	var json structs.Booking
	json = c.MustGet("jsonData").(structs.Booking)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var bookedUserId int32
	if len(json.UserID) > 0 {
		buid, _ := strconv.Atoi(json.UserID)
		bookedUserId = int32(buid)
	}

	bookingData := &bookingPB.Booking{
		UserId:                  bookedUserId,
		Timeline:                json.Timeline,
		Start:                   json.Start,
		Count:                   json.Count,
		FsIds:                   json.FsIds,
		SlotIds:                 json.SlotIds,
		BookingStartDate:        json.BookingStartDate,
		BookingEndDate:          json.BookingEndDate,
		NoShowApplicable:        json.NoShowApplicable,
		AppType:                 headers.AppType,
		AppVersion:              headers.AppVersion,
		FacilityId:              json.FacilityId,
		IsTrial:                 json.IsTrial,
		IsGuest:                 json.IsGuest,
		FetchConsecutiveFlag:    json.FetchConsecutiveFlag,
		ProductArenaCategoryIds: json.ProductArenaCategoryIds,
	}

	//sports dashboard
	if headers.AppType == "web" && len(json.UserID) <= 0 {
		bookingData.UserId = 0
	}

	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := bookingClient.GetBookingDetailsV2(ctxbs, bookingData, shard.Strategy(strconv.Itoa(int(json.BookingID))))

	if err != nil {
		log.Printf("GetBookingDetailsV2C: error in getting booking details: %v", err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func PublishBookingC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	loggedInUser := util.GetUserIDFromContext(ctx)
	headers := c.MustGet("requestHeaders").(structs.Headers)

	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to book a slot."))
		return
	}

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.PublishBooking
	json = c.MustGet("jsonData").(structs.PublishBooking)

	//set default booking type
	if json.BookingType <= 0 {
		json.BookingType = 2
	}
	if json.SubscriptionType <= 0 {
		json.SubscriptionType = 1
	}
	if json.BookingCapacity <= 0 {
		json.BookingCapacity = 1
	}
	//add all default fallback values here
	bookingData := &bookingPB.PublishBookingRequest{
		UserID:                 loggedInUser,
		SubscriptionID:         json.SubscriptionID,
		ProductID:              json.ProductID,
		SessionCount:           json.SessionCount,
		SlotID:                 json.SlotID,
		BookingTime:            json.BookingTime,
		PAID:                   json.PAID,
		FSID:                   json.FSID,
		BookingType:            json.BookingType,
		BookingCapacity:        json.BookingCapacity,
		AppType:                headers.AppType,
		AppVersion:             headers.AppVersion,
		ForBookingModification: false,
		SubscriptionType:       json.SubscriptionType,
	}
	response, err := cl.ApplyPrebookingConditions(context.TODO(), bookingData, shard.Strategy(strconv.Itoa(headers.UserId)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Invalid Request"))
		return
	}

	if response.Status != nil && response.Status.Status == "failure" {
		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": response}, "index.html")

	} else {
		response, err := cl.PublishBooking(context.TODO(), bookingData, shard.Strategy(strconv.Itoa(headers.UserId)))

		if err != nil {
			fmt.Println(err)
			panic(err)
		}
		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": response}, "index.html")

	}

}

func PublishLiveSessionBookingC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.PublishBooking
	json = c.MustGet("jsonData").(structs.PublishBooking)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var bookingUserId int32
	if len(json.UserID) > 0 {
		buid, _ := strconv.Atoi(json.UserID)
		bookingUserId = int32(buid)
	} else {
		bookingUserId = int32(headers.UserId)
	}

	//add all default fallback values here
	bookingData := &bookingPB.PublishBookingRequest{
		UserID:                 bookingUserId,
		SubscriptionID:         json.SubscriptionID,
		ProductID:              json.ProductID,
		SessionCount:           json.SessionCount,
		SlotID:                 json.SlotID,
		BookingTime:            json.BookingTime,
		PAID:                   json.PAID,
		FSID:                   json.FSID,
		BookingType:            json.BookingType,
		BookingCapacity:        json.BookingCapacity,
		AppType:                headers.AppType,
		AppVersion:             headers.AppVersion,
		ForBookingModification: false,
		SubscriptionType:       json.SubscriptionType,
		OnlineSessionId:        json.OnlineSessionID,
		Token:                  headers.AccessToken,
	}

	response, err := cl.PublishLiveSessionBooking(context.TODO(), bookingData, shard.Strategy(strconv.Itoa(headers.UserId)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}
	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func GetDataForBookingCreationC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingDatesGet
	json = c.MustGet("jsonData").(structs.BookingDatesGet)

	bookingDatesGetData := &bookingPB.BookingDatesGet{
		FsId:        json.FSID,
		BookingType: json.BookingType,
	}

	// session affinity based call
	response, err := cl.GetDataForBookingCreation(context.TODO(), bookingDatesGetData, shard.Strategy(strconv.Itoa(int(json.FSID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetBookingSlotsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingSlots
	json = c.MustGet("jsonData").(structs.BookingSlots)
	// dateBookingTime := time.Unix(json.DateBooking, 0)
	// dateBooking, _ := ptypes.TimestampProto(dateBookingTime)

	bookingSlotsData := &bookingPB.BookingSlotsRequest{
		FsId:        json.FSID,
		BookingType: json.BookingType,
		DateBooking: json.DateBooking,
	}

	// session affinity based call
	response, err := cl.GetBookingSlots(context.TODO(), bookingSlotsData, shard.Strategy(strconv.Itoa(int(json.FSID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CancelBookingC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	loggedInUser := util.GetUserIDFromContext(ctx)
	headers := c.MustGet("requestHeaders").(structs.Headers)

	if loggedInUser == 0 && headers.AppType != common.USER_AUTH_WEB_APP_TYPE {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to cancel booking."))
		return
	}

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)
	cl := util.GetBookingClient()

	var json structs.CancelBooking
	json = c.MustGet("jsonData").(structs.CancelBooking)

	cancelledByUserId := loggedInUser
	if loggedInUser == 0 {
		cancelledByUserId = int32(headers.UserId)
	}

	cancelBookingData := &bookingPB.CancelBookingRequest{
		BookingReferenceNumber: json.BookingReferenceNumber,
		ActionUserId:           cancelledByUserId,
		ForBookingModification: false,
	}

	// session affinity based call
	response, err := cl.CancelBooking(context.TODO(), cancelBookingData, shard.Strategy(json.BookingReferenceNumber))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ModifyBookingC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.ModifyBooking
	json = c.MustGet("jsonData").(structs.ModifyBooking)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var bookingUserId int32
	if len(json.UserID) > 0 {
		buid, _ := strconv.Atoi(json.UserID)
		bookingUserId = int32(buid)
	} else {
		bookingUserId = int32(headers.UserId)
	}

	bookingData := &bookingPB.ModifyBookingRequest{
		UserID:                 bookingUserId,
		CancelUserID:           int32(headers.UserId),
		SubscriptionID:         json.SubscriptionID,
		ProductID:              json.ProductID,
		SessionCount:           json.SessionCount,
		SlotID:                 json.SlotID,
		BookingTime:            json.BookingTime,
		PAID:                   json.PAID,
		FSID:                   json.FSID,
		BookingType:            json.BookingType,
		BookingCapacity:        json.BookingCapacity,
		AppType:                headers.AppType,
		AppVersion:             headers.AppVersion,
		BookingReferenceNumber: json.BookingReferenceNumber,
	}
	/*
		arenaSlot := &bookingPB.ArenaSlot{
			SlotID:         json.SlotID,
			PAID:           json.PAID,
			FSID:           json.FSID,
			BookingTime:    json.BookingTime,
			SubscriptionId: json.SubscriptionID,
		}

		slotAvail := &bookingPB.SlotAvailRequest{
			ArenaSlot:              arenaSlot,
			CapacityCount:          json.BookingCapacity,
			CapacityType:           json.BookingType,
			BookingReferenceNumber: json.BookingReferenceNumber,
			ModifyBookingFlag:      true,
		}

		fmt.Println("-----slot avail modify-------------- ", slotAvail, json)
		response1, _ := cl.SlotArenaAvailability(context.TODO(), slotAvail, shard.Strategy(json.UserID))

		fmt.Println("response1 modify ==================== ", response1, bookingData)

		// session affinity based call
		if response1.Status.Status == "success" {
	*/
	response, err := cl.ModifyBooking(context.TODO(), bookingData, shard.Strategy(strconv.Itoa(headers.UserId)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}
	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
	/*else {

		statusObj := structs.StatusOb{}
		statusObj.Status = response1.Status

		render.Render(c, gin.H{
			"title":   "Home Page",
			"payload": statusObj}, "index.html")
	}
	*/
}

func GetBookingSlotsV2C(c *gin.Context) {
	c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to view slot details."))
	return
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingSlotsV2
	json = c.MustGet("jsonData").(structs.BookingSlotsV2)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	fsIds := strings.Split(json.FSIDs, ",")
	var fsidsInt []int32

	for _, fsid := range fsIds {
		vl, _ := strconv.Atoi(fsid)

		if vl > 0 {
			fsidsInt = append(fsidsInt, int32(vl))
		}
	}

	var tabId int32
	if headers.TabId > 0 {
		tabId = int32(headers.TabId)
	} else {
		tabId = json.TabId
	}

	bookingSlotsData := &bookingPB.BookingSlotsRequestV2{
		DateBooking:    json.DateBooking,
		SubscriptionId: json.SubscriptionID,
		FsIds:          fsidsInt,
		TabId:          tabId,
		UserLatitude:   json.UserLatitude,
		UserLongitude:  json.UserLongitude,
	}

	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := cl.GetBookingSlotsV2(ctxbs, bookingSlotsData, shard.Strategy(strconv.Itoa(int(json.SubscriptionID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetLiveSessionBookingSlotsV2C(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingSlotsV2
	json = c.MustGet("jsonData").(structs.BookingSlotsV2)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserID) > 0 {
		buid, _ := strconv.Atoi(json.UserID)
		userId = int32(buid)
	} else {
		userId = int32(headers.UserId)
	}

	bookingSlotsData := &bookingPB.BookingSlotsRequestV2{
		DateBooking:    json.DateBooking,
		SubscriptionId: json.SubscriptionID,
		UserId:         userId,
	}

	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := cl.GetOnlineSessionBookingSlotsV2(ctxbs, bookingSlotsData, shard.Strategy(strconv.Itoa(int(json.SubscriptionID))))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func BookingFeedbackOptionsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingFeedbackOptions
	json = c.MustGet("jsonData").(structs.BookingFeedbackOptions)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var reqUserId int32
	if len(json.UserID) > 0 {
		buid, _ := strconv.Atoi(json.UserID)
		reqUserId = int32(buid)
	} else {
		reqUserId = int32(headers.UserId)
	}

	var tabId int32
	if headers.TabId > 0 {
		tabId = int32(headers.TabId)
	} else {
		tabId = json.TabId
	}

	feedbackOptionData := &bookingPB.BookingFeedbackRequest{
		UserId:                 reqUserId,
		BookingReferenceNumber: json.BookingReferenceNumber,
		TabId:                  tabId,
	}

	// session affinity based call
	response, err := cl.GetBookingFeedbackOptions(context.TODO(), feedbackOptionData, shard.Strategy(strconv.Itoa(headers.UserId)))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func SaveBookingFeedbackC(c *gin.Context) {

	ctx := util.PrepareGRPCMetaData(c)
	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.LegacyFailedStatus("Please update your app to latest version to Submit your feedback."))
		return
	}

	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	cl = util.GetBookingClient()

	var json structs.SaveBookingFeedback
	json = c.MustGet("jsonData").(structs.SaveBookingFeedback)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	feedbackData := &bookingPB.SaveBookingFeedbackRequest{
		RatingId:               json.RatingID,
		BookingReferenceNumber: json.BookingReferenceNumber,
		Note:                   json.Note,
		CategoryOptions:        json.CategoryOptions,
		UserId:                 loggedInUser,
		AppType:                headers.AppType,
		AppVersion:             headers.AppVersion,
	}

	// session affinity based call
	response, err := cl.SaveBookingFeedback(context.TODO(), feedbackData, shard.Strategy(json.BookingReferenceNumber))

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func BlockInventoryC(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.BlockInventory
	json = c.MustGet("jsonData").(structs.BlockInventory)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var fsSlotsData []*bookingPB.BlockedFsSlots
	for _, val := range json.FsSlotsData {
		paId := val.PaId
		if val.ProductArenaCategoryId == PRODUCT_ARENA_CATEGORGY_ID_ACADEMY_SESSION || val.ProductArenaCategoryId == PRODUCT_ARENA_CATEGORGY_ID_SUMMER_CAMP_SESSION {
			paId = int32(0)
		}
		fsSlotDataPb := &bookingPB.BlockedFsSlots{
			FsId:                   val.FsId,
			PaId:                   paId,
			SlotId:                 val.SlotId,
			ProductArenaCategoryId: val.ProductArenaCategoryId,
		}
		fsSlotsData = append(fsSlotsData, fsSlotDataPb)
	}
	blockInventoryData := &bookingPB.BlockInventoryRequest{
		Date:                 json.Date,
		FsSlotsData:          fsSlotsData,
		BlockReasonId:        json.BlockReasonId,
		BlockRelatedNote:     json.BlockRelatedNote,
		BlockedBy:            int32(headers.UserId),
		Title:                json.Title,
		EmailBody:            json.EmailBody,
		SmsBody:              json.SmsBody,
		NotificationBody:     json.NotificationBody,
		EmailFlag:            json.EmailFlag,
		SmsFlag:              json.SmsFlag,
		NotificationFlag:     json.NotificationFlag,
		FixedUserFlag:        json.FixedUserFlag,
		PartnerFacility:      json.PartnerFacility,
		SlotsClosureReasonId: json.SlotsClosureReasonId,
	}

	timeTillContextDeadline := time.Now().Add(20 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := cl.BlockInventory(ctxbs, blockInventoryData)
	if err != nil {
		log.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetBlockedInventoryC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.GetBlockedInventory
	json = c.MustGet("jsonData").(structs.GetBlockedInventory)

	getInventoryData := &bookingPB.GetBlockedInventoryRequest{
		FsIds:      json.FsIds,
		StartDate:  json.StartDate,
		EndDate:    json.EndDate,
		SlotIds:    json.SlotIDs,
		PaIds:      json.PaIds,
		FacilityId: json.FacilityId,
	}

	// session affinity based call
	response, err := cl.GetBlockedInventory(context.TODO(), getInventoryData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func FeedbackListingC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.FeedbackListing
	json = c.MustGet("jsonData").(structs.FeedbackListing)

	// var headers structs.Headers
	// headers = c.MustGet("requestHeaders").(structs.Headers)

	feedbackFilterData := &bookingPB.FeedbackListingRequest{
		FsIds:            json.FSIDs,
		StartDate:        json.StartDate,
		EndDate:          json.EndDate,
		SlotIds:          json.SlotIDs,
		RatingIds:        json.RatingIDs,
		FIds:             json.FIds,
		SIds:             json.SIds,
		BookingStartDate: json.BookingStartDate,
		BookingEndDate:   json.BookingEndDate,
		ExcludeFacility:  json.ExcludeFacility,
		ExcludeSport:     json.ExcludeSport,
		ExcludeSlot:      json.ExcludeSlot,
	}

	fmt.Println("---------------------", feedbackFilterData)

	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	// session affinity based call
	response, err := cl.FeedbackListingGet(ctxbs, feedbackFilterData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func BookingSlotsDetailsC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingSlotsDetails
	json = c.MustGet("jsonData").(structs.BookingSlotsDetails)
	fmt.Println("json data : ", json)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	var reqUserId int32
	if json.UserId > 0 {
		reqUserId = int32(json.UserId)
	} else {
		reqUserId = int32(headers.UserId)
	}
	bookingSlotsDetailsData := &bookingPB.BookingSlotsDetailsRequest{
		CsId:            json.CsId,
		FsId:            json.FsId,
		SlotId:          json.SlotId,
		Date:            json.Date,
		OnlineSessionId: json.OnlineSessionId,
		UserId:          reqUserId,
		AppType:         headers.AppType,
		AppVersion:      headers.AppVersion,
	}
	response, err := cl.BookingSlotsDetailsGet(context.TODO(), bookingSlotsDetailsData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func BookingSlotsDetailsV2C(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.BookingSlotsDetailsV2
	json = c.MustGet("jsonData").(structs.BookingSlotsDetailsV2)
	bookingSlotsDetailsData := &bookingPB.BookingSlotsDetailsRequest{
		BookingId:              json.BookingId,
		BookingReferenceNumber: json.BookingReferenceNumber,
	}
	response, err := cl.BookingSlotsDetailsGetV2(context.TODO(), bookingSlotsDetailsData)
	if err != nil {
		log.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func MarkAttendanceStatusC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.MarkAttendance
	json = c.MustGet("jsonData").(structs.MarkAttendance)
	fmt.Println("json data : ", json)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var reqUserId int32
	if json.MarkedByUserId > 0 {
		reqUserId = json.MarkedByUserId
	} else {
		reqUserId = int32(headers.UserId)
	}

	markAttendanceData := &bookingPB.MarkAttendance{
		BookingReferenceNumber: json.BookingReferenceNumber,
		MarkedByUserId:         reqUserId,
		AttendanceFlag:         json.AttendanceFlag,
	}
	response, err := cl.MarkAttendanceStatus(context.TODO(), markAttendanceData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UnblockBlockedInventoryC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.UnblockInventoryRequest
	json = c.MustGet("jsonData").(structs.UnblockInventoryRequest)
	fmt.Println("json data : ", json)

	blockInventoryIds := strings.Split(json.BlockedInventoryIdArray, ",")
	var blockInventoryIdsArray []int32

	for _, id := range blockInventoryIds {
		vl, _ := strconv.Atoi(id)

		if vl > 0 {
			blockInventoryIdsArray = append(blockInventoryIdsArray, int32(vl))
		}
	}

	unblockDetailsData := &bookingPB.UnblockInventoryRequest{
		BlockedInventoryIdsArray: blockInventoryIdsArray,
		UserId:                   json.UnblockedBy,
	}

	timeTillContextDeadline := time.Now().Add(15 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	response, err := cl.UnblockBlockedSlots(ctxbs, unblockDetailsData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func UpsertSportsInactiveDaysC(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.SportsInactiveDayRequest
	json = c.MustGet("jsonData").(structs.SportsInactiveDayRequest)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	if headers.UserId == 0 {
		log.Printf("UpsertSportsInactiveDaysC: unauthorized request : %v", json)
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusFailure(common.UNAUTHORIZED))
		return
	}

	var sportsInactiveDays []*bookingPB.SportsInactiveDay
	for _, val := range json.FsDateData {
		sportsInactiveDay := &bookingPB.SportsInactiveDay{
			FsId: val.FsId,
			Date: val.Date,
		}
		sportsInactiveDays = append(sportsInactiveDays, sportsInactiveDay)
	}
	markInactiveDayRequest := &bookingPB.MarkInactiveDayRequest{
		CreatedBy:          int32(headers.UserId),
		Title:              json.Title,
		ReasonId:           json.ReasonId,
		SportsInactiveDays: sportsInactiveDays,
	}

	response, err := cl.MarkInactiveDayForFsIds(context.TODO(), markInactiveDayRequest)
	if err != nil {
		log.Printf("UpsertSportsInactiveDaysC: Error in upserting data into sports inactive days for req: %v, err: %v", markInactiveDayRequest, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(err.Error()))
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetInactiveDaysC(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.GetInactiveDaysRequest
	json = c.MustGet("jsonData").(structs.GetInactiveDaysRequest)

	reqData := &bookingPB.GetInactiveDaysRequest{
		StartDate: json.StartDate,
		EndDate:   json.EndDate,
	}

	response, err := cl.GetInactiveDays(c, reqData)
	if err != nil {
		log.Println("Error in getting inactive days data", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(err.Error()))
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetStreamDetailsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.StreamDetailsRequest
	json = c.MustGet("jsonData").(structs.StreamDetailsRequest)
	fmt.Println("json data : ", json)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if json.UserId > 0 {
		userId = json.UserId
	} else {
		userId = int32(headers.UserId)
	}

	streamReq := &bookingPB.StreamDetailsRequest{
		OnlineSessionId: json.OnlineSessionId,
		UserId:          userId,
		AppType:         headers.AppType,
		AppVersion:      headers.AppVersion,
	}

	response, err := cl.StreamDetailsForOnlineSession(context.Background(), streamReq)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetRecommendedSlotsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.GetRecommendedSlots
	json = c.MustGet("jsonData").(structs.GetRecommendedSlots)
	fmt.Println("json data : ", json)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var userId int32
	if len(json.UserID) > 0 {
		buid, _ := strconv.Atoi(json.UserID)
		userId = int32(buid)
	} else {
		userId = int32(headers.UserId)
	}

	getRecommendedSlotsRequest := &bookingPB.GetRecommendedSlotsRequest{
		UserId: userId,
	}

	response, err := cl.GetRecommendedSlots(context.TODO(), getRecommendedSlotsRequest)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func MarkOnlineSessionAttendanceC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.OnlineSessionAttendance
	json = c.MustGet("jsonData").(structs.OnlineSessionAttendance)
	fmt.Println("json data : ", json)

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	var reqUserId int32
	if json.UserId > 0 {
		reqUserId = json.UserId
	} else {
		reqUserId = int32(headers.UserId)
	}

	markAttendanceData := &bookingPB.MarkOnlineSessionAttendanceRequest{
		BookingReferenceNumber: json.BookingReferenceNumber,
		UserId:                 reqUserId,
		OnlineSessionId:        json.OnlineSessionId,
		Duration:               json.Duration,
		StartTimestamp:         json.StartTimestamp,
		EndTimestamp:           json.EndTimestamp,
		VideoEntrySecond:       json.VideoEntrySecond,
		VideoExitSecond:        json.VideoExitSecond,
		BufferingDuration:      json.BufferingDuration,
		AppType:                headers.AppType,
		AppVersion:             headers.AppVersion,
	}

	response, err := cl.MarkOnlineSessionAttendance(context.TODO(), markAttendanceData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func AutoPopulateSlotC(c *gin.Context) {
	fmt.Println("Running active slots population task", time.Now())

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	timeTillContextDeadline := time.Now().Add(120 * time.Second)
	ctxb, ctxCancelFuncb := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncb()

	bookingData := &bookingPB.PopulateSlotRequest{}

	response, err := cl.PopulateActiveSlots(ctxb, bookingData)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

// TEST ENDPOINT - Remove after debugging
func TestPopulateAcademyBookingsC(c *gin.Context) {
	fmt.Println("MANUALLY TRIGGERING PopulateAcademySubscriptionBookings", time.Now())

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	prebookingWindowSize := 2
	currentTime := time.Now().Unix()

	var timeToProcess []int64
	for i := 1; i <= prebookingWindowSize; i++ {
		timeToProcess = append(timeToProcess, currentTime+int64(86400*i))
	}

	req := &bookingPB.CreateAcademySessionBookingsRequest{
		TimeToProcess: timeToProcess,
	}

	timeTillContextDeadline := time.Now().Add(120 * time.Second)
	ctxb, ctxCancelFuncb := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncb()

	response, err := cl.CreateAcademySubscriptionBookings(ctxb, req)
	if err != nil {
		fmt.Printf("Error in manual academy booking population: %v", err)
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"message":       "Academy booking population triggered successfully",
		"response":      response,
		"timeToProcess": timeToProcess,
	})
}

func GetNoShowPenaltyRecordC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.GetNoShowPenaltyRecord
	json = c.MustGet("jsonData").(structs.GetNoShowPenaltyRecord)

	fmt.Printf("---------", json)
	noShowReq := &bookingPB.NoShowPenaltyRecordRequest{
		StartDate:               json.StartDate,
		EndDate:                 json.EndDate,
		SlotIds:                 sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.SlotIDs),
		IsReverted:              json.IsReverted,
		DeclineFlag:             json.DeclineFlag,
		UserIds:                 sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.UserIds),
		FsIds:                   sharedFunc.GetIntegerArrayFromCommaSeparatedString(json.FsIDs),
		BookingReferenceNumbers: strings.Split(json.BookingReferenceNumbers, ","),
		Start:                   json.Start,
		Count:                   json.Count,
		IsRaised:                json.IsRaised,
	}
	// session affinity based call

	response, err := cl.NoShowPenaltyRecordGet(context.TODO(), noShowReq)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func NoShowRevertC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.NoShowRevertRequest
	json = c.MustGet("jsonData").(structs.NoShowRevertRequest)
	reqData := &bookingPB.NoShowRevertRequest{
		BookingReferenceNumbers: json.BookingReferenceNumbers,
		BookingIds:              json.BookingIds,
		RevertedBy:              json.RevertedBy,
		Reason:                  json.Reason,
		EmailNotify:             json.EmailNotify,
		NotificationNotify:      json.NotificationNotify,
		DeclineFlag:             json.DeclineFlag,
		NotRaised:               json.NotRaised,
	}
	// session affinity based call

	response, err := cl.NoShowRevert(context.TODO(), reqData)

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetActiveSlotsC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	var json structs.ActiveSlots
	json = c.MustGet("jsonData").(structs.ActiveSlots)

	AvailableSlotsData := &bookingPB.ActiveSlotsRequest{
		DateBooking:   json.DateBooking,
		ProductId:     json.ProductID,
		UserLatitude:  json.UserLatitude,
		UserLongitude: json.UserLongitude,
	}
	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	response, err := cl.GetAllSportFacilitySlots(ctxbs, AvailableSlotsData, shard.Strategy(strconv.Itoa(int(json.ProductID))))

	if err != nil {
		log.Println(err)
		panic(err)
	}
	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")

}

func UserQueryNoShowRevertC(c *gin.Context) {

	cl = util.GetBookingClient()

	var json structs.UserQuery
	json = c.MustGet("jsonData").(structs.UserQuery)
	reqData := &bookingPB.UserBookingRequestQueries{
		BookingId:              json.BookingId,
		BookingReferenceNumber: json.BookingReferenceNumber,
		ReasonId:               json.ReasonId,
		QueryTypeId:            json.QueryTypeId,
		OtherReason:            json.OtherReason,
	}
	timeTillContextDeadline := time.Now().Add(25 * time.Second)
	ctxbs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	response, err := cl.UserQueryNoShowRevert(ctxbs, reqData)
	if err != nil {
		log.Println("Could not Create User Query for Noshow---", err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func GetSlotPopulationStatusC(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	reqData := &bookingPB.Empty{}

	response, err := cl.GetSlotPopulationStatus(context.TODO(), reqData)
	if err != nil {
		log.Println("Could not Create User Query for Noshow---", err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func AddEditCapacitySlotsC(c *gin.Context) {
	cl = util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)
	var json structs.PlayArenaCapacitySlotRequest
	json = c.MustGet("jsonData").(structs.PlayArenaCapacitySlotRequest)
	startDate, err1 := ptypes.TimestampProto(json.StartDate)
	endDate, err2 := ptypes.TimestampProto(json.EndDate)
	if err1 != nil || err2 != nil {
		log.Printf("Error in converting time.time to timestamp, err:%v, err:%v", err1, err2)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	reqData := &bookingPB.AddEditCapacitySlotRequest{
		FsId:                   json.FsId,
		ProductArenaCategoryId: json.ProductArenaCategoryId,
		ActiveStatus:           json.ActiveStatus,
		DayOfWeeks:             json.DayOfWeeks,
		SlotIds:                json.SlotIds,
		CapacityIndividual:     json.CapacityIndividual,
		PaIds:                  json.PaIds,
		StartDate:              startDate,
		EndDate:                endDate,
		WaitlistFlag:           json.WaitlistFlag,
		CapacityWaitlist:       json.CapacityWaitlist,
	}

	response, err := cl.AddEditCapacitySlots(ctx, reqData)
	if err != nil {
		log.Println("Error in inserting/editing play arena capacity slots", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetSessionCountC(c *gin.Context) {
	cl := util.GetBookingClient()
	var json structs.SessionCountRequest
	json = c.MustGet("jsonData").(structs.SessionCountRequest)
	userIds := util.DeduplicateSlice(json.UserIds)
	reqData := &bookingPB.GetBookingIdSessionCountMapReq{
		UserIds: userIds,
	}
	timeTillContextDeadline := time.Now().Add(3 * time.Second)
	ctx, cancel := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer cancel()
	response, err := cl.GetBookingIdSessionCountMap(ctx, reqData)
	if err != nil {
		log.Printf("Error in getting booking count details: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(
		http.StatusOK,
		response,
	)
}

func AssignCoachToBookingC(c *gin.Context) {
	cl := util.GetBookingClient()
	tokenUserRes := c.Value("user").(*authPB.ValidateTokenResponse)
	if tokenUserRes == nil || tokenUserRes.User == nil || tokenUserRes.User.UserId == 0 {
		log.Printf("Error in validating logged in user token details with validate token response: %v", tokenUserRes)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}
	var json structs.AssignCoachToBookingRequest
	json = c.MustGet("jsonData").(structs.AssignCoachToBookingRequest)
	reqData := &bookingPB.AssignCoachToBookingReq{
		BookingId:   json.BookingId,
		CoachUserId: json.CoachUserId,
		AssignedBy:  tokenUserRes.User.UserId,
	}
	timeTillContextDeadline := time.Now().Add(3 * time.Second)
	ctx, cancel := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer cancel()
	response, err := cl.AssignCoachToBooking(ctx, reqData)
	if err != nil {
		log.Printf("Error in assigning coach to booking id: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(
		http.StatusOK,
		response,
	)
}

func UpsertFMRemarkC(c *gin.Context) {
	cl := util.GetBookingClient()
	tokenUserRes := c.Value("user").(*authPB.ValidateTokenResponse)
	if tokenUserRes == nil || tokenUserRes.User == nil || tokenUserRes.User.UserId == 0 {
		log.Printf("UpsertFMRemarkC: Error in validating logged in user token details with validate token response: %v", tokenUserRes)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}
	var json structs.UpsertFMRemarkRequest
	json = c.MustGet("jsonData").(structs.UpsertFMRemarkRequest)
	reqData := &bookingPB.UpsertFMBookingRemarkReq{
		BookingId: json.BookingId,
		Remark:    json.Remark,
	}
	timeTillContextDeadline := time.Now().Add(3 * time.Second)
	ctx, cancel := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer cancel()
	response, err := cl.UpsertFMBookingRemark(ctx, reqData)
	if err != nil {
		log.Printf("Error in assigning coach to booking id: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(
		http.StatusOK,
		response,
	)
}

func GetSeasonalPoolMessageForPreferredPageList(startDate time.Time, endDate time.Time, season string) string {
	var res string
	startDate = GetLocalDateTime(startDate)
	endDate = GetLocalDateTime(endDate)

	if season == common.SWIMMING_OPENING_SEASON || season == common.SWIMMING_CLOSE_SEASON {
		res = "Seasonal pools are operational from " + strconv.Itoa(startDate.Day()) + Ordinal(startDate.Day()) + " " + startDate.Month().String() + " every year."

	}
	if season == common.SWIMMING_MID_SEASON || season == common.SWIMMING_CLOSING_SEASON {
		res = "Seasonal pools are operational till " + strconv.Itoa(endDate.Day()) + Ordinal(endDate.Day()) + " " + endDate.Month().String() + " every year."
	}
	if season == common.SWIMMING_OPEN_SEASON {
		res = ""
	}
	return res
}

func GetLocalDateTime(date time.Time) time.Time {
	location, _ := time.LoadLocation("Asia/Kolkata") //IST
	return date.In(location)
}

func GetAcademySlotsC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.Booking
	json = c.MustGet("jsonData").(structs.Booking)

	request := &bookingPB.AcademySlot{
		FsId: json.FSID,
	}

	response, err := cl.GetAcademySlotsByFsId(ctx, request)
	if err != nil {
		log.Printf("func:GetAcademySlotsC: Error in getting academy slots for fsid %d, err: %v", json.FSID, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in getting academy slots"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
	return
}

func AddEditAcademySlotsC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.AddEditAcademySlots
	json = c.MustGet("jsonData").(structs.AddEditAcademySlots)

	var academySlots []*bookingPB.AcademySlot
	for _, academySlot := range json.AcademySlots {
		date := time.Unix(academySlot.StartDate, 0)
		startDate, _ := ptypes.TimestampProto(date)
		slotDetails := &bookingPB.AcademySlot{
			SlotId1:                academySlot.SlotId1,
			SlotId2:                academySlot.SlotId2,
			MaxCapacity:            academySlot.MaxCapacity,
			RemainingCapacity:      academySlot.RemainingCapacity,
			IsActive:               academySlot.IsActive,
			StartDate:              startDate,
			OlderRemainingCapacity: academySlot.OlderRemainingCapacity,
		}
		academySlots = append(academySlots, slotDetails)
	}

	request := &bookingPB.AddEditAcademySlotsRequest{
		FsId:                   json.FsId,
		ProductId:              json.ProductId,
		ProductCourseMappingId: json.ProductCourseMappingId,
		SportId:                json.SportId,
		AcademySlots:           academySlots,
	}

	response, err := cl.AddEditAcademySlots(ctx, request)
	if err != nil {
		log.Printf("func:AddEditAcademySlotsR: Error in getting academy slots for fsid %d, err: %v", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in getting academy slots"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
	return
}

func GetRelatedAcademySlotsC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)
	var json structs.GetRelatedAcademySlotsByProductAndFsReq
	json = c.MustGet("jsonData").(structs.GetRelatedAcademySlotsByProductAndFsReq)
	reqData := &bookingPB.GetAllRelatedAcademySlotsByProductAndFSReq{
		ProductId:              json.ProductId,
		SportId:                json.SportId,
		FsId:                   json.FsId,
		ProductCourseMappingId: json.ProductCourseMappingId,
	}
	response, err := cl.GetAllRelatedAcademySlotsByProductAndFS(ctx, reqData)
	if err != nil {
		log.Printf("Error in getting related academy slot details: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(
		http.StatusOK,
		response,
	)
}

func GetRelatedSummerCampSlotsC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)
	var json structs.GetSummerCampSlotsByProductAndFsReq
	json = c.MustGet("jsonData").(structs.GetSummerCampSlotsByProductAndFsReq)
	reqData := &bookingPB.RelatedSummerCampSlotsByProductAndFSReq{
		ProductId: json.ProductId,
		SportId:   json.SportId,
		FsId:      json.FsId,
	}
	response, err := cl.GetRelatedSummerCampSlotsByProductAndFS(ctx, reqData)
	if err != nil {
		log.Printf("GetRelatedSummerCampSlotsC: Error in getting related academy slot details for req: %v, err: %v", reqData, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(
		http.StatusOK,
		response,
	)
}

func GetSummerCampSlotsByFsIdC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.GetSummerCampSlotsByProductAndFsReq
	json = c.MustGet("jsonData").(structs.GetSummerCampSlotsByProductAndFsReq)

	request := &bookingPB.SummercampSlot{
		FsId: json.FsId,
	}

	response, err := cl.GetSummercampSlotsByFsId(ctx, request)
	if err != nil {
		log.Printf("func:GetSummerCampSlotsByFsIdC: Error in getting academy slots for fsid %d, err: %v", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in getting academy slots"))
		return
	}

	c.JSON(http.StatusOK, response)
	return
}

func AddEditSummerCampSlotsC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.AddEditSummerCampSlots
	json = c.MustGet("jsonData").(structs.AddEditSummerCampSlots)

	var summercampSlots []*bookingPB.SummercampSlot
	for _, slot := range json.SummercampSlots {
		date := time.Unix(slot.StartDate, 0)
		startDate, _ := ptypes.TimestampProto(date)
		slotDetails := &bookingPB.SummercampSlot{
			SlotId:                 slot.SlotId,
			MaxCapacity:            slot.MaxCapacity,
			RemainingCapacity:      slot.RemainingCapacity,
			IsActive:               slot.IsActive,
			StartDate:              startDate,
			OlderRemainingCapacity: slot.OlderRemainingCapacity,
		}
		summercampSlots = append(summercampSlots, slotDetails)
	}

	request := &bookingPB.AddEditSummerCampSlotsRequest{
		FsId:                       json.FsId,
		ProductId:                  json.ProductId,
		SummercampProductMappingId: json.SummercampProductMappingId,
		SportId:                    json.SportId,
		SummercampSlots:            summercampSlots,
	}

	response, err := cl.AddEditSummerCampSlots(ctx, request)
	if err != nil {
		log.Printf("func:AddEditSummerCampSlotsC: Error in getting summer-camp slots for fsid %d, err: %v", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in getting academy slots"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
	return
}

func AcademyBookingCreationCultC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.AcademySessionNotification
	json = c.MustGet("jsonData").(structs.AcademySessionNotification)

	request := &bookingPB.CreateAcademySessionBookingsRequest{
		UserId:                 json.UserId,
		CreatedBy:              json.CreatedBy,
		BookingReferenceNumber: json.BookingReferenceNumber,
		BlockRelatedNote:       json.BlockRelatedNote,
		BookingCancelled:       json.BookingCancelled,
		IsBookingBlocked:       json.IsBookingBlocked,
	}

	response, err := cl.AcademyBookingCreationCult(ctx, request)
	if err != nil {
		log.Printf("func:AcademyBookingCreationCultC: Error in sending notification %d, err: %v", json.UserId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in sending academy notification"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("something went wrong"))
		return
	}
	c.JSON(http.StatusOK, response)
	return
}

func CorrectAcademySlotCapacityC(c *gin.Context) {
	cl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)
	ctx, _ = context.WithTimeout(context.Background(), time.Duration(time.Millisecond*8000))
	var json structs.CorrectAcademySlots
	json = c.MustGet("jsonData").(structs.CorrectAcademySlots)

	request := &bookingPB.AcademySlot{
		FsId: json.FsId,
	}

	response, err := cl.CorrectAcademySlotCapacity(ctx, request)
	if err != nil {
		log.Printf("func:CorrectAcademySlotCapacityC: Error in correcting academy slots for fsid %d, err: %v", json.FsId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure("Error in correcting academy slots"))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized("you are not authorized"))
		return
	}

	c.JSON(http.StatusOK, response)
	return
}
