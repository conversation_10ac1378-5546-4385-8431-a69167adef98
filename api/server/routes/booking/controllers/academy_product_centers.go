package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	centreModel "bitbucket.org/jogocoin/go_api/api/models/booking/centres"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"

	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"

	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/golang/protobuf/ptypes"
)

type AcademyProductCentersTemplate struct {
	CustomHeader   *centreModel.CustomHeader                       `json:"custom_header,omitempty"`
	EmptyView      *sushi.EmptyViewType1Layout                     `json:"empty_view,omitempty"`
	Results        []*sushi.FitsoImageTextSnippetType22Layout      `json:"results,omitempty"`
	Footer         *sushi.FooterSnippetType2Layout                 `json:"footer,omitempty"`
	HasMore        bool                                            `json:"has_more"`
	PostbackParams string                                          `json:"postback_params,omitempty"`
	PageData       *productPB.GetAcademyCentersForPurchaseResponse `json:"-"`
}

func GetAcademyProductCentersC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.GetCentersForProductCourseReq
	json = c.MustGet("jsonData").(structs.GetCentersForProductCourseReq)

	productClient := util.GetProductClient()
	previousFacilityIds := getFacilityRequestPostbackParams(json.PostbackParams)
	filledSlotData := getAcademySlotsPostbackParams(json.PostbackParams)
	reqData := &productPB.GetAcademyCentersForPurchaseRequest{
		StartDate:             json.PlanStartDate,
		CourseCategoryId:      json.CourseCategoryId,
		PreviousFacilityIds:   previousFacilityIds,
		AcademySlotFilledData: filledSlotData,
		UserId:                json.UserId,
	}

	var template *AcademyProductCentersTemplate
	response, err := productClient.GetAcademyCentersForPurchase(ctx, reqData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	template = &AcademyProductCentersTemplate{
		PageData: response,
	}
	template.SetCustomHeaderSection(ctx)
	template.SetFooterSection(ctx)

	if len(response.AcademyCenters) > 0 {
		template.SetResultSection(ctx)
	} else if json.PostbackParams == "" { // to ensure we don't send empty view for load more
		template.SetEmptyView(ctx)
	}

	template.SetHasMore(ctx)
	template.SetPostbackParams(ctx, previousFacilityIds)
	c.JSON(http.StatusOK, template)
}

func (c *AcademyProductCentersTemplate) SetPostbackParams(ctx context.Context, previousFacilityIds []int32) {

	for _, facilityId := range c.PageData.FacilityIds {
		previousFacilityIds = append(previousFacilityIds, facilityId)
	}

	var allFacilityIdsStringArray []int32
	for _, element := range previousFacilityIds {
		allFacilityIdsStringArray = append(allFacilityIdsStringArray, element)
	}

	payload := map[string][]int32{
		"previous_facility_ids": allFacilityIdsStringArray,
	}

	postbackParams, _ := json.Marshal(payload)
	c.PostbackParams = string(postbackParams)
}

func (c *AcademyProductCentersTemplate) SetHasMore(ctx context.Context) {
	c.HasMore = c.PageData.HasMore
}

func (c *AcademyProductCentersTemplate) SetCustomHeaderSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Select center & slot")
	colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	fontMed400, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	title.SetColor(colorGrey900)
	title.SetFont(fontMed400)

	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.DismissIcon, colorBlack500)

	c.CustomHeader = &centreModel.CustomHeader{
		Type: centreModel.CustomHeaderType1Val,
		CustomHeaderType1: &centreModel.CustomHeaderType1{
			Title: title,
			Icon:  icon,
		},
	}
}

func (c *AcademyProductCentersTemplate) SetFooterSection(ctx context.Context) {
	facilityId := int32(84)

	facilityTitle, _ := sushi.NewTextSnippet("Workout Studio")
	colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	facilityTitle.SetColor(colorBlack500)
	facilityTitle.SetFont(fontSemi400)

	facilitySubtitle, _ := sushi.NewTextSnippet("Malviya Nagar, New Delhi")
	colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	facilitySubtitle.SetColor(colorGrey500)
	facilitySubtitle.SetFont(fontMed200)

	ratingTitle, _ := sushi.NewTextSnippet("4.7")
	colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
	colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	ratingTitle.SetColor(colorWhite900)
	ratingTitle.SetFont(fontMed100)
	ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
	ratingTitle.SetPrefixIcon(ratingIcon)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}
	rating := &sushi.RatingSnippetBlockItem{
		Title:   ratingTitle,
		BgColor: bgColor,
	}

	facilityImage, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/GymAtWorkoutStudio01617731389.jpg")
	facilityImage.SetAspectRatio(1)
	facilityImage.SetType(sushi.ImageTypeRounded)
	facilityImage.SetHeight(64)
	facilityImage.SetWidth(64)

	bottomContainerTitle, _ := sushi.NewTextSnippet("Mon, Wed & Fri at 05:00 - 06:00 PM")
	bottomContainerTitle.SetColor(colorBlack500)
	bottomContainerTitle.SetFont(fontMed200)
	bottomContainer := &sushi.BottomContainerSnippetItem{Title: bottomContainerTitle}

	academySnippetData := &sushi.AcademySnippetData{
		Id:              facilityId,
		Title:           facilityTitle,
		Subtitle:        facilitySubtitle,
		Rating:          rating,
		Image:           facilityImage,
		BottomContainer: bottomContainer,
	}
	updateAcademySlotPurchaseDetails := &sushi.UpdateAcademySlotPurchaseDetails{
		AcademySlotId:      int32(123),
		AcademySnippetData: academySnippetData,
	}

	updateAcademySlotPurchaseDetailsClickAction := sushi.GetClickAction()
	updateAcademySlotPurchaseDetailsClickAction.SetAcademySlotPurchaseDetails(updateAcademySlotPurchaseDetails)

	fontMed500, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)

	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:             sushi.FooterButtonTypeSolid,
		Text:             "Select a center to proceed",
		Font:             fontMed500,
		Id:               "bottom_button_id1",
		BgColor:          colorGrey500,
		IsActionDisabled: 1,
		ClickAction:      updateAcademySlotPurchaseDetailsClickAction,
	}

	items := []sushi.FooterSnippetType2ButtonItem{buttonItem}

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	c.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
}

func (c *AcademyProductCentersTemplate) SetEmptyView(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("No facility currently available for selected course")
	colorGrey700, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	fontReg400, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	title.SetColor(colorGrey700)
	title.SetFont(fontReg400)

	image, _ := sushi.NewImage(util.GetCDNLink("uploads/yourmemberships1619098531.png"))
	image.SetHeight(158)
	image.SetWidth(160)
	snippet := &sushi.EmptyViewType1Snippet{
		Title: title,
		Image: image,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}
	c.EmptyView = &sushi.EmptyViewType1Layout{
		LayoutConfig:          layoutConfig,
		EmptyViewType1Snippet: snippet,
	}
}

func (c *AcademyProductCentersTemplate) SetResultSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoImageTextSnippetType22,
		LayoutType:  sushi.LayoutTypeGrid,
	}

	daysArr := []string{"MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"}
	for _, facilityData := range c.PageData.AcademyCenters {

		facilityId := facilityData.FacilityId

		if len(facilityData.DisplayName) == 0 || len(facilityData.SubzoneName) == 0 || len(facilityData.DisplayPicture) == 0 || len(facilityData.SportName) == 0 {
			continue
		}

		facilityTitle, _ := sushi.NewTextSnippet(facilityData.DisplayName)
		colorBlack500, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		fontSemi400, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		facilityTitle.SetColor(colorBlack500)
		facilityTitle.SetFont(fontSemi400)

		facilitySubtitle, _ := sushi.NewTextSnippet(facilityData.SubzoneName)
		colorGrey500, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		fontMed200, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		facilitySubtitle.SetColor(colorGrey500)
		facilitySubtitle.SetFont(fontMed200)

		distanceTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facilityData.Distance))
		fontSemi50, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
		distanceTitle.SetColor(colorBlack500)
		distanceTitle.SetFont(fontSemi50)

		colorWhite500, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

		tag := &sushi.Tag{
			Title:   distanceTitle,
			BgColor: colorWhite500,
		}

		ratingTitle, _ := sushi.NewTextSnippet(facilityData.Rating)
		colorWhite900, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
		fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		ratingTitle.SetColor(colorWhite900)
		ratingTitle.SetFont(fontMed100)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, colorWhite500)
		ratingTitle.SetPrefixIcon(ratingIcon)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		isNewColorSupported := featuresupport.SupportsNewColor(ctx)
		if isNewColorSupported {
			bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}
		rating := &sushi.RatingSnippetBlockItem{
			Title:   ratingTitle,
			BgColor: bgColor,
		}

		facilityImage, _ := sushi.NewImage(facilityData.DisplayPicture)
		facilityImage.SetAspectRatio(1)
		facilityImage.SetType(sushi.ImageTypeRounded)
		facilityImage.SetHeight(64)
		facilityImage.SetWidth(64)

		subtitle2, _ := sushi.NewTextSnippet(facilityData.SportName)
		subtitle2.SetColor(colorBlack500)
		subtitle2.SetFont(fontMed200)

		topContainer := &sushi.TopContainer{
			Id:        facilityId,
			Title:     facilityTitle,
			SubTitle:  facilitySubtitle,
			SubTitle2: subtitle2,
			Rating:    rating,
			Tag:       tag,
			Image:     facilityImage,
		}

		if c.PageData.CourseSport == common.SWIMMING_SPORT_ID && facilityData.SeasonalPoolInfo != nil && facilityData.SeasonalPoolInfo.Flag {
			startDate, _ := ptypes.Timestamp(facilityData.SeasonalPoolInfo.StartDate)
			endDate, _ := ptypes.Timestamp(facilityData.SeasonalPoolInfo.EndDate)
			startDate = util.GetLocalDateTime(startDate)
			endDate = util.GetLocalDateTime(endDate)
			seasonalPoolMessage := "<semibold-200|Seasonal pools> are operational from " + strconv.Itoa(startDate.Day()) + Ordinal(startDate.Day()) + " " + startDate.Month().String() + " till " + strconv.Itoa(endDate.Day()) + Ordinal(endDate.Day()) + " " + endDate.Month().String() + " every year. You can buy <semibold-200|1 month membership> for seasonal pools at center."

			titleSeasonalPool, _ := sushi.NewTextSnippet(seasonalPoolMessage)
			colorblue500, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
			fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			titleSeasonalPool.SetColor(colorblue500)
			titleSeasonalPool.SetFont(fontMed100)
			titleSeasonalPool.SetIsMarkdown(1)

			swimmingImage := &sushi.Image{
				URL:         util.GetCDNLink("uploads/swimming_blue1634120225.png"),
				AspectRatio: 1,
				Height:      14,
				Width:       14,
			}
			bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
			borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint200)
			topContainer.TopContainer = &sushi.V2ImageTextSnippetType31TopContainer{
				Title:        titleSeasonalPool,
				Image:        swimmingImage,
				BgColor:      bgColor,
				BorderColor:  borderColor,
				CornerRadius: 8,
			}
		}

		var bottomContainer []*sushi.BottomContainerSnippetItem
		slotsCount := 0
		soldOutSlotsCount := 0
		for _, productCourseMapping := range facilityData.ProductCourseMapping {
			var slots []*sushi.Slot
			slotsCount += len(productCourseMapping.AcademySlots)
			for _, facilitySlot := range productCourseMapping.AcademySlots {
				if facilitySlot.Capacity == 0 {
					soldOutSlotsCount++
				}
				timingString := facilitySlot.SlotTiming1
				if facilitySlot.SlotId2 > 0 {
					timingString = fmt.Sprintf("%s & %s", timingString, facilitySlot.SlotTiming2)
				}
				slotTitle, _ := sushi.NewTextSnippet(timingString)

				button, _ := sushi.NewButton(sushi.ButtonTypeSolid)
				button.SetText("Proceed with selected slot")

				commonPayload := make(map[string]interface{})
				commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
				commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
				commonPayload["source"] = "academy_slot_center_page"
				tapEname := &sushi.EnameData{
					Ename: "academy_proceed_with_slot_tap",
				}
				tapEvent := sushi.NewClevertapEvents()
				tapEvent.SetTap(tapEname)
				tapTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, tapEvent)
				button.AddClevertapTrackingItem(tapTrackItem)

				colorGrey900, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
				fontMed100, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

				facilityTitleItem, _ := sushi.NewTextSnippet(facilityData.DisplayName)
				facilityTitleItem.SetColor(colorGrey900)
				facilityTitleItem.SetFont(fontMed100)

				slotTitleItem, _ := sushi.NewTextSnippet(timingString)
				slotTitleItem.SetColor(colorGrey900)
				slotTitleItem.SetFont(fontMed100)

				var slotStr string
				for _, day := range productCourseMapping.Days {
					dayStr := daysArr[day-1]
					slotStr = slotStr + dayStr[:1]
				}
				dowTitleItem, _ := sushi.NewTextSnippet(slotStr)
				dowTitleItem.SetColor(colorGrey900)
				dowTitleItem.SetFont(fontMed100)

				slotInfo := make([]*sushi.SubtitleItem, 0)
				slotInfo = append(slotInfo, &sushi.SubtitleItem{
					Type: "facility",
					Item: &sushi.Item{Text: facilityTitleItem},
				})
				slotInfo = append(slotInfo, &sushi.SubtitleItem{
					Type: "days",
					Item: &sushi.Item{Text: dowTitleItem},
				})
				slotInfo = append(slotInfo, &sushi.SubtitleItem{
					Type: "time",
					Item: &sushi.Item{Text: slotTitleItem},
				})

				contextualData := &sushi.ContextualData{
					SnippetId:           facilityId,
					TitleText:           facilityData.DisplayName,
					SubtitleText:        facilityData.SubzoneName,
					RatingText:          facilityData.Rating,
					ImageURL:            facilityData.DisplayPicture,
					BottomContainerText: fmt.Sprintf("%s at %s", productCourseMapping.DaysText, timingString),
					AcademySlotId:       facilitySlot.AcademySlotId,
					ProductId:           productCourseMapping.ProductId,
					SlotInfo:            slotInfo,
				}

				slotFacilityContextualData := sushi.UpdateSlotFacilityContextualData{
					ButtonId:       "bottom_button_id1",
					Button:         button,
					ContextualData: contextualData,
				}

				updateSlotFacilityContextualDataClickAction := sushi.GetClickAction()
				updateSlotFacilityContextualDataClickAction.SetSlotFacilityContextualData(&slotFacilityContextualData)

				slot := &sushi.Slot{
					Title:      slotTitle,
					SlotId:     facilitySlot.AcademySlotId,
					IsDisabled: facilitySlot.Disabled,
				}
				if !facilitySlot.Disabled {
					slot.ClickAction = updateSlotFacilityContextualDataClickAction
				} else {
					slotSubTitle, _ := sushi.NewTextSnippet("slot sold out")
					iconColorType := sushi.ColorTypeOrange
					if isNewColorSupported {
						iconColorType = sushi.ALERT_LIGHT_THEME
					}
					iconColor, _ := sushi.NewColor(iconColorType, sushi.ColorTint500)
					soldOutIcon, _ := sushi.NewIcon(sushi.ThunderIcon, iconColor)
					slotSubTitle.SetPrefixIcon(soldOutIcon)
					slot.SubTitle = slotSubTitle
				}

				slots = append(slots, slot)
			}
			var days []string
			for _, day := range productCourseMapping.Days {
				days = append(days, daysArr[day-1])
			}
			sectionTitle, _ := sushi.NewTextSnippet(strings.Join(days, "-"))
			colorGrey600, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			fontRegular100, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
			sectionTitle.SetColor(colorGrey600)
			sectionTitle.SetFont(fontRegular100)
			sectionTitle.SetKerning(3)

			sectionHeader := &sushi.SectionHeaderType1Snippet{
				Title: sectionTitle,
			}

			colorGrey200, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
			colorGrey50, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
			colorGrey400, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
			fontSemi100, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)

			slotConfig := &sushi.ItemConfig{
				SelectedItemColor:      colorBlack500,
				UnselectedItemColor:    colorGrey200,
				EnabledItemColor:       colorWhite500,
				DisabledItemColor:      colorGrey50,
				DisabledItemTitleColor: colorGrey400,
				DisabledItemTitleFont:  fontSemi100,
			}

			slotElement := &sushi.SlotSection{
				SectionHeader: sectionHeader,
				Slots:         slots,
			}
			bottomContainerElement := &sushi.BottomContainerSnippetItem{
				Type:        "slots",
				SlotSection: slotElement,
				SlotConfig:  slotConfig,
			}
			bottomContainer = append(bottomContainer, bottomContainerElement)
		}

		tagString, tagNumber := getFacilityTag(soldOutSlotsCount, slotsCount)

		var facilityTag *sushi.Tag
		if tagNumber != 3 {
			facilityTagTitle, _ := sushi.NewTextSnippet(tagString)
			fontSemi50, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
			colorWhite100, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
			facilityTagTitle.SetColor(colorWhite100)
			facilityTagTitle.SetFont(fontSemi50)

			facilityTag = &sushi.Tag{
				Title:   facilityTagTitle,
				BgColor: colorBlack500,
			}
		}

		expandSnippetClickAction := sushi.GetClickAction()
		if tagNumber != 1 {
			expandSnippetClickAction.SetExpandSnippet()
		}

		item := &sushi.FitsoImageTextSnippetType22Item{
			Tag:          facilityTag,
			TopContainer: topContainer,
			ClickAction:  expandSnippetClickAction,
			IsSelectable: true,
		}
		commonPayload := make(map[string]interface{})
		commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
		commonPayload["city_id"] = util.GetCityIDFromContext(ctx)
		commonPayload["facility_id"] = facilityId
		commonPayload["source"] = "academy_slot_center_page"

		if tagNumber == 1 {
			item.IsSelectable = false
		} else if c.PageData.CourseSport == common.SWIMMING_SPORT_ID && facilityData.SeasonalPoolInfo != nil && facilityData.SeasonalPoolInfo.Flag && featuresupport.SupportAcademySeasonalPoolPopup(ctx) {
			seasonalPoolClickAction := sushi.GetClickAction()
			customAlert := CustomAlertForSeasonalPool()
			impEname := &sushi.EnameData{
				Ename: "academy_seasonal_pool_popup_impression",
			}
			impEvent := sushi.NewClevertapEvents()
			impEvent.SetImpression(impEname)
			impTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, impEvent) 
			customAlert.ClevertapTracking = []*sushi.ClevertapItem{impTrackItem}
			seasonalPoolClickAction.SetCustomAlertAction(customAlert)
			item.ClickAction = seasonalPoolClickAction
		} else {
			item.BottomContainer = bottomContainer
		}
		tapEname := &sushi.EnameData{
			Ename: "academy_preferred_center_tap",
		}
		tapEvent := sushi.NewClevertapEvents()
		tapEvent.SetTap(tapEname)
		tapTrackItem := sushi.GetClevertapTrackItem(ctx, commonPayload, tapEvent)
		item.ClevertapTracking = []*sushi.ClevertapItem{tapTrackItem}

		items := []*sushi.FitsoImageTextSnippetType22Item{item}
		itemsSnippet := &sushi.FitsoImageTextSnippetType22Snippet{
			Items: items,
		}
		resultItem := &sushi.FitsoImageTextSnippetType22Layout{
			LayoutConfig:                       layoutConfig,
			FitsoImageTextSnippetType22Snippet: itemsSnippet,
		}

		c.Results = append(c.Results, resultItem)
	}
}

func CustomAlertForSeasonalPool() *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("You're selecting a seasonal pool")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("Seasonal pools are operational from 15th Mar till 31st Oct every year. You can purchase 1 month membership for this pool offline at the center.")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)

	positiveAction := &sushi.Button{
		Type:        "text",
		Text:        "Ok, Got it",
	}

	custom_alert := &sushi.CustomAlert{
		Title:          title,
		Message:        message,
		PositiveAction: positiveAction,
		DismissAfterAction: true,
	}

	return custom_alert
}

func GetAcademyProductSlotsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.GetSlotsForProductCourseReq
	json = c.MustGet("jsonData").(structs.GetSlotsForProductCourseReq)

	bookingClient := util.GetBookingClient()
	reqData := &bookingPB.AcademySlotsForProductFacilityRequest{
		StartDate:              json.PlanStartDate,
		ProductCourseMappingId: json.ProductCourseMappingId,
		FsId:                   json.FsId,
	}
	res, err := bookingClient.GetAcademySlotsForProductAndFacility(ctx, reqData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.JSON(http.StatusOK, res)
}

// 3 - nothing, 1 - Sold Out, 2 - Filling Fast
func getFacilityTag(soldOutSlotsCount int, slotsCount int) (string, int) {
	if soldOutSlotsCount == slotsCount {
		return "Sold Out", 1
	} else if soldOutSlotsCount > 0 {
		return "Filling Fast", 2
	}
	return "", 3
}

func getFacilityRequestPostbackParams(postbackParams string) []int32 {
	var temp map[string]interface{}
	previousFacilityIds := make([]int32, 0)

	err := json.Unmarshal([]byte(postbackParams), &temp)
	if err != nil {
		return previousFacilityIds
	}

	if facilityIds, ok := temp["previous_facility_ids"]; ok {
		facilityIdsInterface, _ := facilityIds.([]interface{})
		for _, val := range facilityIdsInterface {
			previousFacilityIds = append(previousFacilityIds, int32(val.(float64)))
		}
	}
	return previousFacilityIds
}

func getAcademySlotsPostbackParams(postbackParams string) []*productPB.AcademySlotFilledData {
	var temp *structs.SelectedUsersData
	var postbackParamSlotData []*productPB.AcademySlotFilledData

	err := json.Unmarshal([]byte(postbackParams), &temp)
	if err != nil {
		return postbackParamSlotData
	}

	for _, user := range temp.SelectedUsersData {
		academySlotId, _ := strconv.Atoi(user.AcademySlotId)
		if err != nil {
			return postbackParamSlotData
		}
		slotData := &productPB.AcademySlotFilledData{
			AcademySlotId: int32(academySlotId),
			PlanStartDate: user.PlanStartDate,
		}
		postbackParamSlotData = append(postbackParamSlotData, slotData)
	}

	return postbackParamSlotData
}
