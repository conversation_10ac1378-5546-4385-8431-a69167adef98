package bookingController

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"context"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	slotModel "bitbucket.org/jogocoin/go_api/api/models/booking/slots"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// SlotResponse represents the response structure of slots page
type AcademySlotPageTemplate struct {
	Header            *slotModel.Header                       `json:"header,omitempty"`
	ProgressBarData   *slotModel.ProgressBarStruct            `json:"progress_bar_data,omitempty"`
	CustomHeader      *slotModel.CustomHeader                 `json:"custom_header,omitempty"`
	SelectionTitle    *sushi.TextSnippet                      `json:"selection_title"`
	Footer            *sushi.FooterSnippetType2Layout         `json:"footer"`
	DetailsContainer  *slotModel.DetailsContainer             `json:"details_container"`
	InfoContainer     *slotModel.InfoContainer                `json:"info_container"`
	SupportContainer  *sushi.FitsoImageTextSnippetType7Layout `json:"support_container,omitempty"`
	Config            *slotModel.SlotConfig                   `json:"config"`
	Items             *[]slotModel.SlotItem                   `json:"items"`
	ClevertapTracking []*sushi.ClevertapItem                  `json:"clever_tap_tracking,omitempty"`
}

func GetAcademyTrialSlotsC(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.BookingSlotsV3
	json = c.MustGet("jsonData").(structs.BookingSlotsV3)
	ctx := util.PrepareGRPCMetaData(c)

	if json.FsId == 0 && json.BottomSheet == 1 {
		log.Printf("Error in GetAcademyTrialSlotsC for FsId: %d", json.FsId)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}
	if len(json.BookingUsers) == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("Empty user list"))
		return
	}

	reqData := &bookingPB.BookingSlotsV3Req{
		FsId:         json.FsId,
		SportId:      json.SportId,
		BookingCount: int32(len(json.BookingUsers)),
	}
	for _, bookingUser := range json.BookingUsers {
		reqData.BookingUserIds = append(reqData.BookingUserIds, bookingUser.UserID)
	}

	bookingSlotResponse, err := cl.GetAcademyTrialSlotsV3(ctx, reqData)
	if err != nil {
		log.Println("Could not fetch academy trial slots details ---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if bookingSlotResponse.Status != nil {
		if bookingSlotResponse.Status.Status == common.FAILED {
			c.JSON(http.StatusOK, model.StatusFailure(bookingSlotResponse.Status.Message))
			return
		}
		if bookingSlotResponse.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, model.StatusFailure(bookingSlotResponse.Status.Message))
			return
		}
	}

	template := AcademySlotPageTemplate{}
	if json.BottomSheet != 1 {
		template.SetHeaderSection(ctx)
		template.SetProgressBarSection(ctx)
		template.SetSupportContainerSection(ctx)
	} else {
		template.SetCustomHeaderSection(ctx, bookingSlotResponse)
	}
	template.SetSelectionTitleSection(&json, bookingSlotResponse)
	template.SetFooterSection(&json, bookingSlotResponse)

	showInfoContainerSection := template.checkInfoContainerShow(bookingSlotResponse)
	if showInfoContainerSection {
		template.SetInfoContainerSection(bookingSlotResponse)
	}
	template.SetConfigSection()
	template.SetSlotItemSection(ctx, &json, bookingSlotResponse)
	template.SetPageTracking(ctx, bookingSlotResponse)

	c.JSON(http.StatusOK, template)
	return
}

func (s *AcademySlotPageTemplate) SetHeaderSection(ctx context.Context) {
	title, _ := sushi.NewTextSnippet("Book free trial")
	title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	title.SetFont(title_font)

	title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetColor(title_color)
	trialText := "1 free trial available for each sport"

	facilityClient := util.GetFacilitySportClient()
	response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Printf("Error in getting city based sports %v", err)
		return
	}
	if len(response.AcademySportIds) == 1 {
		trialText = "One free trial available"
	}
	tag, _ := sushi.NewTag(trialText)
	tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
	tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
	if featuresupport.SupportsNewColor(ctx) {
		tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
		tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
	}

	tag.Title.SetFont(tagFont)
	tag.Title.SetColor(tagColor)
	tag.SetBgColor(tagBgColor)
	tag.SetBorderColor(tagBorderColor)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &slotModel.Header{
		Title:   title,
		Tag:     tag,
		BgColor: bgColor,
	}
	s.Header = header
}

func (s *AcademySlotPageTemplate) SetProgressBarSection(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	colorType := sushi.ColorTypeTeal
	if featuresupport.SupportsNewColor(ctx) {
		colorType = sushi.ColorTypeCyan
	}
	progressbarColor := &[]sushi.Color{
		sushi.Color{
			Type: colorType,
			Tint: sushi.ColorTint500,
		},
	}
	progressBarDetails := &slotModel.ProgressBarStruct{
		Progress:       66,
		BgColor:        bgColor,
		ProgressColors: progressbarColor,
	}
	s.ProgressBarData = progressBarDetails
}

func (s *AcademySlotPageTemplate) SetCustomHeaderSection(ctx context.Context, response *bookingPB.BookingSlotsV3Res) {
	title, _ := sushi.NewTextSnippet(response.FacilityName)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	subtitle, _ := sushi.NewTextSnippet(response.SubzoneName)
	subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	subtitle.SetFont(subtitleFont)
	subtitle.SetColor(subtitleColor)

	bgColorTint := sushi.ColorTint100
	if featuresupport.SupportsNewColor(ctx) {
		bgColorTint = sushi.ColorTint50
	}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)
	customHeaderType1Obj := &slotModel.CustomHeaderType1{
		Title:    title,
		SubTitle: subtitle,
		BgColor:  bgColor,
	}

	customHeaderDetails := &slotModel.CustomHeader{
		Type:              slotModel.CustomHeaderType1Val,
		CustomHeaderType1: customHeaderType1Obj,
	}
	s.CustomHeader = customHeaderDetails
}

func (s *AcademySlotPageTemplate) SetSelectionTitleSection(req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	var selection_title_text string

	if req.BottomSheet != 1 {
		selection_title_text = "Select your slot for " + response.SportName
	} else {
		selection_title_text = "Booking slot for " + response.SportName
	}
	title, _ := sushi.NewTextSnippet(selection_title_text)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	s.SelectionTitle = title
}

func (s *AcademySlotPageTemplate) SetFooterSection(req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	var buttonItem2Text string
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Font: buttonItem2Font,
		Id:   "bottom_button_id2",
	}
	buttonItem2Text = "Book free trial slot"
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem2.Text = buttonItem2Text
	buttonItem2.BgColor = color
	buttonItem2.IsActionDisabled = 1

	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}

	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func (s *AcademySlotPageTemplate) SetInfoContainerSection(response *bookingPB.BookingSlotsV3Res) {
	var titleText, subTitleText, imageLink string
	if response.FsId == 82 {
		titleText = "Maintenance Alert"
		subTitleText = "Air-conditioning is temporarily unavailable due to electrical maintenance work. We are working to get them up and working soon. We sincerely regret the inconvenience."
		imageLink = util.GetCDNLink("uploads/<EMAIL>")
	} else {
		titleText = "Monsoon Alert"
		subTitleText = "Please note that this is an open facility. In case it rains, the center may be closed."
		imageLink = util.GetCDNLink("uploads/Group240401627311075.png")
	}

	title, _ := sushi.NewTextSnippet(titleText)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(titleColor)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	title.SetFont(titleFont)
	subTitle, _ := sushi.NewTextSnippet(subTitleText)
	subTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subTitle.SetColor(subTitleColor)
	subTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	subTitle.SetFont(subTitleFont)
	image, _ := sushi.NewImage(imageLink)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	infoContainerData := &slotModel.InfoContainer{
		Title:        title,
		SubTitle:     subTitle,
		Image:        image,
		BgColor:      bgColor,
		BorderColor:  borderColor,
		CornerRadius: int32(12),
	}
	s.InfoContainer = infoContainerData
}

func (s *AcademySlotPageTemplate) SetSupportContainerSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Contact <EMAIL>.")

	rightButton, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)

	rightButton.SetText("Call")
	rightButton.SetPrefixIcon(callIcon)
	rightButton.SetClickAction(clickAction)

	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["source"] = "academy_trial_slot"
	payload["is_trial"] = 1
	tapEname := &sushi.EnameData{
		Ename: "academy_call_click",
	}

	callEvents := sushi.NewClevertapEvents()
	callEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, callEvents)
	rightButton.AddClevertapTrackingItem(trackItem)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{
		BgColor:     bgColor,
		Title:       title,
		//RightButton: rightButton,
		BorderColor: borderColor,
	}
	rightButton1, _ := sushi.NewButton(sushi.ButtontypeText)
	rightButton1.SetText(" ")
	item.RightButton = rightButton1
	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}

	supportContainerData := &sushi.FitsoImageTextSnippetType7Layout{
		LayoutConfig:        layoutConfig,
		FitsoImageTextType7: snippet,
	}

	s.SupportContainer = supportContainerData
}

func (s *AcademySlotPageTemplate) SetConfigSection() {
	selected_title_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	selected_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	unselected_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	unselected_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)

	headerConfigDetails := &slotModel.HeaderConfig{
		SelectedTitleColor:      selected_title_color,
		SelectedSubtitleColor:   selected_subtitle_color,
		UnselectedTitleColor:    unselected_title_color,
		UnselectedSubtitleColor: unselected_subtitle_color,
	}

	selected_item_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	unselected_item_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	enabled_item_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	disabled_item_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)
	enabled_item_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
	disabled_item_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)

	itemConfigDetails := &slotModel.ItemConfig{
		SelectedItemColor:     selected_item_color,
		UnselectedItemColor:   unselected_item_color,
		EnabledItemColor:      enabled_item_color,
		DisabledItemColor:     disabled_item_color,
		EnabledItemTitleColor: enabled_item_title_color,
		DisabledItemTitleFont: disabled_item_title_font,
	}

	configDetails := &slotModel.SlotConfig{
		HeaderConfig: headerConfigDetails,
		ItemConfig:   itemConfigDetails,
	}
	s.Config = configDetails
}

func (s *AcademySlotPageTemplate) SetSlotItemSection(ctx context.Context, req *structs.BookingSlotsV3, response *bookingPB.BookingSlotsV3Res) {
	var slotItems []slotModel.SlotItem

	var closed_message string
	if len(response.BookingSlots) > 0 {
		for _, item := range response.BookingSlots {
			var title_text string
			var dayInterval int32
			if item.IsToday == true {
				title_text = "Today"
				dayInterval = 0
			} else if item.IsTomorrow == true {
				title_text = "Tomorrow"
				dayInterval = 1
			} else {
				title_text = item.WeekDay
				dayInterval = 2
			}
			title, _ := sushi.NewTextSnippet(title_text)
			subtitle, _ := sushi.NewTextSnippet(item.Date)
			if item.IsClosed == false {
				var morningSlots []slotModel.SlotStruct
				var eveningSlots []slotModel.SlotStruct
				s.GetSlotContent(ctx, req, item.MorningSlots, &morningSlots, response, dayInterval)
				s.GetSlotContent(ctx, req, item.EveningSlots, &eveningSlots, response, dayInterval)

				var slotSections []slotModel.SlotSection
				if len(morningSlots) > 0 {
					morning_title, _ := sushi.NewTextSnippet("MORNING")
					morning_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					morning_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					morning_title.SetFont(morning_title_font)
					morning_title.SetColor(morning_title_color)
					morning_title.SetKerning(3)

					morning_subtitle, _ := sushi.NewTextSnippet("(" + strconv.Itoa(len(morningSlots)) + " slots)")
					morning_subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					morning_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					morning_subtitle.SetFont(morning_subtitle_font)
					morning_subtitle.SetColor(morning_subtitle_color)

					morning_slot_section_header := &slotModel.SlotSectionHeader{
						Title:    morning_title,
						SubTitle: morning_subtitle,
					}
					morning_slot_section := &slotModel.SlotSection{
						SectionHeader: morning_slot_section_header,
						Slots:         morningSlots,
					}
					slotSections = append(slotSections, *morning_slot_section)
				}

				if len(eveningSlots) > 0 {
					evening_title, _ := sushi.NewTextSnippet("EVENING")
					evening_title_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					evening_title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
					evening_title.SetFont(evening_title_font)
					evening_title.SetColor(evening_title_color)
					evening_title.SetKerning(3)

					evening_subtitle, _ := sushi.NewTextSnippet("(" + strconv.Itoa(len(eveningSlots)) + " slots)")
					evening_subtitle_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
					evening_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
					evening_subtitle.SetFont(evening_subtitle_font)
					evening_subtitle.SetColor(evening_subtitle_color)

					evening_slot_section_header := &slotModel.SlotSectionHeader{
						Title:    evening_title,
						SubTitle: evening_subtitle,
					}
					evening_slot_section := &slotModel.SlotSection{
						SectionHeader: evening_slot_section_header,
						Slots:         eveningSlots,
					}
					slotSections = append(slotSections, *evening_slot_section)
				}

				tapPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
				tapPayload["day"] = dayInterval
				tapEname := &sushi.EnameData{
					Ename: "academy_slots_page_day_tab_click",
				}
				tabEvents := sushi.NewClevertapEvents()
				tabEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, tabEvents)

				obj := &slotModel.SlotItem{
					Title:             title,
					SubTitle:          subtitle,
					Sections:          slotSections,
					ClevertapTracking: []*sushi.ClevertapItem{trackItem},
				}
				slotItems = append(slotItems, *obj)
			} else {
				closed_image_url := util.GetCDNLink("uploads/Closed1619501106.jpg")
				closed_image, _ := sushi.NewImage(closed_image_url)
				closed_image.SetAspectRatio(1.0)
				closed_message = "Closed due to maintenance"
				closed_title, _ := sushi.NewTextSnippet(closed_message)
				error := &slotModel.SlotErrorStruct{
					Title: closed_title,
					Image: closed_image,
				}
				obj := &slotModel.SlotItem{
					Title:    title,
					SubTitle: subtitle,
					Error:    error,
				}
				slotItems = append(slotItems, *obj)
			}
		}
		s.Items = &slotItems
	} else {
		closed_image_url := util.GetCDNLink("uploads/Closed1619501106.jpg")
		closed_image, _ := sushi.NewImage(closed_image_url)
		closed_image.SetAspectRatio(1.0)
		closed_message = "Closed due to maintenance"
		closed_title, _ := sushi.NewTextSnippet(closed_message)
		error := &slotModel.SlotErrorStruct{
			Title: closed_title,
			Image: closed_image,
		}

		title, _ := sushi.NewTextSnippet("Today")
		t := time.Now()
		str_date := t.Format(time.RFC1123)
		words := strings.Fields(str_date)
		date := words[0] + " " + words[1] + " " + words[2]
		subtitle, _ := sushi.NewTextSnippet(date)
		obj := &slotModel.SlotItem{
			Title:    title,
			SubTitle: subtitle,
			Error:    error,
		}
		slotItems = append(slotItems, *obj)
		s.Items = &slotItems
	}
}

func (s *AcademySlotPageTemplate) GetSlotContent(
	ctx context.Context,
	req *structs.BookingSlotsV3,
	slotsData []*bookingPB.BookingSlot,
	slotsTemplateData *[]slotModel.SlotStruct,
	response *bookingPB.BookingSlotsV3Res,
	dayInterval int32,
) error {
	var slots_template_data []slotModel.SlotStruct
	if len(slotsData) == 0 {
		*slotsTemplateData = slots_template_data
		return nil
	}
	for _, itm := range slotsData {
		slot_title := itm.Timing
		if itm.SlotId == 12 {
			slot_title = "4 - 5 PM"
		}
		if itm.SlotId == 13 {
			slot_title = "5 - 6 PM"
		}
		if itm.SlotId == 14 {
			slot_title = "6 - 7 PM"
		}
		if !itm.BookingAllowed {
			if len(itm.AvailablityText) > 0 {
				slot_title += " " + itm.AvailablityText
			}
		}

		title, _ := sushi.NewTextSnippet(slot_title)
		slot_obj := &slotModel.SlotStruct{
			Title:  title,
			SlotId: itm.SlotId,
		}

		if !itm.BookingAllowed {
			slot_obj.IsDisabled = true

			if itm.IsSoldOut {
				title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
				title_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)
				slot_obj.Title.SetFont(title_font)
				slot_obj.Title.SetColor(title_color)

				// see other venues
				if req.BottomSheet == 1 && itm.OtherCentresAvailable {
					subtitle, _ := sushi.NewTextSnippet("see other venues")
					slot_obj.SubTitle = subtitle

					var bookingUsers []structs.BookingUser
					for _, itm := range req.BookingUsers {
						obj := structs.BookingUser{
							UserID: itm.UserID,
						}
						bookingUsers = append(bookingUsers, obj)
					}

					payload := structs.GetCentresForSlotBookingReq{
						SportId:           response.SportId,
						SlotId:            itm.SlotId,
						BookingDate:       itm.SlotDatetime.Seconds,
						BottomSheet:       req.BottomSheet,
						BookingUsers:      req.BookingUsers,
						ProductCategoryId: common.AcademyCategoryID,
					}
					params, _ := json.Marshal(payload)

					click_action := sushi.GetClickAction()

					deep_link := &sushi.Deeplink{
						URL:            util.GetCentersDeeplink(),
						PostbackParams: string(params),
					}
					click_action.SetDeeplink(deep_link)
					slot_obj.ClickAction = click_action

					otherVenuePayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
					otherVenuePayload["day"] = dayInterval
					otherVenuePayload["slot_id"] = itm.SlotId
					otherVenueTapEname := &sushi.EnameData{
						Ename: "academy_slot_other_venues_tap",
					}
					otherVenueEvents := sushi.NewClevertapEvents()
					otherVenueEvents.SetTap(otherVenueTapEname)
					trackItem := sushi.GetClevertapTrackItem(ctx, otherVenuePayload, otherVenueEvents)
					slot_obj.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
				}
			}

			if itm.AlreadyBooked {
				subtitle, _ := sushi.NewTextSnippet("booked")
				subtitle_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
				subtitle_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				subtitle.SetFont(subtitle_font)
				subtitle.SetColor(subtitle_color)

				slot_obj.SubTitle = subtitle
			}
		} else {

			out := map[string]interface{}{
				"fs_id":                     response.FsId,
				"slot_id":                   itm.SlotId,
				"booking_time":              itm.SlotDatetime.Seconds,
				"booking_users":             req.BookingUsers,
				"product_arena_category_id": PlayArenaAcademySkillUp,
				"product_category_id":       common.AcademyCategoryID,
			}

			payload, err := json.Marshal(out)
			if err != nil {
				log.Println("error in marshalling the payload data")
			}

			var tapEname *sushi.EnameData
			slotFooterPayload := s.GetClevertapTrackingDefaultPayload(ctx, response)
			slotFooterPayload["slot_id"] = itm.SlotId
			slotFooterPayload["day"] = dayInterval

			change_bottom_button_click_action := sushi.GetClickAction()
			var buttonText string
			if req.BottomSheet != 1 {
				payload := map[string]interface{}{
					"sport_id":                  req.SportId,
					"slot_id":                   itm.SlotId,
					"booking_users":             req.BookingUsers,
					"product_arena_category_id": PlayArenaAcademySkillUp,
					"booking_date":              itm.SlotDatetime.Seconds,
					"product_category_id":       common.AcademyCategoryID,
				}

				params, _ := json.Marshal(payload)
				deep_link := &sushi.Deeplink{
					URL:            util.GetTrialCentersPageDeeplink(),
					PostbackParams: string(params),
				}
				change_bottom_button_click_action.SetDeeplink(deep_link)
				buttonText = "Proceed to select center"

				slotFooterPayload["is_trial"] = 1
				tapEname = &sushi.EnameData{
					Ename: "academy_proceed_to_select_center_click",
				}
			} else {
				loggedInUser := util.GetUserIDFromContext(ctx)
				if loggedInUser > 0 {
					api_call_multi_action := &sushi.APICallAction{
						RequestType: sushi.POSTRequestType,
						URL:         "v2/booking/publish",
						Body:        string(payload),
					}

					buttonText = "Book free trial slot"
					change_bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
					change_bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
					slotFooterPayload["membership"] = "TAKE_TRIAL"
					slotFooterPayload["is_trial"] = 1
					tapEname = &sushi.EnameData{
						Ename: "academy_confirm_booking",
					}

				} else {
					auth_title, _ := sushi.NewTextSnippet("Please login to book your free trial. Enter your phone number to login using OTP.")
					buttonText = "Book free trial slot"
					payload := map[string]interface{}{
						"post_action":               "publish_booking",
						"fs_id":                     response.FsId,
						"booking_time":              itm.SlotDatetime.Seconds,
						"slot_id":                   itm.SlotId,
						"product_arena_category_id": PlayArenaAcademySkillUp,
						"product_category_id":       common.AcademyCategoryID,
					}

					postback_params, _ := json.Marshal(payload)
					auth := &sushi.Auth{
						Title:          auth_title,
						PostbackParams: string(postback_params),
						Source:         "slots",
					}
					change_bottom_button_click_action.SetAuth(auth)
				}
			}

			if len(buttonText) == 0 {
				buttonText = "Book selected slot"
			}

			button := &sushi.Button{
				Type:        sushi.ButtonTypeSolid,
				Text:        buttonText,
				ClickAction: change_bottom_button_click_action,
			}

			if tapEname != nil {
				footerButtonEvents := sushi.NewClevertapEvents()
				footerButtonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, slotFooterPayload, footerButtonEvents)
				button.AddClevertapTrackingItem(trackItem)
			}

			change_bottom_button := &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id2",
				Button:   button,
			}

			click_action, _ := sushi.NewTextClickAction("change_bottom_button")
			click_action.ChangeBottomButton = change_bottom_button
			slot_obj.ClickAction = click_action

		}
		slots_template_data = append(slots_template_data, *slot_obj)
	}
	*slotsTemplateData = slots_template_data
	return nil
}

func (s *AcademySlotPageTemplate) SetPageTracking(ctx context.Context, res *bookingPB.BookingSlotsV3Res) {
	payload := s.GetClevertapTrackingDefaultPayload(ctx, res)
	landingEname := &sushi.EnameData{
		Ename: "academy_slot_page_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}
}

func (s *AcademySlotPageTemplate) GetClevertapTrackingDefaultPayload(ctx context.Context, res *bookingPB.BookingSlotsV3Res) map[string]interface{} {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["sport_id"] = res.SportId
	payload["facility_id"] = res.FacilityId
	payload["source"] = "academy_trial_slots"

	return payload
}

func (s *AcademySlotPageTemplate) checkInfoContainerShow(bookingSlotResponse *bookingPB.BookingSlotsV3Res) bool {
	if len(bookingSlotResponse.BookingSlots) == 0 {
		return false
	}
	if bookingSlotResponse.FsId == 82 {
		return true
	}
	if bookingSlotResponse.HasOpenCourt != int32(1) {
		return false
	}
	showTimeObj := util.GetMonsoonAlertLastDate()
	if showTimeObj.Before(time.Now()) {
		return false
	}
	return true
}
