package bookingController

import (
	"context"
	"fmt"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	bookingModel "bitbucket.org/jogocoin/go_api/api/models/booking"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

// PublishBookinngResponse represents the response structure
type PublishBookingTemplate struct {
	Status     string                 `json:"status,omitempty"`
	ActionList []*bookingModel.Action `json:"action_list,omitempty"`
}

// PublishBookingV2C is the controller layer which calls booking service
func PublishBookingV2C(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	cl = util.GetBookingClient()

	failedStatus := model.StatusFailure(common.UNEXPECTED_ERROR)

	loggedInUser := util.GetUserIDFromContext(c)
	if loggedInUser == 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, failedStatus)
		return
	}

	publishBookingRequest := preparePublishBookingRequest(c)
	preBookingResponse, err := cl.ApplyPrebookingConditionsV2(ctx, publishBookingRequest)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, failedStatus)
		return
	}

	if preBookingResponse.Status != nil && preBookingResponse.Status.Status == common.FAILED {
		message := preBookingResponse.Status.Message
		resTemplate := failedResponse(ctx, publishBookingRequest, message)
		c.JSON(http.StatusOK, resTemplate)
		return
	}
	publishBookingRequest.SportId = preBookingResponse.SportId

	response, err := cl.PublishBookingV2(ctx, publishBookingRequest)
	if err != nil {
		fmt.Println(err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, failedStatus)
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		message := response.Status.Message
		resTemplate := failedResponse(ctx, publishBookingRequest, message)
		c.JSON(http.StatusOK, resTemplate)
		return
	}
	c.JSON(http.StatusOK, successResponse(ctx, publishBookingRequest, response))
}

func preparePublishBookingRequest(c *gin.Context) *bookingPB.PublishBookingRequestV2 {

	var json structs.PublishBookingV2
	json = c.MustGet("jsonData").(structs.PublishBookingV2)

	var bookingUsersData []*bookingPB.BookingUser
	for _, bookingUser := range json.BookingUsers {
		bookingUserDataPb := &bookingPB.BookingUser{
			UserID: bookingUser.UserID,
		}
		bookingUsersData = append(bookingUsersData, bookingUserDataPb)
	}
	return &bookingPB.PublishBookingRequestV2{
		SlotID:                 json.SlotID,
		FSID:                   json.FSID,
		BookingUsers:           bookingUsersData,
		BookingTime:            json.BookingTime,
		ProductArenaCategoryId: json.ProductArenaCategoryId,
		Source:                 json.Source,
		ProductCategoryId:      json.ProductCategoryId,
	}
}

func NewPublishBookingTemplate() *PublishBookingTemplate {
	return &PublishBookingTemplate{}
}

func title(text string, success bool) *sushi.TextSnippet {
	fontWeight := sushi.FontSemiBold
	if success {
		fontWeight = sushi.FontMedium
	}
	title, _ := sushi.NewTextSnippet(text)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	font, _ := sushi.NewFont(fontWeight, sushi.FontSize700)
	title.SetColor(color)
	title.SetFont(font)
	return title
}

func message(text string) *sushi.TextSnippet {
	message, _ := sushi.NewTextSnippet(text)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	message.SetFont(font)
	return message
}

func positiveAction(text string, bookingReferenceNumber string) *sushi.Button {
	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetText(text)
	clickAction := &sushi.ClickAction{
		Type: sushi.ClickActionDeeplink,
	}
	clickAction.SetDeeplink(&sushi.Deeplink{URL: util.GetBookingDetailsDeeplink(bookingReferenceNumber)})
	button.ClickAction = clickAction
	return button
}

func image(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (pbt *PublishBookingTemplate) setStatus(status string) {
	pbt.Status = status
}

func failedResponse(
	ctx context.Context,
	req *bookingPB.PublishBookingRequestV2,
	errorMessage string,
) *PublishBookingTemplate {
	response := NewPublishBookingTemplate()
	response.setStatus("failure")

	customAlert := &sushi.CustomAlert{
		Title:   title("Booking failed!", false),
		Message: message(errorMessage),
		Image:   image("https://fitso-images.curefit.co/file_assets/failure_image.json"),
		AutoDismissData: &sushi.AutoDismissData{
			Time: 4,
		},
		ClevertapTracking: getFailureClevertapPayload(ctx, req, errorMessage),
		JumboTracking:     []*jumbo.Item{getJumboFailedTrackItem(ctx, req, errorMessage)},
		DismissAfterAction: true,
	}
	customAlertAction := &bookingModel.Action{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	response.ActionList = append(response.ActionList, customAlertAction)
	return response
}

func getJumboFailedTrackItem(ctx context.Context, req *bookingPB.PublishBookingRequestV2, errorMessage string) *jumbo.Item {
	jumboPayload := make(map[string]interface{})

	jumboPayload["source_page"] = req.Source

	if req.Source == jumbo.SourcePageHomeTrial || req.Source == jumbo.SourcePageSportTrial || req.Source == jumbo.SourcePageFacilityTrial {
		jumboPayload["is_trial"] = true
	}

	userIds := []int32{}
	for _, val := range req.BookingUsers {
		userIds = append(userIds, val.UserID)
	}

	jumboPayload["status"] = common.FAILED
	jumboPayload["reason"] = errorMessage
	jumboPayload["booking_user_ids"] = userIds

	jumboEvents := jumbo.NewEvents()
	jumboImpressionEvent := jumbo.GetEventNameObject(jumbo.BookingPublishResult)
	jumboEvents.SetImpression(jumboImpressionEvent)
	jumboEvent := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	jumboEvent.SetPayload(jumboPayload)
	jumboEvent.SetEventNames(jumboEvents)

	return jumboEvent
}

func successResponse(
	ctx context.Context,
	req *bookingPB.PublishBookingRequestV2,
	res *bookingPB.PublishBookingResponseV2,
) *PublishBookingTemplate {
	bookingReferenceNumber := res.BookingReferenceNumber
	response := NewPublishBookingTemplate()
	response.setStatus("success")

	customAlert := &sushi.CustomAlert{
		Title:          title("Your session is booked", true),
		IsBlocking:     true,
		PositiveAction: positiveAction("See details", bookingReferenceNumber),
		Image:          image("https://fitso-images.curefit.co/file_assets/success_image.json"),
		AutoDismissData: &sushi.AutoDismissData{
			Time:              3,
			DismissActionType: "positive",
		},
		DismissAfterAction: true,
		ClevertapTracking: getSuccessClevertapPayload(ctx, req, res),
		JumboTracking:     []*jumbo.Item{getJumboSuccessTrackItem(ctx, req, res)},
		AppsflyerTracking: getSuccessAppsflyerTrackItem(ctx, req),
	}

	action := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	bookingDismissAction := &bookingModel.Action{
		Type:           sushi.ActionTypeBookingDismiss,
		BookingDismiss: action,
	}
	response.ActionList = append(response.ActionList, bookingDismissAction)
	return response
}

func getJumboSuccessTrackItem(ctx context.Context, req *bookingPB.PublishBookingRequestV2, res *bookingPB.PublishBookingResponseV2) *jumbo.Item {
	jumboPayload := make(map[string]interface{})

	jumboPayload["source_page"] = req.Source

	if req.Source == jumbo.SourcePageHomeTrial || req.Source == jumbo.SourcePageSportTrial || req.Source == jumbo.SourcePageFacilityTrial {
		jumboPayload["is_trial"] = true
	}

	userIds := []int32{}
	for _, val := range req.BookingUsers {
		userIds = append(userIds, val.UserID)
	}

	jumboPayload["status"] = common.SUCCESS
	jumboPayload["reference_number"] = res.BookingReferenceNumber
	jumboPayload["booking_user_ids"] = userIds

	jumboEvents := jumbo.NewEvents()
	jumboImpressionEvent := jumbo.GetEventNameObject(jumbo.BookingPublishResult)
	jumboEvents.SetImpression(jumboImpressionEvent)
	jumboEvent := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	jumboEvent.SetPayload(jumboPayload)
	jumboEvent.SetEventNames(jumboEvents)

	return jumboEvent
}

func getSuccessClevertapPayload(
	ctx context.Context,
	req *bookingPB.PublishBookingRequestV2,
	res *bookingPB.PublishBookingResponseV2,
) []*sushi.ClevertapItem {

	trackItems := make([]*sushi.ClevertapItem, 0)
	usersCommonPayload := getCleverTapCommonPayload(ctx, req)
	ename := "publish_booking_status"
	if req.ProductCategoryId == common.AcademyCategoryID {
		ename = "academy_publish_booking_status"
	}
	impressionEname := &sushi.EnameData{
		Ename: ename,
	}

	for _, val := range usersCommonPayload {
		val["booking_status"] = common.SUCCESS
		val["booking_reference_number"] = res.BookingReferenceNumber
		val["source"] = req.Source

		alertEvents := sushi.NewClevertapEvents()
		alertEvents.SetImpression(impressionEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, val, alertEvents)
		trackItems = append(trackItems, trackItem)
	}
	return trackItems
}

func getFailureClevertapPayload(
	ctx context.Context,
	req *bookingPB.PublishBookingRequestV2,
	errorMessage string,
) []*sushi.ClevertapItem {

	trackItems := make([]*sushi.ClevertapItem, 0)
	usersCommonPayload := getCleverTapCommonPayload(ctx, req)
	ename := "publish_booking_status"
	if req.ProductCategoryId == common.AcademyCategoryID {
		ename = "academy_publish_booking_status"
	}
	impressionEname := &sushi.EnameData{
		Ename: ename,
	}

	for _, val := range usersCommonPayload {
		val["booking_status"] = common.FAILED
		val["reason"] = errorMessage
		val["source"] = req.Source

		alertEvents := sushi.NewClevertapEvents()
		alertEvents.SetImpression(impressionEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, val, alertEvents)
		trackItems = append(trackItems, trackItem)
	}
	return trackItems
}

func getCleverTapCommonPayload(ctx context.Context, req *bookingPB.PublishBookingRequestV2) []map[string]interface{} {
	userIds := make([]int32, 0)
	for _, val := range req.BookingUsers {
		userIds = append(userIds, val.UserID)
	}
	if len(userIds) == 0 {
		userIds = append(userIds, util.GetUserIDFromContext(ctx))
	}

	trackPayloads := make([]map[string]interface{}, 0)
	for _, userID := range userIds {
		commonPayload := make(map[string]interface{})
		commonPayload["user_id"] = userID
		commonPayload["slot_id"] = req.SlotID
		commonPayload["fs_id"] = req.FSID
		commonPayload["booking_time"] = req.BookingTime
		trackPayloads = append(trackPayloads, commonPayload)
	}
	return trackPayloads
}

func getSuccessAppsflyerTrackItem(
	ctx context.Context,
	req *bookingPB.PublishBookingRequestV2,
) []*sushi.AppsflyerItem {
	var ename string
	//var trialEventName string
	trackPayload := make(map[string]interface{})
	userID := util.GetUserIDFromContext(ctx)
	trackPayload["user_id"] = userID

	if req.Source == jumbo.SourcePageHomeTrial || req.Source == jumbo.SourcePageSportTrial || req.Source == jumbo.SourcePageFacilityTrial {
		cityID := util.GetCityIDFromContext(ctx)
		sportID := req.SportId
		if cityID > 0 && sportID > 0 {
			cityName := common.CityIdCityNameMap[cityID]
			sportName := common.SportIdSportNameMap[sportID]
			ename = fmt.Sprintf("%s_%s_trial_publish_booking_success", sportName, cityName)
		} else if cityID > 0 {
			cityName := common.CityIdCityNameMap[cityID]
			ename = fmt.Sprintf("%s_trial_publish_booking_success", cityName)
		} else if sportID > 0 {
			sportName := common.SportIdSportNameMap[sportID]
			ename = fmt.Sprintf("%s_trial_publish_booking_success", sportName)
		} else {
			ename = "trial_publish_booking_success"
		}
		ename = "trial_booking"
		//trialEventName = "trial_publish_booking_success"
	} else {
		ename = "publish_booking"
	}
	fmt.Println("getSuccessAppsflyerTrackItem event log", trackPayload)
	trackPayload["af_customer_user_id"] = userID
	/*if userID == 688673 {
		fmt.Println("getSuccessAppsflyerTrackItem test users", trialEventName, trackPayload)
		trackPayload["af_customer_user_id"] = userID
		trackPayload["test_payload"] = 666
		trialEventName = "test_event_trial"
	}*/

	impressionEname := &sushi.EnameData{
		Ename: ename,
	}
	alertEvents := sushi.NewAppsflyerEvents()
	alertEvents.SetImpression(impressionEname)
	trackItem := sushi.GetAppsflyerTrackItem(ctx, alertEvents)
	trackItem.SetPayload(trackPayload)

	var trackItems []*sushi.AppsflyerItem

	trackItems = append(trackItems, trackItem)

	/*if len(trialEventName) > 0 {
		impressionEname := &sushi.EnameData{
			Ename: trialEventName,
		}
		alertEvents := sushi.NewAppsflyerEvents()
		alertEvents.SetImpression(impressionEname)
		trackItem := sushi.GetAppsflyerTrackItem(ctx, alertEvents)
		trackItem.SetPayload(trackPayload)
		trackItems = append(trackItems, trackItem)
	}*/

	return trackItems
}
