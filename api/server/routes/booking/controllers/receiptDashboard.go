package bookingController

import (
	"log"
	"net/http"

	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func GetAcademyFacilitiesSlotsCapacityC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetFacilitiesSlotsCapacityRequest)
	bookingClient := util.GetBookingClient()

	response, err := bookingClient.GetAcademyFacilitiesSlotsCapacity(ctx, &bookingPB.GetAcademyFacilitiesSlotsCapacityRequest{
		ProductId:     json.ProductId,
		PlanStartDate: json.PlanStartDate,
		UserId:        json.UserId,
	})
	if err != nil {
		log.Printf("Error in GetAcademyFacilitiesSlotsCapacityC: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	c.JSON(http.StatusOK, response)
}

func GetSummerCampFacilitiesSlotsCapacityC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	json := c.MustGet("jsonData").(structs.GetFacilitiesSlotsCapacityRequest)
	bookingClient := util.GetBookingClient()

	response, err := bookingClient.GetSummerCampFacilitiesSlotsCapacity(ctx, &bookingPB.GetAcademyFacilitiesSlotsCapacityRequest{
		ProductId:     json.ProductId,
		PlanStartDate: json.PlanStartDate,
		UserId:        json.UserId,
	})
	if err != nil {
		log.Printf("Error in GetSummerCampFacilitiesSlotsCapacityC: %v", err)
		c.JSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.UNEXPECTED_ERROR))
		return
	}
	c.JSON(http.StatusOK, response)
}
