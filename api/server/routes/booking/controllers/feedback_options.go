package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	statusModels "bitbucket.org/jogocoin/go_api/api/models"
	models "bitbucket.org/jogocoin/go_api/api/models/booking/feedback"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

const (
	TextMandatoryFromRating int32 = 3
)

// FeedbackPage represents the response structure of feedback page
type feedbackPageTemplate struct {
	Response       *bookingPB.BookingDetailsResponse `json:"-"`
	Header         *models.Header                    `json:"header,omitempty"`
	Footer         *sushi.FooterSnippetType2Layout   `json:"footer,omitempty"`
	BookingInfo    *models.BookingInfo               `json:"booking_info,omitempty"`
	RatingData     *models.RatingData                `json:"rating_data,omitempty"`
	InputFieldData *models.InputFieldData            `json:"input_field_data,omitempty"`
	CenterAddress  *sushi.TextSnippet                `json:"center_address,omitempty"`
	TagConfig      *models.TagConfig                 `json:"tag_config,omitempty"`
	BackAction     *models.BackAction                `json:"back_action,omitempty"`
}

func GetBookingFeedbackOptionsV2C(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.BookingFeedbackOptions
	json = c.MustGet("jsonData").(structs.BookingFeedbackOptions)
	ctx := util.PrepareGRPCMetaData(c)
	reqData := &bookingPB.BookingFeedbackRequest{
		BookingId: json.BookingId,
	}

	response, err := cl.GetFeedbackOptionsV2(ctx, reqData)
	if err != nil {
		log.Println("Error In getting feedback options", err)
		c.JSON(http.StatusInternalServerError, statusModels.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	statusCode := http.StatusOK
	if response.Status != nil && response.Status.Status == common.FAILED {
		statusCode = http.StatusInternalServerError
		log.Printf("Api: Feedback Options, Failed request, Error: %v", response.Status.Message)
		c.JSON(statusCode, response)
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		statusCode = http.StatusBadRequest
		log.Printf("Api: Feedback Options, Bad request, Error: %v", response.Status.Message)
		c.JSON(statusCode, response)
		return
	}
	template := feedbackPageTemplate{
		Response: response,
	}
	template.SetHeaders(ctx)
	template.SetBookingInfo(ctx)
	template.SetCenterAddress(ctx)
	template.SetRatingData(ctx)
	template.SetInputFieldData(ctx)
	template.SetFooter(ctx)
	template.SetTagConfig(ctx)
	template.SetBackAction()
	c.JSON(statusCode, template)
}

func (b *feedbackPageTemplate) SetHeaders(ctx context.Context) {

	title, _ := sushi.NewTextSnippet("Feedback")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)

	header := &models.Header{
		Title:   title,
		BgColor: bgColor,
	}
	b.Header = header
}

func (b *feedbackPageTemplate) SetBookingInfo(ctx context.Context) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.SectionHeaderType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	title, _ := sushi.NewTextSnippet(b.Response.Sport.SportName)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(color)
	title.SetFont(font)

	bookingTimeObj := time.Unix(b.Response.Bookings[0].BookingTime.Seconds, 0)
	bookingYear, bookingMonth, bookingDay := bookingTimeObj.Date()
	bookingMonthStr := bookingMonth.String()
	timeStr := bookingTimeObj.Format("03:04 PM")

	subtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d %s %d, %s", bookingDay, bookingMonthStr[:3], bookingYear, timeStr))
	font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	subtitle.SetColor(color)
	subtitle.SetFont(font)

	rightImage, _ := sushi.NewImage(b.Response.Sport.Icon)
	rightImage.SetHeight(44)
	rightImage.SetWidth(44)
	rightImage.SetType(sushi.ImageTypeCircle)

	var sportBgColor *sushi.Color
	if val, ok := common.SportIdBackgroundColorMap[b.Response.Sport.SportId]; ok {
		sportBgColor, _ = sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
	} else {
		sportBgColor, _ = sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint200)
	}
	rightImage.SetColor(sportBgColor)

	sectionHeaderType1 := &sushi.SectionHeaderType1Snippet{
		Title:      title,
		Subtitle:   subtitle,
		RightImage: rightImage,
	}
	bookingInfo := &models.BookingInfo{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: sectionHeaderType1,
	}

	b.BookingInfo = bookingInfo
}

func (b *feedbackPageTemplate) SetCenterAddress(ctx context.Context) {

	title, _ := sushi.NewTextSnippet(b.Response.Facility.DisplayName)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	title.SetFont(font)
	title.SetColor(color)

	b.CenterAddress = title
}

func (b *feedbackPageTemplate) SetRatingData(ctx context.Context) {

	ratingObjects := make([]*models.RatingObject, 0)
	for _, feedback := range b.Response.Ratings {
		tags := make([]*models.Tags, 0)
		for _, option := range feedback.CategoryOptions {
			title, _ := sushi.NewTextSnippet(option.Title)
			font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			title.SetFont(font)
			title.SetColor(color)

			tag := &models.Tags{
				Id:         strconv.Itoa(int(option.FeedbackOptionId)),
				Title:      title,
				IsSelected: b.Response.UserFeedback.FeedbackOptionSelected[option.FeedbackOptionId],
			}

			tags = append(tags, tag)
		}
		//shuffling tags here
		rand.Shuffle(len(tags), func(i, j int) {
			tags[i], tags[j] = tags[j], tags[i]
		})

		title, _ := sushi.NewTextSnippet(feedback.Question)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		title.SetFont(font)
		title.SetColor(color)
		tagDataObject := &models.TagDataObject{
			Tags:  tags,
			Title: title,
		}
		var textMandatory bool
		if feedback.RatingId <= TextMandatoryFromRating {
			textMandatory = true
		}
		titleFeedback, _ := sushi.NewTextSnippet(feedback.HighlightText)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
		ratingObject := &models.RatingObject{
			TagData:       tagDataObject,
			BgColor:       bgColor,
			BorderColor:   borderColor,
			Title:         titleFeedback,
			Value:         feedback.RatingId,
			TextMandatory: textMandatory,
		}

		ratingObjects = append(ratingObjects, ratingObject)
	}
	ratingData := &models.RatingData{
		Rating:       b.Response.UserFeedback.RatingId,
		Alignment:    "left",
		RatingConfig: ratingObjects,
	}
	b.RatingData = ratingData
}

func (b *feedbackPageTemplate) GetCleverTapCommonPayload(ctx context.Context) map[string]interface{} {

	optionTapPayload := make(map[string]interface{})
	optionTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	optionTapPayload["booking_id"] = b.Response.Bookings[0].BookingId
	optionTapPayload["sport_id"] = b.Response.Sport.SportId
	optionTapPayload["facility_id"] = b.Response.Facility.FacilityId
	optionTapPayload["booking_time"] = b.Response.Bookings[0].BookingTime.Seconds
	optionTapPayload["source"] = "feedback"
	return optionTapPayload
}

func (b *feedbackPageTemplate) GetClevertapItem(ctx context.Context, tapEname *sushi.EnameData, tapPayload map[string]interface{}) *sushi.ClevertapItem {
	feedbackEvents := sushi.NewClevertapEvents()
	feedbackEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, feedbackEvents)
	return trackItem
}

func (b *feedbackPageTemplate) SetInputFieldData(ctx context.Context) {

	title, _ := sushi.NewTextSnippet("Could you please tell us more")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	alertText, _ := sushi.NewTextSnippet("Minimum 10 characters are mandatory")
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	color, _ = sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint600)
	alertText.SetFont(font)
	alertText.SetColor(color)

	inputFieldData := &models.InputFieldData{
		Title:           title,
		AlertText:       alertText,
		ReviewText:      b.Response.UserFeedback.Note,
		MaxLength:       500,
		PlaceholderText: "Leave a descriptive review about your booking experience to help other users make better choices",
	}

	b.InputFieldData = inputFieldData
}

func (b *feedbackPageTemplate) SetFooter(ctx context.Context) {

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	out := map[string]interface{}{
		"booking_id":               b.Response.Bookings[0].BookingId,
		"booking_reference_number": b.Response.Bookings[0].BookingReferenceNumber,
	}

	payload, err := json.Marshal(out)
	if err != nil {
		log.Println("error in marshaling the payload data")
	}

	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v2/booking/saveBookingFeedbackV2",
		Body:        string(payload),
	}

	bottom_button_click_action := sushi.GetClickAction()
	bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
	bottom_button_click_action.ApiCallMultiAction = api_call_multi_action

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Type:             "solid",
		Size:             sushi.FooterButtonSizeLarge,
		Text:             "Submit Feedback",
		IsActionDisabled: 0,
		BgColor:          bgColor,
		ClickAction:      bottom_button_click_action,
	}
	submitFeedbackPayload := b.GetCleverTapCommonPayload(ctx)
	tapEname := &sushi.EnameData{
		Ename: "submit_feedback",
	}
	trackItem := b.GetClevertapItem(ctx, tapEname, submitFeedbackPayload)
	buttonItem1.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	items = append(items, buttonItem1)
	footerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	footerSnippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	b.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       footerLayout,
		FooterSnippetType2: footerSnippet,
	}
}

func (b *feedbackPageTemplate) SetTagConfig(ctx context.Context) {

	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	selected := &models.TagConfigs{
		Color:       color,
		BgColor:     bgColor,
		BorderColor: borderColor,
	}

	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	bgColor, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	borderColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	unselected := &models.TagConfigs{
		Color:       color,
		BgColor:     bgColor,
		BorderColor: borderColor,
	}

	tagConfig := &models.TagConfig{
		Selected:   selected,
		Unselected: unselected,
	}
	b.TagConfig = tagConfig
}

func (b *feedbackPageTemplate) SetBackAction() {
	title, _ := sushi.NewTextSnippet("You haven’t submitted")
	titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	title.SetColor(titleColor)
	title.SetFont(titleFont)

	message, _ := sushi.NewTextSnippet("The feedback will be discarded, still want to go back?")
	messageColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	messageFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message.SetColor(messageColor)
	message.SetFont(messageFont)

	positiveAction, _ := sushi.NewButton(sushi.ButtontypeText)
	positiveAction.SetText("Discard feedback")
	positiveClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionGoBack)
	positiveAction.SetClickAction(positiveClickAction)

	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Complete feedback")
	negativeActionColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	negativeAction.SetColor(negativeActionColor)
	negativeClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionSubmitFeedback)
	negativeAction.SetClickAction(negativeClickAction)

	b.BackAction = &models.BackAction{
		Type: sushi.ActionTypeCustomAlert,
		CustomAlert: &sushi.CustomAlert{
			Title:          title,
			Message:        message,
			PositiveAction: positiveAction,
			NegativeAction: negativeAction,
			DismissAfterAction: true,
		},
	}
}
