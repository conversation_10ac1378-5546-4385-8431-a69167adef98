package bookingController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	centreModel "bitbucket.org/jogocoin/go_api/api/models/booking/centres"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/render"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	ptypes "github.com/golang/protobuf/ptypes"
)

type CentrePageData struct {
	SubscriptionDetails   *productPB.GetUserSubscriptionStatusResponse
	TrialDetails          *userPB.TrialDetailsResponse
	IsPremiumUser         bool
	IsTrialUser           bool
	ContainsGuestUsers    bool
	SportId               int32
	ChildSummerCampStatus *userPB.ChildSummerCampStatus
}

// AvailableCentreTemplate represents the response structure of available centres page
type AvailableCentreTemplate struct {
	Header            *centreModel.Header                  `json:"header,omitempty"`
	CustomHeader      *centreModel.CustomHeader            `json:"custom_header,omitempty"`
	ProgressBarData   *centreModel.ProgressBarStruct       `json:"progress_bar_data,omitempty"`
	Results           *[]sushi.CustomTextSnippetTypeLayout `json:"results,omitempty"`
	Footer            *sushi.FooterSnippetType2Layout      `json:"footer"`
	PageData          CentrePageData                       `json:"-"`
	ClevertapTracking []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item                        `json:"jumbo_tracking,omitempty"`
}

func GetFitsoCentresForSlotBookingC(c *gin.Context) {
	cl = util.GetBookingClient()

	var json structs.GetCentresForSlotBookingReq
	ctx := util.PrepareGRPCMetaData(c)

	if util.GetAppTypeFromContext(ctx) == featuresupport.Android {
		jsonV2 := c.MustGet("jsonData").(structs.GetCentresForSlotBookingReqV2)

		var bookingUsers []structs.BookingUser
		for _, elem := range jsonV2.BookingUsers {
			bookingUser := structs.BookingUser{
				UserID: int32(elem.UserID),
			}
			bookingUsers = append(bookingUsers, bookingUser)
		}

		json = structs.GetCentresForSlotBookingReq{
			SportId:                jsonV2.SportId,
			SlotId:                 jsonV2.SlotId,
			BookingDate:            jsonV2.BookingDate,
			BottomSheet:            jsonV2.BottomSheet,
			BookingUsers:           bookingUsers,
			Source:                 jsonV2.Source,
			ProductArenaCategoryId: jsonV2.ProductArenaCategoryId,
		}
	} else {
		json = c.MustGet("jsonData").(structs.GetCentresForSlotBookingReq)
	}

	var bookingCount int32
	var userIds []int32
	for _, data := range json.BookingUsers {
		if data.UserID > 0 {
			userIds = append(userIds, data.UserID)
		}
	}
	if len(json.BookingUsers) > 0 {
		bookingCount = int32(len(json.BookingUsers))
	} else {
		bookingCount = 1
	}

	template := AvailableCentreTemplate{}
	template.SetUserSubscriptionStatus(ctx)

	reqData := &bookingPB.CentresForSlotBookingReq{
		SportId:                json.SportId,
		SlotId:                 json.SlotId,
		BookingCount:           bookingCount,
		BookingDate:            json.BookingDate,
		ProductArenaCategoryId: json.ProductArenaCategoryId,
		UserIds:                userIds,
	}
	if template.PageData.IsPremiumUser {
		reqData.IsPremiumUser = true
	}

	centreForSlotBooking, err := cl.GetFitsoCentresForSlotBooking(ctx, reqData)
	if err != nil {
		log.Println("func:GetFitsoCentresForSlotBookingC, could not fetch booking centers details ---", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if centreForSlotBooking.Status != nil {
		if centreForSlotBooking.Status.Status == common.BAD_REQUEST {
			c.JSON(http.StatusBadRequest, model.StatusFailure(centreForSlotBooking.Status.Message))
			return
		}
		if centreForSlotBooking.Status.Status == common.NOT_AUTHORIZED {
			c.JSON(http.StatusUnauthorized, model.StatusFailure(centreForSlotBooking.Status.Message))
			return
		}
	}
	template.PageData.ContainsGuestUsers = SetContainsGuestForTracking(ctx, userIds, bookingCount, template.PageData.IsTrialUser)

	if template.PageData.IsTrialUser {
		template.SetUserTrialDetails(ctx)
	}

	if json.BottomSheet != 1 {
		template.SetHeaderSection(ctx, &json)
		if template.PageData.IsTrialUser {
			template.SetProgressBarSection(ctx)
		}
	} else {
		template.SetCustomHeaderSection(centreForSlotBooking, bookingCount)
	}
	if featuresupport.SupportsSeasonalPools(c) && json.SportId == common.SWIMMING_SPORT_ID {
		template.ResultSectionForSeasonalPools(ctx, &json, centreForSlotBooking)
	} else {
		template.ResultSection(ctx, &json, centreForSlotBooking)
	}
	template.SetFooterSection(&json, centreForSlotBooking)
	template.SetPageTracking(ctx, json.Source, centreForSlotBooking)

	render.Render(c, gin.H{"payload": template}, "index.html")
}

func (s *AvailableCentreTemplate) SetHeaderSection(ctx context.Context, req *structs.GetCentresForSlotBookingReq) {

	bgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	header := &centreModel.Header{
		BgColor: bgColor,
	}

	var title_text string
	if s.PageData.IsTrialUser {
		title_text = "Book free trial"

		var trialText string
		trialLeft := s.PageData.TrialDetails.TrialDetails.TrialSessionsLeft
		if trialLeft == 1 {
			trialText = fmt.Sprintf("%d free trial available", trialLeft)
		} else {
			trialText = fmt.Sprintf("%d free trials available", trialLeft)
		}

		tag, _ := sushi.NewTag(trialText)
		tagFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		tagColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		tagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint100)
		tagBorderColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint300)
		if featuresupport.SupportsNewColor(ctx) {
			tagColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			tagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint100)
			tagBorderColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint300)
		}

		tag.Title.SetFont(tagFont)
		tag.Title.SetColor(tagColor)
		tag.SetBgColor(tagBgColor)
		tag.SetBorderColor(tagBorderColor)

		header.Tag = tag
	} else {
		if req.ProductArenaCategoryId == sessionTypeSkillup {
			title_text = "Centers for skill up"
		} else if req.ProductArenaCategoryId == sessionTypeGuided {
			title_text = "Centers for Guided"
		} else if req.ProductArenaCategoryId == sessionTypeRegular {
			title_text = "Centers for Regular"
		} else if req.ProductArenaCategoryId == sessionTypeBuddyHour {
			if req.SportId == common.SWIMMING_SPORT_ID {
				title_text = "Centers for Leisure Swim"
			} else {
				title_text = "Centers for Buddy Hour"
			}
		} else {
			title_text = "Centers for Guided"
		}
	}

	title, _ := sushi.NewTextSnippet(title_text)
	title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	title.SetFont(title_font)
	title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetColor(title_color)

	header.Title = title
	s.Header = header
}

func (s *AvailableCentreTemplate) SetProgressBarSection(ctx context.Context) {
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	colorType := sushi.ColorTypeTeal
	if featuresupport.SupportsNewColor(ctx) {
		colorType = sushi.ColorTypeCyan
	}
	progressbarColor := &[]sushi.Color{
		sushi.Color{
			Type: colorType,
			Tint: sushi.ColorTint500,
		},
	}
	progressBarDetails := &centreModel.ProgressBarStruct{
		Progress:       100,
		BgColor:        bgColor,
		ProgressColors: progressbarColor,
	}
	s.ProgressBarData = progressBarDetails
}

func (s *AvailableCentreTemplate) SetCustomHeaderSection(response *bookingPB.CentresForSlotBookingRes, bookingCount int32) {
	items := make([]sushi.TextSnippet, 0)

	sport_title, _ := sushi.NewTextSnippet(response.SportDetails.SportName)
	items = append(items, *sport_title)

	slot_title, _ := sushi.NewTextSnippet(response.SlotDetails.Timing)
	items = append(items, *slot_title)

	booking_count_title, _ := sushi.NewTextSnippet(fmt.Sprintf("%d person", bookingCount))
	items = append(items, *booking_count_title)

	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)

	leftImage, _ := sushi.NewImage(response.SportDetails.Icon)
	customHeaderType2 := &centreModel.CustomHeaderType2{
		Items:     &items,
		Color:     color,
		Font:      font,
		BgColor:   bgColor,
		LeftImage: leftImage,
	}

	customHeaderDetails := &centreModel.CustomHeader{
		Type:              centreModel.CustomHeaderType2Val,
		CustomHeaderType2: customHeaderType2,
	}
	s.CustomHeader = customHeaderDetails
}

func (s *AvailableCentreTemplate) SetFooterSection(req *structs.GetCentresForSlotBookingReq, response *bookingPB.CentresForSlotBookingRes) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	var buttonWeight []int32

	var buttonItem2Text string
	buttonItem2Text = "Select a center to proceed"
	buttonItem2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	buttonItem2BgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
	buttonItem2 := sushi.FooterSnippetType2ButtonItem{
		Type:             "solid",
		Text:             buttonItem2Text,
		Font:             buttonItem2Font,
		Id:               "bottom_button_id2",
		BgColor:          buttonItem2BgColor,
		IsActionDisabled: 1,
	}
	items = append(items, buttonItem2)

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationHorizontal,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData:   buttonData,
		ButtonWeight: buttonWeight,
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	s.Footer = footer
}

func (s *AvailableCentreTemplate) ResultSectionForSeasonalPools(ctx context.Context, req *structs.GetCentresForSlotBookingReq, response *bookingPB.CentresForSlotBookingRes) {
	var items []sushi.CustomTextSnippetTypeLayout
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	if response.CentresAvailable == true {
		for i, elem := range response.Centres {
			if s.PageData.IsTrialUser {
				if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && (elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_OPENING_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSING_SEASON) {
					continue
				}
			} else if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && (elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_OPENING_SEASON) {
				continue
			}
			if util.IsHighPeakSlotFacilitySport(ctx, elem.FsId) && s.PageData.IsPremiumUser != true {
				continue
			}
			// setting facility title
			facility_title, _ := sushi.NewTextSnippet(elem.DisplayName)
			facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			facility_title.SetFont(facility_title_font)
			facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			facility_title.SetColor(facility_title_color)

			// setting facility subtitle
			facility_subtitle, _ := sushi.NewTextSnippet(elem.SubzoneName)
			facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			facility_subtitle.SetFont(facility_subtitle_font)
			facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			facility_subtitle.SetColor(facility_subtitle_color)

			// setting facility tag
			facility_distance_tag_title := "-"
			if elem.Distance != 0 {
				facility_distance_tag_title = fmt.Sprintf("%.1f km", elem.Distance)
			}
			facility_tag_title, _ := sushi.NewTextSnippet(facility_distance_tag_title)
			facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
			facility_tag_title.SetFont(facility_tag_title_font)
			facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			facility_tag_title.SetColor(facility_tag_title_color)

			// setting facility tag bg color
			facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

			tag := &sushi.Tag{
				Title:   facility_tag_title,
				BgColor: facility_tag_bgcolor,
			}

			// setting facility rating title
			facility_rating_title, _ := sushi.NewTextSnippet(elem.Rating)
			facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			facility_rating_title.SetFont(facility_rating_title_font)
			facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
			facility_rating_title.SetColor(facility_rating_title_color)
			facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
			facility_rating_title.SetPrefixIcon(ratingIcon)

			// setting facility rating bg color
			facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if isNewColorSupported {
				facility_rating_bgcolor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			}

			ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

			tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG}
			if sharedFunc.ContainsString(tags, elem.Tag) {
				ratingTitle, _ := sushi.NewTextSnippet(elem.Tag)
				ratingSnippetBlockItem.Title = ratingTitle
				if !isNewColorSupported {
					color := sushi.ColorTypeBlue
					if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
						color = sushi.ColorTypeTeal
					}
					ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)

					ratingSnippetBlockItem.BgColor = ratingBgColor
					ratingSnippetBlockItem.Size = sushi.RatingSize300
				} else {
					if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
						ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
						ratingSnippetBlockItem.BgColor = ratingBgColor
					} else {
						// gradient for new tag
						tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
						tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
						gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
						ratingSnippetBlockItem.Gradient = gradient
					}
				}
			} else {
				ratingSnippetBlockItem.Title = facility_rating_title
				ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
			}

			rating := ratingSnippetBlockItem

			facility_image := &sushi.Image{
				URL:         elem.DisplayPicture,
				AspectRatio: 1,
				Type:        sushi.ImageTypeRounded,
				Height:      64,
				Width:       64,
			}

			seasonalPoolMessage := ""
			facility_bottom_container := &sushi.BottomContainerSnippetItem{}
			if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && elem.SeasonalPoolInfo.CurrentSeason != common.SWIMMING_OPEN_SEASON {
				startDate, _ := ptypes.Timestamp(elem.SeasonalPoolInfo.StartDate)
				endDate, _ := ptypes.Timestamp(elem.SeasonalPoolInfo.EndDate)
				seasonalPoolMessage = GetSeasonalPoolMessageForPreferredPageList(startDate, endDate, elem.SeasonalPoolInfo.CurrentSeason)
				fbc_subtitle, _ := sushi.NewTextSnippet(seasonalPoolMessage)
				colorType := sushi.ColorTypeOrange
				if isNewColorSupported {
					colorType = sushi.ALERT_LIGHT_THEME
				}
				fbc_color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
				fbc_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
				color, _ := sushi.NewColor(colorType, sushi.ColorTint400)
				icon, _ := sushi.NewIcon(sushi.SwimmingIcon, color)
				fbc_subtitle.SetColor(fbc_color)
				fbc_subtitle.SetFont(fbc_font)
				fbc_subtitle.SetPrefixIcon(icon)
				facility_bottom_container_swmming := &sushi.BottomContainerSnippetItem{
					SubTitle: fbc_subtitle,
				}
				facility_bottom_container = facility_bottom_container_swmming
			}
			out := map[string]interface{}{
				"fs_id":                     elem.FsId,
				"slot_id":                   req.SlotId,
				"booking_time":              req.BookingDate,
				"booking_users":             req.BookingUsers,
				"product_arena_category_id": elem.ProductArenaCategoryId,
				"source":                    req.Source,
			}

			payload, err := json.Marshal(out)
			if err != nil {
				log.Println("error in marshaling the payload data")
			}

			api_call_multi_action := &sushi.APICallAction{
				RequestType: sushi.POSTRequestType,
				URL:         "v2/booking/publish",
				Body:        string(payload),
			}

			var button_text string
			var tapEname *sushi.EnameData

			footerButtonPayload := make(map[string]interface{})
			footerButtonPayload["fs_id"] = elem.FsId
			footerButtonPayload["sport_id"] = req.SportId
			footerButtonPayload["facility_id"] = elem.FacilityId
			footerButtonPayload["slot_id"] = req.SlotId
			footerButtonPayload["booking_date"] = req.BookingDate
			footerButtonPayload["source"] = req.Source
			footerButtonPayload["product_arena_category_id"] = elem.ProductArenaCategoryId
			footerButtonPayload["user_id"] = util.GetUserIDFromContext(ctx)

			bottom_button_click_action := sushi.GetClickAction()
			status := s.PageData.SubscriptionDetails.SubscriptionStatus

			tomorrowSubscription := s.PageData.SubscriptionDetails.TomorrowParentSubscription || s.PageData.SubscriptionDetails.TomorrowChildSubscription
			switch status {
			case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
				button_text = "Book selected center"
				footerButtonPayload["membership"] = "TAKE_TRIAL"
				footerButtonPayload["is_trial"] = 1
				bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				break
			case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
				button_text = "Book selected center"
				bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
				footerButtonPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				if s.PageData.ContainsGuestUsers {
					footerButtonPayload["contains_guests"] = 1
				}

				break
			case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
				if tomorrowSubscription {
					button_text = "Book selected center"
					bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
					bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
					footerButtonPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
					tapEname = &sushi.EnameData{
						Ename: "confirm_booking",
					}
				} else {
					button_text = "return to home page"
					deep_link := &sushi.Deeplink{
						URL: util.GetHomeDeeplink(),
					}
					bottom_button_click_action.SetDeeplink(deep_link)
				}
				break
			case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
				fallthrough
			case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
				button_text = "Buy membership to proceed"
				buy_for_me_deep_link := &sushi.Deeplink{
					URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID), //sending to purchase landing page till dec 2021
					//URL: util.GetBuyingForMeDeeplink(s.PageData.SubscriptionDetails.ProductId, false),
				}
				bottom_button_click_action.SetDeeplink(buy_for_me_deep_link)
				break
			}

			button := &sushi.Button{
				Type:        "solid",
				Text:        button_text,
				ClickAction: bottom_button_click_action,
			}

			if tapEname != nil {
				footerButtonEvents := sushi.NewClevertapEvents()
				footerButtonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, footerButtonPayload, footerButtonEvents)
				button.AddClevertapTrackingItem(trackItem)
			}

			button.AddJumboTrackingItem(s.getBookingPublishJumboTrackItem(ctx, footerButtonPayload))

			var change_bottom_button *sushi.ChangeBottomButton
			change_bottom_button = &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id2",
				Button:   button,
			}

			facility_click_action, _ := sushi.NewTextClickAction("change_bottom_button")
			facility_click_action.ChangeBottomButton = change_bottom_button

			facility_obj := sushi.V2ImageTextSnippetType31SnippetItem{
				Title:         facility_title,
				Subtitle:      facility_subtitle,
				Tag:           tag,
				Rating:        rating,
				Image:         facility_image,
				ClickAction:   facility_click_action,
				JumboTracking: []*jumbo.Item{s.getFacilitySelectJumboTrackItem(ctx, req.Source, elem.FacilityId)},
			}

			if elem.ShowRemainingSpots || elem.ProductArenaCategoryId == sessionTypeSkillup {
				remainingSpots := elem.RemainingCapacity
				var overlay_container_title_text string
				if remainingSpots == 1 {
					overlay_container_title_text = fmt.Sprintf("%d spot left", remainingSpots)
				} else {
					overlay_container_title_text = fmt.Sprintf("%d spots left", remainingSpots)
				}

				containerColorType := sushi.ColorTypeOrange

				overlay_container_title, _ := sushi.NewTextSnippet(overlay_container_title_text)
				overlay_container_title_color, _ := sushi.NewColor(containerColorType, sushi.ColorTint600)
				overlay_container_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
				overlay_container_title.SetColor(overlay_container_title_color)
				overlay_container_title.SetFont(overlay_container_title_font)
				prefix_icon_color, _ := sushi.NewColor(containerColorType, sushi.ColorTint400)
				prefix_icon, _ := sushi.NewIcon(sushi.ThunderIcon, prefix_icon_color)
				overlay_container_title.SetPrefixIcon(prefix_icon)
				overlay_container_title.SetAlignment(sushi.TextAlignmentCenter)

				overlay_container_bg_color, _ := sushi.NewColor(containerColorType, sushi.ColorTint100)
				overlay_container := &sushi.OverlayContainer{
					Title:   overlay_container_title,
					BgColor: overlay_container_bg_color,
				}

				facility_obj.OverlayContainer = overlay_container
			}

			if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true {
				facility_obj.BottomContainer = facility_bottom_container
			}

			facility_obj.ClickAction = facility_click_action

			title, _ := sushi.NewTextSnippet("Select your center")
			snippet := &sushi.V2ImageTextSnippetType31Snippet{
				Items: &[]sushi.V2ImageTextSnippetType31SnippetItem{facility_obj},
				Id:    "lockdown",
			}

			if i == 0 {
				snippet.Title = title
			}

			layoutConfig := &sushi.LayoutConfig{
				SnippetType:  sushi.V2ImageTextSnippetType31,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
			}

			item := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:             layoutConfig,
				V2ImageTextSnippetType31: snippet,
			}

			items = append(items, *item)
		}
	} else {
		title, _ := sushi.NewTextSnippet("Sorry! You just missed our last available centre for this time slot. Please select another slot to make a booking.")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)

		title.SetColor(color)
		title.SetFont(font)
		title.SetAlignment(sushi.TextAlignmentCenter)
		title.SetNumberOfLines(3)

		empty_obj := sushi.V2ImageTextSnippetType35SnippetItem{
			Title: title,
		}
		snippet := &sushi.V2ImageTextSnippetType35Snippet{
			Items: &[]sushi.V2ImageTextSnippetType35SnippetItem{empty_obj},
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType35,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType35: snippet,
		}

		items = append(items, *item)
	}

	s.Results = &items
}

func (s *AvailableCentreTemplate) getBookingPublishJumboTrackItem(ctx context.Context, bookingData map[string]interface{}) *jumbo.Item {
	payload := make(map[string]interface{})

	payload["source_page"] = bookingData["source"]

	if s.PageData.IsTrialUser {
		payload["is_trial"] = true
	}
	payload["sport_id"] = bookingData["sport_id"]
	payload["facility_id"] = bookingData["facility_id"]
	payload["slot_id"] = bookingData["slot_id"]
	payload["selected_product_arena_category_id"] = bookingData["product_arena_category_id"]

	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.BookingPublishButtonClick)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (s *AvailableCentreTemplate) getFacilitySelectJumboTrackItem(ctx context.Context, source string, facilityID int32) *jumbo.Item {
	payload := make(map[string]interface{})

	payload["source_page"] = source

	if s.PageData.IsTrialUser {
		payload["is_trial"] = true
	}
	payload["facility_id"] = facilityID

	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.FacilitySelectClick)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (s *AvailableCentreTemplate) ResultSection(ctx context.Context, req *structs.GetCentresForSlotBookingReq, response *bookingPB.CentresForSlotBookingRes) {
	var items []sushi.CustomTextSnippetTypeLayout

	if response.CentresAvailable == true {
		for i, elem := range response.Centres {
			if s.PageData.IsTrialUser {
				if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && (elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_OPENING_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSING_SEASON) {
					continue
				}
			} else if elem.SeasonalPoolInfo != nil && elem.SeasonalPoolInfo.Flag == true && (elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_CLOSE_SEASON || elem.SeasonalPoolInfo.CurrentSeason == common.SWIMMING_OPENING_SEASON) {
				continue
			}

			if util.IsHighPeakSlotFacilitySport(ctx, elem.FsId) && s.PageData.IsPremiumUser != true {
				continue
			}
			// setting facility title
			facility_title, _ := sushi.NewTextSnippet(elem.DisplayName)
			facility_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			facility_title.SetFont(facility_title_font)
			facility_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			facility_title.SetColor(facility_title_color)

			// setting facility subtitle
			facility_subtitle, _ := sushi.NewTextSnippet(elem.SubzoneName)
			facility_subtitle_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			facility_subtitle.SetFont(facility_subtitle_font)
			facility_subtitle_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
			facility_subtitle.SetColor(facility_subtitle_color)

			// setting facility tag
			facility_distance_tag_title := "-"
			if elem.Distance != 0 {
				facility_distance_tag_title = fmt.Sprintf("%.1f km", elem.Distance)
			}
			facility_tag_title, _ := sushi.NewTextSnippet(facility_distance_tag_title)
			facility_tag_title_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize50)
			facility_tag_title.SetFont(facility_tag_title_font)
			facility_tag_title_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
			facility_tag_title.SetColor(facility_tag_title_color)

			// setting facility tag bg color
			facility_tag_bgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)

			tag := &sushi.Tag{
				Title:   facility_tag_title,
				BgColor: facility_tag_bgcolor,
			}

			// setting facility rating title
			facility_rating_title, _ := sushi.NewTextSnippet(elem.Rating)
			facility_rating_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
			facility_rating_title.SetFont(facility_rating_title_font)
			facility_rating_title_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)
			facility_rating_title.SetColor(facility_rating_title_color)
			facility_rating_icon_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, facility_rating_icon_color)
			facility_rating_title.SetPrefixIcon(ratingIcon)

			// setting facility rating bg color
			facility_rating_bgcolor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)

			ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

			tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
			if sharedFunc.ContainsString(tags, elem.Tag) {
				ratingTitle, _ := sushi.NewTextSnippet(elem.Tag)
				color := sushi.ColorTypeBlue
				if elem.Tag == common.OPENING_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeTeal
				} else if elem.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeOrange
				}
				ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)

				ratingSnippetBlockItem.Title = ratingTitle
				ratingSnippetBlockItem.BgColor = ratingBgColor
				ratingSnippetBlockItem.Size = sushi.RatingSize300

			} else {
				ratingSnippetBlockItem.Title = facility_rating_title
				ratingSnippetBlockItem.BgColor = facility_rating_bgcolor
			}

			rating := ratingSnippetBlockItem

			facility_image := &sushi.Image{
				URL:         elem.DisplayPicture,
				AspectRatio: 1,
				Type:        sushi.ImageTypeRounded,
				Height:      64,
				Width:       64,
			}

			out := map[string]interface{}{
				"fs_id":                     elem.FsId,
				"slot_id":                   req.SlotId,
				"booking_time":              req.BookingDate,
				"booking_users":             req.BookingUsers,
				"product_arena_category_id": elem.ProductArenaCategoryId,
				"source":                    req.Source,
			}

			payload, err := json.Marshal(out)
			if err != nil {
				log.Println("error in marshaling the payload data")
			}

			api_call_multi_action := &sushi.APICallAction{
				RequestType: sushi.POSTRequestType,
				URL:         "v2/booking/publish",
				Body:        string(payload),
			}

			var button_text string
			var tapEname *sushi.EnameData

			footerButtonPayload := make(map[string]interface{})
			footerButtonPayload["fs_id"] = elem.FsId
			footerButtonPayload["sport_id"] = req.SportId
			footerButtonPayload["facility_id"] = elem.FacilityId
			footerButtonPayload["slot_id"] = req.SlotId
			footerButtonPayload["booking_date"] = req.BookingDate
			footerButtonPayload["source"] = req.Source
			footerButtonPayload["product_arena_category_id"] = elem.ProductArenaCategoryId
			footerButtonPayload["user_id"] = util.GetUserIDFromContext(ctx)

			bottom_button_click_action := sushi.GetClickAction()
			status := s.PageData.SubscriptionDetails.SubscriptionStatus
			tomorrowSubscription := s.PageData.SubscriptionDetails.TomorrowParentSubscription || s.PageData.SubscriptionDetails.TomorrowChildSubscription
			switch status {
			case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:
				button_text = "Book selected center"
				footerButtonPayload["membership"] = "TAKE_TRIAL"
				footerButtonPayload["is_trial"] = 1
				bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				break
			case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
				button_text = "Book selected center"
				bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
				bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
				footerButtonPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
				tapEname = &sushi.EnameData{
					Ename: "confirm_booking",
				}
				break
			case productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION:
				if tomorrowSubscription {
					button_text = "Book selected center"
					bottom_button_click_action.SetClickActionType(sushi.ApiCallMultiAction)
					bottom_button_click_action.ApiCallMultiAction = api_call_multi_action
					footerButtonPayload["membership"] = "PREMIUM_SUBSCRIPTION_ACTIVE"
					tapEname = &sushi.EnameData{
						Ename: "confirm_booking",
					}
				} else {
					button_text = "return to home page"
					deep_link := &sushi.Deeplink{
						URL: util.GetHomeDeeplink(),
					}
					bottom_button_click_action.SetDeeplink(deep_link)
				}
				break
			case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
				fallthrough
			case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
				button_text = "Buy membership to proceed"
				buy_for_me_deep_link := &sushi.Deeplink{
					URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID), //sending to purchase landing page till dec 2021
					//URL: util.GetBuyingForMeDeeplink(s.PageData.SubscriptionDetails.ProductId, false),
				}
				bottom_button_click_action.SetDeeplink(buy_for_me_deep_link)
				break
			}

			button := &sushi.Button{
				Type:        "solid",
				Text:        button_text,
				ClickAction: bottom_button_click_action,
			}

			if tapEname != nil {
				footerButtonEvents := sushi.NewClevertapEvents()
				footerButtonEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, footerButtonPayload, footerButtonEvents)
				button.AddClevertapTrackingItem(trackItem)
			}

			button.AddJumboTrackingItem(s.getBookingPublishJumboTrackItem(ctx, footerButtonPayload))

			var change_bottom_button *sushi.ChangeBottomButton
			change_bottom_button = &sushi.ChangeBottomButton{
				ButtonId: "bottom_button_id2",
				Button:   button,
			}

			facility_click_action, _ := sushi.NewTextClickAction("change_bottom_button")
			facility_click_action.ChangeBottomButton = change_bottom_button

			facility_obj := sushi.V2ImageTextSnippetType31SnippetItem{
				Title:         facility_title,
				Subtitle:      facility_subtitle,
				Tag:           tag,
				Rating:        rating,
				Image:         facility_image,
				ClickAction:   facility_click_action,
				JumboTracking: []*jumbo.Item{s.getFacilitySelectJumboTrackItem(ctx, req.Source, elem.FacilityId)},
			}

			if elem.ShowRemainingSpots || elem.ProductArenaCategoryId == sessionTypeSkillup {
				remainingSpots := elem.RemainingCapacity
				var overlay_container_title_text string
				if remainingSpots == 1 {
					overlay_container_title_text = fmt.Sprintf("%d spot left", remainingSpots)
				} else {
					overlay_container_title_text = fmt.Sprintf("%d spots left", remainingSpots)
				}

				overlay_container_title, _ := sushi.NewTextSnippet(overlay_container_title_text)
				overlay_container_title_color, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint600)
				overlay_container_title_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize50)
				overlay_container_title.SetColor(overlay_container_title_color)
				overlay_container_title.SetFont(overlay_container_title_font)
				prefix_icon_color, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint400)
				prefix_icon, _ := sushi.NewIcon(sushi.ThunderIcon, prefix_icon_color)
				overlay_container_title.SetPrefixIcon(prefix_icon)
				overlay_container_title.SetAlignment(sushi.TextAlignmentCenter)

				overlay_container_bg_color, _ := sushi.NewColor(sushi.ColorTypeOrange, sushi.ColorTint100)
				overlay_container := &sushi.OverlayContainer{
					Title:   overlay_container_title,
					BgColor: overlay_container_bg_color,
				}

				facility_obj.OverlayContainer = overlay_container
			}

			title, _ := sushi.NewTextSnippet("Select your center")
			snippet := &sushi.V2ImageTextSnippetType31Snippet{
				Items: &[]sushi.V2ImageTextSnippetType31SnippetItem{facility_obj},
				Id:    "lockdown",
			}

			if i == 0 {
				snippet.Title = title
			}

			layoutConfig := &sushi.LayoutConfig{
				SnippetType:  sushi.V2ImageTextSnippetType31,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
			}

			item := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:             layoutConfig,
				V2ImageTextSnippetType31: snippet,
			}

			items = append(items, *item)
		}
	} else {
		title, _ := sushi.NewTextSnippet("Sorry! You just missed our last available centre for this time slot. Please select another slot to make a booking.")
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)

		title.SetColor(color)
		title.SetFont(font)
		title.SetAlignment(sushi.TextAlignmentCenter)
		title.SetNumberOfLines(3)

		empty_obj := sushi.V2ImageTextSnippetType35SnippetItem{
			Title: title,
		}
		snippet := &sushi.V2ImageTextSnippetType35Snippet{
			Items: &[]sushi.V2ImageTextSnippetType35SnippetItem{empty_obj},
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType35,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:             layoutConfig,
			V2ImageTextSnippetType35: snippet,
		}

		items = append(items, *item)
	}

	s.Results = &items
}

func (s *AvailableCentreTemplate) SetUserSubscriptionStatus(ctx context.Context) *productPB.GetUserSubscriptionStatusResponse {
	if s.PageData.SubscriptionDetails != nil {
		return s.PageData.SubscriptionDetails
	}
	productClient := util.GetProductClient()
	subReqData := &productPB.GetUserSubscriptionStatusRequest{
		UserId:                    util.GetUserIDFromContext(ctx),
		ProductSuggestionRequired: true,
		CheckChildSub:             true,
	}
	subResponse, err := productClient.GetUserSubscriptionStatus(ctx, subReqData)
	if err != nil {
		log.Printf("Api: get centers for booking, function: GetUserSubscriptionStatus, Error: %v", err)
		return nil
	}
	tomorrowSubscription := subResponse.TomorrowParentSubscription || subResponse.TomorrowChildSubscription
	if (subResponse.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE) || (subResponse.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION && tomorrowSubscription) {
		s.PageData.IsPremiumUser = true
	} else {
		s.PageData.IsPremiumUser = false
	}

	if subResponse.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL {
		s.PageData.IsTrialUser = true
	} else {
		s.PageData.IsTrialUser = false
	}

	s.PageData.SubscriptionDetails = subResponse
	return s.PageData.SubscriptionDetails
}

func (s *AvailableCentreTemplate) SetUserTrialDetails(ctx context.Context) *userPB.TrialDetailsResponse {
	if s.PageData.TrialDetails != nil {
		return s.PageData.TrialDetails
	}
	userClient := util.GetUserServiceClient()
	userId := util.GetUserIDFromContext(ctx)
	trialDetails, err := userClient.GetTrialDetails(ctx, &userPB.UserRequest{UserId: userId})
	if err != nil {
		log.Printf("Api: get centers for booking, function: GetUserTrialDetails, Error: %v", err)
		return nil
	}
	s.PageData.TrialDetails = trialDetails
	return s.PageData.TrialDetails
}

func (s *AvailableCentreTemplate) SetPageTracking(ctx context.Context, source string, response *bookingPB.CentresForSlotBookingRes) {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	if response.Centres != nil && len(response.Centres) > 0 {
		payload["nearest_distance"] = response.Centres[0].Distance
		payload["total_centers"] = len(response.Centres)
	}
	status := s.PageData.SubscriptionDetails.SubscriptionStatus
	if status == productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL {
		payload["is_trial"] = 1
	}
	landingEname := &sushi.EnameData{
		Ename: "center_selection_landing",
	}
	landingEvents := sushi.NewClevertapEvents()
	landingEvents.SetPageSuccess(landingEname)
	landingTrackItem := sushi.GetClevertapTrackItem(ctx, payload, landingEvents)
	s.ClevertapTracking = []*sushi.ClevertapItem{landingTrackItem}

	jumboPayload := make(map[string]interface{})

	jumboPayload["source_page"] = source

	if s.PageData.IsTrialUser {
		jumboPayload["is_trial"] = true

		if s.PageData.TrialDetails != nil {
			jumboPayload["user_trial_available_count"] = s.PageData.TrialDetails.TrialDetails.TrialSessionsLeft
		}
	}

	var facilityIds []int32

	for _, center := range response.Centres {
		facilityIds = append(facilityIds, center.FacilityId)
	}

	jumboPayload["facility_ids"] = facilityIds

	if len(response.Centres) > 0 {
		jumboPayload["nearest_facility_distance"] = response.Centres[0].Distance
	}

	jumboEvents := jumbo.NewEvents()
	jumboImpressionEvent := jumbo.GetEventNameObject(jumbo.FacilitySelectPageview)
	jumboEvents.SetImpression(jumboImpressionEvent)
	jumboEvent := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	jumboEvent.SetPayload(jumboPayload)
	jumboEvent.SetEventNames(jumboEvents)

	s.JumboTracking = []*jumbo.Item{jumboEvent}
}
