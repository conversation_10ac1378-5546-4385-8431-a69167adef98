package bookingController

import (
	"context"
	"net/http"
	"strconv"
	"strings"
	"log"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type CancelBookTemplate struct {
	CancelData *CancelBookingData `json:"cancel_data,omitempty"`
}

type CancelBookingData struct {
	Type          sushi.ActionType      `json:"type,omitempty"`
	CancelSuccess *sushi.ResponseAction `json:"cancel_success,omitempty"`
	CustomAlert   *sushi.CustomAlert    `json:"custom_alert,omitempty"`
}

func CancelBookingV2C(c *gin.Context) {
	bcl := util.GetBookingClient()
	ctx := util.PrepareGRPCMetaData(c)
	userID := util.GetUserIDFromContext(ctx)
	template := &CancelBookTemplate{
		CancelData: &CancelBookingData{},
	}

	if userID == 0 {
		log.Printf("Unauthorized: Error in getting user id from context: %v", ctx)
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	json := c.MustGet("jsonData").(structs.BookingCancelRequest)
	bookingIdsStr := strings.Split(json.BookingIds, ",")
	bookingIds := make([]int32, 0)
	for _, val := range bookingIdsStr {
		if id, err := strconv.Atoi(val); err == nil {
			bookingIds = append(bookingIds, int32(id))
		}
	}

	bookingIds = util.DeduplicateSlice(bookingIds)

	requestData := &bookingPB.CancelBookingRequestV2{
		BookingIds: bookingIds,
	}

	response, err := bcl.CancelBookingV2(ctx, requestData)
	if err != nil {
		log.Printf("Unhandled Error in cancelling booking for user id: %d, err: %v", userID, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure("Something went wrong"))
		return
	}

	if response.Status != nil && response.Status.Status == common.NOT_AUTHORIZED {
		log.Printf("Unauthorized: Error returned in cancelling booking for user id: %d with failure message: %s", userID, response.Status.Message)
		c.AbortWithStatusJSON(http.StatusUnauthorized, model.StatusUnauthorized(common.UNEXPECTED_ERROR))
		return
	}

	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		log.Printf("Bad request: Error returned in cancelling booking for user id: %d with failure message: %s", userID, response.Status.Message)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(response.Status.Message))
		return
	}

	if response.Status != nil && response.Status.Status == common.FAILED {
		log.Printf("Failed: Error returned in cancelling booking for user id: %d with failure message: %s", userID, response.Status.Message)
		template.SetFailedTemplate(ctx, response)
		c.JSON(http.StatusOK, template)
		return
	}

	if response.Status != nil && response.Status.Status == common.SUCCESS {
		template.SetSuccessTemplate(ctx, response)
		c.JSON(http.StatusOK, template)
	}
}

func (c *CancelBookTemplate) SetFailedTemplate(ctx context.Context, res *bookingPB.CancelBookingResponseV2) {
	message := res.Status.Message
	title, _ := sushi.NewTextSnippet(message)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	autoDismiss := &sushi.AutoDismissData{
		DismissActionType: sushi.ActionTypePositive,
		Time:              3,
	}

	popupImage := getAnimatedImage("https://fitso-images.curefit.co/file_assets/failure_image.json")
	customAlert := &sushi.CustomAlert{
		Title:           title,
		IsBlocking:      true,
		Image:           popupImage,
		AutoDismissData: autoDismiss,
		DismissAfterAction: true,
	}

	cancelBookingPayload := c.GetCleverTapCommonPayload(ctx, res.CleverTapPayload)
	cancelBookingPayload["cancel_status"] = "failed"
	impressionEname := &sushi.EnameData{
		Ename: "cancel_booking_status",
	}

	cancelBookingEvents := sushi.NewClevertapEvents()
	cancelBookingEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, cancelBookingPayload, cancelBookingEvents)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	c.CancelData.Type = sushi.ActionTypeCustomAlert
	c.CancelData.CustomAlert = customAlert
}

func (c *CancelBookTemplate) SetSuccessTemplate(ctx context.Context, res *bookingPB.CancelBookingResponseV2) {
	title, _ := sushi.NewTextSnippet("Your booking is cancelled")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	popupImage := getAnimatedImage("https://fitso-images.curefit.co/file_assets/success_image.json")
	autoDismiss := &sushi.AutoDismissData{
		DismissActionType: sushi.ActionTypePositive,
		Time:              3,
	}

	customAlert := &sushi.CustomAlert{
		Title:           title,
		IsBlocking:      true,
		Image:           popupImage,
		AutoDismissData: autoDismiss,
		DismissAfterAction: true,
	}
	cancelBookingPayload := c.GetCleverTapCommonPayload(ctx, res.CleverTapPayload)
	cancelBookingPayload["cancel_status"] = "success"
	impressionEname := &sushi.EnameData{
		Ename: "cancel_booking_status",
	}

	cancelBookingEvents := sushi.NewClevertapEvents()
	cancelBookingEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, cancelBookingPayload, cancelBookingEvents)
	customAlert.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	responseAction := &sushi.ResponseAction{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	c.CancelData.Type = sushi.ActionTypeCancelSuccess
	c.CancelData.CancelSuccess = responseAction
}

func getAnimatedImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}

func (c *CancelBookTemplate) GetCleverTapCommonPayload(ctx context.Context, payload *bookingPB.CleverTapPayload) map[string]interface{} {

	commonPayload := make(map[string]interface{})
	commonPayload["user_id"] = util.GetUserIDFromContext(ctx)
	commonPayload["fs_id"] = payload.FsId
	commonPayload["booking_time"] = payload.BookingTime
	commonPayload["slot_id"] = payload.SlotId
	commonPayload["booking_reference_number"] = payload.BookingReferenceNumber
	commonPayload["source"] = "booking_details"
	return commonPayload
}
