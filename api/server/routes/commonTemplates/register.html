<!--register.html-->

<!--Embed the header.html template at this location-->
{{ template "header.html" .}}

<h1>Register</h1>

<div class="panel panel-default col-sm-6">
  <div class="panel-body">
    <!--If there's an error, display the error-->
    {{ if .ErrorTitle}}
    <p class="bg-danger">
      {{.ErrorTitle}}: {{.ErrorMessage}}
    </p>
    {{end}}
    <!--Create a form that POSTs to the `/u/register` route-->
    <form class="form" action="/u/register" method="POST">
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" class="form-control" id="username" name="username" placeholder="Username">
      </div>
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" name="password" class="form-control" id="password" placeholder="Password">
      </div>
      <button type="submit" class="btn btn-primary">Register</button>
    </form>
  </div>
</div>  

    
<!--Embed the footer.html template at this location-->
{{ template "footer.html" .}}