package experiment

import (
	"context"
)

type ExperimentResponse int

const (
	ControlGroup ExperimentResponse = iota + 1
	TestGroup1
	TestGroup2
)

// BookaTrialCTA runs an experiment to control the Book a free trial CTA on home & sports page.
func BookaTrialCTA(ctx context.Context, userID int32) ExperimentResponse {
	modUser := userID % 4
	//todo: what wiull happen to logged out user???????
	if modUser == 1 {
		return TestGroup1
	} else if modUser == 3 {
		return TestGroup2
	}
	return ControlGroup
}
