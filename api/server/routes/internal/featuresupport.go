package featuresupport

import (
	"context"
	"log"

	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	version "github.com/hashicorp/go-version"
)

const (
	Ios           = "fitso_ios"
	Android       = "fitso_android"
	WebFrontend   = "web_frontend"
	ClientCtx     = "client"
	AppVersionCtx = "version"
	CoachApp      = "coach_app"
)

// Constraint represent feature support constraint for a client
type Constraint struct {
	constraint version.Constraints
	client     string
}

// NewFeatureSupportConstraint returns feature support constraint for a client
func NewFeatureSupportConstraint(versionCheck string, client string) (Constraint, error) {
	constraint, err := version.NewConstraint(versionCheck)
	return Constraint{constraint: constraint, client: client}, err
}

func versionCompare(c *gin.Context, fsConstraints ...Constraint) bool {
	appVersion := c.Value("requestHeaders").(structs.Headers).AppVersion
	client := c.Value("requestHeaders").(structs.Headers).AppType

	if client == WebFrontend {
		return true
	}

	if len(appVersion) == 0 || len(client) == 0 {

		return false
	}

	currentVersion, err := version.NewVersion(appVersion)
	if err != nil {
		return false
	}
	for _, fsConstraint := range fsConstraints {
		if fsConstraint.client == client {
			return fsConstraint.constraint.Check(currentVersion)
		}
	}
	return false
}

func versionCompareV2(ctx context.Context, fsConstraints ...Constraint) bool {
	client := util.GetAppTypeFromContext(ctx)
	appVersion := util.GetAppVersionFromContext(ctx)

	if client == WebFrontend {
		return true
	}
	if len(appVersion) == 0 || len(client) == 0 {
		return false
	}

	currentVersion, err := version.NewVersion(appVersion)
	if err != nil {
		return false
	}
	for _, fsConstraint := range fsConstraints {
		if fsConstraint.client == client {
			return fsConstraint.constraint.Check(currentVersion)
		}
	}
	return false
}

// SupportsV2 returns true if the request is from new app
func SupportsV2(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Ios)
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Android)

	if err != nil {
		log.Printf("unable to creat constraint with error: %v", err)
		return false
	}

	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsNewLogin(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Ios)
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Android)

	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}

	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsNewNotification(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Ios)
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Android)

	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}

	return versionCompare(c, iosConstraint, androidConstraint)
}

func GetClientID(c *gin.Context) string {
	client := c.Value("requestHeaders").(structs.Headers).AppType
	return client
}

func SupportsPurchaseHistory(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Ios)
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Android)

	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}

	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsNewHomeSportSnippet(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Ios)

	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}

	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Android)

	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}

	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsNewPurchaseBenefitsSnippet(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Ios)
	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.0", Android)
	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}
	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsPurchaseCompletionSnippet(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.2", Ios)
	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.2", Android)
	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}
	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsMedicalForm(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.2", Ios)
	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.2", Android)
	if err != nil {
		log.Printf("unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsPlayArenaCategories(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.4", Ios)
	if err != nil {
		log.Printf("func:SupportsPlayArenaCategories, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.8", Android)
	if err != nil {
		log.Printf("func:SupportsPlayArenaCategories, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsSeasonalPools(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.5", Ios)
	if err != nil {
		log.Printf("func:SupportsSeasonalPools, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.9", Android)
	if err != nil {
		log.Printf("func:SupportsSeasonalPools, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsSeasonalPoolsUsingContext(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.5", Ios)
	if err != nil {
		log.Printf("func:SupportsSeasonalPools, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.0.9", Android)
	if err != nil {
		log.Printf("func:SupportsSeasonalPools, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsFullPageSlot(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.6", Ios)
	if err != nil {
		log.Printf("func:SupportsFullPageSlot, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.1", Android)
	if err != nil {
		log.Printf("func:SupportsFullPageSlot, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsRenewalTile(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.8", Ios)
	if err != nil {
		log.Printf("func:SupportsRenewalTile, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.2", Android)
	if err != nil {
		log.Printf("func:SupportsRenewalTile, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsSingleImageOnSlot(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.6", Ios)
	if err != nil {
		log.Printf("func:SupportsSingleImageOnSlot, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.2", Android)
	if err != nil {
		log.Printf("func:SupportsSingleImageOnSlot, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsRemainingSpotsFeature(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.7", Ios)
	if err != nil {
		log.Printf("func:SupportsRemainingSpotsFeature, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.2", Android)
	if err != nil {
		log.Printf("func:SupportsRemainingSpotsFeature, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsNewBookingDetailsPage(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.0.9", Ios)
	if err != nil {
		log.Printf("func:SupportsNewBookingDetailsPage, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.3", Android)
	if err != nil {
		log.Printf("func:SupportsNewBookingDetailsPage, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsAcademyFlowPage(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.0", Ios)
	if err != nil {
		log.Printf("func:SupportsAcademyFlowPage, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.0", Android)
	if err != nil {
		log.Printf("func:SupportsAcademyFlowPage, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func NotSupportsPurchaseFlowPage(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint("< 3.3.0", Ios)
	if err != nil {
		log.Printf("func:SupportsAcademyFlowPage, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint("< 3.3.0", Android)
	if err != nil {
		log.Printf("func:SupportsAcademyFlowPage, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsReferralAndEarnPage(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.1.0", Ios)
	if err != nil {
		log.Printf("func:SupportsReferralAndEarnPage, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.4", Android)
	if err != nil {
		log.Printf("func:SupportsReferralAndEarnPage, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsLaunchPagePopups(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.1.0", Ios)
	if err != nil {
		log.Printf("func:SupportsLaunchPagePopups, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.4", Android)
	if err != nil {
		log.Printf("func:SupportsLaunchPagePopups, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsAnimatedFeedbackAerobar(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.1.1", Ios)
	if err != nil {
		log.Printf("func:SupportsAnimatedFeedbackAerobar, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.5", Android)
	if err != nil {
		log.Printf("func:SupportsAnimatedFeedbackAerobar, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsMultipleFeaturedProducts(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.1.3", Ios)
	if err != nil {
		log.Printf("func:SupportsMultipleFeaturedProducts, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.6", Android)
	if err != nil {
		log.Printf("func:SupportsMultipleFeaturedProducts, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsTimerSuggestions(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.3", Ios)
	if err != nil {
		log.Printf("func:SupportsTimerSuggestions, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.7", Android)
	if err != nil {
		log.Printf("func:SupportsTimerSuggestions, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsRenewalTileV2(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.2", Ios)
	if err != nil {
		log.Printf("func:SupportsRenewalTileV2, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.1", Android)
	if err != nil {
		log.Printf("func:SupportsRenewalTileV2, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsContextSwitch(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.0", Ios)
	if err != nil {
		log.Printf("func:SupportsContextSwitch, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.0", Android)
	if err != nil {
		log.Printf("func:SupportsContextSwitch, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsThirdDaySlotsTransition(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.2", Ios)
	if err != nil {
		log.Printf("func:SupportsThirdDaySlotsTransition, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.1", Android)
	if err != nil {
		log.Printf("func:SupportsThirdDaySlotsTransition, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsSoldOutReserveForChild(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.3", Ios)
	if err != nil {
		log.Printf("func:SupportsSoldOutReserveForChild, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.2", Android)
	if err != nil {
		log.Printf("func:SupportsSoldOutReserveForChild, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsEmergencyContactDataValidation(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.0", Ios)
	if err != nil {
		log.Printf("func:SupportsEmergencyContactDataValidation, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.3", Android)
	if err != nil {
		log.Printf("func:SupportsEmergencyContactDataValidation, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsLoginRedirectForMultipleFeaturedProducts(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.1.3", Ios)
	if err != nil {
		log.Printf("func:SupportsLoginRedirectForMultipleFeaturedProducts, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.4", Android)
	if err != nil {
		log.Printf("func:SupportsLoginRedirectForMultipleFeaturedProducts, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsBringAGuest(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.3.0", Ios)
	if err != nil {
		log.Printf("func:SupportsBringAGuest, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.3.1", Android)
	if err != nil {
		log.Printf("func:SupportsBringAGuest, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsTimerSuggestionsV2(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.2.5", Ios)
	if err != nil {
		log.Printf("func:SupportsTimerSuggestionsV2, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.6", Android)
	if err != nil {
		log.Printf("func:SupportsTimerSuggestionsV2, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsPurchasePageDynamicBgImage(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.1.3", Ios)
	if err != nil {
		log.Printf("func:SupportsTimerSuggestionsV2, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.2.7", Android)
	if err != nil {
		log.Printf("func:SupportsTimerSuggestionsV2, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsMultipleFeaturedProductsHorizontalLayout(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.3.1", Ios)
	if err != nil {
		log.Printf("func:SupportsMultipleFeaturedProductsHorizontalLayout, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.1.6", Android)
	if err != nil {
		log.Printf("func:SupportsMultipleFeaturedProductsHorizontalLayout, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsAcademyPurchaseFlow(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.3.0", Ios)
	if err != nil {
		log.Printf("func:SupportsAcademyPurchaseFlow, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.3.0", Android)
	if err != nil {
		log.Printf("func:SupportsAcademyPurchaseFlow, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsFullPageCart(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 3.3.2", Ios)
	if err != nil {
		log.Printf("func:SupportsFullPageCart, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 3.3.4", Android)
	if err != nil {
		log.Printf("func:SupportsFullPageCart, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsNewColor(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.0.0", Ios)
	if err != nil {
		log.Printf("func:SupportsNewColor, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.0.0", Android)
	if err != nil {
		log.Printf("func:SupportsNewColor, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsGoogleLocation(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.0.2", Ios)
	if err != nil {
		log.Printf("func:SupportsGoogleLocation, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.0.1", Android)
	if err != nil {
		log.Printf("func:SupportsGoogleLocation, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsGoogleLocationV2(c *gin.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.0.2", Ios)
	if err != nil {
		log.Printf("func:SupportsGoogleLocationV2, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.0.1", Android)
	if err != nil {
		log.Printf("func:SupportsGoogleLocationV2, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompare(c, iosConstraint, androidConstraint)
}

func SupportsNoCostEMI(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.0.1", Ios)
	if err != nil {
		log.Printf("func:SupportsNoCostEMI, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.0.1", Android)
	if err != nil {
		log.Printf("func:SupportsNoCostEMI, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func IsWebRequest(ctx context.Context) bool {
	client, _ := ctx.Value("client").(string)
	if client == WebFrontend {
		return true
	}
	return false
}

func IsWebRequestV2(c *gin.Context) bool {
	client := c.Value("requestHeaders").(structs.Headers).AppType
	if client == WebFrontend {
		return true
	}
	return false
}

func SupportSingleKeyProduct(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.0.5", Ios)
	if err != nil {
		log.Printf("func:SupportSingleKey, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.0.2", Android)
	if err != nil {
		log.Printf("func:SupportSingleKey, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportAcademySeasonalPoolPopup(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.1.1", Ios)
	if err != nil {
		log.Printf("func:SupportAcademySeasonalPoolPopup, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.1.4", Android)
	if err != nil {
		log.Printf("func:SupportAcademySeasonalPoolPopup, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportNewCultAppPopup(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.1.1", Ios)
	if err != nil {
		log.Printf("func:SupportNewCultAppPopup, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.1.7", Android)
	if err != nil {
		log.Printf("func:SupportNewCultAppPopup, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}

func SupportsSummerCamp(ctx context.Context) bool {
	iosConstraint, err := NewFeatureSupportConstraint(">= 4.1.7", Ios)
	if err != nil {
		log.Printf("func:SupportsSummerCamp, [ios] unable to create constraint with error: %v", err)
		return false
	}
	androidConstraint, err := NewFeatureSupportConstraint(">= 4.2.0", Android)
	if err != nil {
		log.Printf("func:SupportsSummerCamp, [android] unable to create constraint with error: %v", err)
		return false
	}
	return versionCompareV2(ctx, iosConstraint, androidConstraint)
}
