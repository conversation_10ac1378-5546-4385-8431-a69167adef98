package routes

import (
	"context"
	"fmt"
	"time"

	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/render"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/broker"
	"github.com/micro/go-micro/config"

	authPB "bitbucket.org/jogocoin/go_api/api/proto/auth"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	fsPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	notifPB "bitbucket.org/jogocoin/go_api/api/proto/notification"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	purPB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
)

var (
	clBooking bookingPB.BookingService
	clFS      fsPB.FacilitySportService
	clPur     purPB.PurchaseService
	clUser    userPB.UserService
	clProduct productPB.ProductService
	clNotif   notifPB.NotificationService
	clAuth    authPB.AuthService
)

func HealthCheck(c *gin.Context) {

	fmt.Println("HealthCheck Staring...")

	checkMysqlConnection(c)
	checkKafkaConnection(c)
	checkRedisConnection(c)

	var serviceList structs.ServiceConfig
	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	pingBookingService(c, service, serviceList)
	pingFacilitySportService(c, service, serviceList)
	pingNotificationService(c, service, serviceList)
	pingProductService(c, service, serviceList)
	pingPurchaseService(c, service, serviceList)
	pingUserService(c, service, serviceList)
	//pingAuthService(c, service, serviceList)
	render.Render(c, gin.H{
		"title":   "health check",
		"payload": "success"}, "index.html")
}

func checkMysqlConnection(c *gin.Context) {
	// mysql connection healthCheck

	fmt.Println("Health Check -- Mysql")

	var db *gorm.DB
	db = c.MustGet("mysql").(*gorm.DB)

	if err := db.DB().Ping(); err != nil {
		fmt.Println("error while connecting to DB:", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Mysql Connection Error"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func checkKafkaConnection(c *gin.Context) {
	// kafka connection healthCheck

	fmt.Println("Health Check -- Kafka")

	kBroker := c.MustGet("kafka").(broker.Broker)

	if err := kBroker.Connect(); err != nil {
		fmt.Println("Kafka Broker Connect error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Kafka Broker Connection Error"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}

	fmt.Println("kBroker:", kBroker)

}

func checkRedisConnection(c *gin.Context) {
	// redis connection healthCheck

	fmt.Println("Health Check -- Redis")

	redisClient := c.MustGet("redis").(redis.Client)

	if _, err := redisClient.Ping().Result(); err != nil {
		fmt.Println("Redis Client Connect error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Redis Client Connect Error"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingBookingService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {
	// booking service connection health check

	fmt.Println("Health Check -- Booking")

	clBooking = bookingPB.NewBookingService(serviceList.Booking, service.Client())

	emptyObjB := &bookingPB.Empty{}

	timeTillContextDeadline := time.Now().Add(15 * time.Second)
	ctxb, ctxCancelFuncb := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncb()

	if response, err := clBooking.Ping(ctxb, emptyObjB); err != nil || response.Response != "Pong" {
		fmt.Println("Booking service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to Booking Service"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingFacilitySportService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {

	fmt.Println("Health Check -- FacilitySport")

	clFS = fsPB.NewFacilitySportService(serviceList.FacilitySport, service.Client())

	emptyObjFS := &fsPB.Empty{}
	timeTillContextDeadline := time.Now().Add(15 * time.Second)

	ctxfs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	if response, err := clFS.Ping(ctxfs, emptyObjFS); err != nil || response.Response != "Pong" {
		fmt.Println("Facility Sport service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to Facility Sport Service"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingNotificationService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {

	fmt.Println("Health Check -- Notification")

	clNotif = notifPB.NewNotificationService(serviceList.Notification, service.Client())

	emptyObjNot := &notifPB.Empty{}
	timeTillContextDeadline := time.Now().Add(15 * time.Second)

	ctxfs, ctxCancelFuncfs := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncfs()

	if response, err := clNotif.Ping(ctxfs, emptyObjNot); err != nil || response.Response != "Pong" {
		fmt.Println("Notification service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to Notification Service"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingProductService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {

	fmt.Println("Health Check -- Product")

	clProduct = productPB.NewProductService(serviceList.Product, service.Client())

	emptyObjPrd := &productPB.Empty{}
	timeTillContextDeadline := time.Now().Add(15 * time.Second)

	ctxprd, ctxCancelFuncpur := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncpur()

	if response, err := clProduct.Ping(ctxprd, emptyObjPrd); err != nil || response.Response != "Pong" {
		fmt.Println("Product service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to Product Service"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingPurchaseService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {

	fmt.Println("Health Check -- Purchase")

	clPur = purPB.NewPurchaseService(serviceList.Purchase, service.Client())

	emptyObjPur := &purPB.Empty{}
	timeTillContextDeadline := time.Now().Add(15 * time.Second)

	ctxpur, ctxCancelFuncpur := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncpur()

	if response, err := clPur.Ping(ctxpur, emptyObjPur); err != nil || response.Response != "Pong" {
		fmt.Println("Purchase service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to Purchase Service"
		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingUserService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {

	fmt.Println("Health Check -- User")

	clUser = userPB.NewUserService(serviceList.User, service.Client())

	emptyObjUsr := &userPB.Empty{}
	timeTillContextDeadline := time.Now().Add(15 * time.Second)

	ctxusr, ctxCancelFuncpur := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncpur()

	if response, err := clUser.Ping(ctxusr, emptyObjUsr); err != nil || response.Response != "Pong" {
		fmt.Println("User service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to User Service"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}

func pingAuthService(c *gin.Context, service micro.Service, serviceList structs.ServiceConfig) {

	fmt.Println("Health Check -- Auth")

	clAuth = authPB.NewAuthService(serviceList.Auth, service.Client())

	emptyObjAuth := &authPB.Empty{}
	timeTillContextDeadline := time.Now().Add(15 * time.Second)

	ctxusr, ctxCancelFuncpur := context.WithDeadline(context.Background(), timeTillContextDeadline)
	defer ctxCancelFuncpur()

	if response, err := clAuth.Ping(ctxusr, emptyObjAuth); err != nil || response.Response != "Pong" {
		fmt.Println("Auth service internal server error: ", err)
		meta := make(map[string]string)
		meta["httpStatusCode"] = "503"
		meta["errorMessage"] = "Unable to Connect to Auth Service"

		c.Error(err).SetType(gin.ErrorTypePublic).SetMeta(meta)
		panic(err.Error())
	}
}
