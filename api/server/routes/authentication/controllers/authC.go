// auth controller

package authController

import (
	"context"
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"

	"bitbucket.org/jogocoin/go_api/api/render"
	"github.com/gin-gonic/gin"
	"github.com/micro/go-micro"

	// "github.com/micro/go-plugins/wrapper/select/roundrobin"
	// "github.com/micro/go-micro/client"
	authPB "bitbucket.org/jogocoin/go_api/api/proto/auth"
	"github.com/micro/go-plugins/client/selector/shard"

	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/go-redis/redis"
	"github.com/micro/go-micro/config"
)

var (
	cl authPB.AuthService
)

func CreateC(c *gin.Context) {
	redisClient := c.MustGet("redis").(redis.Client)

	pong, err := redisClient.Ping().Result()
	fmt.Println("--------", pong, err)

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	// roundrobin call with no session affinity
	service := micro.NewService()

	pub := micro.NewPublisher(asycnServiceList.Authtest, service.Client())

	cl = authPB.NewAuthService(serviceList.Auth, service.Client())

	var json structs.Users

	json = c.MustGet("jsonData").(structs.Users)

	userData := &authPB.Usertest{
		Id:       json.Id,
		Name:     json.Name,
		Company:  json.Company,
		Email:    json.Email,
		Password: json.Password,
	}

	response, err := cl.Create(context.TODO(), userData, shard.Strategy(json.Id))

	if err := pub.Publish(context.TODO(), userData); err != nil {
		log.Println("pub err: ", err)
		return
	}

	if err != nil {
		fmt.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func CreateTokenC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	cl = authPB.NewAuthService(serviceList.Auth, service.Client())

	var json structs.TokenRequest
	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	json = c.MustGet("jsonData").(structs.TokenRequest)

	userData := &authPB.TokenRequest{
		UserId:     json.UserId,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}
	// session affinity based call
	response, err := cl.CreateToken(context.TODO(), userData)

	if err != nil {
		log.Println(err)
		panic(err)
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}

func ValidateTokenC(c *gin.Context) {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	cl = authPB.NewAuthService(serviceList.Auth, service.Client())

	var json structs.TokenRequest
	var headers structs.Headers
	json = c.MustGet("jsonData").(structs.TokenRequest)
	headers = c.MustGet("requestHeaders").(structs.Headers)

	userData := &authPB.Token{
		Token:      json.Token,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}
	// session affinity based call
	response, err := cl.ValidateToken(context.TODO(), userData)

	if err != nil {
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}

	render.Render(c, gin.H{
		"title":   "Home Page",
		"payload": response}, "index.html")
}
