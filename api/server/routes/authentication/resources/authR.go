// auth resource

package authResource

import (
	"fmt"

	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func CreateR(c *gin.Context) {
	validate = validator.New()

	var json structs.Users

	if err := c.ShouldBindJSON(&json); err != nil {
		fmt.Println("err", err)
	}

	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func CreateTokenR(c *gin.Context) {
	validate = validator.New()

	var json structs.TokenRequest

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}
	fmt.Println("query data: ", json)
	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}

func ValidateTokenR(c *gin.Context) {
	validate = validator.New()

	var json structs.TokenRequest

	if err := c.ShouldBind(&json); err != nil {
		fmt.Println("err", err)
	}
	if err := validate.Struct(&json); err != nil {
		fmt.Println("err", err)
	}

	c.Set("jsonData", json)

	c.Next()
}
