// authRoutes.go

package authentication

import (
	authController "bitbucket.org/jogocoin/go_api/api/server/routes/authentication/controllers"
	authResource "bitbucket.org/jogocoin/go_api/api/server/routes/authentication/resources"
	"github.com/gin-gonic/gin"
)

func AuthRoute(routerVersion *gin.RouterGroup) {
	// Group auth related routes together

	authRoutes := routerVersion.Group("/auth")
	{
		authRoutes.POST("/create", authResource.CreateR, authController.CreateC)
		authRoutes.POST("/createToken", authResource.CreateTokenR, authController.CreateTokenC)
		authRoutes.POST("/validateToken", authResource.ValidateTokenR, authController.ValidateTokenC)

	}
}
