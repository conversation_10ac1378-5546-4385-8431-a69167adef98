// validate request

package sharedFunc

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"reflect"
	"strings"
)

/*
	validations:
		query:true/false
		body:true/false
		param:true/false
		forbidden: [list of keys]
		isRequired: true/false
		caseSensitive: true/false
		type:["string", "integer", "boolean", "map", "array", "date", "file"]
		allow:[allowed values]
		items:[allowed data types for array]
*/

type FieldT struct {
	Name  string
	Value string
	Ktype string
	Err   string
}

func getFieldType(kv string) (f FieldT) {
	kvArr := strings.Split(kv, ":")

	if len(kvArr) != 2 {
		f.Err = "meta declaration error"
		return
	}

	f.Name = string(kvArr[0])
	switch fieldType := string(kvArr[1][0]); fieldType {
	case "B":
		f.Ktype = "boolean"
	default:
		f.Ktype = "string"
	}
	f.Value = string(kvArr[1][1:])
	return
}

func Validate(schema reflect.Type, c *gin.Context) (err error) {
	fmt.Println("Type is", schema.Name(), "and kind is", schema.Kind())

	switch schema.Kind() {
	case reflect.Array, reflect.Chan, reflect.Map, reflect.Ptr, reflect.Slice:
		err = Validate(schema.Elem(), c)

		if err != nil {
			var errString []string
			error := fmt.Errorf("error in ", "schema.Name()")
			return fmt.Errorf(strings.Join(append(errString, error.Error(), err.Error()), "\n"))
		}
	case reflect.Struct:
		for i := 0; i < schema.NumField(); i++ {
			field := schema.Field(i)

			if field.Tag == "" {
				error1 := fmt.Errorf("no tag defined for field#", i, " :", field.Name)
				return error1
			}

			// field meta based validation
			tags := strings.Split(string(field.Tag), "|")

			for _, value := range tags {
				fieldType := getFieldType(value)

				if fieldType.Err != "" {
					return fmt.Errorf(fieldType.Err)
				}

				switch fieldType.Name {
				case "query":
					if (fieldType.Value == "true") && c.Query(field.Name) == "" {
						return fmt.Errorf("following field missing in querystring:", field.Name)
					}
				}

			}

			if field.Tag != "" {
				fmt.Println("field#", i+1, "name is", field.Name, ", type is", field.Type.Name(), ",and kind is", field.Type.Kind(), ", meta is", field.Tag)
			}
		}
	}
	return nil
}
