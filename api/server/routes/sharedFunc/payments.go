package sharedFunc

import (
	"bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"github.com/gin-gonic/gin"
	"github.com/micro/go-micro/config"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
)

// ValidatePaymentsCallback validates whether call from payment service is legit or not
func ValidatePaymentsCallback(c *gin.Context) {
	jsonData := c.MustGet("jsonData").(url.Values)

	log.Print("[callback debug] data: ", jsonData)

	purchaseID := jsonData.Get("order_id")

	log.Print("[callback debug] purchase id: ", purchaseID)

	if purchaseID == "" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}

	if !util.IsInternalRequest(c) {
		log.Printf("invalid ip source for purchaseID: %s", purchaseID)

		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("request not coming from a valid source"))
		return
	}

	serviceType := config.Get("payments", "service_type").String("")

	receivedServiceType := jsonData.Get("service_type")

	log.Print("[callback debug] service type: ", receivedServiceType)

	if serviceType != receivedServiceType {
		log.Printf("invalid service type: %s, purchaseID: %s", receivedServiceType, purchaseID)

		c.AbortWithStatusJSON(http.StatusUnauthorized, models.StatusUnauthorized("you are not authorized"))
		return
	}
}

func ValidateRazorpayWebhook(c *gin.Context) {

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	
	data, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		log.Println("func:ValidateRazorpayWebhook, ioutil error")
		c.Abort()
		c.JSON(http.StatusInternalServerError, gin.H{"status": "error"})
		return
	}
	c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(data))

	razorpayHookSecret := config.Get("razorpay", "webhookSecret").String("")

	// Create a new HMAC by defining the hash type and the key (as byte array)
	h := hmac.New(sha256.New, []byte(razorpayHookSecret))

	// Write Data to it // data is already byte array
	h.Write(data)

	// Get result and encode as hexadecimal string
	sha := hex.EncodeToString(h.Sum(nil))

	log.Println("sha", sha)
	log.Println("RazorpaySignature", headers.RazorpaySignature)

	if sha != headers.RazorpaySignature {
		log.Println("func:ValidateRazorpayWebhook, signature did not match")
		c.Abort()
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
		return
	}
}
