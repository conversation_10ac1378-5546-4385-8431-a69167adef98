package sharedFunc

import (
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"context"
	"github.com/gin-gonic/gin"
	"log"
	"github.com/micro/go-micro/config"

	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"

)

func GetLoggedInUserAge(ctx context.Context) int32 {
	loggedInUser := util.GetUserIDFromContext(ctx)
	age := int32(0)
	if loggedInUser > 0 {
		userClient := util.GetUserServiceClient()
		request := &userPB.GetUserAgeRecordReq{
			UserId: loggedInUser,
		}
		response, err := userClient.GetUserAgeRecord(ctx, request)
		if err != nil {
			log.Printf("error in getting user's age: %v, userID: %d", err, loggedInUser)
			return age
		}
		return response.Age
	}
	return age
}

func CreateUserReferralMappingIfEligible(c *gin.Context) {

	var json structs.ReferralUserInfo
	json = c.MustGet("jsonData").(structs.ReferralUserInfo)

	ctx := util.PrepareGRPCMetaData(c)

	if json.ReferralInvite > 0 {
		pcl := util.GetProductClient()
		reqData := &productPB.UserReferralMapping{
			UserId:         json.ReferralInvite,
			ReferredUserId: util.GetUserIDFromContext(ctx),
		}

		_, err := pcl.CreateUserReferralMapping(ctx, reqData)
		if err != nil {
			log.Printf("func:CreateUserReferralMappingIfEligible, error in creating user referral mapping, err:%v", err)
		}
	}
	c.Next()
}

func UseGoogleLocationAPIV1(c *gin.Context) bool {
	locationProvider := int64(c.Value("requestHeaders").(structs.Headers).LocationProvider)
	useGoogleAPI := config.Get("static","enableGoogleMapsAPI").Bool(false)
	log.Printf("func UseGoogleLocationAPIV1: useGoogleAPI - %v, featuresupport.SupportsGoogleLocationV2 - %v, locationProvider - %d, featuresupport.IsWebRequestV2 - %v",useGoogleAPI,featuresupport.SupportsGoogleLocationV2(c),locationProvider,featuresupport.IsWebRequestV2(c))
	if useGoogleAPI && (featuresupport.SupportsGoogleLocationV2(c) || (featuresupport.IsWebRequestV2(c))){
		return true
	}
	return false
}

func UseGoogleLocationAPIV3(ctx context.Context) bool {
	useGoogleAPI := config.Get("static","enableGoogleMapsAPI").Bool(false)
	userId := util.GetUserIDFromContext(ctx)
	log.Printf("func UseGoogleLocationAPIV3: useGoogleAPI - %v, featuresupport.SupportsGoogleLocation - %v, userId - %d, locationProvider - %d, featuresupport.IsWebRequestV2 - %v",useGoogleAPI,featuresupport.SupportsGoogleLocation(ctx),userId,util.GetLocationProviderFromContext(ctx),featuresupport.IsWebRequest(ctx))
	if useGoogleAPI && (featuresupport.SupportsGoogleLocation(ctx) || (featuresupport.IsWebRequest(ctx))){
		return true
	}
	return false
}
