package sharedFunc

import (
	"fmt"
	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/timestamp"
	"strconv"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
)

const SECRET_KEY = "jogo_auth_token"

func GetProtobufTimeFromIntTimestamp(timestamp int64) *timestamp.Timestamp {

	localTime := time.Unix(timestamp, 0)

	protoTime, _ := ptypes.TimestampProto(localTime)

	return protoTime
}

func GetIntegerArrayFromCommaSeparatedString(c_s_string string) []int32 {

	valuesStringArray := strings.Split(c_s_string, ",")
	var valuesInt []int32

	for _, value := range valuesStringArray {
		vl, _ := strconv.Atoi(value)

		if vl > 0 {
			valuesInt = append(valuesInt, int32(vl))
		}
	}

	return valuesInt
}

func KeyExists(decoded map[string]interface{}, key string) bool {
	val, ok := decoded[key]
	return ok && val != nil
}

func DecodeData(encodedData string, userId *int32) {

	claims := jwt.MapClaims{}
	token, err := jwt.ParseWithClaims(encodedData, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(SECRET_KEY), nil
	})

	if err != nil {
		fmt.Println("Error in decoding user id")
		return
	}

	user, err := strconv.ParseInt(claims["user"].(string), 10, 64)
	*userId = int32(user)

	if err != nil {
		fmt.Println("Error in string conversion to integer: ", err)
		return
	}

	fmt.Println(token)

	return
}

func ContainsString(arrStr []string, str string) bool {
	for _, element := range arrStr {
		if element == str {
			return true
		}
	}
	return false
}

func ContainsInteger(arrInt []int32, val int32) bool {
	for _, element := range arrInt {
		if element == val {
			return true
		}
	}
	return false
}

func FindIntersectionForIntegerArrays(firstArr []int32, secondArr []int32) []int32 {
	commonIntegerArr := make([]int32, 0)
	for _, element := range firstArr {
		if ContainsInteger(secondArr, element) {
			commonIntegerArr = append(commonIntegerArr, element)
		}
	}
	return commonIntegerArr
}

func FilterIntegerArray(arrayToExclude []int32, arrayToFilter []int32) []int32 {
	filteredIntegerArr := make([]int32, 0)
	for _, element := range arrayToFilter {
		if !ContainsInteger(arrayToExclude, element) {
			filteredIntegerArr = append(filteredIntegerArr, element)
		}
	}
	return filteredIntegerArr
}
