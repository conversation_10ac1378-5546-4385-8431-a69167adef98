package sharedFunc

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"

	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"

	"bitbucket.org/jogocoin/go_api/api/server/routes/util"

	common "bitbucket.org/jogocoin/go_api/api/common"

	authPB "bitbucket.org/jogocoin/go_api/api/proto/auth"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"

	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/client/selector/shard"
)

var (
	cl  userPB.UserService
	acl authPB.AuthService
)

const (
	dev_datapeace_uuid   = "813e329c-c52f-4875-947d-dd1ccc5e08da"
	dev_datapeace_secret = "35a272765022483d9a83a84ce8e276f69e46ff4475f24ed6b13c095d16eb8a93"

	datapeace_uuid   = "be13a42d-a9ab-4cba-9515-f807e9410809"
	datapeace_secret = "e739d24b9d4843009e8551bbe1a73286de6a6c3e68a64402ad924261c3e3faf6"

	dev_datapeace_customhook_uuid   = "bbd1d115-1767-4967-a645-ebb33de0fdba"
	dev_datapeace_customhook_secret = "a3e8e04a51414774b4d365dd6151a3e531dca358c17f4ca686e768e23d426968"

	datapeace_customhook_uuid   = "884c6240-b577-4069-b300-ebdb22925255"
	datapeace_customhook_secret = "4e110314624d4e73b8ea8f2d6cc9af9c9a0cff3a8c0b4f6ab97572d37e2324ae"

	FITSO_LOCATION_PROVIDER int64 = 8
)

func ValidateTokenShared(c *gin.Context) {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	acl = authPB.NewAuthService(serviceList.Auth, service.Client())

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	userData := &authPB.Token{
		Token:      headers.AuthToken,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}

	if userData.AppType != common.USER_AUTH_WEB_APP_TYPE {
		return
	}
	response, err := acl.ValidateToken(context.TODO(), userData)

	if err != nil {
		log.Println("error in validating error : ", err)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}
	if !response.Valid || len(response.Errors) > 0 {
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	} else {
		c.Set("user", response)
	}
}

func ValidateDashboardUser(c *gin.Context) {
	authClient := util.GetAuthServiceClient()
	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)
	userData := &authPB.Token{
		Token:      headers.AuthToken,
		AppType:    headers.AppType,
		AppVersion: headers.AppVersion,
	}
	if userData.AppType == common.USER_AUTH_PARTNER_FACILITY_TYPE {
		return
	}

	if userData.AppType != featuresupport.CoachApp && userData.AppType != common.USER_AUTH_WEB_APP_TYPE {
		log.Println("error in validating headers for dashboard request : Invalid Headers")
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.BAD_REQUEST))
		return
	}
	response, err := authClient.ValidateToken(context.TODO(), userData)
	if err != nil {
		log.Println("error in validating token : ", err)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	}
	if !response.Valid || len(response.Errors) > 0 {
		log.Println("error in validating token : ",response)
		c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
		return
	} else {
		var serviceList structs.ServiceConfig

		config.Get("codeIndependent", "services").Scan(&serviceList)

		service := micro.NewService()
		cl = userPB.NewUserService(serviceList.User, service.Client())
		log.Println("ValidateDashboardUser: data User --- ", response, userData.AppType)
		if userData.AppType == common.USER_AUTH_WEB_APP_TYPE {
			request := &userPB.User {
				UserId: response.User.UserId,	
			}
			responsePermissions, err1 := cl.GetDashboardUserDetails(context.TODO(),request)
			if err1 != nil {
				log.Println("ValidateDashboardUser: error in validating token : ", err1)
				c.AbortWithStatusJSON(http.StatusUnauthorized, common.StatusFailed(common.UNAUTHORIZED))
				return
			}
			log.Println("ValidateDashboardUser: data res --- ", response.User.UserId,  responsePermissions)
			if responsePermissions != nil && responsePermissions.LoginDetails != nil && responsePermissions.LoginDetails.ToolPermissions != nil {
				log.Println("ValidateDashboardUser: data tool permissions ", response.User.UserId, responsePermissions)
				c.Set("tool_access", responsePermissions.LoginDetails.ToolPermissions)
			}
		}
		c.Set("user_id", response.User.UserId)
		c.Set("user", response)
	}
}

func Authorize(c *gin.Context) {
	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	cl = userPB.NewUserService(serviceList.User, service.Client())

	userData := &userPB.UserRequest{
		Token: c.Request.Header.Get("token"),
	}

	response, err := cl.ValidateToken(context.TODO(), userData, shard.Strategy(userData.Token))

	if err != nil {
		log.Println("error in authorizing  : ", err)
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
		c.Abort()
		return
	}

	if !response.Valid || len(response.Errors) > 0 {
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
		c.Abort()
		return
	} else {
		c.Set("user", response)
	}
}

func ValidateDataPeaceWebhook(c *gin.Context) {

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	if headers.ServicehookUuid == datapeace_uuid {

		data, err := c.GetRawData()
		fmt.Println("ValidateDataPeaceWebhook: test data", data)
		if err != nil {
			fmt.Println(err)
			c.Abort()
			c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
			return
		}

		// Create a new HMAC by defining the hash type and the key (as byte array)
		h := hmac.New(sha256.New, []byte(datapeace_secret))

		// Write Data to it // data is already byte array
		h.Write(data)

		// Get result and encode as hexadecimal string
		sha := hex.EncodeToString(h.Sum(nil))
		fmt.Println("ValidateDataPeaceWebhook: signature", headers.ServicehookSignature, sha)
		if sha != headers.ServicehookSignature {
			fmt.Println("Signature did not match")
			c.Abort()
			c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
			return
		}

		if headers.ServicehookEvent == "visit.checkin" || headers.ServicehookEvent == "before_checkin" {
			fmt.Println("Event Implementable !!")
		} else {
			fmt.Println("Event not implementedh", headers.ServicehookEvent)
			c.Abort()
			c.JSON(501, gin.H{"status": "event not implemented"})
			return
		}

		c.Set("bytedata", data)

	} else {
		c.Abort()
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
	}
}

func ValidateDataPeaceCustomhook(c *gin.Context) {

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	fmt.Println("-- headers -- ", headers)

	if headers.CustomhookUuid == datapeace_customhook_uuid {

		data, err := c.GetRawData()

		if err != nil {
			log.Println("Func: ValidateDataPeaceCustomhook, error in getting data from context: %v", err)
			c.Abort()
			c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
			return
		}

		// Create a new HMAC by defining the hash type and the key (as byte array)
		h := hmac.New(sha256.New, []byte(datapeace_customhook_secret))

		// Write Data to it // data is already byte array
		h.Write(data)

		// Get result and encode as hexadecimal string
		sha := hex.EncodeToString(h.Sum(nil))
		fmt.Println("sha", sha)

		if sha != headers.CustomhookSignature {
			fmt.Println("Signature did not match")
			c.Abort()
			c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
			return
		}

		if headers.CustomhookEvent == "before_checkin" {
			fmt.Println("Event Implementable !!")
		} else {
			fmt.Println("Event not implementedh")
			c.Abort()
			c.JSON(501, gin.H{"status": "event not implemented"})
			return
		}

		c.Set("bytedata", data)

	} else {
		fmt.Println("Invalid CustomhookUuid")
		c.Abort()
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
	}
}

func ValidateJWTToken(c *gin.Context) {

	var headers structs.Headers
	headers = c.MustGet("requestHeaders").(structs.Headers)

	fmt.Println("-- headers -- ", headers)

	tokenString := headers.AuthToken
	claims := jwt.MapClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte("jogo_jwt_secret"), nil
	})
	fmt.Println(token, err)
	var requestorId int

	// do something with decoded claims
	for key, val := range claims {
		if key == "userId" {
			requestorId = int(val.(float64))
		}
		fmt.Printf("Key: %v, value: %v\n", key, val)
	}

	if requestorId > 0 {

		headers.UserId = requestorId

		c.Set("requestHeaders", headers)
	} else {
		fmt.Println("User not found")
		c.Abort()
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
	}
}

// SetUserIDInContextFromToken takes token from request and passes userID in context
func SetUserIDInContextFromToken(c *gin.Context) {
	uCl := util.GetUserServiceClient()
	tokenData := &userPB.Token{
		Token:      c.Value("requestHeaders").(structs.Headers).AccessToken,
		AppVersion: c.Value("requestHeaders").(structs.Headers).AppVersion,
	}

	userTokenData, err := uCl.GetUserFromToken(c, tokenData)
	if err != nil {
		log.Printf("Error in getting user details for token: %s, err: %v", tokenData.Token, err)
	}
	var userID int32
	if err == nil {
		userID = userTokenData.UserId
	}

	c.Set("user_id", userID)
	c.Next()
}

func CheckLocationAuthKey(c *gin.Context) {

	key := c.Value("requestHeaders").(structs.Headers).FitsoLocationApiKey
	if key != config.Get("static", "fitsoLocationApiKey").String("abcd12") {
		c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
		c.Abort()
	}
}

func SetFitsoCityFromZomatoCityInContext(c *gin.Context) {
	cityId := int64(c.Value("requestHeaders").(structs.Headers).CityId)

	if UseGoogleLocationAPIV1(c) {
		c.Set("city_id", int32(cityId))
	}else{
		productService := util.GetProductClient()
		var fitsoCityId int32 = 0
		fitsoCityReq := &productPB.ZCityId{
			ZomatoCityId: cityId,
		}
		fitsoCityIdResponse, err := productService.GetFitsoCityIdFromZCityId(c, fitsoCityReq)
		if err == nil {
			fitsoCityId = fitsoCityIdResponse.FitsoCityId
		}
		c.Set("city_id", fitsoCityId)
	}
	c.Next()
}

func SetZoneIdInContextFromSubzoneId(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	productClient := util.GetProductClient()

	var zoneId int32 = 0
	res, err := productClient.GetZoneForGivenSubzone(ctx, &productPB.Empty{})
	if err == nil {
		zoneId = res.ZoneId
	}

	c.Set("zone_id", zoneId)
	c.Next()
}
