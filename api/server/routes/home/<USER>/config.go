package homeController

import (
	common "bitbucket.org/jogocoin/go_api/api/common"
	models "bitbucket.org/jogocoin/go_api/api/models"
	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	sushi "bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	util "bitbucket.org/jogocoin/go_api/api/server/routes/util"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"context"
	"github.com/gin-gonic/gin"
	ver "github.com/hashicorp/go-version"
	"log"
	"net/http"
)

const (
	NoUpdate    int32 = 1
	SoftUpdate  int32 = 2
	ForceUpdate int32 = 3
)

const (
	SourceHome            string = "home"
	SourceLogin           string = "login"
	INACTIVITY_POPUP_TIME int32  = 10000 // in miliseconds
)

type ConfigTemplate struct {
	Config          *homeModels.Config `json:"config,omitempty"`
	Facts           []string           `json:"facts,omitempty"`
	AlertType       int32              `json:"alert_type,omitempty"`
	Alert           *sushi.CustomAlert `json:"alert,omitempty"`
	InactivityTime  int32              `json:"inactivity_time,omitempty"`
	InactivityPopup *sushi.CustomAlert `json:"inactivity_popup,omitempty"`
	BackPressPopup  *sushi.CustomAlert `json:"backpress_popup,omitempty"`
}

const (
	KEY_FIREBASE_ANALYTICS_ENABLED  = "firebase_analytics_enabled"
	KEY_CLEVERTAP_ANALYTICS_ENABLED = "clevertap_analytics_enabled"
	KEY_FACEBOOK_ANALYTICS_ENABLED  = "facebook_analytics_enabled"
)

var enabledConsoles = map[string]bool{
	KEY_FIREBASE_ANALYTICS_ENABLED:  true,
	KEY_CLEVERTAP_ANALYTICS_ENABLED: true,
	KEY_FACEBOOK_ANALYTICS_ENABLED:  true,
}

func GetConfigC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	cityId := int32(c.Value("requestHeaders").(structs.Headers).CityId)
	configDetails := &homeModels.Config{
		SkipEnabled:           true,
		TermsUrl:              "https://www.getfitso.com/terms?force_browser=1",
		PrivacyUrl:            "https://www.getfitso.com/privacy?force_browser=1",
		LocationThresholdInKm: 1.0,
		EnabledConsoles:       enabledConsoles,
	}
	
	zomatoCityMap := map[int32]int32 {
		1 : 1,
		4 : 68,
		6 : 8, 
	}
	fitsoCityMap := map[int32]int32 {
		68 : 4,
		8  : 6,
	}
	configDetails.CityId = cityId
	if sharedFunc.UseGoogleLocationAPIV3(ctx) {
		configDetails.LocationApiKey = "abcd12"
		configDetails.LocationProvider = 8
		if val, ok := zomatoCityMap[cityId]; ok {
			configDetails.CityId = val
		}
	}else{
		if val, ok := fitsoCityMap[cityId]; ok {
			configDetails.CityId = val
		}	
	}
	
	pucl := util.GetPurchaseClient()
	provider, er := pucl.GetSupportedPaymentProvider(ctx, &purchasePB.Empty{})
	if er != nil {
		log.Printf("func:GetConfigC, error in getting supported payment provider, err:%v", er)
		c.AbortWithStatusJSON(
			http.StatusInternalServerError,
			models.StatusFailure(common.UNEXPECTED_ERROR),
		)
		return
	}

	configDetails.PaymentProvider = provider.PaymentProvider

	facilitySportService := util.GetFacilitySportClient()
	sportsFacts := make([]string, 0)
	sportsFactsRes, err := facilitySportService.GetSportsFacts(c, &facilitySportPB.Empty{})
	if err == nil {
		sportsFacts = sportsFactsRes.Facts
	} else {
		log.Printf("Function: GetConfigC, Error: %v", err)
	}
	loggedInUserId := util.GetUserIDFromContext(ctx)
	template := ConfigTemplate{}
	template.Config = configDetails
	template.Facts = sportsFacts
	if loggedInUserId <= 0 && featuresupport.SupportsLaunchPagePopups(ctx) {
		template.InactivityTime = INACTIVITY_POPUP_TIME
		template.InactivityPopup = customAlertForInactivityPopup(ctx)
		template.BackPressPopup = customAlertForBackpressPopup(ctx)
	}

	newUpdateAvailable := true
	if newUpdateAvailable {
		configData := &structs.SportsAppConfiguration{
			AppType:    util.GetAppTypeFromContext(ctx),
			AppVersion: util.GetAppVersionFromContext(ctx),
			UserId:     loggedInUserId,
		}
		
		updateType := GetAppUpdateType(configData)
		if updateType == SoftUpdate {
			template.AlertType = 1
			template.Alert = CustomAlertForSoftUpdate(ctx)
		} else if updateType == ForceUpdate {
			template.AlertType = 2
			template.Alert = CustomAlertForForceUpdate(ctx)
		}
	}

	c.JSON(http.StatusOK, template)
}

func GetAppUpdateType(configData *structs.SportsAppConfiguration) int32 {

	var minAppVersionForceUpdate, minAppVersionSoftUpdate string
	if configData.AppType == "fitso_android" {
		minAppVersionForceUpdate = "4.1.9"
		minAppVersionSoftUpdate = "4.0.9"
	} else if configData.AppType == "fitso_ios" {
		minAppVersionForceUpdate = "4.1.6"
		minAppVersionSoftUpdate = "4.0.8"
	}
	
	cv1, err1 := ver.NewVersion(configData.AppVersion)
	if err1 != nil {
		log.Println("error in parsing current app version: ", err1)
		return NoUpdate
	}

	cv2, err2 := ver.NewVersion(minAppVersionForceUpdate)
	if err2 != nil {
		log.Println("error in parsing minimum app version: ", err2)
		return NoUpdate
	}
	isForceUpdateRequired := bool(cv1.LessThanOrEqual(cv2))
	if isForceUpdateRequired {
		return ForceUpdate
	}
	cv3, err3 := ver.NewVersion(minAppVersionSoftUpdate)
	if err3 != nil {
		log.Println("error in parsing minimum app version: ", err3)
		return NoUpdate
	}
	isSoftUpdateRequired := bool(cv1.LessThan(cv3))
	if isSoftUpdateRequired {
		return SoftUpdate
	}
	return NoUpdate

}

func CustomAlertForSoftUpdate(ctx context.Context) *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("App update available")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("New features and fixes.")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.IsMarkdown = 1
	message.SetFont(message_font)
	message.SetColor(message_color)

	payload := make(map[string]interface{})
	if util.GetUserIDFromContext(ctx) > 0 {
		payload["user_id"] = util.GetUserIDFromContext(ctx)
	}

	tapEname := &sushi.EnameData{
		Ename: "softupdate_popup_update_tap",
	}
	tapEvents := sushi.NewClevertapEvents()
	tapEvents.SetTap(tapEname)

	skipTrackingItem := sushi.GetClevertapTrackItem(ctx, payload, tapEvents)

	pos_click_action, _ := sushi.NewTextClickAction("app_update")
	positiveAction := &sushi.Button{
		Type:              "text",
		Text:              "Update",
		ClickAction:       pos_click_action,
		ClevertapTracking: []*sushi.ClevertapItem{skipTrackingItem},
	}

	impressionEname := &sushi.EnameData{
		Ename: "softupdate_popup_impression",
	}
	impressionEvents := sushi.NewClevertapEvents()
	impressionEvents.SetImpression(impressionEname)
	trackingItem := sushi.GetClevertapTrackItem(ctx, payload, impressionEvents)

	custom_alert := &sushi.CustomAlert{
		Title:             title,
		Message:           message,
		PositiveAction:    positiveAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackingItem},
		DismissAfterAction: true,
	}

	return custom_alert
}

func CustomAlertForForceUpdate(ctx context.Context) *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("Update required!")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("There is a newer version of cult ACADEMY app available for download. Please update to continue")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)

	pos_click_action, _ := sushi.NewTextClickAction("app_update")
	positiveAction := &sushi.Button{
		Type:        "solid",
		Text:        "Update",
		ClickAction: pos_click_action,
	}

	custom_alert := &sushi.CustomAlert{
		Title:          title,
		Message:        message,
		PositiveAction: positiveAction,
		IsBlocking:     true,
		DismissAfterAction: true,
	}

	return custom_alert
}

func customAlertForBackpressPopup(ctx context.Context) *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("Leaving Already?")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint000)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("Explore home instead to check out \n unlimited sports at premium facilities")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)

	payload := make(map[string]interface{})

	pos_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionDismiss)
	pca_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	pca_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	exitTapEname := &sushi.EnameData{
		Ename: "login_page_backpress_popup_exit_tap",
	}
	exitTapEvents := sushi.NewClevertapEvents()
	exitTapEvents.SetTap(exitTapEname)
	exitTrackingItem := sushi.GetClevertapTrackItem(ctx, payload, exitTapEvents)

	positiveAction := &sushi.Button{
		Type:              "text",
		Size:              "large",
		Text:              "Exit",
		Font:              pca_font,
		Color:             pca_color,
		ClickAction:       pos_click_action,
		ClevertapTracking: []*sushi.ClevertapItem{exitTrackingItem},
	}
	neg_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSkipLogin)
	nca_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	nca_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

	skipTapEname := &sushi.EnameData{
		Ename: "login_page_backpress_popup_skip_to_home_tap",
	}
	skipTapEvents := sushi.NewClevertapEvents()
	skipTapEvents.SetTap(skipTapEname)
	skipTrackingItem := sushi.GetClevertapTrackItem(ctx, payload, skipTapEvents)

	negativeAction := &sushi.Button{
		Type:              "text",
		Size:              "large",
		Text:              "Skip to home",
		Font:              nca_font,
		Color:             nca_color,
		ClickAction:       neg_click_action,
		ClevertapTracking: []*sushi.ClevertapItem{skipTrackingItem},
	}

	impressionEname := &sushi.EnameData{
		Ename: "login_page_backpress_popup_impression",
	}
	impressionEvents := sushi.NewClevertapEvents()
	impressionEvents.SetImpression(impressionEname)
	trackingItem := sushi.GetClevertapTrackItem(ctx, payload, impressionEvents)

	custom_alert := &sushi.CustomAlert{
		Title:             title,
		Message:           message,
		PositiveAction:    positiveAction,
		NegativeAction:    negativeAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackingItem},
		DismissAfterAction: true,
	}

	return custom_alert
}

func customAlertForInactivityPopup(ctx context.Context) *sushi.CustomAlert {
	title, _ := sushi.NewTextSnippet("Still not sure?")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint000)
	title.SetFont(font)
	title.SetColor(color)

	message, _ := sushi.NewTextSnippet("Explore home instead to check out \n unlimited sports at premium facilities")
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)

	payload := make(map[string]interface{})

	pos_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSkipLogin)

	pca_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
	pca_color, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

	tapEname := &sushi.EnameData{
		Ename: "login_page_inactivity_popup_skip_to_home_tap",
	}
	tapEvents := sushi.NewClevertapEvents()
	tapEvents.SetTap(tapEname)
	skipTrackingItem := sushi.GetClevertapTrackItem(ctx, payload, tapEvents)

	positiveAction := &sushi.Button{
		Type:              "text",
		Size:              "large",
		Text:              "Skip to home",
		Font:              pca_font,
		Color:             pca_color,
		ClickAction:       pos_click_action,
		ClevertapTracking: []*sushi.ClevertapItem{skipTrackingItem},
	}

	impressionEname := &sushi.EnameData{
		Ename: "login_page_inactivity_popup_impression",
	}
	impressionEvents := sushi.NewClevertapEvents()
	impressionEvents.SetImpression(impressionEname)
	trackingItem := sushi.GetClevertapTrackItem(ctx, payload, impressionEvents)

	custom_alert := &sushi.CustomAlert{
		Title:             title,
		Message:           message,
		PositiveAction:    positiveAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackingItem},
		DismissAfterAction: true,
	}

	return custom_alert
}
