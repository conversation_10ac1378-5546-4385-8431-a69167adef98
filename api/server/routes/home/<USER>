package home

import (
	homeController "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	homeResource "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func HomeRouteV1(routerVersion *gin.RouterGroup) {
	homeRoutes := routerVersion.Group("/home")
	{
		homeRoutes.GET(
			"/",
			homeResource.GetHomeR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetZoneIdInContextFromSubzoneId,
			sharedFunc.CreateUserReferralMappingIfEligible,
			homeController.GetHomeDetails,
		)
		homeRoutes.GET(
			"/getConfig",
			sharedFunc.SetUserIDInContextFromToken,
			homeController.GetConfigC,
		)
		homeRoutes.GET(
			"/getAerobar",
			homeResource.GetAerobarR,
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.CreateUserReferralMappingIfEligible,
			homeController.GetAerobarC,
		)
		homeRoutes.POST(
			"/nearbyCenters",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			homeResource.GetNearbyFacilitiesR,
			homeController.GetNearbyFacilitiesC,
		)
	}
}

func HomeRouteV2(routerVersion *gin.RouterGroup) {
	homeRoutes := routerVersion.Group("/home")
	{
		homeRoutes.GET(
			"/",
			sharedFunc.SetUserIDInContextFromToken,
			sharedFunc.SetFitsoCityFromZomatoCityInContext,
			sharedFunc.SetZoneIdInContextFromSubzoneId,
			homeController.GetHomeDetailsV2C,
		)
		homeRoutes.POST(
			"/video",
			sharedFunc.ValidateDashboardUser,
			homeResource.CreateVideoR,
			homeController.CreateVideoC,
		)
	}
}
