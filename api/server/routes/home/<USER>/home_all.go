package homeController

import (
	"context"
	"fmt"
	"net/http"
	"encoding/json"

	common "bitbucket.org/jogocoin/go_api/api/common"
	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

type AllFitsoFeaturesPageTemplate struct {
	Sections                []*homeModels.ResultSection     `json:"results,omitempty"`
	Footer                  *sushi.FooterSnippetType2Layout `json:"footer,omitempty"`
	UnreadNotificationCount int32                           `json:"unread_notification_count"`
}

func GetAllFitsoFeaturesPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	template := &AllFitsoFeaturesPageTemplate{}
	template.AddSelectPreferenceSnippet(ctx)
	if util.CheckSummerCamp(c) {
		template.AddSummerCampSnippet(ctx)
	}
	template.AddAcademySnippet(ctx)
	// template.AddMasterkeySnippet(ctx)
	template.SetFooter(ctx)
	template.SetNotificationCount(ctx)
	c.JSON(http.StatusOK, template)
}

func (a *AllFitsoFeaturesPageTemplate) AddSection(section *homeModels.ResultSection) {
	a.Sections = append(a.Sections, section)
}

func (a *AllFitsoFeaturesPageTemplate) AddSelectPreferenceSnippet(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.TextSnippetType1,
	}
	title, _ := sushi.NewTextSnippet("Select your preference")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	title.SetColor(color)
	title.SetFont(font)

	subtitle1, _ := sushi.NewTextSnippet("Learn your favourite sport or book & play as you wish")
	color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	subtitle1.SetColor(color)
	subtitle1.SetFont(font)

	snippet := &sushi.TextSnippetType1Snippet{
		Title:     title,
		Subtitle1: subtitle1,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:     layoutConfig,
		TextSnippetType1: snippet,
	}
	a.AddSection(section)
}

func (a *AllFitsoFeaturesPageTemplate) AddMasterkeySnippet(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType15,
		SectionCount: 1,
		LayoutType:   sushi.LayoutTypeGrid,
	}

	image, _ := sushi.NewImage(util.GetCDNLink(common.MASTERKEY_IMAGE))
	image.SetType(sushi.ImageTypeCircle)

	tagTitle, _ := sushi.NewTextSnippet("Age 18+ years")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	tagTitle.SetColor(color)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)

	tag := &sushi.Tag{
		Title:       tagTitle,
		BgColor:     bgColor,
		BorderColor: borderColor,
	}
	t := "CULTPASS PLAY"

	title, _ := sushi.NewTextSnippet(t)
	title.SetKerning(1)

	subtitle, _ := sushi.NewTextSnippet("Book & play unlimited sports")

	tapEname := &sushi.EnameData{
		Ename: "feature_selection_masterkey_tap",
	}
	impressionEname := &sushi.EnameData{
		Ename: "feature_selection_masterkey_impression",
	}
	trackEvents := sushi.NewClevertapEvents()
	trackEvents.SetTap(tapEname)
	trackEvents.SetImpression(impressionEname)
	trackItem := a.GetClevertapTrackItem(ctx, trackEvents)

	item := &sushi.FitsoImageTextType15SnippetItem{
		Image:             image,
		Tag:               tag,
		Title:             title,
		Subtitle:          subtitle,
		ClickAction:       a.GetClickAction(ctx, common.PAGE_TYPE_MASTERKEY),
		IsSelectable:      true,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	snippet := &sushi.FitsoImageTextType15Snippet{
		Items: []*sushi.FitsoImageTextType15SnippetItem{item},
	}
	section := &homeModels.ResultSection{
		LayoutConfig:         layoutConfig,
		FitsoImageTextType15: snippet,
	}
	a.AddSection(section)
}

func (a *AllFitsoFeaturesPageTemplate) AddAcademySnippet(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType15,
		SectionCount: 1,
		LayoutType:   sushi.LayoutTypeGrid,
	}

	image, _ := sushi.NewImage(util.GetCDNLink(common.ACADEMY_IMAGE))
	image.SetType(sushi.ImageTypeCircle)

	tagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age %d - %d years", common.ACADEMY_MIN_AGE, common.ACADEMY_MAX_AGE))
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	tagTitle.SetColor(color)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)

	tag := &sushi.Tag{
		Title:       tagTitle,
		BgColor:     bgColor,
		BorderColor: borderColor,
	}
	t := "CULT ACADEMY"

	title, _ := sushi.NewTextSnippet(t)
	title.SetKerning(1)

	subtitle, _ := sushi.NewTextSnippet("Learn & improve sport skills")

	/*promoTagTitle, _ := sushi.NewTextSnippet("INTRODUCING")
	color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	promoTagTitle.SetColor(color)
	bgColor, _ = sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint400)
	if featuresupport.SupportsNewColor(ctx) {
		bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}

	promotag := &sushi.Tag{
		Title:   promoTagTitle,
		BgColor: bgColor,
	}*/

	tapEname := &sushi.EnameData{
		Ename: "feature_selection_academy_tap",
	}
	impressionEname := &sushi.EnameData{
		Ename: "feature_selection_academy_impression",
	}
	trackEvents := sushi.NewClevertapEvents()
	trackEvents.SetTap(tapEname)
	trackEvents.SetImpression(impressionEname)
	trackItem := a.GetClevertapTrackItem(ctx, trackEvents)

	item := &sushi.FitsoImageTextType15SnippetItem{
		Image:             image,
		Tag:               tag,
		Title:             title,
		Subtitle:          subtitle,
		//PromoTag:          promotag,
		ClickAction:       a.GetClickAction(ctx, common.PAGE_TYPE_ACADEMY),
		IsSelectable:      true,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	snippet := &sushi.FitsoImageTextType15Snippet{
		Items: []*sushi.FitsoImageTextType15SnippetItem{item},
	}
	section := &homeModels.ResultSection{
		LayoutConfig:         layoutConfig,
		FitsoImageTextType15: snippet,
	}
	a.AddSection(section)
}

func (a *AllFitsoFeaturesPageTemplate) AddSummerCampSnippet(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType15,
		SectionCount: 1,
		LayoutType:   sushi.LayoutTypeGrid,
	}

	image, _ := sushi.NewImage(util.GetCDNLink(common.SUMMER_CAMP_IMAGE))
	image.SetType(sushi.ImageTypeCircle)

	tagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age %d - %d years", common.SUMMER_CAMP_MIN_AGE, common.SUMMER_CAMP_MAX_AGE))
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	tagTitle.SetColor(color)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)

	tag := &sushi.Tag{
		Title:       tagTitle,
		BgColor:     bgColor,
		BorderColor: borderColor,
	}

	title, _ := sushi.NewTextSnippet("CULT SUMMER CAMP")
	title.SetKerning(1)

	subtitle, _ := sushi.NewTextSnippet("Give your kids the best of sports with a summer camp pack!")

	promoTagTitle, _ := sushi.NewTextSnippet("2-4 Week plan")
	color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	promoTagTitle.SetColor(color)
	bgColor, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	if featuresupport.SupportsNewColor(ctx) {
		bgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}

	promotag := &sushi.Tag{
		Title:   promoTagTitle,
		BgColor: bgColor,
	}

	tapEname := &sushi.EnameData{
		Ename: "feature_selection_summer_camp_tap",
	}
	impressionEname := &sushi.EnameData{
		Ename: "feature_selection_summer_camp_impression",
	}
	trackEvents := sushi.NewClevertapEvents()
	trackEvents.SetTap(tapEname)
	trackEvents.SetImpression(impressionEname)
	trackItem := a.GetClevertapTrackItem(ctx, trackEvents)

	item := &sushi.FitsoImageTextType15SnippetItem{
		Image:             image,
		Tag:               tag,
		Title:             title,
		Subtitle:          subtitle,
		PromoTag:          promotag,
		ClickAction:       a.GetClickAction(ctx, common.PAGE_TYPE_SUMMER_CAMP),
		IsSelectable:      true,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	snippet := &sushi.FitsoImageTextType15Snippet{
		Items: []*sushi.FitsoImageTextType15SnippetItem{item},
	}
	section := &homeModels.ResultSection{
		LayoutConfig:         layoutConfig,
		FitsoImageTextType15: snippet,
	}
	a.AddSection(section)
}

func (a *AllFitsoFeaturesPageTemplate) GetClickAction(ctx context.Context, pageType string) *sushi.ClickAction {
	saveKeyClickAction := getFeatureKeySaveClickAction(ctx, pageType, false, true)

	button, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	button.SetText("Proceed with selection")
	button.SetClickAction(saveKeyClickAction)
	loggedInUser := util.GetUserIDFromContext(ctx)
	if util.Contains(common.TestSummercampUsers, loggedInUser) && pageType == common.PAGE_TYPE_MASTERKEY && false {
		userEventClick := &structs.UserEventClick{
			UserId:         loggedInUser,
			HaveMembership: true,
		}

		postback_params, _ := json.Marshal(userEventClick)

		button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
		api_call_multi_action := &sushi.APICallAction{
			RequestType: sushi.POSTRequestType,
			URL:         "v1/user/recordUserClickAction",
			Body:        string(postback_params),
		}
		button_click_action.SetApiCallAction(api_call_multi_action)
		button.SetClickAction(button_click_action)
	}

	changeBottomButton := &sushi.ChangeBottomButton{
		ButtonId: "bottom_button_id1",
		Button:   button,
	}

	finalClickAction := sushi.GetClickAction()
	finalClickAction.SetChangeBottomButton(changeBottomButton)

	return finalClickAction
}

func (a *AllFitsoFeaturesPageTemplate) SetFooter(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint400)

	tapEname := &sushi.EnameData{
		Ename: "feature_selection_proceed_tap",
	}
	trackEvents := sushi.NewClevertapEvents()
	trackEvents.SetTap(tapEname)
	trackItem := a.GetClevertapTrackItem(ctx, trackEvents)

	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeSolid,
		Text:              "Proceed with selection",
		Id:                "bottom_button_id1",
		BgColor:           bgColor,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	items := []sushi.FooterSnippetType2ButtonItem{buttonItem}

	bottomButton := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: bottomButton,
	}
	a.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
}

func (a *AllFitsoFeaturesPageTemplate) SetNotificationCount(ctx context.Context) {
	count, _ := getNotificationCount(ctx)
	a.UnreadNotificationCount = count
}

func (a *AllFitsoFeaturesPageTemplate) GetClevertapTrackItem(ctx context.Context, trackEvents *sushi.EventNames) *sushi.ClevertapItem {
	trackPayload := make(map[string]interface{})
	trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
	trackPayload["subzone_id"] = util.GetSubzoneIDFromContext(ctx)

	return sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
}
