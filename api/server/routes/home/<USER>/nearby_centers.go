package homeController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"

	common "bitbucket.org/jogocoin/go_api/api/common"
	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type NearbyCentersPageData struct {
	NearbyFacilities *facilitySportPB.HomePageNearbyFacilityResponse
	PostbackParams   map[string]interface{}
}

type NearbyCentersTemplate struct {
	PageRequest    homeModels.GetNearbyFacilitiesRequest `json:"-"`
	PageData       NearbyCentersPageData                 `json:"-"`
	Sections       []*homeModels.ResultSection           `json:"results,omitempty"`
	HasMore        bool                                  `json:"has_more"`
	PostbackParams string                                `json:"postback_params,omitempty"`
}

func GetNearbyFacilitiesC(c *gin.Context) {
	var requestData homeModels.GetNearbyFacilitiesRequest
	requestData = c.MustGet("jsonData").(homeModels.GetNearbyFacilitiesRequest)
	requestData.UnmarshalledPostbackParams = getNearbyCentersRequestPostbackParams(requestData.PostbackParams)
	ctx := util.PrepareGRPCMetaData(c)
	log.Printf("GetNearbyFacilitiesC: Error Provider: City Id - %d", util.GetCityIDFromContext(ctx))
	template := NearbyCentersTemplate{
		PageRequest: requestData,
		PageData:    NearbyCentersPageData{},
	}

	template.SetNearbyFacilitySection(ctx)
	template.SetPostbackParams(ctx)
	c.JSON(http.StatusOK, template)
}

func getNearbyCentersRequestPostbackParams(postbackParams string) *homeModels.RequestPostbackParams {
	var temp map[string]interface{}
	json.Unmarshal([]byte(postbackParams), &temp)

	previousFacilityIds := make([]int32, 0)
	if facilityIds, ok := temp["previous_facility_ids"]; ok {
		facilityIdsInterface, _ := facilityIds.([]interface{})
		for _, val := range facilityIdsInterface {
			previousFacilityIds = append(previousFacilityIds, int32(val.(float64)))
		}
	}
	requestPostbackParams := &homeModels.RequestPostbackParams{
		PreviousFacilityIds: previousFacilityIds,
	}
	return requestPostbackParams
}

func (n *NearbyCentersTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.HomePageNearbyFacilityResponse {
	if n.PageData.NearbyFacilities != nil {
		return n.PageData.NearbyFacilities
	}

	facilityClient := util.GetFacilitySportClient()
	request := &facilitySportPB.HomePageNearbyFacilityRequest{
		Count:               facilityCount,
		PreviousFacilityIds: n.PageRequest.UnmarshalledPostbackParams.PreviousFacilityIds,
	}
	response, err := facilityClient.GetHomePageNearbyFacilities(ctx, request)
	if err != nil {
		log.Printf("Api: nearby centers, function: GetNearbyFacilities, Error: %v", err)
		return nil
	}
	n.PageData.NearbyFacilities = response
	return n.PageData.NearbyFacilities
}

func (n *NearbyCentersTemplate) AddSection(section *homeModels.ResultSection) {
	if n.Sections == nil {
		n.Sections = make([]*homeModels.ResultSection, 0)
	}
	n.Sections = append(n.Sections, section)
}

func (n *NearbyCentersTemplate) SetHasMore(hasMore bool) {
	n.HasMore = hasMore
}

func (n *NearbyCentersTemplate) SetPostbackParams(ctx context.Context) {
	if len(n.PageData.PostbackParams) == 0 {
		return
	}
	postbackParam, _ := json.Marshal(n.PageData.PostbackParams)
	n.PostbackParams = string(postbackParam)
}

func (n *NearbyCentersTemplate) AddPostbackParam(key string, val interface{}) {
	if n.PageData.PostbackParams == nil {
		n.PageData.PostbackParams = make(map[string]interface{})
	}
	n.PageData.PostbackParams[key] = val
}

func (n *NearbyCentersTemplate) ReplicateOldPostbackParamsToNewOne(ctx context.Context) {
	var temp map[string]interface{}
	json.Unmarshal([]byte(n.PageRequest.PostbackParams), &temp)
	n.PageData.PostbackParams = temp
}

func (n *NearbyCentersTemplate) SetNearbyFacilitySection(ctx context.Context) {
	n.ReplicateOldPostbackParamsToNewOne(ctx)

	response := n.GetNearbyFacilities(ctx)
	if response == nil {
		return
	}
	if len(response.Facilities) == 0 {
		return
	}

	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	n.AddPostbackParam("previous_facility_ids", response.PreviousFacilityIds)
	n.SetHasMore(response.HasMore)

	for _, facility := range response.Facilities {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.ResSnippetType3,
		}
		snippet := &sushi.ResType3Snippet{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sports {
			sportNames = append(sportNames, sport.SportName)
		}
		if len(sportNames) > 0 {
			subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
			snippet.Subtitle1 = subtitle1
		}

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		if facility.Tag != common.UPCOMING_FACILITY_SPORT_TAG {
			clickAction := sushi.GetClickAction()
			deeplink := sushi.Deeplink{
				URL: facility.Deeplink,
			}
			clickAction.SetDeeplink(&deeplink)
			snippet.ClickAction = clickAction
		}

		ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

		tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, facility.Tag) {
			ratingTitle, _ := sushi.NewTextSnippet(facility.Tag)

			if !isNewColorSupported {
				color := sushi.ColorTypeBlue
				if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeTeal
				} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeOrange
				}
				ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
				ratingSnippetBlockItem.BgColor = ratingBgColor
			} else {
				if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
					ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					ratingBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else {
					// gradient for new tag
					tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
					tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
					gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
					ratingSnippetBlockItem.Gradient = gradient
				}
			}

			ratingSnippetBlockItem.Title = ratingTitle
			ratingSnippetBlockItem.Size = sushi.RatingSize300

		} else {
			ratingTitle, _ := sushi.NewTextSnippet(facility.Rating)
			ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if isNewColorSupported {
				ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			}

			ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

			ratingSnippetBlockItem.Title = ratingTitle
			ratingSnippetBlockItem.BgColor = ratingBgColor
			ratingSnippetBlockItem.RightIcon = ratingIcon
		}

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}
		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "home"
		tapEname := &sushi.EnameData{
			Ename: "facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		section := &homeModels.ResultSection{
			LayoutConfig:    layoutConfig,
			ResType3Snippet: snippet,
		}
		n.AddSection(section)
	}
}
