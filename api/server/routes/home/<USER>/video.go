package homeController

import (
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

func CreateVideoC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	userClient := util.GetUserServiceClient()
	json := c.MustGet("jsonData").(structs.Video)
	response, err := userClient.CreateVideo(ctx, &userPB.CreateVideoRequest{
		VideoUrl: json.URL,
		AspectRatio: json.AspectRatio,
		ThumbnailId: json.ThumbnailId,
	})
	if err != nil {
		log.Printf("Error in CreateVideoC for , err: %v", err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	if response.Status != nil && response.Status.Status == common.BAD_REQUEST {
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	c.AbortWithStatusJSON(http.StatusOK, response)
}
