package homeResource

import (
	"log"

	homeModel "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"gopkg.in/go-playground/validator.v9"
)

var (
	validate *validator.Validate
)

func GetNearbyFacilitiesR(c *gin.Context) {
	validate = validator.New()

	var json homeModel.GetNearbyFacilitiesRequest

	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Function: GetNearbyFacilitiesR, Error: Unable to bind %v ", err)
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Printf("Function: GetNearbyFacilitiesR, Error: Validation issue %v ", err)
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetHomeR(c *gin.Context) {
	validate = validator.New()

	var json structs.ReferralUserInfo

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("Function: GetHomeR, Error: Unable to bind %v ", err)
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Printf("Function: GetHomeR, Error: Validation issue %v ", err)
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func GetAerobarR(c *gin.Context) {
	validate = validator.New()

	var json structs.ReferralUserInfo

	if err := c.ShouldBind(&json); err != nil {
		log.Printf("Function: GetAerobarR, Error: Unable to bind %v ", err)
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Printf("Function: GetAerobarR, Error: Validation issue %v ", err)
		return
	}
	c.Set("jsonData", json)
	c.Next()
}

func CreateVideoR(c *gin.Context) {
	validate := validator.New()
	var json structs.Video
	if err := c.ShouldBindJSON(&json); err != nil {
		log.Printf("Function: CreateVideoR, Error: Unable to bind %v ", err)
		return
	}
	if err := validate.Struct(&json); err != nil {
		log.Printf("Function: CreateVideoR, Error: Validation issue %v ", err)
		return
	}
	c.Set("jsonData", json)
	c.Next()
}
