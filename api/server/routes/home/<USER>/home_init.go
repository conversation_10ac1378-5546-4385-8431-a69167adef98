package homeController

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"encoding/json"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	notificationPB "bitbucket.org/jogocoin/go_api/api/proto/notification"
	//userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func GetHomeDetailsV2C(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	cityId := util.GetCityIDFromContext(ctx)
	fmt.Println("city id: @@@@@@@@", cityId)

	hasMasterKey, hasAcademy, hasSummerCamp, err := checkFitsoFeaturesAvailability(ctx)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	fmt.Println("hasMasterKey, hasAcademy, hasSummerCamp: @@@@@@@@", hasMasterKey, hasAcademy, hasSummerCamp)

	//loggedInUser := util.GetUserIDFromContext(ctx)
	// var hasSummerCampSubscription bool
	// if loggedInUser > 0 && cityId > 0 {
	// 	userClient := util.GetUserServiceClient()
	// 	response, err := userClient.GetSummerCampSubscriptionCountByCity(ctx, &userPB.Empty{})

	// 	if err != nil {
	// 		log.Printf("Unable to fetch city based summer camp subscription details for user %d, city id: %d, Error: %v", loggedInUser, cityId, err)
	// 		c.AbortWithStatusJSON(http.StatusInternalServerError, model.StatusFailure(common.UNEXPECTED_ERROR))
	// 		return
	// 	}
	// 	if response.Count > 0 {
	// 		hasSummerCampSubscription = true
	// 	}
	// }

	hasSummerCamp = hasSummerCamp && featuresupport.SupportsSummerCamp(ctx) //&& hasSummerCampSubscription
	if hasMasterKey {
		c.Set("has_masterkey", 1)
	}
	if hasAcademy {
		c.Set("has_academy", 1)
	}
	//hasSummerCamp = hasSummerCamp && (util.Contains(common.TestSummercampUsers, loggedInUser))
	if hasSummerCamp {
		c.Set("has_summer_camp", 1)
	}

	featureType := util.GetFeatureTypeFromContext(ctx)
	fmt.Println("featureType: @@@@@@@@", featureType)
	var finalPageType string

	switch featureType {
	// home bottom sheet or full page selection is done
	case common.PAGE_TYPE_MASTERKEY:
		switch {
		case hasMasterKey:
			finalPageType = common.PAGE_TYPE_MASTERKEY_HOME

		case !hasMasterKey && hasAcademy:
			finalPageType = common.PAGE_TYPE_MASTERKEY_HOME_EMPTY

		case !hasMasterKey && !hasAcademy:
			finalPageType = common.PAGE_TYPE_NO_FEATURES
		}

	// home bottom sheet or full page selection is done
	case common.PAGE_TYPE_ACADEMY:
		switch {
		case hasAcademy:
			finalPageType = common.PAGE_TYPE_ACADEMY_HOME

		case !hasAcademy && hasMasterKey:
			finalPageType = common.PAGE_TYPE_ACADEMY_HOME_EMPTY

		case !hasAcademy && !hasMasterKey:
			finalPageType = common.PAGE_TYPE_NO_FEATURES
		}

	case common.PAGE_TYPE_SUMMER_CAMP:
		switch {
		case hasSummerCamp:
			finalPageType = common.PAGE_TYPE_SUMMER_CAMP_HOME

		case (hasMasterKey || hasAcademy) && !hasSummerCamp:
			finalPageType = common.PAGE_TYPE_SUMMER_CAMP_HOME_EMPTY

		case !hasMasterKey && !hasAcademy && !hasSummerCamp:
			finalPageType = common.PAGE_TYPE_NO_FEATURES
		}

	// app opened first time OR (home bottom sheet or full page selection hasnt done yet)
	default:
		switch {
		case hasMasterKey && hasAcademy && hasSummerCamp:
			finalPageType = common.PAGE_TYPE_FITSO_FEATURES_LIST

		case hasMasterKey && hasAcademy:
			finalPageType = common.PAGE_TYPE_FITSO_FEATURES_LIST

		case !hasMasterKey && hasAcademy:
			finalPageType = common.PAGE_TYPE_ACADEMY_HOME

		case hasMasterKey && !hasAcademy:
			finalPageType = common.PAGE_TYPE_MASTERKEY_HOME

		case !hasMasterKey && !hasAcademy:
			finalPageType = common.PAGE_TYPE_NO_FEATURES
		}
	}

	log.Printf("GetHomeDetailsV2C: Error Provider: City Id - %d", cityId)

	if finalPageType == "" {
		log.Printf("unable to get page type for cityid: %d and req featureType: %s", cityId, featureType)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.BAD_REQUEST))
		return
	}

	if val, ok := c.Get("user_id"); ok {
		// Convert val to int32 for proper comparison
		fmt.Println("user id: @@@@@@@", val)
		if userID, ok := val.(int32); !ok || userID != 994833 {
			fmt.Println("showing maintenance page with userID @@@@@", userID)
			c.Set("show_maintenance", true)
			GetNoFeatureAvailablePage(c)
			return
		}
	} else {
		c.Set("show_maintenance", true)
		GetNoFeatureAvailablePage(c)
		return
	}
	fmt.Println("final page type is @@@@@@@", finalPageType)

	switch finalPageType {
	case common.PAGE_TYPE_MASTERKEY_HOME:
		GetMasterkeyPage(c)

	case common.PAGE_TYPE_MASTERKEY_HOME_EMPTY:
		GetMasterkeyEmptyPage(c)

	case common.PAGE_TYPE_ACADEMY_HOME:
		GetAcademyPage(c)

	case common.PAGE_TYPE_ACADEMY_HOME_EMPTY:
		GetAcademyEmptyPage(c)

	case common.PAGE_TYPE_FITSO_FEATURES_LIST:
		GetAllFitsoFeaturesPage(c)

	case common.PAGE_TYPE_SUMMER_CAMP_HOME_EMPTY:
		GetSummerCampEmptyPage(c)

	case common.PAGE_TYPE_SUMMER_CAMP_HOME:
		GetSummerCampPage(c)

	case common.PAGE_TYPE_NO_FEATURES:
		GetNoFeatureAvailablePage(c)
	}
}

func checkFitsoFeaturesAvailability(ctx context.Context) (bool, bool, bool, error) {
	hasAcademy, hasMasterKey, hasSummerCamp := false, false, false

	cityId := util.GetCityIDFromContext(ctx)
	facilityClient := util.GetFacilitySportClient()
	response, err := facilityClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})

	if err != nil {
		log.Printf("Unable to fetch city based features details for city id: %d, Error: %v", cityId, err)
		return hasMasterKey, hasAcademy, hasSummerCamp, err
	}
	if len(response.MasterkeySportIds) > 0 {
		hasMasterKey = true
	}
	if len(response.AcademySportIds) > 0 {
		hasAcademy = true
	}
	if len(response.SummerCampSportIds) > 0 {
		hasSummerCamp = true
	}
	return hasMasterKey, hasAcademy, hasSummerCamp, nil
}

func getNotificationCount(ctx context.Context) (int32, error) {
	notificationClient := util.GetNotificationServiceClient()
	userId := util.GetUserIDFromContext(ctx)
	req := &notificationPB.UnreadNotification{UserId: userId}
	countResponse, err := notificationClient.UnreadNotificationCountGet(ctx, req)
	if err != nil {
		log.Printf("Error in getting notification count for user id: %d, Error: %v", userId, err)
		return int32(0), err
	}
	return countResponse.UnreadCount, nil
}

func getFeatureKeySaveClickAction(ctx context.Context, pageType string, needDismissAction bool, needRefreshAction bool) *sushi.ClickAction {
	actionList := make([]*sushi.SaveActionItem, 0)

	if needRefreshAction {
		refreshPageItem := &sushi.RefreshPageItem{
			Type: common.PAGE_TYPE_HOME,
		}
		saveActionItem1 := &sushi.SaveActionItem{
			Type:         sushi.ActionTypeRefreshPages,
			RefreshPages: []*sushi.RefreshPageItem{refreshPageItem},
		}
		actionList = append(actionList, saveActionItem1)
	}

	if needDismissAction {
		saveActionItem2 := &sushi.SaveActionItem{
			Type: sushi.ActionTypeDismissPage,
		}
		actionList = append(actionList, saveActionItem2)
	}

	saveKeyItem := &sushi.SaveKeyItem{
		Key:   "product_type",
		Value: pageType,
	}
	saveKey := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{saveKeyItem},
		ActionList: actionList,
	}
	saveKeyClickAction := sushi.GetClickAction()
	saveKeyClickAction.SetSaveKey(saveKey)

	return saveKeyClickAction
}

func getBottomSheetFeatureChangeClickAction(ctx context.Context, currentPageType string) *sushi.ClickAction {
	masterkeyImage, _ := sushi.NewImage(util.GetCDNLink(common.MASTERKEY_IMAGE))
	masterkeyImage.SetType(sushi.ImageTypeCircle)

	masterkeyTagTitle, _ := sushi.NewTextSnippet("Age 18+ years")
	masterkeyTagColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	masterkeyTagTitle.SetColor(masterkeyTagColor)
	masterkeyBgColorTint := sushi.ColorTint100
	if featuresupport.SupportsNewColor(ctx) {
		masterkeyBgColorTint = sushi.ColorTint50
	}
	masterkeyBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, masterkeyBgColorTint)
	masterkeyBorderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)

	masterkeyTag := &sushi.Tag{
		Title:       masterkeyTagTitle,
		BgColor:     masterkeyBgColor,
		BorderColor: masterkeyBorderColor,
	}
	t := "CULTPASS PLAY"
	loggedInUser := util.GetUserIDFromContext(ctx)

	masterkeyTitle, _ := sushi.NewTextSnippet(t)
	masterkeyTitle.SetKerning(1)

	masterkeySubtitle, _ := sushi.NewTextSnippet("Book & play unlimited sports")

	needMasterkeyRefresh := currentPageType != common.PAGE_TYPE_MASTERKEY
	masterkeyItem := &sushi.FeaturesListBottomSheetItem{
		Image:       masterkeyImage,
		Tag:         masterkeyTag,
		Title:       masterkeyTitle,
		Subtitle:    masterkeySubtitle,
		ClickAction: getFeatureKeySaveClickAction(ctx, common.PAGE_TYPE_MASTERKEY, true, needMasterkeyRefresh),
	}
	if util.Contains(common.TestSummercampUsers, loggedInUser) && false {
		userEventClick := &structs.UserEventClick{
			UserId:         loggedInUser,
			HaveMembership: true,
		}

		postback_params, _ := json.Marshal(userEventClick)

		button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
		api_call_multi_action := &sushi.APICallAction{
			RequestType: sushi.POSTRequestType,
			URL:         "v1/user/recordUserClickAction",
			Body:        string(postback_params),
		}
		button_click_action.SetApiCallAction(api_call_multi_action)
		//button.SetClickAction(button_click_action)
		masterkeyItem.ClickAction = button_click_action
	}

	academyImage, _ := sushi.NewImage(util.GetCDNLink(common.ACADEMY_IMAGE))
	academyImage.SetType(sushi.ImageTypeCircle)

	academyTagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age %d - %d years", common.ACADEMY_MIN_AGE, common.ACADEMY_MAX_AGE))
	academyTagColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	academyTagTitle.SetColor(academyTagColor)
	academyBgColorTint := sushi.ColorTint100
	if featuresupport.SupportsNewColor(ctx) {
		academyBgColorTint = sushi.ColorTint50
	}
	academyBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, academyBgColorTint)
	academyBorderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)

	academyTag := &sushi.Tag{
		Title:       academyTagTitle,
		BgColor:     academyBgColor,
		BorderColor: academyBorderColor,
	}
	ta := "CULT ACADEMY"

	academyTitle, _ := sushi.NewTextSnippet(ta)
	academyTitle.SetKerning(1)

	academySubtitle, _ := sushi.NewTextSnippet("Learn & improve sport skills")

	/*promoTagTitle, _ := sushi.NewTextSnippet("INTRODUCING")
	promoTagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	promoTagTitle.SetColor(promoTagTitleColor)
	promoTagBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint400)
	if featuresupport.SupportsNewColor(ctx) {
		promoTagBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
	}

	academyPromoTag := &sushi.Tag{
		Title:   promoTagTitle,
		BgColor: promoTagBgColor,
	}*/

	needAcademyRefresh := currentPageType != common.PAGE_TYPE_ACADEMY
	academyItem := &sushi.FeaturesListBottomSheetItem{
		Image:    academyImage,
		Tag:      academyTag,
		Title:    academyTitle,
		Subtitle: academySubtitle,
		//PromoTag:    academyPromoTag,
		ClickAction: getFeatureKeySaveClickAction(ctx, common.PAGE_TYPE_ACADEMY, true, needAcademyRefresh),
	}
	summerCampItem := &sushi.FeaturesListBottomSheetItem{}
	if ctx.Value("has_summer_camp") == 1 {
		summerCampImage, _ := sushi.NewImage(util.GetCDNLink(common.SUMMER_CAMP_IMAGE))
		summerCampImage.SetType(sushi.ImageTypeCircle)

		summerCampTagTitle, _ := sushi.NewTextSnippet(fmt.Sprintf("Age %d - %d years", common.SUMMER_CAMP_MIN_AGE, common.SUMMER_CAMP_MAX_AGE))
		summerCampTagColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		summerCampTagTitle.SetColor(summerCampTagColor)
		summerCampBgColorTint := sushi.ColorTint50

		summerCampBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, summerCampBgColorTint)
		summerCampBorderColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint300)

		tag := &sushi.Tag{
			Title:       summerCampTagTitle,
			BgColor:     summerCampBgColor,
			BorderColor: summerCampBorderColor,
		}

		summerCampTitle, _ := sushi.NewTextSnippet("CULT SUMMER CAMP")
		summerCampTitle.SetKerning(1)

		summerCampSubtitle, _ := sushi.NewTextSnippet("Give your kids the best of sports with a summer camp pack!")

		promoTagTitle, _ := sushi.NewTextSnippet("2-4 Week plan")
		promoTagTitleColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		promoTagTitle.SetColor(promoTagTitleColor)
		promoTagBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

		summerCampPromoTag := &sushi.Tag{
			Title:   promoTagTitle,
			BgColor: promoTagBgColor,
		}

		needSummerCampRefresh := currentPageType != common.PAGE_TYPE_SUMMER_CAMP
		summerCampItem = &sushi.FeaturesListBottomSheetItem{
			Image:       summerCampImage,
			Tag:         tag,
			Title:       summerCampTitle,
			Subtitle:    summerCampSubtitle,
			PromoTag:    summerCampPromoTag,
			ClickAction: getFeatureKeySaveClickAction(ctx, common.PAGE_TYPE_SUMMER_CAMP, true, needSummerCampRefresh),
		}
	}
	fmt.Println("MasterkeyItem - ", masterkeyItem)
	items := []*sushi.FeaturesListBottomSheetItem{academyItem}
	if summerCampItem != nil && summerCampItem.Image != nil {
		summerCampItemList := []*sushi.FeaturesListBottomSheetItem{summerCampItem}
		items = append(summerCampItemList, items...)
	}

	headerTitle, _ := sushi.NewTextSnippet("What are you looking for?")
	header := &sushi.FeaturesListBottomSheetHeader{
		Title: headerTitle,
	}

	featureSelectionBottomSheet := &sushi.FeaturesListBottomSheet{
		Header:        header,
		Items:         items,
		IsDismissable: true,
	}

	featureSelectionClickAction := sushi.GetClickAction()
	featureSelectionClickAction.SetOpenPlanSelectionBottomSheet(featureSelectionBottomSheet)

	return featureSelectionClickAction
}
