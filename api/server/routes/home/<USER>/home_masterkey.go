package homeController

import (
	"log"
	"net/http"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	structs "bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

func GetMasterkeyPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	template := HomePageTemplate{
		PageData: HomePageData{},
	}

	template.GetUserSubscriptionStatus(ctx)
	loggedInUserId := util.GetUserIDFromContext(ctx)

	isUserBlacklisted := util.CheckIfBlacklistedUser(ctx)
	if isUserBlacklisted {
		template.SetHardPopUpSectionForBlacklistUser()
		c.<PERSON>(http.StatusOK, template)
		return
	}

	var cultAppUsage structs.CultAppUsageApiResponse
	if err := util.CheckIfCultAppUser(ctx, &cultAppUsage); err != nil {
		log.Printf("GetMasterkeyPage: Error in fetching cult app user status for user id: %d, err: %v", loggedInUserId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	if cultAppUsage.BlockFitsoApp {
		if featuresupport.SupportNewCultAppPopup(ctx) {
			template.SetHardPopUpSectionForCultApp(ctx)
			c.JSON(http.StatusOK, template)
			return
		} else {
			template.SetBlockCultAppUserPopUpSection(ctx, cultAppUsage)
			c.JSON(http.StatusOK, template)
			return
		}
	} else if featuresupport.SupportNewCultAppPopup(ctx) {
		if util.IsIOS(ctx) {
			template.SetIosSoftPopUpSectionForCultApp(ctx)
		} else {
			template.SetAndroidSoftPopUpSectionForCultApp(ctx)
		}
	}

	template.SetCitySportsFacilityCount(ctx)
	if template.PageData.AvailableSportsCount == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}

	template.SetPageTracking(ctx)

	if ctx.Value("has_academy") == 1 {
		template.SetHeaderSection(ctx)
		template.SetFeatureBottomSheetSection(ctx)
	}

	template.SetPageType(common.PAGE_TYPE_MASTERKEY)
	template.SetShortNoticeSection(ctx)
	template.SetBannerSection(ctx)
	if featuresupport.SupportsRenewalTile(ctx) && loggedInUserId > 0 {
		template.SetMembershipRenewalTilesSection(ctx)
	}
	template.SetBookAgainSection(c)
	template.SetSportFacilityCountSection(c)
	template.SetPlanAndPricingSection(ctx)
	template.SetNearbyFacilitySection(ctx)
	template.SetFooterSection(c)
	template.SetNotificationCount(ctx)
	template.SetPostbackParams(ctx)

	c.JSON(http.StatusOK, template)
}
