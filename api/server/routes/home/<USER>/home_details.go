package homeController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	common "bitbucket.org/jogocoin/go_api/api/common"
	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	notificationPB "bitbucket.org/jogocoin/go_api/api/proto/notification"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	ptypes "github.com/golang/protobuf/ptypes"
)

const (
	distanceFilterId      string = "distance_filter_10"
	facilityCount         int32  = 6
	daysToAllowTrialAfter int32  = 30
	expiringSoonDays      int32  = 15
)

var IntegerFields = []string{"fs_id", "bottom_sheet", "slot_id", "sport_id", "fs_id", "is_trial", "booking_id", "booking_ref_no", "rating", "booking_date"}

type HomePageData struct {
	FacilityCountDetails                        []*facilitySportPB.SportFacilitiesCountResponse
	NearbyFacilities                            *facilitySportPB.HomePageNearbyFacilityResponse
	AvailableSportsCount                        int32
	PostbackParams                              map[string]interface{}
	UserSubscriptionStatus                      *productPB.GetUserSubscriptionStatusResponse
	LoggedInUserRenewalDeepLink                 []string
	ActiveOrFutureSubscriptionProductCategoryId int32
	SportsDetails                               *facilitySportPB.SportFacilitiesCountResponse
	ProductCategoryId                           int32
}

type HomePageTemplate struct {
	PageData                HomePageData                             `json:"-"`
	PageType                string                                   `json:"page_type,omitempty"`
	Sections                []*homeModels.ResultSection              `json:"results,omitempty"`
	Popup                   *sushi.CustomAlert                       `json:"popup,omitempty"`
	Header                  *sushi.FitsoTextSnippetType6Layout       `json:"header,omitempty"`
	Footer                  *sushi.FooterSnippetType2Layout          `json:"footer,omitempty"`
	EmptyView               *sushi.EmptyViewType1Layout              `json:"empty_view,omitempty"`
	UnreadNotificationCount int32                                    `json:"unread_notification_count,omitempty"`
	HasMore                 bool                                     `json:"has_more"`
	PostbackParams          string                                   `json:"postback_params,omitempty"`
	ClevertapTracking       []*sushi.ClevertapItem                   `json:"clever_tap_tracking,omitempty"`
	JumboTracking           []*jumbo.Item                            `json:"jumbo_tracking,omitempty"`
	SummercampPageData      *facilitySportPB.SummerCampFacilitiesRes `json:"-"`
	ProductDetails          *productPB.SummercampSportProductsRes    `json:"-"`
}

func (h *HomePageTemplate) SetMembershipRenewalTilesSection(ctx context.Context) {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	cityId := util.GetCityIDFromContext(ctx)
	userService := util.GetUserServiceClient()
	response, err := userService.GetYourMembershipsPage(ctx, &userPB.YourMembershipsPageRequest{ProductCategoryId: common.MasterkeyCategoryID})
	if err != nil {
		log.Println("Error in SetMembershipRenewalTile GetYourMembershipsPage function: ", err)
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.PurchaseWidgetSnippet3,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	items := []*sushi.PurchaseWidgetSnippet3Item{}
	if len(response.MembershipCards) == 0 {
		return
	}
	cardsToDisplay := []*userPB.MembershipCard{}
	cardsToValidate := []*userPB.MembershipCard{}
	//userAcademyCards := make(map[int32][]userPB.MembershipCard)
	userMasterKeyCards := make(map[int32][]userPB.MembershipCard)

	sortedCards := sortRenewalTilesForDisplay(response.MembershipCards, loggedInUserId)
	for _, elem := range sortedCards {
		// if elem.ProductCategoryId == common.AcademyCategoryID {
		// 	userAcademyCards[elem.UserId] = append(userAcademyCards[elem.UserId], *elem)
		// }
		if elem.ProductCategoryId == common.MasterkeyCategoryID || elem.ProductCategoryId == common.SinglekeyCategoryID {
			userMasterKeyCards[elem.UserId] = append(userMasterKeyCards[elem.UserId], *elem)
		}
	}

	for userId, _ := range userMasterKeyCards {
		for _, card := range userMasterKeyCards[userId] {
			if card.MembershipStatus == userPB.MembershipCard_EXPIRES || card.MembershipStatus == userPB.MembershipCard_STARTING {
				userMasterKeyCards[userId] = []userPB.MembershipCard{}
				break
			}
		}
	}

	// for userId, _ := range userAcademyCards {
	// 	for _, card := range userAcademyCards[userId] {
	// 		if card.MembershipStatus == userPB.MembershipCard_EXPIRES || card.MembershipStatus == userPB.MembershipCard_STARTING {
	// 			userAcademyCards[userId] = []userPB.MembershipCard{}
	// 			break
	// 		}
	// 	}
	// }

	for userId, _ := range userMasterKeyCards {
		cityMasterKeyAdded := false
		internalKeyAdded := false
		for _, card := range userMasterKeyCards[userId] {
			if card.ProductLocationIdV2 > 0 {
				productCityId := card.ProductLocationIdV2
				if !cityMasterKeyAdded && cityId == int32(productCityId) && validateRenewalTileDisplay(&card) {
					cardsToDisplay = append(cardsToDisplay, &card)
					cityMasterKeyAdded = true
				}
			} else if !internalKeyAdded && validateRenewalTileDisplay(&card) { //this else handles all acrross india memberships
				cardsToDisplay = append(cardsToDisplay, &card)
				internalKeyAdded = true
			}
		}
	}

	// for userId, _ := range userAcademyCards {
	// 	for _, card := range userAcademyCards[userId] {
	// 		cardsToValidate = append(cardsToValidate, &card)
	// 	}
	// }

	for _, elem := range cardsToValidate {
		if elem.ProductLocationIdV2 > 0 {
			productCityId := elem.ProductLocationIdV2
			if cityId == int32(productCityId) && validateRenewalTileDisplay(elem) {
				cardsToDisplay = append(cardsToDisplay, elem)
			}
		} else if validateRenewalTileDisplay(elem) { //this else handles all acrross india memberships
			cardsToDisplay = append(cardsToDisplay, elem)
		}
	}

	if len(cardsToDisplay) == 0 {
		return
	}
	for _, elem := range cardsToDisplay {
		isLoggedInUserCard := false
		if elem.UserId == loggedInUserId {
			isLoggedInUserCard = true
		}
		item := h.GetRenewalTiles(ctx, elem, isLoggedInUserCard)
		if featuresupport.SupportsRenewalTileV2(ctx) {
			item = h.GetRenewalTilesV2(ctx, elem, isLoggedInUserCard)
		}
		items = append(items, item)
	}

	if len(items) > 1 {
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
		layoutConfig.VisibleCards = 1.25
	}
	purchaseItemsSnippet := &sushi.PurchaseWidgetSnippetType3{
		Items: items,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:           layoutConfig,
		PurchaseWidgetSnippet3: purchaseItemsSnippet,
	}
	h.AddSection(section)

}

func sortRenewalTilesForDisplay(cardsToDisplay []*userPB.MembershipCard, loggedInUserId int32) []*userPB.MembershipCard {
	loggedInUserCards := []*userPB.MembershipCard{}
	childUserCards := []*userPB.MembershipCard{}
	for _, elem := range cardsToDisplay {
		if elem.UserId == loggedInUserId {
			loggedInUserCards = append(loggedInUserCards, elem)
		} else {
			childUserCards = append(childUserCards, elem)
		}
	}
	sort.SliceStable(loggedInUserCards, func(i, j int) bool {
		return loggedInUserCards[i].SubscriptionEndDate < loggedInUserCards[j].SubscriptionEndDate
	})
	sort.SliceStable(childUserCards, func(i, j int) bool {
		return childUserCards[i].SubscriptionEndDate < childUserCards[j].SubscriptionEndDate
	})
	cards := []*userPB.MembershipCard{}
	for _, elem := range loggedInUserCards {
		cards = append(cards, elem)
	}
	for _, elem := range childUserCards {
		cards = append(cards, elem)
	}
	return cards
}

func validateRenewalTileDisplay(elem *userPB.MembershipCard) bool {

	if elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON {
		return true
	}

	if elem.MembershipStatus == userPB.MembershipCard_EXPIRED || elem.MembershipStatus == userPB.MembershipCard_CANCELLED {
		today := time.Now()
		dateToCheck := time.Now()
		if elem.MembershipStatus == userPB.MembershipCard_EXPIRED {
			dateToCheck = time.Unix(elem.SubscriptionEndDate, 0)
		} else if elem.MembershipStatus == userPB.MembershipCard_CANCELLED {
			cancellationDate := elem.CancellationDate
			dateToCheck, _ = ptypes.Timestamp(cancellationDate)
			location, _ := time.LoadLocation("Asia/Kolkata") //IST
			dateToCheck = dateToCheck.In(location)
		}
		if int32(today.Sub(dateToCheck).Hours()/24) < daysToAllowTrialAfter {
			return true
		}
	}
	return false
}

func getDaysLeftOrAgo(subEndDate int64) int32 {
	today := time.Now()
	y, m, d := today.Date()
	location, _ := time.LoadLocation("Asia/Kolkata")
	today = time.Date(y, m, d, int(0), int(0), int(0), int(0), location)

	endDate := time.Unix(subEndDate, 0)
	y, m, d = endDate.Date()
	endDate = time.Date(y, m, d, int(0), int(0), int(0), int(0), location)

	daysLeft := endDate.Sub(today).Hours() / 24
	return int32(daysLeft)
}

func subscriptionDaysRemaining(days int) string {
	if days >= 2 {
		return fmt.Sprintf("Expiring in <semibold-100| %d days>", days)
	} else if days >= 1 {
		return "Expiring tomorrow"
	} else {
		return "Expiring today"
	}
}

// get membership details and templatize them
func (h *HomePageTemplate) GetRenewalTiles(ctx context.Context, elem *userPB.MembershipCard, isLoggedInUserCard bool) *sushi.PurchaseWidgetSnippet3Item {

	profilePicture, _ := sushi.NewImage(elem.ProfilePicture)
	profilePicture.SetType(sushi.ImageTypeCircle)
	profilePicture.SetAspectRatio(1)
	profilePicture.SetHeight(36)
	profilePicture.SetWidth(36)

	titleText := elem.Name
	isMarkdown := int32(0)
	if isLoggedInUserCard {
		titleText = fmt.Sprintf("%s <%s-%s|(You)>", titleText, sushi.FontSemiBold, sushi.FontSize300)
		isMarkdown = 1
	}
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title, _ := sushi.NewTextSnippet(titleText)
	title.SetIsMarkdown(isMarkdown)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	var subtitle *sushi.TextSnippet
	if elem.Phone != "" && !featuresupport.SupportsRenewalTileV2(ctx) {
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle, _ = sushi.NewTextSnippet(elem.Phone)
		subtitle.SetColor(subtitleColor)
		subtitle.SetFont(subtitleFont)
	}

	top_container := &sushi.PurchaseWidgetItemTopContainer{
		Image:    profilePicture,
		Title:    title,
		Subtitle: subtitle,
	}

	progress_bar, daysLeftOrAgo := getProgressBar(ctx, elem)

	progress_container := &sushi.PurchaseWidgetItemProgressContainer{
		ProgressBar: progress_bar,
	}

	bottomContainerTitle := getMembershipStatusText(ctx, elem, daysLeftOrAgo)

	button := getButtonForRenewalTile(ctx, elem, "")
	bottom_container := &sushi.PurchaseWidgetItemBottomContainer{
		Title:  bottomContainerTitle,
		Button: button,
	}

	snippetClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	snippetDeeplink := &sushi.Deeplink{
		URL: elem.RenewalButtonDeeplink,
	}

	snippetClickAction.SetDeeplink(snippetDeeplink)
	clevertapTrackingItems := getRenewalTileImpressionTrackingItem(ctx, elem)

	item := &sushi.PurchaseWidgetSnippet3Item{
		TopContainer:      top_container,
		BottomContainer:   bottom_container,
		ProgressContainer: progress_container,
		ClevertapTracking: clevertapTrackingItems,
		ClickAction:       snippetClickAction,
	}

	if isLoggedInUserCard && (elem.MembershipStatus == userPB.MembershipCard_EXPIRED || elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON) {
		h.PageData.LoggedInUserRenewalDeepLink = append(h.PageData.LoggedInUserRenewalDeepLink, elem.RenewalButtonDeeplink)
	}
	return item
}

func (h *HomePageTemplate) GetRenewalTilesV2(ctx context.Context, elem *userPB.MembershipCard, isLoggedInUserCard bool) *sushi.PurchaseWidgetSnippet3Item {

	is_renewal := false
	sold_out_state := false
	if elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON {
		is_renewal = true
	}
	if elem.MembershipStatus == userPB.MembershipCard_EXPIRED {
		sold_out_state = GetSoldOutStateForFs(ctx, elem)
	}
	offerText := ""
	offerSubtext := ""
	if is_renewal {
		offerText = "Get <bold-300|Upto ₹1500> Off"
		offerSubtext = "on renewal before plan expiry"
	}

	profilePicture, _ := sushi.NewImage(elem.ProfilePicture)
	profilePicture.SetType(sushi.ImageTypeCircle)
	profilePicture.SetAspectRatio(1)
	profilePicture.SetHeight(36)
	profilePicture.SetWidth(36)

	titleText := elem.Name
	isMarkdown := int32(0)
	if isLoggedInUserCard {
		titleText = fmt.Sprintf("%s <%s-%s|(You)>", titleText, sushi.FontSemiBold, sushi.FontSize300)
		isMarkdown = 1
	}
	titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title, _ := sushi.NewTextSnippet(titleText)
	title.SetIsMarkdown(isMarkdown)
	title.SetFont(titleFont)
	title.SetColor(titleColor)

	var subtitle *sushi.TextSnippet
	loggedInUser := util.GetUserIDFromContext(ctx)
	if elem.ProductCategoryId == common.AcademyCategoryID || elem.ProductCategoryId == common.MasterkeyCategoryID {
		productCategory := ""
		if elem.ProductCategoryId == common.AcademyCategoryID {
			productCategory = "CULT ACADEMY"
		} else if elem.ProductCategoryId == common.MasterkeyCategoryID {
			productCategory = "CULTPASS PLAY"
		}
		if util.Contains(common.TestSummercampUsers, loggedInUser) && elem.ProductCategoryId == common.AcademyCategoryID {
			productCategory = "CULT ACADEMY"
		} else if util.Contains(common.TestSummercampUsers, loggedInUser) && elem.ProductCategoryId == common.MasterkeyCategoryID {
			productCategory = "CULTPASS PLAY"
		}
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subtitle, _ = sushi.NewTextSnippet(productCategory)
		subtitle.SetColor(subtitleColor)
		subtitle.SetFont(subtitleFont)
		subtitle.SetKerning(1)
	}

	_, daysLeftOrAgo := getProgressBar(ctx, elem)

	tcSubtitle1 := getMembershipStatusText(ctx, elem, daysLeftOrAgo)

	top_container := &sushi.PurchaseWidgetItemTopContainer{
		Image:     profilePicture,
		Title:     title,
		Subtitle:  subtitle,
		Subtitle1: tcSubtitle1,
	}

	snippetSubtitle1, _ := sushi.NewTextSnippet("Hurry! your favourite center might get sold out")
	snippetSubtitle1Set := true
	if sold_out_state && elem.MembershipStatus == userPB.MembershipCard_EXPIRED {
		reservationText := getReservationTextWithDate(ctx, elem)
		snippetSubtitle1, _ = sushi.NewTextSnippet(reservationText)
	}
	if elem.MembershipStatus == userPB.MembershipCard_CANCELLED {
		cancellationText := getCancellationTextWithDate(ctx, elem)
		snippetSubtitle1, _ = sushi.NewTextSnippet(cancellationText)
	}
	if snippetSubtitle1Set {
		colorType := sushi.ColorTypeOrange
		if featuresupport.SupportsNewColor(ctx) {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		snippetSubtitle1Color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		snippetSubtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		snippetSubtitle1.SetColor(snippetSubtitle1Color)
		snippetSubtitle1.SetFont(snippetSubtitle1Font)
	}

	bcBgcolor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	bottom_container := &sushi.PurchaseWidgetItemBottomContainer{
		BgColor: bcBgcolor,
	}

	if offerText != "" {
		bcTitle, _ := sushi.NewTextSnippet(offerText)
		bcTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		bcTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
		bcTitle.SetColor(bcTitleColor)
		bcTitle.SetFont(bcTitleFont)
		bcTitle.IsMarkdown = 1
		bottom_container.Title = bcTitle
		color1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
		bottom_container.BgColor = color1
	}
	if offerSubtext != "" {
		bcSubTitle, _ := sushi.NewTextSnippet(offerSubtext)
		bcSubTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
		bcSubTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
		bcSubTitle.SetColor(bcSubTitleColor)
		bcSubTitle.SetFont(bcSubTitleFont)
		bottom_container.Subtitle = bcSubTitle
	}

	button := getButtonForRenewalTile(ctx, elem, offerText)

	bottom_container.Button = button

	color1, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	color2, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	snippetGradient, _ := sushi.NewGradient([]sushi.Color{*color1, *color2})

	//clickAction for tap on tile and redirect to renewalDeeplink(as set from handler)
	snippetClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	snippetDeeplink := &sushi.Deeplink{
		URL: elem.RenewalButtonDeeplink,
	}

	snippetClickAction.SetDeeplink(snippetDeeplink)
	clevertapTrackingItems := getRenewalTileImpressionTrackingItem(ctx, elem)

	borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
	item := &sushi.PurchaseWidgetSnippet3Item{
		TopContainer:      top_container,
		BottomContainer:   bottom_container,
		ClevertapTracking: clevertapTrackingItems,
		ClickAction:       snippetClickAction,
		Gradient:          snippetGradient,
		CornerRadius:      12,
		BorderColor:       borderColor,
	}
	if snippetSubtitle1Set {
		item.Subtitle1 = snippetSubtitle1
	}
	if isLoggedInUserCard && (elem.MembershipStatus == userPB.MembershipCard_EXPIRED || elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON) {
		h.PageData.LoggedInUserRenewalDeepLink = append(h.PageData.LoggedInUserRenewalDeepLink, elem.RenewalButtonDeeplink)
	}
	return item
}
func getReservationTextWithDate(ctx context.Context, elem *userPB.MembershipCard) string {
	reservationText := "Hurry! your favourite center might get sold out"
	if elem.SubscriptionEndDate > 0 {
		today := time.Now()
		dateToCheck := time.Now()
		dateToCheck = time.Unix(elem.SubscriptionEndDate, 0)

		if int32(today.Sub(dateToCheck).Hours()/24) <= common.DAYS_TO_RESERVE_SOLDOUT_FACILITY_FOR_EXPIRED_USER {
			reservedTill := dateToCheck.AddDate(0, 0, common.DAYS_TO_RESERVE_SOLDOUT_FACILITY_FOR_EXPIRED_USER)
			if reservedTill.After(today) {
				date := reservedTill.Format("2 Jan, 03:04 PM")
				date1 := strings.Split(date, ",")
				reservationText = "Your seat is reserved till " + date1[0] + " at the preferred center"
			}
		}
	}
	return reservationText
}

func getCancellationTextWithDate(ctx context.Context, elem *userPB.MembershipCard) string {
	cancellationText := "You've cancelled your membership"
	if elem.CancellationDate != nil {
		cancellationDate := elem.CancellationDate
		dateToCheck := time.Now()
		dateToCheck, _ = ptypes.Timestamp(cancellationDate)
		location, _ := time.LoadLocation("Asia/Kolkata") //IST
		dateToCheck = dateToCheck.In(location)
		date := dateToCheck.Format("2 Jan, 03:04 PM")
		date1 := strings.Split(date, ",")
		cancellationText = cancellationText + " on " + date1[0]
	}
	return cancellationText
}

func getRenewalTileImpressionTrackingItem(ctx context.Context, elem *userPB.MembershipCard) []*sushi.ClevertapItem {
	//Setting tileImpression tracking
	tileImpression := getRenewalTileTrackingParams(ctx, elem)
	impressionEname := &sushi.EnameData{
		Ename: "renewal_tile_impression",
	}
	tileImpressionEvents := sushi.NewClevertapEvents()
	tileImpressionEvents.SetImpression(impressionEname)
	impressionTrackItem := sushi.GetClevertapTrackItem(ctx, tileImpression, tileImpressionEvents)

	tileTap := getRenewalTileTrackingParams(ctx, elem)
	tapEname := &sushi.EnameData{
		Ename: "renewal_tile_tap",
	}
	tileTapEvents := sushi.NewClevertapEvents()
	tileTapEvents.SetTap(tapEname)
	tapTrackItem := sushi.GetClevertapTrackItem(ctx, tileTap, tileTapEvents)

	clevertapTrackingItems := []*sushi.ClevertapItem{impressionTrackItem, tapTrackItem}
	return clevertapTrackingItems
}

func getButtonForRenewalTile(ctx context.Context, elem *userPB.MembershipCard, offerText string) *sushi.Button {
	clickActionPurchase := sushi.GetClickAction()

	deeplinkPurchase := sushi.Deeplink{
		URL: elem.RenewalButtonDeeplink,
	}
	clickActionPurchase.SetDeeplink(&deeplinkPurchase)
	button, _ := sushi.NewButton(sushi.ButtontypeText)
	button.SetSize(sushi.ButtonSizeMedium)
	button.SetAlignment(sushi.ButtonAlignmentRight)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	suffixIcon, _ := sushi.NewIcon("e932", iconColor)
	button.SetSuffixIcon(suffixIcon)
	button.SetText("Renew Now ") // remove space after UI fix is live from app.
	if elem.MembershipStatus == userPB.MembershipCard_CANCELLED && !featuresupport.SupportsRenewalTileV2(ctx) {
		button.SetText("Buy Now ")
	}
	if offerText == "" && featuresupport.SupportsRenewalTileV2(ctx) {
		button.SetText("Renew your membership")
		button.SetAlignment(sushi.ButtonAlignmentCenter)
	} else if offerText != "" {
		button.SetText("Renew Now ")
		color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		button.Color = color
		suffixIcon, _ := sushi.NewIcon("e932", color)
		button.SetSuffixIcon(suffixIcon)
	}

	button.ClickAction = clickActionPurchase

	tileClick := getRenewalTileTrackingParams(ctx, elem)
	clickEname := &sushi.EnameData{
		Ename: "renewal_tile_renew_now_click",
	}
	tileClickEvents := sushi.NewClevertapEvents()
	tileClickEvents.SetTap(clickEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tileClick, tileClickEvents)
	button.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	return button
}
func getProgressBar(ctx context.Context, elem *userPB.MembershipCard) (*sushi.ProgressBar, int32) {
	pb_bg_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)

	progressColor1, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
	progressColor2, _ := sushi.NewColor(sushi.ColorTypePurple, sushi.ColorTint400)

	pb_progress_colors := []*sushi.Color{progressColor1, progressColor2}
	var progressValue int32
	progressValue = 100
	daysLeftOrAgo := int32(0)
	if elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON {
		daysLeftOrAgo = getDaysLeftOrAgo(elem.SubscriptionEndDate)
		progressValue = (expiringSoonDays - daysLeftOrAgo) * 100 / expiringSoonDays

	} else if elem.MembershipStatus == userPB.MembershipCard_EXPIRED {
		daysLeftOrAgo = -1 * getDaysLeftOrAgo(elem.SubscriptionEndDate)
		progressColor3, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint600)
		pb_progress_colors = []*sushi.Color{progressColor3, progressColor3}
	} else if elem.MembershipStatus == userPB.MembershipCard_CANCELLED {
		progressValue = 75
		progressColor3, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		pb_progress_colors = []*sushi.Color{progressColor3, progressColor3}
	}
	progress_bar := &sushi.ProgressBar{
		MaxValue:       100,
		Progress:       progressValue,
		BgColor:        pb_bg_color,
		ProgressColors: pb_progress_colors,
	}
	return progress_bar, daysLeftOrAgo
}

func getMembershipStatusText(ctx context.Context, elem *userPB.MembershipCard, daysLeftOrAgo int32) *sushi.TextSnippet {
	var title *sushi.TextSnippet
	blueColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	titleColor := blueColor
	titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)
	if featuresupport.SupportsRenewalTileV2(ctx) {
		titleFont, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
	}
	if elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON {
		title, _ = sushi.NewTextSnippet(subscriptionDaysRemaining(int(daysLeftOrAgo)))
	} else if elem.MembershipStatus == userPB.MembershipCard_EXPIRED {
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		titleColor, _ = sushi.NewColor(colorType, sushi.ColorTint500)
		if daysLeftOrAgo > 1 {
			title, _ = sushi.NewTextSnippet("Expired <semibold-100|" + strconv.Itoa(int(daysLeftOrAgo)) + " days> ago!")
		} else if daysLeftOrAgo == 1 {
			title, _ = sushi.NewTextSnippet("Expired <semibold-100|" + strconv.Itoa(int(daysLeftOrAgo)) + " day> ago!")
		} else if daysLeftOrAgo == 0 {
			title, _ = sushi.NewTextSnippet("Expires <semibold-100|today!>")
		}
	} else if elem.MembershipStatus == userPB.MembershipCard_CANCELLED {
		colorType := sushi.ColorTypeOrange
		if isNewColorSupported {
			colorType = sushi.ALERT_LIGHT_THEME
		}
		titleColor, _ = sushi.NewColor(colorType, sushi.ColorTint500)
		cancelledText := "Membership cancelled!"
		title, _ = sushi.NewTextSnippet(cancelledText)
	}
	title.SetColor(titleColor)
	if featuresupport.SupportsRenewalTileV2(ctx) {
		title.SetColor(blueColor)
	}
	title.SetFont(titleFont)
	title.IsMarkdown = 1

	return title
}

func GetSoldOutStateForFs(ctx context.Context, elem *userPB.MembershipCard) bool {
	IsSoldOut := false
	pcl := util.GetProductClient()
	req := &productPB.GetFacilitySportByUserProductReq{
		ProductId: elem.ProductId,
		UserId:    elem.UserId,
	}
	fsIdResponse, err := pcl.GetFacilitySportByUserProduct(ctx, req)
	if err != nil {
		log.Println("Error in finding fsId for productId and userId: ", elem.ProductId, elem.UserId)
		return false
	}
	if fsIdResponse.FsId > 0 {
		IsSoldOut = util.IsHighPeakSlotFacilitySport(ctx, fsIdResponse.FsId)
	}

	return IsSoldOut
}

func getRenewalTileTrackingParams(ctx context.Context, elem *userPB.MembershipCard) map[string]interface{} {
	tileClick := make(map[string]interface{})
	tileClick["user_id"] = util.GetUserIDFromContext(ctx)
	tileClick["product_details"] = elem.ProductId
	tileClick["source"] = "HomePage"
	tileClick["city"] = elem.ProductLocation
	tileClick["member"] = elem.UserId
	if elem.MembershipStatus == userPB.MembershipCard_EXPIRING_SOON {
		tileClick["renewal_offer_shown"] = true
	}
	return tileClick
}

func GetHomeDetails(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	ctx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()
	log.Printf("GetHomeDetails: Error Provider: City Id - %d", util.GetCityIDFromContext(ctx))
	loggedInUserId := util.GetUserIDFromContext(ctx)
	template := HomePageTemplate{
		PageData: HomePageData{},
	}
	template.GetUserSubscriptionStatus(ctx)
	template.SetCitySportsFacilityCount(ctx)
	template.SetPageTracking(ctx)
	if template.PageData.AvailableSportsCount == 0 {
		template.SetEmptyHomePageSection(ctx)
		template.SetNotificationCount(ctx)
		template.Sections = make([]*homeModels.ResultSection, 0)
	} else {
		template.SetShortNoticeSection(ctx)
		template.SetBannerSection(ctx)
		if featuresupport.SupportsRenewalTile(ctx) && loggedInUserId > 0 {
			template.SetMembershipRenewalTilesSection(ctx)
		}
		template.SetBookAgainSection(c)
		template.SetSportFacilityCountSection(c)
		template.SetNearbyFacilitySection(ctx)
		template.SetFooterSection(c)
		template.SetNotificationCount(ctx)
		template.SetPostbackParams(ctx)
		template.SetReferralRewardCustomPopup(ctx)
	}
	c.JSON(http.StatusOK, template)
}

func (h *HomePageTemplate) GetUserSubscriptionStatus(ctx context.Context) {
	productClient := util.GetProductClient()
	userSubscriptionStatusRequest := &productPB.GetUserSubscriptionStatusRequest{
		UserId:                    util.GetUserIDFromContext(ctx),
		ProductSuggestionRequired: true,
		CheckChildSub:             true,
	}
	userSubscriptionStatusResponse, err := productClient.GetUserSubscriptionStatus(ctx, userSubscriptionStatusRequest)
	if err != nil {
		log.Printf("Api: home, function: GetUserSubscriptionStatus, Error: %v", err)
		return
	}
	if userSubscriptionStatusResponse == nil {
		log.Println("Api: home, function: GetUserSubscriptionStatus, no response")
		return
	}
	h.PageData.UserSubscriptionStatus = userSubscriptionStatusResponse
}

func (h *HomePageTemplate) SetCitySportsFacilityCount(ctx context.Context) {
	facilityClient := util.GetFacilitySportClient()
	request := &facilitySportPB.GetCityActiveSportsWithFacilityCountRequest{
		DistanceFilterId: distanceFilterId,
	}
	response, err := facilityClient.GetCityActiveSportsWithFacilityCount(ctx, request)
	if err != nil {
		log.Printf("Api: home, function: GetCitySportsFacilityCount, Error: %v", err)
		return
	}
	h.PageData.FacilityCountDetails = response.FacilityCountDetails

	h.PageData.AvailableSportsCount = 0
	if len(h.PageData.FacilityCountDetails) > 0 {
		h.PageData.AvailableSportsCount = h.PageData.FacilityCountDetails[0].AvailableSportsCount
	}
}

func (h *HomePageTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.HomePageNearbyFacilityResponse {
	if h.PageData.NearbyFacilities != nil {
		return h.PageData.NearbyFacilities
	}

	facilityClient := util.GetFacilitySportClient()
	request := &facilitySportPB.HomePageNearbyFacilityRequest{
		Count:               facilityCount,
		PreviousFacilityIds: make([]int32, 0),
	}
	response, err := facilityClient.GetHomePageNearbyFacilities(ctx, request)
	if err != nil {
		log.Printf("Api: home, function: GetNearbyFacilities, Error: %v", err)
		return nil
	}
	h.PageData.NearbyFacilities = response
	return h.PageData.NearbyFacilities
}

func (h *HomePageTemplate) AddPostbackParam(key string, val interface{}) {
	if h.PageData.PostbackParams == nil {
		h.PageData.PostbackParams = make(map[string]interface{})
	}
	h.PageData.PostbackParams[key] = val
}

func (h *HomePageTemplate) SetHasMore(hasMore bool) {
	h.HasMore = hasMore
}

func (h *HomePageTemplate) SetPostbackParams(ctx context.Context) {
	if len(h.PageData.PostbackParams) == 0 {
		return
	}
	postbackParam, _ := json.Marshal(h.PageData.PostbackParams)
	h.PostbackParams = string(postbackParam)
}

func (h *HomePageTemplate) SetPageType(pageType string) {
	h.PageType = pageType
}

func (h *HomePageTemplate) AddSection(section *homeModels.ResultSection) {
	h.Sections = append(h.Sections, section)
}

func (h *HomePageTemplate) AddFooter(footer *sushi.FooterSnippetType2Layout) {
	h.Footer = footer
}

func (h *HomePageTemplate) AddEmptyView(ctx context.Context, emptyView *sushi.EmptyViewType1Layout) {
	h.EmptyView = emptyView

	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["city_id"] = util.GetCityIDFromContext(ctx)
	payload["subzone_id"] = util.GetSubzoneIDFromContext(ctx)
	payload["source"] = "home"
	landingEname := &sushi.EnameData{
		Ename: "uncharted_territory_impression",
	}
	pageEvents := sushi.NewClevertapEvents()
	pageEvents.SetPageSuccess(landingEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, pageEvents)

	h.ClevertapTracking = append(h.ClevertapTracking, trackItem)
}

func (h *HomePageTemplate) SetUnreadNotificationCount(unreadNotificationCount int32) {
	h.UnreadNotificationCount = unreadNotificationCount
}

func (h *HomePageTemplate) SetShortNoticeSection(ctx context.Context) {
	userServiceClient := util.GetUserServiceClient()
	shortNoticeResponse, err := userServiceClient.GetShortNotice(ctx, &userPB.GetShortNoticeRequest{
		ProductCategoryId: common.MasterkeyCategoryID,
	})
	if err != nil {
		log.Printf("Api: home, function: SetShortNoticeSection, Error: %v", err)
		return
	}
	if len(shortNoticeResponse.ShortNotice) == 0 {
		return
	}
	shortNoticeData := shortNoticeResponse.ShortNotice[0]

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType32,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	items := make([]sushi.ImageTextSnippetType32SnippetItem, 0)
	item := sushi.ImageTextSnippetType32SnippetItem{}

	imageLeft, _ := sushi.NewImage(util.GetCDNLink(common.SHORT_NOTICE_ICON_PATH))
	placeholderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	imageLeft.SetPlaceholderColor(placeholderColor)
	imageLeft.SetType(sushi.ImageTypeCircle)
	item.Image = imageLeft

	title, _ := sushi.NewTextSnippet(shortNoticeData.Title)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
	title.SetFont(font)
	item.Title = title

	subtitle, _ := sushi.NewTextSnippet(shortNoticeData.Subtitle)
	fontSubtitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	colorSubtitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	subtitle.SetFont(fontSubtitle)
	subtitle.SetColor(colorSubtitle)
	subtitle.SetNumberOfLines(3)
	item.Subtitle1 = subtitle

	isSetButton := false
	actionUrl := shortNoticeData.ActionUrl
	actionId := shortNoticeData.ActionId
	actionContent := shortNoticeData.ActionContent
	bottomSheetHeader := shortNoticeData.BottomSheetHeader
	shortNoticePoints := shortNoticeData.ShortNoticePoints
	if (len(actionUrl) > 0 || len(shortNoticePoints) > 0 || actionId == common.SUGGESTION_ACTION_CONTEXT_SWITCH) && len(actionContent) > 0 && actionId != common.SUGGESTION_ACTION_SPECIAL_NOTICE {
		isSetButton = true
	}

	if isSetButton {
		button, _ := sushi.NewButton(sushi.ButtontypeText)
		button.SetText(shortNoticeData.ActionContent)
		icon, _ := sushi.NewIcon(sushi.RightIcon, nil)
		button.SetSuffixIcon(icon)
		buttonColorArr := strings.Split(shortNoticeData.ActionContentColor, "&")
		if len(buttonColorArr) == 2 {
			buttonColorType := sushi.ColorType(buttonColorArr[0])
			buttonColorTint := sushi.ColorTint(buttonColorArr[1])
			buttonColor, _ := sushi.NewColor(buttonColorType, buttonColorTint)
			button.SetColor(buttonColor)
		}
		if actionId == common.SUGGESTION_ACTION_CONTEXT_SWITCH && featuresupport.SupportsContextSwitch(ctx) {
			// to be replaced with dynamic pageType supposed to come from dashboard
			item.BottomButton = button
			item.ClickAction = getFeatureKeySaveClickAction(ctx, common.PAGE_TYPE_ACADEMY, false, true)
		}

		if (len(shortNoticePoints) > 0 && len(bottomSheetHeader) > 0 && actionId != common.SUGGESTION_ACTION_CONTEXT_SWITCH) || (actionId == common.SUGGESTION_ACTION_CONTEXT_SWITCH && !featuresupport.SupportsContextSwitch(ctx)) {

			reasonsSheetTitle, _ := sushi.NewTextSnippet(bottomSheetHeader)
			bottomSheetLeftImage, _ := sushi.NewImage(util.GetCDNLink(common.SHORT_NOTICE_ICON_PATH))
			header := &sushi.GenericBottomSheetHeader{
				Title: reasonsSheetTitle,
				Image: bottomSheetLeftImage,
			}

			pointerItems := make([]sushi.V2ImageTextSnippetType36SnippetItem, 0)

			for _, noticePoint := range shortNoticePoints {

				colorTitle, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint600)
				fontTitle, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)

				title, _ := sushi.NewTextSnippet(noticePoint)
				title.SetColor(colorTitle)
				title.SetFont(fontTitle)

				image, _ := sushi.NewImage(util.GetCDNLink(common.BULLET_IMAGE_PATH))
				image.SetAspectRatio(1)
				image.SetType(sushi.ImageTypeCircle)
				image.SetHeight(6)
				image.SetWidth(6)

				item := sushi.V2ImageTextSnippetType36SnippetItem{
					Image: image,
					Title: title,
				}
				pointerItems = append(pointerItems, item)
			}

			pointerSnippetItems := &sushi.V2ImageTextSnippetType36Snippet{
				Items: &pointerItems,
			}

			layoutConfigType36 := &sushi.LayoutConfig{
				SnippetType:  sushi.V2ImageTextSnippetType36,
				LayoutType:   sushi.LayoutTypeGrid,
				SectionCount: 1,
				ShouldResize: true,
			}

			pointerSnippetItem := &sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:             layoutConfigType36,
				V2ImageTextSnippetType36: pointerSnippetItems,
			}

			openGenericBottomSheet := &sushi.OpenGenericBottomSheet{
				Header: header,
				Items:  []*sushi.CustomTextSnippetTypeLayout{pointerSnippetItem},
			}

			openSheetClickAction := sushi.GetClickAction()
			openSheetClickAction.SetGenericBottomSheet(openGenericBottomSheet)

			item.BottomButton = button
			item.ClickAction = openSheetClickAction
		} else if actionId == common.SUGGESTION_ACTION_APP_WEB_LINK {
			//clickAction for in_app_web
			clickAction := sushi.GetClickAction()
			openWebview := sushi.OpenWebview{
				URL:   actionUrl,
				InApp: true,
			}
			clickAction.SetOpenWebview(&openWebview)

			item.BottomButton = button
			item.ClickAction = clickAction
		} else if actionId == common.SUGGESTION_ACTION_WEB_LINK {
			//clickAction for web
			clickAction := sushi.GetClickAction()
			openWebview := sushi.OpenWebview{
				URL: util.AddGetParamURL(actionUrl, "force_browser", "1"),
			}
			clickAction.SetOpenWebview(&openWebview)

			item.BottomButton = button
			item.ClickAction = clickAction
		} else if actionId > 0 {
			deeplink := h.getSuggestionDeeplink(ctx, actionId, actionUrl, shortNoticeData.ActionUrlPostbackParams)
			if len(deeplink.URL) > 0 {
				clickAction := sushi.GetClickAction()
				clickAction.SetDeeplink(&deeplink)

				item.BottomButton = button
				item.ClickAction = clickAction
			}
		}
	}
	items = append(items, item)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	bgColorArr := strings.Split(shortNoticeData.BackgroundColor, "&")
	if len(bgColorArr) == 2 {
		bgColorType := sushi.ColorType(bgColorArr[0])
		bgColorTint := sushi.ColorTint(bgColorArr[1])
		bgColor, _ = sushi.NewColor(bgColorType, bgColorTint)
	}

	snippetData := &sushi.ImageTextSnippetType32Snippet{
		Items:   &items,
		BgColor: bgColor,
	}
	section := homeModels.ResultSection{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType32: snippetData,
	}
	h.AddSection(&section)
}

func (h *HomePageTemplate) SetOfferSection(ctx context.Context) {
	ucl := util.GetUserServiceClient()
	response, err := ucl.GetOffer(ctx, &userPB.Empty{})
	if err != nil {
		log.Printf("Api: home, function: GetOffer, Error: %v", err)
		return
	}

	if response.ShowOffers == false {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	if util.IsIOS(ctx) {
		layoutConfig.ShouldResize = true
	}

	items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	type offer struct {
		Image string
		Title string
	}
	offerSection := []offer{}
	acrossAllSportsAndCentersTitle := "Across <bold-300|multiple sports> and <bold-300|centers>"
	if util.GetCityIDFromContext(ctx) == common.CITY_ID_BENGALURU || util.GetCityIDFromContext(ctx) == common.CITY_ID_HYDERABAD { // modifying text for banglore and hyderabad
		acrossAllSportsAndCentersTitle = "Across <bold-300|all centers>"
	}

	offerSection = append(offerSection,
		offer{
			Image: "https://fitso-images.curefit.co/uploads/Coach--.png",
			Title: "Guided by <bold-300|certified coaches>",
		},
		offer{
			Image: "https://fitso-images.curefit.co/uploads/Playingpartner--.png",
			Title: "With guaranteed <bold-300|playing partners>",
		},
		offer{
			Image: "https://fitso-images.curefit.co/uploads/Group227421632313817.png",
			Title: acrossAllSportsAndCentersTitle,
		},
	)

	for _, offer := range offerSection {
		item := sushi.ImageTextSnippetType30SnippetItem{}

		image, _ := sushi.NewImage(offer.Image)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(24)
		item.Image = image

		title, _ := sushi.NewTextSnippet(offer.Title)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint100)
		title.SetFont(font)
		title.SetColor(color)
		title.SetIsMarkdown(1)
		item.Title = title

		items = append(items, item)
	}

	offerSnippetData := &sushi.ImageTextSnippetType30Snippet{
		Items: &items,
	}

	title, _ := sushi.NewTextSnippet("Play unlimited sport at premium facilities")
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize600)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	title.SetColor(color)

	if util.IsIOS(ctx) {
		font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		title.SetFont(font)
		offerSnippetData.Subtitle = title

		button, _ := sushi.NewButton(sushi.ButtontypeText)
		button.SetText("Book a trial now")

		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		button.SetFont(font)
		button.SetAlignment(sushi.ButtonAlignmentLeft)

		rightArrowIcon, _ := sushi.NewIcon(sushi.RightIcon, nil)
		button.SetSuffixIcon(rightArrowIcon)

		clickAction := sushi.GetClickAction()
		auth_title, _ := sushi.NewTextSnippet("Please login to book your free trial. Enter your phone number to login using OTP.")
		payload := map[string]interface{}{
			"post_action": "trial_sports",
		}
		postback_params, _ := json.Marshal(payload)
		auth := &sushi.Auth{
			Title:          auth_title,
			PostbackParams: string(postback_params),
			Source:         "home",
		}
		clickAction.SetAuth(auth)
		button.SetClickAction(clickAction)
		offerSnippetData.BottomButton = button

		offerSection := homeModels.ResultSection{
			LayoutConfig:           layoutConfig,
			ImageTextSnippetType30: offerSnippetData,
		}
		h.AddSection(&offerSection)

	} else {
		title.SetFont(font)
		offerSnippetData.Title = title

		offerSection1 := homeModels.ResultSection{
			LayoutConfig:           layoutConfig,
			ImageTextSnippetType30: offerSnippetData,
		}
		h.AddSection(&offerSection1)

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.FitsoTextSnippetType1,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		deeplinkClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		deeplinkClickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetTrialSportsPageDeeplink(),
		})

		rightArrowIcon, _ := sushi.NewIcon(sushi.RightIcon, nil)

		items := make([]sushi.FitsoTextSnippetType1ButtonItem, 0)
		item := sushi.FitsoTextSnippetType1ButtonItem{
			Type:        sushi.FitsoTextSnippetType1TypeText,
			Text:        "Book a trial now",
			Size:        sushi.FitsoTextSnippetType1ButtonSizeMedium,
			SuffixIcon:  rightArrowIcon,
			ClickAction: deeplinkClickAction,
		}
		items = append(items, item)

		offerSnippetData := &sushi.FitsoTextSnippetType1Snippet{
			ButtonData: &sushi.FitsoTextSnippetType1SnippetButton{
				Orientation: sushi.FitsoTextSnippetType1OrientationHorizontal,
				Items:       &items,
			},
		}
		offerSection2 := homeModels.ResultSection{
			LayoutConfig:          layoutConfig,
			FitsoTextSnippetType1: offerSnippetData,
		}
		h.AddSection(&offerSection2)
	}
}

func (h *HomePageTemplate) SetBannerSection(ctx context.Context) {
	userServiceClient := util.GetUserServiceClient()
	bannersResponse, err := userServiceClient.GetSuggestionsForMasterkey(ctx, &userPB.Suggestion{})
	if err != nil {
		log.Printf("Api: home, function: SetBannerSection, Error: %v", err)
		return
	}
	if len(bannersResponse.Suggestion) == 0 {
		log.Printf("Api: home, function: SetBannerSection, Error: no suggestions for city id %d and user id %d", ctx.Value("city_id"), ctx.Value("user_id"))
		return
	}

	if featuresupport.SupportsTimerSuggestions(ctx) {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:      sushi.FitsoImageTextSnippetType9,
			LayoutType:       sushi.LayoutTypeCarousel,
			SectionCount:     1,
			ShouldAutoScroll: true,
		}
		items := make([]*sushi.FitsoImageTextSnippetType9SnippetItem, 0)

		for _, banner := range bannersResponse.Suggestion {
			if banner.SuggestionId == 99 {
				// hide refer and earn banner for previous versions
				if !featuresupport.SupportsReferralAndEarnPage(ctx) {
					continue
				}
			}

			if banner.SuggestionId == 147 {
				if !featuresupport.SupportsAcademyPurchaseFlow(ctx) {
					continue
				}
			}

			if banner.SuggestionId == 170 {
				//hide bring guest banner for previous versions
				if !featuresupport.SupportsBringAGuest(ctx) {
					continue
				}
			}

			item := &sushi.FitsoImageTextSnippetType9SnippetItem{
				CornerRadius: 12,
			}

			image, _ := sushi.NewImage(banner.SuggestionImage)
			image.SetAspectRatio(1.5)
			item.Image = image
			actionId := banner.ActionId
			if len(banner.Url) > 0 {
				if actionId == common.SUGGESTION_ACTION_WEB_LINK {
					clickAction := sushi.GetClickAction()
					openWebview := sushi.OpenWebview{
						URL: util.AddGetParamURL(banner.Url, "force_browser", "1"),
					}
					clickAction.SetOpenWebview(&openWebview)
					item.ClickAction = clickAction
				} else if actionId == common.SUGGESTION_ACTION_APP_WEB_LINK {
					//clickAction for in_app_web
					clickAction := sushi.GetClickAction()
					openWebview := sushi.OpenWebview{
						URL:   banner.Url,
						InApp: true,
					}
					clickAction.SetOpenWebview(&openWebview)
					item.ClickAction = clickAction
				} else if actionId != 0 {
					deeplink := h.getSuggestionDeeplink(ctx, actionId, banner.Url, banner.CtaPostbackParams)
					if len(deeplink.URL) > 0 {
						clickAction := sushi.GetClickAction()
						clickAction.SetDeeplink(&deeplink)
						item.ClickAction = clickAction
					}
				}

				suggestionTapPayload := make(map[string]interface{})
				suggestionTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
				suggestionTapPayload["city_id"] = util.GetCityIDFromContext(ctx)
				suggestionTapPayload["suggestion_id"] = banner.SuggestionId
				suggestionTapPayload["source"] = "home"
				tapEname := &sushi.EnameData{
					Ename: "suggestion_card_tap",
				}
				suggestionCardEvents := sushi.NewClevertapEvents()
				suggestionCardEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, suggestionTapPayload, suggestionCardEvents)
				item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
			}

			if banner.OfferEndTime != 0 && banner.OfferEndTime > time.Now().Unix() {
				title, _ := sushi.NewTextSnippet("Offer ends in")
				font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
				color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)

				if checkIfDiwaliBanner(banner.SuggestionId) {
					color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
				}

				title.SetFont(font)
				title.SetColor(color)
				item.Title = title

				time := time.Unix(banner.OfferEndTime, 0).Format("02-01-2006 15:04:05")
				subtitle, _ := sushi.NewTextSnippet(time)
				font, _ = sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
				color, _ = sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint900)

				if checkIfDiwaliBanner(banner.SuggestionId) {
					color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
				}

				subtitle.SetFont(font)
				subtitle.SetColor(color)
				subtitle.IsMarkdown = 1
				if featuresupport.SupportsTimerSuggestionsV2(ctx) {
					subtitle.Kerning = 3
				}

				item.Subtitle = subtitle

				bgColor, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint500)

				if checkIfDiwaliBanner(banner.SuggestionId) {
					bgColor, _ = sushi.NewColor(sushi.ColorTypeCider, sushi.ColorTint300)
				}

				item.BgColor = bgColor

				item.PlaceholderFormat = "{$dd}<semibold-100|d> : {$hh}<semibold-100|h> : {$mm}<semibold-100|m> : {$ss}<semibold-100|s>"

			}

			if checkIfDiwaliBanner(banner.SuggestionId) {
				remainingOfferPurchaseCount := getRemainingDiwaliOfferPurchaseCount()
				if remainingOfferPurchaseCount > 0 {
					remainingMemberships := int32(getRemainingDiwaliOfferPurchaseCount())
					title, _ := sushi.NewTextSnippet(fmt.Sprintf("Hurry! Only %d memberships left!", remainingMemberships))
					font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
					color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
					title.SetFont(font)
					title.SetColor(color)
					title.SetAlignment(sushi.TextAlignmentCenter)
					item.Title = title

					bgColor, _ := sushi.NewColor(sushi.ColorTypeCider, sushi.ColorTint300)
					item.BgColor = bgColor
				}
			}

			if actionId == common.SUGGESTION_ACTION_CONTEXT_SWITCH && featuresupport.SupportsContextSwitch(ctx) {
				// to be replaced with dynamic pageType supposed to come from dashboard
				item.ClickAction = getFeatureKeySaveClickAction(ctx, common.PAGE_TYPE_ACADEMY, false, true)
			}
			item.HideGradient = true
			items = append(items, item)
		}

		if len(items) == 0 {
			return
		}

		snippetData := &sushi.FitsoImageTextSnippetType9Snippet{
			Items: items,
		}
		section := homeModels.ResultSection{
			LayoutConfig:               layoutConfig,
			FitsoImageTextSnippetType9: snippetData,
		}
		h.AddSection(&section)

	} else {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:      sushi.ImageTextSnippetType19,
			LayoutType:       sushi.LayoutTypeCarousel,
			SectionCount:     1,
			ShouldAutoScroll: true,
		}
		items := make([]sushi.ImageTextSnippetType19SnippetItem, 0)
		for _, banner := range bannersResponse.Suggestion {
			if banner.SuggestionId == 99 {
				// hide refer and earn banner for previous versions
				if !featuresupport.SupportsReferralAndEarnPage(ctx) {
					continue
				}
			}
			item := sushi.ImageTextSnippetType19SnippetItem{}

			image, _ := sushi.NewImage(banner.SuggestionImage)
			image.SetAspectRatio(1.5)
			item.Image = image
			actionId := banner.ActionId
			if len(banner.Url) > 0 {
				if actionId == common.SUGGESTION_ACTION_WEB_LINK {
					clickAction := sushi.GetClickAction()
					openWebview := sushi.OpenWebview{
						URL: util.AddGetParamURL(banner.Url, "force_browser", "1"),
					}
					clickAction.SetOpenWebview(&openWebview)
					item.ClickAction = clickAction
				} else if actionId == common.SUGGESTION_ACTION_APP_WEB_LINK {
					//clickAction for in_app_web
					clickAction := sushi.GetClickAction()
					openWebview := sushi.OpenWebview{
						URL:   banner.Url,
						InApp: true,
					}
					clickAction.SetOpenWebview(&openWebview)
					item.ClickAction = clickAction
				} else if actionId != 0 {
					deeplink := h.getSuggestionDeeplink(ctx, actionId, banner.Url, banner.CtaPostbackParams)
					if len(deeplink.URL) > 0 {
						clickAction := sushi.GetClickAction()
						clickAction.SetDeeplink(&deeplink)
						item.ClickAction = clickAction
					}
				}

				suggestionTapPayload := make(map[string]interface{})
				suggestionTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
				suggestionTapPayload["city_id"] = util.GetCityIDFromContext(ctx)
				suggestionTapPayload["suggestion_id"] = banner.SuggestionId
				suggestionTapPayload["source"] = "home"
				tapEname := &sushi.EnameData{
					Ename: "suggestion_card_tap",
				}
				suggestionCardEvents := sushi.NewClevertapEvents()
				suggestionCardEvents.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, suggestionTapPayload, suggestionCardEvents)
				item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
			}

			if actionId == common.SUGGESTION_ACTION_CONTEXT_SWITCH && featuresupport.SupportsContextSwitch(ctx) {
				// to be replaced with dynamic pageType supposed to come from dashboard
				item.ClickAction = getFeatureKeySaveClickAction(ctx, common.PAGE_TYPE_ACADEMY, false, true)
			}
			item.HideGradient = true
			items = append(items, item)
		}

		if len(items) == 0 {
			return
		}

		snippetData := &sushi.ImageTextSnippetType19Snippet{
			Items: &items,
		}
		section := homeModels.ResultSection{
			LayoutConfig:           layoutConfig,
			ImageTextSnippetType19: snippetData,
		}
		h.AddSection(&section)
	}

}

func checkIfDiwaliBanner(suggestionID int32) bool {
	return suggestionID == 124 || suggestionID == 122 || suggestionID == 123 || suggestionID == 125 || suggestionID == 131 || suggestionID == 132 || suggestionID == 134
}

func getRemainingDiwaliOfferPurchaseCount() int32 {
	maxPurchases := float64(40)
	startTime := int64(1636538589)    // 10th Nov 03:36:00
	endTimeStamp := int64(1636568999) // 10 Nov 23:59:59

	if time.Now().Unix() > endTimeStamp {
		return 0
	}

	totalTimeDiff := math.Ceil(time.Unix(endTimeStamp, 0).Sub(time.Unix(startTime, 0)).Hours())
	purchasesToDeductEachHour := int32(6)
	offerTimeElapsed := int32(totalTimeDiff) - int32(math.Ceil(time.Unix(endTimeStamp, 0).Sub(time.Now()).Hours()))

	remainingCount := int32(maxPurchases) - (offerTimeElapsed * purchasesToDeductEachHour)
	if remainingCount <= 42 {
		return 12
	}

	return remainingCount
}

func (h *HomePageTemplate) SetFitsoBenefitsSection(ctx context.Context) {
	facilityClient := util.GetFacilitySportClient()
	fitsoBenefitsResponse, err := facilityClient.GetFitsoBenefits(ctx, &facilitySportPB.FitsoBenefitReq{ExtraBenefits: true})

	if err != nil {
		log.Printf("Api: home, function: SetFitsoBenefitsSection, Error: %v", err)
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet(fitsoBenefitsResponse.Title)
	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	sectionHeaderSection := homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	h.AddSection(&sectionHeaderSection)

	layoutConfig = &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 2,
		ShouldResize: true,
	}

	items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _, benefit := range fitsoBenefitsResponse.BenefitDescription {
		item := sushi.ImageTextSnippetType30SnippetItem{}

		image, _ := sushi.NewImage(benefit.Image2)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(24)
		image.SetWidth(24)
		item.Image = image

		title, _ := sushi.NewTextSnippet(benefit.Title)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		title.SetNumberOfLines(2)
		title.SetFont(font)
		title.SetColor(color)
		item.Title = title

		items = append(items, item)
	}
	benefitsSnippetData := &sushi.ImageTextSnippetType30Snippet{
		Items: &items,
	}
	benefitsSection := homeModels.ResultSection{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType30: benefitsSnippetData,
	}
	h.AddSection(&benefitsSection)
}

func (h *HomePageTemplate) SetBookAgainSection(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	source := "book_again_home"

	userID := ctx.Value("user_id")
	if userID == 0 {
		return
	}

	bookingClient := util.GetBookingClient()
	previousBookFsIds, err := bookingClient.GetUserBookAgainFsIds(ctx, &bookingPB.Empty{})
	if err != nil {
		log.Printf("Api: home, function: SetBookAgainSection, Error: %v", err)
		return
	}
	if len(previousBookFsIds.FsIds) == 0 {
		return
	}

	facilityClient := util.GetFacilitySportClient()
	facilitySportsByFsIDRequest := &facilitySportPB.FacilitySportsByFsIdRequest{
		FsIds: previousBookFsIds.FsIds,
	}
	facilitySportsByFsIDResponse, err := facilityClient.GetFacilitySportsDetailsByFsID(ctx, facilitySportsByFsIDRequest)
	if err != nil {
		log.Printf("Api: home, function: SetBookAgainSection, Error: %v", err)
		return
	}
	if len(facilitySportsByFsIDResponse.FacilitySports) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType35,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	items := make([]sushi.V2ImageTextSnippetType35SnippetItem, 0)
	userSubscriptionStatus := h.PageData.UserSubscriptionStatus
	loggedInUser := util.GetUserIDFromContext(ctx)
	loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)
	for _, val := range facilitySportsByFsIDResponse.FacilitySports {
		facility := val.Facility[0]
		sport := val.Sport[0]
		item := sushi.V2ImageTextSnippetType35SnippetItem{}

		image, _ := sushi.NewImage(sport.DisplayPicture)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeRounded)
		image.SetHeight(72)
		image.SetWidth(72)
		item.Image = image

		title, _ := sushi.NewTextSnippet(sport.SportName)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title.SetFont(font)
		title.SetColor(color)
		item.Title = title

		subtitle1, _ := sushi.NewTextSnippet(facility.DisplayName)
		color, _ = sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		subtitle1.SetFont(font)
		subtitle1.SetColor(color)
		subtitle1.SetNumberOfLines(2)
		item.SubTitle1 = subtitle1

		if bookingElapsedTime, ok := previousBookFsIds.BookingElapsedTime[val.FsId]; ok {
			subtitle2, _ := sushi.NewTextSnippet(bookingElapsedTime)
			color, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
			subtitle2.SetFont(font)
			subtitle2.SetColor(color)
			item.SubTitle2 = subtitle2
		}

		clickAction := sushi.GetClickAction()
		tomorrowSubscription := userSubscriptionStatus.TomorrowParentSubscription || userSubscriptionStatus.TomorrowChildSubscription
		if userSubscriptionStatus.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED || (userSubscriptionStatus.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION && !tomorrowSubscription) {
			customAlert := util.CustomAlertForExploreSlots(ctx, val.FsId, userSubscriptionStatus, source)
			clickAction.SetCustomAlertAction(customAlert)
		} else {
			var openBuddyList bool
			if userSubscriptionStatus.ActiveChildSubscriptions {
				openBuddyList = true
			} else {
				openBuddyList = false
			}

			addAgeDeeplink, addBookingFlowDeeplink, addMedicalFormDeeplink := false, false, false
			isTrial := userSubscriptionStatus.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL || userSubscriptionStatus.SubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE

			if openBuddyList == true {
				addBookingFlowDeeplink = true
			} else { // single user
				if !featuresupport.SupportsMedicalForm(ctx) {
					if loggedInUserAge >= util.GetMinAgeForBooking(isTrial, h.PageData.ProductCategoryId) {
						addBookingFlowDeeplink = true
					} else {
						addAgeDeeplink = true
					}
				} else { // supports medical form UI
					if util.NeedMedicalDetails(ctx, loggedInUser) {
						addMedicalFormDeeplink = true
					} else if loggedInUserAge < util.GetMinAgeForBooking(isTrial, h.PageData.ProductCategoryId) {
						addAgeDeeplink = true
					} else {
						addBookingFlowDeeplink = true
					}
				}
			}

			if addBookingFlowDeeplink {
				deeplink, _ := util.GetBookingFlowDeepLink(ctx, val.FsId, openBuddyList, source)
				clickAction.SetDeeplink(&deeplink)
			} else if addAgeDeeplink {
				obj := map[string]interface{}{
					"fs_id":        val.FsId,
					"bottom_sheet": 1,
					"source":       source,
				}
				post_params, _ := json.Marshal(obj)
				payload := map[string]interface{}{
					"post_action":     "slots_get_home",
					"user_id":         loggedInUser,
					"postback_params": string(post_params),
				}
				postback_params, _ := json.Marshal(payload)
				clickAction.Type = sushi.ClickActionOpenAgeBottomSheet
				clickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, isTrial, h.PageData.ProductCategoryId)
			} else if addMedicalFormDeeplink {
				payload := map[string]interface{}{
					"page_type":    common.PAGE_TYPE_HOME,
					"user_id":      loggedInUser,
					"fs_id":        val.FsId,
					"bottom_sheet": 1,
					"source":       source,
				}
				postbackParams, _ := json.Marshal(payload)
				alert := util.GetCompleteMedicalDetailsAlert(ctx, loggedInUser, string(postbackParams))
				clickAction.SetCustomAlertAction(alert)
			}
		}
		item.ClickAction = clickAction

		facilityTapPayload := make(map[string]interface{})
		facilityTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		facilityTapPayload["city_id"] = util.GetCityIDFromContext(ctx)
		facilityTapPayload["facility_id"] = facility.FacilityId
		facilityTapPayload["sport_id"] = sport.SportId
		facilityTapPayload["source"] = "home"
		tapEname := &sushi.EnameData{
			Ename: "book_again_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, facilityTapPayload, facilityCardEvents)
		item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, item)
	}

	if len(items) > 1 {
		layoutConfig.VisibleCards = 1.25
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	}

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["city_id"] = util.GetCityIDFromContext(ctx)
	sectionImpressionPayload["source"] = "home"
	impressionEname := &sushi.EnameData{
		Ename: "book_again_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	header, _ := sushi.NewTextSnippet("Book again")
	snippetData := &sushi.V2ImageTextSnippetType35Snippet{
		Title:             header,
		Items:             &items,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	section := homeModels.ResultSection{
		LayoutConfig:             layoutConfig,
		V2ImageTextSnippetType35: snippetData,
	}
	h.AddSection(&section)
}

func (h *HomePageTemplate) SetSportFacilityCountSection(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	if len(h.PageData.FacilityCountDetails) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Explore sports around you")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
	}
	h.AddSection(section)

	tabSnippetType2Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.TabSnippetType2,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	unselectColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	unselectedBgColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	unselectedBorderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)

	filterConfig := &sushi.TabSnippetType2SnippetConfig{
		BorderColor:           borderColor,
		UnSelectedBorderColor: unselectedBorderColor,
		UnselectedBgColor:     unselectedBgColor,
		BgColor:               bgColor,
		UnSelectedColor:       unselectColor,
		Color:                 color,
	}

	filters := h.PageData.FacilityCountDetails

	var tabOptions []sushi.TabSnippetType2SnippetItem
	for _, val := range filters {
		var tabItem sushi.TabSnippetType2SnippetItem
		if val.AvailableSportsCount > 3 {
			tabItem = h.getFacilityCountTabItemForSports(c, val)
		} else {
			tabItem = h.getFacilityCountTabItemForMimimalSports(ctx, val)
		}

		if len(filters) < 3 {
			// hiding filter tabs if available filters are less than 3
			tabItem.Title = nil
			tabItem.IsSelected = true
		}

		filterTapPayload := make(map[string]interface{})
		filterTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		filterTapPayload["city_id"] = util.GetCityIDFromContext(ctx)
		filterTapPayload["filter_id"] = val.Filters.FilterId
		filterTapPayload["source"] = "home"
		tapEname := &sushi.EnameData{
			Ename: "radius_filter_tap",
		}
		distanceFilterEvents := sushi.NewClevertapEvents()
		distanceFilterEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, filterTapPayload, distanceFilterEvents)
		tabItem.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		tabOptions = append(tabOptions, tabItem)
	}

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["city_id"] = util.GetCityIDFromContext(ctx)
	sectionImpressionPayload["source"] = "home"
	impressionEname := &sushi.EnameData{
		Ename: "sports_facility_count_section_impr",
	}
	sportsListingEvents := sushi.NewClevertapEvents()
	sportsListingEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sportsListingEvents)

	if len(tabOptions) == 1 || h.PageData.AvailableSportsCount < 3 {
		// no need for tabSnippetType2Layout, as we're hiding filter tabs
		filterSnippet := *tabOptions[0].Snippets
		snippet := filterSnippet[0]

		section = &homeModels.ResultSection{
			LayoutConfig:           snippet.LayoutConfig,
			ImageTextSnippetType33: snippet.ImageTextSnippetType33,
			ClevertapTracking:      []*sushi.ClevertapItem{trackItem},
		}

		if featuresupport.SupportsNewHomeSportSnippet(c) {
			section.V2ImageTextSnippetType42 = snippet.V2ImageTextSnippetType42
		} else {
			section.ImageTextSnippetType41 = snippet.ImageTextSnippetType41
		}

		h.AddSection(section)
	} else {
		tabSnippet := &sushi.TabSnippetType2Snippet{
			Config:            filterConfig,
			Items:             &tabOptions,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		section = &homeModels.ResultSection{
			LayoutConfig:    tabSnippetType2Layout,
			TabSnippetType2: tabSnippet,
		}
		h.AddSection(section)
	}
}

func (h *HomePageTemplate) getFacilityCountTabItemForMimimalSports(ctx context.Context, response *facilitySportPB.SportFacilitiesCountResponse) sushi.TabSnippetType2SnippetItem {
	filterName := "-"
	isSelected := false
	if response.Filters != nil && len(response.Filters.FilterName) > 0 {
		filterName = response.Filters.FilterName
		isSelected = response.Filters.IsSelected
	}
	filterTitle, _ := sushi.NewTextSnippet(filterName)
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType33,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	items := make([]sushi.ImageTextSnippetType33SnippetItem, 0)
	for _, sport := range response.SportFacilitiesCount {

		item := sushi.ImageTextSnippetType33SnippetItem{}

		leftImage, _ := sushi.NewImage(sport.SportImage)
		leftImage.SetAspectRatio(1)
		leftImage.SetType(sushi.ImageTypeCircle)
		leftImage.SetHeight(42)
		leftImage.SetWidth(42)
		bgColor, _ := sushi.NewColor(sushi.ColorType(sport.BackgroundColor), sushi.ColorTint(sport.BackgroundColorShade))
		leftImage.SetColor(bgColor)
		item.LeftImage = leftImage

		title, _ := sushi.NewTextSnippet(sport.SportName)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		title.SetFont(font)
		item.Title = title

		var subtitle1Text string
		var colorType sushi.ColorType
		var colorTint sushi.ColorTint

		if sport.Tag == common.UPCOMING_FACILITY_SPORT_TAG {
			subtitle1Text = common.UPCOMING_FACILITY_SPORT_TAG
			colorType = sushi.ColorTypeBlue
			colorTint = sushi.ColorTint500
		} else {
			if sport.FacilityCount == 1 {
				subtitle1Text = "1 Center"
				colorType = sushi.ColorTypeGrey
				colorTint = sushi.ColorTint700
			} else if sport.FacilityCount > 1 {
				subtitle1Text = fmt.Sprintf("%d Centers", sport.FacilityCount)
				colorType = sushi.ColorTypeGrey
				colorTint = sushi.ColorTint700
			} else {
				subtitle1Text = fmt.Sprintf("outside %dkm", response.Filters.FilterDistance)
				colorType = sushi.ColorTypeRed
				colorTint = sushi.ColorTint500
			}
		}

		subtitle1, _ := sushi.NewTextSnippet(subtitle1Text)
		color, _ := sushi.NewColor(colorType, colorTint)

		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
		if sport.Tag == common.UPCOMING_FACILITY_SPORT_TAG {
			font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		}
		subtitle1.SetFont(font)
		subtitle1.SetColor(color)
		item.SubTitle1 = subtitle1

		rightColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		rightIcon, _ := sushi.NewIcon(sushi.RightIcon, rightColor)
		item.RightIcon = rightIcon

		tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, sport.Tag) {
			colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleTag, _ := sushi.NewTextSnippet(sport.Tag)
			titleTag.SetColor(colorTag)
			fontTag, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
			titleTag.SetFont(fontTag)

			tag := &sushi.Tag{
				Title: titleTag,
			}

			if !featuresupport.SupportsNewColor(ctx) {
				color := sushi.ColorTypeBlue
				if sport.Tag == common.OPENING_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeTeal
				} else if sport.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeOrange
				}
				tagBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
				tag.BgColor = tagBgColor
			} else {
				if sport.Tag == common.OPENING_FACILITY_SPORT_TAG {
					tagBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
					tag.BgColor = tagBgColor
				} else if sport.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					tagBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
					tag.BgColor = tagBgColor
				} else {
					// gradient for new tag
					tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
					tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
					gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
					tag.Gradient = gradient
				}
			}

			item.Tag1 = tag
		}
		if sport.Tag != common.UPCOMING_FACILITY_SPORT_TAG {
			clickAction := sushi.GetClickAction()
			deeplink := sushi.Deeplink{
				URL: sport.Deeplink,
			}
			if h.PageData.ProductCategoryId == common.SummerCampCategoryID {
				payload := map[string]int32{
					"sport_id":            sport.SportId,
					"product_category_id": common.SummerCampCategoryID,
				}
				postbackParams, _ := json.Marshal(payload)
				deeplink.PostbackParams = string(postbackParams)
			}
			clickAction.SetDeeplink(&deeplink)
			item.ClickAction = clickAction
		}

		subDetailsResponse := h.PageData.UserSubscriptionStatus
		rightImage, _ := sushi.NewImage("")
		if subDetailsResponse != nil && subDetailsResponse.ActiveParentSubscriptions && subDetailsResponse.ProductCategoryId == common.SinglekeyCategoryID && sport.SportId == common.BADMINTON_SPORT_ID {
			if featuresupport.SupportSingleKeyProduct(ctx) {
				rightImage, _ = sushi.NewImage(util.GetCDNLink("uploads/VectorTick11646820175.png"))
				rightImage.SetAspectRatio(1)
				rightImage.SetType(sushi.ImageTypeCircle)
				rightImage.SetHeight(16)
				rightImage.SetWidth(16)
				bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				rightImage.SetColor(bgColor)
				item.RightImage = rightImage
			} else {
				iconColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				icon, _ := sushi.NewIcon(sushi.CheckIcon, iconColor)
				item.Title.SetSuffixIcon(icon)
			}
		}

		sportTapPayload := make(map[string]interface{})
		sportTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		sportTapPayload["city_id"] = util.GetCityIDFromContext(ctx)
		sportTapPayload["sport_id"] = sport.SportId
		sportTapPayload["source"] = "home"
		tapEname := &sushi.EnameData{
			Ename: "sport_icon_tap",
		}
		sportTileEvents := sushi.NewClevertapEvents()
		sportTileEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, sportTapPayload, sportTileEvents)
		item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		item.JumboTracking = []*jumbo.Item{getHomeSportIconTapJumboTrackItem(sport.SportId)}

		items = append(items, item)
	}
	snippet := &sushi.ImageTextSnippetType33Snippet{
		Id:    "sport_facility_count",
		Items: &items,
	}
	section := sushi.CustomTextSnippetTypeLayout{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType33: snippet,
	}
	filterSnippet := []sushi.CustomTextSnippetTypeLayout{section}
	tabOption := sushi.TabSnippetType2SnippetItem{
		Title:      filterTitle,
		IsSelected: isSelected,
		Snippets:   &filterSnippet,
	}
	return tabOption
}

func getHomeSportIconTapJumboTrackItem(sportId int32) *jumbo.Item {
	jumboPayload := make(map[string]interface{})
	jumboPayload["sport_id"] = sportId
	jumboEvents := jumbo.NewEvents()
	sportCardTapEvent := jumbo.GetEventNameObject(jumbo.HomeSportIconTap)
	jumboEvents.SetTap(sportCardTapEvent)
	jumboTrackItem := jumbo.GetJumboTrackItem(jumboPayload, jumboEvents, jumbo.EventsTableName)

	return jumboTrackItem
}

func (h *HomePageTemplate) getFacilityCountTabItemForSports(c *gin.Context, response *facilitySportPB.SportFacilitiesCountResponse) sushi.TabSnippetType2SnippetItem {
	ctx := util.PrepareGRPCMetaData(c)
	// cityID := util.GetCityIDFromContext(ctx)
	filterName := "-"
	isSelected := false
	if response.Filters != nil && len(response.Filters.FilterName) > 0 {
		filterName = response.Filters.FilterName
		isSelected = response.Filters.IsSelected
	}

	filterTitle, _ := sushi.NewTextSnippet(filterName)

	layoutConfig := &sushi.LayoutConfig{
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 3,
	}

	if featuresupport.SupportsNewHomeSportSnippet(c) {
		layoutConfig.SnippetType = sushi.V2ImageTextSnippetType42
	} else {
		layoutConfig.SnippetType = sushi.ImageTextSnippetType41
	}

	items := make([]sushi.ImageTextSnippetType41SnippetItem, 0)
	newItems := make([]sushi.V2ImageTextSnippetType42SnippetItem, 0)

	allSports := response.SportFacilitiesCount
	// if cityID == 1 {
	// 	var filteredSports []*facilitySportPB.SportFacilitiesCount
	// 	var swimmingSport *facilitySportPB.SportFacilitiesCount
	// 	for _, sport := range response.SportFacilitiesCount {
	// 		if sport.SportId == 3 {
	// 			swimmingSport = sport
	// 			continue
	// 		}
	// 		filteredSports = append(filteredSports, sport)
	// 	}
	// 	filteredSports = append(filteredSports, swimmingSport)
	// 	allSports = filteredSports
	// }

	for _, sport := range allSports {
		title, _ := sushi.NewTextSnippet(sport.SportName)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
		title.SetFont(font)

		var tag *sushi.Tag

		tags := []string{common.NEW_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, sport.Tag) {
			colorTag, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			titleTag, _ := sushi.NewTextSnippet(sport.Tag)
			titleTag.SetColor(colorTag)
			fontTag, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize100)
			titleTag.SetFont(fontTag)

			tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
			gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})

			tag = &sushi.Tag{
				Title:    titleTag,
				Gradient: gradient,
			}
		}

		var subtitle1Text string
		var colorType sushi.ColorType
		var colorTint sushi.ColorTint
		if sport.Tag == common.UPCOMING_FACILITY_SPORT_TAG {
			subtitle1Text = common.UPCOMING_FACILITY_SPORT_TAG
			colorType = sushi.ColorTypeBlue
			colorTint = sushi.ColorTint500
		} else {
			if sport.FacilityCount == 1 {
				subtitle1Text = "1 Center"
				colorType = sushi.ColorTypeGrey
				colorTint = sushi.ColorTint700
			} else if sport.FacilityCount > 1 {
				subtitle1Text = fmt.Sprintf("%d Centers", sport.FacilityCount)
				colorType = sushi.ColorTypeGrey
				colorTint = sushi.ColorTint700
			} else {
				subtitle1Text = fmt.Sprintf("outside %dkm", response.Filters.FilterDistance)
				colorType = sushi.ColorTypeRed
				colorTint = sushi.ColorTint500
			}
		}

		subtitle1, _ := sushi.NewTextSnippet(subtitle1Text)
		color, _ := sushi.NewColor(colorType, colorTint)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)

		subtitle1.SetFont(font)
		subtitle1.SetColor(color)

		if sport.Tag != common.UPCOMING_FACILITY_SPORT_TAG {
			iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
			icon, _ := sushi.NewIcon(sushi.RightArrowIcon, iconColor)
			if sport.FacilityCount > 0 {
				subtitle1.SetSuffixIcon(icon)
			}
		}

		var clickAction *sushi.ClickAction
		if sport.Tag != common.UPCOMING_FACILITY_SPORT_TAG {
			clickAction = sushi.GetClickAction()
			deeplink := sushi.Deeplink{
				URL: sport.Deeplink,
			}
			if h.PageData.ProductCategoryId == common.SummerCampCategoryID {
				payload := map[string]int32{
					"sport_id":            sport.SportId,
					"product_category_id": common.SummerCampCategoryID,
				}
				postbackParams, _ := json.Marshal(payload)
				deeplink.PostbackParams = string(postbackParams)
			}
			clickAction.SetDeeplink(&deeplink)
		}

		image, _ := sushi.NewImage(sport.SportImage)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(40)
		image.SetWidth(40)
		bgColor, _ := sushi.NewColor(sushi.ColorType(sport.BackgroundColor), sushi.ColorTint(sport.BackgroundColorShade))
		image.SetColor(bgColor)

		subDetailsResponse := h.PageData.UserSubscriptionStatus
		rightImage, _ := sushi.NewImage("")
		if subDetailsResponse != nil && subDetailsResponse.ActiveParentSubscriptions && subDetailsResponse.ProductCategoryId == common.SinglekeyCategoryID && sport.SportId == common.BADMINTON_SPORT_ID {
			if featuresupport.SupportSingleKeyProduct(ctx) {
				rightImage, _ = sushi.NewImage(util.GetCDNLink("uploads/VectorTick11646820175.png"))
				rightImage.SetAspectRatio(1)
				rightImage.SetType(sushi.ImageTypeCircle)
				rightImage.SetHeight(14)
				rightImage.SetWidth(14)
				bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
				rightImage.SetColor(bgColor)
			} else {
				iconColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
				icon, _ := sushi.NewIcon(sushi.CheckIcon, iconColor)
				title.SetSuffixIcon(icon)
			}
		}

		sportTapPayload := make(map[string]interface{})
		sportTapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		sportTapPayload["sport_id"] = sport.SportId
		sportTapPayload["source"] = "home"
		tapEname := &sushi.EnameData{
			Ename: "sport_icon_tap",
		}
		sportTileEvents := sushi.NewClevertapEvents()
		sportTileEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, sportTapPayload, sportTileEvents)

		// if cityID == 1 && sport.SportId == 3 {
		// 	titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		// 	title.SetColor(titleColor)

		// 	subtitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		// 	subtitle1.SetColor(subtitle1Color)

		// 	if sport.FacilityCount > 0 {
		// 		iconColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		// 		icon, _ := sushi.NewIcon(sushi.RightArrowIcon, iconColor)
		// 		subtitle1.SetSuffixIcon(icon)
		// 	}

		// 	imgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
		// 	image.SetColor(imgColor)

		// 	greyImage, _ := sushi.NewImage(util.GetCDNLink("uploads/Swimming-grey.png"))
		// 	bgColor, _ = sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
		// 	greyImage.SetColor(bgColor)

		// 	greyImage.SetAspectRatio(1)
		// 	greyImage.SetType(sushi.ImageTypeCircle)
		// 	greyImage.SetHeight(40)
		// 	greyImage.SetWidth(40)

		// 	filter, _ := sushi.NewFilter(sushi.FilterTypeGrayScale)
		// 	greyImage.SetFilter(filter)

		// 	image = greyImage
		// }

		if featuresupport.SupportsNewHomeSportSnippet(c) {
			item := sushi.V2ImageTextSnippetType42SnippetItem{}

			item.Title = title
			item.Tag = tag
			item.SubTitle1 = subtitle1
			item.ClickAction = clickAction
			item.Image = image
			item.RightImage = rightImage
			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
			item.JumboTracking = []*jumbo.Item{getHomeSportIconTapJumboTrackItem(sport.SportId)}
			newItems = append(newItems, item)
		} else {
			item := sushi.ImageTextSnippetType41SnippetItem{}

			item.Title = title
			item.Tag = tag
			item.SubTitle1 = subtitle1
			item.ClickAction = clickAction
			item.Image = image
			item.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
			item.JumboTracking = []*jumbo.Item{getHomeSportIconTapJumboTrackItem(sport.SportId)}
			items = append(items, item)
		}
	}

	section := sushi.CustomTextSnippetTypeLayout{
		LayoutConfig: layoutConfig,
	}

	if featuresupport.SupportsNewHomeSportSnippet(c) {
		snippet := &sushi.V2ImageTextSnippetType42Snippet{
			Id:    "sport_facility_count",
			Items: &newItems,
		}
		section.V2ImageTextSnippetType42 = snippet
	} else {
		snippet := &sushi.ImageTextSnippetType41Snippet{
			Id:    "sport_facility_count",
			Items: &items,
		}
		section.ImageTextSnippetType41 = snippet
	}

	filterSnippet := []sushi.CustomTextSnippetTypeLayout{section}
	tabOption := sushi.TabSnippetType2SnippetItem{
		Title:      filterTitle,
		IsSelected: isSelected,
		Snippets:   &filterSnippet,
	}
	return tabOption
}

func (h *HomePageTemplate) SetNearbyFacilitySectionHeader(ctx context.Context) {

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	takeALookText := "Take a look at our sports centers"
	if h.PageData.AvailableSportsCount == 1 {
		if h.PageData.FacilityCountDetails[0].SportFacilitiesCount[0].SportName != "" {
			takeALookText = h.PageData.FacilityCountDetails[0].SportFacilitiesCount[0].SportName + " centers near you"
		}
	}
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	title, _ := sushi.NewTextSnippet(takeALookText)
	title.SetColor(color)
	title.SetFont(font)

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["city_id"] = util.GetCityIDFromContext(ctx)
	sectionImpressionPayload["source"] = "home"
	impressionEname := &sushi.EnameData{
		Ename: "fitso_centers_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
	}
	h.AddSection(section)
}

func (h *HomePageTemplate) SetNearbyFacilitySection(ctx context.Context) {
	response := h.GetNearbyFacilities(ctx)
	if response == nil {
		return
	}
	if len(response.Facilities) == 0 {
		return
	}

	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	h.AddPostbackParam("previous_facility_ids", response.PreviousFacilityIds)
	h.SetNearbyFacilitySectionHeader(ctx)

	for _, facility := range response.Facilities {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType: sushi.ResSnippetType3,
		}
		snippet := &sushi.ResType3Snippet{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sports {
			sportNames = append(sportNames, sport.SportName)
		}
		if len(sportNames) > 0 {
			subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
			snippet.Subtitle1 = subtitle1
		}

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		if facility.Tag != common.UPCOMING_FACILITY_SPORT_TAG {
			clickAction := sushi.GetClickAction()
			deeplink := sushi.Deeplink{
				URL: facility.Deeplink,
			}
			clickAction.SetDeeplink(&deeplink)
			snippet.ClickAction = clickAction
		}

		ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

		tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
		if sharedFunc.ContainsString(tags, facility.Tag) {
			ratingTitle, _ := sushi.NewTextSnippet(facility.Tag)

			if !isNewColorSupported {
				color := sushi.ColorTypeBlue
				if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeTeal
				} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					color = sushi.ColorTypeOrange
				}
				ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
				ratingSnippetBlockItem.BgColor = ratingBgColor
			} else {
				if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
					ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
					ratingBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
					ratingSnippetBlockItem.BgColor = ratingBgColor
				} else {
					// gradient for new tag
					tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
					tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
					gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
					ratingSnippetBlockItem.Gradient = gradient
				}
			}

			ratingSnippetBlockItem.Title = ratingTitle
			ratingSnippetBlockItem.Size = sushi.RatingSize300

		} else {
			ratingTitle, _ := sushi.NewTextSnippet(facility.Rating)
			ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
			if isNewColorSupported {
				ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
			}

			ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
			ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

			ratingSnippetBlockItem.Title = ratingTitle
			ratingSnippetBlockItem.BgColor = ratingBgColor
			ratingSnippetBlockItem.RightIcon = ratingIcon
		}

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}
		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["city_id"] = util.GetCityIDFromContext(ctx)
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "home"
		tapEname := &sushi.EnameData{
			Ename: "facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		section := &homeModels.ResultSection{
			LayoutConfig:    layoutConfig,
			ResType3Snippet: snippet,
		}
		h.AddSection(section)
	}

	h.setSeeMoreSection(ctx)
}

func (h *HomePageTemplate) setSeeMoreSection(ctx context.Context) {
	response := h.GetNearbyFacilities(ctx)
	if response == nil {
		return
	}
	if response.HasMore == false {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ActionSnippetType2,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	id := "see_other_centers"
	button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	button.SetText("see more")
	icon, _ := sushi.NewIcon(sushi.ChevronDownIcon, nil)
	button.SetSuffixIcon(icon)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	button.SetColor(color)
	clickAction := sushi.GetClickAction()
	loadMoreSnippet := &sushi.ForceLoadMoreAndRemoveSnippet{
		SnippetIds: []string{id},
	}
	clickAction.SetForceLoadMoreAndRemoveSnippet(loadMoreSnippet)
	button.SetClickAction(clickAction)

	borderColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint200)
	snippetData := &sushi.ActionSnippetType2Snippet{
		Id:          id,
		Button:      button,
		BorderColor: borderColor,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		ActionSnippetType2: snippetData,
	}
	h.AddSection(section)
}

func (h *HomePageTemplate) SetHeaderSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType6,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	clickAction := getBottomSheetFeatureChangeClickAction(ctx, common.PAGE_TYPE_MASTERKEY)
	t := "CULTPASS PLAY"

	title, _ := sushi.NewTextSnippet(t)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, iconColor)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)
	title.SetSuffixIcon(icon)

	snippet := &sushi.FitsoTextType6Snippet{
		Title:       title,
		ClickAction: clickAction,
	}
	h.Header = &sushi.FitsoTextSnippetType6Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType6: snippet,
	}
}

func (h *HomePageTemplate) SetFeatureBottomSheetSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType6,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	clickAction := getBottomSheetFeatureChangeClickAction(ctx, common.PAGE_TYPE_MASTERKEY)
	t := "CULTPASS PLAY"

	title, _ := sushi.NewTextSnippet(t)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, iconColor)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)
	title.SetSuffixIcon(icon)

	snippet := &sushi.FitsoTextType6Snippet{
		Title:       title,
		ClickAction: clickAction,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType6: snippet,
	}
	h.AddSection(section)
}

func (h *HomePageTemplate) SetFooterSection(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	footerDetailsResponse := h.PageData.UserSubscriptionStatus
	loggedInUserRenewalDeepLink := ""
	if len(h.PageData.LoggedInUserRenewalDeepLink) > 0 {
		loggedInUserRenewalDeepLink = h.PageData.LoggedInUserRenewalDeepLink[0]
	}
	if footerDetailsResponse == nil {
		return
	}

	status := footerDetailsResponse.SubscriptionStatus
	loggedInUser := util.GetUserIDFromContext(ctx)

	var productSubtext string
	var isMarkdownTitle int32
	var isMarkdown int32
	if len(footerDetailsResponse.SuggestedProducts) > 0 {
		subscriptionProduct := footerDetailsResponse.SuggestedProducts[0]
		productSubtext = fmt.Sprintf("₹%d for %d %ss", int32(subscriptionProduct.RetailPrice), subscriptionProduct.Duration, subscriptionProduct.DurationUnit)
		if featuresupport.SupportsMultipleFeaturedProducts(ctx) {
			perMonthPrice := int32(subscriptionProduct.RetailPrice)
			if subscriptionProduct.Duration > 0 {
				perMonthPrice = int32(subscriptionProduct.RetailPrice) / subscriptionProduct.Duration
			}
			productSubtext = fmt.Sprintf("starts from ₹%d per month", int32(perMonthPrice))
		}

		if loggedInUser > 0 {
			pcl := util.GetProductClient()
			reqData := &productPB.Empty{}
			response, err := pcl.GetReferralEligiblity(ctx, reqData)
			if err == nil {
				if response.EligibleForReferral && response.ReferralUserId > 0 {
					discountAmtReq := &productPB.ApplyVoucherRequest{
						CustomAmount: subscriptionProduct.RetailPrice,
					}
					discountAmtResp, err := pcl.GetDiscountAmountForReferral(ctx, discountAmtReq)
					if err != nil {
						log.Printf("func:SetFooterSection, error in getting discounted amount", err)
					} else {
						discountedAmount := int32(subscriptionProduct.RetailPrice) - discountAmtResp.DiscountAmount
						productSubtext = fmt.Sprintf("<regular-100|~~₹%d~~ ₹%d for three months>", int32(subscriptionProduct.RetailPrice), discountedAmount)
						isMarkdownTitle = 1
						isMarkdown = 1
					}
				}
			}
		}
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	clickActionPurchase := sushi.GetClickAction()
	deeplinkPurchase := sushi.Deeplink{
		URL: util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID),
	}
	clickActionPurchase.SetDeeplink(&deeplinkPurchase)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = loggedInUser
	tapPayload["city_id"] = util.GetCityIDFromContext(ctx)
	tapPayload["source"] = "home"

	if featuresupport.SupportsRenewalTile(ctx) && loggedInUserRenewalDeepLink != "" {
		tapEname := &sushi.EnameData{
			Ename: "renew_membership_button_tap",
		}
		buttonItem1Events := sushi.NewClevertapEvents()
		buttonItem1Events.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)
		renewMembershipClickAction := sushi.GetClickAction()
		deeplinkRenew := sushi.Deeplink{
			URL: loggedInUserRenewalDeepLink,
		}
		renewMembershipClickAction.SetDeeplink(&deeplinkRenew)
		buttonItem1 := sushi.FooterSnippetType2ButtonItem{
			Type:              "solid",
			Text:              "Renew Membership",
			ClickAction:       renewMembershipClickAction,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		items = append(items, buttonItem1)
	} else {
		switch status {
		case productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE:
			if !footerDetailsResponse.ActiveParentSubscriptions {
				tapEname := &sushi.EnameData{
					Ename: "buy_membership_button_tap",
				}
				buttonItem1Events := sushi.NewClevertapEvents()
				buttonItem1Events.SetTap(tapEname)
				trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

				buttonItem1 := sushi.FooterSnippetType2ButtonItem{
					Type:              "solid",
					Text:              "Become a cultpass PLAY member",
					Subtext:           productSubtext,
					IsMarkdownTitle:   isMarkdownTitle,
					ClickAction:       clickActionPurchase,
					ClevertapTracking: []*sushi.ClevertapItem{trackItem},
				}
				items = append(items, buttonItem1)
			}
			break
		case productPB.GetUserSubscriptionStatusResponse_TAKE_TRIAL:

			tapEname := &sushi.EnameData{
				Ename: "buy_membership_button_tap",
			}
			buttonItem1Events := sushi.NewClevertapEvents()
			buttonItem1Events.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

			buttonItemPurchase := sushi.FooterSnippetType2ButtonItem{
				ClickAction:       clickActionPurchase,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}

			clickAction := sushi.GetClickAction()
			if loggedInUser > 0 {
				loggedInUserAge := sharedFunc.GetLoggedInUserAge(ctx)
				// Age validation check
				if loggedInUserAge < util.GetMinAgeForBooking(true, h.PageData.ProductCategoryId) {
					payload := map[string]interface{}{
						"post_action": "trial_sports",
						"user_id":     loggedInUser,
					}
					postback_params, _ := json.Marshal(payload)
					clickAction.Type = sushi.ClickActionOpenAgeBottomSheet
					clickAction.OpenAgeBottomSheet = util.GetOpenAgeBottomSheet(string(postback_params), true, true, h.PageData.ProductCategoryId)
				} else {

					deeplink := sushi.Deeplink{}
					if h.PageData.AvailableSportsCount == 1 &&
						h.PageData.FacilityCountDetails[0].SportFacilitiesCount[0].SportId != 0 {
						sportID := h.PageData.FacilityCountDetails[0].SportFacilitiesCount[0].SportId
						payload := map[string]interface{}{
							"sport_id": sportID,
							"is_trial": 1,
							"source":   "home_trial",
						}
						params, _ := json.Marshal(payload)
						deeplink.URL = util.GetTrialSlotsPageDeeplinkWithSportID(sportID)
						deeplink.PostbackParams = string(params)

					} else {
						deeplink.URL = util.GetTrialSportsPageDeeplink()
					}
					clickAction.SetDeeplink(&deeplink)
				}
			} else {
				auth_title, _ := sushi.NewTextSnippet("Please login to book your free trial. Enter your phone number to login using OTP.")
				payload := map[string]interface{}{
					"post_action": "trial_sports",
				}

				postback_params, _ := json.Marshal(payload)
				auth := &sushi.Auth{
					Title:          auth_title,
					PostbackParams: string(postback_params),
					Source:         "home",
				}
				clickAction.SetAuth(auth)
			}

			tapEname = &sushi.EnameData{
				Ename: "book_trail_button_tap",
			}
			buttonItem2Events := sushi.NewClevertapEvents()
			buttonItem2Events.SetTap(tapEname)
			trackItem = sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem2Events)

			buttonItemTrial := sushi.FooterSnippetType2ButtonItem{
				ClickAction:       clickAction,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
				JumboTracking:     []*jumbo.Item{h.getTrialButtonJumboTrackItem(ctx)},
			}

			firstTrial := false
			if footerDetailsResponse.TrialTaken == 0 && !footerDetailsResponse.IsTrialUserWithPastSubscription {
				firstTrial = true
			}
			if firstTrial == true || loggedInUser == 0 {

				buttonItemTrial.Type = "solid"
				buttonItemTrial.Text = "Book a free trial session"
				items = append(items, buttonItemTrial)
				buttonItemPurchase.Type = "text"
				buttonItemPurchase.Text = "or become a cultpass PLAY member"
				items = append(items, buttonItemPurchase)

			} else {
				buttonItemPurchase.Type = "solid"
				buttonItemPurchase.Text = "Become a cultpass PLAY member"
				buttonItemPurchase.Subtext = productSubtext
				buttonItemPurchase.IsMarkdownTitle = isMarkdownTitle
				items = append(items, buttonItemPurchase)

				buttonItemTrial.Type = "text"
				buttonItemTrial.Text = "or book a free trial session"
				items = append(items, buttonItemTrial)
			}

			break

		case productPB.GetUserSubscriptionStatusResponse_TRIAL_SUBSCRIPTION_ACTIVE:
			fallthrough
		case productPB.GetUserSubscriptionStatusResponse_ALL_SUBSCRIPTION_EXPIRED:
			tapEname := &sushi.EnameData{
				Ename: "buy_membership_button_tap",
			}
			buttonItem1Events := sushi.NewClevertapEvents()
			buttonItem1Events.SetTap(tapEname)
			trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, buttonItem1Events)

			buttonItem1 := sushi.FooterSnippetType2ButtonItem{
				Type:              "solid",
				Text:              "Become a cultpass PLAY member",
				Subtext:           productSubtext,
				IsMarkdownTitle:   isMarkdownTitle,
				IsMarkDown:        isMarkdown,
				ClickAction:       clickActionPurchase,
				ClevertapTracking: []*sushi.ClevertapItem{trackItem},
			}
			items = append(items, buttonItem1)
			break
		}
	}

	buttonData := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: buttonData,
	}
	footer := &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
	h.AddFooter(footer)
}

func (h *HomePageTemplate) getTrialButtonJumboTrackItem(ctx context.Context) *jumbo.Item {
	payload := map[string]interface{}{
		"source_page": jumbo.SourcePageHomeTrial,
		"is_trial":    true,
	}
	events := jumbo.NewEvents()
	clickEvent := jumbo.GetEventNameObject(jumbo.BookingButtonClick)
	events.SetTap(clickEvent)
	trackItem := jumbo.NewTrackItem(jumbo.BookingEventsTableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(events)

	return trackItem
}

func (h *HomePageTemplate) SetEmptyHomePageSection(ctx context.Context) {
	items := make([]sushi.FooterSnippetType2ButtonItem, 0)

	clickAction := sushi.GetClickAction()
	deeplink := sushi.Deeplink{
		URL: util.GetChangeLocationDeeplink(),
	}
	clickAction.SetDeeplink(&deeplink)

	tapPayload := make(map[string]interface{})
	tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
	tapPayload["city_id"] = util.GetCityIDFromContext(ctx)
	tapPayload["subzone_id"] = util.GetSubzoneIDFromContext(ctx)
	tapPayload["source"] = "home"
	tapPayload["error"] = "Uncharted Territory"
	tapEname := &sushi.EnameData{
		Ename: "change_location_button_tap",
	}
	changeLocationEvents := sushi.NewClevertapEvents()
	changeLocationEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, changeLocationEvents)

	buttonItem1 := sushi.FooterSnippetType2ButtonItem{
		Type:              "text",
		Text:              "Change location",
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	items = append(items, buttonItem1)

	bottomButton := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}

	title, _ := sushi.NewTextSnippet("Uncharted territory")
	subtitle1, _ := sushi.NewTextSnippet("Whoopsie! No cultpass PLAY centers nearby. Change location to keep exploring.")
	image, _ := sushi.NewImage(util.GetCDNLink("uploads/Group23303.png"))
	image.SetHeight(220)
	image.SetWidth(258)

	snippet := &sushi.EmptyViewType1Snippet{
		Title:        title,
		Subtitle1:    subtitle1,
		Image:        image,
		BottomButton: bottomButton,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}
	section := &sushi.EmptyViewType1Layout{
		LayoutConfig:          layoutConfig,
		EmptyViewType1Snippet: snippet,
	}
	h.AddEmptyView(ctx, section)
}

func (h *HomePageTemplate) SetNotificationCount(ctx context.Context) {

	cl := util.GetNotificationServiceClient()
	var userId int32
	userId = util.GetUserIDFromContext(ctx)
	req := &notificationPB.UnreadNotification{UserId: userId}
	countResponse, err := cl.UnreadNotificationCountGet(ctx, req)
	if err != nil {
		log.Println("Error in setting notification count for user id:", userId, " Error:", err)
		return
	}
	h.SetUnreadNotificationCount(countResponse.UnreadCount)
}

func (h *HomePageTemplate) SetPageTracking(ctx context.Context) {
	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["city_id"] = util.GetCityIDFromContext(ctx)
	landingEname := &sushi.EnameData{
		Ename: "home_page_landing",
	}
	pageEvents := sushi.NewClevertapEvents()
	pageEvents.SetPageSuccess(landingEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, pageEvents)
	h.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	jumboPayload := make(map[string]interface{})
	jumboPayload["user_id"] = util.GetUserIDFromContext(ctx)
	jumboEvents := jumbo.NewEvents()
	landingEvent := jumbo.GetEventNameObject(jumbo.HomePageLanding)
	jumboEvents.SetPageSuccess(landingEvent)
	jumboTrackItem := jumbo.GetJumboTrackItem(jumboPayload, jumboEvents, jumbo.EventsTableName)

	h.JumboTracking = []*jumbo.Item{jumboTrackItem}

	go h.updateUserSelectedLocation(ctx)
}

func getSuggestionDeeplinkPostBackParams(ctx context.Context, actionId int32, ctaPostbackParams string) string {
	varMap := getPostbackParamsMap(ctaPostbackParams)
	postbackParamsMap := make(map[string]interface{})
	if actionId == common.SUGGESTION_ACTION_BOOKING_DEEPLINK {
		if val, ok := varMap["fs_id"]; ok && len(val) > 0 {
			if intVar, err := strconv.Atoi(val); err == nil {
				postbackParamsMap["fs_id"] = int32(intVar)
				postbackParamsMap["bottom_sheet"] = 1
				postbackParamsMap["booking_users"] = []interface{}{
					map[string]int32{
						"user_id": util.GetUserIDFromContext(ctx),
					},
				}
			}
		}
	} else if actionId == common.SUGGESTION_ACTION_FACILITY_DEEPLINK {
		if val, ok := varMap["sport_id"]; ok && len(val) > 0 {
			if intVar, err := strconv.Atoi(val); err == nil {
				postbackParamsMap["sport_id"] = int32(intVar)
			}
		}
	} else if actionId == common.SUGGESTION_ACTION_TRIAL_DEEPLINK {
		if val, ok := varMap["sport_id"]; ok && len(val) > 0 {
			if intVar, err := strconv.Atoi(val); err == nil {
				postbackParamsMap["sport_id"] = int32(intVar)
			}
		}
	} else if actionId > 0 {
		for key, value := range varMap {
			if sharedFunc.ContainsString(IntegerFields, key) {
				intVar, err := strconv.Atoi(value)
				if err != nil || (intVar == 0 && len(value) != 0 && value != "0") {
					log.Printf("Api: home, function: getSuggestionDeeplinkPostBackParams, Error: not able to convert cta_postback_param %s - %s correctly", key, value)
					return ""
				}
				postbackParamsMap[key] = int32(intVar)
			}
		}
	}
	if len(postbackParamsMap) > 0 {
		payload, err := json.Marshal(postbackParamsMap)
		if err != nil {
			log.Printf("Api: home, function: SetBannerSection, Error: not able to process cta_postback_params %f correctly", ctaPostbackParams)
			return ""
		}
		return string(payload)
	}
	return ""
}

func getDeeplink(userMembershipType userPB.MembershipType, facilitySport *facilitySportPB.FacilitySport) sushi.Deeplink {
	facility := facilitySport.Facility[0]
	var url = facility.Deeplink
	deeplink := sushi.Deeplink{
		URL: url,
	}

	var params map[string]interface{}
	if userMembershipType == userPB.MembershipType_TRIAL || userMembershipType == userPB.MembershipType_SINGLE_MEMBERSHIP {
		deeplink.URL = util.GetSlotsDeeplink()
		params = map[string]interface{}{
			"fs_id":        facilitySport.FsId,
			"bottom_sheet": 1,
		}
	} else if userMembershipType == userPB.MembershipType_CHILD_MEMBERSHIP {
		deeplink.URL = util.GetBuddiesSelectionDeeplink()
		params = map[string]interface{}{
			"fs_id": facilitySport.FsId,
		}
	}

	if len(params) != 0 {
		payload, err := json.Marshal(params)
		if err != nil {
			log.Printf("json marshalling error: %v", err)
		}
		deeplink.PostbackParams = string(payload)
	}

	return deeplink
}

func (h *HomePageTemplate) getSuggestionDeeplink(ctx context.Context, actionId int32, ctaText string, ctaPostbackParams string) sushi.Deeplink {
	userServiceClient := util.GetUserServiceClient()
	deeplink := sushi.Deeplink{}
	reqData := &userPB.Suggestion{
		ActionId:          actionId,
		CtaText:           ctaText,
		CtaPostbackParams: ctaPostbackParams,
	}
	if res, err := userServiceClient.ValidateSuggestionDeeplinkCtaText(ctx, reqData); err != nil {
		log.Println("Api: home, function: getSuggestionDeeplink, Error: ", err, res)
		return deeplink
	}
	if actionId == common.SUGGESTION_ACTION_RENEWAL_DEEPLINK {
		userSubscriptionStatus := h.PageData.UserSubscriptionStatus
		productId := userSubscriptionStatus.ProductId
		if productId > 0 {
			//ctaText = util.GetBuyingForMeDeeplink(productId, true)
			ctaText = util.GetPurchaseMembershipDeeplink(common.MasterkeyCategoryID) //sending to purchase landing page till dec 2021
		} else {
			return deeplink
		}
	}
	if len(ctaPostbackParams) == 0 && len(ctaText) > 0 {
		deeplink = sushi.Deeplink{
			URL: ctaText,
		}
	}
	if len(ctaPostbackParams) > 0 && strings.HasPrefix(ctaText, util.DEEPLINK_HOST) {
		if res, err := userServiceClient.ValidateSuggestionDeeplinkCtaPostbackParams(ctx, reqData); err != nil {
			log.Println("Api: home, function: getSuggestionDeeplink, Error: ", err, res)
			return deeplink
		}
		postbackParams := getSuggestionDeeplinkPostBackParams(ctx, actionId, ctaPostbackParams)
		if len(postbackParams) > 0 {
			deeplink = sushi.Deeplink{
				URL:            ctaText,
				PostbackParams: postbackParams,
			}
		}
	}
	return deeplink
}

func getPostbackParamsMap(ctaPostbackParams string) map[string]string {
	varMap := make(map[string]string)
	arrStr := strings.Split(ctaPostbackParams, "&")
	for _, varArr := range arrStr {
		variable := strings.Split(varArr, "=")
		if len(variable) == 2 {
			varMap[variable[0]] = variable[1]
		}
	}
	return varMap
}

func (h *HomePageTemplate) updateUserSelectedLocation(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from updateUserSelectedLocation panic- ", r)
		}
	}()
	userServiceClient := util.GetUserServiceClient()
	res, err := userServiceClient.UpdateUserSelectedLocation(ctx, &userPB.Empty{})
	if err != nil {
		log.Printf("Api: Home, function: updateUserSelectedLocation, error in updating user select city logs,res: %v, err: %v", res, err)
		return
	}
	return
}

func (h *HomePageTemplate) SetReferralRewardCustomPopup(ctx context.Context) {
	productClient := util.GetProductClient()
	loggedInUser := util.GetUserIDFromContext(ctx)
	if loggedInUser <= 0 {
		return
	}

	res, err := productClient.GetReferralRewardPopupDetails(ctx, &productPB.Empty{})
	if err != nil {
		log.Printf("Api: Home, function: SetReferralRewardCustomPopup, error in getting referral reward popup details, res: %v, err: %v", res, err)
		return
	}

	if !res.EligibleForReferralRewardPopup {
		return
	}

	if res.RewardType != 2 {
		// currently this popup is supported only for reward_type=extension.
		return
	}

	referralPopup := &sushi.CustomAlert{}
	referralPopup.DismissAfterAction = true

	title, _ := sushi.NewTextSnippet("Successful referral!")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	title.SetFont(font)
	title.SetColor(color)
	referralPopup.Title = title

	var messageText string
	if res.CurrentSubRewarded {
		messageText = fmt.Sprintf("+%d days\n{grey-700|<medium-300|added to your membership>}", res.RewardValue)
	} else {
		messageText = fmt.Sprintf("+%d days\n{grey-700|<medium-300|will be added to your next purchase>}", res.RewardValue)
	}

	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize900)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)
	message.SetIsMarkdown(1)
	referralPopup.Message = message

	if res.CurrentSubRewarded {
		subtitle2Text := fmt.Sprintf("\nMembership expiring on\n{grey-800|<semibold-400|%s>}", res.MembershipExpiringOn)

		subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
		subtitle2_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subtitle2_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle2.SetFont(subtitle2_font)
		subtitle2.SetColor(subtitle2_color)
		subtitle2.SetIsMarkdown(1)
		referralPopup.Subtitle2 = subtitle2

		bottomContainerTitle, _ := sushi.NewTextSnippet("Keep referring to add more days ✌️")
		bottomContainerTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		bottomContainerTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		bottomContainerTitle.SetFont(bottomContainerTitleFont)
		bottomContainerTitle.SetColor(bottomContainerTitleColor)

		bottomContainer := &sushi.BottomContainer{
			Title: bottomContainerTitle,
		}
		referralPopup.BottomContainer = bottomContainer
	}

	autoDismiss := &sushi.AutoDismissData{
		DismissActionType: sushi.ActionTypePositive,
		Time:              8,
	}
	referralPopup.AutoDismissData = autoDismiss

	overlayAnimation := &sushi.OverlayAnimation{
		URL: "https://fitso-images.curefit.co/file_assets/home_detail_overlay_animation.json",
	}
	referralPopup.OverlayAnimation = overlayAnimation

	payload := make(map[string]interface{})
	payload["user_id"] = loggedInUser
	payload["reward_value"] = res.RewardValue
	payload["reward_type"] = res.RewardType
	payload["current_sub_reward"] = res.CurrentSubRewarded

	impressionEname := &sushi.EnameData{
		Ename: "success_referral_popup_impression",
	}
	impressionEvents := sushi.NewClevertapEvents()
	impressionEvents.SetImpression(impressionEname)
	impressionTrackItem := sushi.GetClevertapTrackItem(ctx, payload, impressionEvents)
	referralPopup.ClevertapTracking = []*sushi.ClevertapItem{impressionTrackItem}

	h.Popup = referralPopup
}

func (h *HomePageTemplate) SetPlanAndPricingSection(ctx context.Context) {
	status := h.PageData.UserSubscriptionStatus.SubscriptionStatus

	if status == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || status == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
		return
	}

	var imageId int32
	cityId := util.GetCityIDFromContext(ctx)
	if val, ok := common.CITY_PLAN_PRICING_IMAGE_MAP[cityId]; ok {
		imageId = val
	}

	if imageId <= 0 {
		return
	}

	facilityClient := util.GetFacilitySportClient()
	imagePathReq := &facilitySportPB.GetImagePathByImageIdRequest{ImageId: imageId}
	pointerImage, err := facilityClient.GetImagePathByImageId(ctx, imagePathReq)
	if err != nil {
		log.Printf("Could not find plan and pricing section image for image id: %d, Error:%v", imageId, err)
		return
	}
	imageUrl := pointerImage.ImagePath

	if len(imageUrl) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:      sushi.FitsoImageTextSnippetType9,
		LayoutType:       sushi.LayoutTypeCarousel,
		SectionCount:     1,
		ShouldAutoScroll: true,
	}

	layoutConfigHeading := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Plans & Pricing")
	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	sectionHeaderSection := homeModels.ResultSection{
		LayoutConfig:       layoutConfigHeading,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	h.AddSection(&sectionHeaderSection)

	items := make([]*sushi.FitsoImageTextSnippetType9SnippetItem, 0)
	image, _ := sushi.NewImage(util.GetCDNLink(imageUrl))
	image.SetAspectRatio(1.5)

	clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	snippetDeeplink := &sushi.Deeplink{
		URL: util.GetPurchaseMembershipDeeplink(2),
	}
	clickAction.SetDeeplink(snippetDeeplink)
	item := &sushi.FitsoImageTextSnippetType9SnippetItem{
		CornerRadius: 12,
		Image:        image,
		HideGradient: true,
		ClickAction:  clickAction,
	}
	items = append(items, item)

	currTime := time.Now().Unix()

	if currTime <= 1675621799 {
		image, _ := sushi.NewImage(util.GetCDNLink("uploads/PriceHikeLiteCard-min1675157501.png"))
		image.SetAspectRatio(1.5)

		clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		snippetDeeplink := &sushi.Deeplink{
			URL: util.GetPurchaseMembershipDeeplink(2),
		}
		clickAction.SetDeeplink(snippetDeeplink)
		item := &sushi.FitsoImageTextSnippetType9SnippetItem{
			CornerRadius: 12,
			Image:        image,
			HideGradient: true,
			ClickAction:  clickAction,
		}
		items = append(items, item)
	}

	snippetData := &sushi.FitsoImageTextSnippetType9Snippet{
		Items: items,
	}

	section := &homeModels.ResultSection{
		LayoutConfig:               layoutConfig,
		FitsoImageTextSnippetType9: snippetData,
	}

	h.AddSection(section)
}

func (h *HomePageTemplate) SetBlockCultAppUserPopUpSection(ctx context.Context, cultAppUsage structs.CultAppUsageApiResponse) {
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = true
	title, _ := sushi.NewTextSnippet("\nCongrats! You're now a cult PLAY member\n")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	blockingPopup.Title = title

	messageText := "Now book your favourite sports on cult.fit app. Go Play!\n"
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)
	blockingPopup.Message = message

	button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	click_action_open_web_view := &sushi.OpenWebview{
		URL:   "https://cure.app.link/OoQhm3IEpDb?force_browser=1",
		InApp: false,
	}
	button_click_action.SetOpenWebview(click_action_open_web_view)

	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("OPEN CULT")
	negativeAction.SetColor(button_color)
	negativeAction.SetClickAction(button_click_action)
	blockingPopup.NegativeAction = negativeAction

	blockingPopup.IsBlocking = true
	blockingPopup.DismissAfterAction = false
	blockingPopup.BlockAfterAction = true

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/verticalwhitelogomark(3)1668176116.png")
	popup_image.SetHeight(106)
	popup_image.SetWidth(90)
	blockingPopup.Image = popup_image

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetAndroidSoftPopUpSectionForCultApp(ctx context.Context) {
	userSubscriptionStatus := h.PageData.UserSubscriptionStatus.SubscriptionStatus
	if userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
		h.SetAndroidSoftPopUpSectionForMembers(ctx)
	}
}

func (h *HomePageTemplate) SetAndroidHardPopUpSectionForMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = false
	blockingPopup.IsBlocking = true
	blockingPopup.BlockAfterAction = true

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: true,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	negative_button_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSaveKey)
	save_key_item := &sushi.SaveKeyItem{
		Value: "academy",
		Key:   "product_type",
	}
	save_key_item_refresh_page := &sushi.RefreshPageItem{
		Type: "home",
	}
	save_action_item := &sushi.SaveActionItem{
		Type:         sushi.ActionTypeRefreshPages,
		RefreshPages: []*sushi.RefreshPageItem{save_key_item_refresh_page},
	}
	click_action_save_key := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{save_key_item},
		ActionList: []*sushi.SaveActionItem{save_action_item},
	}
	negative_button_click_action.SetSaveKey(click_action_save_key)
	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Switch to academy")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	negativeAction.SetClickAction(negative_button_click_action)
	blockingPopup.NegativeAction = negativeAction

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/fitso_soft_nudge_popup1673267855.png")
	popup_image.SetHeight(450)
	popup_image.SetWidth(335)
	blockingPopup.Image = popup_image

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetAndroidSoftPopUpSectionForMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = true
	blockingPopup.IsBlocking = false
	blockingPopup.BlockAfterAction = false

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: true,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = true
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Close")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	blockingPopup.NegativeAction = negativeAction

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/fitso_soft_nudge_popup1673267855.png")
	popup_image.SetHeight(450)
	popup_image.SetWidth(335)
	blockingPopup.Image = popup_image

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetAndroidSoftPopUpSectionForNonMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = true
	blockingPopup.IsBlocking = false
	blockingPopup.BlockAfterAction = false

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: false,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Close")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	blockingPopup.NegativeAction = negativeAction

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/FitsoPopup41673864790.png")
	popup_image.SetHeight(450)
	popup_image.SetWidth(335)
	blockingPopup.Image = popup_image

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetAndroidHardPopUpSectionForNonMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = false
	blockingPopup.IsBlocking = true
	blockingPopup.BlockAfterAction = true

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: false,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = true
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	negative_button_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSaveKey)
	save_key_item := &sushi.SaveKeyItem{
		Value: "academy",
		Key:   "product_type",
	}
	save_key_item_refresh_page := &sushi.RefreshPageItem{
		Type: "home",
	}
	save_action_item := &sushi.SaveActionItem{
		Type:         sushi.ActionTypeRefreshPages,
		RefreshPages: []*sushi.RefreshPageItem{save_key_item_refresh_page},
	}
	click_action_save_key := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{save_key_item},
		ActionList: []*sushi.SaveActionItem{save_action_item},
	}
	negative_button_click_action.SetSaveKey(click_action_save_key)
	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Switch to academy")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	negativeAction.SetClickAction(negative_button_click_action)
	blockingPopup.NegativeAction = negativeAction

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/FitsoPopup41673864790.png")
	popup_image.SetHeight(450)
	popup_image.SetWidth(335)
	blockingPopup.Image = popup_image

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetIosSoftPopUpSectionForCultApp(ctx context.Context) {
	userSubscriptionStatus := h.PageData.UserSubscriptionStatus.SubscriptionStatus
	if userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
		h.SetIosSoftPopUpSectionForMembers(ctx)
	}
}

func (h *HomePageTemplate) SetIosSoftPopUpSectionForMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = true
	title, _ := sushi.NewTextSnippet("\nFitso {yellow-700|Masterkey} is now cultpass{green-600| PLAY}")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	title.SetIsMarkdown(1)
	blockingPopup.Title = title

	messageText := "All your masterkey benefits along with\n\n💪 2 FREE cult gym/group sessions per month\n⏸ Pause your pack ANY TIME\n✅ PLAY at the SAFEST centres in TOWN!"
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	message_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)
	message.SetAlignment(sushi.TextAlignmentLeft)
	blockingPopup.Message = message

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: false,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = false
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Close")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	blockingPopup.NegativeAction = negativeAction

	blockingPopup.IsBlocking = false
	blockingPopup.BlockAfterAction = false

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/verticalwhitelogomark(3)1668176116.png")
	blockingPopup.Image = popup_image
	popup_image.SetHeight(200)
	popup_image.SetWidth(200)

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetIosHardPopUpSectionForMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = false
	title, _ := sushi.NewTextSnippet("\nFitso {yellow-700|Masterkey} is now cultpass{green-600| PLAY}")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	title.SetIsMarkdown(1)
	blockingPopup.Title = title

	messageText := "All your masterkey benefits along with\n\n💪 2 FREE cult gym/group sessions per month\n⏸ Pause your pack ANY TIME\n✅ PLAY at the SAFEST centres in TOWN!"
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	message_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)
	message.SetAlignment(sushi.TextAlignmentLeft)
	blockingPopup.Message = message

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: false,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = true
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	negative_button_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSaveKey)
	save_key_item := &sushi.SaveKeyItem{
		Value: "academy",
		Key:   "product_type",
	}
	save_key_item_refresh_page := &sushi.RefreshPageItem{
		Type: "home",
	}
	save_action_item := &sushi.SaveActionItem{
		Type:         sushi.ActionTypeRefreshPages,
		RefreshPages: []*sushi.RefreshPageItem{save_key_item_refresh_page},
	}
	click_action_save_key := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{save_key_item},
		ActionList: []*sushi.SaveActionItem{save_action_item},
	}
	negative_button_click_action.SetSaveKey(click_action_save_key)
	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Switch to academy")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	negativeAction.SetClickAction(negative_button_click_action)
	blockingPopup.NegativeAction = negativeAction

	blockingPopup.IsBlocking = true
	blockingPopup.BlockAfterAction = true

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/verticalwhitelogomark(3)1668176116.png")
	blockingPopup.Image = popup_image
	popup_image.SetHeight(200)
	popup_image.SetWidth(200)

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetIosSoftPopUpSectionForNonMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = true
	title, _ := sushi.NewTextSnippet("\nFitso {yellow-700|Masterkey} is now cultpass{green-600| PLAY}")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	title.SetIsMarkdown(1)
	blockingPopup.Title = title

	messageText := "For the best experience and many more benefits...\n"
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize600)
	message_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)
	blockingPopup.Message = message

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: false,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = false
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)

	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Close")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	blockingPopup.NegativeAction = negativeAction

	blockingPopup.IsBlocking = false
	blockingPopup.BlockAfterAction = false

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/verticalwhitelogomark(3)1668176116.png")
	blockingPopup.Image = popup_image
	popup_image.SetHeight(200)
	popup_image.SetWidth(200)

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetIosHardPopUpSectionForNonMembers(ctx context.Context) {
	userId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = false
	title, _ := sushi.NewTextSnippet("\nFitso {yellow-700|Masterkey} is now cultpass{green-600| PLAY}")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	title.SetIsMarkdown(1)
	blockingPopup.Title = title

	messageText := "For the best experience and many more benefits...\n"
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize600)
	message_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)
	blockingPopup.Message = message

	userEventClick := &structs.UserEventClick{
		UserId:         userId,
		HaveMembership: false,
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = true
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT.FIT APP")
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	negative_button_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSaveKey)
	save_key_item := &sushi.SaveKeyItem{
		Value: "academy",
		Key:   "product_type",
	}
	save_key_item_refresh_page := &sushi.RefreshPageItem{
		Type: "home",
	}
	save_action_item := &sushi.SaveActionItem{
		Type:         sushi.ActionTypeRefreshPages,
		RefreshPages: []*sushi.RefreshPageItem{save_key_item_refresh_page},
	}
	click_action_save_key := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{save_key_item},
		ActionList: []*sushi.SaveActionItem{save_action_item},
	}
	negative_button_click_action.SetSaveKey(click_action_save_key)
	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Switch to academy")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	negativeAction.SetClickAction(negative_button_click_action)
	blockingPopup.NegativeAction = negativeAction

	blockingPopup.IsBlocking = true
	blockingPopup.BlockAfterAction = true

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/verticalwhitelogomark(3)1668176116.png")
	blockingPopup.Image = popup_image
	popup_image.SetHeight(200)
	popup_image.SetWidth(200)

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetHardPopUpSectionForCultApp(ctx context.Context) {
	userSubscriptionStatus := h.PageData.UserSubscriptionStatus.SubscriptionStatus
	if util.IsIOS(ctx) {
		if userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
			h.SetIosHardPopUpSectionForMembers(ctx)
		} else {
			h.SetIosHardPopUpSectionForNonMembers(ctx)
		}
	} else {
		if userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
			h.SetAndroidHardPopUpSectionForMembers(ctx)
		} else {
			h.SetAndroidHardPopUpSectionForNonMembers(ctx)
		}
	}
}

func (h *HomePageTemplate) SetHardPopUpSectionForMembers(ctx context.Context) {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = true
	title, _ := sushi.NewTextSnippet("\nCongrats! You're now a cult PLAY member\n")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	blockingPopup.Title = title

	messageText := "Now book your favourite sports on cult.fit app. Go Play!\n"
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	message_color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
	message.SetFont(message_font)
	message.SetColor(message_color)
	blockingPopup.Message = message

	userEventClick := &structs.UserEventClick{
		UserId: loggedInUserId,
	}

	userSubscriptionStatus := h.PageData.UserSubscriptionStatus.SubscriptionStatus
	if userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE || userSubscriptionStatus == productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
		userEventClick.HaveMembership = true
	}

	postback_params, _ := json.Marshal(userEventClick)

	button_color, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
	button_click_action, _ := sushi.NewTextClickAction(sushi.ApiCallMultiAction)
	button_click_action.IsBlocking = true
	api_call_multi_action := &sushi.APICallAction{
		RequestType: sushi.POSTRequestType,
		URL:         "v1/user/recordUserClickAction",
		Body:        string(postback_params),
	}
	button_click_action.SetApiCallAction(api_call_multi_action)

	positiveAction, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	positiveAction.SetText("OPEN CULT")
	positiveAction.SetColor(button_color)
	positiveAction.SetClickAction(button_click_action)
	positiveAction.SetSize(sushi.ButtonSizeLarge)
	blockingPopup.PositiveAction = positiveAction

	negative_button_color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	negative_button_click_action, _ := sushi.NewTextClickAction(sushi.ClickActionSaveKey)
	save_key_item := &sushi.SaveKeyItem{
		Value: "academy",
		Key:   "product_type",
	}
	save_key_item_refresh_page := &sushi.RefreshPageItem{
		Type: "home",
	}
	save_action_item := &sushi.SaveActionItem{
		Type:         sushi.ActionTypeRefreshPages,
		RefreshPages: []*sushi.RefreshPageItem{save_key_item_refresh_page},
	}
	click_action_save_key := &sushi.SaveKey{
		Items:      []*sushi.SaveKeyItem{save_key_item},
		ActionList: []*sushi.SaveActionItem{save_action_item},
	}
	negative_button_click_action.SetSaveKey(click_action_save_key)
	negativeAction, _ := sushi.NewButton(sushi.ButtontypeText)
	negativeAction.SetText("Switch to academy")
	negativeAction.SetSize(sushi.ButtonSizeLarge)
	negativeAction.SetColor(negative_button_color)
	negativeAction.SetClickAction(negative_button_click_action)
	blockingPopup.NegativeAction = negativeAction

	blockingPopup.IsBlocking = true
	blockingPopup.DismissAfterAction = false
	blockingPopup.BlockAfterAction = true

	popup_image, _ := sushi.NewImage("https://fitso-images.curefit.co/uploads/verticalwhitelogomark(3)1668176116.png")
	popup_image.SetHeight(106)
	popup_image.SetWidth(90)
	blockingPopup.Image = popup_image

	h.Popup = blockingPopup
}

func (h *HomePageTemplate) SetHardPopUpSectionForBlacklistUser() {
	blockingPopup := &sushi.CustomAlert{}

	blockingPopup.DismissAfterAction = false
	blockingPopup.IsBlocking = true
	blockingPopup.BlockAfterAction = true

	title, _ := sushi.NewTextSnippet("You are Black Listed!")
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize800)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)
	title.SetIsMarkdown(1)
	blockingPopup.Title = title

	messageText := "Your account has been blocked!. Please contact customer support for more details."
	message, _ := sushi.NewTextSnippet(messageText)
	message_font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	message_color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint900)
	message.SetFont(message_font)
	message.SetColor(message_color)
	message.SetAlignment(sushi.TextAlignmentLeft)
	blockingPopup.Message = message

	popupImage := getAnimatedImage("https://fitso-images.curefit.co/file_assets/failure_image.json")
	blockingPopup.Image = popupImage
	popupImage.SetHeight(200)
	popupImage.SetWidth(200)

	h.Popup = blockingPopup
}

func getAnimatedImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}
