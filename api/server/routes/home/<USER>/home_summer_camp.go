package homeController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"

	common "bitbucket.org/jogocoin/go_api/api/common"
	model "bitbucket.org/jogocoin/go_api/api/models"
	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featuresupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

var (
	attributePeopleCount     = "PEOPLE_COUNT"
	attributeMaxSafety       = "MAX_SAFETY"
	attributeVaccinatedStaff = "VACCINATED_STAFF"
	maxSafetyBenefits        = "Max Safety measures"
)

func GetSummerCampPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	template := HomePageTemplate{
		PageData: HomePageData{},
	}
	template.PageData.ProductCategoryId = common.SummerCampCategoryID

	template.SetPageType(common.PAGE_TYPE_SUMMER_CAMP)
	template.SetCitySportsForSummerCamp(ctx)
	if len(template.PageData.SportsDetails.SportFacilitiesCount) == 0 {
		c.AbortWithStatusJSON(http.StatusBadRequest, model.StatusFailure(common.UNEXPECTED_ERROR))
		return
	}
	template.SetProductBySport(ctx)
	template.SetHeaderSectionSummerCamp(ctx)
	template.SetFeatureBottomSheetSectionSummerCamp(ctx)
	template.SetSummerCampBannerSection(ctx)
	template.SetHighlightsSection(ctx)
	template.SetExploreSportsSection(ctx)
	template.SetNearbySectionHeading(ctx)
	template.SetNearByCentersSection(ctx)
	template.SetSupportContainerSection(ctx)
	template.SetCustomersReviewSection(ctx)
	template.SetFAQSection(ctx)
	template.SetFooterSectionSummercamp(ctx)
	c.JSON(http.StatusOK, template)
}

func (h *HomePageTemplate) SetCitySportsForSummerCamp(ctx context.Context) {
	facilityClient := util.GetFacilitySportClient()

	response, err := facilityClient.GetSummerCampActiveSportsByCity(ctx, &facilitySportPB.Empty{})
	if err != nil {
		log.Printf("Api: home, function: SetCitySportsForSummerCamp, Error: %v", err)
		return
	}
	log.Println(response)
	h.PageData.SportsDetails = response
}

func (h *HomePageTemplate) SetProductBySport(ctx context.Context) {
	productClient := util.GetProductClient()
	summercampProductResponse, err := productClient.GetFeaturedSummercampProducts(ctx, &productPB.Empty{})
	if err != nil {
		log.Printf("Error in HomePageTemplate error: %v", err)
		return
	}
	h.ProductDetails = summercampProductResponse
}

func (h *HomePageTemplate) SetSummerCampBannerSection(ctx context.Context) {
	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	videoDetails := &facilitySportPB.Video{
		ThumbnailUrl: "uploads/SummerCamp-Appbanner1709063626.png",
		VideoUrl:     "https://curefit-content.s3.ap-south-1.amazonaws.com/image/icons/fitsoImages/LP+Video_Low_01.mp4",
		AspectRatio:  1.7,
		ShowMute:     true,
		Autoplay:     true,
		HasAudio:     true,
	}
	cityId := util.GetCityIDFromContext(ctx)
	if cityId == common.CITY_ID_BENGALURU {
		videoDetails.ThumbnailUrl = "uploads/SummerCamp-Appbanner-min21712048113.png"
	} else if cityId == common.CITY_ID_HYDERABAD {
		videoDetails.VideoUrl = "https://curefit-content.s3.ap-south-1.amazonaws.com/image/icons/fitsoImages/LP+Video+(1).mp4"
		videoDetails.ThumbnailUrl = "uploads/SummerCamp-AppbannerHyderabad1710832956.png"
	}
	video := util.ConvertVideoProtoToVideoTemplate(videoDetails)
	mediaContent := &sushi.MediaContent{
		MediaType: sushi.MEDIA_TYPE_VIDEO,
		Video:     video,
	}
	item := &sushi.MediaType2SnippetItem{
		MediaContent: mediaContent,
	}
	//mediaItems = append(mediaItems, item)

	if true {
		if cityId != common.CITY_ID_DELHI_NCR {
			mediaItems = append(mediaItems, item)
		}
		for _, banner := range common.SummercampBannerWithDeeplink[cityId] {
			image, _ := sushi.NewImage(util.GetCDNLink(banner.BannerURL))
			image.SetAspectRatio(1.7)
			mediaContent := &sushi.MediaContent{
				MediaType: "image",
				Image:     image,
			}
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			urlDeeplink := banner.BannerDeeplink

			clickAction.SetDeeplink(&sushi.Deeplink{
				URL: urlDeeplink,
			})
			if banner.HasPostBack {

				payload := map[string]int32{
					"sport_id":            banner.SportId,
					"product_category_id": common.SummerCampCategoryID,
				}
				postbackParams, _ := json.Marshal(payload)

				clickAction.SetDeeplink(&sushi.Deeplink{
					URL:            banner.BannerDeeplink,
					PostbackParams: string(postbackParams),
				})
			}
			mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
				MediaContent: mediaContent,
				ClickAction:  clickAction,
			})
		}
	} else if cityId == common.CITY_ID_HYDERABAD {
		imageWeb, _ := sushi.NewImage(util.GetCDNLink("uploads/hyd_gala1686028683.jpg"))
		imageWeb.SetAspectRatio(1.7)
		mediaContent1 := &sushi.MediaContent{
			MediaType: "image",
			Image:     imageWeb,
		}
		clickActionWeb := sushi.GetClickAction()
		openWebview := sushi.OpenWebview{
			URL:   "https://rzp.io/l/EEwtkw7cm2",
			InApp: true,
		}
		clickActionWeb.SetOpenWebview(&openWebview)

		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent1,
			ClickAction:  clickActionWeb,
		})
		mediaItems = append(mediaItems, item)
		/*for _, banner := range common.SummercampBannerWithDeeplink[cityId] {
			image, _ := sushi.NewImage(util.GetCDNLink(banner.BannerURL))
			image.SetAspectRatio(1.7)
			mediaContent := &sushi.MediaContent{
				MediaType: "image",
				Image:     image,
			}
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			urlDeeplink := banner.BannerDeeplink

			clickAction.SetDeeplink(&sushi.Deeplink{
				URL: urlDeeplink,
			})
			if banner.HasPostBack {

				payload := map[string]int32{
					"sport_id":            banner.SportId,
					"product_category_id": common.SummerCampCategoryID,
				}
				postbackParams, _ := json.Marshal(payload)

				clickAction.SetDeeplink(&sushi.Deeplink{
					URL:            banner.BannerDeeplink,
					PostbackParams: string(postbackParams),
				})
			}
			mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
				MediaContent: mediaContent,
				ClickAction:  clickAction,
			})
		}*/
	} else {
		imageWeb, _ := sushi.NewImage(util.GetCDNLink("uploads/blr_gala1686028597.jpeg"))
		imageWeb.SetAspectRatio(1.7)
		mediaContent1 := &sushi.MediaContent{
			MediaType: "image",
			Image:     imageWeb,
		}
		clickActionWeb := sushi.GetClickAction()
		openWebview := sushi.OpenWebview{
			URL:   "https://rzp.io/l/sdFEPLAj",
			InApp: true,
		}
		clickActionWeb.SetOpenWebview(&openWebview)

		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent1,
			ClickAction:  clickActionWeb,
		})

		image, _ := sushi.NewImage(util.GetCDNLink("uploads/sc_suggestion21682488914.png"))
		image.SetAspectRatio(1.7)
		mediaContent := &sushi.MediaContent{
			MediaType: "image",
			Image:     image,
		}
		clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
		urlDeeplink := "fitso://academy_purchase_members?product_category_id=13"

		clickAction.SetDeeplink(&sushi.Deeplink{
			URL: urlDeeplink,
		})
		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
			ClickAction:  clickAction,
		})
		mediaItems = append(mediaItems, item)
	}

	mediaSnippet := &sushi.MediaType2Snippet{
		Items: mediaItems,
	}
	section := &homeModels.ResultSection{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType:      sushi.MediaSnippetType2,
			LayoutType:       sushi.LayoutTypeCarousel,
			SectionCount:     1,
			ShouldAutoScroll: true,
		},
		MediaSnippetType2: mediaSnippet,
	}

	h.AddSection(section)
}

func (h *HomePageTemplate) SetHighlightsSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Highlights")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}
	log.Println(snippetConfig)
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
		//SnippetConfig:		snippetConfig,
	}
	h.AddSection(section)

	imageTextSnippetType30Layout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	var items []sushi.ImageTextSnippetType30SnippetItem
	for _, data := range common.AllHighlights {
		image, _ := sushi.NewImage(data.Image)
		title, _ := sushi.NewTextSnippet(data.Title)
		font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint100)
		title.SetFont(font)
		title.SetColor(color)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(28)
		image.SetWidth(28)

		substitle1, _ := sushi.NewTextSnippet(data.Subtitle1)
		substitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		substitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		substitle1.SetFont(substitle1Font)
		substitle1.SetColor(substitle1Color)

		textSnippet30Item := sushi.ImageTextSnippetType30SnippetItem{
			Image:     image,
			Title:     title,
			Subtitle1: substitle1,
		}
		items = append(items, textSnippet30Item)
	}

	imageTextSnippetType30Snippet := &sushi.ImageTextSnippetType30Snippet{
		Items: &items,
	}

	sectionHighlights := &homeModels.ResultSection{
		LayoutConfig:           imageTextSnippetType30Layout,
		ImageTextSnippetType30: imageTextSnippetType30Snippet,
	}
	h.AddSection(sectionHighlights)
}

func (h *HomePageTemplate) SetExploreSportsSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Explore Sports")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}
	log.Println(snippetConfig)
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
		//SnippetConfig:		snippetConfig,
	}
	h.AddSection(section)

	sportsSelectionLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType33,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	var sportSnippets []sushi.ImageTextSnippetType33SnippetItem

	for _, data := range h.PageData.SportsDetails.SportFacilitiesCount {
		if data.SportId != 7 {
			continue
		}
		sportImage, _ := sushi.NewImage(data.SportImage)
		sportImageBgColor, _ := sushi.NewColor(sushi.ColorType(data.BackgroundColor), sushi.ColorTint(data.BackgroundColorShade))
		sportImage.SetAspectRatio(1)
		sportImage.SetType(sushi.ImageTypeCircle)
		sportImage.SetHeight(70)
		sportImage.SetWidth(70)
		sportImage.SetColor(sportImageBgColor)

		sportTitle, _ := sushi.NewTextSnippet(data.SportName)
		sportTitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		sportTitleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
		sportTitle.SetColor(sportTitleColor)
		sportTitle.SetFont(sportTitleFont)

		substitle1, _ := sushi.NewTextSnippet(common.SummerCampHomeSubtileSport[data.SportId])
		substitle1Color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint700)
		substitle1Font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		substitle1.SetColor(substitle1Color)
		substitle1.SetFont(substitle1Font)

		sportClickAction := sushi.GetClickAction()

		payload := map[string]int32{
			"sport_id":            data.SportId,
			"product_category_id": common.SummerCampCategoryID,
		}
		postbackParams, _ := json.Marshal(payload)

		sportClickAction.SetDeeplink(&sushi.Deeplink{
			URL:            util.GetSummercampDeeplink(data.SportId),
			PostbackParams: string(postbackParams),
		})

		sportItem := sushi.ImageTextSnippetType33SnippetItem{
			LeftImage:     sportImage,
			Title:         sportTitle,
			IsSelected:    false,
			IsSelectable:  true,
			CornerRadius:  16,
			SubTitle1:     substitle1,
			ClickAction:   sportClickAction,
			ShowSeparator: true,
		}

		products := h.ProductDetails
		if products != nil && products.SummercampProductsBySport[data.SportId] != nil {
			if len(products.SummercampProductsBySport[data.SportId].Products) > 0 {
				sportProduct := products.SummercampProductsBySport[data.SportId].Products[len(products.SummercampProductsBySport[data.SportId].Products)-1]
				daysInt, _ := util.GetProductDaysOfWeekSummercamp(sportProduct.DaysOfWeek)
				daysIntCount := int32(len(daysInt))
				log.Println("days count summer camp", daysInt,daysIntCount)
				perSessionPrice := int32(sportProduct.RetailPrice) / (sportProduct.Duration * daysIntCount)
				for _, product := range products.SummercampProductsBySport[data.SportId].Products {
					daysInt, _ = util.GetProductDaysOfWeekSummercamp(product.DaysOfWeek)
					daysIntCount = int32(len(daysInt))
					if perSessionPrice > (int32(product.RetailPrice) / (product.Duration * daysIntCount)) {
						perSessionPrice = int32(product.RetailPrice) / (product.Duration * daysIntCount)
					}
				}
				subtitle2Text := fmt.Sprintf("Starts at ₹%s/session", strconv.Itoa(int(perSessionPrice)))
				subtitle2, _ := sushi.NewTextSnippet(subtitle2Text)
				subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
				subtitle2Font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize200)
				subtitle2.SetColor(subtitle2Color)
				subtitle2.SetFont(subtitle2Font)
				sportItem.SubTitle2 = subtitle2
			}
		}

		sportSnippets = append(sportSnippets, sportItem)
	}

	sportsSelectionSection := &homeModels.ResultSection{
		LayoutConfig: sportsSelectionLayoutConfig,
		ImageTextSnippetType33: &sushi.ImageTextSnippetType33Snippet{
			Items: &sportSnippets,
		},
	}
	h.AddSection(sportsSelectionSection)
}

func (h *HomePageTemplate) SetNearbySectionHeading(ctx context.Context) {
	nearbyFacilitiesResponse := h.GetNearbyFacilitiesSummercamp(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Centers) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Centers near you")

	/*color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
	title.SetFont(font)
	title.SetColor(color)*/
	//title.SetKerning(3)

	sectionImpressionPayload := make(map[string]interface{})
	sectionImpressionPayload["user_id"] = util.GetUserIDFromContext(ctx)
	sectionImpressionPayload["source"] = "home"
	impressionEname := &sushi.EnameData{
		Ename: "summercamp_home_section_impr",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, sectionImpressionPayload, sectionEvents)

	sectionHeaderSnippetData := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeThick, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}
	log.Println(snippetConfig)
	sectionHeaderSection := &homeModels.ResultSection{
		LayoutConfig: layoutConfig,
		//SnippetConfig:      snippetConfig,
		SectionHeaderType1: sectionHeaderSnippetData,
	}
	h.AddSection(sectionHeaderSection)
}

func (h *HomePageTemplate) GetNearbyFacilitiesSummercamp(ctx context.Context) *facilitySportPB.SummerCampFacilitiesRes {
	if h.SummercampPageData != nil {
		return h.SummercampPageData
	}
	facilityClient := util.GetFacilitySportClient()
	req := &facilitySportPB.SummerCampFacilitiesReq{
		AllSports: true,
	}
	res, err := facilityClient.GetSummerCampFacilitiesForSport(ctx, req)
	if err != nil {
		log.Printf("nearby summercamp facilities | home page | Error: %v", err)
		return nil
	}
	if res.Status != nil && res.Status.Status == common.FAILED {
		log.Printf("nearby summercamp facilities | home page | Error: %v", res.Status.Message)
		return nil
	}
	h.SummercampPageData = res
	return res
}

func ratingSnippet(ctx context.Context, tag string, rating string) *sushi.RatingSnippetBlockItem {
	ratingSnippet := &sushi.RatingSnippetBlockItem{}
	isNewColorSupported := featuresupport.SupportsNewColor(ctx)

	tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
	if sharedFunc.ContainsString(tags, tag) {
		ratingTitle, _ := sushi.NewTextSnippet(tag)
		if !isNewColorSupported {
			color := sushi.ColorTypeBlue
			if tag == common.OPENING_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeTeal
			} else if tag == common.ONHOLD_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeOrange
			}
			ratingBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
			ratingSnippet.BgColor = ratingBgColor
		} else {
			if tag == common.OPENING_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				ratingSnippet.BgColor = ratingBgColor
			} else if tag == common.ONHOLD_FACILITY_SPORT_TAG {
				ratingBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
				ratingSnippet.BgColor = ratingBgColor
			} else {
				// gradient for new tag
				tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
				gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
				ratingSnippet.Gradient = gradient
			}
		}

		ratingSnippet.Title = ratingTitle
		ratingSnippet.Size = sushi.RatingSize300

	} else {
		ratingTitle, _ := sushi.NewTextSnippet(rating)
		ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if isNewColorSupported {
			ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}
		ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

		ratingSnippet.Title = ratingTitle
		ratingSnippet.BgColor = ratingBgColor
		ratingSnippet.RightIcon = ratingIcon
	}
	return ratingSnippet
}

func (h *HomePageTemplate) SetNearByCentersSection(ctx context.Context) {

	nearbyFacilitiesResponse := h.GetNearbyFacilitiesSummercamp(ctx)
	if nearbyFacilitiesResponse == nil {
		return
	}
	if len(nearbyFacilitiesResponse.Centers) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FitsoFacilityCardType2,
	}
	if len(nearbyFacilitiesResponse.Centers) == 1 {
		layoutConfig.LayoutType = sushi.LayoutTypeGrid
		layoutConfig.SectionCount = 1
	} else {
		layoutConfig.LayoutType = sushi.LayoutTypeCarousel
	}

	items := make([]sushi.FitsoFacilityCardType2SnippetItem, 0)
	for _, facility := range nearbyFacilitiesResponse.Centers {
		snippet := sushi.FitsoFacilityCardType2SnippetItem{}

		title, _ := sushi.NewTextSnippet(facility.DisplayName)
		snippet.Title = title

		sportNames := []string{}
		for _, sport := range facility.Sport {
			sportNames = append(sportNames, sport.SportName)
		}
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1

		image, _ := sushi.NewImage(facility.DisplayPicture)
		snippet.Image = image

		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction

		ratingSnippetBlockItem := ratingSnippet(ctx, facility.Tag, facility.Rating)

		ratingSnippet := sushi.RatingSnippet{
			Type:  sushi.RatingTypeTagV2,
			TagV2: ratingSnippetBlockItem,
		}

		snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

		var tagTitle *sushi.TextSnippet
		_, div := math.Modf(facility.Distance)
		if (div >= 0.95 || div < 0.05) && div != 0 {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
		} else {
			tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
		}

		tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		tagTitle.SetColor(tagColor)

		topRightTag := &sushi.Tag{
			Title:        tagTitle,
			Size:         sushi.TagSizeMedium,
			Transparency: 0.2,
		}
		snippet.TopRightTag = topRightTag

		if len(facility.Attributes) > 0 {
			bottomItems := make([]sushi.BottomContainerSnippetItem, 0)
			for _, val := range facility.Attributes {
				attrImage, _ := sushi.NewImage(val.Image)

				if val.Type == attributeMaxSafety {
					attrImage.SetAspectRatio(2.277777)
					attrImage.SetType(sushi.ImageTypeRectangle)
				} else if val.Type == attributePeopleCount {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeCircle)
				} else if val.Type == attributeVaccinatedStaff {
					attrImage.SetAspectRatio(1)
					attrImage.SetType(sushi.ImageTypeRectangle)
				}

				attrTitle, _ := sushi.NewTextSnippet(val.Title)
				titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
				attrTitle.SetColor(titleColor)

				bottomContainerSnippetItem := sushi.BottomContainerSnippetItem{
					LeftImage: attrImage,
					Title:     attrTitle,
				}
				bottomItems = append(bottomItems, bottomContainerSnippetItem)
			}
			snippet.BottomContainerSnippetItems = &bottomItems
		}
		tagAdded := false
		multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
		cityId := util.GetCityIDFromContext(ctx)
		for _, sport := range facility.Sport {
			if tagAdded {
				break
			}
			for _, attribute := range sport.FsInfo {
				if attribute == "Heated Pool" && common.BANGALORE_CITY_ID != cityId && false {
					continue
				}
				tagAdded = true
				tagTitle, _ := sushi.NewTextSnippet(attribute)

				if !featuresupport.SupportsNewColor(ctx) {
					multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
					multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
					gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:    tagTitle,
						Gradient: gradient,
					})
				} else {
					multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

					multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
						Title:   tagTitle,
						BgColor: multiTagSnippetItemColor,
					})
				}
			}
		}
		if len(multiTagItems) > 0 {
			snippet.TopTags = &multiTagItems
		}

		horizontalSubtitleItem := &sushi.HorizontalSubtitleItem{
			Text: facility.SubzoneName,
		}
		horizontalSubtitleItems := &sushi.HorizontalSubtitleSnippet{
			HorizontalSubtitles: []*sushi.HorizontalSubtitleItem{horizontalSubtitleItem},
		}
		snippet.VerticalSubtitles = []*sushi.HorizontalSubtitleSnippet{horizontalSubtitleItems}

		tapPayload := make(map[string]interface{})
		tapPayload["user_id"] = util.GetUserIDFromContext(ctx)
		tapPayload["facility_id"] = facility.FacilityId
		tapPayload["source"] = "facility"
		tapEname := &sushi.EnameData{
			Ename: "summercamp_home_facility_card_tap",
		}
		facilityCardEvents := sushi.NewClevertapEvents()
		facilityCardEvents.SetTap(tapEname)
		trackItem := sushi.GetClevertapTrackItem(ctx, tapPayload, facilityCardEvents)
		snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

		items = append(items, snippet)
	}
	facilitiesSnippet := &sushi.FitsoFacilityCardType2Snippet{
		Items: &items,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:           layoutConfig,
		FitsoFacilityCardType2: facilitiesSnippet,
	}
	h.AddSection(section)
}

func (h *HomePageTemplate) SetHeaderSectionSummerCamp(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType6,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	clickAction := getBottomSheetFeatureChangeClickAction(ctx, common.PAGE_TYPE_SUMMER_CAMP)

	title, _ := sushi.NewTextSnippet("CULT SUMMER CAMP")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, iconColor)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)
	title.SetSuffixIcon(icon)

	snippet := &sushi.FitsoTextType6Snippet{
		Title:       title,
		ClickAction: clickAction,
	}
	h.Header = &sushi.FitsoTextSnippetType6Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType6: snippet,
	}
}

func (h *HomePageTemplate) SetFeatureBottomSheetSectionSummerCamp(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType6,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	clickAction := getBottomSheetFeatureChangeClickAction(ctx, common.PAGE_TYPE_SUMMER_CAMP)

	title, _ := sushi.NewTextSnippet("CULT SUMMER CAMP")
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, iconColor)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)
	title.SetSuffixIcon(icon)

	snippet := &sushi.FitsoTextType6Snippet{
		Title:       title,
		ClickAction: clickAction,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType6: snippet,
	}
	h.AddSection(section)
}

func (h *HomePageTemplate) SetFooterSectionSummercamp(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}
	items := h.GetSummercampFooterCTA(ctx)

	if len(items) > 0 {
		bottomButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}
		snippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: bottomButton,
		}
		h.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig:       layoutConfig,
			FooterSnippetType2: snippet,
		}
	}
}

func (h *HomePageTemplate) GetSummercampFooterCTA(ctx context.Context) []sushi.FooterSnippetType2ButtonItem {

	loggedInUser := util.GetUserIDFromContext(ctx)
	var hasSummerCampSubscription bool
	if loggedInUser > 0 {
		userClient := util.GetUserServiceClient()
		response, err := userClient.GetSummerCampSubscriptionCountByCity(ctx, &userPB.Empty{})

		if err != nil {
			log.Printf("Unable to fetch city based summer camp subscription details for user %d, Error: %v", loggedInUser, err)
		}
		if response.Count > 0 {
			hasSummerCampSubscription = true
		}
	}
	buttonText := "Buy Summer Camp membership"
	if hasSummerCampSubscription {
		buttonText = "Buy another membership"
	}
	items := []sushi.FooterSnippetType2ButtonItem{}
	membershipButton := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Text: buttonText,
	}
	membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	urlDeeplink := util.GetPurchaseMembershipDeeplink(common.SummerCampCategoryID)

	membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
		URL: urlDeeplink,
	})
	membershipButton.ClickAction = membershipButtonClickAction
	products := h.ProductDetails
	if false && products != nil && products.SummercampProductsBySport[common.BADMINTON_SPORT_ID] != nil {
		if len(products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products) > 0 {
			sportProduct := products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products[len(products.SummercampProductsBySport[common.BADMINTON_SPORT_ID].Products)-1]
			perSessionPrice := int32(sportProduct.RetailPrice) / (sportProduct.Duration * 7)
			subtitle2Text := fmt.Sprintf("starts @ just ₹%s/session", strconv.Itoa(int(perSessionPrice)))
			membershipButton.Subtext = subtitle2Text
		}
	}
	items = append(items, membershipButton)
	//cityId := util.GetCityIDFromContext(ctx)
	if true {//util.Contains(common.TestSummercampUsers, loggedInUser) { // util.Contains(common.TestUsers, loggedInUser)
		sportIds := []int32{7}
		trialButton := h.GetSummercampTrialButton(ctx, sportIds)
		if trialButton != nil {
			items = append(items, *trialButton)
		}
	}
	if !hasSummerCampSubscription && false {// !util.Contains(common.TestUsers, loggedInUser)
		callButton := h.GetSummercampCallBackRequest(ctx)
		if callButton != nil {
			items = append(items, *callButton)
		}
	}
	return items
}

func (h *HomePageTemplate) GetSummercampCallBackRequest(ctx context.Context) *sushi.FooterSnippetType2ButtonItem {
	button := &sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeText,
		Text: "Request a callback",
	}

	tapEname := &sushi.EnameData{
		Ename: "summercamp_request_call_cta",
	}
	tapEvents := sushi.NewClevertapEvents()
	tapEvents.SetTap(tapEname)

	trackItem := GetClevertapTrackItem(ctx, nil, tapEvents)
	button.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	callClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	cityId := util.GetCityIDFromContext(ctx)
	callClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: "",
		URL:   common.CityCallClickAction[cityId],
	})
	button.ClickAction = callClickAction

	return button
}

func (h *HomePageTemplate) GetSummercampTrialButton(ctx context.Context, sportIds []int32) *sushi.FooterSnippetType2ButtonItem {
	button := &sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeText,
		Text: "Try for free",
	}

	tapEname := &sushi.EnameData{
		Ename: "summercamp_book_trial_button_tap",
	}
	tapEvents := sushi.NewClevertapEvents()
	tapEvents.SetTap(tapEname)

	trackItem := GetClevertapTrackItem(ctx, nil, tapEvents)
	button.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	clickAction := h.GetBookATrialClickAction(ctx, "postAction", sportIds, 0)
	button.ClickAction = clickAction

	return button
}

func (h *HomePageTemplate) GetBookATrialClickAction(ctx context.Context, postAction string, courseSportIds []int32, courseId int32) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()
	if util.GetUserIDFromContext(ctx) == 0 {
		authTitle, _ := sushi.NewTextSnippet("Please login to book your free trial class. Enter your phone number to login using OTP.")
		payload := map[string]interface{}{
			"post_action":         postAction,
			"sport_ids":           courseSportIds,
			"course_id":           courseId,
			"product_category_id": 13,
		}
		postbackParams, _ := json.Marshal(payload)
		auth := &sushi.Auth{
			Title:          authTitle,
			PostbackParams: string(postbackParams),
			Source:         "summercamp_home_banner",
		}
		clickAction.SetAuth(auth)
	} else {
		clickAction = h.GetMultiSportsClickAction(ctx, courseSportIds, courseId)
	}
	return clickAction
}

func (h *HomePageTemplate) GetMultiSportsClickAction(ctx context.Context, sportIds []int32, courseId int32) *sushi.ClickAction {
	facilityClient := util.GetFacilitySportClient()
	sportsDataRequest := &facilitySportPB.GetDataForSportIdsRequest{
		SportIdsArray:       sportIds,
		MinimalDataRequired: true,
	}

	trialSportsData, err := facilityClient.GetDataForSportIds(ctx, sportsDataRequest)
	if err != nil {
		log.Printf("error in GetAcademyPage while getting trial sports for ids: %v, err: %v", sportIds, err)
		return nil
	}

	bottomButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	bottomButton.SetText("Select a sport to proceed")
	bottomButtonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	bottomButtonColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)

	bottomButton.SetFont(bottomButtonFont)
	bottomButton.SetBgColor(bottomButtonColor)
	bottomButton.SetActionDisabled()
	bottomButton.SetID("bottom_button_id1")

	bottomSheetTitle, _ := sushi.NewTextSnippet("Select sport")

	sportSelectionBottomSheet := &sushi.SportSelectionBottomSheet{
		Button: bottomButton,
		Header: &sushi.SportSelectionBottomSheetHeader{
			Title: bottomSheetTitle,
		},
	}

	sportsSelectionLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType33,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: len(trialSportsData.Sports),
	}

	var trialSportSnippets []sushi.ImageTextSnippetType33SnippetItem

	for _, trialSportData := range trialSportsData.Sports {
		sportImage, _ := sushi.NewImage(trialSportData.Icon)
		sportImageBgColor, _ := sushi.NewColor(sushi.ColorType(trialSportData.IconBackgroundColor), sushi.ColorTint(sushi.ColorTint100))
		sportImage.SetAspectRatio(1)
		sportImage.SetType(sushi.ImageTypeCircle)
		sportImage.SetHeight(42)
		sportImage.SetColor(sportImageBgColor)

		sportTitle, _ := sushi.NewTextSnippet(trialSportData.SportName)
		sportTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		sportTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		sportTitle.SetColor(sportTitleColor)
		sportTitle.SetFont(sportTitleFont)

		sportClickAction := sushi.GetClickAction()

		changeBottomButton := &sushi.ChangeBottomButton{
			ButtonId: "bottom_button_id1",
		}

		changeButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
		changeButton.SetText("Proceed with selected sport")
		changeButton.SetID("bottom_button_id1")

		changeButtonClickAction := sushi.GetClickAction()
		payload := map[string]int32{
			"sport_id":            trialSportData.SportId,
			"course_id":           courseId,
			"product_category_id": 13,
		}
		postbackParams, _ := json.Marshal(payload)

		changeButtonClickAction.SetDeeplink(&sushi.Deeplink{
			URL:            util.GetSummercampTrialListMemberDeeplink(),
			PostbackParams: string(postbackParams),
		})

		changeButton.SetClickAction(changeButtonClickAction)

		changeBottomButton.Button = changeButton

		sportClickAction.SetChangeBottomButton(changeBottomButton)

		sportItem := sushi.ImageTextSnippetType33SnippetItem{
			LeftImage:    sportImage,
			Title:        sportTitle,
			IsSelected:   false,
			IsSelectable: true,
			ClickAction:  sportClickAction,
		}

		trialSportSnippets = append(trialSportSnippets, sportItem)
	}

	sportsSelectionLayout := &sushi.ImageTextSnippetType33Layout{
		LayoutConfig: sportsSelectionLayoutConfig,
		ImageTextSnippetType33: &sushi.ImageTextSnippetType33Snippet{
			Items: &trialSportSnippets,
		},
	}

	sportSelectionBottomSheet.Items = []*sushi.ImageTextSnippetType33Layout{sportsSelectionLayout}

	clickAction := sushi.GetClickAction()
	clickAction.SetSportSelectBottomSheet(sportSelectionBottomSheet)

	return clickAction
}

func (h *HomePageTemplate) SetSupportContainerSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType7,
		LayoutType:   sushi.LayoutTypeCarousel,
		SectionCount: 1,
	}

	items := make([]sushi.FitsoImageTextSnippetType7SnippetItem, 0)

	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	borderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint300)
	title, _ := sushi.NewTextSnippet("Have any queries? Help us reach you and solve your query.")

	rightButton, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
	callIconColor, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
	callIcon, _ := sushi.NewIcon(sushi.CallLineThinIcon, callIconColor)

	callClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
	cityId := util.GetCityIDFromContext(ctx)
	callClickAction.SetOpenWebview(&sushi.OpenWebview{
		Title: "",
		URL:   common.CityCallClickAction[cityId],
	})
	/*clickAction := sushi.GetClickAction()
	call := &sushi.Call{
		Number: common.FITSO_SALES_CONTACT,
	}
	clickAction.SetCall(call)*/

	rightButton.SetText("Get a call")
	rightButton.SetPrefixIcon(callIcon)
	rightButton.SetClickAction(callClickAction)

	payload := make(map[string]interface{})
	payload["user_id"] = util.GetUserIDFromContext(ctx)
	payload["source"] = "summer_camp_slots"
	tapEname := &sushi.EnameData{
		Ename: "summer_camp_call_click",
	}

	callEvents := sushi.NewClevertapEvents()
	callEvents.SetTap(tapEname)
	trackItem := sushi.GetClevertapTrackItem(ctx, payload, callEvents)
	rightButton.AddClevertapTrackingItem(trackItem)

	item := sushi.FitsoImageTextSnippetType7SnippetItem{
		BgColor:     bgColor,
		Title:       title,
		RightButton: rightButton,
		BorderColor: borderColor,
	}

	items = append(items, item)

	snippet := &sushi.FitsoImageTextSnippetType7Snippet{
		Items: &items,
	}

	separatorColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeLine, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}

	supportContainerData := &homeModels.ResultSection{
		LayoutConfig:        layoutConfig,
		FitsoImageTextType7: snippet,
		SnippetConfig:       snippetConfig,
	}

	h.AddSection(supportContainerData)

}

func (h *HomePageTemplate) SetCustomersReviewSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Hear from our customers")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}
	log.Println(snippetConfig)
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
		//SnippetConfig:		snippetConfig,
	}
	h.AddSection(section)

	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	for _, banner := range common.SummercampHearFromPeopleBanner {
		image, _ := sushi.NewImage(util.GetCDNLink(banner))
		image.SetAspectRatio(1.7)
		mediaContent := &sushi.MediaContent{
			MediaType: "image",
			Image:     image,
		}

		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
		})
	}
	mediaSnippet := &sushi.MediaType2Snippet{
		Items: mediaItems,
	}
	section1 := &homeModels.ResultSection{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType:      sushi.MediaSnippetType2,
			LayoutType:       sushi.LayoutTypeCarousel,
			SectionCount:     1,
			ShouldAutoScroll: true,
		},
		MediaSnippetType2: mediaSnippet,
	}

	h.AddSection(section1)
}

func (h *HomePageTemplate) SetFAQSection(ctx context.Context) {
	/*layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Hear from our customers")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	separatorColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint100)
	topSeparator, _ := sushi.NewSeparator(sushi.SeparatorTypeMedium, separatorColor)
	snippetConfig := &sushi.SnippetConfig{
		TopSeparator: topSeparator,
	}
	log.Println(snippetConfig)
	section := &homeModels.ResultSection{
		LayoutConfig:       layoutConfig,
		SectionHeaderType1: headerSnippet,
		//SnippetConfig:		snippetConfig,
	}
	h.AddSection(section)*/

	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	for _, banner := range common.SummercampFAQBanner {
		faqClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionOpenWebview)
		faqClickAction.SetOpenWebview(&sushi.OpenWebview{
			Title: "",
			//URL: "https://support.cult.fit/support/solutions/25000019991?force_browser=1",
			URL:   "https://www.getfitso.com/faq?force_browser=1",
		})
		image, _ := sushi.NewImage(util.GetCDNLink(banner))
		image.SetAspectRatio(0.90)
		mediaContent := &sushi.MediaContent{
			MediaType: "image",
			Image:     image,
		}

		mediaItems = append(mediaItems, &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
			ClickAction:  faqClickAction,
		})
	}
	mediaSnippet := &sushi.MediaType2Snippet{
		Items: mediaItems,
	}
	section1 := &homeModels.ResultSection{
		LayoutConfig: &sushi.LayoutConfig{
			SnippetType:      sushi.MediaSnippetType2,
			LayoutType:       sushi.LayoutTypeCarousel,
			SectionCount:     1,
			ShouldAutoScroll: true,
		},
		MediaSnippetType2: mediaSnippet,
	}

	h.AddSection(section1)
}
