package homeController

import (
	"context"
	"net/http"
	"fmt"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type EmptyHomePageTemplate struct {
	PageType                string                          `json:"-"`
	EmptyView               *sushi.EmptyViewType1Layout     `json:"empty_view,omitempty"`
	Footer                  *sushi.FooterSnippetType2Layout `json:"footer,omitempty"`
	UnreadNotificationCount int32                           `json:"unread_notification_count"`
	ClevertapTracking       []*sushi.ClevertapItem          `json:"clever_tap_tracking,omitempty"`
}

func GetMasterkeyEmptyPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	template := &EmptyHomePageTemplate{}
	template.SetPageType(common.PAGE_TYPE_MASTERKEY_HOME_EMPTY)
	template.SetEmptyView(ctx)
	template.SetFooter(ctx)
	template.SetNotificationCount(ctx)
	template.SetPageTracking(ctx)
	c.JSON(http.StatusOK, template)
}

func GetAcademyEmptyPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	template := &EmptyHomePageTemplate{}
	template.SetPageType(common.PAGE_TYPE_ACADEMY_HOME_EMPTY)
	template.SetEmptyView(ctx)
	template.SetFooter(ctx)
	template.SetNotificationCount(ctx)
	template.SetPageTracking(ctx)
	c.JSON(http.StatusOK, template)
}

func GetSummerCampEmptyPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	template := &EmptyHomePageTemplate{}
	template.SetPageType(common.PAGE_TYPE_SUMMER_CAMP_HOME_EMPTY)
	template.SetEmptyView(ctx)
	//template.SetFooter(ctx)
	template.SetNotificationCount(ctx)
	template.SetPageTracking(ctx)
	c.JSON(http.StatusOK, template)
}

func GetNoFeatureAvailablePage(c *gin.Context) {
    ctx := util.PrepareGRPCMetaData(c)
    template := &EmptyHomePageTemplate{}
    template.SetPageType(common.PAGE_TYPE_NO_FEATURES)
    if val,ok:=c.Get("show_maintenance"); ok && val.(bool)==true{
        fmt.Println("showing maintenance page")
        template.SetEmptyViewV2(ctx,true)
    }else{
            template.SetEmptyView(ctx)
    }
    template.SetNotificationCount(ctx)
    template.SetPageTracking(ctx)
    c.JSON(http.StatusOK, template)
}

func (e *EmptyHomePageTemplate) SetPageType(pageType string) {
	e.PageType = pageType
}

func (e *EmptyHomePageTemplate) SetEmptyView(ctx context.Context) {
	clickAction := sushi.GetClickAction()
	deeplink := sushi.Deeplink{
		URL: util.GetChangeLocationDeeplink(),
	}
	clickAction.SetDeeplink(&deeplink)

	var title, subtitle1 *sushi.TextSnippet
	var locationError string

	switch e.PageType {
	case common.PAGE_TYPE_MASTERKEY_HOME_EMPTY:
		title, _ = sushi.NewTextSnippet("cultpass PLAY not available")
		subtitle1, _ = sushi.NewTextSnippet("cultpass PLAY is not available at your current location.")
		locationError = "no_masterkey"

	case common.PAGE_TYPE_ACADEMY_HOME_EMPTY:
		title, _ = sushi.NewTextSnippet("cult ACADEMY not available")
		subtitle1, _ = sushi.NewTextSnippet("cult ACADEMY is not available at your current location.")
		locationError = "no_academy"

	case common.PAGE_TYPE_SUMMER_CAMP_HOME_EMPTY:
		title, _ = sushi.NewTextSnippet("cult Summer camp not available")
		subtitle1, _ = sushi.NewTextSnippet("cult Summer camp is not available at your current location.")
		locationError = "no_summer_camp"

	case common.PAGE_TYPE_NO_FEATURES:
		title, _ = sushi.NewTextSnippet("Uncharted territory")
		subtitle1, _ = sushi.NewTextSnippet("Whoopsie! No cultpass PLAY centers nearby. Change location to keep exploring.")
		locationError = "no_fitso"
	}

	tapPayload := make(map[string]interface{})
	tapPayload["error"] = locationError
	tapEname := &sushi.EnameData{
		Ename: "change_location_button_tap",
	}
	changeLocationEvents := sushi.NewClevertapEvents()
	changeLocationEvents.SetTap(tapEname)
	trackItem := e.GetClevertapTrackItem(ctx, tapPayload, changeLocationEvents)

	items := make([]sushi.FooterSnippetType2ButtonItem, 0)
	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeText,
		Text:              "Change location",
		ClickAction:       clickAction,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	items = append(items, buttonItem)

	bottomButton := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}

	image, _ := sushi.NewImage(util.GetCDNLink("uploads/Group23303.png"))
	image.SetHeight(220)
	image.SetWidth(258)

	snippet := &sushi.EmptyViewType1Snippet{
		Title:        title,
		Subtitle1:    subtitle1,
		Image:        image,
		BottomButton: bottomButton,
	}
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.EmptyViewType1,
	}
	section := &sushi.EmptyViewType1Layout{
		LayoutConfig:          layoutConfig,
		EmptyViewType1Snippet: snippet,
	}
	e.EmptyView = section
}

func (e *EmptyHomePageTemplate) SetEmptyViewV2(ctx context.Context, showMaintenance bool) {
    var title, subtitle1 *sushi.TextSnippet
    var locationError string

    // Show maintenance message for all page types
    title, _ = sushi.NewTextSnippet("Maintenance in progress")
    subtitle1, _ = sushi.NewTextSnippet("We're currently performing scheduled maintenance to improve our services. Please check back later.")
    
    // Set appropriate error code based on page type
    switch e.PageType {
    case common.PAGE_TYPE_MASTERKEY_HOME_EMPTY:
        locationError = "maintenance_masterkey"
    case common.PAGE_TYPE_ACADEMY_HOME_EMPTY:
        locationError = "maintenance_academy"
    case common.PAGE_TYPE_SUMMER_CAMP_HOME_EMPTY:
        locationError = "maintenance_summer_camp"
    case common.PAGE_TYPE_NO_FEATURES:
        locationError = "maintenance_no_features"
    default:
        locationError = "maintenance_general"
    }

    // Create tracking payload
    tapPayload := make(map[string]interface{})
    tapPayload["error"] = locationError
    tapEname := &sushi.EnameData{
        Ename: "maintenance_page_impression",
    }
    maintenanceEvents := sushi.NewClevertapEvents()
    maintenanceEvents.SetPageSuccess(tapEname)
    trackItem := e.GetClevertapTrackItem(ctx, tapPayload, maintenanceEvents)

    image, _ := sushi.NewImage(util.GetCDNLink("uploads/Group23303.png"))
    image.SetHeight(220)
    image.SetWidth(258)

    // Create the empty view without a button
    snippet := &sushi.EmptyViewType1Snippet{
        Title:        title,
        Subtitle1:    subtitle1,
        Image:        image,
        // No bottom button
    }
    
    layoutConfig := &sushi.LayoutConfig{
        SnippetType: sushi.EmptyViewType1,
    }
    
    section := &sushi.EmptyViewType1Layout{
        LayoutConfig:          layoutConfig,
        EmptyViewType1Snippet: snippet,
    }
    
    e.EmptyView = section
    
    // Add tracking to the page itself
    e.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
}


func (e *EmptyHomePageTemplate) SetFooter(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	var title, clickActionPageType, ename string
	switch e.PageType {
	case common.PAGE_TYPE_ACADEMY_HOME_EMPTY:
		title = "Switch to cultpass PLAY"
		clickActionPageType = common.PAGE_TYPE_MASTERKEY
		ename = "switch_masterkey_tap"

	case common.PAGE_TYPE_MASTERKEY_HOME_EMPTY:
		title = "Switch to cult ACADEMY"
		clickActionPageType = common.PAGE_TYPE_SUMMER_CAMP_HOME
		ename = "switch_academy_tap"
	}

	tapEname := &sushi.EnameData{
		Ename: ename,
	}
	switchFeatureEvents := sushi.NewClevertapEvents()
	switchFeatureEvents.SetTap(tapEname)
	trackItem := e.GetClevertapTrackItem(ctx, nil, switchFeatureEvents)

	buttonItem := sushi.FooterSnippetType2ButtonItem{
		Type:              sushi.FooterButtonTypeSolid,
		Text:              title,
		ClickAction:       getFeatureKeySaveClickAction(ctx, clickActionPageType, false, true),
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	items := []sushi.FooterSnippetType2ButtonItem{buttonItem}

	bottomButton := &sushi.FooterSnippetType2Button{
		Orientation: sushi.FooterButtonOrientationVertical,
		Items:       &items,
	}
	snippet := &sushi.FooterSnippetType2Snippet{
		ButtonData: bottomButton,
	}
	e.Footer = &sushi.FooterSnippetType2Layout{
		LayoutConfig:       layoutConfig,
		FooterSnippetType2: snippet,
	}
}

func (e *EmptyHomePageTemplate) SetNotificationCount(ctx context.Context) {
	count, _ := getNotificationCount(ctx)
	e.UnreadNotificationCount = count
}

func (e *EmptyHomePageTemplate) SetPageTracking(ctx context.Context) {
	payload := make(map[string]interface{})
	payload["page_type"] = e.PageType
	landingEname := &sushi.EnameData{
		Ename: "uncharted_territory_impression",
	}
	pageEvents := sushi.NewClevertapEvents()
	pageEvents.SetPageSuccess(landingEname)
	trackItem := e.GetClevertapTrackItem(ctx, payload, pageEvents)
	e.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
}

func (e *EmptyHomePageTemplate) GetClevertapTrackItem(
	ctx context.Context,
	trackPayload map[string]interface{},
	trackEvents *sushi.EventNames,
) *sushi.ClevertapItem {
	if trackPayload == nil {
		trackPayload = make(map[string]interface{})
	}
	trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
	trackPayload["subzone_id"] = util.GetSubzoneIDFromContext(ctx)
	trackPayload["source"] = "home"

	return sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
}
