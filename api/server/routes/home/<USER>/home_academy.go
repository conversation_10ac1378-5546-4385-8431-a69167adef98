package homeController

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"

	common "bitbucket.org/jogocoin/go_api/api/common"
	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/api/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	featureSupport "bitbucket.org/jogocoin/go_api/api/server/routes/internal"
	sharedFunc "bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"github.com/gin-gonic/gin"
)

type AcademyHomePageData struct {
	CourseCreators []*facilitySportPB.CourseCreator
	CoursesDetails []*facilitySportPB.Course
	Benefit        *facilitySportPB.Benefit
	Testimonials   []string
	SportIds       []int32
	PostbackParams map[string]interface{}
}

type AcademyHomePageTemplate struct {
	Sections                []*homeModels.ResultSection        `json:"results,omitempty"`
	PageType                string                             `json:"page_type,omitempty"`
	Header                  *sushi.FitsoTextSnippetType6Layout `json:"header,omitempty"`
	Footer                  *sushi.FooterSnippetType2Layout    `json:"footer,omitempty"`
	UnreadNotificationCount int32                              `json:"unread_notification_count"`
	PostbackParams          string                             `json:"postback_params,omitempty"`
	ClevertapTracking       []*sushi.ClevertapItem             `json:"clever_tap_tracking,omitempty"`
	PageData                *AcademyHomePageData               `json:"-"`
}

const SHORT_NOTICE_ACADEMY_PURCHASE = 23

func GetAcademyPage(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	template := AcademyHomePageTemplate{
		PageData: &AcademyHomePageData{},
	}
	template.SetPageData(ctx)
	template.SetPageType(common.PAGE_TYPE_ACADEMY)
	template.SetHeader(ctx)
	template.SetFooter(ctx)
	template.AddFeatureBottomSheetSection(ctx)
	template.SetShortNoticeSection(ctx)
	template.AddSuggestionsSection(ctx)
	template.SetAssessmentsSection(ctx)
	template.AddAcademyBenefitsSection(ctx)
	template.AddCoursesSection(ctx)
	template.AddCourseCreatorsSection(ctx)
	template.AddTestimonialsSection(ctx)
	template.AddNearbyFacilitiesSection(ctx)
	template.SetPostbackParams(ctx)
	template.SetNotificationCount(ctx)
	template.SetPageTracking(ctx)
	c.JSON(http.StatusOK, template)
}

func (template *AcademyHomePageTemplate) SetAssessmentsSection(ctx context.Context) {
	userID := util.GetUserIDFromContext(ctx)

	if userID == 0 {
		return
	}

	userClient := util.GetUserServiceClient()

	response, err := userClient.GetUserAcademyAssessments(ctx, &userPB.UserRequest{})

	if err != nil {
		log.Printf("Error in home SetAssessmentsSection for user_id: %d, error: %v", userID, err)
		return
	}

	if len(response.UserAssessments) == 0 {
		log.Printf("no asessments found for user_id: %d", userID)
		return
	}

	isNewColorSupported := featureSupport.SupportsNewColor(ctx)

	userAssessment := response.UserAssessments[0]

	userHeaderLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoImageTextSnippetType12,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	userImage, _ := sushi.NewImage(userAssessment.ProfileImage)
	userImage.SetAspectRatio(1)
	userImage.SetType(sushi.ImageTypeCircle)
	userImage.SetHeight(40)
	userImage.SetWidth(40)

	userTitle, _ := sushi.NewTextSnippet(userAssessment.UserName)
	userTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize400)
	userTitle.SetFont(userTitleFont)

	userSubtitle, _ := sushi.NewTextSnippet(fmt.Sprintf("%d years", userAssessment.UserAge))
	userSubtitleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
	userSubtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
	userSubtitle.SetColor(userSubtitleColor)
	userSubtitle.SetFont(userSubtitleFont)

	rightButton, _ := sushi.NewButton(sushi.ButtontypeText)
	rightButton.SetText("All Members")

	rightButtonClickAction := sushi.GetClickAction()
	rightButtonClickAction.SetClickActionType(sushi.ClickActionDeeplink)
	rightButtonClickAction.SetDeeplink(&sushi.Deeplink{
		URL: util.GetAssessmentsPageDeeplink(),
	})

	rightButton.SetClickAction(rightButtonClickAction)

	imageTextSnippet12Item := &sushi.FitsoImageTextSnippetType12SnippetItem{
		Image:     userImage,
		Title:     userTitle,
		Subtitle1: userSubtitle,
	}
	if len(response.UserAssessments) > 1 {
		imageTextSnippet12Item.RightButton = rightButton
	}

	imageTextSnippetBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)

	template.AddSection(&homeModels.ResultSection{
		LayoutConfig: userHeaderLayoutConfig,
		FitsoImageTextSnippetType12: &sushi.FitsoImageTextSnippetType12Snippet{
			Items:   []*sushi.FitsoImageTextSnippetType12SnippetItem{imageTextSnippet12Item},
			BgColor: imageTextSnippetBgColor,
		},
	})

	var assessmentLayout *sushi.LayoutConfig
	if len(userAssessment.Assessments) == 1 {
		assessmentLayout = &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType50,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
	} else {
		assessmentLayout = &sushi.LayoutConfig{
			SnippetType:  sushi.V2ImageTextSnippetType50,
			LayoutType:   sushi.LayoutTypeCarousel,
			VisibleCards: 1.5,
		}
	}

	var v2ImageTextSnippetType50Items []*sushi.V2ImageTextSnippetType50SnippetItem

	for _, assessment := range userAssessment.Assessments {
		image, _ := sushi.NewImage(assessment.SportImage)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(40)
		image.SetWidth(40)

		if val, ok := common.SportIdBackgroundColorMap[assessment.SportId]; ok {
			sportBgColor, _ := sushi.NewColor(sushi.ColorType(val[0]), sushi.ColorTint(val[1]))
			image.SetColor(sportBgColor)
		}

		title, _ := sushi.NewTextSnippet(assessment.SportName)
		titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		title.SetFont(titleFont)

		subtitle, _ := sushi.NewTextSnippet(assessment.AssessmentDateTime)
		subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)
		subtitle.SetFont(subtitleFont)
		subtitle.SetColor(subtitleColor)

		var level string
		if strings.ToLower(assessment.CourseCategoryName) == strings.ToLower(assessment.CourseCategoryLevelName) {
			level = assessment.CourseCategoryName
		} else {
			level = fmt.Sprintf("%s %s", assessment.CourseCategoryName, assessment.CourseCategoryLevelName)
		}

		subtitle1, _ := sushi.NewTextSnippet(level)
		subtitle1Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		colorType := sushi.ColorTypeBlue
		if isNewColorSupported {
			colorType = sushi.NEUTRAL_LIGHT_THEME
		}
		subtitle1Color, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		iconColor, _ := sushi.NewColor(colorType, sushi.ColorTint500)
		prefixIcon, _ := sushi.NewIcon(sushi.CheckIcon, iconColor)

		subtitle1.SetFont(subtitle1Font)
		subtitle1.SetColor(subtitle1Color)
		subtitle1.SetPrefixIcon(prefixIcon)

		subtitle2, _ := sushi.NewTextSnippet("Assessment Report")
		subtitle2Font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		subtitle2Color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		iconColor2, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		suffixIcon, _ := sushi.NewIcon(sushi.RightIcon, iconColor2)

		subtitle2.SetFont(subtitle2Font)
		subtitle2.SetColor(subtitle2Color)
		subtitle2.SetSuffixIcon(suffixIcon)

		clickAction := sushi.GetClickAction()
		clickAction.SetClickActionType(sushi.ClickActionDeeplink)
		clickAction.SetDeeplink(&sushi.Deeplink{
			URL: util.GetAssessmentReportDeeplink(assessment.AssessmentId),
		})

		item := &sushi.V2ImageTextSnippetType50SnippetItem{
			Image:       image,
			Title:       title,
			Subtitle:    subtitle,
			Subtitle1:   subtitle1,
			Subtitle2:   subtitle2,
			ClickAction: clickAction,
		}

		v2ImageTextSnippetType50Items = append(v2ImageTextSnippetType50Items, item)
	}

	crouselSnippetBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint50)

	template.AddSection(&homeModels.ResultSection{
		LayoutConfig: assessmentLayout,
		V2ImageTextSnippetType50: &sushi.V2ImageTextSnippetType50Snippet{
			Items:   v2ImageTextSnippetType50Items,
			BgColor: crouselSnippetBgColor,
		},
	})
}

func (a *AcademyHomePageTemplate) GetUserSuggestions(ctx context.Context) []*userPB.Suggestion {
	var suggestions []*userPB.Suggestion

	userClient := util.GetUserServiceClient()
	suggestionResponse, err := userClient.GetSuggestionsForAcademy(ctx, &userPB.Empty{})
	if err != nil {
		log.Printf("GetSuggestionsForAcademy, Error: %v", err)
		return suggestions
	}
	if suggestionResponse.Status != nil && suggestionResponse.Status.Status == common.FAILED {
		log.Printf("GetSuggestionsForAcademy, Error: %s", suggestionResponse.Status.Message)
		return suggestions
	}
	suggestions = suggestionResponse.Suggestion
	return suggestions
}

func (a *AcademyHomePageTemplate) SetPageData(ctx context.Context) {
	facilityClient := util.GetFacilitySportClient()
	resp, err := facilityClient.GetAcademyHomeDetails(ctx, &facilitySportPB.Empty{})
	if err != nil {
		return
	}
	a.PageData.CourseCreators = resp.Creators
	a.PageData.CoursesDetails = resp.Courses
	a.PageData.Benefit = resp.Benefit
	a.PageData.Testimonials = resp.Testimonials
	a.PageData.SportIds = resp.SportIds

	return
}

func (a *AcademyHomePageTemplate) GetNearbyFacilities(ctx context.Context) *facilitySportPB.AcademyFacilitiesResponse {
	facilityClient := util.GetFacilitySportClient()
	req := &facilitySportPB.AcademyFacilitiesRequest{
		Count: common.ACADEMY_HOME_PAGE_FACILITY_COUNT,
	}
	res, err := facilityClient.GetAcademyFacilities(ctx, req)
	if err != nil {
		log.Printf("nearby academy facilities | home page | Error: %v", err)
		return nil
	}
	if res.Status != nil && res.Status.Status == common.FAILED {
		log.Printf("nearby academy facilities | home page | Error: %v", res.Status.Message)
		return nil
	}
	return res
}

func (a *AcademyHomePageTemplate) SetShortNoticeSection(ctx context.Context) {
	userServiceClient := util.GetUserServiceClient()
	shortNoticeResponse, err := userServiceClient.GetShortNotice(ctx, &userPB.GetShortNoticeRequest{
		ProductCategoryId: common.AcademyCategoryID,
	})
	if err != nil {
		log.Printf("Api: home academy, function: SetShortNoticeSection, Error: %v", err)
		return
	}
	if len(shortNoticeResponse.ShortNotice) == 0 {
		return
	}
	shortNoticeData := shortNoticeResponse.ShortNotice[0]
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType32,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	items := make([]sushi.ImageTextSnippetType32SnippetItem, 0)
	item := sushi.ImageTextSnippetType32SnippetItem{}

	imageLeft, _ := sushi.NewImage(util.GetCDNLink(common.SHORT_NOTICE_ICON_PATH))
	placeholderColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
	imageLeft.SetPlaceholderColor(placeholderColor)
	imageLeft.SetType(sushi.ImageTypeCircle)
	item.Image = imageLeft
	var snippetData *sushi.ImageTextSnippetType32Snippet
	if shortNoticeData.ShortNoticeId == SHORT_NOTICE_ACADEMY_PURCHASE {
		if featureSupport.SupportsAcademyPurchaseFlow(ctx) {
			title, _ := sushi.NewTextSnippet(shortNoticeData.Title)
			font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
			title.SetFont(font)
			item.Title = title

			subtitle, _ := sushi.NewTextSnippet(shortNoticeData.Subtitle)
			fontSubtitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			colorSubtitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
			subtitle.SetFont(fontSubtitle)
			subtitle.SetColor(colorSubtitle)
			subtitle.SetNumberOfLines(3)
			item.Subtitle1 = subtitle

			button, _ := sushi.NewButton(sushi.ButtontypeText)
			button.SetText(shortNoticeData.ActionContent)
			icon, _ := sushi.NewIcon(sushi.RightIcon, nil)
			button.SetSuffixIcon(icon)
			buttonColorArr := strings.Split(shortNoticeData.ActionContentColor, "&")
			if len(buttonColorArr) == 2 {
				buttonColorType := sushi.ColorType(buttonColorArr[0])
				buttonColorTint := sushi.ColorTint(buttonColorArr[1])
				buttonColor, _ := sushi.NewColor(buttonColorType, buttonColorTint)
				button.SetColor(buttonColor)
			}
			item.BottomButton = button

			clickAction := sushi.GetClickAction()
			deeplink := &sushi.Deeplink{
				URL: shortNoticeData.ActionUrl,
			}
			clickAction.SetDeeplink(deeplink)
			item.ClickAction = clickAction

			items = append(items, item)
			bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
			bgColorArr := strings.Split(shortNoticeData.BackgroundColor, "&")
			if len(bgColorArr) == 2 {
				bgColorType := sushi.ColorType(bgColorArr[0])
				bgColorTint := sushi.ColorTint(bgColorArr[1])
				bgColor, _ = sushi.NewColor(bgColorType, bgColorTint)
			}
			snippetData = &sushi.ImageTextSnippetType32Snippet{
				Items:   &items,
				BgColor: bgColor,
			}
		} else {
			return
		}

	} else {

		title, _ := sushi.NewTextSnippet(shortNoticeData.Title)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title.SetFont(font)
		item.Title = title

		subtitle, _ := sushi.NewTextSnippet(shortNoticeData.Subtitle)
		fontSubtitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
		colorSubtitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		subtitle.SetFont(fontSubtitle)
		subtitle.SetColor(colorSubtitle)
		subtitle.SetNumberOfLines(3)
		item.Subtitle1 = subtitle

		items = append(items, item)
		bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint50)
		bgColorArr := strings.Split(shortNoticeData.BackgroundColor, "&")
		if len(bgColorArr) == 2 {
			bgColorType := sushi.ColorType(bgColorArr[0])
			bgColorTint := sushi.ColorTint(bgColorArr[1])
			bgColor, _ = sushi.NewColor(bgColorType, bgColorTint)
		}

		snippetData = &sushi.ImageTextSnippetType32Snippet{
			Items:   &items,
			BgColor: bgColor,
		}
	}
	section := homeModels.ResultSection{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType32: snippetData,
	}
	a.AddSection(&section)
}

func (a *AcademyHomePageTemplate) GetVideosDetails(ctx context.Context, videoIds []int32) map[int32]*facilitySportPB.Video {
	facilityClient := util.GetFacilitySportClient()
	req := &facilitySportPB.VideoDetailsRequest{
		VideoIds: videoIds,
	}
	res, err := facilityClient.GetVideoDetails(ctx, req)

	videosMap := make(map[int32]*facilitySportPB.Video)
	if err != nil {
		log.Printf("unable to get videos, Error: %v", err)
		return videosMap
	}

	for _, val := range res.Videos {
		videosMap[val.VideoId] = val
	}
	return videosMap
}

func (a *AcademyHomePageTemplate) AddSection(section *homeModels.ResultSection) {
	a.Sections = append(a.Sections, section)
}

func (a *AcademyHomePageTemplate) AddPostbackParam(key string, val interface{}) {
	if a.PageData.PostbackParams == nil {
		a.PageData.PostbackParams = make(map[string]interface{})
	}
	a.PageData.PostbackParams[key] = val
}

func (a *AcademyHomePageTemplate) SetPostbackParams(ctx context.Context) {
	if len(a.PageData.PostbackParams) == 0 {
		return
	}
	postbackParam, _ := json.Marshal(a.PageData.PostbackParams)
	a.PostbackParams = string(postbackParam)
}

func (a *AcademyHomePageTemplate) SetPageType(pageType string) {
	a.PageType = pageType
}

func (a *AcademyHomePageTemplate) SetHeader(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType6,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	clickAction := getBottomSheetFeatureChangeClickAction(ctx, common.PAGE_TYPE_ACADEMY)
	t := "CULT ACADEMY"

	title, _ := sushi.NewTextSnippet(t)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, iconColor)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)
	title.SetSuffixIcon(icon)

	snippet := &sushi.FitsoTextType6Snippet{
		Title:       title,
		ClickAction: clickAction,
	}
	a.Header = &sushi.FitsoTextSnippetType6Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType6: snippet,
	}
}

func (a *AcademyHomePageTemplate) SetNotificationCount(ctx context.Context) {
	count, _ := getNotificationCount(ctx)
	a.UnreadNotificationCount = count
}

func (a *AcademyHomePageTemplate) SetFooter(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType: sushi.FooterSnippetType2,
	}

	items := []sushi.FooterSnippetType2ButtonItem{}

	if featureSupport.SupportsAcademyPurchaseFlow(ctx) {
		items = GetAcademyFooterCTA(ctx, "academy_trial_sport_select", a.PageData.SportIds, 0)
	} else {
		trialButton := GetTrialButton(ctx, "Book a free trial class", "academy_trial_sport_select", a.PageData.SportIds, 0)
		if trialButton != nil {
			items = append(items, *trialButton)
		}
	}

	if len(items) > 0 {
		bottomButton := &sushi.FooterSnippetType2Button{
			Orientation: sushi.FooterButtonOrientationVertical,
			Items:       &items,
		}
		snippet := &sushi.FooterSnippetType2Snippet{
			ButtonData: bottomButton,
		}
		a.Footer = &sushi.FooterSnippetType2Layout{
			LayoutConfig:       layoutConfig,
			FooterSnippetType2: snippet,
		}
	}
}

func GetTrialButton(ctx context.Context, buttonText string, postAction string, courseSportIds []int32, courseId int32) *sushi.FooterSnippetType2ButtonItem {
	button := &sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeSolid,
		Text: buttonText,
	}

	tapEname := &sushi.EnameData{
		Ename: "academy_book_trial_button_tap",
	}
	tapEvents := sushi.NewClevertapEvents()
	tapEvents.SetTap(tapEname)

	trackItem := GetClevertapTrackItem(ctx, nil, tapEvents)
	button.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	clickAction := GetBookATrialClickAction(ctx, postAction, courseSportIds, courseId)
	button.ClickAction = clickAction

	return button
}

func GetTrialBookClickAction(ctx context.Context, sportIds []int32, courseId int32) *sushi.ClickAction {
	if len(sportIds) > 1 {
		return GetMultiSportsClickAction(ctx, sportIds, courseId)
	}
	if len(sportIds) == 1 {
		return GetSingleSportClickAction(ctx, sportIds[0], courseId)
	}
	return nil
}

func GetSingleSportClickAction(ctx context.Context, sportId int32, courseId int32) *sushi.ClickAction {
	payload := map[string]int32{
		"sport_id":  sportId,
		"course_id": courseId,
	}
	postbackParams, _ := json.Marshal(payload)

	clickAction := sushi.GetClickAction()
	clickAction.SetDeeplink(&sushi.Deeplink{
		URL:            util.GetAcademyTrialListMemberDeeplink(),
		PostbackParams: string(postbackParams),
	})
	return clickAction
}

func GetMultiSportsClickAction(ctx context.Context, sportIds []int32, courseId int32) *sushi.ClickAction {
	facilityClient := util.GetFacilitySportClient()
	sportsDataRequest := &facilitySportPB.GetDataForSportIdsRequest{
		SportIdsArray:       sportIds,
		MinimalDataRequired: true,
	}

	trialSportsData, err := facilityClient.GetDataForSportIds(ctx, sportsDataRequest)
	if err != nil {
		log.Printf("error in GetAcademyPage while getting trial sports for ids: %v, err: %v", sportIds, err)
		return nil
	}

	bottomButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
	bottomButton.SetText("Select a sport to proceed")
	bottomButtonFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
	bottomButtonColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint500)

	bottomButton.SetFont(bottomButtonFont)
	bottomButton.SetBgColor(bottomButtonColor)
	bottomButton.SetActionDisabled()
	bottomButton.SetID("bottom_button_id1")

	bottomSheetTitle, _ := sushi.NewTextSnippet("Select sport")

	sportSelectionBottomSheet := &sushi.SportSelectionBottomSheet{
		Button: bottomButton,
		Header: &sushi.SportSelectionBottomSheetHeader{
			Title: bottomSheetTitle,
		},
	}

	sportsSelectionLayoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType33,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: len(trialSportsData.Sports),
	}

	var trialSportSnippets []sushi.ImageTextSnippetType33SnippetItem

	for _, trialSportData := range trialSportsData.Sports {
		sportImage, _ := sushi.NewImage(trialSportData.Icon)
		sportImageBgColor, _ := sushi.NewColor(sushi.ColorType(trialSportData.IconBackgroundColor), sushi.ColorTint(sushi.ColorTint100))
		sportImage.SetAspectRatio(1)
		sportImage.SetType(sushi.ImageTypeCircle)
		sportImage.SetHeight(42)
		sportImage.SetColor(sportImageBgColor)

		sportTitle, _ := sushi.NewTextSnippet(trialSportData.SportName)
		sportTitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
		sportTitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize500)
		sportTitle.SetColor(sportTitleColor)
		sportTitle.SetFont(sportTitleFont)

		sportClickAction := sushi.GetClickAction()

		changeBottomButton := &sushi.ChangeBottomButton{
			ButtonId: "bottom_button_id1",
		}

		changeButton, _ := sushi.NewButton(sushi.ButtonTypeSolid)
		changeButton.SetText("Proceed with selected sport")
		changeButton.SetID("bottom_button_id1")

		changeButtonClickAction := sushi.GetClickAction()
		payload := map[string]int32{
			"sport_id":  trialSportData.SportId,
			"course_id": courseId,
		}
		postbackParams, _ := json.Marshal(payload)

		changeButtonClickAction.SetDeeplink(&sushi.Deeplink{
			URL:            util.GetAcademyTrialListMemberDeeplink(),
			PostbackParams: string(postbackParams),
		})

		changeButton.SetClickAction(changeButtonClickAction)

		changeBottomButton.Button = changeButton

		sportClickAction.SetChangeBottomButton(changeBottomButton)

		sportItem := sushi.ImageTextSnippetType33SnippetItem{
			LeftImage:    sportImage,
			Title:        sportTitle,
			IsSelected:   false,
			IsSelectable: true,
			ClickAction:  sportClickAction,
		}

		trialSportSnippets = append(trialSportSnippets, sportItem)
	}

	sportsSelectionLayout := &sushi.ImageTextSnippetType33Layout{
		LayoutConfig: sportsSelectionLayoutConfig,
		ImageTextSnippetType33: &sushi.ImageTextSnippetType33Snippet{
			Items: &trialSportSnippets,
		},
	}

	sportSelectionBottomSheet.Items = []*sushi.ImageTextSnippetType33Layout{sportsSelectionLayout}

	clickAction := sushi.GetClickAction()
	clickAction.SetSportSelectBottomSheet(sportSelectionBottomSheet)

	return clickAction
}

func (a *AcademyHomePageTemplate) AddFeatureBottomSheetSection(ctx context.Context) {
	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType6,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	clickAction := getBottomSheetFeatureChangeClickAction(ctx, common.PAGE_TYPE_ACADEMY)
	t := "CULT ACADEMY"

	title, _ := sushi.NewTextSnippet(t)
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize500)
	iconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
	icon, _ := sushi.NewIcon(sushi.ArrowDownIconV2, iconColor)
	title.SetFont(font)
	title.SetColor(color)
	title.SetKerning(1)
	title.SetSuffixIcon(icon)

	snippet := &sushi.FitsoTextType6Snippet{
		Title:       title,
		ClickAction: clickAction,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType6: snippet,
	}
	a.AddSection(section)
}

func (a *AcademyHomePageTemplate) AddSuggestionsSection(ctx context.Context) {
	suggestions := a.GetUserSuggestions(ctx)

	if len(suggestions) == 0 {
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:      sushi.MediaSnippetType2,
		LayoutType:       sushi.LayoutTypeCarousel,
		SectionCount:     1,
		ShouldAutoScroll: true,
	}

	videoIds := make([]int32, 0)
	for _, val := range suggestions {
		if val.VideoId != 0 {
			videoIds = append(videoIds, val.VideoId)
		}
	}

	var videosMap map[int32]*facilitySportPB.Video
	if len(videoIds) > 0 {
		videosMap = a.GetVideosDetails(ctx, videoIds)
	}

	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	for _, val := range suggestions {
		var mediaContent *sushi.MediaContent
		if val.VideoId != 0 {
			videoDetails, ok := videosMap[val.VideoId]
			if !ok {
				continue
			}
			video := util.ConvertVideoProtoToVideoTemplate(videoDetails)
			mediaContent = &sushi.MediaContent{
				MediaType: sushi.MEDIA_TYPE_VIDEO,
				Video:     video,
			}
		} else if val.SuggestionImage != "" {
			image, _ := sushi.NewImage(val.SuggestionImage)
			image.SetAspectRatio(1.77)
			mediaContent = &sushi.MediaContent{
				MediaType: sushi.MEDIA_TYPE_IMAGE,
				Image:     image,
			}
		}

		item := &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
		}

		switch val.ActionId {
		case common.SUGGESTION_ACTION_MAX_SAFETY_BOTTOM_SHEET:
			clickAction := a.GetMaxSafetyBottomSheetClickAction(ctx)
			item.ClickAction = clickAction

		case common.SUGGESTION_ACTION_ACADEMY_BOOK_TRIAL:
			clickAction := GetBookATrialClickAction(ctx, "academy_trial_sport_select", a.PageData.SportIds, 0)
			item.ClickAction = clickAction

		case common.SUGGESTION_ACTION_ACADEMY_WALKTHROUGH:
			clickAction := a.GetWhatToExpectBottomSheet(ctx)
			item.ClickAction = clickAction

		case common.SUGGESTION_ACTION_GENERIC_DEEPLINK:
			deeplink := &sushi.Deeplink{
				URL: val.Url,
			}
			clickAction := sushi.GetClickAction()
			clickAction.SetDeeplink(deeplink)
			item.ClickAction = clickAction

		case common.SUGGESTION_ACTION_APP_WEB_LINK:

			clickAction := sushi.GetClickAction()
			openWebview := sushi.OpenWebview{
				URL:   val.Url,
				InApp: true,
			}
			clickAction.SetOpenWebview(&openWebview)
			item.ClickAction = clickAction

		case common.SUGGESTION_ACTION_WEB_LINK:
			//clickAction for web
			clickAction := sushi.GetClickAction()
			openWebview := sushi.OpenWebview{
				URL: util.AddGetParamURL(val.Url, "force_browser", "1"),
			}
			clickAction.SetOpenWebview(&openWebview)
			item.ClickAction = clickAction

		}

		mediaItems = append(mediaItems, item)
	}
	snippet := &sushi.MediaType2Snippet{
		Items: mediaItems,
	}
	section := &homeModels.ResultSection{
		LayoutConfig:      layoutConfig,
		MediaSnippetType2: snippet,
	}
	a.AddSection(section)
}

func (a *AcademyHomePageTemplate) AddCourseCreatorsSection(ctx context.Context) {
	if len(a.PageData.CourseCreators) == 0 {
		return
	}

	headerTitle, _ := sushi.NewTextSnippet("Curriculum created by")

	impressionEname := &sushi.EnameData{
		Ename: "course_creators_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := GetClevertapTrackItem(ctx, nil, sectionEvents)

	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title:             headerTitle,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	headerSection := &homeModels.ResultSection{
		LayoutConfig:       headerLayout,
		SectionHeaderType1: headerSnippet,
	}
	a.AddSection(headerSection)

	items := make([]sushi.V2ImageTextSnippetType49SnippetItem, 0)
	for _, val := range a.PageData.CourseCreators {
		item := sushi.V2ImageTextSnippetType49SnippetItem{}

		title, _ := sushi.NewTextSnippet(val.Name)
		titleFont, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
		titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
		title.SetFont(titleFont)
		title.SetColor(titleColor)
		item.Title = title

		if val.Title != "" {
			subtitle, _ := sushi.NewTextSnippet(val.Title)
			subtitleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint700)
			subtitleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
			subtitle.SetColor(subtitleColor)
			subtitle.SetFont(subtitleFont)
			item.Subtitle = subtitle
		}

		rightImage, _ := sushi.NewImage(util.GetCDNLink(val.ImageUrl))
		rightImage.SetAspectRatio(0.9)
		rightImage.SetHeight(125)
		rightImage.SetWidth(125)
		item.RightImage = rightImage

		subtitleItems := make([]sushi.SubtitleListItem, 0)
		for _, detail := range val.Details {
			icon, _ := sushi.NewIcon(sushi.PointIcon, nil)
			title, _ := sushi.NewTextSnippet(detail)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint600)
			titleFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
			title.SetFont(titleFont)
			title.SetColor(titleColor)

			subtitleItem := sushi.SubtitleListItem{
				Title: title,
				Icon:  icon,
			}
			subtitleItems = append(subtitleItems, subtitleItem)
		}
		item.SubtitlesList = &subtitleItems

		items = append(items, item)
	}

	snippet := &sushi.V2ImageTextSnippetType49Snippet{
		Items: &items,
	}
	contentLayout := &sushi.LayoutConfig{
		SnippetType: sushi.V2ImageTextSnippetType49,
		LayoutType:  sushi.LayoutTypeCarousel,
	}
	if len(a.PageData.CourseCreators) == 1 {
		contentLayout.LayoutType = sushi.LayoutTypeGrid
	}
	section := &homeModels.ResultSection{
		LayoutConfig:             contentLayout,
		V2ImageTextSnippetType49: snippet,
	}
	a.AddSection(section)
}

func (a *AcademyHomePageTemplate) AddAcademyBenefitsSection(ctx context.Context) {
	if a.PageData.Benefit == nil || len(a.PageData.Benefit.BenefitDescription) == 0 {
		return
	}

	benefitDetails := a.PageData.Benefit
	headerLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.TextSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	headerTitle, _ := sushi.NewTextSnippet(benefitDetails.Title)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	headerTitle.SetFont(font)

	bgColorTint := sushi.ColorTint100
	if featureSupport.SupportsNewColor(ctx) {
		bgColorTint = sushi.ColorTint50
	}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)

	headerSnippet := &sushi.TextSnippetType1Snippet{
		Title:   headerTitle,
		BgColor: bgColor,
	}
	headerSection := &homeModels.ResultSection{
		LayoutConfig:     headerLayout,
		TextSnippetType1: headerSnippet,
	}
	a.AddSection(headerSection)

	contentLayout := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}

	items := make([]sushi.ImageTextSnippetType30SnippetItem, 0)
	for _, val := range benefitDetails.BenefitDescription {
		title, _ := sushi.NewTextSnippet(val.Title)
		color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
		font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		title.SetColor(color)
		title.SetFont(font)

		image, _ := sushi.NewImage(val.Image1)
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(24)

		item := sushi.ImageTextSnippetType30SnippetItem{
			Title: title,
			Image: image,
		}
		items = append(items, item)
	}

	contentSnippet := &sushi.ImageTextSnippetType30Snippet{
		Items:   &items,
		BgColor: bgColor,
	}
	contentSection := &homeModels.ResultSection{
		LayoutConfig:           contentLayout,
		ImageTextSnippetType30: contentSnippet,
	}
	a.AddSection(contentSection)
}

func (a *AcademyHomePageTemplate) AddCoursesSection(ctx context.Context) {
	if len(a.PageData.CoursesDetails) == 0 {
		return
	}

	isNewColorSupported := featureSupport.SupportsNewColor(ctx)

	impressionEname := &sushi.EnameData{
		Ename: "course_section_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	impressionTrackItem := GetClevertapTrackItem(ctx, nil, sectionEvents)

	if len(a.PageData.CoursesDetails) == 1 {
		course := a.PageData.CoursesDetails[0]
		if len(course.Categories) == 0 {
			return
		}

		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.ImageTextSnippetType33,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}
		items := make([]sushi.ImageTextSnippetType33SnippetItem, 0)

		leftImage, _ := sushi.NewImage(util.GetCDNLink(course.ImageUrl))
		leftImage.SetAspectRatio(1)
		leftImage.SetType(sushi.ImageTypeCircle)
		leftImage.SetHeight(48)

		title, _ := sushi.NewTextSnippet(course.ShortTitle)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize500)
		title.SetFont(font)

		subtitle, _ := sushi.NewTextSnippet("Explore Plan & Curriculum")
		subtitleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize200)
		subtitle.SetColor(subtitleColor)
		subtitle.SetFont(font)

		rightIconColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		rightIcon, _ := sushi.NewIcon(sushi.RightIcon, rightIconColor)

		deeplink := &sushi.Deeplink{
			URL: util.GetCourseCategoryDeeplink(course.CourseId, common.PAGE_TYPE_COURSE_CATEGORY, course.Categories[0].CourseCategoryId, ""),
		}
		clickAction := sushi.GetClickAction()
		clickAction.SetDeeplink(deeplink)

		courseImpression := &sushi.EnameData{
			Ename: "course_impression",
		}
		courseTap := &sushi.EnameData{
			Ename: "course_tap",
		}
		courseEvents := sushi.NewClevertapEvents()
		courseEvents.SetImpression(courseImpression)
		courseEvents.SetTap(courseTap)

		coursePayload := make(map[string]interface{})
		coursePayload["course_id"] = course.CourseId
		coursePayload["course_name"] = strings.ToLower(course.ShortTitle)

		trackItem := GetClevertapTrackItem(ctx, coursePayload, courseEvents)

		items = append(items, sushi.ImageTextSnippetType33SnippetItem{
			LeftImage:         leftImage,
			Title:             title,
			SubTitle1:         subtitle,
			ClickAction:       clickAction,
			RightIcon:         rightIcon,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		})
		bgColorTint := sushi.ColorTint100
		if isNewColorSupported {
			bgColorTint = sushi.ColorTint50
		}
		bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)
		snippet := &sushi.ImageTextSnippetType33Snippet{
			Items:             &items,
			BgColor:           bgColor,
			ClevertapTracking: []*sushi.ClevertapItem{impressionTrackItem},
		}
		section := &homeModels.ResultSection{
			LayoutConfig:           layoutConfig,
			ImageTextSnippetType33: snippet,
		}
		a.AddSection(section)
		return
	}

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.V2ImageTextSnippetType52,
		LayoutType:   sushi.LayoutTypeCarousel,
		VisibleCards: 2.2,
	}

	items := make([]*sushi.V2ImageTextType52SnippetItem, 0)
	for _, val := range a.PageData.CoursesDetails {
		image, _ := sushi.NewImage(util.GetCDNLink(val.ImageUrl))
		image.SetAspectRatio(1)
		image.SetType(sushi.ImageTypeCircle)
		image.SetHeight(80)
		image.SetWidth(80)

		title, _ := sushi.NewTextSnippet(val.ShortTitle)
		font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize400)
		title.SetFont(font)

		subtitle, _ := sushi.NewTextSnippet("Explore course")
		color, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
		font, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize100)
		icon, _ := sushi.NewIcon(sushi.RightIcon, color)
		subtitle.SetColor(color)
		subtitle.SetFont(font)
		subtitle.SetSuffixIcon(icon)

		payload := map[string]int32{
			"product_category_id": 12,
		}
		postbackParams, _ := json.Marshal(payload)
		deeplink := &sushi.Deeplink{
			URL:            util.GetAcademyCourseDeeplink(val.CourseId) + "?product_category_id=12",
			PostbackParams: string(postbackParams),
		}
		clickAction := sushi.GetClickAction()
		clickAction.SetDeeplink(deeplink)

		courseImpression := &sushi.EnameData{
			Ename: "course_impression",
		}
		courseTap := &sushi.EnameData{
			Ename: "course_tap",
		}
		courseEvents := sushi.NewClevertapEvents()
		courseEvents.SetImpression(courseImpression)
		courseEvents.SetTap(courseTap)

		coursePayload := make(map[string]interface{})
		coursePayload["course_id"] = val.CourseId
		coursePayload["course_name"] = strings.ToLower(val.ShortTitle)

		trackItem := GetClevertapTrackItem(ctx, coursePayload, courseEvents)

		item := &sushi.V2ImageTextType52SnippetItem{
			Title:             title,
			Subtitle:          subtitle,
			ClickAction:       clickAction,
			Image:             image,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		items = append(items, item)
	}

	bgColorTint := sushi.ColorTint100
	if isNewColorSupported {
		bgColorTint = sushi.ColorTint50
	}
	bgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, bgColorTint)

	snippet := &sushi.V2ImageTextType52Snippet{
		Items:             items,
		BgColor:           bgColor,
		ClevertapTracking: []*sushi.ClevertapItem{impressionTrackItem},
	}
	section := &homeModels.ResultSection{
		LayoutConfig:             layoutConfig,
		V2ImageTextSnippetType52: snippet,
	}
	a.AddSection(section)

	if len(a.PageData.CoursesDetails) > 1 {
		exploreLayout := &sushi.LayoutConfig{
			SnippetType:  sushi.ActionSnippetType2,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
		}

		courses := a.PageData.CoursesDetails
		payload := map[string]int32{
			"product_category_id": 12,
		}
		postbackParams, _ := json.Marshal(payload)
		deeplink := &sushi.Deeplink{
			URL:            util.GetAcademyCourseDeeplink(courses[0].CourseId) + "?product_category_id=12",
			PostbackParams: string(postbackParams),
		}
		clickAction := sushi.GetClickAction()
		clickAction.SetDeeplink(deeplink)

		button, _ := sushi.NewButton(sushi.ButtonTypeOutlined)
		button.SetText("Explore all courses")
		button.SetClickAction(clickAction)

		tapEname := &sushi.EnameData{
			Ename: "explore_all_courses_tap",
		}
		tapEvents := sushi.NewClevertapEvents()
		tapEvents.SetTap(tapEname)
		trackItem := GetClevertapTrackItem(ctx, nil, tapEvents)

		exporeSnippet := &sushi.ActionSnippetType2Snippet{
			Button:            button,
			BgColor:           bgColor,
			ClevertapTracking: []*sushi.ClevertapItem{trackItem},
		}
		exploreSection := &homeModels.ResultSection{
			ActionSnippetType2: exporeSnippet,
			LayoutConfig:       exploreLayout,
		}
		a.AddSection(exploreSection)
	}
}

func (a *AcademyHomePageTemplate) AddTestimonialsSection(ctx context.Context) {
	if len(a.PageData.Testimonials) == 0 {
		return
	}

	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	title, _ := sushi.NewTextSnippet("Hear from our customers")
	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title: title,
	}
	headerSection := &homeModels.ResultSection{
		LayoutConfig:       headerLayout,
		SectionHeaderType1: headerSnippet,
	}
	a.AddSection(headerSection)

	contentLayout := &sushi.LayoutConfig{
		SnippetType:      sushi.MediaSnippetType2,
		LayoutType:       sushi.LayoutTypeCarousel,
		SectionCount:     1,
		ShouldAutoScroll: true,
	}
	mediaItems := make([]*sushi.MediaType2SnippetItem, 0)
	for _, val := range a.PageData.Testimonials {
		image, _ := sushi.NewImage(util.GetCDNLink(val))
		image.SetAspectRatio(1.77)

		mediaContent := &sushi.MediaContent{
			MediaType: sushi.MEDIA_TYPE_IMAGE,
			Image:     image,
		}
		item := &sushi.MediaType2SnippetItem{
			MediaContent: mediaContent,
		}
		mediaItems = append(mediaItems, item)
	}

	impressionEname := &sushi.EnameData{
		Ename: "testimonials_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := GetClevertapTrackItem(ctx, nil, sectionEvents)

	contentSnippet := &sushi.MediaType2Snippet{
		Items:             mediaItems,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	contentSection := &homeModels.ResultSection{
		LayoutConfig:      contentLayout,
		MediaSnippetType2: contentSnippet,
	}
	a.AddSection(contentSection)
}

func (a *AcademyHomePageTemplate) AddNearbyFacilitiesSection(ctx context.Context) {
	response := a.GetNearbyFacilities(ctx)
	if response == nil {
		return
	}

	a.AddPostbackParam("previous_facility_ids", response.PreviousFacilityIds)

	headerLayout := &sushi.LayoutConfig{
		SnippetType: sushi.SectionHeaderType1,
	}
	color, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	title, _ := sushi.NewTextSnippet("Take a look at our sports centers")
	title.SetColor(color)
	title.SetFont(font)

	impressionEname := &sushi.EnameData{
		Ename: "academy_centers_impression",
	}
	sectionEvents := sushi.NewClevertapEvents()
	sectionEvents.SetImpression(impressionEname)
	trackItem := GetClevertapTrackItem(ctx, nil, sectionEvents)

	headerSnippet := &sushi.SectionHeaderType1Snippet{
		Title:             title,
		ClevertapTracking: []*sushi.ClevertapItem{trackItem},
	}
	headerSection := &homeModels.ResultSection{
		LayoutConfig:       headerLayout,
		SectionHeaderType1: headerSnippet,
	}
	a.AddSection(headerSection)

	for _, val := range response.Facilities {
		facilityLayout := &sushi.LayoutConfig{
			SnippetType: sushi.ResSnippetType3,
		}
		facilitySnippet := a.GetFacilitySnippet(ctx, val)
		section := &homeModels.ResultSection{
			LayoutConfig:    facilityLayout,
			ResType3Snippet: facilitySnippet,
		}
		a.AddSection(section)
	}
}

func (a *AcademyHomePageTemplate) GetFacilitySnippet(ctx context.Context, facility *facilitySportPB.Facility) *sushi.ResType3Snippet {
	isNewColorSupported := featureSupport.SupportsNewColor(ctx)
	snippet := &sushi.ResType3Snippet{}

	title, _ := sushi.NewTextSnippet(facility.DisplayName)
	snippet.Title = title

	sportNames := []string{}
	for _, sport := range facility.Sports {
		sportNames = append(sportNames, sport.SportName)
	}
	if len(sportNames) > 0 {
		subtitle1, _ := sushi.NewTextSnippet(strings.Join(sportNames, ", "))
		snippet.Subtitle1 = subtitle1
	}

	image, _ := sushi.NewImage(facility.DisplayPicture)
	snippet.Image = image

	if facility.Tag != common.UPCOMING_FACILITY_SPORT_TAG {
		clickAction := sushi.GetClickAction()
		deeplink := sushi.Deeplink{
			URL: facility.Deeplink,
		}
		clickAction.SetDeeplink(&deeplink)
		snippet.ClickAction = clickAction
	}

	ratingSnippetBlockItem := &sushi.RatingSnippetBlockItem{}

	tags := []string{common.NEW_FACILITY_SPORT_TAG, common.OPENING_FACILITY_SPORT_TAG, common.ONHOLD_FACILITY_SPORT_TAG}
	if sharedFunc.ContainsString(tags, facility.Tag) {
		ratingTitle, _ := sushi.NewTextSnippet(facility.Tag)

		if !isNewColorSupported {
			color := sushi.ColorTypeBlue
			if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeTeal
			} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				color = sushi.ColorTypeOrange
			}
			tagBgColor, _ := sushi.NewColor(color, sushi.ColorTint500)
			ratingSnippetBlockItem.BgColor = tagBgColor
		} else {
			if facility.Tag == common.OPENING_FACILITY_SPORT_TAG {
				tagBgColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint400)
				ratingSnippetBlockItem.BgColor = tagBgColor
			} else if facility.Tag == common.ONHOLD_FACILITY_SPORT_TAG {
				tagBgColor, _ := sushi.NewColor(sushi.ALERT_LIGHT_THEME, sushi.ColorTint500)
				ratingSnippetBlockItem.BgColor = tagBgColor
			} else {
				// gradient for new tag
				tagColor1, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint500)
				tagColor2, _ := sushi.NewColor(sushi.ColorTypeYellow, sushi.ColorTint500)
				gradient, _ := sushi.NewGradient([]sushi.Color{*tagColor1, *tagColor2})
				ratingSnippetBlockItem.Gradient = gradient
			}
		}

		ratingSnippetBlockItem.Title = ratingTitle
		ratingSnippetBlockItem.Size = sushi.RatingSize300

	} else {
		ratingTitle, _ := sushi.NewTextSnippet(facility.Rating)
		ratingBgColor, _ := sushi.NewColor(sushi.ColorTypeTeal, sushi.ColorTint500)
		if isNewColorSupported {
			ratingBgColor, _ = sushi.NewColor(sushi.ColorTypeCyan, sushi.ColorTint700)
		}

		ratingIconColor, _ := sushi.NewColor(sushi.ColorTypeWhite, sushi.ColorTint500)
		ratingIcon, _ := sushi.NewIcon(sushi.RatingIconCode, ratingIconColor)

		ratingSnippetBlockItem.Title = ratingTitle
		ratingSnippetBlockItem.BgColor = ratingBgColor
		ratingSnippetBlockItem.RightIcon = ratingIcon
	}
	ratingSnippet := sushi.RatingSnippet{
		Type:  sushi.RatingTypeTagV2,
		TagV2: ratingSnippetBlockItem,
	}
	snippet.RatingSnippets = &([]sushi.RatingSnippet{ratingSnippet})

	var tagTitle *sushi.TextSnippet
	_, div := math.Modf(facility.Distance)
	if (div >= 0.95 || div < 0.05) && div != 0 {
		tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.f km", facility.Distance))
	} else {
		tagTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("%.1f km", facility.Distance))
	}
	tagColor, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	tagTitle.SetColor(tagColor)

	topRightTag := &sushi.Tag{
		Title:        tagTitle,
		Size:         sushi.TagSizeMedium,
		Transparency: 0.2,
	}
	snippet.TopRightTag = topRightTag

	multiTagItems := make([]sushi.MultiTagSnippetItem, 0)
	for _, sport := range facility.Sports {
		for _, attribute := range sport.FsInfo {
			tagTitle, _ := sushi.NewTextSnippet(attribute)
			if !isNewColorSupported {
				multiTagSnippetItemColor1, _ := sushi.NewColor(sushi.ColorTypeZRed, sushi.ColorTint500)
				multiTagSnippetItemColor2, _ := sushi.NewColor(sushi.ColorTypePink, sushi.ColorTint600)
				gradient, _ := sushi.NewGradient([]sushi.Color{*multiTagSnippetItemColor1, *multiTagSnippetItemColor2})

				multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
					Title:    tagTitle,
					Gradient: gradient,
				})
			} else {
				multiTagSnippetItemColor, _ := sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)

				multiTagItems = append(multiTagItems, sushi.MultiTagSnippetItem{
					Title:   tagTitle,
					BgColor: multiTagSnippetItemColor,
				})
			}
		}
	}
	if len(multiTagItems) > 0 {
		snippet.TopTags = &multiTagItems
	}

	trackPayload := make(map[string]interface{})
	trackPayload["facility_id"] = facility.FacilityId
	tapEname := &sushi.EnameData{
		Ename: "academy_facility_card_tap",
	}
	facilityCardEvents := sushi.NewClevertapEvents()
	facilityCardEvents.SetTap(tapEname)
	trackItem := GetClevertapTrackItem(ctx, trackPayload, facilityCardEvents)
	snippet.ClevertapTracking = []*sushi.ClevertapItem{trackItem}

	return snippet
}

func (a *AcademyHomePageTemplate) SetPageTracking(ctx context.Context) {
	landingEname := &sushi.EnameData{
		Ename: "academy_home_page_landing",
	}
	pageEvents := sushi.NewClevertapEvents()
	pageEvents.SetPageSuccess(landingEname)
	trackItem := GetClevertapTrackItem(ctx, nil, pageEvents)

	a.ClevertapTracking = []*sushi.ClevertapItem{trackItem}
}

func GetClevertapTrackItem(
	ctx context.Context,
	trackPayload map[string]interface{},
	trackEvents *sushi.EventNames,
) *sushi.ClevertapItem {
	if trackPayload == nil {
		trackPayload = make(map[string]interface{})
	}
	trackPayload["user_id"] = util.GetUserIDFromContext(ctx)
	trackPayload["city_id"] = util.GetCityIDFromContext(ctx)
	trackPayload["source"] = "home"

	return sushi.GetClevertapTrackItem(ctx, trackPayload, trackEvents)
}

func (a *AcademyHomePageTemplate) GetMaxSafetyBottomSheetClickAction(ctx context.Context) *sushi.ClickAction {
	facilityClient := util.GetFacilitySportClient()
	maxSafetyMeasuresMap, err := facilityClient.GetMaxSafetyMeasures(ctx, &facilitySportPB.GetMaxSafetyMeasuresRequest{
		SportIds:          a.PageData.SportIds,
		ProductCategoryId: common.AcademyCategoryID,
	})
	if err != nil || len(maxSafetyMeasuresMap.SportSafetyMap) == 0 {
		log.Printf("Error in AddSuggestionsSection while getting max safety measures for sport_ids: %v, err: %v", a.PageData.SportIds, err)
		return nil
	}

	safetyMeasureIds := make([]int32, 0)
	safetyItems := make([]sushi.ImageTextSnippetType30SnippetItem, 0)

	for _, sportSafetyMeasures := range maxSafetyMeasuresMap.SportSafetyMap {
		for _, safetyMeasure := range sportSafetyMeasures.BenefitDescription {
			if util.Int32InSlice(safetyMeasure.Id, safetyMeasureIds) {
				continue
			}
			safetyMeasureIds = append(safetyMeasureIds, safetyMeasure.Id)
			image, _ := sushi.NewImage(safetyMeasure.Image1)
			image.SetType(sushi.ImageTypeCircle)
			image.SetAspectRatio(1)
			image.SetHeight(24)

			title, _ := sushi.NewTextSnippet(safetyMeasure.Title)
			titleColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
			titleFont, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
			title.SetColor(titleColor)
			title.SetFont(titleFont)

			safetyItems = append(safetyItems, sushi.ImageTextSnippetType30SnippetItem{
				Image: image,
				Title: title,
			})
		}
	}

	headerImage, _ := sushi.NewImage("https://b.zmtcdn.com/data/o2_assets/d7f1ac5349616e3e90ef967bec69b8721612171855.png")
	headerImage.SetHeight(22)
	headerImage.SetWidth(50)
	headerImage.SetType(sushi.ImageTypeRectangle)
	headerTitle, _ := sushi.NewTextSnippet("Max Safety measures")

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType30,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
		ShouldResize: true,
	}
	snippet := &sushi.ImageTextSnippetType30Snippet{
		Items: &safetyItems,
	}
	genericBottomSheet := &sushi.OpenGenericBottomSheet{
		Header: &sushi.GenericBottomSheetHeader{
			Image: headerImage,
			Title: headerTitle,
		},
		Items: []*sushi.CustomTextSnippetTypeLayout{
			&sushi.CustomTextSnippetTypeLayout{
				LayoutConfig:           layoutConfig,
				ImageTextSnippetType30: snippet,
			},
		},
	}

	clickAction := sushi.GetClickAction()
	clickAction.SetGenericBottomSheet(genericBottomSheet)
	return clickAction
}

func (a *AcademyHomePageTemplate) GetWhatToExpectBottomSheet(ctx context.Context) *sushi.ClickAction {
	items := make([]*sushi.CustomTextSnippetTypeLayout, 0)

	for _, v := range common.ACADEMY_WHAT_TO_EXPECT_POINTS {
		layoutConfig := &sushi.LayoutConfig{
			SnippetType:  sushi.TextSnippetType1,
			LayoutType:   sushi.LayoutTypeGrid,
			SectionCount: 1,
			ShouldResize: true,
		}

		title, _ := sushi.NewTextSnippet(v)
		color900Grey, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint900)
		fontReg300, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
		title.SetColor(color900Grey)
		title.SetFont(fontReg300)
		title.SetIsMarkdown(1)

		snippet := &sushi.TextSnippetType1Snippet{
			Title: title,
		}

		item := &sushi.CustomTextSnippetTypeLayout{
			LayoutConfig:            layoutConfig,
			TextSnippetType1Snippet: snippet,
		}
		items = append(items, item)
	}

	headerTitle, _ := sushi.NewTextSnippet("What to expect at your free trial class")
	header := &sushi.GenericBottomSheetHeader{
		Title: headerTitle,
	}

	bottomButton := GetTrialButton(ctx, "Book a free trial class", "academy_trial_sport_select", a.PageData.SportIds, 0)
	bottomSheet := &sushi.OpenGenericBottomSheet{
		Header:       header,
		Items:        items,
		BottomButton: bottomButton,
	}
	clickAction := sushi.GetClickAction()
	clickAction.SetGenericBottomSheet(bottomSheet)
	return clickAction
}

func GetBookATrialClickAction(ctx context.Context, postAction string, courseSportIds []int32, courseId int32) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()
	if util.GetUserIDFromContext(ctx) == 0 {
		authTitle, _ := sushi.NewTextSnippet("Please login to book your free trial class. Enter your phone number to login using OTP.")
		payload := map[string]interface{}{
			"post_action": postAction,
			"sport_ids":   courseSportIds,
			"course_id":   courseId,
		}
		postbackParams, _ := json.Marshal(payload)
		auth := &sushi.Auth{
			Title:          authTitle,
			PostbackParams: string(postbackParams),
			Source:         "academy_banner",
		}
		clickAction.SetAuth(auth)
	} else {
		clickAction = GetTrialBookClickAction(ctx, courseSportIds, courseId)
	}
	return clickAction
}

func GetAcademyFooterCTA(ctx context.Context, postAction string, courseSportIds []int32, courseId int32) []sushi.FooterSnippetType2ButtonItem {
	/*
		1. If user or any of his children have purchased academy membership, do not show any CTA
		2. If user or any of his children have taken at least 1 trial, make membership as primary CTA and trial as secondary
		3. If none of above is true, make trial as primary CTA and membership as secondary
	*/
	items := []sushi.FooterSnippetType2ButtonItem{}
	loggedInUserId := util.GetUserIDFromContext(ctx)
	productClient := util.GetProductClient()
	userClient := util.GetUserServiceClient()

	if !featureSupport.SupportsAcademyPurchaseFlow(ctx) {
		trialButton := GetTrialButton(ctx, "Book a free trial class", postAction, courseSportIds, courseId)
		if trialButton != nil {
			items = append(items, *trialButton)
		}
		return items
	}

	if loggedInUserId == 0 {
		return GetDefaultFooterCTAItems(ctx, postAction, courseSportIds, courseId)
	}

	academyMembershipStatus, err := productClient.GetUserAcademySubscriptionStatus(ctx, &productPB.GetUserSubscriptionStatusRequest{
		UserId: loggedInUserId,
	})
	if err != nil {
		log.Printf("not able to get user subscription for userID: %d, err: %v", loggedInUserId, err)
	} else {
		if academyMembershipStatus.SubscriptionStatus != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE &&
			academyMembershipStatus.SubscriptionStatus != productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
			childUserIds := make([]int32, 0)
			childUsersResponse, err := userClient.GetChildUsers(
				ctx,
				&userPB.UserInfo{
					UserId: loggedInUserId,
				},
			)
			if err != nil {
				log.Printf(
					"Error in SetFooter while getting children for userId: %d, Error: %v",
					loggedInUserId,
					err,
				)
			} else {
				if childUsersResponse.Status.Status == common.SUCCESS && len(childUsersResponse.AllConnectedUsers) > 0 {
					atLeastOneMembership := false
					for _, child := range childUsersResponse.AllConnectedUsers {
						childUserIds = append(childUserIds, child.UserId)
						academyMembership, err := productClient.GetUserAcademySubscriptionStatus(ctx, &productPB.GetUserSubscriptionStatusRequest{
							UserId: child.UserId,
						})
						if err != nil {
							log.Printf("not able to get user subscription for userID: %d, err: %v", child.UserId, err)
						} else {
							if academyMembership.SubscriptionStatus != productPB.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE &&
								academyMembership.SubscriptionStatus != productPB.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {
								continue
							} else {
								atLeastOneMembership = true
								break
							}
						}
					}
					if !atLeastOneMembership {
						userIds := make([]int32, 0)
						userIds = append(userIds, loggedInUserId)
						userIds = append(userIds, childUserIds...)
						items = GetFooterCTAItemsForTrialTaken(ctx, userIds, postAction, courseSportIds, courseId)
						if len(items) == 0 {
							items = GetDefaultFooterCTAItems(ctx, postAction, courseSportIds, courseId)
						}
					}
				} else {
					// when there are no children, just check for logged in user's trial status
					items = GetFooterCTAItemsForTrialTaken(ctx, []int32{loggedInUserId}, postAction, courseSportIds, courseId)
				}
			}
		}
	}
	return items
}

func GetFooterCTAItemsForTrialTaken(ctx context.Context, userIds []int32, postAction string, courseSportIds []int32, courseId int32) []sushi.FooterSnippetType2ButtonItem {
	items := []sushi.FooterSnippetType2ButtonItem{}
	bookingClient := util.GetBookingClient()
	loggedInUserId := util.GetUserIDFromContext(ctx)
	academyTrials, err := bookingClient.GetUserTrialsWithinCityByProductCategory(ctx, &bookingPB.TrialsStatusRequest{
		UserIds:           userIds,
		ProductCategoryId: common.AcademyCategoryID,
	})
	if err != nil {
		log.Printf("not able to get academy trials for userIds: %v, err: %v", userIds, err)
	} else {
		atLeastOneTrial := false
		for _, sportTrials := range academyTrials.UserTrials {
			for _, taken := range sportTrials.SportTrial {
				if taken {
					atLeastOneTrial = true
					membershipButton := sushi.FooterSnippetType2ButtonItem{
						Type: sushi.FooterButtonTypeText,
						Text: "Become Academy member",
					}
					membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
					urlDeeplink := util.GetPurchaseMembershipDeeplink(common.AcademyCategoryID)
					if loggedInUserId == 688673 || loggedInUserId == 5506739 {
						urlDeeplink = util.GetPurchaseMembershipDeeplink(common.SummerCampCategoryID)
					}
					membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
						URL: urlDeeplink,
					})
					membershipButton.ClickAction = membershipButtonClickAction
					trialButton := GetTrialButton(ctx, "book a free trial class", postAction, courseSportIds, courseId)
					if trialButton != nil {
						items = append(items, *trialButton)
					}
					items = append(items, membershipButton)
					break
				}
			}
			if atLeastOneTrial {
				break
			}
		}
	}
	return items
}

func GetDefaultFooterCTAItems(ctx context.Context, postAction string, courseSportIds []int32, courseId int32) []sushi.FooterSnippetType2ButtonItem {
	items := []sushi.FooterSnippetType2ButtonItem{}
	trialButton := GetTrialButton(ctx, "Book a free trial class", postAction, courseSportIds, courseId)
	if trialButton != nil {
		items = append(items, *trialButton)
	}
	membershipButton := sushi.FooterSnippetType2ButtonItem{
		Type: sushi.FooterButtonTypeText,
		Text: "or become Academy member",
	}
	membershipButtonClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
	urlDeeplink := util.GetPurchaseMembershipDeeplink(common.AcademyCategoryID)
	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 688673 || loggedInUserId == 5506739 {
		urlDeeplink = util.GetPurchaseMembershipDeeplink(common.SummerCampCategoryID)
	}
	membershipButtonClickAction.SetDeeplink(&sushi.Deeplink{
		URL: urlDeeplink,
	})
	membershipButton.ClickAction = membershipButtonClickAction
	items = append(items, membershipButton)
	return items
}
