package homeController

import (
	"fmt"
	"log"
	"net/http"

	"bitbucket.org/jogocoin/go_api/api/common"

	homeModels "bitbucket.org/jogocoin/go_api/api/models/home"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	bookingPB "bitbucket.org/jogocoin/go_api/api/proto/booking"
	purchasePB "bitbucket.org/jogocoin/go_api/api/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
)

const pendingPaymentStatus = 4

func GetAerobarC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	bookingClient := util.GetBookingClient()
	purchaseClient := util.GetPurchaseClient()
	userClient := util.GetUserServiceClient()
	var data []*homeModels.Aerobar

	purchaseAerobar, err := purchaseClient.GetAerobar(ctx, &purchasePB.Empty{})
	if err != nil {
		log.Printf("Error while getting purchase aerobars, Error: %v", err)
	} else {
		var aerobars []*structs.Aerobar
		for _, aerobar := range purchaseAerobar.Aerobars {
			var bottomSheet bool
			if aerobar.PurchaseClickAction != nil {
				bottomSheet = true
			}
			aerobars = append(aerobars, &structs.Aerobar{
				AerobarId:       aerobar.AerobarId,
				Title:           aerobar.Title,
				Subtitle:        aerobar.Subtitle,
				Image:           aerobar.Image,
				Deeplink:        aerobar.Deeplink,
				PostbackParams:  aerobar.PostbackParams,
				RightIcon:       aerobar.RightIcon,
				RightButtonText: aerobar.RightButtonText,
				Type:            aerobar.Type,
				BottomSheet:     bottomSheet,
				WebDeeplink:     aerobar.WebDeeplink,
			})
		}
		data = append(data, GetTemplatizedAerobars(aerobars, purchaseAerobar)...)
	}

	bookingsAerobar, err := bookingClient.GetAerobar(ctx, &bookingPB.Empty{})
	if err != nil {
		log.Printf("Error while getting bookings aerobars, Error: %v", err)
	} else {
		var aerobars []*structs.Aerobar
		for _, aerobar := range bookingsAerobar.Aerobars {
			aerobarToAppend := &structs.Aerobar{
				AerobarId:              aerobar.AerobarId,
				Title:                  aerobar.Title,
				Subtitle:               aerobar.Subtitle,
				Image:                  aerobar.Image,
				LottieUrl:              aerobar.LottieUrl,
				Deeplink:               aerobar.Deeplink,
				RightIcon:              aerobar.RightIcon,
				RightButtonText:        aerobar.RightButtonText,
				Type:                   aerobar.Type,
				IsRatingSnippetVisible: aerobar.IsRatingSnippetVisible,
			}

			aerobars = append(aerobars, aerobarToAppend)
		}
		data = append(data, GetTemplatizedAerobars(aerobars, nil)...)
	}

	userAerobar, err := userClient.GetAerobar(ctx, &userPB.Empty{})
	if err != nil {
		log.Printf("Error while getting users aerobars, Error: %v", err)
	} else {
		var aerobars []*structs.Aerobar
		for _, aerobar := range userAerobar.Aerobars {
			aerobarToAppend := &structs.Aerobar{
				AerobarId:       aerobar.AerobarId,
				Title:           aerobar.Title,
				Subtitle:        aerobar.Subtitle,
				Deeplink:        aerobar.Deeplink,
				RightIcon:       aerobar.RightIcon,
				RightButtonText: aerobar.RightButtonText,
				Type:            aerobar.Type,
				Image:           aerobar.Image,
			}

			aerobars = append(aerobars, aerobarToAppend)
		}
		data = append(data, GetTemplatizedAerobars(aerobars, nil)...)
	}
	aerobarResponse := homeModels.AerobarResponse{
		Status: common.SUCCESS,
		Data:   data,
	}
	c.JSON(http.StatusOK, aerobarResponse)
}

func GetTemplatizedAerobars(serviceAerobars []*structs.Aerobar, bottomSheetDetails *purchasePB.GetAerobarResponse) []*homeModels.Aerobar {
	var aerobars []*homeModels.Aerobar
	for i, serviceAerobar := range serviceAerobars {
		title, _ := sushi.NewTextSnippet(serviceAerobar.Title)
		subtitle, _ := sushi.NewTextSnippet(serviceAerobar.Subtitle)
		aerobar := &homeModels.Aerobar{
			AerobarId: serviceAerobar.AerobarId,
			Title:     title,
			Subtitle:  subtitle,
			Type:      serviceAerobar.Type,
		}
		if serviceAerobar.Image != "" && serviceAerobar.LottieUrl == "" {
			image, _ := sushi.NewImage(serviceAerobar.Image)
			aerobar.Image = image
		}
		if serviceAerobar.LottieUrl != "" { // if image is not set but lottie url is there
			aerobar.LottieUrl = serviceAerobar.LottieUrl
		}
		if serviceAerobar.RightIcon {
			rightIcon, _ := sushi.NewIcon(sushi.CrossCircleFillIcon, nil)
			rightIconClickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDismissAerobar)
			rightIcon.SetClickAction(rightIconClickAction)
			aerobar.RightIcon = rightIcon
		}
		if serviceAerobar.RightButtonText != "" {
			rightButton, _ := sushi.NewTextSnippet(serviceAerobar.RightButtonText)
			aerobar.RightButton = rightButton
		}
		if serviceAerobar.Deeplink != "" && !serviceAerobar.WebDeeplink {
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			deeplink := &sushi.Deeplink{
				URL: serviceAerobar.Deeplink,
			}
			if serviceAerobar.PostbackParams != "" {
				deeplink.PostbackParams = serviceAerobar.PostbackParams
			}
			clickAction.SetDeeplink(deeplink)
			aerobar.ClickAction = clickAction
		}
		if serviceAerobar.BottomSheet {
			clickAction := SetPurchaseBottomSheetAction(bottomSheetDetails.Aerobars[i].PurchaseClickAction)
			aerobar.ClickAction = clickAction
		}
		if serviceAerobar.WebDeeplink {
			clickAction, _ := sushi.NewTextClickAction(sushi.ClickActionDeeplink)
			openWebView := &sushi.OpenWebview{
				URL:   serviceAerobar.Deeplink,
				InApp: false,
			}
			clickAction.SetOpenWebview(openWebView)
			aerobar.ClickAction = clickAction
		}
		if serviceAerobar.IsRatingSnippetVisible {
			aerobar.IsRatingSnippetVisible = true
		}
		aerobars = append(aerobars, aerobar)
	}
	return aerobars
}

func SetPurchaseBottomSheetAction(bottomSheetData *purchasePB.AerobarPurchaseData) *sushi.ClickAction {
	clickAction := sushi.GetClickAction()

	layoutConfig := &sushi.LayoutConfig{
		SnippetType:  sushi.ImageTextSnippetType32,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}

	//Payment Status Heading
	items := make([]sushi.ImageTextSnippetType32SnippetItem, 0)
	item := sushi.ImageTextSnippetType32SnippetItem{}

	image, _ := sushi.NewImage(bottomSheetData.Image)
	image.SetHeight(16)
	image.SetWidth(16)
	image.SetType(sushi.ImageTypeCircle)
	image.SetAspectRatio(float32(1))
	item.Image = image

	title, _ := sushi.NewTextSnippet(bottomSheetData.StatusTitle)
	font, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize600)
	title.SetFont(font)
	item.Title = title

	subtitle, _ := sushi.NewTextSnippet(bottomSheetData.StatusSubtitle)
	fontSubtitle, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	colorSubtitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	subtitle.SetFont(fontSubtitle)
	subtitle.SetColor(colorSubtitle)
	item.Subtitle1 = subtitle

	items = append(items, item)
	bgColor, _ := sushi.NewColor(sushi.ColorTypeRed, sushi.ColorTint100)
	if bottomSheetData.PaymentStatus == pendingPaymentStatus {
		bgColor, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint100)
	}

	pointer_item1 := &sushi.ImageTextSnippetType32Snippet{
		Items:   &items,
		BgColor: bgColor,
	}
	paymentStatus := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:           layoutConfig,
		ImageTextSnippetType32: pointer_item1,
	}

	//Product Name and City
	title1, _ := sushi.NewTextSnippet(bottomSheetData.ProductName)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize400)
	title1.SetFont(font)
	title1.SetKerning(3)
	font, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize100)
	tagBgColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint300)
	tagTextColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	tag, _ := sushi.NewTag(bottomSheetData.CityName)
	tag.Title.SetColor(tagTextColor)
	tag.Title.SetFont(font)
	tag.SetBgColor(tagBgColor)
	pointer_item8 := &sushi.FitsoTextSnippetType5Item{
		Title: title1,
		Tag:   tag,
	}
	fitso_text_snippet_type_5_items := make([]*sushi.FitsoTextSnippetType5Item, 0)
	fitso_text_snippet_type_5_items = append(fitso_text_snippet_type_5_items, pointer_item8)
	pointer_item2 := &sushi.FitsoTextType5{
		Items: fitso_text_snippet_type_5_items,
	}

	layoutConfig = &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType5,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	productDetails := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType5: pointer_item2,
	}

	//User names for purchase
	title, _ = sushi.NewTextSnippet(bottomSheetData.NamesOfUsers)
	fontTitle, _ := sushi.NewFont(sushi.FontSemiBold, sushi.FontSize300)
	title.SetFont(fontTitle)
	pointer_item3 := &sushi.TextSnippetType1Snippet{
		Title: title,
	}

	layoutConfig = &sushi.LayoutConfig{
		SnippetType:  sushi.TextSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	namesOfUsers := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:            layoutConfig,
		TextSnippetType1Snippet: pointer_item3,
	}

	//Transaction by
	title, _ = sushi.NewTextSnippet("Transaction done by")
	fontTitle, _ = sushi.NewFont(sushi.FontMedium, sushi.FontSize300)
	colorTitle, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint800)
	title.SetFont(fontTitle)
	title.SetColor(colorTitle)

	subTitle, _ := sushi.NewTextSnippet(bottomSheetData.ParentUser)
	subTitle.SetFont(fontTitle)
	subTitle.SetColor(colorTitle)

	pointerItem := &sushi.FitsoTextSnippetType4Item{
		Title:    title,
		Subtitle: subTitle,
	}

	fitso_text_snippet_type_4_item := make([]*sushi.FitsoTextSnippetType4Item, 0)
	fitso_text_snippet_type_4_item = append(fitso_text_snippet_type_4_item, pointerItem)
	pointer_item4 := &sushi.FitsoTextType4{
		Items: fitso_text_snippet_type_4_item,
	}
	layoutConfig = &sushi.LayoutConfig{
		SnippetType:  sushi.FitsoTextSnippetType4,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	PurchaseBy := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType4: pointer_item4,
	}

	//Transaction On date
	title, _ = sushi.NewTextSnippet("Transaction started on ")
	title.SetFont(fontTitle)
	title.SetColor(colorTitle)
	subTitle, _ = sushi.NewTextSnippet(bottomSheetData.CreatedAt)
	subTitle.SetFont(fontTitle)
	subTitle.SetColor(colorTitle)

	pointerItem1 := &sushi.FitsoTextSnippetType4Item{
		Title:    title,
		Subtitle: subTitle,
	}
	fitso_text_snippet_type_4_item1 := make([]*sushi.FitsoTextSnippetType4Item, 0)
	fitso_text_snippet_type_4_item1 = append(fitso_text_snippet_type_4_item1, pointerItem1)
	pointer_item5 := &sushi.FitsoTextType4{
		Items: fitso_text_snippet_type_4_item1,
	}
	PurchasedOn := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType4: pointer_item5,
	}

	//Amount paid
	title, _ = sushi.NewTextSnippet("Payment amount")
	title.SetFont(fontTitle)
	title.SetColor(colorTitle)
	subTitle, _ = sushi.NewTextSnippet(fmt.Sprintf("₹%.f", bottomSheetData.AmountPaid))
	subTitle.SetFont(fontTitle)
	subTitle.SetColor(colorTitle)

	pointerItem2 := &sushi.FitsoTextSnippetType4Item{
		Title:    title,
		Subtitle: subTitle,
	}
	if len(bottomSheetData.PaymentMethod) > 0 {
		subTitle1, _ := sushi.NewTextSnippet(bottomSheetData.PaymentMethod)
		subFont, _ := sushi.NewFont(sushi.FontRegular, sushi.FontSize200)
		subColor, _ := sushi.NewColor(sushi.ColorTypeGrey, sushi.ColorTint600)
		subTitle1.SetFont(subFont)
		subTitle1.SetColor(subColor)
		pointerItem2.Subtitle1 = subTitle1
	}
	fitso_text_snippet_type_4_item2 := make([]*sushi.FitsoTextSnippetType4Item, 0)
	fitso_text_snippet_type_4_item2 = append(fitso_text_snippet_type_4_item2, pointerItem2)
	pointer_item6 := &sushi.FitsoTextType4{
		Items: fitso_text_snippet_type_4_item2,
	}
	PurchaseDetails := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:          layoutConfig,
		FitsoTextSnippetType4: pointer_item6,
	}

	//Bottom text
	title, _ = sushi.NewTextSnippet(bottomSheetData.BottomText)
	fontTitle, _ = sushi.NewFont(sushi.FontRegular, sushi.FontSize300)
	colorTitle, _ = sushi.NewColor(sushi.ColorTypeBlue, sushi.ColorTint500)
	title.SetFont(fontTitle)
	title.SetColor(colorTitle)
	if bottomSheetData.PaymentStatus == pendingPaymentStatus {
		title.SetIsMarkdown(1)
	}
	pointer_item7 := &sushi.TextSnippetType1Snippet{
		Title: title,
	}

	layoutConfig = &sushi.LayoutConfig{
		SnippetType:  sushi.TextSnippetType1,
		LayoutType:   sushi.LayoutTypeGrid,
		SectionCount: 1,
	}
	bottomText := &sushi.V2ImageTextSnippetType36Layout{
		LayoutConfig:            layoutConfig,
		TextSnippetType1Snippet: pointer_item7,
	}

	openBottomSheet := &sushi.OpenPaymentInprogressBottomSheet{
		Items: []*sushi.V2ImageTextSnippetType36Layout{paymentStatus, productDetails, namesOfUsers, PurchaseBy, PurchasedOn, PurchaseDetails, bottomText},
	}
	clickAction.SetPaymentBottomSheet(openBottomSheet)
	return clickAction
}
