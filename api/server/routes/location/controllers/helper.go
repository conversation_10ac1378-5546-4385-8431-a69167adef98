package locationController

import (
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
)

// GetProductClient returns product client
func GetProductClient() productPB.ProductService {

	var serviceList structs.ServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)

	service := micro.NewService()

	return productPB.NewProductService(serviceList.Product, service.Client())
}
