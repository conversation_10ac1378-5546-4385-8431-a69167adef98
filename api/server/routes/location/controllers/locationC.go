package locationController

import (
	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models"
	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"strconv"
)

func GetLocationSuggestionC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)
	cityId := util.GetCityIDFromContext(ctx)

	var json structs.GetLocationSuggestion
	if text, ok := c.GetQuery("text"); ok {
		json.Text = text
	}

	if len(json.Text) <= 2 {
		log.Printf("Error in GetLocationSuggestionC - Text length is not valid %s, city_id %d", json.Text, cityId)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}

	productClient := util.GetProductClient()
	locationSuggestionResponse, err := productClient.GetLocationSuggestions(ctx, &productPB.GetLocationSuggestionRequest{
		Text:   json.Text,
		CityId: cityId,
	})

	if err != nil {
		log.Printf("GetLocationSuggestionC - error in getting suggestion %s, city %d, error: %v", json.Text, cityId, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.INTERNAL_SERVER_ERROR))
		return
	}
	if len(locationSuggestionResponse.Status) > 0 && locationSuggestionResponse.Status == apiCommon.BAD_REQUEST {
		log.Printf("GetLocationSuggestionC - error in getting suggestion %s, city %d, error: %v", json.Text, cityId, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}
	c.JSON(http.StatusOK, locationSuggestionResponse)
}

func GetLocationDetailsC(c *gin.Context) {
	ctx := util.PrepareGRPCMetaData(c)

	var json structs.GetLocationDetails
	if lat, ok := c.GetQuery("lat"); ok {
		if s, err := strconv.ParseFloat(lat, 32); err == nil {
			json.Lat = float32(s)
		}
	}

	if long, ok := c.GetQuery("long"); ok {
		if s, err := strconv.ParseFloat(long, 32); err == nil {
			json.Long = float32(s)
		}
	}

	if placeId, ok := c.GetQuery("place_id"); ok {
		json.PlaceId = placeId
	}

	if (json.Lat == 0 || json.Long == 0) && len(json.PlaceId) == 0 {
		log.Printf("Error in GetLocationDetailsC - Request is not valid %v", json)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}
	if displayTitle, ok := c.GetQuery("display_title"); ok {
		json.DisplayTitle = displayTitle
	}
	if displaySubtitle, ok := c.GetQuery("display_subtitle"); ok {
		json.DisplaySubtitle = displaySubtitle
	}

	productClient := util.GetProductClient()
	locationDetailsResponse, err := productClient.GetLocationDetails(ctx, &productPB.GetLocationDetailsRequest{
		Lat:             json.Lat,
		Long:            json.Long,
		PlaceId:         json.PlaceId,
		DisplayTitle:    json.DisplayTitle,
		DisplaySubtitle: json.DisplaySubtitle,
	})

	if err != nil {
		log.Printf("GetLocationDetailsC - error in getting details %v, error: %v", json, err)
		c.AbortWithStatusJSON(http.StatusInternalServerError, models.StatusFailure(apiCommon.INTERNAL_SERVER_ERROR))
		return
	}
	if len(locationDetailsResponse.Status) > 0 && locationDetailsResponse.Status == apiCommon.BAD_REQUEST {
		log.Printf("GetLocationDetailsC - error in getting details %v, error: %v", json, err)
		c.AbortWithStatusJSON(http.StatusBadRequest, models.StatusFailure(apiCommon.BAD_REQUEST))
		return
	}
	c.JSON(http.StatusOK, locationDetailsResponse)
}
