package location

import (
	locationController "bitbucket.org/jogocoin/go_api/api/server/routes/location/controllers"
	//locationResource "bitbucket.org/jogocoin/go_api/api/server/routes/home/<USER>"
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func LocationRouteV1(routerVersion *gin.RouterGroup) {
	locationRoutes := routerVersion.Group("/location")
	{
		locationRoutes.GET(
			"/get_suggestions",
			sharedFunc.CheckLocationAuthKey,
			locationController.GetLocationSuggestionC,
		)
		locationRoutes.GET(
			"/get_details",
			sharedFunc.CheckLocationAuthKey,
			locationController.GetLocationDetailsC,
		)
	}
}
