package commonFunc

import (
	"log"
	"strconv"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func WebCookieMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		headers := c.Value("requestHeaders").(structs.Headers)
		appType := headers.AppType

		if appType == common.AppTypeWebFrontend {
			if subzoneIdCookie, err := c.Request.Cookie("subzone_id"); err == nil {
				subzoneID, err := strconv.ParseInt(subzoneIdCookie.Value, 10, 64)
				if err != nil {
					log.Printf("Unable to parse subzone cookie val: %s, Error: %v", subzoneIdCookie.Value, err)
				} else {
					headers.SubzoneId = subzoneID
				}
			}
			if cityIdCookie, err := c.Request.Cookie("city_id"); err == nil {
				cityID, err := strconv.Atoi(cityIdCookie.Value)
				if err != nil {
					log.Printf("Unable to parse city cookie val: %s, Error: %v", cityIdCookie.Value, err)
				} else {
					c.Set("city_id", int32(cityID))
					headers.CityId = cityID
				}
			}
			if sessionIdCookie, err := c.Request.Cookie("session_id"); err == nil {	
				headers.SessionId = sessionIdCookie.Value
			} else {
				headers.SessionId = uuid.New().String()
			}
				
			if fitsoTrackIdCookie, err := c.Request.Cookie("fitso_track_id"); err == nil {
				headers.FitsoTrackId = fitsoTrackIdCookie.Value
			} else {
				headers.FitsoTrackId = uuid.New().String()
			}
			
			if locationDisplayNameCookie, err := c.Request.Cookie("location_display_title"); err == nil {
				c.Set("location_display_title", locationDisplayNameCookie.Value)
			}

			if presentLatCookie, err := c.Request.Cookie("x-present-lat"); err == nil {
				headers.PresentLat, _ = strconv.ParseFloat(presentLatCookie.Value, 64)
			} 
			
			if presentLongCookie, err := c.Request.Cookie("x-present-long"); err == nil {
				headers.PresentLong, _ = strconv.ParseFloat(presentLongCookie.Value, 64)
			} 

			if userDefinedLat, err := c.Request.Cookie("x-user-defined-lat"); err == nil {
				headers.UserDefinedLat, _ = strconv.ParseFloat(userDefinedLat.Value, 64)
			} 
			
			if userDefinedLong, err := c.Request.Cookie("x-user-defined-long"); err == nil {
				headers.UserDefinedLong, _ = strconv.ParseFloat(userDefinedLong.Value, 64)
			} 
			c.Set("requestHeaders", headers)
		}
		c.Next()
	}
}
