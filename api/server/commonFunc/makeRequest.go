package commonFunc

import (
	"context"
	"encoding/base64"
	"io"
	"io/ioutil"
	"net/http"

	"log"
)

// Response represents custom HTTP request response object
type Response struct {
	Body   string
	Status int
}

// Response represents custom HTTP request object
type Request struct {
	Method      string
	RequestURL  string
	RequestBody io.Reader
	Headers     map[string]string
}

const (
	FormContentType = "application/x-www-form-urlencoded"
	Authorization   = "Authorization"
	ContentType     = "Content-Type"
)

const (
	MethodTypePost = "POST"
)

// MakeRequest helps to make a HTTP request
func MakeHTTPRequest(ctx context.Context, request *Request) (*Response, error) {
	requestObject, err := http.NewRequest(request.Method, request.RequestURL, request.RequestBody)
	if err != nil {
		log.Printf("Make request: Error in making a request object for url %v, err %v", request.RequestURL, err)

		return nil, err
	}

	for k, v := range request.Headers {
		requestObject.Header.Set(k, v)
	}

	client := &http.Client{}
	httpRequestResponse, requestErr := client.Do(requestObject)

	if requestErr != nil {
		log.Printf("Make request: Error in making a request to url %v, err %v", request.RequestURL, requestErr)

		return nil, requestErr
	}

	responseBody, err := ioutil.ReadAll(httpRequestResponse.Body)

	if err != nil {
		log.Printf("Make request: Error in reading body from response for request url %v, err %v", request.RequestURL, err)

		return nil, err
	}

	httpRequestResponse.Body.Close()

	responseObject := &Response{}

	responseObject.Body = string(responseBody)
	responseObject.Status = httpRequestResponse.StatusCode

	return responseObject, nil
}

// creates basic auth string for basic auth header
func BasicAuth(username, password string) string {
	auth := username + ":" + password
	return base64.StdEncoding.EncodeToString([]byte(auth))
}
