package commonFunc

import (
	"bitbucket.org/jogocoin/go_api/api/server/routes/sharedFunc"
	"github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {

	return func(c *gin.Context) {
		allowedHosts := []string{"https://dev.getfitso.com", "https://www.getfitso.com"}

		requestOrigin := c.Request.Header.Get("origin")

		if sharedFunc.ContainsString(allowedHosts, requestOrigin) {
			c.Writer.Header().Set("Access-Control-Allow-Origin", requestOrigin)
		} else {
			c.Writer.Header().Set("Access-Control-Allow-Origin", "https://api.getfitso.com")
		}

		c.<PERSON>.Header().Set("Access-Control-Allow-Credentials", "true")
		c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "app-type, access-token, content-type, *")
		c.<PERSON>.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
