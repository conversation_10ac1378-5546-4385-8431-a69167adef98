package commonFunc

import (
	"bitbucket.org/jogocoin/go_api/api/structs"
	"github.com/gin-gonic/gin"
	"strconv"
)

func ParseAndSetHeaders() gin.HandlerFunc {

	return func(c *gin.Context) {

		var headers structs.Headers
		// validation related data
		headers.ApiKey = c.Request.Header.Get("api-key")
		headers.UserId, _ = strconv.Atoi(c.Request.Header.Get("x-j-user-id"))
		headers.AccessToken = c.Request.Header.Get("access-token")
		headers.AppType = c.Request.Header.Get("app-type")
		headers.AppVersion = c.Request.Header.Get("app-version")
		headers.CityId, _ = strconv.Atoi(c.Request.Header.Get("city-id"))
		headers.SocietyId, _ = strconv.Atoi(c.Request.Header.Get("society-id"))
		headers.TabId, _ = strconv.Atoi(c.Request.Header.Get("tab-id"))
		headers.FeatureType = c.Request.Header.Get("feature-type")
		headers.PresentLat, _ = strconv.ParseFloat(c.Request.Header.Get("x-present-lat"), 64)
		headers.PresentLong, _ = strconv.ParseFloat(c.Request.Header.Get("x-present-long"), 64)
		headers.UserDefinedLat, _ = strconv.ParseFloat(c.Request.Header.Get("x-user-defined-lat"), 64)
		headers.UserDefinedLong, _ = strconv.ParseFloat(c.Request.Header.Get("x-user-defined-long"), 64)

		// tz data
		headers.Timezone = c.Request.Header.Get("timezone")
		headers.IsDebug, _ = strconv.Atoi(c.Request.Header.Get("is-debug"))
		headers.Language = c.Request.Header.Get("language")
		headers.SubzoneId, _ = strconv.ParseInt(c.Request.Header.Get("subzone-id"), 10, 64)

		// Device data
		headers.DeviceManufacturer = c.Request.Header.Get("device-manufacturer")
		headers.DeviceName = c.Request.Header.Get("device-name")
		headers.DeviceOs = c.Request.Header.Get("device-os")
		headers.DeviceID = c.Request.Header.Get("device-id")

		// extra data
		headers.ContentLength, _ = strconv.Atoi(c.Request.Header.Get("content-length"))
		headers.ContentType = c.Request.Header.Get("content-type")
		headers.Accept = c.Request.Header.Get("accept")
		headers.AcceptEncoding = c.Request.Header.Get("accept-encoding")

		headers.AccessKey = c.Request.Header.Get("access-key")

		headers.ServicehookUuid = c.Request.Header.Get("x-servicehook-uuid")
		headers.ServicehookSignature = c.Request.Header.Get("x-servicehook-signature")
		headers.ServicehookEvent = c.Request.Header.Get("x-servicehook-event")

		headers.CustomhookUuid = c.Request.Header.Get("x-customhook-uuid")
		headers.CustomhookSignature = c.Request.Header.Get("x-customhook-signature")
		headers.CustomhookEvent = c.Request.Header.Get("x-customhook-event")

		//dashboard headers
		headers.AuthToken = c.Request.Header.Get("auth-token")

		// razorpay
		headers.RazorpaySignature = c.Request.Header.Get("x-razorpay-signature")

		// source
		headers.UserAgent = c.Request.Header.Get("User-Agent")
		headers.PostmanToken = c.Request.Header.Get("Postman-Token")

		headers.FitsoLocationApiKey = c.Request.Header.Get("fitso-location-api-key")
		headers.LocationProvider, _ = strconv.ParseInt(c.Request.Header.Get("location-provider"), 10, 64)
		headers.AppsflyerId = c.Request.Header.Get("X-Appsflyer-UID")

		c.Set("requestHeaders", headers)

		c.Next()
	}
}
