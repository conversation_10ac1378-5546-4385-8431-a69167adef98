package common

import (
	//structs "bitbucket.org/jogocoin/go_api/api/structs"
)

 const MANAGE_PRODUCT_TOOL = 1;
 const BOOKING_TOOL = 2;
 const NO_SHOW_PENALTY_TOOL = 3;
 const ATTENDANCE_VIEW_TOOL = 4;
 const GOOGLE_SHEET_REPORT_TOOL = 5;
 const PAYMENT_LINK_TOOL = 6;
 const BULK_PAYMENT_LINK_TOOL = 7;
 const PENDING_PAYMENT_LINK_TOOL = 8;
 const SUBSCRIPTION_TOOL = 9;
 const RECIEPT_GENERATION_TOOL = 10;
 const CAMPAIGN_TOOL = 11;
 const SPORT_OFFERS_TOOL = 12;
 const SUGGESTIONS_TOOL = 13;
 const UPLOAD_NOTIFICATIONS_TOOL = 14;
 const NOTIFICATION_CATEGORIES_TOOL = 15;
 const VIDEO_SESSIONS_TOOL = 16;
 const BROADCAST_TOOL = 17;
 const REFERRAL_REWARDS_TOOL = 18;
 const FEEDBACK_LISTING_TOOL = 19;
 const MANAGE_USER_TOOL = 20;
 const PLAN_EXTENSION_TOOL = 21;
 const MANAGE_DASHBOARD_USER_TOOL = 22;
 const BLOCK_SESSION_INVENTORY_TOOL = 23;
 const SESSION_INVENTORY_TOOL = 24;
 const BLOCKED_INVENTORIES_TOOL = 25;
 const PLAY_ARENA_TOOL = 26;
 const FACILITY_SPORTS_LISTING_TOOL = 27;
 const ADD_EDIT_FACILITY_TOOL = 28;
 const ANALYTICS_TOOL = 29;
 const FEEDBACK_ANALYSIS_TOOL = 30;
 const BULK_ANALYSIS_TOOL = 31;
 const PLAN_UTILIZATION_TOOL = 32;
 const RENEWAL_RATE_TOOL = 33;
 const COMPRESS_IMAGE_TOOL = 34;
 const UPLOAD_BANNER_TOOL = 35;
 const URL_SHORTENER_TOOL = 36;
 const AMAZON_GITCARD_TOOL = 37;
 const CAMPAIGN_OFFERS_TOOL = 38;
 const FACILITY_PRODUCT_MAP_TOOL = 39;
 const ADD_EDIT_INVENTORY_TOOL = 40;
 const ADD_EDIT_COUPON_TOOL = 41;
 const COUPON_LISTING_TOOL = 42;
 const RESEND_INVOICE_TOOL = 43;
 const SPORTS_BOOKING_TOOL = 44;
 const ADMIN_BLOCK_INVENTORY_TOOL = 45;
 const CREATE_USER_PERMISSIONS_TOOL = 46;
 const IMAGE_MAPPING_TOOL = 47;
 const REFUNDS_TOOL = 48;
 const TAG_MAPPING_TOOL = 49;
 const CACHE_HANDLING_TOOL = 50;
 const ACADEMY_SLOT_TOOL = 51; //52 for DEV
 const VIEW_ASSESSMENTS_TOOL = 52; //56 for DEV
 const INACTIVE_DAYS_TOOL = 55;
 const FACILITY_AMENITY_MAPPING_TOOL = 53;
 const COACH_FS_MAPPING_TOOL = 56;

 const SHOW_TOOL = "SHOW TOOL"
 const SHOW__TOOL = "SHOW_TOOL"

const EDIT_GOOGLE_SHEET_PERMISSION = "EDIT_GOOGLE_SHEET";        // google sheet tool
 const ADD_PRODUCT_PERMISSION = "ADD_PRODUCT";                    // manage product tool
 const EDIT_PRODUCT_PERMISSION = "EDIT_PRODUCT";                  // manage product tool
 const ADD_DELETE_USER_PERMISSION = "ADD_DELETE_USER";            // manage user tool
 const EDIT_USER_PERMISSION = "EDIT_USER";                        // manage user tool
 const NO_SHOW_REVERT_PERMISSION = "NO_SHOW_REVERT";              // no show penalty tool
 const ADD_ARENA_PERMISSION = "ADD_ARENA";                        // play arena tool
 const EDIT_ARENA_PERMISSION = "EDIT_ARENA";                      // play arena tool
 const EDIT_SUBSCRIPTION_PERMISSION = "EDIT_SUBSCRIPTION";        // subscription tool
 const UPDATE_ATTENDANCE_PERMISSION = "UPDATE_ATTENDANCE";        // booking tool
 const EDIT_INVENTORY_PERMISSION = "EDIT_INVENTORY";              // session inventory tool
 const SLOT_POPULATE_PERMISSION = "SLOT_POPULATE";                // session inventory tool
 const UNBLOCK_INVENTORY_PERMISSION = "UNBLOCK_INVENTORY";        // block inventories tool
 const GENERATE_PARTNERS_CREDENTIAL_PERMISSION = "GENERATE_PARTNERS_CREDENTIAL"; //facilitysports tool
 const UPDATE_REFUND_STATUS_PERMISSION = "UPDATE_REFUND_STATUS";  // refunds tool
 const ASSIGN_COACH_PERMISSION = "ASSIGN_COACH";
 const ADD_ACADEMY_SLOT_PERMISSION = "ADD_ACADEMY_SLOT";
 const EDIT_ACADEMY_SLOT_PERMISSION = "EDIT_ACADEMY_SLOT";
 const VIEW_ASSESSMENTS_PERMISSION = "VIEW_ASSESSMENTS";
 const MARK_INACTIVE_DAY_PERMISSION = "MARK_INACTIVE_DAY";
 const ADD_REFUND = "ADD_REFUND";
 const EDIT_DISCOUNT_PERMISSION = "EDIT_DISCOUNT";
 const EDIT_AMOUNT_PERMISSION = "EDIT_AMOUNT";

