package common

type Highlights struct {
	Image     string
	Title     string
	Subtitle1 string
}

var AllHighlights = []Highlights{
	Highlights{
		Image:     "https://fitso-images.curefit.co/uploads/schedule_confirm1678430050.png",
		Title:     "Multiple centers & batches",
		Subtitle1: "Choose your slot & center once & find it reserved",
	},
	Highlights{
		Image:     "https://fitso-images.curefit.co/uploads/<EMAIL>",
		Title:     "Expert coaches",
		Subtitle1: "Learn and improve your game with our expert coaches ",
	},
	Highlights{
		Image:     "https://fitso-images.curefit.co/uploads/securityIcon1678874784.png",
		Title:     "100% safety",
		Subtitle1: "No overcrowding. Full-time professional coaches",
	},
	Highlights{
		Image:     "https://fitso-images.curefit.co/uploads/Achivements1678429951.png",
		Title:     "Certified Curriculum",
		Subtitle1: "Get a certificate on the successful completion",
	},
}

var AllSportHighlights = map[int32][]Highlights{
	BADMINTON_SPORT_ID: []Highlights{
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/Group1678430473.png",
			Title:     "Ideal coach to student ratio",
			Subtitle1: "Ideal coach to student ratio provides focused approach to train your kids",
		},
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/<EMAIL>",
			Title:     "Expert coaches",
			Subtitle1: "Learn and improve your game with our expert coaches",
		},
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/securityIcon1678874784.png",
			Title:     "100% safety",
			Subtitle1: "No overcrowding. Full-time professional coaches",
		},
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/Achivements1678429951.png",
			Title:     "Certified Curriculum",
			Subtitle1: "Get a certificate on the successful completion",
		},
	},
	SWIMMING_SPORT_ID: []Highlights{
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/Group1678430473.png",
			Title:     "Ideal coach to student ratio",
			Subtitle1: "Ideal coach to student ratio provides focused approach to train your kids",
		},
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/<EMAIL>",
			Title:     "Expert coaches",
			Subtitle1: "Learn and improve your game with our expert coaches",
		},
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/securityIcon1678874784.png",
			Title:     "100% safety",
			Subtitle1: "No overcrowding. Full-time professional coaches",
		},
		Highlights{
			Image:     "https://fitso-images.curefit.co/uploads/Achivements1678429951.png",
			Title:     "Certified Curriculum",
			Subtitle1: "Get a certificate on the successful completion",
		},
	},
}

var SummerCampHomeSubtileSport = map[int32]string{
	SWIMMING_SPORT_ID:  "Beat the heat and learn to swim with our expert coaches",
	BADMINTON_SPORT_ID: "Serve it, smash it, learn it with our expert coaches",
}

var SummercampHomeBanners = []string{
	"uploads/SummerCamp-SuggestionCard-min1678815191.png",
	"uploads/SummerCamp-SuggestionCard-2-min1678815146.png",
	"uploads/SummerCamp-SuggestionCard-1-min1678815090.png",
}

type SummercampBanner struct {
	BannerURL      string
	SportId        int32
	HasPostBack    bool
	BannerDeeplink string
}

var SummercampBannerWithDeeplink = map[int32][]SummercampBanner{
	CITY_ID_BENGALURU: []SummercampBanner{
		/*SummercampBanner{
			BannerURL:      "uploads/SummerCamp-Appbanner1709063626.png",
			BannerDeeplink: "fitso://buy_membership?product_category_id=13",
		},
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-Pricebanner1709063651.png",
			BannerDeeplink: "fitso://buy_membership?product_category_id=13",
		},
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-PricebannerSwimming4weeks1713415061.png",
			BannerDeeplink: "fitso://course/3?product_category_id=13",
			SportId:        3,
			HasPostBack:    true,
		},*/
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-PricebannerBadminton4weeks1713414999.png",//"uploads/SummerCamp-PricebannerBadminton6weeks1709063694.png",
			BannerDeeplink: "fitso://course/7?product_category_id=13",
			SportId:        7,
			HasPostBack:    true,
		},
		/*SummercampBanner{
			BannerURL:	"uploads/Summercamp_750_badminton1680504825.png",//"uploads/SummerCamp-SuggestionCard-1-min1678815090.png",
			BannerDeeplink: "fitso://course/7?product_category_id=13",
			SportId:	7,
			HasPostBack:	true,
		},*/
	},
	CITY_ID_HYDERABAD: []SummercampBanner{
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-AppbannerHyderabad1710832956.png",
			BannerDeeplink: "fitso://buy_membership?product_category_id=13",
		},
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-Pricebanner1709063651.png",
			BannerDeeplink: "fitso://buy_membership?product_category_id==13",
		},
		/*SummercampBanner{
			BannerURL:      "uploads/SummerCamp-PricebannerSwimming6weeks1709063714.png",
			BannerDeeplink: "fitso://course/3?product_category_id=13",
			SportId:        3,
			HasPostBack:    true,
		},*/
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-PricebannerBadminton6weeks1709063694.png",
			BannerDeeplink: "fitso://course/7?product_category_id=13",
			SportId:        7,
			HasPostBack:    true,
		},
	},
	CITY_ID_DELHI_NCR: []SummercampBanner{
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-Appbanner-min1715681566.png",
			BannerDeeplink: "fitso://academy_purchase_members?product_category_id=13",
		},
		/*SummercampBanner{
			BannerURL:      "uploads/ncr_sc_swim1682925743.png",
			BannerDeeplink: "fitso://course/3?product_category_id=13",
			SportId:        3,
			HasPostBack:    true,
		},*/
		SummercampBanner{
			BannerURL:      "uploads/SummerCamp-PricebannerBadminton4weeks-min1715681674.png", //"uploads/SummerCamp-SuggestionCard-1-min1678815090.png",
			BannerDeeplink: "fitso://course/7?product_category_id=13",
			SportId:        7,
			HasPostBack:    true,
		},
	},
}

var SummercampHearFromPeopleBanner = []string{
	"uploads/<EMAIL>",
	"uploads/<EMAIL>",
	"uploads/<EMAIL>",
	//"uploads/<EMAIL>",
	//"uploads/<EMAIL>",
	//"uploads/<EMAIL>",
}

var SummercampFAQBanner = []string{
	"uploads/Group2192671678429550.png",
}

type SummercampCourse struct {
	SportName     string
	SportIconPath string
	Subtitle      string
}

var AllSummercampCourse = map[int32]SummercampCourse{
	BADMINTON_SPORT_ID: SummercampCourse{
		SportIconPath: "uploads/<EMAIL>",
		SportName:     "Badminton",
		Subtitle:      "We put the bad in baddy",
	},
	SWIMMING_SPORT_ID: SummercampCourse{
		SportIconPath: "uploads/<EMAIL>",
		SportName:     "Swimming",
		Subtitle:      "Its all cool in the pool",
	},
}

var OfferSubtitle = []string{
	"1 hr session/day",
	"No Cost EMI available",
}

var CityCallClickAction = map[int32]string{
	CITY_ID_DELHI_NCR: "https://forms.gle/xMAaMURSJPEEKwpU9?force_browser=1",//"https://forms.gle/sTTrJSCDALR8u1QR8?force_browser=1",
	CITY_ID_BENGALURU: "https://forms.gle/VRZjwf5SHn5Tq3UZ6?force_browser=1",//"https://forms.gle/sjqwRvkQUi9sYM1r9?force_browser=1",
	CITY_ID_HYDERABAD: "https://forms.gle/r2wCixd3AxeWGedj9?force_browser=1",//"https://forms.gle/ahQg65NFQWcLFPhJA?force_browser=1",
}

var IndoorPools = []int32{56, 28, 52, 411, 415, 210, 397, 193}

var FsIdNonOperationalDay = map[int32]int32{
	397: 5,
	468: 5,
	463: 5,
}