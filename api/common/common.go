package common

import "encoding/json"

// Status denotes the response status
type Status struct {
	Status  string `json:"status,omitempty"`
	Message string `json:"message,omitempty"`
}

// StatusSuccess is a helper function to create a successful Status object
func StatusSuccess(message string) Status {
	return Status{
		Status:  "Success",
		Message: message,
	}
}

// StatusFailed is a helper function to create a unsuccessful Status object
func StatusFailed(message string) Status {
	return Status{
		Status:  "Failure",
		Message: message,
	}
}

func GetStringifyPayload(payload interface{}) string {
	data, _ := json.Marshal(payload)
	return string(data)
}
