package common

const (
	FITSO_SALES_CONTACT              = "+918800884949"
	USER_AUTH_WEB_APP_TYPE           = "web"
	USER_AUTH_PARTNER_FACILITY_TYPE  = "partner-facility"
	SHORT_NOTICE_ICON_PATH           = "uploads/short_notice1620211262.png"
	BULLET_IMAGE_PATH                = "uploads/Ellipse2271617607606.png"
	DEFAULT_FACILITY_RATING          = "0.0"
	PAGE_TYPE_SPORT                  = "sport"
	PAGE_TYPE_FACILITY               = "facility"
	PAGE_TYPE_BUDDY_LIST             = "buddies"
	PAGE_TYPE_HOME                   = "home"
	PAGE_TYPE_MY_BOOKINGS            = "my_bookings"
	PAGE_TYPE_PURCHASE_SUCCESS       = "purchase_success"
	PAGE_TYPE_AEROBAR                = "aerobar"
	PAGE_TYPE_MASTERKEY_HOME         = "masterkey_home"
	PAGE_TYPE_MASTERKEY_HOME_EMPTY   = "masterkey_home_empty"
	PAGE_TYPE_ACADEMY_HOME           = "academy_home"
	PAGE_TYPE_ACADEMY_HOME_EMPTY     = "academy_home_empty"
	PAGE_TYPE_SUMMER_CAMP_HOME       = "summer_camp_home"
	PAGE_TYPE_SUMMER_CAMP_HOME_EMPTY = "summer_camp_home_empty"
	PAGE_TYPE_FITSO_FEATURES_LIST    = "fitso_features_list"
	PAGE_TYPE_NO_FEATURES            = "no_features"
	PAGE_TYPE_COURSE_CATEGORY        = "course_category"
	PAGE_TYPE_MASTERKEY              = "masterkey"
	PAGE_TYPE_ACADEMY                = "academy"
	PAGE_TYPE_SUMMER_CAMP            = "summer_camp"
	Academy                          = "academy"
	MASTERKEY_IMAGE                  = "uploads/<EMAIL>"
	ACADEMY_IMAGE                    = "uploads/<EMAIL>"
	SUMMER_CAMP_IMAGE                = "uploads/<EMAIL>"
	POST_ACTION_BUYING_FOR_ME        = "buy_for_me"
	POST_ACTION_BUYING_FOR_OTHERS    = "buy_for_others"
)

// ACADEMY
const (
	ACADEMY_MIN_AGE                      = 5
	ACADEMY_MAX_AGE                      = 18
	SUMMER_CAMP_MIN_AGE                  = 5
	SUMMER_CAMP_MAX_AGE                  = 15
	ACADEMY_HOME_PAGE_FACILITY_COUNT     = 20
	ACADEMY_COURSE_PAGE_FACILITY_COUNT   = 15
	ACADEMY_FACILITY_PAGE_FACILITY_COUNT = 20
	MasterkeyCategoryID                  = 2
	SinglekeyCategoryID                  = 3
	AcademyCategoryID                    = 12
	SummerCampCategoryID                 = 13
	PlayArenaSummerCampGuided            = 7
	ACADEMY_PURCHASE_FLOW                = "academy_purchase"
	ACADEMY_PURCHASE_RECOMMENDED_FLOW    = "academy_purchase_recommended_plan_page"
	ACADEMY_PRODUCT_ARENA_CATEGORY       = 5
)

// FACILITY TAGS
const (
	NEW_FACILITY_SPORT_TAG      = "NEW"
	UPCOMING_FACILITY_SPORT_TAG = "Coming soon"
	OPENING_FACILITY_SPORT_TAG  = "OPENING SOON"
	ONHOLD_FACILITY_SPORT_TAG   = "ON HOLD"
)

// MEDICAL DETAILS
const (
	PURCHASE_TIME_MIN_FOR_MEDICAL_INFO = ********** // 2021-07-17 00:00:01
	MEDICAL_FORM_FILLED_STATUS         = 1
	MEDICAL_FORM_NOT_FILLED_STATUS     = 0
	MEDICAL_FORM_NAME_INPUT_ID         = 1
	MEDICAL_FORM_CONTACT_NO_INPUT_ID   = 2
)

const (
	SWIMMING_CLOSE_SEASON   = "close"
	SWIMMING_CLOSING_SEASON = "closing"
	SWIMMING_MID_SEASON     = "mid"
	SWIMMING_OPENING_SEASON = "opening"
	SWIMMING_OPEN_SEASON    = "open"
)

const (
	AppTypeFitsoAndroid = "fitso_android"
	AppTypeFitsoIos     = "fitso_ios"
)

// POSSIBLE STATUS
const (
	UNEXPECTED_ERROR      = "something unexpected happened"
	SUCCESS               = "success"
	FAILURE               = "failure"
	UNAUTHORIZED          = "unauthorized"
	NOT_AUTHORIZED        = "not authorized"
	INTERNAL_SERVER_ERROR = "internal server error"
	BAD_REQUEST           = "bad request"
	FAILED                = "failed"
	FAILURE_IMAGE         = "https://fitso-images.curefit.co/file_assets/failure_image.json"
	SUCCESS_IMAGE         = "https://fitso-images.curefit.co/file_assets/success_image.json"
)

const (
	CITY_ID_DELHI_NCR = int32(1)
	CITY_ID_HYDERABAD = int32(8)
	CITY_ID_BENGALURU = int32(68)
)

const (
	CRICKET_SPORT_ID       = 1
	TENNIS_SPORT_ID        = 2
	SWIMMING_SPORT_ID      = 3
	BADMINTON_SPORT_ID     = 7
	FOOTBALL_SPORT_ID      = 8
	SQUASH_SPORT_ID        = 10
	TABLE_TENNIS_SPORT_ID  = 11
	RUN_SPORT_ID           = 26
	GYM_SPORT_ID           = 41
	YOGA_SPORT_ID          = 33
	DANCE_FITNESS_SPORT_ID = 43
	PUMP_STRENGTH_SPORT_ID = 44
)

// SPORTS COLOR MAP
var SportIdBackgroundColorMap = map[int32][2]string{
	CRICKET_SPORT_ID:       {"orange", "100"},
	TENNIS_SPORT_ID:        {"lime", "100"},
	SWIMMING_SPORT_ID:      {"blue", "100"},
	BADMINTON_SPORT_ID:     {"yellow", "100"},
	FOOTBALL_SPORT_ID:      {"blue", "100"},
	SQUASH_SPORT_ID:        {"indigo", "100"},
	TABLE_TENNIS_SPORT_ID:  {"red", "100"},
	RUN_SPORT_ID:           {"green", "100"},
	YOGA_SPORT_ID:          {"purple", "100"},
	GYM_SPORT_ID:           {"indigo", "100"},
	DANCE_FITNESS_SPORT_ID: {"pink", "100"},
	PUMP_STRENGTH_SPORT_ID: {"orange", "100"},
}

//sportId to sportName map
var SportIdSportNameMap = map[int32]string{
	CRICKET_SPORT_ID:       "Cricket",
	TENNIS_SPORT_ID:        "Tennis",
	SWIMMING_SPORT_ID:      "Swimming",
	BADMINTON_SPORT_ID:     "Badminton",
	FOOTBALL_SPORT_ID:      "Football",
	SQUASH_SPORT_ID:        "Squash",
	TABLE_TENNIS_SPORT_ID:  "Table tennis",
	RUN_SPORT_ID:           "Running",
	YOGA_SPORT_ID:          "Yoga",
	GYM_SPORT_ID:           "Gym",
	DANCE_FITNESS_SPORT_ID: "Dance & Fitness",
	PUMP_STRENGTH_SPORT_ID: "Pump & Strength",
}

const (
	SUGGESTION_ACTION_SPECIAL_NOTICE             = 1
	SUGGESTION_ACTION_APP_WEB_LINK               = 2
	SUGGESTION_ACTION_WEB_LINK                   = 3
	SUGGESTION_ACTION_BOOKING_DEEPLINK           = 4
	SUGGESTION_ACTION_UPCOMING_BOOKINGS_DEEPLINK = 6
	SUGGESTION_ACTION_PAST_BOOKINGS_DEEPLINK     = 7
	SUGGESTION_ACTION_MY_BOOKINGS_DEEPLINK       = 11
	SUGGESTION_ACTION_MY_MEMBERSHIPS_DEEPLINK    = 12
	SUGGESTION_ACTION_HOME_DEEPLINK              = 14
	SUGGESTION_ACTION_SPORT_DEEPLINK             = 15
	SUGGESTION_ACTION_FACILITY_DEEPLINK          = 16
	SUGGESTION_ACTION_PURCHASE_DEEPLINK          = 17
	SUGGESTION_ACTION_RENEWAL_DEEPLINK           = 18
	SUGGESTION_ACTION_TRIAL_DEEPLINK             = 19
	SUGGESTION_ACTION_GENERIC_DEEPLINK           = 20
	SUGGESTION_ACTION_MAX_SAFETY_BOTTOM_SHEET    = 23
	SUGGESTION_ACTION_ACADEMY_BOOK_TRIAL         = 24
	SUGGESTION_ACTION_ACADEMY_WALKTHROUGH        = 25
	SUGGESTION_ACTION_CONTEXT_SWITCH             = 26
)

var ACADEMY_WHAT_TO_EXPECT_POINTS = []string{
	"<u><semibold-300|Step 1:><u>\nBook your <semibold-300|free trial class> from the app at a center of your choice.",
	"<u><semibold-300|Step 2:><u>\nOur expert coaches will <semibold-300| assess the student and will assign them a level> to start their learning journey.",
	"<u><semibold-300|Step 3:><u>\nOnce the level is assigned, you can <semibold-300|purchase a plan for that level> and our coaches will train the student to grow from thereon, under a program especially designed by industry experts.",
	"<semibold-300|For more information on the courses, please check out plans and curriculum section.>\n<semibold-300|You may also call on 8800884949 for further assistance>",
}

const (
	DAYS_TO_RESERVE_SOLDOUT_FACILITY_FOR_EXPIRED_USER = 7
	BANGALORE_CITY_ID                                 = 68
	DELHI_NCR_CITY_ID                                 = 1
	HYDERABAD_CITY_ID                                 = 8
)

var TestUsersAutoPromoCode = []int32{780335, 764972, 23, 736715, 688673, 885820, 687802}

const SUPER_ADMIN_GROUP_ID = 1

type CityPlanImage struct {
	CityId int32
	Image  string
}

var CITY_PLAN_PRICING_IMAGE_MAP = map[int32]int32{
	DELHI_NCR_CITY_ID: 5001, 
	HYDERABAD_CITY_ID: 5002,
	BANGALORE_CITY_ID: 5002,
}

const (
	SaleStartDateUnix                 = 1648751400
	SaleEndDateUnix                   = 1649183399
	SUMMERCAMP_PRODUCT_ARENA_CATEGORY = 11
)

var TestUsers = []int32{780335, 764972, 23, 736715, 688673, 885820, 622652, 686802, 697642, 895065, 895007, 853788, 79533, 582188, 687802, 5506739, 116117, 6104263, 1334093, 544849}

const PLAY_ARENA_SUMMER_CAMP_AGE_GREATER_THAN_10 = 9

var CityIdCityNameMap = map[int32]string{
	DELHI_NCR_CITY_ID: "Delhi-NCR",
	BANGALORE_CITY_ID: "Bangalore",
	HYDERABAD_CITY_ID: "Hyderabad",
}

var TestSummercampUsers = []int32{688673, 5077133, 1067194, 13674601, 697642, 687802}
