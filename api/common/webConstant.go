package common

import (
	structs "bitbucket.org/jogocoin/go_api/api/structs"
)

const AppTypeWebFrontend = "web_frontend"
const BranchAppDownloadLink = "https://link.getfitso.com/DEHm/a963a614"

const (
	EntityTypeSport       string = "sport"
	EntityTypeCity        string = "city"
	EntityTypeFacility    string = "facility"
	EntityTypeTerms       string = "terms"
	EntityTypeFaq         string = "faq"
	EntityTypePrivacy     string = "privacy"
	EntityTypeAppDownload string = "app-download"
	EntityTypeZone        string = "zone"
	EntityTypeVerifyGuest string = "verify-guest"
)

const (
	PageNameNotFound          string = "404"
	PageNameSport             string = "sport"
	PageNameFacility          string = "facility"
	PageNameCity              string = "city"
	PageNameFacilitySlots     string = "facility_slots"
	PageNameCitySlots         string = "city_slots"
	PageNameTerms             string = "terms"
	PageNameFaq               string = "faq"
	PageNamePrivacy           string = "privacy"
	PageNameTrial             string = "trial"
	PageNameAppDownload       string = "app-download"
	PageNameMembership        string = "membership"
	PageNameVerifyGuest       string = "verify-guest"
	PageNameSummerCamp        string = "summer-camp"
	PageNameSummerCampPlans   string = "summer-camp-plans"
	PageNameSummerCampFaq     string = "summer-camp-faq"
)

var DEFAULT_SUBZONE = map[int32]int64{
	CITY_ID_DELHI_NCR: 69,
	CITY_ID_HYDERABAD: 7005,
	CITY_ID_BENGALURU: 5103,
}

var SportBgMap = map[int32]string{
	CRICKET_SPORT_ID:       "",
	TENNIS_SPORT_ID:        "#F5ECFF",
	SWIMMING_SPORT_ID:      "#EDF4FF",
	BADMINTON_SPORT_ID:     "#FFF1E6",
	FOOTBALL_SPORT_ID:      "",
	SQUASH_SPORT_ID:        "#EBFFEF",
	TABLE_TENNIS_SPORT_ID:  "#E5F3F3",
	RUN_SPORT_ID:           "#F4FECD",
	YOGA_SPORT_ID:          "#E8EAF5",
	GYM_SPORT_ID:           "#FFEDF3;",
	DANCE_FITNESS_SPORT_ID: "#FEFAEC;",
	PUMP_STRENGTH_SPORT_ID: "#FFEDEF",
}

var DesktopAllowedPages = []string{
	PageNameTerms,
	PageNameFaq,
	PageNamePrivacy,
	PageNameAppDownload,
}

const (
	GURUGRAM  int32 = 2
	FARIDABAD int32 = 1
	DELHI     int32 = 10
	GHAZIABAD int32 = 12
	NOIDA     int32 = 9
)

const (
	DEFAULT_DELHI_SUBZONE_NAME     string = "R K Puram"
	DEFAULT_HYDERABAD_SUBZONE_NAME string = "Kondapur"
	DEFAULT_BANGALORE_SUBZONE_NAME string = "Whitefield"
	DEFAULT_GURUGRAM_SUBZONE_NAME  string = "DLF Cyber City"
	DEFAULT_FARIDABAD_SUBZONE_NAME string = "Sector 15, Faridabad"
	DEFAULT_NOIDA_SUBZONE_NAME     string = "Sector 18, Noida"
	DEFAULT_GHAZIABAD_SUBZONE_NAME string = "Indirapuram"
)

var DEFAULT_CITY_BIFURCATION_SUBZONE = map[int32]int64{
	GURUGRAM:  697,
	FARIDABAD: 529,
	DELHI:     69,
	GHAZIABAD: 601,
	NOIDA:     362,
}

var DEFAULT_SUBZONE_DISPLAY_TITLE = map[int64]string{
	DEFAULT_CITY_BIFURCATION_SUBZONE[GURUGRAM]:  DEFAULT_GURUGRAM_SUBZONE_NAME,
	DEFAULT_CITY_BIFURCATION_SUBZONE[FARIDABAD]: DEFAULT_FARIDABAD_SUBZONE_NAME,
	DEFAULT_CITY_BIFURCATION_SUBZONE[GHAZIABAD]: DEFAULT_GHAZIABAD_SUBZONE_NAME,
	DEFAULT_CITY_BIFURCATION_SUBZONE[NOIDA]:     DEFAULT_NOIDA_SUBZONE_NAME,
	DEFAULT_SUBZONE[CITY_ID_DELHI_NCR]:          DEFAULT_DELHI_SUBZONE_NAME,
	DEFAULT_SUBZONE[CITY_ID_BENGALURU]:          DEFAULT_BANGALORE_SUBZONE_NAME,
	DEFAULT_SUBZONE[CITY_ID_HYDERABAD]:          DEFAULT_HYDERABAD_SUBZONE_NAME,
}

const (
	DELHI_NCR int32 = 1
	HYDERABAD int32 = 8
	BENGALURU int32 = 68
)

var DEFAULT_COORDINATES = map[int32]structs.GetLocationDetails{
	DELHI_NCR: structs.GetLocationDetails{
		Lat:  28.7041,
		Long: 77.1025,
	},
	HYDERABAD: structs.GetLocationDetails{
		Lat:  17.4706,
		Long: 78.3574,
	},
	BENGALURU: structs.GetLocationDetails{
		Lat:  12.9715,
		Long: 77.7482,
	},
}

const (
	DELHI_STRING      string = "delhi"
	NOIDA_STRING      string = "noida"
	GURGAON_STRING    string = "gurgaon"
	GURUGRAM_STRING   string = "gurugram"
	FARIDABAD_STRING  string = "faridabad"
)

var DEFAULT_COORDINATES_NCR = map[int32]structs.GetLocationDetails{
	DELHI: structs.GetLocationDetails{
		Lat:  28.5595,
		Long: 77.1775,
	},
	NOIDA: structs.GetLocationDetails{
		Lat:  28.5730,
		Long: 77.3248,
	},
	FARIDABAD: structs.GetLocationDetails{
		Lat:  28.3986,
		Long: 77.3243,
	},
	GURUGRAM: structs.GetLocationDetails{
		Lat:  28.4973,
		Long: 77.0912,
	},
}
