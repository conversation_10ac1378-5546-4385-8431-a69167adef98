// main.go

package main

import (
	"fmt"
	"log"
	"time"

	"bitbucket.org/jogocoin/go_api/api/data/kafka"
	"bitbucket.org/jogocoin/go_api/api/data/mysql"
	"bitbucket.org/jogocoin/go_api/api/data/redis"
	"bitbucket.org/jogocoin/go_api/api/server"
	// commonFunc "bitbucket.org/jogocoin/go_api/api/server/commonFunc"
	"bitbucket.org/jogocoin/go_api/api/setupFunc"
	"bitbucket.org/jogocoin/go_api/api/structs"
	//"github.com/Zomato/go/metrics"
	"github.com/gin-gonic/gin"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/web"
	"github.com/newrelic/go-agent"
)

var router *gin.Engine

func main() {
	// Create service
	service := web.NewService(
		web.Name("product.api"),
		web.RegisterTTL(20*time.Second),
		web.RegisterInterval(20*time.Second),
	)
	service.Init()

	sConfig := structs.Config{}

	// initial setup
	setupFunc.SetUp(&sConfig)

	// Set Gin to production mode
	gin.SetMode(gin.ReleaseMode)

	// Set the router as the default one provided by Gin
	router = gin.Default()

	env := config.Get("env").String("api_service_development")
	newrelicKey := config.Get("codeIndependent", "newrelicKey").String("18929e46d34d63c4d2294590cf4521b93b4b9060")

	cfg := newrelic.NewConfig(env, newrelicKey)
	app, err := newrelic.NewApplication(cfg)
	if err != nil {
		log.Printf("failed to make new_relic app: %v", err)
	} else {
		router.Use(setupFunc.NewRelicMonitoring(app))
	}

	router.Use(setupFunc.WebSocketMiddleware())
	router.Use(setupFunc.Recovery(setupFunc.RecoveryHandler))

	// Process the templates at the start so that they don't have to be loaded
	// from the disk again. This makes serving HTML pages very fast.
	router.LoadHTMLGlob("./server/routes/commonTemplates/*")

	// ping server
	router.GET("/ping", func(c *gin.Context) {
		c.String(200, "pong")
	})

	mysqldb, _ := mysql.Connect(&sConfig)

	// close once process exits
	defer mysqldb.Close()

	fmt.Println("db status: connected to mysql db ...")
	// setup middleware for mysql to pass it across project
	router.Use(setupFunc.MysqlMiddleware(mysqldb))

	redisClient, errR := redis.RedisConnect()

	// close once process exits
	defer redisClient.Close()

	kbroker := kafka.KafkaConnect()

	router.Use(setupFunc.KafkaMiddleware(kbroker))

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	fmt.Println("db status: connected to redis ...")
	// setup middleware for redis to pass it across project
	router.Use(setupFunc.RedisMiddleware(*redisClient))

	// initialize metrics
	// // statsDAddress := config.Get("codeIndependent", "statsDAddress").String("localhost:9125")
	// // metricErr := metrics.Initialize(metrics.Config{
	// // 	Name:           env,
	// // 	StatsdAddr:     statsDAddress,
	// // 	StatsdProtocol: metrics.UDP,
	// // })
	// // if metricErr != nil {
	// // 	log.Println("failed to initialize metric client")
	// // }
	// commonFunc.RegisterMetrics()

	// Initialize the routes
	server.InitializeRoutes(router)

	// Start serving the application
	fmt.Println("status: initiating application on 8080 ...")
	// default gin run
	// router.Run()

	service.Handle("/", router)
	// Run service server
	if err := service.Run(); err != nil {
		log.Fatal(err)
	}
}
