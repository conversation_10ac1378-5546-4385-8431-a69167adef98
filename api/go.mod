module bitbucket.org/jogocoin/go_api/api

go 1.13

replace github.com/micro/protoc-gen-micro v1.0.0 => ./protoc-gen-micro@v0.8.0

require (
	github.com/DataDog/sketches-go v0.0.0-20190923095040-43f19ad77ff7 // indirect
	github.com/Nvveen/Gotty v0.0.0-20120604004816-cd527374f1e5 // indirect
	github.com/Shaked/gomobiledetect v0.0.0-20171211181707-25f014f66568
	github.com/Shopify/sarama v1.26.4 // indirect
	github.com/aws/aws-sdk-go v1.29.11
	github.com/benbjohnson/clock v1.1.0 // indirect
	github.com/certifi/gocertifi v0.0.0-20180118203423-deb3ae2ef261 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/getsentry/raven-go v0.1.0 // indirect
	github.com/gin-gonic/gin v1.6.2
	github.com/go-errors/errors v1.0.1
	github.com/go-redis/redis v6.15.7+incompatible
	github.com/go-redis/redis/v8 v8.6.0 // indirect
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.4.3
	github.com/google/go-cmp v0.5.6 // indirect
	github.com/google/uuid v1.1.1
	github.com/gorilla/websocket v1.4.1
	github.com/gotestyourself/gotestyourself v2.2.0+incompatible // indirect
	github.com/hashicorp/go-version v1.2.0
	github.com/jinzhu/gorm v1.9.12
	github.com/lib/pq v1.3.0 // indirect
	github.com/micro/go-micro v1.18.0
	github.com/micro/go-plugins/broker/kafka v0.0.0-20200119172437-4fe21aa238fd
	github.com/micro/go-plugins/client/selector/shard v0.0.0-20200119172437-4fe21aa238fd
	github.com/newrelic/go-agent v3.3.0+incompatible
	github.com/nlopes/slack v0.6.0
	github.com/opencontainers/runc v1.0.0-rc6 // indirect
	github.com/opentracing/opentracing-go v1.1.1-0.20190913142402-a7454ce5950e // indirect
	github.com/ory/dockertest v3.3.4+incompatible // indirect
	github.com/testcontainers/testcontainers-go v0.4.0 // indirect
	go.opentelemetry.io/contrib/exporters/metric/dogstatsd v0.20.0 // indirect
	golang.org/x/net v0.0.0-20210119194325-5f4716e94777
	golang.org/x/sys v0.0.0-20210124154548-22da62e12c0c // indirect
	golang.org/x/text v0.3.5
	google.golang.org/grpc v1.27.1
	google.golang.org/protobuf v1.23.0
	gopkg.in/go-playground/validator.v9 v9.31.0
)

replace github.com/coreos/etcd v3.3.17+incompatible => github.com/coreos/etcd v3.3.4+incompatible
