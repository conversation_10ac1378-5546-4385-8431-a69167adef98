package structs

import (
	"mime/multipart"
	. "time"

	productPB "bitbucket.org/jogocoin/go_api/api/proto/product"
)

type Product struct {
	PID                         int32                   `validate:"omitempty,min=1" form:"product_id" json:"product_id,omitempty" gorm:"type:bigint(8);PRIMARY_KEY"`
	IsPass                      int32                   `validate:"omitempty" json:"is_pass,omitempty" form:"is_pass" gorm:"type:tinyint(1)"`
	FSID                        int32                   `validate:"omitempty,min=1" form:"fs_id" json:"fs_id,omitempty"`
	ProductKey                  string                  `validate:"omitempty" json:"product_key" form:"product_key"`
	Price                       float32                 `validate:"omitempty" json:"price" form:"price" gorm:"type:decimal(6,2)"`
	SessionCount                int32                   `validate:"omitempty" json:"session_count,omitempty" form:"session_count" gorm:"type:mediumint(4)"`
	AllSportPass                int32                   `validate:"omitempty" form:"all_sport_pass" json:"all_sport_pass,omitempty" gorm:"type:mediumint(4)"`
	Duration                    int32                   `validate:"omitempty" json:"duration" form:"duration" gorm:"type:mediumint(4)"`
	DurationUnit                string                  `validate:"omitempty" json:"duration_unit" form:"duration_unit"`
	AllFacilities               int32                   `validate:"omitempty" json:"all_facilities" form:"all_facilities"`
	DurationInDays              int32                   `validate:"omitempty,min=1" form:"duration_in_days" json:"duration_in_days,omitempty" gorm:"type:mediumint(4)"`
	AllSlotsFlag                int32                   `validate:"omitempty" json:"all_slots_flag" form:"all_slots_flag"`
	ProductDescription          string                  `validate:"omitempty" json:"product_description" form:"product_description"`
	CreatedBy                   int32                   `validate:"omitempty" json:"created_by" form:"created_by"`
	SlotId                      int32                   `validate:"omitempty" json:"slot_id" form:"slot_id"`
	PlanDays                    string                  `validate:"omitempty" json:"plan_days" form:"plan_days"`
	TotalSellingCapacity        int32                   `validate:"omitempty" json:"total_selling_capacity" form:"total_selling_capacity"`
	RetailPrice                 float32                 `validate:"omitempty" json:"retail_price" form:"retail_price"`
	LocationId                  int32                   `validate:"omitempty" json:"location_id" form:"location_id"`
	AllFacilitySportFlag        int32                   `validate:"omitempty" json:"all_facility_sport_flag" form:"all_facility_sport_flag"`
	IsVisible                   int32                   `validate:"omitempty" json:"is_visible" form:"is_visible"`
	IsActive                    int32                   `validate:"omitempty" json:"is_active" form:"is_active"`
	IsTrial                     bool                    `validate:"omitempty" json:"is_trial" form:"is_trial"`
	ProductCategoryId           int32                   `validate:"omitempty" form:"product_category_id" json:"product_category_id,omitempty" gorm:"type:mediumint(4)"`
	FeatureType                 int32                   `validate:"omitempty" json:"feature_type" form:"feature_type"`
	MaxSportsActiveBookingLimit int32                   `validate:"omitempty" json:"max_sports_active_booking_limit" form:"max_sports_active_booking_limit"`
	PpTokenAmount               float32                 `validate:"omitempty" json:"pp_token_amount" form:"pp_token_amount"`
	PpFinalAmount               float32                 `validate:"omitempty" json:"pp_final_amount" form:"pp_final_amount"`
	TokenPurchaseEligible       int32                   `validate:"omitempty" json:"token_purchase_eligible" form:"token_purchase_eligible"`
	FsData                      []FsData                `validate:"omitempty" form:"fs_data" json:"fs_data"`
	AllSports                   int32                   `validate:"omitempty" json:"all_sports" form:"all_sports"`
	AllSlots                    int32                   `validate:"omitempty" json:"all_slots" form:"all_slots"`
	AllLocations                int32                   `validate:"omitempty" json:"all_locations" form:"all_locations"`
	CoachingIncluded            int32                   `validate:"omitempty" json:"coaching_included" form:"coaching_included"`
	ActiveBookingCount          int32                   `validate:"omitempty" json:"active_booking_count" form:"active_booking_count"`
	PfsmProductIds              string                  `validate:"omitempty" json:"pfsm_product_ids" form:"pfsm_product_ids"`
	ClearCache                  bool                    `validate:"omitempty" json:"clear_cache" form:"clear_cache,omitempty"`
	LocationIdV2                int32                   `validate:"omitempty" json:"location_id_v2" form:"location_id_v2,omitempty"`
	ProductCourseMappings       []ProductCourseMappings `validate:"omitempty" json:"product_course_mappings" form:"product_course_mappings"`
	CourseCategoryId            int32                   `validate:"omitempty" json:"course_category_id" form:"course_category_id"`
}

type ProductCourseMappings struct {
	SportId    int32  `validate:"omitempty" json:"sport_id" form:"sport_id"`
	Duration   int32  `validate:"omitempty" json:"duration" form:"duration"`
	DaysOfWeek string `validate:"omitempty" json:"days_of_week" form:"days_of_week"`
}

type ProductFacilitySport struct {
	PFSID             int32   `validate:"omitempty,min=1" json:"pfs_id,omitempty" form:"pfs_id" gorm:"type:bigint(8);PRIMARY_KEY"`
	FSID              int32   `validate:"omitempty,min=1" json:"fs_id,omitempty" gorm:"type:mediumint(4)"`
	PID               int32   `validate:"omitempty,min=1" json:"product_id,omitempty" form:"product_id" gorm:"type:bigint(8)"`
	FSAPTID           int32   `validate:"omitempty" json:"fsa_pt_id,omitempty" gorm:"type:bigint(8)"`
	CoachingFlag      int32   `validate:"omitempty" json:"coaching_flag,omitempty" gorm:"type:tinyint(1)"`
	ActivationDate    Time    `validate:"omitempty" json:"activation_date,omitempty" gorm:"type:date"`
	IsActive          int32   `validate:"omitempty" json:"is_active,omitempty" form:"is_active" gorm:"type:tinyint(1)"`
	IsVisible         int32   `validate:"omitempty" json:"is_visible,omitempty" form:"is_visible" gorm:"type:tinyint(1)"`
	DeactivationDate  Time    `validate:"omitempty" json:"deactivation_date,omitempty" form:"deactivation_date" gorm:"type:date"`
	CostForBooking    float32 `validate:"omitempty" json:"cost_for_booking,omitempty" gorm:"type:decimal(6,2)"`
	DiscountOnBooking float32 `validate:"omitempty" json:"discount_on_booking,omitempty" gorm:"type:decimal(6,2)"`
	FsDescription     string  `validate:"omitempty" json:"fs_description" form:"fs_description"`
}

type ProductLinkRequest struct {
	Purpose             string  `validate:"omitempty" form:"purpose" json:"purpose,omitempty" gorm:"type:varchar(200)"`
	RetailPrice         float32 `validate:"omitempty" form:"retail_price" json:"retail_price,omitempty" gorm:"type:decimal(6,2)"`
	PID                 int32   `validate:"required,min=1" form:"product_id" json:"product_id,omitempty" gorm:"type:bigint(8)"`
	UserID              int32   `validate:"omitempty,min=1" form:"user_id" json:"user_id,omitempty" gorm:"type:bigint(8)"`
	TTL                 int32   `validate:"omitempty" form:"time_to_live" json:"time_to_live,omitempty" gorm:"type:bigint(8)"`
	MinUsersPerPurchase int32   `validate:"omitempty" form:"min_users_per_purchase" json:"min_users_per_purchase,omitempty" gorm:"type:bigint(8)"`
	CouponApplicable    bool    `validate:"omitempty" form:"coupon_applicable" json:"coupon_applicable,omitempty" gorm:"type:bigint(8)"`
	OfferId             int32   `validate:"omitempty" form:"offer_id" json:"offer_id,omitempty" gorm:"type:bigint(8)"`
	EmployeeCode        string  `validate:"omitempty" form:"employee_code" json:"employee_code,omitempty" gorm:"type:varchar(200)"`
	UserEncodeVal       string  `validate:"omitempty" form:"user_encode_val" json:"user_encode_val,omitempty"`
	PurchaseType        int32   `validate:"omitempty" form:"purchase_type" json:"purchase_type,omitempty" gorm:"type:mediumint(8)"`
	RemainingAmount     float32 `validate:"omitempty" form:"remaining_amount" json:"remaining_amount,omitempty" gorm:"type:decimal(6,2)"`
	SubscriptionId      string  `validate:"omitempty" form:"subscription_id" json:"subscription_id,omitempty"`
	UserIds             string  `validate:"omitempty" form:"user_ids" json:"user_ids,omitempty"`
	GroupOffer          bool    `validate:"omitempty" form:"group_offer" json:"group_offer,omitempty"`
	MaxUsersPerGroup    int32   `validate:"omitempty" form:"max_users_per_group" json:"max_users_per_group,omitempty"`
}

type BulkProductLinkRequest struct {
	Purpose             string  `validate:"omitempty" form:"purpose" json:"purpose,omitempty" gorm:"type:varchar(200)"`
	PIDs                []int32 `validate:"omitempty" form:"p_ids" json:"p_ids"`
	TTL                 int32   `validate:"omitempty" form:"time_to_live" json:"time_to_live,omitempty" gorm:"type:bigint(8)"`
	MinUsersPerPurchase int32   `validate:"omitempty" form:"min_users_per_purchase" json:"min_users_per_purchase,omitempty" gorm:"type:bigint(8)"`
	CouponApplicable    bool    `validate:"omitempty" form:"coupon_applicable" json:"coupon_applicable,omitempty" gorm:"type:bigint(8)"`
	OfferId             int32   `validate:"omitempty" form:"offer_id" json:"offer_id,omitempty" gorm:"type:bigint(8)"`
}

type IsVoucherValid struct {
	Code         string  `validate:"omitempty" form:"code" json:"code,omitempty"`
	VoucherId    int32   `validate:"omitempty" form:"voucher_id" json:"voucher_id,omitempty"`
	ProductId    int32   `validate:"required" form:"product_id" json:"product_id,omitempty"`
	CustomAmount float32 `validate:"omitempty" form:"custom_amount" json:"custom_amount,omitempty"`
	UsersCount   int32   `validate:"omitempty" form:"users_count" json:"users_count,omitempty"`
	UserId       int32   `validate:"omitempty" form:"user_id" json:"user_id,omitempty"`
	FacilityId   int32   `validate:"omitempty" form:"facility_id" json:"facility_id,omitempty"`
	SportId      int32   `validate:"omitempty" form:"sport_id" json:"sport_id,omitempty"`
	ReferralId   int32   `validate:"omitempty" form:"referral_id" json:"referral_id,omitempty"`
}

type PlanDataForSubscription struct {
	Email         string  `validate:"omitempty" form:"email" json:"email"`
	Name          string  `validate:"omitempty" form:"name" json:"name"`
	MemberUserId  int32   `validate:"omitempty,min=1" form:"member_user_id" json:"member_user_id,omitempty"`
	ActualAmount  float32 `validate:"omitempty" form:"actual_amount" json:"actual_amount"`
	PlanStartDate int64   `validate:"omitempty,min=1" form:"plan_start_date" json:"plan_start_date,omitempty"`
}

type PurchaseDataForSubscription struct {
	MemberPurchaseId int32   `validate:"omitempty,min=1" form:"member_purchase_id" json:"member_purchase_id,omitempty"`
	Amount           float32 `validate:"omitempty" form:"amount" json:"amount"`
}

type CreateSubscription struct {
	UserID       int32                       `validate:"required,min=1" form:"user_id" json:"user_id,omitempty" gorm:"type:bigint(8)"`
	ProductID    int32                       `validate:"required,min=1" form:"product_id" json:"product_id,omitempty"`
	PurchaseID   int32                       `validate:"omitempty,min=1" form:"purchase_id" json:"purchase_id,omitempty"`
	PlanData     PlanDataForSubscription     `validate:"omitempty" form:"plan_data" json:"plan_data,omitempty"`
	PurchaseData PurchaseDataForSubscription `validate:"omitempty" form:"purchase_data" json:"purchase_data,omitempty"`
}

type GetSportsForSubscription struct {
	SubscriptionId int32 `validate:"required,min=1" form:"subscription_id" json:"subscription_id" gorm:"type:bigint(8)"`
}

type GetFacilitiesForSubscription struct {
	SubscriptionId int32 `validate:"required,min=1" form:"subscription_id" json:"subscription_id" gorm:"type:bigint(8)"`
	CityId         int32 `validate:"omitempty" form:"city_id" json:"city_id" gorm:"type:bigint(8)"`
}

type GetTimelineForSubscription struct {
	SubscriptionId int32 `validate:"required,min=1" form:"subscription_id" json:"subscription_id"`
}

type OptInSubscription struct {
	SubscriptionId int32  `validate:"required,min=1" form:"subscription_id" json:"subscription_id"`
	UserId         string `validate:"omitempty" form:"user_id" json:"user_id,omitempty"`
}
type FsData struct {
	FSID             int32 `validate:"required" json:"fs_id" form:"fs_id"`
	ActivationDate   int64 `validate:"omitempty" json:"activation_date" form:"activation_date"`
	DeactivationDate int64 `validate:"omitempty" json:"deactivation_date" form:"deactivation_date"`
}

type CreateProductFSMapping struct {
	ProductIds string   `validate:"omitempty" form:"product_ids" json:"product_ids"`
	FsData     []FsData `validate:"required" form:"fs_data" json:"fs_data"`
}

type UpdateSubscription struct {
	UserId              int32  `validate:"omitempty" form:"user_id" json:"user_id,omitempty" gorm:"type:bigint(8)"`
	SubscriptionId      int32  `validate:"omitempty" form:"subscription_id" json:"subscription_id,omitempty" gorm:"type:bigint(8)"`
	ProductId           int32  `validate:"omitempty" form:"product_id" json:"product_id,omitempty"`
	PurchaseId          int32  `validate:"omitempty" form:"purchase_id" json:"purchase_id,omitempty"`
	StartDate           int64  `validate:"omitempty" json:"start_date,omitempty" gorm:"type:datetime"`
	SubscriptionEndDate int64  `validate:"omitempty" json:"subscription_end_date,omitempty" gorm:"type:datetime"`
	SessionCount        int32  `validate:"omitempty" json:"session_count,omitempty" gorm:"type:bigint(8)"`
	FreezesProvided     int32  `validate:"omitempty" json:"freezes_provided,omitempty" gorm:"type:bigint(8)"`
	IsActive            bool   `validate:"omitempty" json:"is_active,omitempty" gorm:"type:tinyint(1)"`
	Reason              string `validate:"omitempty" form:"reason" json:"reason,omitempty"`
	PreferredFacilityId int32  `validate:"omitempty" json:"preferred_facility_id,omitempty" gorm:"type:bigint(8)"`
	PreferredSportId    int32  `validate:"omitempty" json:"preferred_sport_id,omitempty" gorm:"type:bigint(8)"`
	DeactivationDate    int64  `validate:"omitempty" json:"deactivation_date,omitempty" gorm:"type:datetime"`
}

type GetTrialDetailsForUser struct {
	UserId string `validate:"omitempty" form:"user_id" json:"user_id,omitempty" gorm:"type:bigint(8)"`
	CityId int32  `validate:"omitempty" form:"city_id" json:"city_id,omitempty"`
}

type AboutFitso struct {
	UserId     string `validate:"omitempty" form:"user_id" json:"user_id,omitempty"`
	CityId     int32  `validate:"omitempty" form:"city_id" json:"city_id,omitempty"`
	AppType    string `validate:"omitempty" form:"app_type" json:"app_type,omitempty"`
	AppVersion string `validate:"omitempty" form:"app_version" json:"app_version,omitempty"`
}

type AboutFistoHomeGround struct {
	UserId     string `validate:"required" form:"user_id" json:"user_id,omitempty"`
	CityId     int32  `validate:"omitempty" form:"city_id" json:"city_id,omitempty"`
	SocietyId  int32  `validate:"omitempty" form:"society_id" json:"society_id,omitempty"`
	AppType    string `validate:"omitempty" form:"app_type" json:"app_type,omitempty"`
	AppVersion string `validate:"omitempty" form:"app_version" json:"app_version,omitempty"`
}

type HomeGroundOfferedSports struct {
	UserId     string `validate:"omitempty" form:"user_id" json:"user_id,omitempty"`
	CityId     int32  `validate:"omitempty" form:"city_id" json:"city_id,omitempty"`
	SportId    int32  `validate:"omitempty" form:"sport_id" json:"sport_id,omitempty"`
	SocietyId  int32  `validate:"omitempty" form:"society_id" json:"society_id,omitempty"`
	AppType    string `validate:"omitempty" form:"app_type" json:"app_type,omitempty"`
	AppVersion string `validate:"omitempty" form:"app_version" json:"app_version,omitempty"`
}

type MarkFreeze struct {
	SubscriptionId int32   `validate:"required" form:"subscription_id" json:"subscription_id,omitempty"`
	MarkerUserId   int32   `validate:"omitempty" form:"marker_user_id" json:"marker_user_id,omitempty"` //fallback if uid not in headers
	FreezeDates    []int64 `validate:"required" form:"freeze_dates" json:"freeze_dates"`
	FreezeReasonId int32   `validate:"omitempty" form:"freeze_reason_id" json:"freeze_reason_id,omitempty"` //make required later
	FreezeNote     string  `validate:"omitempty" form:"freeze_note" json:"freeze_note,omitempty"`
}

type Unfreeze struct {
	SubscriptionId     int32  `validate:"required" form:"subscription_id" json:"subscription_id,omitempty"`
	MarkerUserId       int32  `validate:"omitempty" form:"marker_user_id" json:"marker_user_id,omitempty"` //fallback if uid not in headers
	UniqueFreezeString string `validate:"omitempty" form:"unique_freeze_string" json:"unique_freeze_string,omitempty"`
}

type PreMarkFreeze struct {
	SubscriptionId int32  `validate:"required" form:"subscription_id" json:"subscription_id,omitempty"`
	UserId         string `validate:"omitempty" form:"user_id" json:"user_id,omitempty" gorm:"type:bigint(8)"`
}

type FreezeLogGet struct {
	SubscriptionId     int32  `validate:"required" form:"subscription_id" json:"subscription_id,omitempty"`
	UserId             string `validate:"omitempty" form:"user_id" json:"user_id,omitempty"`
	UniqueFreezeString string `validate:"omitempty" form:"unique_freeze_string" json:"unique_freeze_string,omitempty"`
	FetchUpcoming      bool   `validate:"omitempty" form:"fetch_upcoming" json:"fetch_upcoming,omitempty"`
}

type FreezeReasonGet struct {
	FreezeReasonId int32 `validate:"omitempty" form:"freeze_reason_id" json:"freeze_reason_id,omitempty"`
}

type PostFreezeOrUnfreeze struct {
	SubscriptionId     int32   `validate:"required" form:"subscription_id" json:"subscription_id,omitempty"`
	FreezeDates        []int64 `validate:"required" form:"freeze_dates" json:"freeze_dates"`
	UniqueFreezeString string  `validate:"omitempty" form:"unique_freeze_string" json:"unique_freeze_string,omitempty"`
	ForFreeze          bool    `validate:"omitempty" form:"for_freeze" json:"for_freeze,omitempty"`
}

type GetPlanStatusForUser struct {
	UserId string `validate:"omitempty" form:"user_id" json:"user_id,omitempty" gorm:"type:bigint(8)"`
}

type Vouchers struct {
	VoucherId        int32   `validate:"omitempty" form:"voucher_id" json:"voucher_id,omitempty"`
	Code             string  `validate:"required" form:"code" json:"code,omitempty"`
	Descripton       string  `validate:"required" form:"description" json:"description,omitempty"`
	ApplicantType    int32   `validate:"required" form:"applicant_type" json:"applicant_type,omitempty"`
	CouponType       int32   `validate:"required" form:"coupon_type" json:"coupon_type,omitempty"`
	DiscountType     int32   `validate:"required" form:"discount_type" json:"discount_type,omitempty"`
	DiscountValue    int32   `validate:"required" form:"discount_value" json:"discount_value,omitempty"`
	MaxDiscount      int32   `validate:"required" form:"max_discount" json:"max_discount,omitempty"`
	MinPlanPrice     int32   `validate:"omitempty" form:"min_plan_price" json:"min_plan_price,omitempty"`
	MaxPlanPrice     int32   `validate:"omitempty" form:"max_plan_price" json:"max_plan_price,omitempty"`
	DurationInDays   int32   `validate:"omitempty" form:"duration_in_days" json:"duration_in_days,omitempty"`
	UsageCount       int32   `validate:"omitempty" form:"usage_count" json:"usage_count,omitempty"`
	MinUsers         int32   `validate:"omitempty" form:"min_users" json:"min_users,omitempty"`
	ExpiryDate       int64   `validate:"omitempty" form:"expiry_date" json:"expiry_date,omitempty"`
	CouponTypeValues []int32 `validate:"omitempty" form:"coupon_type_values" json:"coupon_type_values,omitempty"`
	UserId           int32   `validate:"omitempty" form:"user_id" json:"user_id,omitempty"`
	DeletedFlag      int32   `validate:"omitempty" form:"deleted_flag" json:"deleted_flag"`
	IsPromoted       int32   `validate:"omitempty" form:"is_promoted" json:"is_promoted,omitempty"`
	RewardType       int32   `validate:"omitempty" form:"reward_type" json:"reward_type,omitempty"`
	RewardValue      int32   `validate:"omitempty" form:"reward_value" json:"reward_value,omitempty"`
}

type GetVouchers struct {
	VouchersStatus bool  `validate:"omitempty" form:"vouchers_status" json:"vouchers_status,omitempty"`
	ApplicantType  int32 `validate:"omitempty" form:"applicant_type" json:"applicant_type,omitempty"`
	CouponType     int32 `validate:"omitempty" form:"coupon_type" json:"coupon_type,omitempty"`
	IsPromoted     bool  `validate:"omitempty" form:"is_promoted" json:"is_promoted,omitempty"`
}
type ReferralCodeGenerateOrGet struct {
	UserId string `validate:"omitempty" form:"user_id" json:"user_id"`
}

type PlanUtilizationRequest struct {
	StartDate  int64  `validate:"required" form:"start_date" json:"start_date"`
	EndDate    int64  `validate:"required" form:"end_date" json:"end_date"`
	FsIds      string `validate:"omitempty" form:"fs_ids" json:"fs_ids"`
	ProductIds string `validate:"omitempty" form:"product_ids" json:"product_ids"`
	IsTrial    bool   `validate:"omitempty" form:"is_trial" json:"is_trial"`
}

type PlanUtilizationResponse struct {
	TotalSubscription         int32 `validate:"omitempty" form:"total_subscription" json:"total_subscription,omitempty"`
	TotalBooking              int32 `validate:"omitempty" form:"total_booking" json:"total_booking,omitempty"`
	PlanUtilizationBooking    int32 `validate:"omitempty" form:"plan_utilization_booking" json:"plan_utilization_booking,omitempty"`
	PlanUtilizationAttendance int32 `validate:"omitempty" form:"plan_utilization_attendance" json:"plan_utilization_attendance,omitempty"`
	TotalAttendance           int32 `validate:"omitempty" form:"total_attendance" json:"total_attendance,omitempty"`
}

type SportsOffersGet struct {
	OfferIds        string `validate:"omitempty" form:"offer_ids"`
	OfferCampaignId int32  `validate:"omitempty" form:"offer_campaign_id"`
	ProductIds      string `validate:"omitempty" form:"product_ids"`
	IncludeUpcoming int32  `validate:"omitempty" form:"include_upcoming"`
	IncludeExpired  bool   `validate:"omitempty" form:"include_expired"`
	CityIds         string `validate:"omitempty" form:"city_ids"`
	SportIds        string `validate:"omitempty" form:"sport_ids"`
	DurationInDays  string `validate:"omitempty" form:"duration_in_days"`
	FacilityNameUrl string `validate:"omitempty" form:"facility_name_url"`
}

type SportsOfferCampaignsGet struct {
	OfferCampaignId int32 `validate:"omitempty" form:"offer_campaign_id"`
	IncludeUpcoming int32 `validate:"omitempty" form:"include_upcoming"`
	IncludeExpired  bool  `validate:"omitempty" form:"include_expired"`
}

type SportsOffer struct {
	OfferId          int32  `validate:"min=1" json:"offer_id" gorm:"type:bigint(8) unsigned auto_increment;PRIMARY_KEY"`
	OfferName        string `json:"offer_name,omitempty" gorm:"type:varchar(40)"`
	OfferCampaignId  int32  `validate:"omitempty" json:"offer_campaign_id,omitempty" gorm:"type:mediumint(4)"`
	ProductId        int32  `validate:"omitempty" json:"product_id,omitempty" gorm:"type:bigint(8)"`
	OfferedPrice     int32  `validate:"omitempty" json:"offered_price,omitempty" gorm:"type:mediumint(8)"`
	CouponApplicable int32  `validate:"omitempty" json:"coupon_applicable,omitempty" gorm:"type:tinyint(1)"`
	CouponCode       string `json:"coupon_code,omitempty" gorm:"type:varchar(40)"`
	CouponInfoText   string `json:"coupon_info_text,omitempty" gorm:"type:varchar(200)"`
	ImageDesktopUrl  string `validate:"omitempty" json:"image_desktop_url,omitempty" gorm:"type:varchar(200)"`
	ImageMobileUrl   string `validate:"omitempty" json:"image_mobile_url,omitempty" gorm:"type:varchar(200)"`
	StartTime        int64  `validate:"omitempty" json:"start_time,omitempty" gorm:"type:datetime"`
	EndTime          int64  `validate:"omitempty" json:"end_time,omitempty" gorm:"type:datetime"`
	Priority         int32  `json:"priority" gorm:"type:tinyint(1)"`
	IsVisible        int32  `json:"is_visible" gorm:"type:tinyint(1)"`
	DeletedFlag      int32  `json:"deleted_flag" gorm:"type:tinyint(1)"`
	CreatedBy        int32  `json:"created_by"`
	SectionTitle     string `json:"section_title"`
	SpecialFeature   string `json:"special_feature,omitempty" gorm:"type:varchar(40)"`
	GroupOffer       bool   `validate:"omitempty" form:"group_offer" json:"group_offer,omitempty"`
	MaxUsersPerGroup int32  `validate:"omitempty" form:"max_users_per_group" json:"max_users_per_group,omitempty"`
}

type OfferCampaign struct {
	OfferCampaignId       int32  `validate:"min=1" json:"offer_campaign_id" gorm:"type:bigint(5) unsigned auto_increment;PRIMARY_KEY"`
	CampaignName          string `json:"campaign_name" gorm:"type:varchar(40)"`
	CampaignTitle         string `json:"campaign_title" gorm:"type:varchar(80)"`
	CampaignDescription   string `json:"campaign_description" gorm:"type:varchar(800)"`
	CampaignType          int32  `json:"campaign_type" gorm:"type:tinyint(1)"`
	BannerImageDesktopUrl string `validate:"omitempty" json:"banner_image_desktop_url,omitempty" gorm:"type:varchar(200)"`
	BannerImageMobileUrl  string `validate:"omitempty" json:"banner_image_mobile_url,omitempty" gorm:"type:varchar(200)"`
	StartTime             int64  `validate:"omitempty" json:"start_time,omitempty" gorm:"type:datetime"`
	EndTime               int64  `validate:"omitempty" json:"end_time,omitempty" gorm:"type:datetime"`
	TimerEndDate          int64  `validate:"omitempty" json:"timer_end_date,omitempty" gorm:"type:datetime"`
	DeletedFlag           int32  `json:"deleted_flag" gorm:"type:tinyint(1)"`
	CreatedBy             int32  `json:"created_by"`
}

type GetRenewalRequest struct {
	StartDate    int64  `validate:"required" form:"start_date" json:"start_date"`
	EndDate      int64  `validate:"required" form:"end_date" json:"end_date"`
	ProductIds   string `validate:"omitempty" form:"product_ids" json:"product_ids"`
	IsTrial      bool   `validate:"omitempty" form:"is_trial" json:"is_trial"`
	AnalysisType int64  `validate:"required" form:"analysis_type" json:"analysis_type"`
}

type GetRenewalResponse struct {
	StartDateTemp             int64 `validate:"omitempty" form:"start_date_temp" json:"start_date_temp"`
	EndDateTemp               int64 `validate:"omitempty" form:"end_date_temp" json:"end_date_temp"`
	TotalExpiredSubscriptions int32 `validate:"omitempty" form:"total_expired_subscriptions" json:"total_expired_subscriptions,omitempty"`
	TotalRenewedSubscriptions int32 `validate:"omitempty" form:"total_renewed_subscriptions" json:"total_renewed_subscriptions,omitempty"`
	RenewedByExpired          int32 `validate:"omitempty" form:"renewed_by_expired" json:"renewed_by_expired,omitempty"`
}

type GetFacilitiesForProduct struct {
	ProductId int32 `form:"product_id"`
}

type GetProductCategories struct {
	ProductCategoryId int32 `validate:"omitempty" form:"product_category_id" json:"product_category_id"`
}

type HomeGroundLeadData struct {
	UserID      int32   `validate:"omitempty" form:"user_id" json:"user_id"`
	Phone       string  `validate:"omitempty" form:"phone" json:"phone"`
	Name        string  `validate:"omitempty" form:"name" json:"name"`
	Email       string  `validate:"omitempty" form:"email" json:"email"`
	Address     string  `validate:"omitempty" form:"address" json:"address"`
	Sport       []Sport `validate:"omitempty" form:"sport" json:"sport"`
	Age         int32   `validate:"omitempty" form:"age" json:"age"`
	HiringFor   string  `validate:"omitempty" form:"hiring_for" json:"hiring_for"`
	ProgramType string  `validate:"omitempty" form:"program_type" json:"program_type"`
}

type SealsUpgrade struct {
	SwimmingUserPlanId int32 `validate:"omitempty" form:"swimming_user_plan_id" json:"swimming_user_plan_id"`
	UserId             int32 `validate:"omitempty" form:"user_id" json:"user_id"`
}

type ImageUpload struct {
	UploadImage multipart.FileHeader `validate:"omitempty" form:"upload_image" json:"upload_image"`
	UserId      int32                `validate:"omitempty" form:"user_id" json:"user_id"`
	IsDbEntry   bool                 `validate:"omitempty" json:"is_db_entry" form:"is_db_entry"`
	ImageId     int32                `validate:"omitempty" form:"image_id" json:"image_id"`
}

type GetPurchasePageDetailsRequest struct {
	ProductId         int32  `validate:"omitempty" json:"product_id" form:"product_id"`
	ProductCategoryId string `validate:"omitempty" json:"product_category_id" form:"product_id"`
}

type GetBuyOrRenewDetailsRequest struct {
	IsRenewal bool   `validate:"omitempty" json:"is_renewal" form:"is_renewal"`
	Type      string `validate:"omitempty" json:"type" form:"type"`
	ProductId int32  `validate:"omitempty" json:"product_id" form:"product_id"`
}

type GetBuyOrRenewDetailsResponse struct {
	CurrentUser MembershipPurchaseUserInfo            `validate:"required" json:"current_user" form:"current_user"`
	Children    []*MembershipPurchaseUserInfo         `validate:"omitempty" json:"children" form:"children"`
	Response    *productPB.GetBuyingOrRenewalResponse `validate:"omitempty" json:"response" form:"response"`
}

type MembershipPurchaseUserInfo struct {
	Name         string `validate:"required" json:"user"`
	Phone        string `validate:"omitempty" json:"phone"`
	Age          int32  `validate:"omitempty" json:"age"`
	ExpiryString string `validate:"omitempty" json:"expiry_string,omitempty",`
}

type MembershipDetailsRequest struct {
	UserId    int32 `validate:"required,min=1" form:"user_id" json:"user_id,omitempty"`
	ProductId int32 `validate:"required,min=1" form:"product_id" json:"product_id,omitempty"`
}

type CancelMembershipReq struct {
	MembershipsInfo     []MembershipInfoForCancellation `validate:"required" form:"memberships_info" json:"memberships_info"`
	CancellationReasons []CancellationReasons           `validate:"omitempty" form:"cancel_reasons" json:"cancel_reasons"`
	PaymentId           string                          `validate:"omitempty" form:"payment_id" json:"payment_id"`
	RefundAmount        float32                         `validate:"omitempty" form:"refund_amount" json:"refund_amount"`
}

type MembershipInfoForCancellation struct {
	SubscriptionId int32 `validate:"required" form:"subscription_id" json:"subscription_id"`
}

type CancellationReasons struct {
	ReasonId int32 `validate:"required" form:"reason_id" json:"reason_id"`
}

type GetMembershipsEligibleForRefundReq struct {
	UserId    int32 `validate:"required" form:"user_id"`
	ProductId int32 `validate:"required" form:"product_id"`
}

type LeadSquaredCaptureLead struct {
	FirstName        string           `json:"first_name,omitempty" form:"first_name"`
	Mobile           string           `validate:"required" json:"mobile,omitempty" form:"mobile"`
	CityName         string           `json:"city_name,omitempty" form:"city_name"`
	PoolLocation     string           `json:"pool_location,omitempty" form:"pool_location"`
	LeadSource       string           `json:"lead_source,omitempty" form:"lead_source"`
	LeadSubSource    string           `json:"lead_sub_source,omitempty" form:"lead_sub_source"`
	SourceCampaign   string           `json:"source_campaign,omitempty" form:"source_campaign"`
	Remark           string           `json:"remark,omitempty" form:"remark"`
	AppSignUpDetails AppSignUpDetails `json:"app_sign_up_details,omitempty" form:"app_sign_up"`
}

type AppSignUpDetails struct {
	SubzoneName      string  `json:"subzone_name,omitempty" form:"subzone_name"`
	ClosestFacility  string  `json:"closest_facility,omitempty" form:"closest_facility"`
	FacilityDistance float32 `json:"facility_distance,omitempty" form:"facility_distance"`
}

type GetAcademyPreferredPlan struct {
	UserId                    int32                   `validate:"required" form:"user_id" json:"user_id,omitempty"`
	ProductId                 int32                   `validate:"omitempty" form:"product_id" json:"product_id,omitempty"`
	CourseCategoryId          int32                   `validate:"omitempty" form:"course_category_id" json:"course_category_id,omitempty"`
	IsRecommendedCourseOpened bool                    `validate:"omitempty" form:"is_recommended_course_opened" json:"is_recommended_course_opened"`
	ProductCategoryId         int32                   `validate:"omitempty" form:"product_category_id" json:"product_category_id,omitempty"`
	SelectedUsers             []*PurchaseSelectedUser `json:"selected_users,omitempty"`
}

type GetAcademyRecommendedCourse struct {
	UserId                     int32  `validate:"required" json:"user_id"`
	CourseCategoryId           int32  `json:"course_category_id,omitempty"`
	ProductCategoryId          int32  `json:"product_category_id,omitempty"`
	SummercampProductMappingId int32  `json:"summercamp_product_mapping_id,omitempty"`
	PostbackParams             string `json:"postback_params,omitempty"`
	//SelectedUsers              []*PurchaseSelectedUser `json:"selected_users,omitempty"`
	CourseCategoryPostback string `json:"course_category_postback,omitempty"`
}

type PurchaseSelectedUser struct {
	UserId        int32           `json:"user_id,omitempty"`
	ProductId     int32           `json:"product_id,omitempty"`
	Name          string          `json:"name,omitempty"`
	PlanStartDate int64           `json:"plan_start_date,omitempty"`
	FacilitySlots []*FacilitySlot `json:"facility_slots,omitempty"`
}

type CalculateAcademyCartRequest struct {
	ChildUsers        []*CalculateAcademyCartUser `json:"child_users"`
	ParentUser        *CalculateAcademyCartUser   `json:"parent_user"`
	ReferralCode      string                      `json:"referral_code"`
	PromoCode         string                      `json:"promo_code"`
	ProductCategoryId interface{}                 `json:"product_category_id"`
}

type CalculateAcademyCartUser struct {
	UserId        int32           `validate:"required" json:"user_id"`
	PlanStartDate int64           `validate:"required" json:"plan_start_date"`
	Name          string          `validate:"required" json:"name"`
	ProductId     int32           `validate:"required" json:"product_id"`
	FacilitySlots []*FacilitySlot `validate:"min=1" json:"facility_slots"`
}

type FacilitySlot struct {
	AcademySlotId    int32 `validate:"omitempty" json:"academy_slot_id"`
	SummercampSlotId int32 `validate:"omitempty" json:"summercamp_slot_id"`
}

type FacilityAcademySlot struct {
	AcademySlotId string `validate:"required" json:"academy_slot_id"`
	PlanStartDate int64  `validate:"required" json:"plan_start_date"`
}

type CommonCartUser struct {
	UserId           int32
	PlanStartDate    int64
	Name             string
	ProductId        int32
	FsId             int32
	SlotId           int32
	ProductName      string
	ProductDuration  string
	ProductFrequency string
	FormattedAmount  string
}
