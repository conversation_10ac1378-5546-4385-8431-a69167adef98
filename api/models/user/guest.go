package user

import (
	"context"

	"bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	"bitbucket.org/jogocoin/go_api/api/server/routes/util"
)

type GuestModificationTemplate struct {
	Status     string            `json:"status,omitempty"`
	ActionList *[]ActionListItem `json:"action_list,omitempty"`
}

type ActionListItem struct {
	Type        sushi.ActionType   `json:"type,omitempty"`
	CustomAlert *sushi.CustomAlert `json:"custom_alert,omitempty"`
	DismissPage *sushi.DismissPage `json:"dismiss_page,omitempty"`
}

func (d *GuestModificationTemplate) SetFailedTemplate(ctx context.Context, message string, dismissAfterAlert bool) {
	d.Status = common.SUCCESS //for android to handle showing of action list components.
	title, _ := sushi.NewTextSnippet("Failed")
	if message != "" {
		title, _ = sushi.NewTextSnippet(message)
	}
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	clickAtionRefreshPages, _ := sushi.NewTextClickAction(sushi.ClickActionRefreshPages)
	refreshPageItem := &sushi.RefreshPageItem{
		Type: sushi.ClickActionAddGuest,
	}
	clickAtionRefreshPages.RefreshPages = []*sushi.RefreshPageItem{refreshPageItem}
	autoDismiss := &sushi.AutoDismissData{
		Time:        3,
		ClickAction: clickAtionRefreshPages,
	}

	popupImage := getAnimatedImage(util.GetFailedLoty())
	customAlert := &sushi.CustomAlert{
		Title:              title,
		IsBlocking:         true,
		Image:              popupImage,
		AutoDismissData:    autoDismiss,
		PageName:           "bring_guest",
		DismissAfterAction: true,
	}
	if dismissAfterAlert {
		dismissPage := &sushi.DismissPage{
			Type:        sushi.DismissWithCustomAlert,
			CustomAlert: customAlert,
		}
		actionListItem := &ActionListItem{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismissPage,
		}
		d.ActionList = &[]ActionListItem{*actionListItem}
		return
	}

	actionListItem := &ActionListItem{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	d.ActionList = &[]ActionListItem{*actionListItem}

	return
}

func (d *GuestModificationTemplate) SetSuccessTemplate(ctx context.Context, message string, dismissAfterAlert bool, trackingPayload map[string]interface{}, trakingEname string) {
	d.Status = common.SUCCESS
	title, _ := sushi.NewTextSnippet(message)
	font, _ := sushi.NewFont(sushi.FontMedium, sushi.FontSize700)
	color, _ := sushi.NewColor(sushi.ColorTypeBlack, sushi.ColorTint500)
	title.SetFont(font)
	title.SetColor(color)

	popupImage := getAnimatedImage(util.GetSuccessLoty())

	clickAtionRefreshPages, _ := sushi.NewTextClickAction(sushi.ClickActionRefreshPages)
	refreshPageItem := &sushi.RefreshPageItem{
		Type: sushi.ClickActionAddGuest,
	}
	clickAtionRefreshPages.RefreshPages = []*sushi.RefreshPageItem{refreshPageItem}
	autoDismiss := &sushi.AutoDismissData{
		Time:        3,
		ClickAction: clickAtionRefreshPages,
	}

	impressionEname := &sushi.EnameData{
		Ename: trakingEname,
	}
	impressionEvent := sushi.NewClevertapEvents()
	impressionEvent.SetImpression(impressionEname)
	trackItemImpression := sushi.GetClevertapTrackItem(ctx, trackingPayload, impressionEvent)

	customAlert := &sushi.CustomAlert{
		Title:              title,
		IsBlocking:         true,
		DismissAfterAction: true,
		Image:              popupImage,
		AutoDismissData:    autoDismiss,
		PageName:           "bring_guest",
		ClevertapTracking:  []*sushi.ClevertapItem{trackItemImpression},
	}

	if dismissAfterAlert {
		dismissPage := &sushi.DismissPage{
			Type:        sushi.DismissWithCustomAlert,
			CustomAlert: customAlert,
		}
		actionListItem := &ActionListItem{
			Type:        sushi.ClickActionDismiss,
			DismissPage: dismissPage,
		}
		d.ActionList = &[]ActionListItem{*actionListItem}
		return
	}

	actionListItem := &ActionListItem{
		Type:        sushi.ActionTypeCustomAlert,
		CustomAlert: customAlert,
	}
	d.ActionList = &[]ActionListItem{*actionListItem}

	return
}

func getAnimatedImage(url string) *sushi.Image {
	animation, _ := sushi.NewAnimation(url)
	image, _ := sushi.NewImageWithAnimation(animation)
	return image
}
