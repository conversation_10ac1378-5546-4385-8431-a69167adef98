package user

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type ModificationTemplate struct {
	Status     string     `json:"status,omitempty"`
	ActionList *[]Actions `json:"action_list,omitempty"`
}

type Actions struct {
	Type                sushi.ActionType     `json:"type,omitempty"`
	RefreshPages        []*RefreshPage       `json:"refresh_pages,omitempty"`
	SafetyInfoSubmitted *SafetyInfoSubmitted `json:"safety_info_submitted,omitempty"`
	DismissPage         *DismissPage         `json:"dismiss_page,omitempty"`
}

type ActionType struct {
	Type sushi.ActionType `json:"type,omitempty"`
}

type DismissPage struct {
	Type     string          `json:"type"`
	Deeplink *sushi.Deeplink `json:"deeplink"`
}

type RefreshPage struct {
	Type string `json:"type"`
}
