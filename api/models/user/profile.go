package user

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type Response struct {
	ProfileItems       []*ProfileItem         `json:"items,omitempty"`
	ProfileHeader      *ProfileHeader         `json:"header_data,omitempty"`
	BottomSectionItems []*BottomSectionItem   `json:"bottom_sections,omitempty"`
	ClevertapTracking  []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type ProfileItem struct {
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	Icon1             *sushi.Icon            `json:"icon,omitempty"`
	Icon2             *sushi.Icon            `json:"icon2,omitempty"`
	ClickAction       *sushi.ClickAction     `json:"click_action,omitempty"`
	Tag               *sushi.Tag             `json:"tag,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
	BottomSeparator   *sushi.Separator       `json:"bottom_separator,omitempty"`
	Type              string                 `json:"type,omitempty"`
	Button            *sushi.Button          `json:"button,omitempty"`
}

type ProfileHeader struct {
	Title          *sushi.TextSnippet `json:"title,omitempty"`
	Subtitle1      *sushi.TextSnippet `json:"subtitle1,omitempty"`
	Subtitle2      *sushi.TextSnippet `json:"subtitle2,omitempty"`
	ProfileImage   *sushi.Image       `json:"profile_image,omitempty"`
	MembershipData *MembershipData    `json:"membership_data,omitempty"`
	MembershipList []*MembershipData  `json:"membership_list,omitempty"`
}

type MembershipData struct {
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	Subtitle          *sushi.TextSnippet     `json:"subtitle,omitempty"`
	Subtitle1         *sushi.TextSnippet     `json:"subtitle1,omitempty"`
	Button            *sushi.Button          `json:"button,omitempty"`
	ProgressBarData   *sushi.ProgressBar     `json:"progress_data,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type BottomSectionItem struct {
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	ClickAction       *sushi.ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
