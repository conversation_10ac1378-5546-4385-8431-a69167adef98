package user

import (
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	userPB "bitbucket.org/jogocoin/go_api/api/proto/user"
)

type PhoneLoginV2NewRes struct {
	Status        *userPB.Status        `json:"status"`
	User          *userPB.User          `json:"user,omitempty"`
	Token         *userPB.Token         `json:"token,omitempty"`
	UserExists    bool                  `json:"user_exists,omitempty"`
	SuccessAction *sushi.ResponseAction `json:"success_action,omitempty"`
}
