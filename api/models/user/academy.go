package user

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type AcademyTrialHeader struct {
	Title   *sushi.TextSnippet `json:"title,omitempty"`
	Tag     *sushi.Tag         `json:"tag,omitempty"`
	BgColor *sushi.Color       `json:"bg_color,omitempty"`
}

type AcademyMembersData struct {
	Members        []*sushi.AcademyMember `json:"members,omitempty"`
	DefaultSection *DefaultSection        `json:"default_section"`
}

type DefaultSection struct {
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	Subtitle          *sushi.TextSnippet     `json:"subtitle,omitempty"`
	RightIcon         *sushi.Icon            `json:"right_icon,omitempty"`
	TopSeparator      *sushi.Separator       `json:"top_separator,omitempty"`
	ClickAction       *sushi.ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type AcademyPurchaseMember struct {
	UserId       int32                 `json:"user_id"`
	Title        *sushi.TextSnippet    `json:"title,omitempty"`
	Subtitles    []*sushi.SubtitleItem `json:"subtitles,omitempty"`
	RightButton  *sushi.Button         `json:"right_button,omitempty"`
	BottomButton *sushi.Button         `json:"bottom_button,omitempty"`
	Status       int32                 `json:"status,omitempty"`
	Note         *sushi.TextSnippet    `json:"note,omitempty"`
	UserObject   *sushi.UserObject     `json:"user_object,omitempty"`
}

type AcademyPurchaseMemberItemConfig struct {
	BottomButton *sushi.Button `json:"bottom_button,omitempty"`
}

type PurchaseMemberList struct {
	ProductCategoryId string `json:"product_category_id,omitempty"`
}