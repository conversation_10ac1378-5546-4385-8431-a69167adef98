package user

type AssessmentHeader struct {
	Title string `json:"title,omitempty"`
}

type Button struct {
	Title string `json:"title,omitempty"`
}

type LastAssessment struct {
	Title         string `json:"title,omitempty"`
	FormattedTime string `json:"formattedTime,omitempty"`
}

type AssessmentCard struct {
	FacilityId           int32           `json:"facilityId,omitempty"`
	FacilityName         string          `json:"facilityName,omitempty"`
	SportID              int32           `json:"sportId,omitempty"`
	SportName            string          `json:sportName,omitempty`
	UserName             string          `json:"userName,omitempty"`
	UserImage            string          `json:"userImage,omitempty"`
	CoachName            string          `json:"coachName,omitempty"`
	CoachImage           string          `json:"coachImage,omitempty"`
	UserAge              string          `json:"age,omitempty"`
	CourseCategory       string          `json:"courseCategory,omitempty"`
	Level                string          `json:"level,omitempty"`
	Button               *Button         `json:"button,omitempty"`
	LastAssessment       *LastAssessment `json:"lastAssessment,omitempty"`
	SessionType          int32           `json:"sessionType,omitempty"`
	SessionTypeId        int32           `json:"sessionTypeId,omitempty"`
	Attendance           string          `json:"attendance,omitempty"`
	AssessmentEnabled    bool            `json:"assessmentEnabled,omitempty"`
	SubscriptionTimeline string          `json:"subscriptionTimeline,omitEmpty"`
	IsAssessmentOverdue  bool            `json:"isAssessmentOverdue,omitEmpty"`
	PlanDuration         string          `json:"planDuration, omitempty"`
}

type AssessmentSlotSection struct {
	SlotTime        string           `json:"slotTime,omitempty"`
	AssessmentCards []AssessmentCard `json:"assessmentCards,omitempty"`
}

type AssessmentSections struct {
	Title                string                  `json:"title,omitempty"`
	TrialSectionTitle    string                  `json:"trialSectionTitle,omitempty"`
	PurchaseSectionTitle string                  `json:"purchaseSectionTitle,omitempty"`
	PurchaseSection      []AssessmentSlotSection `json:"purchaseSections,omitempty"`
	TrialSection         []AssessmentSlotSection `json:"trialSections,omitempty"`
}

type AssessmentDataForCoach struct {
	Header             AssessmentHeader   `json:"header,omitempty"`
	AssessmentSections AssessmentSections `json:"assessmentSections,omitempty"`
}

type SkillAttribute struct {
	SkillAttributeId int32  `json:"skillAttributeId,omitempty"`
	Name             string `json:"name,omitempty"`
	MarksObtained    int32  `json:"marksObtained,omitempty"`
	MaxMarks         int32  `json:"maxMarks,omitempty"`
	ShowStars        bool   `json:"showStars"`
}

type SportSkill struct {
	Name            string           `json:"name,omitempty"`
	SkillAttributes []SkillAttribute `json:"skillAttributes,omitempty"`
}

type SportLevel struct {
	CourseCategoryLevelId int32  `json:"courseCategoryLevelId,omitempty"`
	Name                  string `json:"name,omitempty"`
}

type LevelSection struct {
	Title         string       `json:"title,omitempty"`
	AssignedLevel string       `json:"assignedLevel,omitempty"`
	SportLevels   []SportLevel `json:"sportLevels,omitempty"`
	Button        *Button      `json:"button,omitempty"`
}

type UserAssessmentData struct {
	Header       AssessmentHeader `json:"header,omitempty"`
	SkillSection []SportSkill     `json:"skillSection,omitempty"`
	LevelSection LevelSection     `json:"levelSection,omitempty"`
}

type Tab struct {
	Type       string `json:"type,omitempty"`
	Title      string `json:"title,omitempty"`
	ImageUrl   string `json:"imageUrl,omitempty"`
	IsSelected bool   `json:"isSelected,omitempty"`
}

type CoachAppHomepage struct {
	Tabs []Tab `json:"tabs,omitempty"`
}

type StudentTabAssessmentCard struct {
	UserName          string  `json:"userName,omitempty"`
	UserImage         string  `json:"userImage,omitempty"`
	SportName         string  `json:"sportName,omitempty"`
	Level             string  `json:"level,omitempty"`
	Button            *Button `json:"button,omitempty"`
	AssessmentEnabled bool    `json:"assessmentEnabled,omitempty"`
	AssessmentDue     bool    `json:"assessmentDue,omitempty"`
	DateString        string  `json:"dateString,omitempty"`
	SessionTypeId     int32   `json:"sessionTypeId,omitempty"`
	PlanDuration      string  `json:"planDuration,omitempty"`
}

type StudentTabSlotSection struct {
	SlotTime        string                     `json:"slotTime,omitempty"`
	CourseCategory  string                     `json:"courseCategory,omitempty"`
	AssessmentCards []StudentTabAssessmentCard `json:"assessmentCards,omitempty"`
}

type StudentTabFacilitySection struct {
	FacilityName string                  `json:"facilityName,omitempty"`
	SlotSections []StudentTabSlotSection `json:"slotSections,omitempty"`
}

type StudentTabAssessmentSection struct {
	FacilitySections []StudentTabFacilitySection `json:"facilitySections,omitempty"`
}

type StudentsTabData struct {
	Header            AssessmentHeader            `json:"header,omitempty"`
	Tabs              []Tab                       `json:"tabs,omitempty"`
	AssessmentSection StudentTabAssessmentSection `json:"assessmentSection,omitempty"`
}

type ScoreCard struct {
	SkillAttributeName  string `json:"skillAttributeName,omitempty"`
	MarksObtainedString string `json:"marksObtainedString,omitempty"`
}

type AssessmentHistoryPageAssessmentCard struct {
	Title                  string      `json:"title,omitempty"`
	AssessmentDate         string      `json:"assessmentDate,omitempty"`
	LevelSectionTitle      string      `json:"levelSectionTitle,omitempty"`
	AssignedLevel          string      `json:"assignedLevel,omitempty"`
	ScoreSections          []ScoreCard `json:"scoreSection,omitempty"`
	Button                 *Button     `json:"button,omitempty"`
	AssessmentDueDateTitle string      `json:"assessmentDueDateTitle,omitempty"`
	AssessmentDueDate      string      `json:"assessmentDueDate,omitempty"`
}

type AssessmentHistoryPageAssessmentSection struct {
	UpcomingAssessment     AssessmentHistoryPageAssessmentCard   `json:"upcomingAssessment,omitempty"`
	PastAssessmentSections []AssessmentHistoryPageAssessmentCard `json:"pastAssessmentSections,omitempty"`
}

type AssessmentHistoryPageUserData struct {
	UserName             string `json:"userName,omitempty"`
	UserImage            string `json:"userImage,omitempty"`
	UserAge              string `json:"age,omitempty"`
	Level                string `json:"level,omitempty"`
	SubscriptionTimeline string `json:"subscriptionTimeline,omitempty"`
	PlanDuration         string `json:"planDuration,omitempty"`
}

type AssessmentHistoryPageData struct {
	Header            AssessmentHeader                       `json:"header,omitempty"`
	SportName         string                                 `json:"sportName,omitempty"`
	SlotTime          string                                 `json:"slotTime,omitempty"`
	CourseCategory    string                                 `json:"courseCategory,omitempty"`
	UserSection       AssessmentHistoryPageUserData          `json:"userSection,omitempty"`
	AssessmentSection AssessmentHistoryPageAssessmentSection `json:"assessmentSection,omitempty"`
}
