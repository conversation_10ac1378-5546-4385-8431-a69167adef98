package user

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type MedicalPageType string

const (
	EmergencyContactPage   MedicalPageType = "emergency_contact"
	ConsentFormPage        MedicalPageType = "consent_form"
	MedicalDeclarationPage MedicalPageType = "declaration"
)

type MedicalPageHeader struct {
	Title   *sushi.TextSnippet `json:"title,omitempty"`
	Tag     *sushi.Tag         `json:"tag,omitempty"`
	BgColor *sushi.Color       `json:"bg_color,omitempty"`
}

type MedicalPageData struct {
	Type             MedicalPageType          `json:"type"`
	EmergencyContact *MedicalEmergencyContact `json:"emergency_contact,omitempty"`
	ConsentForm      *MedicalConsentForm      `json:"consent_form,omitempty"`
	Declaration      *MedicalDeclaration      `json:"declaration,omitempty"`
}

type MedicalPage struct {
	Header             *MedicalPageHeader        `json:"header,omitempty"`
	ProgressBar        *sushi.ProgressBar        `json:"progress_bar_data,omitempty"`
	PageProgress       int32                     `json:"page_progress_unit,omitempty"`
	PageData           *MedicalPageData          `json:"page_data,omitempty"`
	BottomButtonStates *sushi.BottomButtonStates `json:"bottom_button_states,omitempty"`
}

type MedicalEmergencyContact struct {
	PageTitle *sushi.TextSnippet `json:"page_title,omitempty"`
	Name      *sushi.InputField  `json:"name,omitempty"`
	Mobile    *sushi.InputField  `json:"mobile,omitempty"`
}

type MedicalConsentForm struct {
	PageTitle    *sushi.TextSnippet `json:"page_title,omitempty"`
	PageSubtitle *sushi.TextSnippet `json:"page_subtitle,omitempty"`
	Options      []*CheckboxField   `json:"medical_consent_options,omitempty"`
	Declaration  *sushi.TextSnippet `json:"declaration_statement,omitempty"`
}

type MedicalDeclaration struct {
	Id             string             `json:"id"`
	PageTitle      *sushi.TextSnippet `json:"page_title,omitempty"`
	Title          *sushi.TextSnippet `json:"title,omitempty"`
	FloatingAction *FloatingAction    `json:"floating_action,omitempty"`
}

type CheckboxField struct {
	Id         string             `json:"id"`
	IsSelected bool               `json:"is_selected"`
	Title      *sushi.TextSnippet `json:"title,omitempty"`
}

type FloatingAction struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
	Icon  *sushi.Icon        `json:"icon,omitempty"`
}

type MedicalUser struct {
	Image        *sushi.Image       `json:"image"`
	Title        *sushi.TextSnippet `json:"title"`
	RightIcon    *sushi.Icon        `json:"right_icon,omitempty"`
	IsSelected   bool               `json:"is_selected,omitempty"`
	IsSelectable bool               `json:"is_selectable"`
	ClickAction  *sushi.ClickAction `json:"click_action,omitempty"`
}

type MedicalInfoSubmitResponse struct {
	Status     string     `json:"status"`
	ActionList []*Actions `json:"action_list"`
}

type SafetyInfoSubmitted struct {
	Type        sushi.ActionType   `json:"type"`
	CustomAlert *sushi.CustomAlert `json:"custom_alert"`
}
