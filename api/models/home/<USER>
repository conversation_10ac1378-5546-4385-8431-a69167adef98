package home

import (
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
)

type AerobarResponse struct {
	Status string     `json:"status"`
	Data   []*Aerobar `json:"data,omitempty"`
}

type Aerobar struct {
	AerobarId              string             `json:"aerobar_id"`
	Image                  *sushi.Image       `json:"image,omitempty"`
	Title                  *sushi.TextSnippet `json:"title,omitempty"`
	Subtitle               *sushi.TextSnippet `json:"subtitle,omitempty"`
	ClickAction            *sushi.ClickAction `json:"click_action,omitempty"`
	RightIcon              *sushi.Icon        `json:"right_icon,omitempty"`
	RightButton            *sushi.TextSnippet `json:"right_button,omitempty"`
	Type                   string             `json:"type"`
	LottieUrl              string             `json:"lottie_url,omitempty"`
	IsRatingSnippetVisible bool               `json:"is_rating_snippet_visible,omitempty"`
}
