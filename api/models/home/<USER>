package home

type Config struct {
	SkipEnabled           bool            `json:"skip_enabled"`
	TermsUrl              string          `json:"terms_url"`
	PrivacyUrl            string          `json:"privacy_url"`
	LocationThresholdInKm float64         `json:"location_threshold_km"`
	EnabledConsoles       map[string]bool `json:"enabled_consoles"`
	PaymentProvider       int32           `json:"payment_provider,omitempty"`
	LocationApiKey        string          `json:"location_api_key,omitempty"`
	LocationProvider      int32           `json:"location_provider,omitempty"`
	CityId                int32           `json:"city_id,omitempty"`
}
