package home

import (
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
)

// ResultSection represents structure of each result section
type ResultSection struct {
	LayoutConfig                *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	ImageTextSnippetType9       *sushi.ImageTextSnippetType9Snippet       `json:"image_text_snippet_type_9,omitempty"`
	ImageTextSnippetType19      *sushi.ImageTextSnippetType19Snippet      `json:"image_text_snippet_type_19,omitempty"`
	ImageTextSnippetType10      *sushi.ImageTextSnippetType10Snippet      `json:"image_text_snippet_type_10,omitempty"`
	ImageTextSnippetType30      *sushi.ImageTextSnippetType30Snippet      `json:"image_text_snippet_type_30,omitempty"`
	V2ImageTextSnippetType35    *sushi.V2ImageTextSnippetType35Snippet    `json:"v2_image_text_snippet_type_35,omitempty"`
	SectionHeaderType1          *sushi.SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
	TagLayoutType2Snippet       *sushi.TagLayoutType2Snippet              `json:"tag_layout_type_2,omitempty"`
	TagLayoutType3Snippet       *sushi.TagLayoutType3Snippet              `json:"tag_layout_type_3,omitempty"`
	ImageTextSnippetType41      *sushi.ImageTextSnippetType41Snippet      `json:"image_text_snippet_type_41,omitempty"`
	TabSnippetType2             *sushi.TabSnippetType2Snippet             `json:"tab_snippet_type_2,omitempty"`
	ImageTextSnippetType32      *sushi.ImageTextSnippetType32Snippet      `json:"image_text_snippet_type_32,omitempty"`
	ImageTextSnippetType33      *sushi.ImageTextSnippetType33Snippet      `json:"image_text_snippet_type_33,omitempty"`
	ClevertapTracking           []*sushi.ClevertapItem                    `json:"clever_tap_tracking,omitempty"`
	ActionSnippetType2          *sushi.ActionSnippetType2Snippet          `json:"action_snippet_type_2,omitempty"`
	V2ImageTextSnippetType42    *sushi.V2ImageTextSnippetType42Snippet    `json:"v2_image_text_snippet_type_42,omitempty"`
	ResType3Snippet             *sushi.ResType3Snippet                    `json:"v2_res_snippet_type_3,omitempty"`
	FitsoTextSnippetType1       *sushi.FitsoTextSnippetType1Snippet       `json:"fitso_text_snippet_type_1,omitempty"`
	FitsoImageTextSnippetType9  *sushi.FitsoImageTextSnippetType9Snippet  `json:"fitso_image_text_snippet_type_9,omitempty"`
	TextSnippetType1            *sushi.TextSnippetType1Snippet            `json:"text_snippet_type_1,omitempty"`
	FitsoImageTextType15        *sushi.FitsoImageTextType15Snippet        `json:"fitso_image_text_snippet_type_15,omitempty"`
	MediaSnippetType2           *sushi.MediaType2Snippet                  `json:"media_snippet_type_2,omitempty"`
	V2ImageTextSnippetType49    *sushi.V2ImageTextSnippetType49Snippet    `json:"v2_image_text_snippet_type_49,omitempty"`
	V2ImageTextSnippetType52    *sushi.V2ImageTextType52Snippet           `json:"v2_image_text_snippet_type_52,omitempty"`
	FitsoTextSnippetType6       *sushi.FitsoTextType6Snippet              `json:"fitso_text_snippet_type_6,omitempty"`
	FitsoImageTextSnippetType12 *sushi.FitsoImageTextSnippetType12Snippet `json:"fitso_image_text_snippet_type_12,omitempty"`
	SnippetConfig               *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	V2ImageTextSnippetType50    *sushi.V2ImageTextSnippetType50Snippet    `json:"v2_image_text_snippet_type_50,omitempty"`
	PurchaseWidgetSnippet3      *sushi.PurchaseWidgetSnippetType3         `json:"purchase_widget_snippet_3,omitempty"`
	FitsoFacilityCardType2      *sushi.FitsoFacilityCardType2Snippet      `json:"fitso_facility_card_type_2,omitempty"`
	FitsoImageTextType7         *sushi.FitsoImageTextSnippetType7Snippet  `json:"fitso_image_text_snippet_type_7,omitempty"`
}

type GetNearbyFacilitiesRequest struct {
	PostbackParams             string                 `json:"postback_params" form:"postback_params"`
	UnmarshalledPostbackParams *RequestPostbackParams `json:"-"`
}

type RequestPostbackParams struct {
	PreviousFacilityIds []int32
}
