package jumbo

import "bitbucket.org/jogocoin/go_api/api/common"

type Ename string

const SourcePageHomeTrial = "home_trial"
const SourcePageSportTrial = "sport_trial"
const SourcePageFacilityTrial = "facility_trial"

const (
	BookingButtonClick          Ename = "book_button_click"
	TrialSportsPageView         Ename = "sport_select_pageview"
	TrialSportIconClick         Ename = "sport_icon_click"
	SportSelectButtonClick      Ename = "sport_select_button_click"
	SlotsPageView               Ename = "slots_pageview"
	SlotPageDayTabChange        Ename = "slot_page_day_tab_change"
	SlotSelectClick             Ename = "slot_select_click"
	SlotSelectCenterButtonClick Ename = "slot_select_center_button_click"
	FacilitySelectPageview      Ename = "facility_select_pageview"
	FacilitySelectClick         Ename = "facility_select_click"
	BookingPublishButtonClick   Ename = "booking_publish_button_click"
	BookingPublishResult        Ename = "booking_publish_result"
	HomePageLanding             Ename = "home_page_landing"
	HomeSportIconTap            Ename = "sport_icon_tap"
	SportPageLanding            Ename = "sport_page_landing"
	FacilityCardTap             Ename = "facility_card_tap"
	FacilityPageLanding         Ename = "facility_page_landing"
	BuyMembershipLanding        Ename = "buy_membership_landing"
	BuyForOthersTap             Ename = "buy_for_others_tap"
	BuyForMeTap                 Ename = "buy_for_me_tap"
	CartCardImpression          Ename = "cart_card_impression"
	PurchaseStatusResponse      Ename = "purchase_status_response"
)

const BookingEventsTableName = "fitso_booking_events"
const LoginEventsTableName = "fitso_login_events"
const EventsTableName = "fitso_events"
const PurchaseEventsTableName = "fitso_purchase_events"

type EnameData struct {
	Ename Ename `json:"ename"`
}

type EventNames struct {
	Tap         string `json:"tap,omitempty"`
	Impression  string `json:"impression,omitempty"`
	PageSuccess string `json:"page_success,omitempty"`
	PageDismiss string `json:"page_dismiss,omitempty"`
}

type Item struct {
	Payload    string      `json:"payload"`
	EventNames *EventNames `json:"event_names"`
	TableName  string      `json:"table_name"`
}

func NewEvents() *EventNames {
	return &EventNames{}
}

func (e *EventNames) SetTap(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.Tap = enameStr
}

func (e *EventNames) SetImpression(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.Impression = enameStr
}

func NewTrackItem(tableName string) *Item {
	return &Item{
		TableName: tableName,
	}
}

func GetEventNameObject(ename Ename) *EnameData {
	return &EnameData{
		Ename: ename,
	}
}

func (item *Item) SetEventNames(eventNames *EventNames) {
	item.EventNames = eventNames
}

func (item *Item) SetPayload(payload map[string]interface{}) {
	payloadStr := common.GetStringifyPayload(payload)
	item.Payload = payloadStr
}

func (e *EventNames) SetPageSuccess(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.PageSuccess = enameStr
}

func GetJumboTrackItem(payload map[string]interface{}, eventNames *EventNames, tableName string) *Item{
	trackItem := NewTrackItem(tableName)
	trackItem.SetPayload(payload)
	trackItem.SetEventNames(eventNames)

	return trackItem
}
