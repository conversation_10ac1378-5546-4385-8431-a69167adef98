package models

import (
	common "bitbucket.org/jogocoin/go_api/api/common"
)

// Status denotes the response status
type Status struct {
	Status  string `json:"status,omitempty"`
	Message string `json:"message,omitempty"`
}

// LegacyStatus denotes the response status for older apps
type LegacyStatus struct {
	Status Status `json:"status,omitempty"`
}

// StatusSuccess is a helper function to create a successful Status object
func StatusSuccess(message string) Status {
	return Status{
		Status:  common.SUCCESS,
		Message: message,
	}
}

// StatusFailure is a helper function to create a unsuccessful Status object
func StatusFailure(message string) Status {
	return Status{
		Status:  common.FAILURE,
		Message: message,
	}
}

// StatusUnauthorized is a helper function to create a unauthorized Status object
func StatusUnauthorized(message string) Status {
	return Status{
		Status:  common.UNAUTHORIZED,
		Message: message,
	}
}

func LegacyFailedStatus(message string) LegacyStatus {
	status := Status{
		Status:  common.FAILED,
		Message: message,
	}

	return LegacyStatus{
		Status: status,
	}
}
