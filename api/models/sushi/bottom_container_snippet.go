package sushi

// BottomContainerSnippetItem represents bottom container item
type BottomContainerSnippetItem struct {
	Type        string       `json:"type,omitempty"`
	LeftImage   *Image       `json:"left_image,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	SubTitle    *TextSnippet `json:"subtitle,omitempty"`
	RightButton *Button      `json:"right_button,omitempty"`
	SlotSection *SlotSection `json:"slots,omitempty"`
	SlotConfig  *ItemConfig  `json:"slot_config,omitempty"`
}
