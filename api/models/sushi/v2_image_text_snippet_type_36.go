package sushi

// V2ImageTextSnippetType36Snippet represents the type of image text snippet
type V2ImageTextSnippetType36Snippet struct {
	BgColor *Color                                 `json:"bg_color,omitempty"`
	Items   *[]V2ImageTextSnippetType36SnippetItem `json:"items,omitempty"`
	Title   *TextSnippet                           `json:"title,omitempty"`
}

// V2ImageTextSnippetType36SnippetItem represents the type of image text snippet item
type V2ImageTextSnippetType36SnippetItem struct {
	Image     *Image       `json:"image,omitempty"`
	Title     *TextSnippet `json:"title,omitempty"`
	Tag       *Tag         `json:"tag,omitempty"`
	Subtitle1 *TextSnippet `json:"subtitle1,omitempty"`
}

// V2ImageTextSnippetType36Layout represents V2ImageTextSnippetType36 sushi layout
type V2ImageTextSnippetType36Layout struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	V2ImageTextSnippetType36 *V2ImageTextSnippetType36Snippet `json:"v2_image_text_snippet_type_36,omitempty"`
	SnippetConfig            *SnippetConfig                   `json:"snippet_config,omitempty"`
	TextSnippetType1Snippet  *TextSnippetType1Snippet         `json:"text_snippet_type_1,omitempty"`
	TextSnippetType12Snippet *TextSnippetType12Snippet        `json:"text_snippet_type_12,omitempty"`
	ImageTextSnippetType32   *ImageTextSnippetType32Snippet   `json:"image_text_snippet_type_32,omitempty"`
	FitsoTextSnippetType5    *FitsoTextType5                  `json:"fitso_text_snippet_type_5,omitempty"`
	FitsoTextSnippetType4    *FitsoTextType4                  `json:"fitso_text_snippet_type_4,omitempty"`
	SectionHeaderType1       *SectionHeaderType1Snippet       `json:"section_header_type_1,omitempty"`
}
