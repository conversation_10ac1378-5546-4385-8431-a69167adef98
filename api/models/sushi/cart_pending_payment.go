package sushi

type CartPendingPayment struct {
	Header    *CartPendingPaymentHeader    `json:"header,omitempty"`
	Container *CartPendingPaymentContainer `json:"container,omitempty"`
}

type CartPendingPaymentHeader struct {
	Title     *TextSnippet `json:"title,omitempty"`
	Subtitle  *TextSnippet `json:"subtitle,omitempty"`
	BgColor   *Color       `json:"bg_color,omitempty"`
	LeftImage *Image       `json:"left_image,omitempty"`
}

type CartPendingPaymentContainer struct {
	Title *TextSnippet `json:"title,omitempty"`
}
