package sushi

import "errors"

type Animation struct {
	URL         string          `json:"url,omitempty"`
	Height      AnimationHeight `json:"height,omitempty"`
	Width       AnimationWidth  `json:"width,omitempty"`
	Animate     bool            `json:"animate,omitempty"`
	Repeat      bool            `json:"repeat,omitempty"`
	RepeatCount int32           `json:"repeat_count,omitempty"`
}

type AnimationHeight int
type AnimationWidth int

const (
	AnimationHeight120 AnimationHeight = 120

	AnimationWidth120 AnimationWidth = 120
)

func NewAnimation(url string) (*Animation, error) {
	if url == "" {
		return nil, errors.New("Empty Animation url")
	}
	return &Animation{URL: url}, nil
}

func (a *Animation) SetHeight(height AnimationHeight) {
	a.Height = height
}

func (a *Animation) SetWidth(width AnimationWidth) {
	a.Width = width
}

func (a *Animation) SetAnimate(animate bool) {
	a.Animate = animate
}

func (a *Animation) SetRepeat(repeat bool) {
	a.Repeat = repeat
}

func (a *Animation) SetRepeatCount(count int32) {
	a.RepeatCount = count
}
