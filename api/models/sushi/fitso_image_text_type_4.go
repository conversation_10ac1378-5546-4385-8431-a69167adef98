package sushi

type FitsoImageTextSnippetType4SnippetItem struct {
	Title  *TextSnippet   `json:"title,omitempty"`
	Image  *Image         `json:"image,omitempty"`
	<PERSON><PERSON><PERSON> bool           `json:"is_grey,omitempty"`
	Tag    *Tag           `json:"tag,omitempty"`
	Rating *RatingSnippet `json:"rating,omitempty"`
}

type FitsoImageTextSnippetType4Snippet struct {
	Items []*FitsoImageTextSnippetType4SnippetItem `json:"items,omitempty"`
}

// FitsoImageTextSnippetType4Layout represents FitsoImageTextSnippetType4 sushi layout
type FitsoImageTextSnippetType4Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType4 *FitsoImageTextSnippetType4Snippet `json:"fitso_image_text_snippet_type_4,omitempty"`
}
