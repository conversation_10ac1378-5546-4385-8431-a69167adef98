package sushi

type InputFieldStates struct {
	Always     *InputFieldStateTitle      `json:"always,omitempty"`
	Empty      *InputFieldStateTitle      `json:"empty,omitempty"`
	CharLength *InputFieldStateCharLength `json:"char_length,omitempty"`
	MinAge     *InputFieldStateAge        `json:"min_age,omitempty"`
	MaxAge     *InputFieldStateAge        `json:"max_age,omitempty"`
}

type InputFieldStateTitle struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type InputFieldStateCharLength struct {
	Length     int32        `json:"length,omitempty"`
	ErrorTitle *TextSnippet `json:"error_title,omitempty"`
}

type InputFieldStateAge struct {
	Age        int32        `json:"age,omitempty"`
	ErrorTitle *TextSnippet `json:"error_title,omitempty"`
}

type InputField struct {
	Id             string            `json:"id"`
	Optional       bool              `json:"optional"`
	IsDisabled     bool              `json:"is_disabled,omitempty"`
	Placeholder    *TextSnippet      `json:"placeholder,omitempty"`
	States         *InputFieldStates `json:"states,omitempty"`
	Value          string            `json:"value,omitempty"`
	StartDate      int64             `json:"start_date,omitempty"`
	DurationInDays int32             `json:"duration_in_days,omitempty"`
}
