package sushi

import "errors"

type SeparatorType string

const (
	SeparatorTypeLine   SeparatorType = "line"
	SeparatorTypeThick  SeparatorType = "thick"
	SeparatorTypeMedium SeparatorType = "medium"
)

type Separator struct {
	Type  SeparatorType `json:"type,omitempty"`
	Color *Color        `json:"color,omitempty"`
}

func NewSeparator(separatorType SeparatorType, color *Color) (*Separator, error) {
	if color == nil {
		return nil, errors.New("Color not defined")
	}
	return &Separator{
		Type:  separatorType,
		Color: color,
	}, nil
}
