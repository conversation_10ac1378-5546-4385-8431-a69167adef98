package sushi

const (
	// FontSemiBold represents semibold font
	FontSemiBold FontWeight = "semibold"
	// FontMedium represents medium font
	FontMedium FontWeight = "medium"
	// FontRegular represents regular font
	FontRegular FontWeight = "regular"
	// FontBold represents bold font
	FontBold FontWeight = "bold"
)

const (
	// FontSize50 represents 50 font size
	FontSize50 FontSize = "050"
	// FontSize100 represents 100 font size
	FontSize100 FontSize = "100"
	// FontSize800 represents 800 font size
	FontSize800 FontSize = "800"
	// FontSize300 represents 300 font size
	FontSize300 FontSize = "300"
	// FontSize200 represents 200 font size
	FontSize200 FontSize = "200"
	// FontSize400 represents 400 font size
	FontSize400 FontSize = "400"
	// FontSize500 represents 500 font size
	FontSize500 FontSize = "500"
	// FontSize600 represents 600 font size
	FontSize600 FontSize = "600"
	// FontSize700 represents 700 font size
	FontSize700 FontSize = "700"
	// FontSize700 represents 900 font size
	FontSize900 FontSize = "900"
)

// FontSize represents font size type
type FontSize string

// FontWeight represents font weiight type
type FontWeight string

//Font represents a sushi font item
type Font struct {
	Size   FontSize   `json:"size,omitempty"`
	Weight FontWeight `json:"weight,omitempty"`
}

//NewFont creates a new font item
func NewFont(weight FontWeight, size FontSize) (*Font, error) {
	f := &Font{
		Weight: weight,
		Size:   size,
	}

	return f, nil
}
