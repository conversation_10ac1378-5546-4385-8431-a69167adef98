package sushi

// ImageTextSnippetType13SnippetItem represents the type of image text snippet item
type ImageTextSnippetType13SnippetItem struct {
	Image             *Image           `json:"image,omitempty"`
	CornerRadius      int32            `json:"corner_radius,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

// ImageTextSnippetType13Snippet represents the type of image text snippet
type ImageTextSnippetType13Snippet struct {
	Title *TextSnippet                         `json:"title,omitempty"`
	Items []*ImageTextSnippetType13SnippetItem `json:"items,omitempty"`
}

// ImageTextSnippetType13Layout represents ImageTextSnippetType13 sushi layout
type ImageTextSnippetType13Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType13 *ImageTextSnippetType13Snippet `json:"image_text_snippet_type_13,omitempty"`
	SnippetConfig          *SnippetConfig                 `json:"snippet_config,omitempty"`
}
