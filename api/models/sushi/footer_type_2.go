package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

// FooterButtonType represents footer button type
type FooterButtonType string

// FooterButtonOrientation represents footer button orientation
type FooterButtonOrientation string

// FooterButtonSize represents footer button size
type FooterButtonSize string

const (
	// FooterButtonTypeSolid represents a solid footer button type
	FooterButtonTypeSolid   FooterButtonType = "solid"
	FooterButtonTypeOutline FooterButtonType = "outline"
	FooterButtonTypeText    FooterButtonType = "text"
)

const (
	FooterButtonSizeSmall  FooterButtonSize = "small"
	FooterButtonSizeMedium FooterButtonSize = "medium"
	FooterButtonSizeLarge  FooterButtonSize = "large"
)

const (
	// FooterButtonOrientationVertical represents a vertical footer button
	FooterButtonOrientationVertical FooterButtonOrientation = "vertical"
	// FooterButtonOrientationHorizontal represents a horizontal footer button
	FooterButtonOrientationHorizontal FooterButtonOrientation = "horizontal"
)

// FooterSnippetType2ButtonItem represents button item
type FooterSnippetType2ButtonItem struct {
	Type              FooterButtonType `json:"type,omitempty"`
	Size              FooterButtonSize `json:"size,omitempty"`
	Text              string           `json:"text,omitempty"`
	Subtext           string           `json:"subtext,omitempty"`
	Font              *Font            `json:"font,omitempty"`
	Id                string           `json:"id,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	IsActionDisabled  int32            `json:"is_action_disabled,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item    `json:"jumbo_tracking,omitempty"`
	IsMarkdownTitle   int32            `json:"is_mardown_title,omitempty"`
	IsMarkDown        int32            `json:"is_markdown,omitempty"`
}

// FooterSnippetType2Button represents the button payload inside
type FooterSnippetType2Button struct {
	Orientation FooterButtonOrientation         `json:"orientation,omitempty"`
	Items       *[]FooterSnippetType2ButtonItem `json:"items,omitempty"`
}

// FooterSnippetType2Snippet represents the type of footer snippet
type FooterSnippetType2Snippet struct {
	ButtonData   *FooterSnippetType2Button `json:"button_data,omitempty"`
	ButtonWeight []int32                   `json:"button_weight,omitempty"`
}

// FooterSnippetType2Layout represents FooterSnippetType2 sushi layout
type FooterSnippetType2Layout struct {
	LayoutConfig       *LayoutConfig              `json:"layout_config,omitempty"`
	FooterSnippetType2 *FooterSnippetType2Snippet `json:"footer_snippet_type_2,omitempty"`
}
