package sushi

// TagLayoutType2Snippet represents the tag snippet type
type TagLayoutType2Snippet struct {
	Items *[]TagLayoutType2SnippetItem `json:"items,omitempty"`
}

// TagLayoutType2SnippetItem represents the tag snippet item type
type TagLayoutType2SnippetItem struct {
	BorderColor *Color       `json:"border_color,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	BgColor     *Color       `json:"bg_color,omitempty"`
}

// TagLayoutType2Layout represents TagLayoutType2Layout sushi layout
type TagLayoutType2Layout struct {
	LayoutConfig   *LayoutConfig          `json:"layout_config,omitempty"`
	TagLayoutType2 *TagLayoutType2Snippet `json:"tag_layout_type_2,omitempty"`
}
