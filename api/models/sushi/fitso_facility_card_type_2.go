package sushi

type FitsoFacilityCardType2Snippet struct {
	Items *[]FitsoFacilityCardType2SnippetItem `json:"items,omitempty"`
}

type FitsoFacilityCardType2SnippetItem struct {
	FilterIds                   []string                      `json:"filter_ids,omitempty"`
	Title                       *TextSnippet                  `json:"title,omitempty"`
	Subtitle1                   *TextSnippet                  `json:"subtitle1,omitempty"`
	Subtitle2                   *TextSnippet                  `json:"subtitle2,omitempty"`
	Image                       *Image                        `json:"image,omitempty"`
	ClickAction                 *ClickAction                  `json:"click_action,omitempty"`
	RatingSnippets              *[]RatingSnippet              `json:"rating_snippets,omitempty"`
	TopLeftTag                  *Tag                          `json:"top_left_tag,omitempty"`
	TopRightTag                 *Tag                          `json:"top_right_tag,omitempty"`
	BottomRightTag              *Tag                          `json:"bottom_right_tag,omitempty"`
	MultiTag                    *MultiTagSnippet              `json:"multi_tag,omitempty"`
	TopTags                     *[]MultiTagSnippetItem        `json:"top_tags,omitempty"`
	BottomContainerSnippetItems *[]BottomContainerSnippetItem `json:"bottom_container_items,omitempty"`
	VerticalSubtitles           []*HorizontalSubtitleSnippet  `json:"vertical_subtitles,omitempty"`
	ClevertapTracking           []*ClevertapItem              `json:"clever_tap_tracking,omitempty"`
}

// FitsoFacilityCardSnippetType2Layout represents FitsoFacilityCardType2 sushi layout
type FitsoFacilityCardSnippetType2Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	FitsoFacilityCardType2 *FitsoFacilityCardType2Snippet `json:"fitso_facility_card_type_2,omitempty"`
}
