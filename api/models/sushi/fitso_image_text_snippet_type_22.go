package sushi

type FitsoImageTextSnippetType22Layout struct {
	LayoutConfig                       *LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextSnippetType22Snippet *FitsoImageTextSnippetType22Snippet `json:"fitso_image_text_snippet_type_22,omitempty"`
}

type FitsoImageTextSnippetType22Snippet struct {
	Items []*FitsoImageTextSnippetType22Item `json:"items,omitempty"`
}

type FitsoImageTextSnippetType22Item struct {
	TopContainer    *TopContainer                 `json:"top_container,omitempty"`
	BottomContainer []*BottomContainerSnippetItem `json:"bottom_container,omitempty"`
	ClickAction     *ClickAction                  `json:"click_action,omitempty"`
	Tag             *Tag                          `json:"tag,omitempty"`
	IsSelectable    bool                          `json:"is_selectable"`
	ClevertapTracking []*ClevertapItem 	  `json:"clever_tap_tracking,omitempty"`
}

type TopContainer struct {
	Id              int32                                 `json:"id,omitempty"`
	Title           *TextSnippet                          `json:"title,omitempty"`
	SubTitle        *TextSnippet                          `json:"subtitle,omitempty"`
	SubTitle2       *TextSnippet                          `json:"subtitle2,omitempty"`
	Tag             *Tag                                  `json:"tag,omitempty"`
	Rating          *RatingSnippetBlockItem               `json:"rating,omitempty"`
	Image           *Image                                `json:"image,omitempty"`
	BottomContainer *BottomContainerSnippetItem           `json:"bottom_container,omitempty"`
	TopContainer    *V2ImageTextSnippetType31TopContainer `json:"top_container,omitempty"`
}

type SlotSection struct {
	SectionHeader *SectionHeaderType1Snippet `json:"section_header,omitempty"`
	Slots         []*Slot                    `json:"slots,omitempty"`
}

type Slot struct {
	Title       *TextSnippet `json:"title,omitempty"`
	SubTitle    *TextSnippet `json:"subtitle,omitempty"`
	SlotId      int32        `json:"slot_id"`
	IsDisabled  bool         `json:"is_disabled"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}
