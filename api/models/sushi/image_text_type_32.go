package sushi

// ImageTextSnippetType32Snippet represents the type of image text snippet
type ImageTextSnippetType32Snippet struct {
	Items   *[]ImageTextSnippetType32SnippetItem `json:"items,omitempty"`
	BgColor *Color                               `json:"bg_color,omitempty"`
}

// ImageTextSnippetType32SnippetItem represents the type of image text snippet item
type ImageTextSnippetType32SnippetItem struct {
	Image        *Image       `json:"image,omitempty"`
	Title        *TextSnippet `json:"title,omitempty"`
	Subtitle1    *TextSnippet `json:"subtitle1,omitempty"`
	BottomButton *Button      `json:"bottom_button,omitempty"`
	ClickAction  *ClickAction `json:"click_action,omitempty"`
}

// ImageTextSnippetType32Layout represents ImageTextSnippetType32 sushi layout
type ImageTextSnippetType32Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType32 *ImageTextSnippetType32Snippet `json:"image_text_snippet_type_32,omitempty"`
}
