package sushi

type ResponseAction struct {
	Type                          ActionType                 `json:"type,omitempty"`
	CustomAlert                   *CustomAlert               `json:"custom_alert,omitempty"`
	Deeplink                      *Deeplink                  `json:"deeplink,omitempty"`
	ApiCallMultiAction            *APICallAction             `json:"api_call_multi_action,omitempty"`
	OpenSportSelectionBottomSheet *SportSelectionBottomSheet `json:"open_sport_selection_bottom_sheet,omitempty"`
}
