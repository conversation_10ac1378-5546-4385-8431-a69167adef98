package sushi

type SaveKeyItem struct {
	Value string `json:"value"`
	Key   string `json:"key"`
}

type SaveActionItem struct {
	Type                      ActionType                 `json:"type"`
	DismissPage               *DismissPage               `json:"dismiss_page,omitempty"`
	RefreshPages              []*RefreshPageItem         `json:"refresh_pages,omitempty"`
	CustomAlert               *CustomAlert               `json:"custom_alert,omitempty"`
	AddTrialPreselectedMember *AddTrialPreselectedMember `json:"add_trial_preselected_member,omitempty"`
	OpenOtpBottomSheet        *OpenOtpBottomSheet        `json:"open_otp_bottom_sheet,omitempty"`
	UpdateAcademyMemberList   *UpdateAcademyMemberList   `json:"update_academy_member_list,omitempty"`
}

type RefreshPageItem struct {
	Type string `json:"type"`
}

type SaveKey struct {
	Items      []*SaveKeyItem    `json:"items,omitempty"`
	ActionList []*SaveActionItem `json:"action_list,omitempty"`
}

type AddTrialPreselectedMember struct {
	UserId int32 `json:"user_id,omitempty"`
}

type OpenOtpBottomSheet struct {
	Header             *Header             `json:"header,omitempty"`
	Title              *TextSnippet        `json:"title,omitempty"`
	InfoText           *TextSnippet        `json:"info_text,omitempty"`
	ResendButtonStates *BottomButtonStates `json:"resend_button_states,omitempty"`
	ResendSeconds      int32               `json:"resend_seconds,omitempty"`
	VerificationApi    string              `json:"verification_api,omitempty"`
	PostbackParams     string              `json:"postback_params,omitempty"`
	ClevertapTracking  []*ClevertapItem    `json:"clever_tap_tracking,omitempty"`
}
