package sushi

// HeaderType4Snippet represents header type 4 snippet
type HeaderType4SnippetItem struct {
	Image           *Image                      `json:"image,omitempty"`
	BottomContainer *BottomContainerSnippetItem `json:"bottom_container,omitempty"`
}

type HeaderType4Snippet struct {
	Items       []*HeaderType4SnippetItem `json:"items,omitempty"`
	AspectRatio float32                   `json:"aspect_ratio,omitempty"`
}

type HeaderType4SnippetLayout struct {
	LayoutConfig       *LayoutConfig       `json:"layout_config,omitempty"`
	HeaderType4Snippet *HeaderType4Snippet `json:"header_snippet_type_4,omitempty"`
}
