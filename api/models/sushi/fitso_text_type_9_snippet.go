package sushi

type FitsoTextSnippetType9Snippet struct {
	Items []*FitsoTextSnippetType9SnippetItem `json:"items,omitempty"`
}

type FitsoTextSnippetType9SnippetItem struct {
	Title        *TextSnippet `json:"title,omitempty"`
	RightButton  *Button      `json:"right_button,omitempty"`
	BottomButton *Button      `json:"bottom_button,omitempty"`
	BgColor      *Color       `json:"bg_color,omitempty"`
	BorderColor  *Color       `json:"border_color,omitempty"`
	CornerRadius int32        `json:"corner_radius,omitempty"`
}

type FitsoTextSnippetType9Layout struct {
	LayoutConfig          *LayoutConfig                 `json:"layout_config,omitempty"`
	FitsoTextSnippetType9 *FitsoTextSnippetType9Snippet `json:"fitso_text_snippet_type_9,omitempty"`
}
