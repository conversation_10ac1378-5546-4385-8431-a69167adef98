package sushi

import "errors"

type Video struct {
	Id            string       `json:"id"`
	Url           string       `json:"url"`
	AspectRatio   float64      `json:"aspect_ratio,omitempty"`
	Thumbnail     *Image       `json:"thumb,omitempty"`
	SnippetConfig *VideoConfig `json:"snippet_config,omitempty"`
	Config        *VideoConfig `json:"config,omitempty"`
}

type VideoConfig struct {
	ShowMute        int8  `json:"show_mute,omitempty"`
	Autoplay        int8  `json:"autoplay,omitempty"`
	HasAudio        int8  `json:"has_audio,omitempty"`
	Orientation     int32 `json:"orientation,omitempty"`
	ForceFullScreen int8  `json:"force_full_screen,omitempty"`
}

func NewVideo(url string) (*Video, error) {
	if url == "" {
		return nil, errors.New("video url is empty")
	}
	return &Video{Url: url}, nil
}

func (v *Video) SetAspectRatio(aspectRatio float64) {
	v.AspectRatio = aspectRatio
}

func (v *Video) SetThumb(thumbnail *Image) {
	v.Thumbnail = thumbnail
}

func (v *Video) SetSnippetConfig(snippetConfig *VideoConfig) {
	v.SnippetConfig = snippetConfig
}
