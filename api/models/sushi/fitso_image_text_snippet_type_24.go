package sushi

type FitsoImageTextSnippetType24Snippet struct {
	Title           *TextSnippet `json:"title,omitempty"`
	Subtitle        *TextSnippet `json:"subtitle,omitempty"`
	Subtitle2       *TextSnippet `json:"subtitle2,omitempty"`
	LeftIcon        *Icon        `json:"left_icon,omitempty"`
	RightIcon       *Icon        `json:"right_icon,omitempty"`
	RightTitle      *TextSnippet `json:"right_title,omitempty"`
	BackgroundColor *Color       `json:"bg_color,omitempty"`
	ClickAction     *ClickAction `json:"click_action,omitempty"`
	RightAction     *Button      `json:"right_action,omitempty"`
}
