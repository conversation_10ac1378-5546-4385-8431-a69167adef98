package sushi

type FitsoImageTextSnippetType12Snippet struct {
	Items   []*FitsoImageTextSnippetType12SnippetItem `json:"items,omitempty"`
	BgColor *Color                                    `json:"bg_color,omitempty"`
}

type FitsoImageTextSnippetType12SnippetItem struct {
	Title       *TextSnippet `json:"title,omitempty"`
	Subtitle1   *TextSnippet `json:"subtitle1,omitempty"`
	Image       *Image       `json:"image,omitempty"`
	RightButton *Button      `json:"right_button,omitempty"`
}
