package sushi

type FitsoTextSnippetType8Snippet struct {
	Title           *TextSnippet     `json:"title,omitempty"`
	BgColor         *Color           `json:"bg_color,omitempty"`
	BorderColor     *Color           `json:"border_color,omitempty"`
	Subtitle1       *TextSnippet     `json:"subtitle1,omitempty"`
	Subtitle2       *TextSnippet     `json:"subtitle2,omitempty"`
	Subtitle3       *TextSnippet     `json:"subtitle3,omitempty"`
	InfoIcon        *Icon            `json:"info_icon,omitempty"`
	RightTitle      *TextSnippet     `json:"right_title,omitempty"`
	RightSubtitle   *TextSnippet     `json:"right_subtitle,omitempty"`
	InfoText        *TextSnippet     `json:"info_text,omitempty"`
	IsSelected      bool             `json:"is_selected,omitempty"`
	IsSelectable    bool             `json:"is_selectable,omitempty"`
	Image           *Image           `json:"image,omitempty"`
	Tag             *Tag             `json:"tag,omitempty"`
	RightTag        *Tag             `json:"right_tag,omitempty"`
	Tag1            *Tag             `json:"tag1,omitempty"`
	ClickAction     *ClickAction     `json:"click_action,omitempty"`
	BottomContainer *BottomContainer `json:"bottom_container,omitempty"`
	BottomInfo      *Tag             `json:"bottom_info,omitempty"`
	Id              int32            `json:"id",omitempty`
}

type BottomContainer struct {
	Title          *TextSnippet `json:"title,omitempty"`
	RightTitle     *TextSnippet `json:"right_title,omitempty"`
	RightSubtitle  *TextSnippet `json:"right_subtitle,omitempty"`
	RightSubtitle2 *TextSnippet `json:"right_subtitle_2,omitempty"`
	LeftTitle      *TextSnippet `json:"left_title,omitempty"`
	LeftSubtitle   *TextSnippet `json:"left_subtitle,omitempty"`
	LeftSubtitle2  *TextSnippet `json:"left_subtitle_2,omitempty"`
}

type FitsoTextSnippetType8Layout struct {
	LayoutConfig          *LayoutConfig                 `json:"layout_config,omitempty"`
	FitsoTextSnippetType8 *FitsoTextSnippetType8Snippet `json:"fitso_text_snippet_type_8,omitempty"`
}
