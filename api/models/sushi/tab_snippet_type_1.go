package sushi

// TabSnippetType1Snippet represents the tab snippet type 1
type TabSnippetType1Snippet struct {
	Config *TabSnippetType1Config `json:"config,omitempty"`
	Items  *[]TabSnippetType1Item `json:"items,omitempty"`
}

// TabSnippetType1Item represents the tab snippet type 1 item
type TabSnippetType1Item struct {
	ID         string       `json:"id,omitempty"`
	Title      *TextSnippet `json:"title,omitempty"`
	IsSelected bool         `json:"is_selected,omitempty"`
}

// TabSnippetType1Config represents the tab snippet item type
type TabSnippetType1Config struct {
	BorderColor           *Color `json:"selected_border_color,omitempty"`
	UnSelectedBorderColor *Color `json:"unselected_border_color,omitempty"`
	BgColor               *Color `json:"selected_bg_color,omitempty"`
	UnselectedBgColor     *Color `json:"unselected_bg_color,omitempty"`
	Color                 *Color `json:"selected_title_color,omitempty"`
	UnSelectedColor       *Color `json:"unselected_title_color,omitempty"`
}
