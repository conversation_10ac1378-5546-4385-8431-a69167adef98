package sushi

type V2ImageTextSnippetType50Snippet struct {
	BgColor *Color                                 `json:"bg_color,omitempty"`
	Items   []*V2ImageTextSnippetType50SnippetItem `json:"items,omitempty"`
}

type V2ImageTextSnippetType50SnippetItem struct {
	Image       *Image       `json:"image,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	Subtitle    *TextSnippet `json:"subtitle,omitempty"`
	Subtitle1   *TextSnippet `json:"subtitle1,omitempty"`
	Subtitle2   *TextSnippet `json:"subtitle2,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}
