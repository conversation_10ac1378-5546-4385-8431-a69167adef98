package sushi

// TagLayoutType3Snippet represents the tag snippet type
type TagLayoutType3Snippet struct {
	BgColor *Color                       `json:"bg_color,omitempty"`
	Items   *[]TagLayoutType3SnippetItem `json:"items,omitempty"`
}

// TagLayoutType3SnippetItem represents the tag snippet item type
type TagLayoutType3SnippetItem struct {
	BorderColor *Color       `json:"border_color,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
}

// TagLayoutType3Layout represents TagLayoutType3Layout sushi layout
type TagLayoutType3Layout struct {
	LayoutConfig   *LayoutConfig          `json:"layout_config,omitempty"`
	TagLayoutType3 *TagLayoutType3Snippet `json:"tag_layout_type_3,omitempty"`
}
