package sushi

type FitsoImageTextSnippetType5Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType5 *FitsoImageTextSnippetType5Snippet `json:"fitso_image_text_snippet_type_5,omitempty"`
}

type FitsoImageTextSnippetType5Snippet struct {
	Items []*FitsoImageTextSnippetType5SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextSnippetType5SnippetItem struct {
	Image             *Image           `json:"image,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	Subtitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	Subtitle2         *TextSnippet     `json:"subtitle2,omitempty"`
	Subtitle3         *TextSnippet     `json:"subtitle3,omitempty"`
	Tag               *Tag             `json:"tag,omitempty"`
	BottomButton      *Button          `json:"bottom_button,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
