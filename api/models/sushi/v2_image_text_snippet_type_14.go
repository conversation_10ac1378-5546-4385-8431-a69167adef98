package sushi

type V2ImageTextType14Snippet struct {
	Item *[]V2ImageTextType14SnippetItem `json:"items,omitempty"`
}

type V2ImageTextType14SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type V2ImageTextSnippetType14Layout struct {
	LayoutConfig              *LayoutConfig                     `json:"layout_config,omitempty"`
	V2ImageTextSnippetType14  *V2ImageTextType14Snippet         `json:"v2_image_text_snippet_type_14,omitempty"`
	FitsoPurchaseSnippetType2 *FitsoPurchaseSnippetType2Snippet `json:"fitso_purchase_snippet_type_2,omitempty"`
	SectionHeaderType1 *SectionHeaderType1Snippet        `json:"section_header_type_1,omitempty"`
}
