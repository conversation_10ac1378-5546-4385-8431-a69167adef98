package sushi

type V2ImageTextType24Snippet struct {
	Item *[]V2ImageTextType24SnippetItem `json:"items,omitempty"`
}

type V2ImageTextType24SnippetItem struct {
	Title       *TextSnippet `json:"title,omitempty"`
	BgColor     *Color       `json:"bg_color,omitempty"`
	BorderColor *Color       `json:"border_color,omitempty"`
}

type V2ImageTextSnippetType24Layout struct {
	LayoutConfig             *LayoutConfig             `json:"layout_config,omitempty"`
	V2ImageTextSnippetType24 *V2ImageTextType24Snippet `json:"v2_image_text_snippet_type_24,omitempty"`
}
