package sushi

type FitsoImageTextSnippetType10Snippet struct {
	Items []*FitsoImageTextSnippetType10SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextSnippetType10SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	RightImage        *Image           `json:"right_image,omitempty"`
	Images            []*Image         `json:"images,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	Gradient          *Gradient        `json:"gradient,omitempty"`
	BottomButton      *Button          `json:"bottom_button,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
