package sushi

// V2ImageTextSnippetType43Snippet represents the type of image text snippet
type V2ImageTextSnippetType43Snippet struct {
	Items *[]V2ImageTextSnippetType43SnippetItem `json:"items,omitempty"`
	Id    string                                 `json:"id,omitempty"`
	Title *TextSnippet                           `json:"title,omitempty"`
}

// V2ImageTextSnippetType43SnippetItem represents the type of image text snippet item
type V2ImageTextSnippetType43SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Tag               *Tag             `json:"tag,omitempty"`
	SubTitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	SubTitle2         *TextSnippet     `json:"subtitle2,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

// ImageTextSnippetType43Layout represents ImageTextSnippetType43 sushi layout
type ImageTextSnippetType43Layout struct {
	LayoutConfig           *LayoutConfig                    `json:"layout_config,omitempty"`
	ImageTextSnippetType43 *V2ImageTextSnippetType43Snippet `json:"image_text_snippet_type_43,omitempty"`
}
