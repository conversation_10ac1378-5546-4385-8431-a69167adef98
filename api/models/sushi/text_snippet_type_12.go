package sushi

type TextSnippetType12Snippet struct {
	Title      *TextSnippet `json:"title,omitempty"`
	LinkConfig *LinkConfig  `json:"link_config,omitempty"`
}

type TextSnippetType12Layout struct {
	LayoutConfig     *LayoutConfig             `json:"layout_config,omitempty"`
	TextSnippetType1 *TextSnippetType12Snippet `json:"text_snippet_type_12,omitempty"`
}

type LinkConfig struct {
	Color *Color `json:"color,omitempty"`
}
