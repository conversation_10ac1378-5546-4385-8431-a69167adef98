package sushi

import (
	"context"

	common "bitbucket.org/jogocoin/go_api/api/common"
)

type AppsflyerItem struct {
	Payload    string      `json:"payload"`
	EventNames *EventNames `json:"event_names"`
}

func NewAppsflyerEvents() *EventNames {
	return &EventNames{}
}

func GetAppsflyerTrackItem(ctx context.Context, eventNames *EventNames) *AppsflyerItem {
	return &AppsflyerItem{
		EventNames: eventNames,
	}
}

func (item *AppsflyerItem) SetPayload(payload map[string]interface{}) {
	payloadStr := common.GetStringifyPayload(payload)
	item.Payload = payloadStr
}
