package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

type V2ImageTextSnippetType31Snippet struct {
	Title   *TextSnippet                           `json:"title,omitempty"`
	Items   *[]V2ImageTextSnippetType31SnippetItem `json:"items,omitempty"`
	Id      string                                 `json:"id,omitempty"`
	BgColor *Color                                 `json:"bg_color,omitempty"`
}

type V2ImageTextSnippetType31SnippetItem struct {
	Id               int32                                 `json:"id,omitempty"`
	Title            *TextSnippet                          `json:"title,omitempty"`
	BgColor          *Color                                `json:"bg_color,omitempty"`
	Subtitle         *TextSnippet                          `json:"subtitle,omitempty"`
	Subtitle1        *TextSnippet                          `json:"subtitle1,omitempty"`
	Subtitle2        *TextSnippet                          `json:"subtitle2,omitempty"`
	Tag              *Tag                                  `json:"tag,omitempty"`
	Rating           *RatingSnippetBlockItem               `json:"rating,omitempty"`
	Image            *Image                                `json:"image,omitempty"`
	IsInactive       bool                                  `json:"is_inactive,omitempty"`
	TopContainer     *V2ImageTextSnippetType31TopContainer `json:"top_container,omitempty"`
	BottomContainer  *BottomContainerSnippetItem           `json:"bottom_container,omitempty"`
	ClickAction      *ClickAction                          `json:"click_action,omitempty"`
	ClevertapItem    []*ClevertapItem                      `json:"clever_tap_tracking,omitempty"`
	JumboTracking    []*jumbo.Item                         `json:"jumbo_tracking,omitempty"`
	OverlayContainer *OverlayContainer                     `json:"overlay_container,omitempty"`
	LeftImage        *Image                                `json:"left_image,omitempty"`
}

type OverlayContainer struct {
	Title   *TextSnippet `json:"title,omitempty"`
	BgColor *Color       `json:"bg_color,omitempty"`
}

type V2ImageTextSnippetType31Layout struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	V2ImageTextSnippetType31 *V2ImageTextSnippetType31Snippet `json:"v2_image_text_snippet_type_31,omitempty"`
}

type V2ImageTextSnippetType31TopContainer struct {
	Title        *TextSnippet `json:"title,omitempty"`
	Image        *Image       `json:"image,omitempty"`
	BorderColor  *Color       `json:"border_color,omitempty"`
	BgColor      *Color       `json:"bg_color,omitempty"`
	CornerRadius int32        `json:"corner_radius,omitempty"`
}
