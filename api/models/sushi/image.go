package sushi

import "errors"

type ImageType string

const (
	ImageTypeCircle    ImageType = "circle"
	ImageTypeRounded   ImageType = "rounded"
	ImageTypeRectangle ImageType = "rectangle"
)

type FilterType string

const (
	FilterTypeGrayScale FilterType = "grayscale"
)

type Filter struct {
	Type  FilterType `json:"type,omitempty"`
	Data  string     `json:"data,omitempty"`
	Value int32      `json:"value,omitempty"`
}

// Image sushi item for image
type Image struct {
	Id               int32          `json:"id,omitempty"`
	URL              string         `json:"url,omitempty"`
	AspectRatio      float32        `json:"aspect_ratio,omitempty"`
	Type             ImageType      `json:"type,omitempty"`
	Height           int32          `json:"height,omitempty"`
	Width            int32          `json:"width,omitempty"`
	BgColor          *Color         `json:"bg_color,omitempty"`
	Animation        *Animation     `json:"animation,omitempty"`
	IsGrey           bool           `json:"is_grey,omitempty"`
	PlaceholderColor *Color         `json:"placeholder_color,omitempty"`
	Border           *BorderSnippet `json:"border,omitempty"`
	Filter           *Filter        `json:"filter,omitempty"`
}

// NewImage creates a new image item
func NewImage(url string) (*Image, error) {
	if url == "" {
		return nil, errors.New("Empty Image url")
	}
	return &Image{URL: url}, nil
}

func NewFilter(filterType FilterType) (*Filter, error) {
	if filterType == "" {
		return nil, errors.New("Empty filterType")
	}
	return &Filter{Type: filterType}, nil
}

func NewImageWithAnimation(animation *Animation) (*Image, error) {
	return &Image{Animation: animation}, nil
}

// SetAspectRatio sets the aspect ratio foor the image
func (image *Image) SetAspectRatio(aspectRatio float32) {
	image.AspectRatio = aspectRatio
}

func (image *Image) SetType(imageType ImageType) {
	image.Type = imageType
}

func (image *Image) SetHeight(height int32) {
	image.Height = height
}

func (image *Image) SetWidth(width int32) {
	image.Width = width
}

func (image *Image) SetColor(color *Color) {
	image.BgColor = color
}

func (image *Image) SetAnimation(animation *Animation) {
	image.Animation = animation
}

func (image *Image) SetIsGrey() {
	image.IsGrey = true
}

func (image *Image) SetPlaceholderColor(color *Color) {
	image.PlaceholderColor = color
}

func (image *Image) SetBorder(border *BorderSnippet) {
	image.Border = border
}

func (image *Image) SetFilter(filter *Filter) {
	image.Filter = filter
}
