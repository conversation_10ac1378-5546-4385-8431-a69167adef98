package sushi

type FitsoPurchaseSnippetType1Snippet struct {
	Title             *TextSnippet                            `json:"title,omitempty"`
	Items             []*FitsoPurchaseSnippetType1SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                        `json:"clever_tap_tracking,omitempty"`
}

type FitsoPurchaseSnippetType1SnippetItem struct {
	Id                int32                                       `json:"id,omitempty"`
	CornerRadius      int32                                       `json:"corner_radius,omitempty"`
	Title             *TextSnippet                                `json:"title,omitempty"`
	Subtitle          *TextSnippet                                `json:"subtitle,omitempty"`
	Subtitle1         *TextSnippet                                `json:"subtitle1,omitempty"`
	Subtitle2         *TextSnippet                                `json:"subtitle2,omitempty"`
	Subtitle3         *TextSnippet                                `json:"subtitle3,omitempty"`
	RightImage        *Image                                      `json:"right_image,omitempty"`
	Images            []*Image                                    `json:"images,omitempty"`
	BgColor           *Color                                      `json:"bg_color,omitempty"`
	BorderColor       *Color                                      `json:"border_color,omitempty"`
	Gradient          *Gradient                                   `json:"gradient,omitempty"`
	TopContainer      *Tag                                        `json:"top_container,omitempty"`
	BottomContainer   *Tag                                        `json:"bottom_container,omitempty"`
	ShowOfferTag      bool                                        `json:"show_offer_tag,omitempty"`
	Tag               *Tag                                        `json:"tag,omitempty"`
	Tag1              *Tag                                        `json:"tag1,omitempty"`
	RightTitle        *TextSnippet                                `json:"right_title,omitempty"`
	SubtitleList      []*FitsoTextSnippetType7SnippetSubtitleItem `json:"subtitles_list,omitempty"`
	ClickAction       *ClickAction                                `json:"click_action,omitempty"`
	BottomSeparator   *Separator                                  `json:"bottom_separator,omitempty"`
	ClevertapTracking []*ClevertapItem                            `json:"clever_tap_tracking,omitempty"`
}

type FitsoPurchaseSnippetType1Layout struct {
	LayoutConfig              *LayoutConfig                     `json:"layout_config,omitempty"`
	FitsoPurchaseSnippetType1 *FitsoPurchaseSnippetType1Snippet `json:"fitso_purchase_snippet_type_1,omitempty"`
}
