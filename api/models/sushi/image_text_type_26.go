package sushi

type ImageTextSnippetType26Snippet struct {
	Items *[]ImageTextSnippetType26SnippetItem `json:"items,omitempty"`
}

type ImageTextSnippetType26SnippetItem struct {
	Title       *TextSnippet   `json:"title,omitempty"`
	SubTitle1   *TextSnippet   `json:"subtitle1,omitempty"`
	Rating      *RatingSnippet `json:"rating,omitempty"`
	ClickAction *ClickAction   `json:"click_action,omitempty"`
	RightAction *Button        `json:"right_action,omitempty"`
}

type ImageTextSnippetType26Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType26 *ImageTextSnippetType26Snippet `json:"image_text_snippet_type_26,omitempty"`
}
