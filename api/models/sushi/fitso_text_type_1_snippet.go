package sushi

type FitsoTextSnippetType1ButtonType string

type FitsoTextSnippetType1ButtonOrientation string

type FitsoTextSnippetType1ButtonSize string

const (
	FitsoTextSnippetType1TypeSolid   FitsoTextSnippetType1ButtonType = "solid"
	FitsoTextSnippetType1TypeOutline FitsoTextSnippetType1ButtonType = "outline"
	FitsoTextSnippetType1TypeText    FitsoTextSnippetType1ButtonType = "text"
)

const (
	FitsoTextSnippetType1OrientationVertical   FitsoTextSnippetType1ButtonOrientation = "vertical"
	FitsoTextSnippetType1OrientationHorizontal FitsoTextSnippetType1ButtonOrientation = "horizontal"
)

const (
	FitsoTextSnippetType1ButtonSizeSmall  FitsoTextSnippetType1ButtonSize = "small"
	FitsoTextSnippetType1ButtonSizeMedium FitsoTextSnippetType1ButtonSize = "medium"
	FitsoTextSnippetType1ButtonSizeLarge  FitsoTextSnippetType1ButtonSize = "large"
)

type FitsoTextSnippetType1ButtonItem struct {
	Type              FitsoTextSnippetType1ButtonType `json:"type,omitempty"`
	Text              string                          `json:"text,omitempty"`
	Size              FitsoTextSnippetType1ButtonSize `json:"size,omitempty"`
	SuffixIcon        *Icon                           `json:"suffix_icon,omitempty"`
	ClickAction       *ClickAction                    `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem                `json:"clever_tap_tracking,omitempty"`
}

type FitsoTextSnippetType1Snippet struct {
	ButtonData *FitsoTextSnippetType1SnippetButton `json:"button_data,omitempty"`
}
type FitsoTextSnippetType1SnippetButton struct {
	Orientation FitsoTextSnippetType1ButtonOrientation `json:"orientation,omitempty"`
	Items       *[]FitsoTextSnippetType1ButtonItem     `json:"items,omitempty"`
}
