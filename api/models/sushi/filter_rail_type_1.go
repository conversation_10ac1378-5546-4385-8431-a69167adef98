package sushi

// FilterRailType1Snippet represents the filter snippet type
type FilterRailType1Snippet struct {
	Config *FilterRailType1SnippetConfig `json:"config,omitempty"`
	Items  *[]FilterRailType1SnippetItem `json:"items,omitempty"`
}

// FilterRailType1SnippetItem represents the filter snippet item type
type FilterRailType1SnippetItem struct {
	ID         string       `json:"id,omitempty"`
	Title      *TextSnippet `json:"title,omitempty"`
	IsSelected bool         `json:"is_selected,omitempty"`
}

// FilterRailType1SnippetConfig represents the filter snippet item type
type FilterRailType1SnippetConfig struct {
	BorderColor          *Color `json:"selected_border_color,omitempty"`
	DefaultBorderColor   *Color `json:"default_border_color,omitempty"`
	SelectedBgColor      *Color `json:"selected_bg_color,omitempty"`
	DefaultBgColor       *Color `json:"default_bg_color,omitempty"`
	SelectedTextColor    *Color `json:"selected_text_color,omitempty"`
	DefaultSelectedColor *Color `json:"default_text_color,omitempty"`
}
