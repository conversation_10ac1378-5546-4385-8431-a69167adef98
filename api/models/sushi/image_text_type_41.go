package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

// ImageTextSnippetType41Snippet represents the type of image text snippet
type ImageTextSnippetType41Snippet struct {
	Items *[]ImageTextSnippetType41SnippetItem `json:"items,omitempty"`
	Id    string                               `json:"id,omitempty"`
	Title *TextSnippet                         `json:"title,omitempty"`
}

// ImageTextSnippetType41SnippetItem represents the type of image text snippet item
type ImageTextSnippetType41SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Tag               *Tag             `json:"tag,omitempty"`
	SubTitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	JumboTracking	  []*jumbo.Item    `json:"jumbo_tracking,omitempty"`
}

// ImageTextSnippetType41Layout represents ImageTextSnippetType41 sushi layout
type ImageTextSnippetType41Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType41 *ImageTextSnippetType41Snippet `json:"image_text_snippet_type_41,omitempty"`
}
