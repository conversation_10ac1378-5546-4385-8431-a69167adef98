package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

type CustomAlert struct {
	Title              *TextSnippet      `json:"title,omitempty"`
	Subtitle           *TextSnippet      `json:"subtitle,omitempty"`
	IsBlocking         bool              `json:"is_blocking,omitempty"`
	DismissAfterAction bool              `json:"dismiss_after_action"`
	BlockAfterAction   bool              `json:"block_after_action,omitempty"`
	Image              *Image            `json:"image,omitempty"`
	AutoDismissData    *AutoDismissData  `json:"auto_dismiss_data,omitempty"`
	PositiveAction     *Button           `json:"positive_action,omitempty"`
	NegativeAction     *Button           `json:"negative_action,omitempty"`
	Message            *TextSnippet      `json:"message,omitempty"`
	ClevertapTracking  []*ClevertapItem  `json:"clever_tap_tracking,omitempty"`
	AppsflyerTracking  []*AppsflyerItem  `json:"apps_flyer_tracking,omitempty"`
	JumboTracking      []*jumbo.Item     `json:"jumbo_tracking,omitempty"`
	Subtitle2          *TextSnippet      `json:"subtitle2,omitempty"`
	OverlayAnimation   *OverlayAnimation `json:"overlay_animation,omitempty"`
	BottomContainer    *BottomContainer  `json:"bottom_container,omitempty"`
	PageName           string            `json:"page_name,omitempty"`
}

type AutoDismissData struct {
	DismissActionType ActionType   `json:"dismiss_action_type,omitempty"`
	Time              int          `json:"time,omitempty"`
	ClickAction       *ClickAction `json:"click_action,omitempty"`
}

type OverlayAnimation struct {
	URL string `json:"url"`
}
