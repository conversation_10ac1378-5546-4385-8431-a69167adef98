package sushi

type TextField struct {
	Placeholder *TextSnippet `json:"placeholder,omitempty"`
	MinLength   *MinLength   `json:"min_length,omitempty"`
}

type TextView struct {
	Placeholder *TextSnippet `json:"placeholder,omitempty"`
	MinLength   *MinLength   `json:"min_length,omitempty"`
}

type MinLength struct {
	Length    int32  `json:"length,omitempty"`
	ErrorText string `json:"error_text,omitempty"`
}
