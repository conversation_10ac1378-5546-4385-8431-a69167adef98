package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

// ResType3Snippet represents tyype 3 res snippet
type ResType3Snippet struct {
	FilterIds                   []string                      `json:"filter_ids,omitempty"`
	Title                       *TextSnippet                  `json:"title,omitempty"`
	Subtitle1                   *TextSnippet                  `json:"subtitle1,omitempty"`
	Subtitle2                   *TextSnippet                  `json:"subtitle2,omitempty"`
	Image                       *Image                        `json:"image,omitempty"`
	ClickAction                 *ClickAction                  `json:"click_action,omitempty"`
	RatingSnippets              *[]RatingSnippet              `json:"rating_snippets,omitempty"`
	TopLeftTag                  *Tag                          `json:"top_left_tag,omitempty"`
	TopRightTag                 *Tag                          `json:"top_right_tag,omitempty"`
	BottomRightTag              *Tag                          `json:"bottom_right_tag,omitempty"`
	MultiTag                    *MultiTagSnippet              `json:"multi_tag,omitempty"`
	TopTags                     *[]MultiTagSnippetItem        `json:"top_tags,omitempty"`
	BottomContainerSnippetItems *[]BottomContainerSnippetItem `json:"bottom_container_items,omitempty"`
	VerticalSubtitles           []*HorizontalSubtitleSnippet  `json:"vertical_subtitles,omitempty"`
	ClevertapTracking           []*ClevertapItem              `json:"clever_tap_tracking,omitempty"`
	JumboTracking    			[]*jumbo.Item                 `json:"jumbo_tracking,omitempty"`
}
