package sushi

type RadioContainerType1 struct {
	Id                    int32                               `json:"id,omitempty"`
	PostbackParams        string                              `json:"postback_params,omitempty"`
	Title                 *TextSnippet                        `json:"title,omitempty"`
	Subtitle              *TextSnippet                        `json:"subtitle,omitempty"`
	IsSelected            bool                                `json:"is_selected,omitempty"`
	VerticalTitleSubtitle *FitsoBottomSheetType1ResultSection `json:"vertical_title_subtitle,omitempty"`
	IsCheckboxDisabled    bool                                `json:"is_checkbox_disabled,omitempty"`
}
