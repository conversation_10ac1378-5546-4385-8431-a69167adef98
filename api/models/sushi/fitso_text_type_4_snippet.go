package sushi

type FitsoTextType4 struct {
	Items []*FitsoTextSnippetType4Item `json:"items,omitempty"`
}

type FitsoTextSnippetType4Item struct {
	Title                     *TextSnippet                          `json:"title,omitempty"`
	Subtitle                  *TextSnippet                          `json:"subtitle,omitempty"`
	BottomSeparatorData       *TextSnippet                          `json:"bottom_separator_data,omitempty"`
	TopSpacing                int32                                 `json:"top_spacing,omitempty"`
	Sublist                   []*FitsoBottomSheetType1ResultSection `json:"sublist,omitempty"`
	Subtitle1                 *TextSnippet                          `json:"subtitle1,omitempty"`
	Subtitle2                 *TextSnippet                          `json:"subtitle2,omitempty"`
	Subtitle3                 *TextSnippet                          `json:"subtitle3,omitempty"`
	Subtitle4                 *TextSnippet                          `json:"subtitle4,omitempty"`
	IsExpanded                bool                                  `json:"is_expanded,omitempty"`
	ExpandedData              *FitsoBottomSheetType1ResultSection   `json:"expanded_data,omitempty"`
	VerticalExpandedSubtitles []*TextSnippet                        `json:"vertical_expanded_subtitles,omitempty"`
}
