package sushi

type VerticalListType1 struct {
	Items      []VerticalListItem `json:"items,omitempty"`
	BgColor    *Color             `json:"bg_color,omitempty"`
	BorderData *BorderSnippet     `json:"border_data,omitempty"`
}

type VerticalListItem struct {
	LayoutConfig               *LayoutConfig                      `json:"layout_config,omitempty"`
	SectionHeaderType1         *SectionHeaderType1Snippet         `json:"section_header_type_1,omitempty"`
	FitsoImageTextSnippetType6 *FitsoImageTextSnippetType6Snippet `json:"fitso_image_text_snippet_type_6,omitempty"`
	FitsoTextSnippetType4      *FitsoTextType4                    `json:"fitso_text_snippet_type_4,omitempty"`
	FitsoTextSnippetType5      *FitsoTextType5                    `json:"fitso_text_snippet_type_5,omitempty"`
}
