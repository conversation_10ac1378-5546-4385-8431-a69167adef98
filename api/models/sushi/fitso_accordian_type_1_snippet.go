package sushi

type FitsoAccordianType1Snippet struct {
	Title        *TextSnippet                   `json:"title,omitempty"`
	Icon         *Icon                          `json:"icon,omitempty"`
	BottomButton *Button                        `json:"bottom_button,omitempty"`
	Images       []*Image                       `json:"images,omitempty"`
	Items        []*CustomTextSnippetTypeLayout `json:"items,omitempty"`
	PenaltyList  []*CustomTextSnippetTypeLayout `json:"penalty_list,omitempty"`
	Expanded     int32                          `json:"expanded,omitempty"`
}

// FitsoAccordianSnippetType1Layout represents FitsoAccordianType1 sushi layout
type FitsoAccordianSnippetType1Layout struct {
	LayoutConfig        *LayoutConfig               `json:"layout_config,omitempty"`
	FitsoAccordianType1 *FitsoAccordianType1Snippet `json:"fitso_accordion_snippet_type_1,omitempty"`
}
