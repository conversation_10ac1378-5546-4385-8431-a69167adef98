package sushi

import (
	"context"

	common "bitbucket.org/jogocoin/go_api/api/common"
	"bitbucket.org/jogocoin/go_api/api/structs"
)

type EventNames struct {
	Tap         string `json:"tap,omitempty"`
	Impression  string `json:"impression,omitempty"`
	PageSuccess string `json:"page_success,omitempty"`
	PageDismiss string `json:"page_dismiss,omitempty"`
}

type EnameData struct {
	Ename string `json:"ename"`
}

type ClevertapItem struct {
	Payload         string      `json:"payload"`
	EventNames      *EventNames `json:"event_names"`
	SendToAppsflyer bool        `json:"send_to_appsflyer,omitempty"`
}

func NewClevertapEvents() *EventNames {
	return &EventNames{}
}

func (e *EventNames) SetTap(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.Tap = enameStr
}

func (e *EventNames) SetImpression(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.Impression = enameStr
}

func (e *EventNames) SetPageSuccess(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.PageSuccess = enameStr
}

func (e *EventNames) SetPageDismiss(enameData *EnameData) {
	enameStr := common.GetStringifyPayload(enameData)
	e.PageDismiss = enameStr
}

func GetClevertapTrackItem(ctx context.Context, payload map[string]interface{}, eventNames *EventNames) *ClevertapItem {
	headers := ctx.Value("requestHeaders").(structs.Headers)
	deviceID := headers.DeviceID

	payload["device_id"] = deviceID

	payloadStr := common.GetStringifyPayload(payload)
	return &ClevertapItem{
		Payload:    payloadStr,
		EventNames: eventNames,
	}
}
