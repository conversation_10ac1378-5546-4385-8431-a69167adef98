package sushi

// AccordionSnippetType4Item represents the accordion snippet type 4
type AccordionSnippetType4Snippet struct {
	Image             *Image                              `json:"image,omitempty"`
	Title             *TextSnippet                        `json:"title,omitempty"`
	Items             []*AccordionSnippetType4SnippetItem `json:"items,omitempty"`
	Expanded          int32                               `json:"expanded,omitempty"`
	ClevertapTracking []*ClevertapItem                    `json:"clever_tap_tracking,omitempty"`
}

type AccordionSnippetType4SnippetItem struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	ImageTextSnippetType30   *ImageTextSnippetType30Snippet   `json:"image_text_snippet_type_30,omitempty"`
	ImageTextSnippetType35   *ImageTextSnippetType35Snippet   `json:"image_text_snippet_type_35,omitempty"`
	V2ImageTextSnippetType35 *V2ImageTextSnippetType35Snippet `json:"v2_image_text_snippet_type_35,omitempty"`
	V2ImageTextSnippetType36 *V2ImageTextSnippetType36Snippet `json:"v2_image_text_snippet_type_36,omitempty"`
	SnippetConfig            *SnippetConfig                   `json:"snippet_config,omitempty"`
}
