package sushi

type FitsoImageTextSnippetType16SnippetItem struct {
	Title          *TextSnippet   `json:"title,omitempty"`
	Image          *Image         `json:"image,omitempty"`
	Subtitle1      *TextSnippet   `json:"subtitle1,omitempty"`
	Subtitle2      *TextSnippet   `json:"subtitle2,omitempty"`
	Subtitle3      *TextSnippet   `json:"subtitle3,omitempty"`
	Tag            *Tag           `json:"tag,omitempty"`
	ClickAction    *ClickAction   `json:"click_action,omitempty"`
	BottomButton   *Button        `json:"bottom_button,omitempty"`
	InfoContainer  *InfoContainer `json:"info_container,omitempty"`
	InfoContainer2 *InfoContainer `json:"info_container_2,omitempty"`
}

type FitsoImageTextSnippetType16Snippet struct {
	Items []*FitsoImageTextSnippetType16SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextSnippetType16Layout struct {
	LayoutConfig           *LayoutConfig                       `json:"layout_config,omitempty"`
	ImageTextSnippetType16 *FitsoImageTextSnippetType16Snippet `json:"fitso_image_text_snippet_type_16,omitempty"`
}
