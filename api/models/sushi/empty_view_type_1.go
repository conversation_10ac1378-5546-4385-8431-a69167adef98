package sushi

// EmptyViewType1Snippet represents the type of empty view snippet
type EmptyViewType1Snippet struct {
	Title        *TextSnippet              `json:"title,omitempty"`
	Subtitle1    *TextSnippet              `json:"subtitle1,omitempty"`
	Image        *Image                    `json:"image,omitempty"`
	BottomButton *FooterSnippetType2Button `json:"bottom_button,omitempty"`
}

// EmptyViewType1Layout represents EmptyViewType1 sushi layout
type EmptyViewType1Layout struct {
	LayoutConfig          *LayoutConfig          `json:"layout_config,omitempty"`
	EmptyViewType1Snippet *EmptyViewType1Snippet `json:"empty_view_type_1,omitempty"`
}
