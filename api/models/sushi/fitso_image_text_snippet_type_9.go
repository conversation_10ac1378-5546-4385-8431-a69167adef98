package sushi

type FitsoImageTextSnippetType9Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType9 *FitsoImageTextSnippetType9Snippet `json:"fitso_image_text_snippet_type_9,omitempty"`
}

type FitsoImageTextSnippetType9Snippet struct {
	Items []*FitsoImageTextSnippetType9SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextSnippetType9SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	CornerRadius      int              `json:"corner_radius,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	HideGradient      bool             `json:"hide_gradient,omitempty"`
	PlaceholderFormat string           `json:"placeholder_format,omitempty"`
}
