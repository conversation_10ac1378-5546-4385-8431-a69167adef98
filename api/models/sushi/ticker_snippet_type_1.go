package sushi

type TickerSnippetType1Snippet struct {
	Items   *[]TickerSnippetType1SnippetItem `json:"items,omitempty"`
	Id      string                           `json:"id,omitempty"`
	BgColor *Color                           `json:"bg_color,omitempty"`
}

type TickerSnippetType1SnippetItem struct {
	LeftImage *Image       `json:"left_image,omitempty"`
	Title     *TextSnippet `json:"title,omitempty"`
}

type TickerSnippetType1Layout struct {
	LayoutConfig       *LayoutConfig              `json:"layout_config,omitempty"`
	TickerSnippetType1 *TickerSnippetType1Snippet `json:"ticker_snippet_type_1,omitempty"`
}
