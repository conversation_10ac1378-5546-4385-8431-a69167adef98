package sushi

type FitsoHeaderSnippetType1Snippet struct {
	Image     *Image       `json:"image,omitempty"`
	Title     *TextSnippet `json:"title,omitempty"`
	Tag       *Tag         `json:"tag,omitempty"`
	RightTag  *Tag         `json:"right_tag,omitempty"`
	Subtitle1 *TextSnippet `json:"subtitle1,omitempty"`
	Subtitle2 *TextSnippet `json:"subtitle2,omitempty"`
	Button    *Button      `json:"button,omitempty"`
	BgColor   *Color       `json:"bg_color,omitempty"`
	BottomTag *Tag         `json:"bottom_tag,omitempty"`
}
