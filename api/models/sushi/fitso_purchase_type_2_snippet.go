package sushi

type FitsoPurchaseSnippetType2Snippet struct {
	Title             *TextSnippet                            `json:"title,omitempty"`
	Subtitle          *TextSnippet                            `json:"subtitle,omitempty"`
	Items             []*FitsoPurchaseSnippetType1SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                        `json:"clever_tap_tracking,omitempty"`
}

type FitsoPurchaseSnippetType2Layout struct {
	LayoutConfig              *LayoutConfig                     `json:"layout_config,omitempty"`
	FitsoPurchaseSnippetType2 *FitsoPurchaseSnippetType2Snippet `json:"fitso_purchase_snippet_type_2,omitempty"`
}
