package sushi

import (
	"errors"
)

const (
	ClickActionDeeplink                         = "deeplink"
	ClickActionOpenWebview                      = "open_webview"
	ClickActionOpenMap                          = "open_map"
	ClickActionChangeBottomButton               = "change_bottom_button"
	ClickActionCall                             = "call"
	ApiCallMultiAction                          = "api_call_multi_action"
	ClickActionDismiss                          = "dismiss_page"
	ClickActionOpenInputBottomSheet             = "open_input_bottom_sheet"
	ClickActionOpenSheet                        = "open_sheet"
	ClickActionOpenSettings                     = "open_settings"
	ClickActionShare                            = "share"
	POSTRequestType                             = "post"
	ClickActionOpenCancellingBottomSheet        = "open_cancelling_bottom_sheet"
	ClickActionOpenAuth                         = "auth"
	ClickActionOpenReasonPickerSheet            = "open_reason_picker_sheet"
	ClickActionForceLoadMoreAndRemoveSnippet    = "force_load_more_and_remove_snippet"
	ClickActionLogout                           = "logout"
	ClickActionAppRate                          = "open_app_rating"
	ClickActionCustomAlert                      = "custom_alert"
	ClickActionOpenGenericBottomSheet           = "open_generic_bottom_sheet"
	ClickActionDismissAerobar                   = "dismiss_aerobar"
	ClickActionOpenPaymentInprogressBottomSheet = "open_payment_inprogress_bottom_sheet"
	ClickActionGoBack                           = "go_back"
	ClickActionSubmitFeedback                   = "submit_feedback"
	ClickActionOpenAgeBottomSheet               = "open_age_bottom_sheet"
	ClickActionDismissSafetyInfo                = "dismiss_safety_info_pages"
	ClickActionOpenPaymentCompletionPage        = "open_payment_completion_page"
	ClickActionOpenPlanSelectionBottomSheet     = "open_plan_selection_bottom_sheet"
	ClickActionSaveKey                          = "save_key"
	ClickActionOpenAddDetailsPage               = "open_add_details_page"
	ClickActionUserId                           = "user_id"
	ClickActionAddNewMember                     = "add_new_member"
	ClickActionShowToolTip                      = "show_tooltip"
	ClickActionOpenConfirmationBottomSheet      = "open_confirmation_bottom_sheet"
	ClickActionCallback                         = "perform_callback_click_action"
	ClickActionSelectPreferredCenter            = "select_preferred_center"
	ClickActionRefreshDetailsPage               = "refresh_details_page"
	ClickOpenSportSelectBottomSheet             = "open_sport_selection_bottom_sheet"
	ClickActionSaveMemberWithBackendData        = "save_member_with_backend_data"
	ClickActionProceedToCart                    = "proceed_to_cart"
	ClickActionOpenCart                         = "open_cart"
	ClickActionCopyToClipboard                  = "copy_to_clipboard"
	ClickActionOpenVideoPage                    = "open_video_page"
	ClickActionSkipLogin                        = "skip_login"
	ClickActionExpandSnippet                    = "expand_snippet"
	ClickActionUpdateSlotFacilityContextualData = "update_slot_facility_contextual_data"
	ClickActionAddGuest                         = "add_guest"
	ClickActionOpenContextualMenu               = "open_contextual_menu"
	DismissWithCustomAlert                      = "custom_alert"
	ClickActionRefreshPages                     = "refresh_pages"
	ClickActionUpdateAcademySlotPurchaseDetails = "update_academy_slot_purchase_details"
	ClickActionOpenAcademyPreferredPlan         = "open_academy_preferred_course"
	ClickActionOpenAcademyRecommendedCourse     = "open_academy_course_details"
	ClickActionOpenPromoTray                    = "open_promo_tray"
	ClickActionScrollDown                       = "scroll_down"
)

type PerformCallbackClickAction struct {
	PageType    string       `json:"page_type,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

type SelectPreferredCenter struct {
	Id                int32                   `json:"id,omitempty"`
	Title             *TextSnippet            `json:"title,omitempty"`
	SubTitle          *TextSnippet            `json:"subtitle,omitempty"`
	Tag               *Tag                    `json:"tag,omitempty"`
	Rating            *RatingSnippetBlockItem `json:"rating,omitempty"`
	Image             *Image                  `json:"image,omitempty"`
	PreferredCenterId int32                   `json:"preferred_center_id,omitempty"`
	PreferredSportId  int32                   `json:"preferred_sport_id,omitempty"`
	SnippetData       *SnippetData            `json:"snippet_data,omitempty"`
}

type SnippetData struct {
	Title     *TextSnippet `json:"title,omitempty"`
	SubTitle  *TextSnippet `json:"subtitle,omitempty"`
	SubTitle1 *TextSnippet `json:"subtitle1,omitempty"`
	LeftImage *Image       `json:"left_image,omitempty"`
}

// Deeplink represents deeplink object
type Deeplink struct {
	URL            string `json:"url"`
	PostbackParams string `json:"postback_params,omitempty"`
	Text           string `json:"text,omitempty"`
}

// OpenWebview represents open_webview object
type OpenWebview struct {
	Title          string `json:"title"`
	URL            string `json:"url"`
	InApp          bool   `json:"in_app"`
	PostbackParams string `json:"postback_params,omitempty"`
}

// OpenMap represents open map object
type OpenMap struct {
	Latitude            string `json:"latitude"`
	Longitude           string `json:"longitude"`
	ShouldExcludeInZoom bool   `json:"should_exclude_in_zoom,omitempty"`
}

type Call struct {
	Number string `json:"number"`
}

// ChangeBottomButton represents change bottom button object
type ChangeBottomButton struct {
	ButtonId string  `json:"button_id"`
	Button   *Button `json:"button"`
}

type UpdateBookingContextualDataButton struct {
	ButtonId       string                    `json:"button_id"`
	Button         *Button                   `json:"button"`
	ContextualData *ContextualData           `json:"contexual_data,omitempty"`
	Footer         *FooterSnippetType2Layout `json:"footer,omitempty"`
}

// APICallAction action to initiate an API call from App side
type APICallAction struct {
	RequestType  string `json:"type,omitempty"`
	URL          string `json:"url,omitempty"`
	Body         string `json:"body,omitempty"`
	HideProgress bool   `json:"hide_progress,omitempty"`
}

type Share struct {
	Text string `json:"text"`
	URL  string `json:"url"`
}

type Reason struct {
	Id                int32            `json:"id,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	IsSelected        bool             `json:"is_selected,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type OpenSheet struct {
	Type                  string                 `json:"type,omitempty"`
	OpenReasonPickerSheet *OpenReasonPickerSheet `json:"open_reason_picker_sheet,omitempty"`
}

type OpenReasonPickerSheet struct {
	Title  *TextSnippet                 `json:"title,omitempty"`
	Footer *CustomTextSnippetTypeLayout `json:"footer,omitempty"`
	Items  []*Reason                    `json:"items,omitempty"`
}

type CancelUserHeader struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type CancelledItemConfig struct {
	SelectedItemColor   *Color `json:"selected_item_color,omitempty"`
	UnselectedItemColor *Color `json:"unselected_item_color,omitempty"`
	SelectedIcon        *Icon  `json:"selected_icon,omitempty"`
}

type CancelledUserItem struct {
	Id                string           `json:"id,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	Images            []*Image         `json:"images,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	BookingOwner      bool             `json:"booking_owner,omitempty"`
}

type OpenCancelBottomSheet struct {
	Header       *CancelUserHeader                 `json:"header,omitempty"`
	Footer       *CustomTextSnippetTypeLayout      `json:"footer,omitempty"`
	ItemConfig   *CancelledItemConfig              `json:"item_config,omitempty"`
	UserList     []*CancelledUserItem              `json:"user_list,omitempty"`
	AddGuestData *GuestData                        `json:"add_guest_data,omitempty"`
	CancelAll    *CancelledUserItem                `json:"cancel_all,omitempty"`
	PostBody     string                            `json:"post_body,omitempty"`
	Results      []*V2ImageTextSnippetType36Layout `json:"results,omitempty"`
}

type GuestData struct {
	GuestData *FitsoImageTextSnippetType23Layout `json:"guest_data,omitempty"`
	ErrorData *TextSnippet                       `json:"error_data,omitempty"`
}

type CustomSelectionPopup struct {
	Results []*CustomTextSnippetTypeLayout `json:"results,omitempty"`
	Footer  *FooterSnippetType2Layout      `json:"footer,omitempty"`
}

type Auth struct {
	Title          *TextSnippet `json:"title,omitempty"`
	PostbackParams string       `json:"postback_params,omitempty"`
	Source         string       `json:"source,omitempty"`
}

type ForceLoadMoreAndRemoveSnippet struct {
	SnippetIds []string `json:"snippet_ids,omitempty"`
}

type GenericBottomSheetHeader struct {
	Image *Image       `json:"image,omitempty"`
	Title *TextSnippet `json:"title,omitempty"`
}

type OpenGenericBottomSheet struct {
	Header       *GenericBottomSheetHeader      `json:"header,omitempty"`
	Items        []*CustomTextSnippetTypeLayout `json:"items,omitempty"`
	BottomButton *FooterSnippetType2ButtonItem  `json:"bottom_button,omitempty"`
}

type OpenPaymentInprogressBottomSheet struct {
	Items []*V2ImageTextSnippetType36Layout `json:"items,omitempty"`
}

type MinAge struct {
	Age        int          `json:"age,omitempty"`
	ErrorTitle *TextSnippet `json:"error_title,omitempty"`
}

type MaxAge struct {
	Age        int          `json:"age,omitempty"`
	ErrorTitle *TextSnippet `json:"error_title,omitempty"`
}

type AgeFieldStates struct {
	Always *TextSnippetType1Snippet `json:"always,omitempty"`
	Empty  *TextSnippetType1Snippet `json:"empty,omitempty"`
	MinAge *MinAge                  `json:"min_age,omitempty"`
	MaxAge *MaxAge                  `json:"max_age,omitempty"`
}

type AgeField struct {
	Placeholder *TextSnippet    `json:"placeholder,omitempty"`
	States      *AgeFieldStates `json:"states,omitempty"`
}

type OpenAgeBottomSheet struct {
	Header   *GenericBottomSheetHeader `json:"header,omitempty"`
	Title    *TextSnippet              `json:"title,omitempty"`
	AgeField *AgeField                 `json:"age_field,omitempty"`
	Footer   *FooterSnippetType2Layout `json:"footer,omitempty"`
}

type SaveMemberAge struct {
	PostbackParams string `json:"postback_params,omitempty"`
}

type AddNewMember struct {
	Member *AcademyMember `json:"member,omitempty"`
}

type RefreshDetailsPage struct {
	UserId                     int32 `json:"user_id,omitempty"`
	CourseCategoryId           int32 `json:"course_category_id"`
	ProductId                  int32 `json:"product_id,omitempty"`
	ProductCategoryId          int32 `json:"product_category_id,omitempty"`
	SummercampProductMappingId int32 `json:"summercamp_product_mapping_id,omitempty"`
	CourseCategoryPostback 	   string `json:"course_category_postback,omitempty"`
}

type DismissPage struct {
	Type                         string                        `json:"type,omitempty"`
	SaveMemberAge                *SaveMemberAge                `json:"save_member_age,omitempty"`
	Deeplink                     *Deeplink                     `json:"deeplink,omitempty"`
	AddNewMember                 *AddNewMember                 `json:"add_new_member,omitempty"`
	RefreshPages                 []*RefreshPageItem            `json:"refresh_pages,omitempty"`
	DismissPage                  *DismissPage                  `json:"dismiss_page,omitempty"`
	RefreshDetailsPage           *RefreshDetailsPage           `json:"refresh_details_page,omitempty"`
	SavePlanDetails              *SavePlanDetails              `json:"save_plan_details,omitempty"`
	PerformCallbackClickAction   *PerformCallbackClickAction   `json:"perform_callback_click_action,omitempty"`
	AddGuest                     *AddGuestItem                 `json:"add_guest,omitempty"`
	CustomAlert                  *CustomAlert                  `json:"custom_alert,omitempty"`
	UpdateAcademyMemberList      *UpdateAcademyMemberList      `json:"update_academy_member_list,omitempty"`
	OpenAcademyRecommendedCourse *OpenAcademyRecommendedCourse `json:"open_academy_course_details,omitempty"`
	ApplyPromoCode               *ApplyPromoCode               `json:"apply_promo_code,omitempty"`
}

type UserData struct {
	UserId                     int32 `json:"user_id"`
	ProductId                  int32 `json:"product_id"`
	CourseCategoryId           int32 `json:"course_category_id"`
	SummercampProductMappingId int32 `json:"summercamp_product_mapping_id,omitempty"`
	ProductCategoryId          int32 `json:"product_category_id"`
	CourseCategoryPostback 	   string `json:"course_category_postback,omitempty"`
}

type OpenAcademyPreferredCourse struct {
	UserData                  *UserData `json:"user_data,omitempty"`
	IsRecommendedCourseOpened bool      `json:"is_recommended_course_opened"`
}

type OpenAcademyRecommendedCourse struct {
	UserData *UserData `json:"user_data,omitempty"`
}

type UpdateAcademyMemberList struct {
	UserId      int32           `json:"user_id,omitempty"`
	Title       *TextSnippet    `json:"title,omitempty"`
	Subtitles   []*SubtitleItem `json:"subtitles,omitempty"`
	RightButton *Button         `json:"right_button,omitempty"`
	UserObject  *UserObject     `json:"user_object,omitempty"`
}

type SubtitleItem struct {
	Type string `json:"type"`
	Item *Item  `json:"item,omitempty"`
}

type Item struct {
	Text  *TextSnippet `json:"text,omitempty"`
	Image *Image       `json:"image,omitempty"`
}

type UserObject struct {
	UserId   int32  `json:"user_id,omitempty"`
	Name     string `json:"name,omitempty"`
	Age      int32  `json:"age,omitempty"`
	IsParent bool   `json:"is_parent,omitempty"`
}

type DismissSafetyInfoPages struct {
	Type     string    `json:"type,omitempty"`
	Deeplink *Deeplink `json:"deeplink,omitempty"`
}

type ShowTooltip struct {
	Title    *TextSnippet `json:"title"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
	BgColor  *Color       `json:"bg_color,omitempty"`
}

type CopyToClipboard struct {
	Text string `json:"text"`
}

type ApplyPromoCode struct {
	PromoCode string `json:"promo_code"`
}

type OpenPromoTray struct {
	Header             *CouponTrayHeader        `json:"header,omitempty"`
	Sections           []*CouponTraySectionItem `json:"sections,omitempty"`
	Results            []*CouponTrayResultItem  `json:"results,omitempty"`
	BottomButtonStates *BottomButtonStates      `json:"bottom_button_states,omitempty"`
}

type CouponTrayResultItem struct {
	LayoutConfig          *LayoutConfig                 `json:"layout_config,omitempty"`
	SnippetConfig         *SnippetConfig                `json:"snippet_config,omitempty"`
	FitsoTextSnippetType3 *FitsoTextSnippetType3Snippet `json:"fitso_text_snippet_type_3,omitempty"`
}

type CouponTrayTextInput struct {
	Optional    bool         `json:"optional,omitempty"`
	Id          string       `json:"id,omitempty"`
	Placeholder *TextSnippet `json:"placeholder,omitempty"`
	RightButton *Button      `json:"right_button,omitempty"`
}
type CouponTrayHeader struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type CouponTraySectionItem struct {
	Type string               `json:"type"`
	Text *CouponTrayTextInput `json:"text,omitempty"`
}

// ClickAction represents the possible action to take based on type and data
type ClickAction struct {
	Type                             string                             `json:"type,omitempty"`
	IsBlocking                       bool                               `json:"is_blocking,omitempty"`
	Deeplink                         *Deeplink                          `json:"deeplink,omitempty"`
	OpenWebview                      *OpenWebview                       `json:"open_webview,omitempty"`
	OpenMap                          *OpenMap                           `json:"open_map,omitempty"`
	ChangeBottomButton               *ChangeBottomButton                `json:"change_bottom_button,omitempty"`
	Call                             *Call                              `json:"call,omitempty"`
	ApiCallMultiAction               *APICallAction                     `json:"api_call_multi_action,omitempty"`
	Share                            *Share                             `json:"share,omitempty"`
	OpenInputBottomSheetItem         *OpenInputBottomSheetItem          `json:"open_input_bottom_sheet,omitempty"`
	OpenSheet                        *OpenSheet                         `json:"open_sheet,omitempty"`
	OpenCancellingBottomSheet        *OpenCancelBottomSheet             `json:"open_cancelling_bottom_sheet,omitempty"`
	Auth                             *Auth                              `json:"auth,omitempty"`
	OpenReasonPickerSheet            *OpenReasonPickerSheet             `json:"open_reason_picker_sheet,omitempty"`
	FitsoBottomSheetType1            *FitsoBottomSheetType1             `json:"fitso_bottom_sheet_type_1,omitempty"`
	ForceLoadMoreAndRemoveSnippet    *ForceLoadMoreAndRemoveSnippet     `json:"force_load_more_and_remove_snippet,omitempty"`
	CustomAlert                      *CustomAlert                       `json:"custom_alert,omitempty"`
	OpenGenericBottomSheet           *OpenGenericBottomSheet            `json:"open_generic_bottom_sheet,omitempty"`
	OpenPaymentInprogressBottomSheet *OpenPaymentInprogressBottomSheet  `json:"open_payment_inprogress_bottom_sheet,omitempty"`
	DismissPage                      *DismissPage                       `json:"dismiss_page,omitempty"`
	OpenAgeBottomSheet               *OpenAgeBottomSheet                `json:"open_age_bottom_sheet,omitempty"`
	SaveMemberAge                    *SaveMemberAge                     `json:"save_member_age,omitempty"`
	DismissSafetyInfoPages           *DismissSafetyInfoPages            `json:"dismiss_safety_info_pages,omitempty"`
	OpenPaymentCompletionPage        *OpenPaymentCompletionPage         `json:"open_payment_completion_page,omitempty"`
	SaveKey                          *SaveKey                           `json:"save_key,omitempty"`
	OpenPlanSelectionBottomSheet     *FeaturesListBottomSheet           `json:"open_plan_selection_bottom_sheet,omitempty"`
	CustomSelectionPopup             *CustomSelectionPopup              `json:"custom_selection_popup,omitempty"`
	OpenAddDetailsPage               *OpenAddDetailsPage                `json:"open_add_details_page,omitempty"`
	UserId                           *UserId                            `json:"user_id,omitempty"`
	ShowTooltip                      *ShowTooltip                       `json:"show_tooltip,omitempty"`
	PerformCallbackClickAction       *PerformCallbackClickAction        `json:"perform_callback_click_action,omitempty"`
	SelectPreferredCenter            *SelectPreferredCenter             `json:"select_preferred_center,omitempty"`
	OpenSportSelectionBottomSheet    *SportSelectionBottomSheet         `json:"open_sport_selection_bottom_sheet,omitempty"`
	OpenConfirmationBottomSheet      *OpenConfirmationBottomSheet       `json:"open_confirmation_bottom_sheet,omitempty"`
	UpdateBookingConfirmContextData  *UpdateBookingContextualDataButton `json:"update_booking_confirmation_contexual_data,omitempty"`
	SaveMemberWithBackendData        *SaveMemberWithBackendData         `json:"save_member_with_backend_data,omitempty"`
	CopyToClipboard                  *CopyToClipboard                   `json:"copy_to_clipboard,omitempty"`
	OpenVideoPage                    *Video                             `json:"open_video_page,omitempty"`
	SavePlanDetails                  *SavePlanDetails                   `json:"save_plan_details,omitempty"`
	OpenContextualMenu               *OpenContextualMenu                `json:"open_contextual_menu,omitempty"`
	AddGuest                         *AddGuestItem                      `json:"add_guest,omitempty"`
	RefreshPages                     []*RefreshPageItem                 `json:"refresh_pages,omitempty"`
	UpdateSlotFacilityContextualData *UpdateSlotFacilityContextualData  `json:"update_slot_facility_contextual_data,omitempty"`
	UpdateAcademySlotPurchaseDetails *UpdateAcademySlotPurchaseDetails  `json:"update_academy_slot_purchase_details,omitempty"`
	OpenAcademyPreferredCourse       *OpenAcademyPreferredCourse        `json:"open_academy_preferred_course,omitempty"`
	OpenAcademyRecommendedCourse     *OpenAcademyRecommendedCourse      `json:"open_academy_course_details,omitempty"`
	OpenPromoTray                    *OpenPromoTray                     `json:"open_promo_tray,omitempty"`
}
type AddGuestItem struct {
	Title              *TextSnippet        `json:"title,omitempty"`
	BottomButtonStates *BottomButtonStates `json:"bottom_button_states,omitempty"`
	Sections           *[]SectionItem      `json:"sections,omitempty"`
}

type SectionItem struct {
	Type        string               `json:"type,omitempty"`
	Name        *AddGuestSectionItem `json:"name,omitempty"`
	Age         *AddGuestSectionItem `json:"age,omitempty"`
	Mobile      *AddGuestSectionItem `json:"mobile,omitempty"`
	Placeholder *TextSnippet         `json:"placeholder,omitempty"`
}

type AddGuestSectionItem struct {
	Optional    bool         `json:"optional"`
	Placeholder *TextSnippet `json:"placeholder,omitempty"`
	Value       string       `json:"value,omitempty"`
	States      *States      `json:"states,omitempty"`
}

type States struct {
	Empty      *EmptyState      `json:"empty,omitempty"`
	Always     *AlwaysState     `json:"always,omitempty"`
	MinAge     *MinAgeState     `json:"min_age,omitempty"`
	CharLength *CharLengthState `json:"char_length,omitempty"`
}

type EmptyState struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type AlwaysState struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type MinAgeState struct {
	Age        int32        `json:"age,omitempty"`
	ErrorTitle *TextSnippet `json:"error_title,omitempty"`
}

type CharLengthState struct {
	Length     int32        `json:"length,omitempty"`
	ErrorTitle *TextSnippet `json:"error_title,omitempty"`
}

type EditButtonCustom struct {
	Text        string       `json:"text,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

type EditInfoSectionFieldPlaceholderContainer struct {
	Title       *TextSnippet `json:"title,omitempty"`
	Icon        *Icon        `json:"icon,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

type PreferredCenterSectionItem struct {
	Title                *TextSnippet                              `json:"title,omitempty"`
	RightIcon            *Icon                                     `json:"icon,omitempty"`
	EditButton           *EditButtonCustom                         `json:"edit_button,omitempty"`
	PlaceholderContainer *EditInfoSectionFieldPlaceholderContainer `json:"placeholder_container,omitempty"`
	DurationInDays       int32                                     `json:"duration_in_days,omitempty"`
	Optional             bool                                      `json:"optional"`
}

type PlanEditInfoSection struct {
	Type            string                      `json:"type,omitempty"`
	PreferredCenter *PreferredCenterSectionItem `json:"preferred_center,omitempty"`
}

type SavePlanDetails struct {
	ProductId             int32                `json:"product_id,omitempty"`
	Duration              *TextSnippet         `json:"duration,omitempty"`
	ChangePlanButton      *Button              `json:"change_plan_button,omitempty"`
	Price                 *TextSnippet         `json:"price,omitempty"`
	PreferredCenterButton *PlanEditInfoSection `json:"preferred_center_button,omitempty"`
}

type SaveMemberWithBackendData struct {
	Type     string `json:"type,omitempty"`
	URL      string `json:"url,omitempty"`
	PostBody string `json:"post_body,omitempty"`
}

type OpenConfirmationBottomSheet struct {
	Header  *ConfirmBottomSheetHeader            `json:"header,omitempty"`
	Footer  *FooterSnippetType2Layout            `json:"footer,omitempty"`
	Results []*OpenConfirmationBottomSheetResult `json:"results,omitempty"`
}

type ConfirmBottomSheetHeader struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type OpenConfirmationBottomSheetResult struct {
	LayoutConfig                *LayoutConfig                       `json:"layout_config,omitempty"`
	HeaderSnippetType1          *SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
	FitsoImageTextSnippetType13 *FitsoImageTextSnippetType13Snippet `json:"fitso_image_text_snippet_type_13,omitempty"`
}

func (c *ClickAction) SetConfirmationBottomSheet(openBottomSheet *OpenConfirmationBottomSheet) {
	c.Type = ClickActionOpenConfirmationBottomSheet
	c.OpenConfirmationBottomSheet = openBottomSheet
}

type OpenInputBottomSheetItem struct {
	OpenInputBottomSheetItemHeader *OpenInputBottomSheetItemHeader `json:"header,omitempty"`
	Textfield                      *TextField                      `json:"text_field,omitempty"`
	TextView                       *TextView                       `json:"text_view,omitempty"`
	Button                         *Button                         `json:"bottom_button,omitempty"`
}

type SportSelectionBottomSheet struct {
	Button *Button                          `json:"bottom_button,omitempty"`
	Header *SportSelectionBottomSheetHeader `json:"header,omitempty"`
	Items  []*ImageTextSnippetType33Layout  `json:"items,omitempty"`
}

type SportSelectionBottomSheetHeader struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type OpenPaymentCompletionPageHeader struct {
	Button *Button `json:"button,omitempty"`
}

type OpenPaymentCompletionPage struct {
	Header  *OpenPaymentCompletionPageHeader          `json:"header,omitempty"`
	BgImage *Image                                    `json:"bg_image,omitempty"`
	Footer  *FooterSnippetType2Layout                 `json:"footer,omitempty"`
	Results []*OpenPaymentCompletionPageResultSection `json:"results,omitempty"`
}

type OpenPaymentCompletionPageResultSection struct {
	LayoutConfig               *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoHeaderSnippetType1    *FitsoHeaderSnippetType1Snippet    `json:"fitso_header_snippet_type_1,omitempty"`
	FitsoImageTextSnippetType8 *FitsoImageTextSnippetType8Snippet `json:"fitso_image_text_snippet_type_8,omitempty"`
}

type OpenPaymentCompletionPageResultItem struct {
	BgImage *Image `json:"bg_image,omitempty"`
}

type OpenInputBottomSheetItemHeader struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type ContextualData struct {
	SnippetId              int32           `json:"snippet_id,omitempty"`
	TitleText              string          `json:"title_text,omitempty"`
	SubtitleText           string          `json:"subtitle_text,omitempty"`
	RatingText             string          `json:"rating_text,omitempty"`
	ImageURL               string          `json:"image_url,omitempty"`
	BottomContainerText    string          `json:"bottom_container_text,omitempty"`
	AcademySlotId          int32           `json:"academy_slot_id,omitempty"`
	ProductId              int32           `json:"product_id,omitempty"`
	SlotInfo               []*SubtitleItem `json:"slot_info,omitempty"`
	FacilityInfoText       string          `json:"facility_info_text,omitempty"`
	FacilityId             int32           `json:"facility_id,omitempty"`
	ShouldShowWeatherAlert bool            `json:"should_show_weather_alert,omitempty"`
	SummercampSlotId       int32           `json:"summercamp_slot_id,omitempty"`
	ProductCategoryId      int32           `json:"product_category_id,omitempty"`
}

type UpdateSlotFacilityContextualData struct {
	ButtonId       string          `json:"button_id"`
	Button         *Button         `json:"button"`
	ContextualData *ContextualData `json:"contextual_data,omitempty"`
}

type OpenContextualMenu struct {
	CornerRadius int32                     `json:"corner_radius,omitempty"`
	Options      []*OpenContextualMenuItem `json:"options,omitempty"`
}

type OpenContextualMenuItem struct {
	Title       *TextSnippet `json:"title,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

type UpdateAcademySlotPurchaseDetails struct {
	AcademySlotId      int32               `json:"academy_slot_id,omitempty"`
	AcademySnippetData *AcademySnippetData `json:"snippet_data,omitempty"`
	SummercampSlotId   int32               `json:"summercamp_slot_id,omitempty"`
}

type AcademySnippetData struct {
	Id              int32                       `json:"id,omitempty"`
	Title           *TextSnippet                `json:"title,omitempty"`
	Subtitle        *TextSnippet                `json:"subtitle,omitempty"`
	Rating          *RatingSnippetBlockItem     `json:"rating,omitempty"`
	Image           *Image                      `json:"image,omitempty"`
	BottomContainer *BottomContainerSnippetItem `json:"bottom_container,omitempty"`
}

//NewTextClickAction creates a new TextClickAction
func NewTextClickAction(text string) (*ClickAction, error) {
	if text == "" {
		return nil, errors.New("Empty Text")
	}

	clickAction := &ClickAction{
		Type: text,
	}
	return clickAction, nil
}

// SetClickActionType sets the click action type
func (c *ClickAction) SetClickActionType(actionType string) {
	c.Type = actionType
}

// GetClickAction returns empty object
func GetClickAction() *ClickAction {
	return &ClickAction{}
}

// SetDeeplink sets the deeplink in click action
func (c *ClickAction) SetDeeplink(deeplink *Deeplink) {
	c.Type = ClickActionDeeplink
	c.Deeplink = deeplink
}

// SetOpenWebview sets the openWebview object in click action
func (c *ClickAction) SetOpenWebview(openWebview *OpenWebview) {
	c.Type = ClickActionOpenWebview
	c.OpenWebview = openWebview
}

func (c *ClickAction) SetOpenInputBottomSheet(sheet *OpenInputBottomSheetItem) {
	c.Type = ClickActionOpenInputBottomSheet
	c.OpenInputBottomSheetItem = sheet
}

// SetOpenMap sets google map object in click action
func (c *ClickAction) SetOpenMap(openMap *OpenMap) {
	c.Type = ClickActionOpenMap
	c.OpenMap = openMap
}

func (c *ClickAction) SetChangeBottomButton(changeBottomButton *ChangeBottomButton) {
	c.Type = ClickActionChangeBottomButton
	c.ChangeBottomButton = changeBottomButton
}

func (c *ClickAction) SetCall(call *Call) {
	c.Type = ClickActionCall
	c.Call = call
}

func (c *ClickAction) SetApiCallAction(apiCall *APICallAction) {
	c.Type = ApiCallMultiAction
	c.ApiCallMultiAction = apiCall
}

func (c *ClickAction) SetShare(share *Share) {
	c.Type = ClickActionShare
	c.Share = share
}

func (c *ClickAction) SetOpenSheet(openSheet *OpenSheet) {
	c.Type = ClickActionOpenSheet
	c.OpenSheet = openSheet
}

func (c *ClickAction) SetOpenCancelBottomSheet(openCancelBottomSheet *OpenCancelBottomSheet) {
	c.Type = ClickActionOpenCancellingBottomSheet
	c.OpenCancellingBottomSheet = openCancelBottomSheet
}

func (c *ClickAction) SetAuth(auth *Auth) {
	c.Type = ClickActionOpenAuth
	c.Auth = auth
}

func (c *ClickAction) SetOpenReasonPickerSheet(openReasonPickerSheet *OpenReasonPickerSheet) {
	c.Type = ClickActionOpenReasonPickerSheet
	c.OpenReasonPickerSheet = openReasonPickerSheet
}

func (c *ClickAction) SetForceLoadMoreAndRemoveSnippet(snippet *ForceLoadMoreAndRemoveSnippet) {
	c.Type = ClickActionForceLoadMoreAndRemoveSnippet
	c.ForceLoadMoreAndRemoveSnippet = snippet
}

func (c *ClickAction) SetCustomAlertAction(customAlert *CustomAlert) {
	c.Type = ClickActionCustomAlert
	c.CustomAlert = customAlert
}

func (c *ClickAction) SetGenericBottomSheet(openGenericBottomSheet *OpenGenericBottomSheet) {
	c.Type = ClickActionOpenGenericBottomSheet
	c.OpenGenericBottomSheet = openGenericBottomSheet
}

func (c *ClickAction) SetPaymentBottomSheet(openBottomSheet *OpenPaymentInprogressBottomSheet) {
	c.Type = ClickActionOpenPaymentInprogressBottomSheet
	c.OpenPaymentInprogressBottomSheet = openBottomSheet
}

func (c *ClickAction) SetPageDismiss(data *DismissPage) {
	c.Type = ClickActionDismiss
	c.DismissPage = data
}

func (c *ClickAction) SetDismissSafetyInfoPages(dismissSafetyInfoPages *DismissSafetyInfoPages) {
	c.Type = ClickActionDismissSafetyInfo
	c.DismissSafetyInfoPages = dismissSafetyInfoPages
}

// SetOpenPaymentCompletionPage sets payment completion page in click action
func (c *ClickAction) SetOpenPaymentCompletionPage(openPaymentCompletionPage *OpenPaymentCompletionPage) {
	c.Type = ClickActionOpenPaymentCompletionPage
	c.OpenPaymentCompletionPage = openPaymentCompletionPage
}

func (c *ClickAction) SetSaveKey(saveKey *SaveKey) {
	c.Type = ClickActionSaveKey
	c.SaveKey = saveKey
}

func (c *ClickAction) SetOpenPlanSelectionBottomSheet(bottomSheet *FeaturesListBottomSheet) {
	c.Type = ClickActionOpenPlanSelectionBottomSheet
	c.OpenPlanSelectionBottomSheet = bottomSheet
}

func (c *ClickAction) SetSportSelectBottomSheet(sportSelectionBottomSheet *SportSelectionBottomSheet) {
	c.Type = ClickOpenSportSelectBottomSheet
	c.OpenSportSelectionBottomSheet = sportSelectionBottomSheet
}

func (c *ClickAction) SetShowTooltip(showTooltip *ShowTooltip) {
	c.Type = ClickActionShowToolTip
	c.ShowTooltip = showTooltip
}

func (c *ClickAction) SetCopyToClipboard(copyToClipboard *CopyToClipboard) {
	c.Type = ClickActionCopyToClipboard
	c.CopyToClipboard = copyToClipboard
}

func (c *ClickAction) SetOpenVideoPage(video *Video) {
	c.Type = ClickActionOpenVideoPage
	c.OpenVideoPage = video
}

func (c *ClickAction) SetExpandSnippet() {
	c.Type = ClickActionExpandSnippet
}

func (c *ClickAction) SetSlotFacilityContextualData(updateSlotFacilityContextualData *UpdateSlotFacilityContextualData) {
	c.Type = ClickActionUpdateSlotFacilityContextualData
	c.UpdateSlotFacilityContextualData = updateSlotFacilityContextualData
}
func (c *ClickAction) SetOpenContextualMenu(menu *OpenContextualMenu) {
	c.Type = ClickActionOpenContextualMenu
	c.OpenContextualMenu = menu
}

func (c *ClickAction) SetAcademySlotPurchaseDetails(updateAcademySlotPurchaseDetails *UpdateAcademySlotPurchaseDetails) {
	c.Type = ClickActionUpdateAcademySlotPurchaseDetails
	c.UpdateAcademySlotPurchaseDetails = updateAcademySlotPurchaseDetails
}

func (c *ClickAction) SetOpenAcademyPreferredCourse(data *OpenAcademyPreferredCourse) {
	c.Type = ClickActionOpenAcademyPreferredPlan
	c.OpenAcademyPreferredCourse = data
}

func (c *ClickAction) SetOpenAcademyRecommendedCourse(data *OpenAcademyRecommendedCourse) {
	c.Type = ClickActionOpenAcademyRecommendedCourse
	c.OpenAcademyRecommendedCourse = data
}

func (c *ClickAction) SetOpenPromoTray(data *OpenPromoTray) {
	c.Type = ClickActionOpenPromoTray
	c.OpenPromoTray = data
}
