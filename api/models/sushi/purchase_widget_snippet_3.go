package sushi

type PurchaseWidgetSnippet3Item struct {
	TopContainer      *PurchaseWidgetItemTopContainer      `json:"top_container,omitempty"`
	BottomContainer   *PurchaseWidgetItemBottomContainer   `json:"bottom_container,omitempty"`
	ProgressContainer *PurchaseWidgetItemProgressContainer `json:"progress_container,omitempty"`
	Title             *TextSnippet                         `json:"title,omitempty"`
	Subtitle1         *TextSnippet                         `json:"subtitle1,omitempty"`
	Subtitle2         *TextSnippet                         `json:"subtitle2,omitempty"`
	ClevertapTracking []*ClevertapItem                     `json:"clever_tap_tracking,omitempty"`
	ClickAction       *ClickAction                         `json:"click_action,omitempty"`
	Gradient          *Gradient                            `json:"gradient,omitempty"`
	CornerRadius      int32                                `json:"corner_radius,omitempty"`
	BorderColor       *Color                               `json:"border_color,omitempty"`
}

type PurchaseWidgetItemTopContainer struct {
	Image     *Image       `json:"image,omitempty"`
	Title     *TextSnippet `json:"title,omitempty"`
	Subtitle  *TextSnippet `json:"subtitle,omitempty"`
	Subtitle1 *TextSnippet `json:"subtitle1,omitempty"`
}

type PurchaseWidgetItemBottomContainer struct {
	Title    *TextSnippet `json:"title,omitempty"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
	Button   *Button      `json:"button,omitempty"`
	Gradient *Gradient    `json:"gradient,omitempty"`
	BgColor  *Color       `json:"bg_color,omitempty"`
}

type PurchaseWidgetItemProgressContainer struct {
	Title1      *TextSnippet `json:"title1,omitempty"`
	Subtitle1   *TextSnippet `json:"subtitle1,omitempty"`
	Title2      *TextSnippet `json:"title2,omitempty"`
	Subtitle2   *TextSnippet `json:"subtitle2,omitempty"`
	ProgressBar *ProgressBar `json:"progress_bar,omitempty"`
}

type PurchaseWidgetSnippetType3 struct {
	Items []*PurchaseWidgetSnippet3Item `json:"items,omitempty"`
}
