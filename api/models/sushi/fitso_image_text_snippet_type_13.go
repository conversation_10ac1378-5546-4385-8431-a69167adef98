package sushi

type FitsoImageTextSnippetType13Layout struct {
	LayoutConfig        *LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextType8 *FitsoImageTextSnippetType13Snippet `json:"fitso_image_text_snippet_type_13,omitempty"`
}

type FitsoImageTextSnippetType13Snippet struct {
	Title           *TextSnippet                                `json:"title,omitempty"`
	Subtitle        *TextSnippet                                `json:"subtitle,omitempty"`
	Subtitle1       *TextSnippet                                `json:"subtitle1,omitempty"`
	Image           *Image                                      `json:"image,omitempty"`
	TopContainer    *FitsoImageTextSnippetType13TopContainer    `json:"top_container,omitempty"`
	BottomContainer *FitsoImageTextSnippetType13BottomContainer `json:"bottom_container,omitempty"`
}

type FitsoImageTextSnippetType13TopContainer struct {
	Image        *Image       `json:"image,omitempty"`
	Title        *TextSnippet `json:"title,omitempty"`
	BgColor      *Color       `json:"bg_color,omitempty"`
	BorderColor  *Color       `json:"border_color,omitempty"`
	CornerRadius int          `json:"corner_radius,omitempty"`
	Subtitle     *TextSnippet `json:"subtitle,omitempty"`
}

type FitsoImageTextSnippetType13BottomContainer struct {
	Image    *Image       `json:"image,omitempty"`
	Title    *TextSnippet `json:"title,omitempty"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
	BgColor  *Color       `json:"bg_color,omitempty"`
}
