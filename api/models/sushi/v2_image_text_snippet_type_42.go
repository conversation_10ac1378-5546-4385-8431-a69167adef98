package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

// V2ImageTextSnippetType42Snippet represents the type of image text snippet

type V2ImageTextSnippetType42Snippet struct {
	Items *[]V2ImageTextSnippetType42SnippetItem `json:"items,omitempty"`
	Id    string                                 `json:"id,omitempty"`
	Title *TextSnippet                           `json:"title,omitempty"`
}

// V2ImageTextSnippetType42SnippetItem represents the type of image text snippet item

type V2ImageTextSnippetType42SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Tag               *Tag             `json:"tag,omitempty"`
	SubTitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	RightImage        *Image           `json:"right_image,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item    `json:"jumbo_tracking,omitempty"`
}

// V2ImageTextSnippetType42Layout represents V2ImageTextSnippetType42 sushi layout

type V2ImageTextSnippetType42Layout struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	V2ImageTextSnippetType42 *V2ImageTextSnippetType42Snippet `json:"v2_image_text_snippet_type_42,omitempty"`
}
