package sushi

type FitsoTextType5 struct {
	Items []*FitsoTextSnippetType5Item `json:"items,omitempty"`
}

type FitsoTextSnippetType5Item struct {
	Title               *TextSnippet                          `json:"title,omitempty"`
	Subtitle            *TextSnippet                          `json:"subtitle,omitempty"`
	BottomSeparatorData *TextSnippet                          `json:"bottom_separator_data,omitempty"`
	TopSpacing          int32                                 `json:"top_spacing,omitempty"`
	Subtitle1           *TextSnippet                          `json:"subtitle1,omitempty"`
	Sublist             []*FitsoBottomSheetType1ResultSection `json:"sublist,omitempty"`
	Subtitle2           *TextSnippet                          `json:"subtitle2,omitempty"`
	IsExpanded          bool                                  `json:"is_expanded,omitempty"`
	Tag                 *Tag                                  `json:"tag,omitempty"`
}
