package sushi

type ImageTextSnippetType16SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	BorderColor       *Color           `json:"border_color,omitempty"`
	CornerRadius      int              `json:"corner_radius,omitempty"`
	RightIcon         *Icon            `json:"right_icon,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	ShouldHideShadow  bool             `json:"should_hide_shadow,omitempty"`
}

type ImageTextSnippetType16Snippet struct {
	Items []*ImageTextSnippetType16SnippetItem `json:"items,omitempty"`
}

type ImageTextSnippetType16Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType16 *ImageTextSnippetType16Snippet `json:"image_text_snippet_type_16,omitempty"`
}
