package sushi

type V2ImageTextType52Snippet struct {
	BgColor           *Color                          `json:"bg_color,omitempty"`
	Items             []*V2ImageTextType52SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                `json:"clever_tap_tracking,omitempty"`
}

type V2ImageTextType52SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	Image             *Image           `json:"image,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type V2ImageTextSnippetType52Layout struct {
	LayoutConfig           *LayoutConfig             `json:"layout_config,omitempty"`
	ImageTextSnippetType52 *V2ImageTextType52Snippet `json:"v2_image_text_snippet_type_52,omitempty"`
}
