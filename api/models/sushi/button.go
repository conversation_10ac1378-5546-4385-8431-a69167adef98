package sushi

import (
	"errors"

	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
)

type ButtonType string

const (
	ButtonTypeSolid      ButtonType = "solid"
	ButtonTypeOutlined   ButtonType = "outlined"
	ButtontypeText       ButtonType = "text"
	ButtonTypeUnderlined ButtonType = "underlined"
)

type ButtonSize string

const (
	ButtonSizeMedium ButtonSize = "medium"
	ButtonSizeSmall  ButtonSize = "small"
	ButtonSizeLarge  ButtonSize = "large"
)

type UnderlineType string

const (
	ButtonUnderlineTypeDashed UnderlineType = "dashed"
)

type ButtonAlignment string

const (
	ButtonAlignmentLeft   ButtonAlignment = "left"
	ButtonAlignmentRight  ButtonAlignment = "right"
	ButtonAlignmentCenter ButtonAlignment = "center"
)

type Button struct {
	Type                ButtonType       `json:"type,omitempty"`
	Text                string           `json:"text,omitempty"`
	Subtext             string           `json:"subtext,omitempty"`
	Size                ButtonSize       `json:"size,omitempty"`
	Font                *Font            `json:"font,omitempty"`
	Color               *Color           `json:"color,omitempty"`
	BgColor             *Color           `json:"bg_color,omitempty"`
	BorderColor         *Color           `json:"border_color,omitempty"`
	PrefixIcon          *Icon            `json:"prefix_icon,omitempty"`
	SuffixIcon          *Icon            `json:"suffix_icon,omitempty"`
	ClickAction         *ClickAction     `json:"click_action,omitempty"`
	ActiveClickAction   *ClickAction     `json:"active_click_action,omitempty"`
	InactiveClickAction *ClickAction     `json:"inactive_click_action,omitempty"`
	Alignment           ButtonAlignment  `json:"alignment,omitempty"`
	IsActionDisabled    int32            `json:"is_action_disabled,omitempty"`
	ButtonUnderline     *ButtonUnderline `json:"underline,omitempty"`
	ClevertapTracking   []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	JumboTracking       []*jumbo.Item    `json:"jumbo_tracking,omitempty"`
	ID                  string           `json:"id,omitempty"`
	IsEnabled           bool             `json:"is_enabled,omitempty"`
	IsSelected          bool             `json:"is_selected,omitempty"`
}

type ButtonUnderline struct {
	Type UnderlineType `json:"type,omitempty"`
}

func NewButton(buttonType ButtonType) (*Button, error) {
	if buttonType == ButtonType("") {
		return nil, errors.New("Invalid type")
	}
	return &Button{Type: buttonType}, nil
}

func (b *Button) SetText(text string) {
	b.Text = text
}

func (b *Button) SetSubtext(subtext string) {
	b.Subtext = subtext
}

func (b *Button) SetPrefixIcon(prefixIcon *Icon) {
	b.PrefixIcon = prefixIcon
}

func (b *Button) SetSuffixIcon(suffixIcon *Icon) {
	b.SuffixIcon = suffixIcon
}

func (b *Button) SetSize(size ButtonSize) {
	b.Size = size
}

func (b *Button) SetColor(color *Color) {
	b.Color = color
}

func (b *Button) SetClickAction(clickAction *ClickAction) {
	b.ClickAction = clickAction
}

func (b *Button) SetFont(font *Font) {
	b.Font = font
}

func (b *Button) SetAlignment(alignment ButtonAlignment) {
	b.Alignment = alignment
}

func (b *Button) SetActionDisabled() {
	b.IsActionDisabled = 1
}

func (b *Button) SetBgColor(bgColor *Color) {
	b.BgColor = bgColor
}

func (b *Button) SetBorderColor(borderColor *Color) {
	b.BorderColor = borderColor
}

func (b *Button) SetUnderline(underline *ButtonUnderline) {
	b.ButtonUnderline = underline
}

func (b *Button) AddClevertapTrackingItem(item *ClevertapItem) {
	if b.ClevertapTracking == nil {
		b.ClevertapTracking = make([]*ClevertapItem, 0)
	}
	b.ClevertapTracking = append(b.ClevertapTracking, item)
}

func (b *Button) AddJumboTrackingItem(item *jumbo.Item) {
	if item != nil {
		b.JumboTracking = append(b.JumboTracking, item)
	}
}

func (b *Button) SetID(id string) {
	b.ID = id
}
