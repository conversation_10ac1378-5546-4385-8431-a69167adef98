package sushi

// RatingType represents type of rating snippet
type RatingType string

const (
	// RatingTypeBlock represents type block
	RatingTypeBlock RatingType = "block"
	// RatingTypeTagV2 represents type tag_v2
	RatingTypeTagV2 RatingType = "tag_v2"
	// RatingTypeStars represents type stars
	RatingTypeStars RatingType = "stars"
)

// RatingSize represents rating box size
type RatingSize string

const (
	RatingSize500   RatingSize = "500"
	RatingSize300   RatingSize = "300"
	RatingSize400   RatingSize = "400"
	RatingSizeLarge RatingSize = "large"
)

// RatingSnippet represents rating snippet
type RatingSnippet struct {
	Type  RatingType              `json:"type,omitempty"`
	Block *RatingSnippetBlockItem `json:"block,omitempty"`
	TagV2 *RatingSnippetBlockItem `json:"tag_v2,omitempty"`
	Stars *RatingSnippetBlockItem `json:"stars,omitempty"`
}

// RatingSnippetBlockItem represents rating snippet block item
type RatingSnippetBlockItem struct {
	Title             *TextSnippet `json:"title,omitempty"`
	Subtitle          *TextSnippet `json:"subtitle,omitempty"`
	Icon              *Icon        `json:"icon,omitempty"`
	Color             *Color       `json:"star_bg_color,omitempty"`
	BgColor           *Color       `json:"bg_color,omitempty"`
	Size              RatingSize   `json:"size,omitempty"`
	RightIcon         *Icon        `json:"right_icon,omitempty"`
	PrefixIcon        *Icon        `json:"prefix_icon,omitempty"`
	StarColor         *Color       `json:"star_color,omitempty"`
	StarUnfilledColor *Color       `json:"star_unfilled_color,omitempty"`
	DefaultColor      *Color       `json:"default_color,omitempty"`
	Value             int32        `json:"value,omitempty"`
	Gradient          *Gradient    `json:"gradient,omitempty"`
}
