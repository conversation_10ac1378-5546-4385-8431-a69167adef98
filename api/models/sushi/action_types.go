package sushi

type ActionType string

const (
	ActionTypeDismiss                   ActionType = "dismiss_action_type"
	ActionTypeCustomAlert               ActionType = "custom_alert"
	ActionTypeBookingDismiss            ActionType = "booking_dismiss"
	ActionTypeFeedbackDismiss           ActionType = "feedback_dismiss"
	ActionTypeChangeBottomButton        ActionType = "change_bottom_button"
	ActionTypeDeeplink                  ActionType = "deeplink"
	ActionTypeBottomSheetDismiss        ActionType = "bottom_sheet_dismiss"
	ActionTypeRefreshPages              ActionType = "refresh_pages"
	ActionUpdateAcademyMemberList       ActionType = "update_academy_member_list"
	ActionTypePositive                  ActionType = "positive"
	ActionTypeCancelSuccess             ActionType = "cancel_success"
	ActionTypeOpenProfile               ActionType = "open_profile"
	ActionTypeApiCallMultiAction        ActionType = "api_call_multi_action"
	ActionTypeCreateUserSuccess         ActionType = "create_user_success"
	ActionTypeUserQuerySuccess          ActionType = "publish_support_email_success"
	ActionTypeUserQueryFailure          ActionType = "publish_support_email_failure"
	ActionTypeCancelMembership          ActionType = "cancel_membership_event"
	ActionTypeCartPendingpPaymentData   ActionType = "cart_pending_payment_data"
	ActionTypeSafetyInfoSubmitted       ActionType = "safety_info_submitted"
	ActionTypeDismissPage               ActionType = "dismiss_page"
	ActionTypeOpenOtpBottomSheet        ActionType = "open_otp_bottom_sheet"
	ActionTypeAddTrialPreselectedMember ActionType = "add_trial_preselected_member"
	ActionTypeShowToast                 ActionType = "show_toast"
)
