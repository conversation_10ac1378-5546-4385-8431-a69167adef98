package sushi

type FitsoBottomSheetType1 struct {
	Header            *FitsoBottomSheetType1Header          `json:"header,omitempty"`
	Results           []*FitsoBottomSheetType1ResultSection `json:"results,omitempty"`
	Footer            *FooterSnippetType2Layout             `json:"footer,omitempty"`
	ClevertapTracking []*ClevertapItem                      `json:"clever_tap_tracking,omitempty"`
}

type FitsoBottomSheetType1ResultSection struct {
	LayoutConfig        *LayoutConfig              `json:"layout_config,omitempty"`
	VerticalListType1   *VerticalListType1         `json:"vertical_list_type_1,omitempty"`
	SectionHeaderType1  *SectionHeaderType1Snippet `json:"section_header_type_1,omitempty"`
	FitsoPillsType1     *FitsoPillsType1           `json:"fitso_pills_type_1,omitempty"`
	TextSnippetType1    *TextSnippetType1Snippet   `json:"text_snippet_type_1,omitempty"`
	RadioContainerType1 *RadioContainerType1       `json:"radio_container_type_1,omitempty"`
}

type FitsoBottomSheetType1Header struct {
	Title *TextSnippet `json:"title,omitempty"`
}
