package sushi

type TextSnippetType8Snippet struct {
	BgColor       *Color         `json:"bg_color, omitempty"`
	Title         *TextSnippet   `json:"title,omitempty"`
	SubTitle1     *TextSnippet   `json:"subtitle1,omitempty"`
	SubTitle2     *TextSnippet   `json:"subtitle2,omitempty"`
	RatingSnippet *RatingSnippet `json:"rating_snippet,omitempty"`
	RightButton   *Button        `json:"right_button,omitempty"`
}

type TextSnippetType8Layout struct {
	LayoutConfig     *LayoutConfig            `json:"layout_config,omitempty"`
	TextSnippetType8 *TextSnippetType8Snippet `json:"text_snippet_type_8,omitempty"`
}
