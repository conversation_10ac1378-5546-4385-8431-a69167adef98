package sushi

type FitsoImageTextSnippetType17Snippet struct {
	Items             []*FitsoImageTextSnippetType17SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                          `json:"clever_tap_tracking,omitempty"`
}

type FitsoImageTextSnippetType17SnippetItem struct {
	Image        *Image          `json:"image,omitempty"`
	Title        *TextSnippet    `json:"title,omitempty"`
	BgColor      *Color          `json:"bg_color,omitempty"`
	Buttons      *CollapseExpand `json:"buttons,omitempty"`
	IsExpanded   bool            `json:"is_expanded"`
	ExpandedData *ExpandedData   `json:"expanded_data,omitempty"`
}

type CollapseExpand struct {
	Collapsed *Button `json:"collapsed,omitempty"`
	Expanded  *Button `json:"expanded,omitempty"`
}

type ExpandedData struct {
	BgColor      *Color                            `json:"bg_color,omitempty"`
	BorderColor  *Color                            `json:"border_color,omitempty"`
	CornerRadius int                               `json:"corner_radius,omitempty"`
	Items        *[]V2ImageTextSnippetType36Layout `json:"items,omitempty"`
}
