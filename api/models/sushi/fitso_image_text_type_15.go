package sushi

type FitsoImageTextType15Layout struct {
	LayoutConfig         *LayoutConfig                `json:"layout_config,omitempty"`
	FitsoImageTextType15 *FitsoImageTextType15Snippet `json:"fitso_image_text_snippet_type_15,omitempty"`
}

type FitsoImageTextType15Snippet struct {
	Items []*FitsoImageTextType15SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextType15SnippetItem struct {
	Image             *Image           `json:"image,omitempty"`
	Tag               *Tag             `json:"tag,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	PromoTag          *Tag             `json:"promo_tag,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	IsSelectable      bool             `json:"is_selectable,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
