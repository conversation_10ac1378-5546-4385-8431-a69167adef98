package sushi

type CustomTextSnippetTypeLayout struct {
	LayoutConfig                       *LayoutConfig                       `json:"layout_config,omitempty"`
	ImageTextSnippetType28             *ImageTextSnippetType28Snippet      `json:"image_text_snippet_type_28,omitempty"`
	ImageTextSnippetType41             *ImageTextSnippetType41Snippet      `json:"image_text_snippet_type_41,omitempty"`
	ImageTextSnippetType30             *ImageTextSnippetType30Snippet      `json:"image_text_snippet_type_30,omitempty"`
	FitsoImageTextSnippetType3         *FitsoImageTextSnippetType3Snippet  `json:"fitso_image_text_snippet_type_3,omitempty"`
	FitsoImageTextSnippetType4         *FitsoImageTextSnippetType4Snippet  `json:"fitso_image_text_snippet_type_4,omitempty"`
	FitsoImageTextSnippetType18        *FitsoImageTextSnippetType18Snippet `json:"fitso_image_text_snippet_type_18,omitempty"`
	FitsoImageTextSnippetType21        *FitsoImageTextSnippetType21Snippet `json:"fitso_image_text_snippet_type_21,omitempty"`
	SnippetConfig                      *SnippetConfig                      `json:"snippet_config,omitempty"`
	FooterSnippetType2                 *FooterSnippetType2Snippet          `json:"footer_snippet_type_2,omitempty"`
	ImageTextSnippetType33             *ImageTextSnippetType33Snippet      `json:"image_text_snippet_type_33,omitempty"`
	V2ImageTextSnippetType42           *V2ImageTextSnippetType42Snippet    `json:"v2_image_text_snippet_type_42,omitempty"`
	V2ImageTextSnippetType31           *V2ImageTextSnippetType31Snippet    `json:"v2_image_text_snippet_type_31,omitempty"`
	V2ImageTextSnippetType35           *V2ImageTextSnippetType35Snippet    `json:"image_text_snippet_type_35,omitempty"`
	FitsoTextSnippetType2              *FitsoTextSnippetType2Snippet       `json:"fitso_text_snippet_type_2,omitempty"`
	FitsoTextSnippetType7              *FitsoTextSnippetType7Snippet       `json:"fitso_text_snippet_type_7,omitempty"`
	FitsoTextSnippetType8              *FitsoTextSnippetType8Snippet       `json:"fitso_text_snippet_type_8,omitempty"`
	FitsoTextSnippetType9              *FitsoTextSnippetType9Snippet       `json:"fitso_text_snippet_type_9,omitempty"`
	V2ImageTextSnippetType10           *V2ImageTextSnippetType10Snippet    `json:"v2_image_text_snippet_type_10,omitempty"`
	V2ImageTextSnippetType24           *V2ImageTextType24Snippet           `json:"v2_image_text_snippet_type_24,omitempty"`
	SectionHeaderType1Snippet          *SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
	AccordionSnippetType4              *AccordionSnippetType4Snippet       `json:"accordion_snippet_type_4,omitempty"`
	AccordionSnippetType3              *AccordionSnippetType3Snippet       `json:"accordion_snippet_type_3,omitempty"`
	FitsoAccordionSnippetType2         *FitsoAccordianType2Snippet         `json:"fitso_accordion_snippet_type_2,omitempty"`
	V2ImageTextSnippetType36           *V2ImageTextSnippetType36Snippet    `json:"v2_image_text_snippet_type_36,omitempty"`
	FitsoFacilityCardType2             *FitsoFacilityCardType2Snippet      `json:"fitso_facility_card_type_2,omitempty"`
	TextSnippetType1Snippet            *TextSnippetType1Snippet            `json:"text_snippet_type_1,omitempty"`
	TextSnippetType12Snippet           *TextSnippetType12Snippet           `json:"text_snippet_type_12,omitempty"`
	FitsoImageTextType5                *FitsoImageTextSnippetType5Snippet  `json:"fitso_image_text_snippet_type_5,omitempty"`
	FitsoImageTextSnippetType16        *FitsoImageTextSnippetType16Snippet `json:"fitso_image_text_snippet_type_16,omitempty"`
	ImageTextSnippetType16             *ImageTextSnippetType16Snippet      `json:"image_text_snippet_type_16,omitempty"`
	Version2ImageTextSnippetType35     *V2ImageTextSnippetType35Snippet    `json:"v2_image_text_snippet_type_35,omitempty"`
	ImageTextSnippetType13             *ImageTextSnippetType13Snippet      `json:"image_text_snippet_type_13,omitempty"`
	FitsoToggleSnippetType1            *FitsoToggleSnippetType1Snippet     `json:"fitso_toggle_snippet_type_1,omitempty"`
	FitsoImageTextSnippetType23Snippet *FitsoImageTextSnippetType23Snippet `json:"fitso_text_snippet_type_10,omitempty"`
}
