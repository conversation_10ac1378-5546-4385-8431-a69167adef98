package sushi

type TextSnippetType1Snippet struct {
	Title     *TextSnippet `json:"title,omitempty"`
	Subtitle  *TextSnippet `json:"subtitle,omitempty"`
	Subtitle1 *TextSnippet `json:"subtitle1,omitempty"`
	BgColor   *Color       `json:"bg_color,omitempty"`
}

type TextSnippetType1Layout struct {
	LayoutConfig     *LayoutConfig            `json:"layout_config,omitempty"`
	TextSnippetType1 *TextSnippetType1Snippet `json:"text_snippet_type_1,omitempty"`
}
