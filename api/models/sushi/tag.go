package sushi

import "errors"

// TagTitle represents tag title
type TagTitle string

type TagSize string

const TagSizeTiny TagSize = "tiny"
const TagSizeMedium TagSize = "medium"
const TagSizeSmall TagSize = "small"

const TagTypeSmall string = "small"

// Tag represents sushi tag item
type Tag struct {
	Title        *TextSnippet `json:"title,omitempty"`
	Subtitle     *TextSnippet `json:"subtitle,omitempty"`
	Transparency float32      `json:"transparency,omitempty"`
	BgColor      *Color       `json:"bg_color,omitempty"`
	PrefixIcon   *Icon        `json:"prefix_icon,omitempty"`
	RightIcon    *Icon        `json:"right_icon,omitempty"`
	Size         TagSize      `json:"size,omitempty"`
	BorderColor  *Color       `json:"border_color,omitempty"`
	Image        *Image       `json:"image,omitempty"`
	Gradient     *Gradient    `json:"gradient,omitempty"`
	Type         string       `json:"type,omitempty"`
	ClickAction  *ClickAction `json:"click_action,omitempty"`
}

const (
	// TagTitlePromoted represents promoted tag title
	TagTitlePromoted TagTitle = "promoted"
)

// NewTag creates a new sushi tag
func NewTag(text string) (*Tag, error) {
	if text == "" {
		return nil, errors.New("Empty Text")
	}

	textSnippet := &TextSnippet{
		Text: text,
	}

	tag := &Tag{
		Title: textSnippet,
	}

	return tag, nil
}

//SetTransparency sets the color of the TextSnippet
func (t *Tag) SetTransparency(transparency float32) {
	t.Transparency = transparency
}

func (t *Tag) SetBgColor(bgColor *Color) {
	t.BgColor = bgColor
}

func (t *Tag) SetBorderColor(borderColor *Color) {
	t.BorderColor = borderColor
}

func (t *Tag) SetType(tagType string) {
	t.Type = tagType
}
