package sushi

// ImageTextSnippetType35Snippet represents the type of image text snippet
type ImageTextSnippetType35Snippet struct {
	Items *[]ImageTextSnippetType35SnippetItem `json:"items,omitempty"`
}

// ImageTextSnippetType35SnippetItem represents the type of image text snippet item
type ImageTextSnippetType35SnippetItem struct {
	Title *TextSnippet `json:"title,omitempty"`
}

// ImageTextSnippetType35Layout represents ImageTextSnippetType35 sushi layout
type ImageTextSnippetType35Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType35 *ImageTextSnippetType35Snippet `json:"image_text_snippet_type_35,omitempty"`
}
