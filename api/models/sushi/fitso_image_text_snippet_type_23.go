package sushi

type FitsoImageTextSnippetType23Layout struct {
	LayoutConfig                       *LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextSnippetType23Snippet *FitsoImageTextSnippetType23Snippet `json:"fitso_image_text_snippet_type_23,omitempty"`
}

type FitsoImageTextSnippetType23Snippet struct {
	CornerRadius int32                                     `json:"corner_radius,omitempty"`
	Title        *TextSnippet                              `json:"title,omitempty"`
	BorderColor  *Color                                    `json:"border_color,omitempty"`
	BgColor      *Color                                    `json:"bg_color,omitempty"`
	Items        *[]FitsoImageTextSnippetType23SnippetItem `json:"items,omitempty"`
	RightButton  *Button                                   `json:"right_button,omitempty"`
	Checkbox     *Checkbox                                 `json:"checkbox,omitempty"`
	Tag          *Tag                                      `json:"tag,omitempty"`
}

type FitsoImageTextSnippetType23SnippetItem struct {
	ID            int32        `json:"id"`
	Image         *Image       `json:"image"`
	Title         *TextSnippet `json:"title"`
	Subtitle      *TextSnippet `json:"subtitle,omitempty"`
	IsSelected    bool         `json:"is_selected,omitempty"`
	IsSelectable  bool         `json:"is_selectable,omitempty"`
	PostbackParam string       `json:"postback_params,omitempty"`
	RightButton   *Button      `json:"right_button,omitempty"`
}
