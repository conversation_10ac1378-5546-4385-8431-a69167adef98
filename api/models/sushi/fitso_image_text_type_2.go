package sushi

// FitsoImageTextSnippetType2Snippet represents the type of fitso image text snippet item
type FitsoImageTextSnippetType2SnippetItem struct {
	TopContainer    *FitsoImageTextSnippetType2TopContainer    `json:"top_container,omitempty"`
	CenterContainer *FitsoImageTextSnippetType2CenterContainer `json:"center_container,omitempty"`
	InfoContainer   *FitsoImageTextSnippetType2InfoContainer   `json:"info_container,omitempty"`
	BottomContainer *FitsoImageTextSnippetType2BottomContainer `json:"bottom_container,omitempty"`
	AlertContainer  *FitsoImageTextSnippetType2Alert           `json:"alert_container,omitempty"`
	RatingContainer *FitsoImageTextSnippetType2RatingContainer `json:"rating_container,omitempty"`
}

// FitsoImageTextSnippetType2Snippet represents the type of fitso image text snippet
type FitsoImageTextSnippetType2Snippet struct {
	Items *[]FitsoImageTextSnippetType2SnippetItem `json:"items,omitempty"`
}

// FitsoImageTextSnippetType2Layout represents FitsoImageTextSnippetType2 sushi layout
type FitsoImageTextSnippetType2Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType2 *FitsoImageTextSnippetType2Snippet `json:"fitso_image_text_snippet_type_2,omitempty"`
}

type FitsoImageTextSnippetType2TopContainer struct {
	Image    *Image       `json:"image,omitempty"`
	Title    *TextSnippet `json:"title,omitempty"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
	Button   *Button      `json:"button,omitempty"`
	Tag      *Tag         `json:"tag,omitempty"`
}

type FitsoImageTextSnippetType2CenterContainer struct {
	Title    *TextSnippet `json:"title,omitempty"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
	Button   *Button      `json:"button,omitempty"`
}

type FitsoImageTextSnippetType2InfoContainer struct {
	Title        *TextSnippet `json:"title"`
	SubTitle     *TextSnippet `json:"subtitle"`
	Image        *Image       `json:"image"`
	BgColor      *Color       `json:"bg_color"`
	BorderColor  *Color       `json:"border_color"`
	CornerRadius int32        `json:"corner_radius"`
}

type FitsoImageTextSnippetType2BottomContainer struct {
	Title1    *TextSnippet `json:"title1,omitempty"`
	Subtitle1 *TextSnippet `json:"subtitle1,omitempty"`
	Title2    *TextSnippet `json:"title2,omitempty"`
	Subtitle2 *TextSnippet `json:"subtitle2,omitempty"`
}

type FitsoImageTextSnippetType2Alert struct {
	BorderColor *Color       `json:"border_color,omitempty"`
	BgColor     *Color       `json:"bg_color,omitempty"`
	Image       *Image       `json:"image,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
}

type FitsoImageTextSnippetType2RatingContainer struct {
	IsRated         bool                                             `json:"is_rated,omitempty"`
	BottomContainer *FitsoImageTextSnippetType2RatingBottomContainer `json:"bottom_container,omitempty"`
	BorderColor     *Color                                           `json:"border_color,omitempty"`
	BgColor         *Color                                           `json:"bg_color,omitempty"`
	Title           *TextSnippet                                     `json:"title,omitempty"`
	Subtitle        *TextSnippet                                     `json:"subtitle,omitempty"`
	RatingStars     []*Button                                        `json:"rating_stars,omitempty"`
}

type FitsoImageTextSnippetType2RatingBottomContainer struct {
	Tag         *Tag         `json:"tag,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	RightButton *Button      `json:"right_button,omitempty"`
}
