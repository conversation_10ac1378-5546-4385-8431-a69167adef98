package sushi

import "errors"

type GradientDirection string

const HorizontalDirection GradientDirection = "horizontal"

// Gradient represents sushi gradient item
type Gradient struct {
	Colors    *[]Color          `json:"colors"`
	Direction GradientDirection `json:"direction,omitempty"`
}

// NewGradient creates a new gradient
func NewGradient(colors []Color) (*Gradient, error) {
	if len(colors) == 0 {
		return nil, errors.New("No color provided")
	}

	gradient := &Gradient{
		Colors: &colors,
	}
	return gradient, nil
}
