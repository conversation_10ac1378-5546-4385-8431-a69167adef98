package sushi

type AcademyMember struct {
	UserId        int32          `json:"user_id,omitempty"`
	DisplayMember *DisplayMember `json:"display_member,omitempty"`
	MemberObject  *MemberObject  `json:"member_object,omitempty"`
	EditButton    *EditButton    `json:"edit_button,omitempty"`
}

type DisplayMember struct {
	Name         *TextSnippet `json:"name,omitempty"`
	DisplayAge   *TextSnippet `json:"age,omitempty"`
	Phone        *TextSnippet `json:"phone,omitempty"`
	IsSelectable bool         `json:"is_selectable"`
	IsSelected   bool         `json:"is_selected"`
	InfoText     *TextSnippet `json:"info_text,omitempty"`
	ErrorText    *TextSnippet `json:"error_text,omitempty"`
	BgColor      *Color       `json:"bg_color,omitempty"`
}

type MemberObject struct {
	UserId int32  `json:"user_id,omitempty"`
	Name   string `json:"name,omitempty"`
	Age    int32  `json:"age,omitempty"`
	Phone  string `json:"phone,omitempty"`
}

type EditButton struct {
	Text              string           `json:"text"`
	ClickAction       *ClickAction     `json:"click_action"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
