package sushi

type MediaType string

const MEDIA_TYPE_VIDEO MediaType = "video"
const MEDIA_TYPE_IMAGE MediaType = "image"

type MediaType2SnippetLayout struct {
	LayoutConfig *LayoutConfig      `json:"layout_config,omitempty"`
	MediaType2   *MediaType2Snippet `json:"media_snippet_type_2,omitempty"`
}

type MediaType2Snippet struct {
	Items             []*MediaType2SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem         `json:"clever_tap_tracking,omitempty"`
}

type MediaType2SnippetItem struct {
	MediaContent *MediaContent `json:"media_content,omitempty"`
	ClickAction  *ClickAction  `json:"click_action,omitempty"`
}

type MediaContent struct {
	MediaType MediaType `json:"media_type"`
	Video     *Video    `json:"video,omitempty"`
	Image     *Image    `json:"image,omitempty"`
}
