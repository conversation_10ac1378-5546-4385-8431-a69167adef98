package sushi

type FitsoImageTextSnippetType7Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType7 *FitsoImageTextSnippetType7Snippet `json:"fitso_image_text_snippet_type_7,omitempty"`
}

type FitsoImageTextSnippetType7Snippet struct {
	Items *[]FitsoImageTextSnippetType7SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextSnippetType7SnippetItem struct {
	Image        *Image       `json:"image,omitempty"`
	Title        *TextSnippet `json:"title,omitempty"`
	BgColor     *Color       `json:"bg_color,omitempty"`
	BorderColor *Color       `json:"border_color,omitempty"`
	RightButton *Button      `json:"right_button,omitempty"`
	RightIcon   *Icon        `json:"right_icon,omitempty"`
	ClickAction  *ClickAction `json:"click_action,omitempty"`
}
