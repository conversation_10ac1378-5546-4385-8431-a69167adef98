package sushi

// ImageTextSnippetType10SnippetItem represents the type of image text snippet item
type ImageTextSnippetType10SnippetItem struct {
	Image       *Image       `json:"image,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	Subtitle1   *TextSnippet `json:"subtitle1,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

// ImageTextSnippetType10Snippet represents the type of image text snippet
type ImageTextSnippetType10Snippet struct {
	Title *TextSnippet                         `json:"title,omitempty"`
	Items *[]ImageTextSnippetType10SnippetItem `json:"items,omitempty"`
}

// ImageTextSnippetType10Layout represents ImageTextSnippetType10 sushi layout
type ImageTextSnippetType10Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType10 *ImageTextSnippetType10Snippet `json:"image_text_snippet_type_10,omitempty"`
}
