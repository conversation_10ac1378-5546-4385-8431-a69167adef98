package sushi

type BorderType string

type BorderRadius int32

type BorderWidth float32

const (
	BorderTypeRounded BorderType = "rounded"
)

const (
	BorderRadius8 BorderRadius = 8
)

type BorderSnippet struct {
	Type   BorderType   `json:"type,omitempty"`
	Radius BorderRadius `json:"radius,omitempty"`
	Color  *Color       `json:"color,omitempty"`
	Colors *[]Color     `json:"colors,omitempty"`
	Width  BorderWidth  `json:"width,omitempty"`
}

//NewBorderSnippet creates a new BorderSnippet
func NewBorderSnippet() (*BorderSnippet, error) {
	borderSnippet := &BorderSnippet{}
	return borderSnippet, nil
}

//SetBorderColor sets the color of the Border
func (b *BorderSnippet) SetBorderColor(color *Color) {
	b.Color = color
}

//SetBorderTyoe sets the type of the Border
func (b *BorderSnippet) SetBorderType(border_type BorderType) {
	b.Type = border_type
}

//SetBorderRadius sets the radius of the Border
func (b *BorderSnippet) SetBorderRadius(radius BorderRadius) {
	b.Radius = radius
}

//SetBorderWidth sets the width of the Border
func (b *BorderSnippet) SetBorderWidth(width BorderWidth) {
	b.Width = width
}
