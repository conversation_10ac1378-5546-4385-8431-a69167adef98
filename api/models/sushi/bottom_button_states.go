package sushi

type BottomButtonStates struct {
	Disabled  *BottomButtonState `json:"disabled,omitempty"`
	Edited    *BottomButtonState `json:"edited,omitempty"`
	Completed *BottomButtonState `json:"completed,omitempty"`
	Enabled   *BottomButtonState `json:"enabled,omitempty"`
}

type BottomButtonState struct {
	Button     *Button `json:"button,omitempty"`
	PluralText string  `json:"plural_text,omitempty"`
}
