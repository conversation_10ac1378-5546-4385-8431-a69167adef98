package sushi

// ColorType represents color type
type ColorType string

const (
	// ColorTypeGrey represents grey color
	ColorTypeGrey ColorType = "grey"
	// ColorTypeBlack represents black color
	ColorTypeBlack ColorType = "black"
	// ColorTypeWhite represents white color
	ColorTypeWhite ColorType = "white"
	// ColorTypeRed represents red color
	ColorTypeRed ColorType = "red"
	// ColorTypeZRed represents zRed color
	ColorTypeZRed ColorType = "zRed"
	// ColorTypePink represents pink color
	ColorTypePink ColorType = "pink"
	// ColorTypeTeal represents teal color
	ColorTypeTeal ColorType = "teal"
	// ColorTypeBlue represents blue color
	ColorTypeBlue ColorType = "blue"
	// ColorTypeOrange represents orange color
	ColorTypeOrange ColorType = "orange"
	// ColorTypeYellow represents yellow color
	ColorTypeYellow ColorType = "yellow"
	// ColorTypeLime represents lime color
	ColorTypeLime ColorType = "lime"
	// ColorTypeGreen represents green color
	ColorTypeGreen ColorType = "green"
	// ColorTypePurple represents purple color
	ColorTypePurple ColorType = "purple"
	// ColorTypeIndigo represents indigo color
	ColorTypeIndigo ColorType = "indigo"
	//ColorTypeCider represents cider color
	ColorTypeCider ColorType = "cider"
	//ColorTypeCyan represents cyan color
	ColorTypeCyan ColorType = "cyan"
	//ALERT_LIGHT_THEME represents alert_light theme
	ALERT_LIGHT_THEME ColorType = "alert_light"
	//ALERT_DARK_THEME represents alert_dark theme
	ALERT_DARK_THEME ColorType = "alert_dark"
	//NEUTRAL_LIGHT_THEME represents neutral_light theme
	NEUTRAL_LIGHT_THEME ColorType = "neutral_light"
	//NEUTRAL_DARK_THEME represents neutral_dark theme
	NEUTRAL_DARK_THEME ColorType = "neutral_dark"
)

// ColorTint represents color tint
type ColorTint string

const (
	// ColorTint000 represents 000 color tint
	ColorTint000 ColorTint = "000"
	// ColorTint50 represents 050 color tint
	ColorTint50 ColorTint = "050"
	// ColorTint100 represents 100 color tint
	ColorTint100 ColorTint = "100"
	// ColorTint200 represents 200 color tint
	ColorTint200 ColorTint = "200"
	// ColorTint300 represents 300 color tint
	ColorTint300 ColorTint = "300"
	// ColorTint400 represents 400 color tint
	ColorTint400 ColorTint = "400"
	// ColorTint500 represents 500 color tint
	ColorTint500 ColorTint = "500"
	// ColorTint600 represents 600 color tint
	ColorTint600 ColorTint = "600"
	// ColorTint700 represents 700 color tint
	ColorTint700 ColorTint = "700"
	// ColorTint800 represents 800 color tint
	ColorTint800 ColorTint = "800"
	// ColorTint900 represents 900 color tint
	ColorTint900 ColorTint = "900"
)

// Color represents sushi color item
type Color struct {
	Tint         ColorTint `json:"tint,omitempty"`
	Type         ColorType `json:"type,omitempty"`
	Alpha        float32   `json:"alpha,omitempty"`
	Transparency float32   `json:"transparency,omitempty"`
}

// NewColor Gives a new sushi color object.
func NewColor(colorType ColorType, tint ColorTint) (*Color, error) {
	c := &Color{
		Type: colorType,
		Tint: tint,
	}

	return c, nil
}
