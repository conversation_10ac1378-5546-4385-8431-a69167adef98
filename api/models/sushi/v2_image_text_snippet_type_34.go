package sushi

type V2ImageTextSnippetType34Snippet struct {
	Items *[]V2ImageTextSnippetType34SnippetItem `json:"items,omitempty"`
}

type V2ImageTextSnippetType34SnippetButtonData struct {
	Items []*Button `json:"items,omitempty"`
}

type V2ImageTextSnippetType34SnippetItem struct {
	Title           *TextSnippet                               `json:"title,omitempty"`
	Subtitle        *TextSnippet                               `json:"subtitle,omitempty"`
	Tag             *Tag                                       `json:"tag,omitempty"`
	Rating          *RatingSnippetBlockItem                    `json:"rating,omitempty"`
	Image           *Image                                     `json:"image,omitempty"`
	Images          []*Image                                   `json:"images,omitempty"`
	BottomContainer *BottomContainerSnippetItem                `json:"bottom_container,omitempty"`
	ClickAction     *ClickAction                               `json:"click_action,omitempty"`
	ButtonData      *V2ImageTextSnippetType34SnippetButtonData `json:"button_data,omitempty"`
}

type V2ImageTextSnippetType34Layout struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	V2ImageTextSnippetType34 *V2ImageTextSnippetType34Snippet `json:"v2_image_text_snippet_type_34,omitempty"`
}
