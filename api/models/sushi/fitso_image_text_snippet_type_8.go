package sushi

type FitsoImageTextSnippetType8Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType8 *FitsoImageTextSnippetType8Snippet `json:"fitso_image_text_snippet_type_8,omitempty"`
}

type FitsoImageTextSnippetType8Snippet struct {
	TopContainer    *FitsoImageTextSnippetType8TopContainer    `json:"top_container,omitempty"`
	BottomContainer *FitsoImageTextSnippetType8BottomContainer `json:"bottom_container,omitempty"`
}

type FitsoImageTextSnippetType8TopContainer struct {
	Image            *Image                                              `json:"image,omitempty"`
	Title            *TextSnippet                                        `json:"title,omitempty"`
	Tag              *Tag                                                `json:"tag,omitempty"`
	Subtitle         *TextSnippet                                        `json:"subtitle,omitempty"`
	TimelineData     *FitsoImageTextSnippetType8TopContainerTimelineData `json:"timeline_data,omitempty"`
	DetailsContainer *DetailsContainer                                   `json:"details_container,omitempty"`
}

type DetailsContainer struct {
	Title *TextSnippet   `json:"title,omitempty"`
	Tag   *Tag           `json:"tag,omitempty"`
	Items []*TextSnippet `json:"items,omitempty"`
}

type FitsoImageTextSnippetType8TopContainerTimelineData struct {
	Title         *TextSnippet `json:"title,omitempty"`
	Subtitle      *TextSnippet `json:"subtitle,omitempty"`
	RightTitle    *TextSnippet `json:"right_title,omitempty"`
	RightSubtitle *TextSnippet `json:"right_subtitle,omitempty"`
}

type FitsoImageTextSnippetType8BottomContainer struct {
	Image    *Image       `json:"image,omitempty"`
	Title    *TextSnippet `json:"title,omitempty"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
}
