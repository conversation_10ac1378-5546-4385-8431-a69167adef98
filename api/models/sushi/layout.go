package sushi

import "errors"

const (
	// LayoutTypeGrid represents a grid layout
	LayoutTypeGrid LayoutType = "grid"
	// LayoutTypeCarousel represents a carousel layout
	LayoutTypeCarousel LayoutType = "carousel"
)

const (
	// SectionHeaderType1 represents section_header_type_1
	SectionHeaderType1 SnippetType = "section_header_type_1"
	// FitsoAccordionSnippetType1 represents fitso_accordion_snippet_type_1
	FitsoAccordionSnippetType1 SnippetType = "fitso_accordion_snippet_type_1"
	// FitsoAccordionSnippetType2 represents fitso_accordion_snippet_type_2
	FitsoAccordionSnippetType2 SnippetType = "fitso_accordion_snippet_type_2"
	// AccordionSnippetType3 represents accordion_snippet_type_3
	AccordionSnippetType3 SnippetType = "accordion_snippet_type_3"
	// AccordionSnippetType4 represents accordion_snippet_type_4
	AccordionSnippetType4 SnippetType = "accordion_snippet_type_4"
	// TagLayoutType2 represents tag_layout_type_2
	TagLayoutType2 SnippetType = "tag_layout_type_2"
	// TagLayoutType3 represents tag_layout_type_3
	TagLayoutType3 SnippetType = "tag_layout_type_3"
	// FooterSnippetType2 represents footer_snippet_type_2
	FooterSnippetType2 SnippetType = "footer_snippet_type_2"
	// TextSnippetType1 represents text_snippet_type_1
	TextSnippetType1 SnippetType = "text_snippet_type_1"

	// TextButtonSnippetType3 represents text_button_snippet_type_3
	TextButtonSnippetType3 SnippetType = "text_button_snippet_type_3"

	// TextSnippetType12 represents text_snippet_type_12
	TextSnippetType12 SnippetType = "text_snippet_type_12"
	// ImageTextSnippetType9 represents image_text_snippet_type_9
	ImageTextSnippetType9 SnippetType = "image_text_snippet_type_9"
	// ImageTextSnippetType30 represents image_text_snippet_type_30
	ImageTextSnippetType30 SnippetType = "image_text_snippet_type_30"
	// ImageTextSnippetType32 represents image_text_snippet_type_32
	ImageTextSnippetType32 SnippetType = "image_text_snippet_type_32"
	// ImageTextSnippetType19 represents image_text_snippet_type_19
	ImageTextSnippetType19 SnippetType = "image_text_snippet_type_19"
	// ImageTextSnippetType10 represents image_text_snippet_type_10
	ImageTextSnippetType10 SnippetType = "image_text_snippet_type_10"
	// ImageTextSnippetType41 represents image_text_snippet_type_41
	ImageTextSnippetType41 SnippetType = "image_text_snippet_type_41"
	// V2ImageTextSnippetType42 represents v2_image_text_snippet_type_42
	V2ImageTextSnippetType42 SnippetType = "v2_image_text_snippet_type_42"
	// ImageTextSnippetType33 represents image_text_snippet_type_33
	ImageTextSnippetType33 SnippetType = "image_text_snippet_type_33"
	// ImageTextSnippetType35 represents image_text_snippet_type_35
	ImageTextSnippetType35 SnippetType = "image_text_snippet_type_35"
	// ImageTextSnippetType26 represents image_text_snippet_type_26
	ImageTextSnippetType26 SnippetType = "image_text_snippet_type_26"
	// ImageTextSnippetType28 represents image_text_snippet_type_28
	ImageTextSnippetType28 SnippetType = "image_text_snippet_type_28"
	// V2ImageTextSnippetType24 represents v2_image_text_snippet_type_24
	V2ImageTextSnippetType24 SnippetType = "v2_image_text_snippet_type_24"
	// V2ImageTextSnippetType31 represents v2_image_text_snippet_type_31
	V2ImageTextSnippetType31 SnippetType = "v2_image_text_snippet_type_31"
	// V2ImageTextSnippetType35 represents v2_image_text_snippet_type_35
	V2ImageTextSnippetType35 SnippetType = "v2_image_text_snippet_type_35"
	// ResSnippetType3 represents v2_res_snippet_type_3
	ResSnippetType3 SnippetType = "v2_res_snippet_type_3"
	// FitsoFacilityCardType2 represets fitso_facility_card_type_2
	FitsoFacilityCardType2 SnippetType = "fitso_facility_card_type_2"
	// HeaderSnippetType4 represents header_snippet_type_4
	HeaderSnippetType4 SnippetType = "header_snippet_type_4"
	// V2ImageTextSnippetType10 represents v2_image_text_snippet_type_10
	V2ImageTextSnippetType10 SnippetType = "v2_image_text_snippet_type_10"
	// V2ImageTextSnippetType34 represents v2_image_text_snippet_type_34
	V2ImageTextSnippetType34 SnippetType = "v2_image_text_snippet_type_34"
	// V2ImageTextSnippetType36 represents v2_image_text_snippet_type_36
	V2ImageTextSnippetType36 SnippetType = "v2_image_text_snippet_type_36"
	// V2ImageTextSnippetType43 represents v2_image_text_snippet_type_43
	V2ImageTextSnippetType43 SnippetType = "v2_image_text_snippet_type_43"
	// V2ImageTextSnippetType49 represents v2_image_text_snippet_type_49
	V2ImageTextSnippetType49 SnippetType = "v2_image_text_snippet_type_49"
	// V2ImageTextSnippetType50 represents v2_image_text_snippet_type_50
	V2ImageTextSnippetType50 SnippetType = "v2_image_text_snippet_type_50"
	// V2ImageTextSnippetType52 represents v2_image_text_snippet_type_52
	V2ImageTextSnippetType52 SnippetType = "v2_image_text_snippet_type_52"
	// FilterRailType1 represents filter_rail_snippet_type_1
	FilterRailType1 SnippetType = "filter_rail_snippet_type_1"
	// TabSnippetType2 represents tab_snippet_type_2
	TabSnippetType2 SnippetType = "tab_snippet_type_2"
	// TabSnippetType1 represents tab_snippet_type_1
	TabSnippetType1 SnippetType = "tab_snippet_type_1"
	// TickerSnippetType1 represents ticker_snippet_type_1
	TickerSnippetType1 SnippetType = "ticker_snippet_type_1"
	// TextSnippetType8 represents text_snippet_type_8
	TextSnippetType8 SnippetType = "text_snippet_type_8"
	// EmptyViewType1 represents empty_view_type_1
	EmptyViewType1 SnippetType = "empty_view_type_1"
	// FitsoHeaderSnippetType1 represents fitso_header_snippet_type_1
	FitsoHeaderSnippetType1 SnippetType = "fitso_header_snippet_type_1"
	// FitsoTextSnippetType1 represents fitso_text_snippet_type_1
	FitsoTextSnippetType1 SnippetType = "fitso_text_snippet_type_1"
	// FitsoTextSnippetType2 represents fitso_text_snippet_type_2
	FitsoTextSnippetType2 SnippetType = "fitso_text_snippet_type_2"
	// FitsoTextSnippetType3 represents fitso_text_snippet_type_3
	FitsoTextSnippetType3 SnippetType = "fitso_text_snippet_type_3"
	// FitsoTextSnippetType4 represents fitso_text_snippet_type_4
	FitsoTextSnippetType4 SnippetType = "fitso_text_snippet_type_4"
	// FitsoTextSnippetType5 represents fitso_text_snippet_type_5
	FitsoTextSnippetType5 SnippetType = "fitso_text_snippet_type_5"
	// FitsoTextSnippetType6 represents fitso_text_snippet_type_6
	FitsoTextSnippetType6 SnippetType = "fitso_text_snippet_type_6"
	// FitsoTextSnippetType7 represents fitso_text_snippet_type_7
	FitsoTextSnippetType7 SnippetType = "fitso_text_snippet_type_7"
	// FitsoTextSnippetType8 represents fitso_text_snippet_type_8
	FitsoTextSnippetType8 SnippetType = "fitso_text_snippet_type_8"
	// FitsoTextSnippetType9 represents fitso_text_snippet_type_9
	FitsoTextSnippetType9 SnippetType = "fitso_text_snippet_type_9"
	// FitsoTextSnippetType11 represents fitso_text_snippet_type_11
	FitsoTextSnippetType11 SnippetType = "fitso_text_snippet_type_11"

	// FitsoImageTextSnippetType2 represents fitso_image_text_snippet_type_2
	FitsoImageTextSnippetType2 SnippetType = "fitso_image_text_snippet_type_2"
	// FitsoImageTextSnippetType3 represents fitso_image_text_snippet_type_3
	FitsoImageTextSnippetType3 SnippetType = "fitso_image_text_snippet_type_3"
	// FitsoImageTextSnippetType4 represents fitso_image_text_snippet_type_4
	FitsoImageTextSnippetType4 SnippetType = "fitso_image_text_snippet_type_4"
	// FitsoImageTextSnippetType18 represents fitso_image_text_snippet_type_18
	FitsoImageTextSnippetType18 SnippetType = "fitso_image_text_snippet_type_18"
	// FitsoImageTextSnippetType21 represents fitso_image_text_snippet_type_21
	FitsoImageTextSnippetType21 SnippetType = "fitso_image_text_snippet_type_21"
	// VerticalListType1 represents vertical_list_type_1
	VerticalListSnipppetType1 SnippetType = "vertical_list_type_1"
	// fitso_pills_type_1 represents fitso_pills_type_1
	FitsoPillsSnippetType1 SnippetType = "fitso_pills_type_1"
	// RadioContainerSnippetType1 represents radio_container_type_1
	RadioContainerSnippetType1 SnippetType = "radio_container_type_1"
	// FitsoImageTextSnippetType6 represents fitso_image_text_snippet_type_6
	FitsoImageTextSnippetType6 SnippetType = "fitso_image_text_snippet_type_6"
	// PurchaseWidgetSnippet3 represents purchase_widget_snippet_3
	PurchaseWidgetSnippet3 SnippetType = "purchase_widget_snippet_3"
	// FitsoImageTextSnippetType5 represents fitso_image_text_snippet_type_5
	FitsoImageTextSnippetType5 SnippetType = "fitso_image_text_snippet_type_5"
	// FitsoImageTextSnippetType7 represents fitso_image_text_snippet_type_7
	FitsoImageTextSnippetType7 SnippetType = "fitso_image_text_snippet_type_7"
	// FitsoImageTextSnippetType8 represents fitso_image_text_snippet_type_8
	FitsoImageTextSnippetType8 SnippetType = "fitso_image_text_snippet_type_8"
	// FitsoImageTextSnippetType13 represents fitso_image_text_snippet_type_13
	FitsoImageTextSnippetType13 SnippetType = "fitso_image_text_snippet_type_13"
	// FitsoImageTextSnippetType15 represents fitso_image_text_snippet_type_15
	FitsoImageTextSnippetType15 SnippetType = "fitso_image_text_snippet_type_15"
	// ActionSnippetType2 represents action_snippet_type_2
	ActionSnippetType2 SnippetType = "action_snippet_type_2"
	// MediaSnippetType2 represents media_snippet_type_2
	MediaSnippetType2 SnippetType = "media_snippet_type_2"
	// FitsoImageTextSnippetType10 represents fitso_image_text_snippet_type_10
	FitsoImageTextSnippetType10 SnippetType = "fitso_image_text_snippet_type_10"
	// FitsoImageTextSnippetType11 represents fitso_image_text_snippet_type_11
	FitsoImageTextSnippetType11 SnippetType = "fitso_image_text_snippet_type_11"
	// FitsoImageTextSnippetType12 represents fitso_image_text_snippet_type_12
	FitsoImageTextSnippetType12 SnippetType = "fitso_image_text_snippet_type_12"
	// FitsoImageTextSnippetType14 represents fitso_image_text_snippet_type_14
	FitsoImageTextSnippetType14 SnippetType = "fitso_image_text_snippet_type_14"
	// FitsoImageTextSnippetType16 represents fitso_image_text_snippet_type_16
	FitsoImageTextSnippetType16 SnippetType = "fitso_image_text_snippet_type_16"
	// FitsoImageTextSnippetType17 represents fitso_image_text_snippet_type_17
	FitsoImageTextSnippetType17 SnippetType = "fitso_image_text_snippet_type_17"
	// ImageTextSnippetType16 represents image_text_snippet_type_16
	ImageTextSnippetType16 SnippetType = "image_text_snippet_type_16"
	// TextSnippetType3 represents text_snippet_type_3
	TextSnippetType3 SnippetType = "text_snippet_type_3"
	// FitsoTextSnippetType10 represents fitso_text_snippet_type_10
	FitsoTextSnippetType10 SnippetType = "fitso_text_snippet_type_10"
	// FitsoPurchaseSnippetType1 represents fitso_purchase_snippet_type_1
	FitsoPurchaseSnippetType1 SnippetType = "fitso_purchase_snippet_type_1"
	//FitsoTextSnippetSnippetType19 represnets fitso_image_text_snippet_type_19
	FitsoTextSnippetSnippetType19 SnippetType = "fitso_image_text_snippet_type_19"
	//V2ImageTextType14SnippetItem represents v2_image_text_snippet_type_14
	V2ImageTextSnippetType14 SnippetType = "v2_image_text_snippet_type_14"
	// FitsoPurchaseSnippetType1 represents fitso_purchase_snippet_type_1
	FitsoPurchaseSnippetType2   SnippetType = "fitso_purchase_snippet_type_2"
	FitsoImageTextSnippetType9  SnippetType = "fitso_image_text_snippet_type_9"
	FitsoImageTextSnippetType22 SnippetType = "fitso_image_text_snippet_type_22"

	// FitsoToggleSnippetType1 represents fitso_toggle_snippet_type_1
	FitsoToggleSnippetType1 SnippetType = "fitso_toggle_snippet_type_1"
	// ImageTextSnippetType13 represents image_text_snippet_type_13
	ImageTextSnippetType13 SnippetType = "image_text_snippet_type_13"

	// FitsoImageTextSnippetType23 represents fitso_image_text_snippet_type_23
	FitsoImageTextSnippetType23 SnippetType = "fitso_image_text_snippet_type_23"

	// FitsoImageTextSnippetType24 represents fitso_image_text_snippet_type_24
	FitsoImageTextSnippetType24 SnippetType = "fitso_image_text_snippet_type_24"
)

// LayoutType represents a layout data type
type LayoutType string

// SnippetType represents a snippet data type
type SnippetType string

// LayoutConfig represents the layout of the snippet
type LayoutConfig struct {
	SnippetType      SnippetType `json:"snippet_type,omitempty"`
	LayoutType       LayoutType  `json:"layout_type,omitempty"`
	SectionCount     int         `json:"section_count,omitempty"`
	VisibleCards     float32     `json:"visible_cards,omitempty"`
	ShouldAutoScroll bool        `json:"should_auto_scroll,omitempty"`
	CollapsedCount   int         `json:"collapsed_count,omitempty"`
	ShouldResize     bool        `json:"should_resize,omitempty"`
	IsExpanded       bool        `json:"is_expanded,omitempty"`
}

// NewLayout creates a new layout
func NewLayout(snippetType SnippetType, layout LayoutType, sectionCount int) (*LayoutConfig, error) {
	if sectionCount == 0 {
		return nil, errors.New("section count can not be zero")
	}

	return &LayoutConfig{
		SnippetType:  snippetType,
		LayoutType:   layout,
		SectionCount: sectionCount,
	}, nil
}

func (l *LayoutConfig) SetVisibleCards(val float32) {
	l.VisibleCards = val
}
