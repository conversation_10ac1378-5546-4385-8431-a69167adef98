package sushi

type FitsoPillsType1 struct {
	Items  []*FitsoPillsType1Item `json:"items,omitempty"`
	Config *FitsoPillsType1Config `json:"config,omitempty"`
}

type FitsoPillsType1Item struct {
	PostbackParams    string       `json:"postback_params,omitempty"`
	Title             *TextSnippet `json:"title,omitempty"`
	IsSelected        bool         `json:"is_selected,omitempty"`
}

type FitsoPillsType1Config struct {
	Selected   *FitsoPillsType1ConfigDetail `json:"selected,omitempty"`
	Unselected *FitsoPillsType1ConfigDetail `json:"unselected,omitempty"`
}

type FitsoPillsType1ConfigDetail struct {
	TextColor   *Color `json:"text_color,omitempty"`
	BgColor     *Color `json:"bg_color,omitempty"`
	BorderColor *Color `json:"border_color,omitempty"`
}
