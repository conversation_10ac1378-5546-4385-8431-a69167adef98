package sushi

import "errors"

// IconCode represents the icon code
type IconCode string

// IconSize represents the icon code
type IconSize string

// Refer https://github.com/Zomato/web/blob/master/modules/Assets/Icon.php for icon code names
const (
	// RatingIconCode represents the rating icon code
	RatingIconCode        IconCode = "e905"
	RightArrowIcon        IconCode = "e875"
	RightArrowOutlineIcon IconCode = "e936"
	CallLineThinIcon      IconCode = "e8b2"
	CrossCircleFillIcon   IconCode = "e93d"
	BookmarkIcon          IconCode = "e94a"
	DirectionIcon         IconCode = "e956"
	RightIcon             IconCode = "e8ff"
	TickMarkIcon          IconCode = "e845"
	ShareIcon             IconCode = "e87e"
	RepeatIcon            IconCode = "e86c"
	StarIcon              IconCode = "e951"
	DismissIcon           IconCode = "e921"
	PromoIcon             IconCode = "e8fa"
	ChevronDownIcon       IconCode = "e92d"
	ChevronUpIcon         IconCode = "e930"
	ChevronLeftIcon       IconCode = "e92e"
	ChevronRightIcon      IconCode = "e92f"
	PlusCircleIcon        IconCode = "e93a"
	CheckCircleIcon       IconCode = "e93c"
	LocationIcon          IconCode = "e92c"
	ArrowDownIcon         IconCode = "e8fe"
	BookingIcon           IconCode = "e887"
	MembershipIcon        IconCode = "ec86"
	SettingIcon           IconCode = "e833"
	NotificationIcon      IconCode = "e834"
	SupportIcon           IconCode = "e88c"
	TickMarkIconV2        IconCode = "e93b"
	ArrowDownIconV2       IconCode = "e874"
	PointIcon             IconCode = "e840"
	SwimmingIcon          IconCode = "e9db"
	ThunderIcon           IconCode = "ec7c"
	CrossIcon             IconCode = "e923"
	CheckIcon             IconCode = "e8ae"
	CopyIconCode          IconCode = "e959"
	CancelIcon            IconCode = "e938"
	ReferEarnIcon         IconCode = "e892"
	DiscountIcon          IconCode = "e8f9"
	InfoIcon              IconCode = "e805"
	CalenderIcon          IconCode = "e83f"
	BuyMembership         IconCode = "e943"
	Memberships           IconCode = "e88f"
	UnVerifiedIcon        IconCode = "e880"
	DeleteIcon            IconCode = "e8ac"
	EditIcon              IconCode = "e88b"
	ExtraInfoIcon         IconCode = "e879"
	EmptyCircleIcon       IconCode = "e895"
	BringGuestIcon        IconCode = "e88d"
	ScrollDownIcon        IconCode = "e85a"
)

const (
	// IconSize18 represents 18 icon size
	IconSize18 IconSize = "18"
	// IconSize20 represents 20 icon size
	IconSize20 IconSize = "20"
	// IconSize100 represents 100 icon size
	IconSize100 IconSize = "100"
	// IconSize800 represents 800 icon size
	IconSize800 IconSize = "800"
	// IconSize300 represents 300 icon size
	IconSize300 IconSize = "300"
	// IconSize200 represents 200 icon size
	IconSize200 IconSize = "200"
	// IconSize400 represents 400 icon size
	IconSize400 IconSize = "400"
	// IconSize500 represents 500 icon size
	IconSize500 IconSize = "500"
	// IconSize700 represents 700 icon size
	IconSize700 IconSize = "700"
)

// Icon represents sushi icon item
type Icon struct {
	Code              IconCode         `json:"code,omitempty"`
	Color             *Color           `json:"color,omitempty"`
	Size              IconSize         `json:"size,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

//NewIcon creates a new Icon
func NewIcon(code IconCode, color *Color) (*Icon, error) {
	if code == "" {
		return nil, errors.New("Empty icon code")
	}

	icon := &Icon{
		Code:  code,
		Color: color,
	}
	return icon, nil
}

//SetColor sets the color of the TextSnippet
func (i *Icon) SetColor(color *Color) {
	i.Color = color
}

func (i *Icon) SetSize(size IconSize) {
	i.Size = size
}

func (i *Icon) SetClickAction(clickAction *ClickAction) {
	i.ClickAction = clickAction
}

func (i *Icon) AddClevertapTrackingItem(item *ClevertapItem) {
	if i.ClevertapTracking == nil {
		i.ClevertapTracking = make([]*ClevertapItem, 0)
	}
	i.ClevertapTracking = append(i.ClevertapTracking, item)
}
