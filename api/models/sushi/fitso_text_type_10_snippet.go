package sushi

type FitsoTextSnippetType10Layout struct {
	LayoutConfig                  *LayoutConfig                  `json:"layout_config,omitempty"`
	FitsoTextSnippetType10Snippet *FitsoTextSnippetType10Snippet `json:"fitso_text_snippet_type_10,omitempty"`
	ClevertapTracking             []*ClevertapItem               `json:"clever_tap_tracking,omitempty"`
}

type FitsoTextSnippetType10Snippet struct {
	Id                int32            `json:"id,omitempty"`
	CornerRadius      int32            `json:"corner_radius,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	Subtitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	Subtitle2         *TextSnippet     `json:"subtitle2,omitempty"`
	Subtitle3         *TextSnippet     `json:"subtitle3,omitempty"`
	RightImage        *Image           `json:"right_image,omitempty"`
	Images            []*Image         `json:"images,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	BorderColor       *Color           `json:"border_color,omitempty"`
	Gradient          *Gradient        `json:"gradient,omitempty"`
	TopLeftTag        *Tag             `json:"top_left_tag,omitempty"`
	Tag               *Tag             `json:"tag,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
