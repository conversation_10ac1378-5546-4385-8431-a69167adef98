package sushi

import "errors"

type TextAlignment string

type TextType string

type TextSize string

const (
	TextAlignmentEnd    TextAlignment = "end"
	TextAlignmentBottom TextAlignment = "bottom"
	TextAlignmentCenter TextAlignment = "center"
	TextAlignmentLeft   TextAlignment = "left"
	TextAlignmentRight  TextAlignment = "right"
)

const TextTypeText TextType = "text"

const (
	TextSizeMedium TextSize = "medium"
	TextSizeSmall  TextSize = "small"
	TextSizeLarge  TextSize = "large"
)

// TextSnippet Sushi item for Text Snippet
type TextSnippet struct {
	Text             string        `json:"text,omitempty"`
	Placeholder      string        `json:"placeholder,omitempty"`
	Color            *Color        `json:"color,omitempty"`
	Font             *Font         `json:"font,omitempty"`
	NumberOfLines    int           `json:"number_of_lines,omitempty"`
	Alignment        TextAlignment `json:"alignment,omitempty"`
	SuffixIcon       *Icon         `json:"suffix_icon,omitempty"`
	PrefixIcon       *Icon         `json:"prefix_icon,omitempty"`
	Type             TextType      `json:"type,omitempty"`
	Kerning          int32         `json:"kerning,omitempty"`
	PrefixImage      *Image        `json:"prefix_image,omitempty"`
	IsMarkdown       int32         `json:"is_markdown,omitempty"`
	IsCopyingEnabled bool          `json:"is_copying_enabled,omitempty"`
	Size             TextSize      `json:"size,omitempty"`
	ClickAction      *ClickAction  `json:"click_action,omitempty"`
	Strikethrough    bool          `json:"strikethrough,omitempty"`
	Gradient         *Gradient     `json:"gradient,omitempty"`
	Title            *TextSnippet   `json:"title,omitempty"`
}

//NewTextSnippet creates a new TextSnippet
func NewTextSnippet(text string) (*TextSnippet, error) {
	if text == "" {
		return nil, errors.New("Empty Text")
	}

	textSnippet := &TextSnippet{
		Text: text,
	}
	return textSnippet, nil
}

//SetColor sets the color of the TextSnippet
func (t *TextSnippet) SetColor(color *Color) {
	t.Color = color
}

//SetFont sets the font of the TextSnippet
func (t *TextSnippet) SetFont(font *Font) {
	t.Font = font
}

// SetNumberOfLines set the no of lines in which the text is to be wrapped
func (t *TextSnippet) SetNumberOfLines(numberOfLines int) {
	t.NumberOfLines = numberOfLines
}

// SetAlignment set the alignment type
func (t *TextSnippet) SetAlignment(alignment TextAlignment) {
	t.Alignment = alignment
}

// SetSuffixIcon sets suffix icon
func (t *TextSnippet) SetSuffixIcon(suffixIcon *Icon) {
	t.SuffixIcon = suffixIcon
}

// SetPrefixIcon sets prefix icon
func (t *TextSnippet) SetPrefixIcon(prefixIcon *Icon) {
	t.PrefixIcon = prefixIcon
}

func (t *TextSnippet) SetKerning(kerning int32) {
	t.Kerning = kerning
}

func (t *TextSnippet) SetType(textType TextType) {
	t.Type = textType
}

func (t *TextSnippet) SetPrefixImage(prefixImage *Image) {
	t.PrefixImage = prefixImage
}

func (t *TextSnippet) SetIsMarkdown(isMarkdown int32) {
	t.IsMarkdown = isMarkdown
}

func (t *TextSnippet) SetIsCopyingEnabled(isCopyingEnabled bool) {
	t.IsCopyingEnabled = isCopyingEnabled
}

func (t *TextSnippet) SetSize(size TextSize) {
	t.Size = size
}

func (t *TextSnippet) SetClickAction(clickAction *ClickAction) {
	t.ClickAction = clickAction
}
