package sushi

type FitsoImageTextSnippetType19Layout struct {
	LayoutConfig                       *LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextSnippetType19Snippet *FitsoImageTextSnippetType19Snippet `json:"fitso_text_snippet_type_10,omitempty"`
}

type FitsoImageTextSnippetType19Snippet struct {
	CornerRadius int32                             `json:"corner_radius"`
	Title        *TextSnippet                      `json:"title,omitempty"`
	BorderColor  *Color                            `json:"border_color,omitempty"`
	Items        *[]V2ImageTextSnippetType14Layout `json:"items,omitempty"`
	Subtitle     *TextSnippet                      `json:"subtitle,omitempty"`
	Tag          *Tag                              `json:"tag,omitempty"`
	Gradient     *Gradient                         `json:"gradient,omitempty"`
	BorderWidth  int32                             `json:"border_width"`
	Tag1         *Tag                              `json:"tag1,omitempty"`
}
