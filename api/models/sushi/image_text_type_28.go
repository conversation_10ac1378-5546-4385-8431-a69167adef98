package sushi

type ImageTextSnippetType28Snippet struct {
	Id    string                               `json:"id,omitempty"`
	Items *[]ImageTextSnippetType28SnippetItem `json:"items,omitempty"`
}

type ImageTextSnippetType28SnippetItem struct {
	Title       *TextSnippet `json:"title,omitempty"`
	SubTitle1   *TextSnippet `json:"subtitle1,omitempty"`
	SubTitle2   *TextSnippet `json:"subtitle2,omitempty"`
	LeftImage   *Image       `json:"left_image,omitempty"`
	RightIcon   *Button      `json:"right_icon,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

type ImageTextSnippetType28Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType28 *ImageTextSnippetType28Snippet `json:"image_text_snippet_type_28,omitempty"`
}
