package sushi

type FitsoTextSnippetType7Snippet struct {
	Id           string                                      `json:"id,omitempty"`
	Title        *TextSnippet                                `json:"title,omitempty"`
	Button       *Button                                     `json:"button,omitempty"`
	BgColor      *Color                                      `json:"bg_color,omitempty"`
	BorderColor  *Color                                      `json:"border_color,omitempty"`
	ClickAction  *ClickAction                                `json:"click_action,omitempty"`
	SubtitleList []*FitsoTextSnippetType7SnippetSubtitleItem `json:"subtitles_list,omitempty"`
}

type FitsoTextSnippetType7SnippetSubtitleItem struct {
	Title *TextSnippet `json:"title,omitempty"`
	Icon  *Icon        `json:"icon,omitempty"`
}

type FitsoTextSnippetType7Layout struct {
	LayoutConfig          *LayoutConfig                 `json:"layout_config,omitempty"`
	FitsoTextSnippetType7 *FitsoTextSnippetType7Snippet `json:"fitso_text_snippet_type_7,omitempty"`
}
