package sushi

// ImageTextSnippetType19Snippet represents the type of image text snippet
type ImageTextSnippetType19Snippet struct {
	Items *[]ImageTextSnippetType19SnippetItem `json:"items,omitempty"`
}

// ImageTextSnippetType19SnippetItem represents the type of image text snippet item
type ImageTextSnippetType19SnippetItem struct {
	Image             *Image           `json:"image,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	HideGradient      bool             `json:"hide_gradient,omitempty"`
}

// ImageTextSnippetType19Layout represents ImageTextSnippetType19 sushi layout
type ImageTextSnippetType19Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType19 *ImageTextSnippetType19Snippet `json:"image_text_snippet_type_19,omitempty"`
}
