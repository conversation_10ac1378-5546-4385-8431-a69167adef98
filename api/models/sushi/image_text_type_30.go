package sushi

// ImageTextSnippetType30Snippet represents the type of image text snippet
type ImageTextSnippetType30Snippet struct {
	BgColor           *Color                               `json:"bg_color,omitempty"`
	Items             *[]ImageTextSnippetType30SnippetItem `json:"items,omitempty"`
	Title             *TextSnippet                         `json:"title,omitempty"`
	Subtitle          *TextSnippet                         `json:"subtitle,omitempty"`
	BottomButton      *Button                              `json:"bottom_button,omitempty"`
	ClevertapTracking []*ClevertapItem                     `json:"clever_tap_tracking,omitempty"`
}

// ImageTextSnippetType30SnippetItem represents the type of image text snippet item
type ImageTextSnippetType30SnippetItem struct {
	Image         *Image           `json:"image,omitempty"`
	Title         *TextSnippet     `json:"title,omitempty"`
	Tag           *Tag             `json:"tag,omitempty"`
	ClickAction   *ClickAction     `json:"click_action,omitempty"`
	ClevertapItem []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	Subtitle1     *TextSnippet     `json:"subtitle1,omitempty"`
}

// ImageTextSnippetType30Layout represents ImageTextSnippetType30 sushi layout
type ImageTextSnippetType30Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType30 *ImageTextSnippetType30Snippet `json:"image_text_snippet_type_30,omitempty"`
	SnippetConfig          *SnippetConfig                 `json:"snippet_config,omitempty"`
}
