package sushi

type FitsoTextSnippetType3Snippet struct {
	Items []*FitsoTextSnippetType3SnippetItem `json:"items,omitempty"`
}

type FitsoTextSnippetType3SnippetItem struct {
	TopLeftTag  *Tag         `json:"top_left_tag,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	Subtitle    *TextSnippet `json:"subtitle,omitempty"`
	Subtitle2   *TextSnippet `json:"subtitle2,omitempty"`
	RightButton *Button      `json:"right_button,omitempty"`
}
