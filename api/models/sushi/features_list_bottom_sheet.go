package sushi

type FeaturesListBottomSheetItem struct {
	Image       *Image       `json:"image,omitempty"`
	Tag         *Tag         `json:"tag,omitempty"`
	PromoTag    *Tag         `json:"promo_tag,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	Subtitle    *TextSnippet `json:"subtitle,omitempty"`
	ClickAction *ClickAction `json:"click_action,omitempty"`
}

type FeaturesListBottomSheetHeader struct {
	Title *TextSnippet `json:"title,omitempty"`
}

type FeaturesListBottomSheet struct {
	Header        *FeaturesListBottomSheetHeader `json:"header,omitempty"`
	Items         []*FeaturesListBottomSheetItem `json:"items,omitempty"`
	IsDismissable bool                           `json:"is_dismissable"`
}
