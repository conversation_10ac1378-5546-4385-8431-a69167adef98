package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

const (
	BgCornerRadius8     = 8
	BgCornerTypeRounded = "rounded"
)

// V2ImageTextSnippetType35Snippet represents the type of image text snippet
type V2ImageTextSnippetType35Snippet struct {
	Title             *TextSnippet                           `json:"title,omitempty"`
	Items             *[]V2ImageTextSnippetType35SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                       `json:"clever_tap_tracking,omitempty"`
	BgColor           *Color                                 `json:"bg_color,omitempty"`
}

// V2ImageTextSnippetType35SnippetItem represents the type of image text snippet item
type V2ImageTextSnippetType35SnippetItem struct {
	Image             *Image           `json:"image,omitempty"`
	TopImage          *Image           `json:"top_image,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	SubTitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	SubTitle2         *TextSnippet     `json:"subtitle2,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	IsSelected        bool             `json:"is_selected,omitempty"`
	IsSelectable      bool             `json:"is_selectable,omitempty"`
	RightIcon         *Button          `json:"right_icon,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	BgCornerRadius    int              `json:"bg_corner_radius,omitempty"`
	BgCornerType      string           `json:"bg_corner_type,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	BorderColor       *Color           `json:"border_color,omitempty"`
	JumboTracking     []*jumbo.Item    `json:"jumbo_tracking,omitempty"`
}

// V2ImageTextSnippetType35Layout represents V2ImageTextSnippetType35 sushi layout
type V2ImageTextSnippetType35Layout struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	V2ImageTextSnippetType35 *V2ImageTextSnippetType35Snippet `json:"image_text_snippet_type_35,omitempty"`
}
