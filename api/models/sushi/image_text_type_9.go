package sushi

// ImageTextSnippetType9SnippetItem represents the type of image text snippet item
type ImageTextSnippetType9SnippetItem struct {
	Image             *Image           `json:"image,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	TopLeftTag        *Tag             `json:"top_left_tag,omitempty"`
	Tag1              *Tag             `json:"tag1,omitempty"`
	ShowBlurBg        bool             `json:"show_blur_bg,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

// ImageTextSnippetType9Snippet represents the type of image text snippet
type ImageTextSnippetType9Snippet struct {
	Title             *TextSnippet                        `json:"title,omitempty"`
	Items             *[]ImageTextSnippetType9SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                    `json:"clever_tap_tracking,omitempty"`
}

// ImageTextSnippetType9Layout represents ImageTextSnippetType9 sushi layout
type ImageTextSnippetType9Layout struct {
	LayoutConfig          *LayoutConfig                 `json:"layout_config,omitempty"`
	ImageTextSnippetType9 *ImageTextSnippetType9Snippet `json:"image_text_snippet_type_9,omitempty"`
}
