package sushi

// MultiTagOrientation represents tags orientation type
type MultiTagOrientation string

const (
	// MultiTagOrientationVertical represents vertical orientation type
	MultiTagOrientationVertical MultiTagOrientation = "vertical"
)

// MultiTagSnippet represents muti tag sushi snippet
type MultiTagSnippet struct {
	Orientation MultiTagOrientation    `json:"orientation,omitempty"`
	Items       *[]MultiTagSnippetItem `json:"items,omitempty"`
}

// MultiTagSnippetItem represents muti tag sushi snippet item
type MultiTagSnippetItem struct {
	Title    *TextSnippet `json:"title,omitempty"`
	Gradient *Gradient    `json:"gradient,omitempty"`
	BgColor  *Color       `json:"bg_color,omitempty"`
}
