package sushi

type OpenAddDetailsPage struct {
	EditInfo          *EditInfo        `json:"edit_info,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type UserId struct {
	EditInfo *EditInfo `json:"edit_info,omitempty"`
}

type EditInfo struct {
	Title              *TextSnippet        `json:"title,omitempty"`
	BottomButtonStates *BottomButtonStates `json:"bottom_button_states,omitempty"`
	Sections           []*EditInfoSection  `json:"sections,omitempty"`
	EditPageType       string              `json:"edit_page_type,omitempty"`
}

type EditInfoSection struct {
	Type   string      `json:"type"`
	Name   *InputField `json:"name,omitempty"`
	Age    *InputField `json:"age,omitempty"`
	Mobile *InputField `json:"mobile,omitempty"`
}
