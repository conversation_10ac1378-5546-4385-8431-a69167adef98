package sushi

type FitsoTextSnippetType2Snippet struct {
	Title       *TextSnippet                        `json:"title,omitempty"`
	RightButton *Button                             `json:"right_button,omitempty"`
	Items       []*FitsoTextSnippetType2SnippetItem `json:"items,omitempty"`
}

type FitsoTextSnippetType2SnippetItem struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Subtitle          *TextSnippet     `json:"subtitle,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	CornerRadius      int32            `json:"corner_radius,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
