package sushi

type ItemConfig struct {
	SelectedItemColor      *Color `json:"selected_item_color"`
	UnselectedItemColor    *Color `json:"unselected_item_color"`
	SelectedIcon           *Icon  `json:"selected_icon"`
	EnabledItemColor       *Color `json:"enabled_item_color,omitempty"`
	DisabledItemColor      *Color `json:"disabled_item_color,omitempty"`
	DisabledItemTitleColor *Color `json:"disabled_item_title_color,omitempty"`
	DisabledItemTitleFont  *Font  `json:"disabled_item_title_font,omitempty"`
}

func NewItemConfig(url string) *ItemConfig {
	return &ItemConfig{}
}

func (ic *ItemConfig) SetSelectedItemColor(selectedItemColor *Color) {
	ic.SelectedItemColor = selectedItemColor
}

func (ic *ItemConfig) SetUnselectedItemColor(unselectedItemColor *Color) {
	ic.UnselectedItemColor = unselectedItemColor
}

func (ic *ItemConfig) SetSelectedIcon(selectedIcon *Icon) {
	ic.SelectedIcon = selectedIcon
}
