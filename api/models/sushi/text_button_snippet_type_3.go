package sushi

type TextButtonSnippetType3Snippet struct {
	Title             *TextSnippet     `json:"title,omitempty"`
	Button            *Button          `json:"button,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type TextButtonSnippetType3Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	TextButtonSnippetType3 *TextButtonSnippetType3Snippet `json:"text_button_snippet_type_3,omitempty"`
}
