package sushi

// TabSnippetType2Snippet represents the tab snippet type
type TabSnippetType2Snippet struct {
	Config            *TabSnippetType2SnippetConfig `json:"config,omitempty"`
	Items             *[]TabSnippetType2SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem              `json:"clever_tap_tracking,omitempty"`
}

// TabSnippetType2SnippetItem represents the tab snippet item type
type TabSnippetType2SnippetItem struct {
	Id                string                         `json:"id,omitempty"`
	Title             *TextSnippet                   `json:"title,omitempty"`
	IsSelected        bool                           `json:"is_selected,omitempty"`
	Snippets          *[]CustomTextSnippetTypeLayout `json:"snippets,omitempty"`
	ClevertapTracking []*ClevertapItem               `json:"clever_tap_tracking,omitempty"`
}

// TabSnippetType2SnippetConfig represents the tab snippet item type
type TabSnippetType2SnippetConfig struct {
	BorderColor           *Color `json:"selected_border_color,omitempty"`
	UnSelectedBorderColor *Color `json:"default_border_color,omitempty"`
	BgColor               *Color `json:"selected_bg_color,omitempty"`
	UnselectedBgColor     *Color `json:"default_bg_color,omitempty"`
	Color                 *Color `json:"selected_text_color,omitempty"`
	UnSelectedColor       *Color `json:"default_text_color,omitempty"`
}
