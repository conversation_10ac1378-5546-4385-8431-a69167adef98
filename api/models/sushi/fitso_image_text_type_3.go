package sushi

type FitsoImageTextSnippetType3SnippetItem struct {
	BorderColor *Color       `json:"border_color,omitempty"`
	BgColor     *Color       `json:"bg_color,omitempty"`
	Title       *TextSnippet `json:"title,omitempty"`
	Subtitle1   *TextSnippet `json:"subtitle1,omitempty"`
	Subtitle2   *TextSnippet `json:"subtitle2,omitempty"`
	Subtitle3   *TextSnippet `json:"subtitle3,omitempty"`
	Subtitle4   *TextSnippet `json:"subtitle4,omitempty"`
	Tag         *Tag         `json:"tag,omitempty"`
	Image       *Image       `json:"image,omitempty"`
	Button      *Button      `json:"button,omitempty"`
}

type FitsoImageTextSnippetType3Snippet struct {
	Items []*FitsoImageTextSnippetType3SnippetItem `json:"items,omitempty"`
}

// FitsoImageTextSnippetType3Layout represents FitsoImageTextSnippetType3 sushi layout
type FitsoImageTextSnippetType3Layout struct {
	LayoutConfig        *LayoutConfig                      `json:"layout_config,omitempty"`
	FitsoImageTextType3 *FitsoImageTextSnippetType3Snippet `json:"fitso_image_text_snippet_type_3,omitempty"`
}
