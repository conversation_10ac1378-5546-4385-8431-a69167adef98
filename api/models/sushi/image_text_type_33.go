package sushi

import "bitbucket.org/jogocoin/go_api/api/models/jumbo"

// ImageTextSnippetType33Snippet represents the type of image text snippet
type ImageTextSnippetType33Snippet struct {
	Id                string                               `json:"id,omitempty"`
	BgColor           *Color                               `json:"bg_color,omitempty"`
	Title             *TextSnippet                         `json:"title,omitempty"`
	Items             *[]ImageTextSnippetType33SnippetItem `json:"items,omitempty"`
	ClevertapTracking []*ClevertapItem                     `json:"clever_tap_tracking,omitempty"`
}

// ImageTextSnippetType33SnippetItem represents the type of image text snippet item
type ImageTextSnippetType33SnippetItem struct {
	Id                int32            `json:"id,omitempty"`
	LeftImage         *Image           `json:"left_image,omitempty"`
	RightImage        *Image           `json:"right_image,omitempty"`
	TopImage          *Image           `json:"top_image,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	SubTitle1         *TextSnippet     `json:"subtitle1,omitempty"`
	SubTitle2         *TextSnippet     `json:"subtitle2,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	IsSelected        bool             `json:"is_selected,omitempty"`
	IsSelectable      bool             `json:"is_selectable,omitempty"`
	RightIcon         *Icon            `json:"right_icon,omitempty"`
	Tag1              *Tag             `json:"tag1,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
	BgColor           *Color           `json:"bg_color,omitempty"`
	IsInactive        bool             `json:"is_inactive,omitempty"`
	BottomButton      *Button          `json:"bottom_button,omitempty"`
	JumboTracking     []*jumbo.Item    `json:jumbo_tracking,omitempty`
	CornerRadius      int32            `json:"corner_radius,omitempty"`
	ShowSeparator     bool             `json:"show_separator,omitempty"`
}

// ImageTextSnippetType33Layout represents ImageTextSnippetType33 sushi layout
type ImageTextSnippetType33Layout struct {
	LayoutConfig           *LayoutConfig                  `json:"layout_config,omitempty"`
	ImageTextSnippetType33 *ImageTextSnippetType33Snippet `json:"image_text_snippet_type_33,omitempty"`
}
