package sushi

type V2ImageTextSnippetType10Snippet struct {
	BgColor *Color                                 `json:"bg_color,omitempty"`
	Items   *[]V2ImageTextSnippetType10SnippetItem `json:"items,omitempty"`
}

type V2ImageTextSnippetType10SnippetItem struct {
	BgColor           *Color           `json:"bg_color,omitempty"`
	BorderColor       *Color           `json:"border_color,omitempty"`
	Title             *TextSnippet     `json:"title,omitempty"`
	RightButton       *Button          `json:"right_button,omitempty"`
	RightIcon         *Icon            `json:"right_icon,omitempty"`
	ClickAction       *ClickAction     `json:"click_action,omitempty"`
	BottomSeparator   *Separator       `json:"bottom_separator,omitempty"`
	ClevertapTracking []*ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type V2ImageTextSnippetType10Layout struct {
	LayoutConfig             *LayoutConfig                    `json:"layout_config,omitempty"`
	v2ImageTextSnippetType10 *V2ImageTextSnippetType10Snippet `json:"v2_image_text_snippet_type_10,omitempty"`
}

func (b *V2ImageTextSnippetType10SnippetItem) AddClevertapTrackingItem(item *ClevertapItem) {
	if b.ClevertapTracking == nil {
		b.ClevertapTracking = make([]*ClevertapItem, 0)
	}
	b.ClevertapTracking = append(b.ClevertapTracking, item)
}
