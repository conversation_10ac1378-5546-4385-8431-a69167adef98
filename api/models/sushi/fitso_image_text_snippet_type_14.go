package sushi

type FitsoImageTextSnippetType14Snippet struct {
	Items []*FitsoImageTextSnippetType14SnippetItem `json:"items,omitempty"`
}

type FitsoImageTextSnippetType14SnippetItem struct {
	Image       *Image                                    `json:"image,omitempty"`
	Title       *TextSnippet                              `json:"title,omitempty"`
	Subtitle    *TextSnippet                              `json:"subtitle,omitempty"`
	Subtitle1   *TextSnippet                              `json:"subtitle1,omitempty"`
	BgColor     *Color                                    `json:"bg_color,omitempty"`
	RightButton *Button                                   `json:"right_button,omitempty"`
	Container1  *FitsoImageTextSnippetType14ItemContainer `json:"container1,omitempty"`
	Container2  *FitsoImageTextSnippetType14ItemContainer `json:"container2,omitempty"`
}

type FitsoImageTextSnippetType14ItemContainer struct {
	Title    *TextSnippet `json:"title,omitempty"`
	Subtitle *TextSnippet `json:"subtitle,omitempty"`
}
