package sushi

type V2ImageTextSnippetType49Snippet struct {
	Title *TextSnippet                           `json:"title,omitempty"`
	Items *[]V2ImageTextSnippetType49SnippetItem `json:"items,omitempty"`
}

type V2ImageTextSnippetType49SnippetItem struct {
	Title         *TextSnippet        `json:"title,omitempty"`
	LeftImage     *Image              `json:"left_image,omitempty"`
	Tag           *Tag                `json:"tag,omitempty"`
	Subtitle      *TextSnippet        `json:"subtitle,omitempty"`
	RightImage    *Image              `json:"right_image,omitempty"`
	Image         *Image              `json:"image,omitempty"`
	ClickAction   *ClickAction        `json:"click_action,omitempty"`
	SubtitlesList *[]SubtitleListItem `json:"subtitles_list,omitempty"`
	BottomButton  *Button             `json:"bottom_button,omitempty"`
}

type SubtitleListItem struct {
	Title *TextSnippet `json:"title,omitempty"`
	Icon  *Icon        `json:"icon,omitempty"`
}

type V2ImageTextSnippetType49Layout struct {
	LayoutConfig           *LayoutConfig                    `json:"layout_config,omitempty"`
	ImageTextSnippetType49 *V2ImageTextSnippetType49Snippet `json:"image_text_snippet_type_49,omitempty"`
}
