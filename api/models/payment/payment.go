package payment

type SdkInitResponse struct {
	Code        string       `json:"token,omitempty"`
	ExpiresIn   int32        `json:"expires_in,omitempty"`
	Status      string       `json:"status,omitempty"`
	PaymentData *PaymentData `json:"payments_data,omitempty"`
}

type PaymentData struct {
	AdditionalParams string `json:"additional_params,omitempty"`
	CountryID        int32  `json:"country_id,omitempty"`
	ServiceType      string `json:"service_type,omitempty"`
}

type AdditionalParams struct {
	HiddenPaymentMethods []PaymentMethodData `json:"hidden_payment_methods,omitempty"`
}

type PaymentMethodData struct {
	Type string `json:"payment_method_type"`
}
