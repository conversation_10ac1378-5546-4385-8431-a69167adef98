package sport

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

// Response represents the response structure of sports page
type Response struct {
	Results        *[]ResultSection                `json:"results"`
	Header         *sushi.HeaderType4SnippetLayout `json:"header"`
	Footer         *ResultSection                  `json:"footer"`
	PostbackParams string                          `json:"postback_params"`
}

// ResultSection represents result item of sport page response
type ResultSection struct {
	LayoutConfig                 *sushi.LayoutConfig                  `json:"layout_config,omitempty"`
	SectionHeaderType1           *sushi.SectionHeaderType1Snippet     `json:"section_header_type_1,omitempty"`
	ImageTextSnippetType32       *sushi.ImageTextSnippetType32Snippet `json:"image_text_snippet_type_32,omitempty"`
	AccordionSnippetType4Snippet *sushi.AccordionSnippetType4Snippet  `json:"accordion_snippet_type_4,omitempty"`
	TagLayoutType3Snippet        *sushi.TagLayoutType3Snippet         `json:"tag_layout_type_3,omitempty"`
	FilterRailType1              *sushi.FilterRailType1Snippet        `json:"filter_rail_snippet_type_1,omitempty"`
	ResType3Snippet              *sushi.ResType3Snippet               `json:"v2_res_snippet_type_3,omitempty"`
	HeaderSnippetType4           *sushi.HeaderType4Snippet            `json:"header_snippet_type_4,omitempty"`
	FooterSnippetType2Snippet    *sushi.FooterSnippetType2Snippet     `json:"footer_snippet_type_2,omitempty"`
	TabSnippetType1Snippet       *sushi.TabSnippetType1Snippet        `json:"tab_snippet_type_1,omitempty"`
	ImageTextSnippetType30       *sushi.ImageTextSnippetType30Snippet `json:"image_text_snippet_type_30,omitempty"`
	SnippetConfig                *sushi.SnippetConfig                 `json:"snippet_config,omitempty"`
	TextSnippetType1             *sushi.TextSnippetType1Snippet       `json:"text_snippet_type_1,omitempty"`
	FitsoTextSnippetType7        *sushi.FitsoTextSnippetType7Snippet  `json:"fitso_text_snippet_type_7,omitempty"`
	ClevertapTracking            []*sushi.ClevertapItem               `json:"clever_tap_tracking,omitempty"`
}

// GetSportRequest represents sport page request parameters
type GetSportRequest struct {
	DistanceFilterID           string                 `json:"distance_filter_id" form:"distance_filter_id"`
	Count                      int32                  `json:"count" form:"count"`
	PostbackParams             string                 `json:"postback_params" form:"postback_params"`
	SportId                    int32                  `json:"-"`
	UnmarshalledPostbackParams *RequestPostbackParams `json:"-"`
}

type RequestPostbackParams struct {
	PreviousFacilityIds []int32
}

type StickyHeader struct {
	Title      *sushi.TextSnippet `json:"title,omitempty"`
	RightImage *sushi.Image       `json:"right_image,omitempty"`
}

type Filter struct {
	Id      string `json:"id,omitempty"`
	HasMore bool   `json:"has_more,omitempty"`
}
