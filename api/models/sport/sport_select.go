package sport

import (
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
)

const (
	CustomHeaderType1Val string = "custom_header_type_1"
	CustomHeaderType2Val string = "custom_header_type_2"
)

type SportSelectResponse struct {
	Header            *SportSelectHeader      `json:"header,omitempty"`
	CustomHeader      *CustomHeader           `json:"custom_header,omitempty"`
	ProgressBar       *SportSelectProgressBar `json:"progress_bar_data,omitempty"`
	Results           []*SportResultSection   `json:"results,omitempty"`
	Footer            *SportResultSection     `json:"footer,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem  `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item           `json:"jumbo_tracking,omitempty"`
}

type SportSelectHeader struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
	Tag   *sushi.Tag         `json:"tag,omitempty"`
	Color *sushi.Color       `json:"bg_color,omitempty"`
}

type CustomHeader struct {
	Type              string             `json:"type,omitempty"`
	CustomHeaderType1 *CustomHeaderType1 `json:"custom_header_type_1,omitempty"`
	CustomHeaderType2 *CustomHeaderType2 `json:"custom_header_type_2,omitempty"`
}

type CustomHeaderType1 struct {
	Title     *sushi.TextSnippet `json:"title"`
	SubTitle  *sushi.TextSnippet `json:"subtitle,omitempty"`
	BgColor   *sushi.Color       `json:"bg_color,omitempty"`
	LeftImage *sushi.Image       `json:"left_image,omitempty"`
	Icon      *sushi.Icon        `json:"icon,omitempty"`
}

type CustomHeaderType2 struct {
	Items     *[]sushi.TextSnippet `json:"items"`
	Color     *sushi.Color         `json:"color"`
	Font      *sushi.Font          `json:"font"`
	BgColor   *sushi.Color         `json:"bg_color"`
	LeftImage *sushi.Image         `json:"left_image,omitempty"`
}

type SportSelectProgressBar struct {
	Progress       float32        `json:"progress,omitempty"`
	Color          *sushi.Color   `json:"bg_color,omitempty"`
	ProgressColors []*sushi.Color `json:"progress_colors,omitempty"`
}

type SportResultSection struct {
	LayoutConfig             *sushi.LayoutConfig                    `json:"layout_config,omitempty"`
	ImageTextSnippetType41   *sushi.ImageTextSnippetType41Snippet   `json:"image_text_snippet_type_41,omitempty"`
	FooterSnippetType2       *sushi.FooterSnippetType2Snippet       `json:"footer_snippet_type_2,omitempty"`
	V2ImageTextSnippetType42 *sushi.V2ImageTextSnippetType42Snippet `json:"v2_image_text_snippet_type_42,omitempty"`
}
