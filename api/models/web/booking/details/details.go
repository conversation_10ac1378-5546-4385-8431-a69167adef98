package details

type Header struct {
	Title string `json:"title"`
	Icon  string `json:"icon"`
}

type TopContainer struct {
	Title     string `json:"title"`
	Subtitle1 string `json:"subtitle1"`
	Subtitle2 string `json:"subtitle2"`
	ImageURL  string `json:"img"`
}

type FacilityLocation struct {
	Icon      string `json:"icon"`
	Latitude  string `json:"lat"`
	Longitude string `json:"lon"`
}

type BottomContainer struct {
	SharingCourtTitle string             `json:"sharingCourtTitle"`
	TextBodySections  []*TextBodySection `json:"textBodySections"`
}

type TextBodySection struct {
	Title string          `json:"title"`
	Items []*TextBodyItem `json:"items"`
}

type TextBodyItem struct {
	ImageURL string `json:"img"`
	Text     string `json:"text"`
}

type BookingDetailsPageData struct {
	Header          *Header           `json:"header"`
	TopContainer    *TopContainer     `json:"topContainer"`
	Location        *FacilityLocation `json:"facilityLocation"`
	BottomContainer *BottomContainer  `json:"bottomContainer"`
}
