package slots

type SlotFacilityDetails struct {
	Name          string `json:"name,omitempty"`
	Rating        string `json:"rating,omitempty"`
	ShortAddress  string `json:"shortAddress,omitempty"`
	FacilityImage string `json:"img,omitempty"`
	FsId          int32  `json:"fsId,omitempty"`
	Tag           string `json:"tag,omitempty"`
	SoldOut       bool   `json:"soldOut"`
}

type Slot struct {
	Id                     int32  `json:"slotId,omitempty"`
	Title                  string `json:"title,omitempty"`
	IsDisabled             bool   `json:"isDisabled,omitempty"`
	ImageURL               string `json:"img,omitempty"`
	SlotDatetime           int64  `json:"slotDatetime,omitempty"`
	FormattedSlotTime      string `json:"formattedSlotTime,omitempty"`
	ProductArenaCategoryId int32  `json:"productArenaCategoryID,omitempty"`
}

type SlotSection struct {
	Title string  `json:"title"`
	Slots []*Slot `json:"slots"`
}

type SlotTab struct {
	Title    string         `json:"title"`
	Subtitle string         `json:"subtitle"`
	Sections []*SlotSection `json:"sections"`
}

type CoachDescriptionSection struct {
	Title    string `json:"title"`
	Subtitle string `json:"subtitle"`
	ImageURL string `json:"img"`
}

type SlotFooterButton struct {
	Title string `json:"title"`
}

type GetSlotsPageData struct {
	Facility         *SlotFacilityDetails     `json:"facilityDetails,omitempty"`
	Heading          string                   `json:"heading,omitempty"`
	SlotTabs         []*SlotTab               `json:"slotSections,omitempty"`
	CoachDescription *CoachDescriptionSection `json:"coachDescriptionSection,omitempty"`
	FooterButton     *SlotFooterButton        `json:"button,omitempty"`
	Header           *TrialSlotHeader         `json::"header,omitempty"`
}

type TrialSlotHeader struct {
	Title    string `json:"title,omitempty"`
	Subtitle string `json:"subtitle,omitempty"`
}
