package page

import (
	webCommonModels "bitbucket.org/jogocoin/go_api/api/models/web/common"
)

type PageInfo struct {
	Id          int32    `json:"id,omitempty"`
	Name        string   `json:"name,omitempty"`
	URL         string   `json:"url,omitempty"`
	Title       string   `json:"title,omitempty"`
	Description string   `json:"description,omitempty"`
	Keywords    []string `json:"keywords,omitempty"`
	IsMobile    bool     `json:"isMobile"`
}

type PageTemplate struct {
	PageInfo       *PageInfo                 `json:"pageInfo,omitempty"`
	PageData       interface{}               `json:"pageData,omitempty"`
	Location       *webCommonModels.Location `json:"location,omitempty"`
	PageFooter     interface{}               `json:"footerData,omitempty"`
	Has<PERSON>ore        bool                      `json:"has_more"`
	PostbackParams string                    `json:"postback_params,omitempty"`
	HeaderData     *HeaderData               `json:"headerData,omitempty"`
}

type HeaderData struct {
	MembershipUrl string    `json:"membershipUrl,omitempty"`
	CityPageInfo  *PageInfo `json:"cityPageInfo,omitempty"`
}
