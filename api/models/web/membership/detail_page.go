package membership

type Product struct {
	PId               int32  `json:"pId,omitempty"`
	Duration          int32  `json:"duration,omitempty"`
	DurationText      string `json:"durationText,omitempty"`
	PerMonthText      string `json:"perMonthText,omitempty"`
	DiscountText      string `json:"discountText,omitempty"`
	TopCardTitle      string `json:"topCardTitle,omitempty"`
	RetailPrice       string `json:"retailPrice,omitempty"`
	Price             string `json:"price,omitempty"`
	ProductCategoryId int32  `json:"productCategoryId,omitempty"`
}

type MembershipPageData struct {
	PId              int32                `json:"pId,omitempty"`
	ProductName      string               `json:"productName,omitempty"`
	ProductPrice     string               `json:"productPrice,omitempty"`
	ProductDuration  string               `json:"productDuration,omitempty"`
	MinAgeSubtitle   string               `json:"minAgeSubtitle,omitempty"`
	Benefits         []*ProductBenefits   `json:"benefits,omitempty"`
	FooterCta        string               `json:"footerCta,omitempty"`
	FitsoLogo        string               `json:"fitsoLogo,omitempty"`
	HeaderBackground string               `json:"headerBackground,omitempty"`
	EmptyView        *EmptyView           `json:"emptyView,omitempty"`
	Products         []*Product           `json:"products,omitempty"`
	ProductMap       map[int32][]*Product `json:"productMap,omitempty"`
	Title            string               `json:"title,omitempty"`
	CityName         string               `json:"cityName,omitempty"`
	BenefitsHeading  string               `json:"benefitsHeading,omitempty"`
	CardFooterText   string               `json:"cardFooterText,omitempty"`
}

type ProductBenefits struct {
	BenefitTitle     string `json:"benefitTitle,omitempty"`
	BenefitSubtitle1 string `json:"benefitSubtitle1,omitempty"`
	BenefitSubtitle2 string `json:"benefitSubtitle2,omitempty"`
	Image            string `json:"image,omitempty"`
}

type EmptyView struct {
	Title      string `json:"title,omitempty"`
	Subtitle   string `json:"subtitle,omitempty"`
	Image      string `json:"image,omitempty"`
	ButtonText string `json:"buttonText,omitempty"`
}
