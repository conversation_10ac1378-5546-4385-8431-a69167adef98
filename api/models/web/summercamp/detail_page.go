package summercamp
import (
	apiCommon "bitbucket.org/jogocoin/go_api/api/common"
)

type SummerCampPageData struct {
	Header            *SummerCampHeader            `json:"header,omitempty"`
	Sports            *SportsSection               `json:"sports,omitempty"`
	Benefits          *SummerCampBenefits          `json:"benefits,omitempty"`
	Plans             *SummerCampPlans             `json:"plans,omitempty"`
	SportsFacilities  *SportFacilitiesSection      `json:"sports_facilities,omitempty"`
	ContactSection    *SummerCampContactSection    `json:"contact,omitempty"`
	HowItWorksSection *SummerCampHowItWorksSection `json:"how_it_works,omitempty"`
	FooterCta         string                       `json:"footerCta,omitempty"`
	EmptyView         *EmptyView                   `json:"emptyView,omitempty"`
}

type SportFacilitiesSection struct {
	Title           string   `json:"title,omitempty"`
	SportFacilities []*Sport `json:"sport_facilities,omitempty"`
}

type Sport struct {
	SportId   int32       `json:"sport_id"`
	SportName string      `json:"sport_name,omitempty"`
	Centers   []*Facility `json:"centers,omitempty"`
}

type Facility struct {
	FacilityId        int32   `json:"facility_id,omitempty"`
	DisplayName       string  `json:"display_name,omitempty"`
	DisplayAddress    string  `json:"display_address,omitempty"`
	ShortAddress      string  `json:"short_address,omitempty"`
	DisplayPicture    string  `json:"display_picture,omitempty"`
	Distance          string  `json:"distance,omitempty"`
	Rating            string  `json:"rating,omitempty"`
	Tag               string  `json:"tag,omitempty"`
	SummerCampTimings string  `json:"summer_camp_timings,omitempty"`
	Sport             []*Sport `json:"sport"`
}

type SummerCampFacilitiesRes struct {
	Status  apiCommon.Status `json:"status"`
	Centers []*Facility      `json:"centers"`
}

type SummerCampHeader struct {
	Subtitle         string `json:"subtitle,omitempty"`
	HeaderBackground string `json:"headerBackground,omitempty"`
}

type SportsSection struct {
	Title string `json:"title,omitempty"`
	Image string `json:"image,omitempty"`
}

type SummerCampBenefits struct {
	Title string `json:"title,omitempty"`
	Image string `json:"image,omitempty"`
}

type SummerCampPlans struct {
	Title string `json:"title,omitempty"`
	Image string `json:"image,omitempty"`
}

type SummerCampContactSection struct {
	EmailImage string `json:"email_image,omitempty"`
	Email      string `json:"email,omitempty"`
	PhoneImage string `json:"phone_image,omitempty"`
	Phone      string `json:"phone,omitempty"`
}

type SummerCampHowItWorksSection struct {
	Title string `json:"title,omitempty"`
	Image string `json:"image,omitempty"`
}

type EmptyView struct {
	Title      string `json:"title,omitempty"`
	Subtitle   string `json:"subtitle,omitempty"`
	Image      string `json:"image,omitempty"`
	ButtonText string `json:"buttonText,omitempty"`
}
