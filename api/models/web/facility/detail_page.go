package facility

import (
	webCommonModels "bitbucket.org/jogocoin/go_api/api/models/web/common"
	webPageModels "bitbucket.org/jogocoin/go_api/api/models/web/page"
)

type FacilityPageData struct {
	Id               int32                `json:"id,omitempty"`
	Name             string               `json:"name,omitempty"`
	Address          string               `json:"address,omitempty"`
	Rating           string               `json:"rating,omitempty"`
	Tag              string               `json:"tag,omitempty"`
	Images           []string             `json:"images,omitempty"`
	Location         *FacilityLocation    `json:"location,omitempty"`
	Amenities        []*Amenity           `json:"amenities,omitempty"`
	Attributes       []*FacilityAttribute `json:"attributes,omitempty"`
	Sports           *FacilitySports      `json:"sports,omitempty"`
	NearbyFacilities *NearbyFacilities    `json:"nearbyFacilities,omitempty"`
	CallFitsoSection *CallFitso           `json:"callFitsoSection,omitempty"`
}

type Amenity struct {
	Name     string `json:"name"`
	ImageURL string `json:"img"`
}

type FacilitySport struct {
	Id           int32                   `json:"id"`
	ImageURL     string                  `json:"img"`
	Title        string                  `json:"title"`
	Subtitle1    string                  `json:"subtitle1,omitempty"`
	Subtitle2    string                  `json:"subtitle2,omitempty"`
	SlotPageInfo *webPageModels.PageInfo `json:"slotPageInfo,omitempty"`
	IsSelected	 bool					 `json:"isSelected,omitempty"`
}

type NearbyFacility struct {
	Id           int32                   `json:"id"`
	Title        string                  `json:"title"`
	Subtitle1    string                  `json:"subtitle1"`
	Subtitle2    string                  `json:"subtitle2"`
	ImageURL     string                  `json:"img"`
	Rating       string                  `json:"rating"`
	Tag          string                  `json:"tag,omitempty"`
	DistanceText string                  `json:"distanceText"`
	PageInfo     *webPageModels.PageInfo `json:"pageInfo"`
}

type FacilityAttribute struct {
	ImageURL string `json:"img"`
	Title    string `json:"title"`
	Type     string `json:"type"`
}

type NearbyFacilities struct {
	Heading    string            `json:"heading"`
	Facilities []*NearbyFacility `json:"facilities"`
}

type FacilitySports struct {
	Heading string           `json:"heading"`
	Sports  []*FacilitySport `json:"sports"`
}

type CallFitso struct {
	Title  string                  `json:"title"`
	Button *webCommonModels.Button `json:"button"`
}

type FacilityLocation struct {
	Title     string `json:"title"`
	ImageURL  string `json:"img"`
	Latitude  string `json:"lat"`
	Longitude string `json:"lon"`
}
