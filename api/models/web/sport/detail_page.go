package sport

import webPageModels "bitbucket.org/jogocoin/go_api/api/models/web/page"

type Amenity struct {
	Icon string `json:"icon"`
	Text string `json:"text"`
}

type SportAmenities struct {
	Heading   string     `json:"heading"`
	Amenities []*Amenity `json:"amenities"`
}

type Facility struct {
	Name                  string                  `json:"name,omitempty"`
	Rating                string                  `json:"rating,omitempty"`
	DistanceText          string                  `json:"distanceText,omitempty"`
	PlayArenaTags         []string                `json:"playArenaLabels,omitempty"`
	SportsAvailable       string                  `json:"sportsAvailable,omitempty"`
	ShortAddress          string                  `json:"shortAddress,omitempty"`
	SlotsAvailableText    string                  `json:"slotsAvailableText,omitempty"`
	FreeTrialText         string                  `json:"freeTrialText,omitempty"`
	FacilityImage         string                  `json:"facilityImage,omitempty"`
	FacilityID            int32                   `json:"facilityID,omitempty"`
	FacilityPageInfo      *webPageModels.PageInfo `json:"facilityPageInfo,omitempty"`
	FacilitySlotsPageInfo *webPageModels.PageInfo `json:"facilitySlotsPageInfo,omitempty"`
	Tag                   string                  `json:"tag,omitempty"`
	FsId                  int32                   `json:"fsId,omitempty"`
	SoldOut               bool                    `json:"soldOut"`
	SummerCampTimings     string                  `json:"summer_camp_timings,omitempty"`
}

type NearbyFacilities struct {
	Heading    string      `json:"heading"`
	Facilities []*Facility `json:"facilities"`
}

type SportPageData struct {
	Name             string            `json:"name"`
	SportImage       string            `json:"sportImage"`
	Subtitle         string            `json:"subtitle"`
	SportAmenities   *SportAmenities   `json:"sportAmenities"`
	NearbyFacilities *NearbyFacilities `json:"nearByFacilities"`
	SportBgColor     string            `json:"bgColor"`
	SportIconImage   string            `json:"sportIconImage"`
}
