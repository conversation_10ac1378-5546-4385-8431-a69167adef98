package common

type PageFooter struct {
	Sections    []*FooterSection `json:"sections"`
	Info        string           `json:"info,omitempty"`
	AppLinks    []*Link          `json:"appLinks,omitempty"`
	SocialLinks *SocialLinks     `json:"socialLinks,omitempty"`
}

type FooterSection struct {
	Heading string  `json:"heading"`
	Links   []*Link `json:"links"`
}

type Link struct {
	ImageURL string `json:"img,omitempty"`
	Url      string `json:"link"`
	Text     string `json:"text,omitempty"`
}

type SocialLinks struct {
	Heading string  `json:"heading"`
	Links   []*Link `json:"links"`
}
