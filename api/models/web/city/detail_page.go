package city

import (
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
	webPageModels "bitbucket.org/jogocoin/go_api/api/models/web/page"
	webSportModels "bitbucket.org/jogocoin/go_api/api/models/web/sport"
)

type CityPageData struct {
	Id                   int32                   `json:"id,omitempty"`
	Header               *CityHeader             `json:"header,omitempty"`
	Sports               *CitySports             `json:"sports,omitempty"`
	Benefits             *FitsoBenefits          `json:"benefits,omitempty"`
	Facilities           *CityFacilities         `json:"facilities,omitempty"`
	Subzones             *CitySubzones           `json:"subzones,omitempty"`
	EmptyView            *EmptyView              `json:"emptyView,omitempty"`
	Button               *FooterButton           `json:"button,omitempty"`
	TrialPageInfo        *webPageModels.PageInfo `json:"trialInfo,omitempty"`
	ShowPopUpModalOnLoad bool                    `json:"showPopUpModalOnLoad"`
	Membership           *MembershipCTAData      `json:"membership,omitempty"`
	Offers               *OffersData             `json:"offers,omitempty"`
}

type CityHeader struct {
	Notice   string `json:"notice,omitempty"`
	Title    string `json:"title,omitempty"`
	Subtitle string `json:"subtitle,omitempty"`
}

type FooterButton struct {
	Title string `json:"title,omitempty"`
}

type CitySports struct {
	Title        string        `json:"title,omitempty"`
	Items        []*Sport      `json:"items,omitempty"`
	FooterButton *FooterButton `json:"button,omitempty"`
}

type FitsoBenefits struct {
	Title string             `json:"title,omitempty"`
	Items []FitsoBenefitItem `json:"items,omitempty"`
}

type CityFacilities struct {
	Title      string                     `json:"title,omitempty"`
	SideButton string                     `json:"sideButton,omitempty"`
	Items      []*webSportModels.Facility `json:"items,omitempty"`
}

type Sport struct {
	SportId       int32                   `json:"sport_id,omitempty"`
	Title         string                  `json:"title,omitempty"`
	Subtitle      string                  `json:"subtitle,omitempty"`
	Image         *sushi.Image            `json:"image,omitempty"`
	SportPageInfo *webPageModels.PageInfo `json:"sportPageInfo,omitempty"`
	SportBgColor  string                  `json:"bgColor"`
	IconImage     string                  `json:"iconImage,omitempty"`
}

type FitsoBenefitItem struct {
	Title    string       `json:"title"`
	Subtitle string       `json:"subtitle"`
	Image    *sushi.Image `json:"image"`
}

type CitySubzones struct {
	Title string             `json:"title"`
	Items []*CitySubzoneItem `json:"items"`
}

type CitySubzoneItem struct {
	Id             int32   `json:"id"`
	CollapsedTitle string  `json:"collapsedTitle"`
	ExpandedTitle  string  `json:"expandedTitle"`
	SideButton     string  `json:"sideButton"`
	FacilityIds    []int32 `json:"facilityIds"`
}

type FacilityCard struct {
	FacilityPageInfo *webPageModels.PageInfo `json:"facilityPageInfo,omitempty"`
	Title            string                  `json:"title,omitempty"`
	Subtitle1        string                  `json:"subtitle1,omitempty"`
	Subtitle2        string                  `json:"subtitle2,omitempty"`
	ImageURL         string                  `json:"imageUrl,omitempty"`
	Rating           string                  `json:"rating,omitempty"`
	DistanceText     string                  `json:"distanceText,omitempty"`
	Tag              string                  `json:"tag,omitempty"`
}

type EmptyView struct {
	HeaderImage string                      `json:"headerImage"`
	HeaderText  string                      `json:"headerText"`
	Title       string                      `json:"title"`
	Items       []*ActiveCitiesCentersCount `json:"items,omitempty"`
}

type ActiveCitiesCentersCount struct {
	Id           int32                   `json:"id"`
	Image        string                  `json:"image"`
	Title        string                  `json:"title"`
	Subtitle     string                  `json:"subtitle"`
	CityPageInfo *webPageModels.PageInfo `json:"cityPageInfo"`
}

type GetNearbyFacilitiesRequest struct {
	SportID             int32   `json:"sport_id" form:"sport_id"`
	Count               int32   `json:"count" form:"count"`
	PostbackParams      string  `json:"postback_params" form:"postback_params"`
	PreviousFacilityIds []int32 `json:"previous_facility_ids"`
	Path                string  `json:"path" form:"path"`
}

type MembershipCTAData struct {
	Title    string `json:"title,omitempty"`
	Subtitle string `json:"subtitle,omitempty"`
	Url      string `json:"url,omitempty"`
}

type OffersData struct {
	Title string  `json:"title,omitempty"`
	Items []Offer `json:"items,omitempty"`
}

type Offer struct {
	OfferId      int32  `json:"offerId,omitempty"`
	OfferName    string `json:"offerName,omitempty"`
	ImageDesktop string `json:"imageDesktop,omitempty"`
	ImageMobile  string `json:"imageMobile,omitempty"`
	Url          string `json:"url,omitempty"`
}
