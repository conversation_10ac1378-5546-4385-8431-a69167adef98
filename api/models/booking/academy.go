package booking

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type UserAssessmentsPageResult struct {
	LayoutConfig                *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextSnippetType12 *sushi.FitsoImageTextSnippetType12Snippet `json:"fitso_image_text_snippet_type_12,omitempty"`
	SnippetConfig               *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	V2ImageTextSnippetType50    *sushi.V2ImageTextSnippetType50Snippet    `json:"v2_image_text_snippet_type_50,omitempty"`
}

type AssessmentsPageHeader struct {
	Title    *sushi.TextSnippet `json:"title,omitempty"`
	LeftIcon *sushi.Icon        `json:"left_icon,omitempty"`
}

type AssessmentPageResult struct {
	LayoutConfig                *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextSnippetType14 *sushi.FitsoImageTextSnippetType14Snippet `json:"fitso_image_text_snippet_type_14,omitempty"`
	FitsoImageTextSnippetType4  *sushi.FitsoImageTextSnippetType4Snippet  `json:"fitso_image_text_snippet_type_4,omitempty"`
	SnippetConfig               *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	SectionHeaderType1          *sushi.SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
}

type AssessmentPageHeader struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}
