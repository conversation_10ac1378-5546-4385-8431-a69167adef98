package users

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type PageHeader struct {
	Title   *sushi.TextSnippet `json:"title,omitempty"`
	BgColor *sushi.Color       `json:"bg_color,omitempty"`
}

type Filter struct {
	Id      string `json:"id,omitempty"`
	<PERSON><PERSON><PERSON> bool   `json:"has_more,omitempty"`
}

type Bookings struct {
	BgColor *sushi.Color     `json:"bg_color,omitempty"`
	Header  *ResultSection   `json:"header,omitempty"`
	Results []*ResultSection `json:"results,omitempty"`
}

type ResultSection struct {
	LayoutConfig             *sushi.LayoutConfig                    `json:"layout_config,omitempty"`
	SectionHeaderType1       *sushi.SectionHeaderType1Snippet       `json:"section_header_type_1,omitempty"`
	V2ImageTextSnippetType34 *sushi.V2ImageTextSnippetType34Snippet `json:"v2_image_text_snippet_type_34,omitempty"`
	EmptyViewType1           *sushi.EmptyViewType1Snippet           `json:"empty_view_type_1,omitempty"`
	FooterType2              *sushi.FooterSnippetType2Snippet       `json:"footer_snippet_type_2,omitempty"`
	FilterRailType1          *sushi.FilterRailType1Snippet          `json:"filter_rail_snippet_type_1,omitempty"`
	ImageTextSnippetType35   *sushi.ImageTextSnippetType35Snippet   `json:"image_text_snippet_type_35,omitempty"`
}
