package slot

import (
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
)

const (
	CustomHeaderType1Val string = "custom_header_type_1"
	CustomHeaderType2Val string = "custom_header_type_2"
)

type Header struct {
	Title   *sushi.TextSnippet `json:"title"`
	Tag     *sushi.Tag         `json:"tag,omitempty"`
	BgColor *sushi.Color       `json:"bg_color"`
}

type ProgressBarStruct struct {
	Progress       float32        `json:"progress"`
	BgColor        *sushi.Color   `json:"bg_color"`
	ProgressColors *[]sushi.Color `json:"progress_colors"`
}

type CustomHeader struct {
	Type              string             `json:"type"`
	CustomHeaderType1 *CustomHeaderType1 `json:"custom_header_type_1,omitempty"`
	CustomHeaderType2 *CustomHeaderType2 `json:"custom_header_type_2,omitempty"`
}

type CustomHeaderType1 struct {
	Title     *sushi.TextSnippet `json:"title"`
	SubTitle  *sushi.TextSnippet `json:"subtitle,omitempty"`
	BgColor   *sushi.Color       `json:"bg_color,omitempty"`
	LeftImage *sushi.Image       `json:"left_image,omitempty"`
}

type CustomHeaderType2 struct {
	TitleStack *[]sushi.TextSnippet `json:"title_stack"`
	BgColor    *sushi.Color         `json:"bg_color,omitempty"`
	LeftImage  *sushi.Image         `json:"left_image,omitempty"`
}

type DetailsContainer struct {
	Title    *sushi.TextSnippet `json:"title"`
	SubTitle *sushi.TextSnippet `json:"subtitle,omitempty"`
	Image    *sushi.Image       `json:"image"`
}

type InfoContainer struct {
	Title        *sushi.TextSnippet `json:"title"`
	SubTitle     *sushi.TextSnippet `json:"subtitle"`
	Image        *sushi.Image       `json:"image"`
	BgColor      *sushi.Color       `json:"bg_color"`
	BorderColor  *sushi.Color       `json:"border_color"`
	CornerRadius int32              `json:"corner_radius"`
}

type SlotConfig struct {
	HeaderConfig *HeaderConfig `json:"header_config"`
	ItemConfig   *ItemConfig   `json:"item_config"`
}

type HeaderConfig struct {
	SelectedTitleColor      *sushi.Color `json:"selected_title_color"`
	SelectedSubtitleColor   *sushi.Color `json:"selected_subtitle_color"`
	UnselectedTitleColor    *sushi.Color `json:"unselected_title_color"`
	UnselectedSubtitleColor *sushi.Color `json:"unselected_subtitle_color"`
}

type ItemConfig struct {
	SelectedItemColor     *sushi.Color `json:"selected_item_color"`
	UnselectedItemColor   *sushi.Color `json:"unselected_item_color"`
	EnabledItemColor      *sushi.Color `json:"enabled_item_color"`
	DisabledItemColor     *sushi.Color `json:"disabled_item_color"`
	EnabledItemTitleColor *sushi.Color `json:"enabled_item_title_color"`
	DisabledItemTitleFont *sushi.Font  `json:"disabled_item_title_font"`
}

type SlotItem struct {
	Title             *sushi.TextSnippet                    `json:"title"`
	SubTitle          *sushi.TextSnippet                    `json:"subtitle"`
	Sections          []SlotSection                         `json:"sections,omitempty"`
	Error             *SlotErrorStruct                      `json:"error,omitempty"`
	Loader            *SlotLoaderStruct                     `json:"loader,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem                `json:"clever_tap_tracking,omitempty"`
	AdditionalInfo    []*sushi.TextButtonSnippetType3Layout `json:"additional_info,omitempty"`
	JumboTracking     []*jumbo.Item                         `json:"jumbo_tracking,omitempty"`
	IsDisabled        bool                                  `json:"is_disabled"`
}

type SlotSection struct {
	SectionHeader *SlotSectionHeader `json:"section_header"`
	Slots         []SlotStruct       `json:"slots"`
}

type SlotSectionHeader struct {
	Title    *sushi.TextSnippet `json:"title"`
	SubTitle *sushi.TextSnippet `json:"subtitle"`
}

type SlotStruct struct {
	Title             *sushi.TextSnippet     `json:"title"`
	SubTitle          *sushi.TextSnippet     `json:"subtitle,omitempty"`
	Image             *sushi.Image           `json:"image,omitempty"`
	Images            []*sushi.Image         `json:"images,omitempty"`
	ClickAction       *sushi.ClickAction     `json:"click_action,omitempty"`
	SlotId            int32                  `json:"slot_id"`
	IsDisabled        bool                   `json:"is_disabled,omitempty"`
	IsSelected        bool                   `json:"is_selected,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item          `json:"jumbo_tracking,omitempty"`
	BottomContainer   *BottomContainer       `json:"bottom_container,omitempty"`
}

type BottomContainer struct {
	Title   *sushi.TextSnippet `json:"title,omitempty"`
	BgColor *sushi.Color       `json:"bg_color,omitempty"`
}

type SlotErrorStruct struct {
	Title *sushi.TextSnippet `json:"title"`
	Image *sushi.Image       `json:"image,omitempty"`
}

type SlotLoaderStruct struct {
	Title           *sushi.TextSnippet `json:"title"`
	IsLoaderPresent bool               `json:"is_loader_present,omitempty"`
}

type SessionsInfo struct {
	Image             *sushi.Image           `json:"image,omitempty"`
	Title             *sushi.TextSnippet     `json:"title"`
	BottomButton      *sushi.Button          `json:"bottom_button,omitempty"`
	ClickAction       *sushi.ClickAction     `json:"click_action,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}
