package centres

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

const (
	CustomHeaderType1Val string = "custom_header_type_1"
	CustomHeaderType2Val string = "custom_header_type_2"
)

// ResultSection represents structure of each result section
type GetCentresForSlotBookingReq struct {
	SportId      int32 `json:"sport_id" form:"sport_id"`
	SlotId       int32 `json:"slot_id" form:"slot_id"`
	BookingCount int32 `json:"booking_count" form:"booking_count"`
	IsTrial      int32 `json:"is_trial" form:"is_trial"`
	BookingDate  int64 `json:"booking_date" form:"booking_date"`
}

type Header struct {
	Title   *sushi.TextSnippet `json:"title"`
	Tag     *sushi.Tag         `json:"tag,omitempty"`
	BgColor *sushi.Color       `json:"bg_color,omitempty"`
}

type CustomHeader struct {
	Type              string             `json:"type"`
	CustomHeaderType1 *CustomHeaderType1 `json:"custom_header_type_1,omitempty"`
	CustomHeaderType2 *CustomHeaderType2 `json:"custom_header_type_2,omitempty"`
}

type CustomHeaderType1 struct {
	Title     *sushi.TextSnippet `json:"title,omitempty"`
	SubTitle  *sushi.TextSnippet `json:"subtitle,omitempty"`
	BgColor   *sushi.Color       `json:"bg_color,omitempty"`
	LeftImage *sushi.Image       `json:"left_image,omitempty"`
	Icon      *sushi.Icon        `json:"icon,omitempty"`
}

type CustomHeaderType2 struct {
	Items     *[]sushi.TextSnippet `json:"items"`
	Color     *sushi.Color         `json:"color"`
	Font      *sushi.Font          `json:"font"`
	BgColor   *sushi.Color         `json:"bg_color"`
	LeftImage *sushi.Image         `json:"left_image,omitempty"`
}

type ProgressBarStruct struct {
	Progress       int32          `json:"progress"`
	BgColor        *sushi.Color   `json:"bg_color"`
	ProgressColors *[]sushi.Color `json:"progress_colors"`
}
