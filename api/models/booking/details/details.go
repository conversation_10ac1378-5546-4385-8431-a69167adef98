package details

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type Header struct {
	*sushi.Image
	Title      *sushi.TextSnippet `json:"title,omitempty"`
	RightIcons []*sushi.Icon      `json:"right_icons,omitempty"`
}

type FloatingView struct {
	Id                string                 `json:"id,omitempty"`
	Identifier        string                 `json:"identifier,omitempty"`
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	BgColor           *sushi.Color           `json:"bg_color,omitempty"`
	BorderColor       *sushi.Color           `json:"border_color,omitempty"`
	Button            *sushi.Button          `json:"right_button,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type ResultSection struct {
	Id                            string                                    `json:"id,omitempty"`
	LayoutConfig                  *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoImageTextType2           *sushi.FitsoImageTextSnippetType2Snippet  `json:"fitso_image_text_snippet_type_2,omitempty"`
	SectionHeaderType1            *sushi.SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
	ImageSnippetType30            *sushi.ImageTextSnippetType30Snippet      `json:"image_text_snippet_type_30,omitempty"`
	SnippetConfig                 *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	AccordionType4                *sushi.AccordionSnippetType4Snippet       `json:"accordion_snippet_type_4,omitempty"`
	V2ImageSnippetType10          *sushi.V2ImageTextSnippetType10Snippet    `json:"v2_image_text_snippet_type_10,omitempty"`
	FitsoAccordianType1           *sushi.FitsoAccordianType1Snippet         `json:"fitso_accordion_snippet_type_1,omitempty"`
	FitsoImageTextSnippetType17   *sushi.FitsoImageTextSnippetType17Snippet `json:"fitso_image_text_snippet_type_17,omitempty"`
	ImageTextSnippetType16Snippet *sushi.ImageTextSnippetType16Snippet      `json:"image_text_snippet_type_16,omitempty"`
	TextSnippetType3              *sushi.TextSnippetType3Snippet            `json:"text_snippet_type_3,omitempty"`
}
