package booking

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type ReversalRequestAction struct {
	Type               sushi.ActionType      `json:"type,omitempty"`
	BottomSheetDismiss *sushi.ResponseAction `json:"bottom_sheet_dismiss,omitempty"`
	CustomAlert        *sushi.CustomAlert    `json:"custom_alert,omitempty"`
	RefreshPages       *[]RefreshItem        `json:"refresh_pages,omitempty"`
}

type RefreshItem struct {
	Id string `json:"id"`
}
