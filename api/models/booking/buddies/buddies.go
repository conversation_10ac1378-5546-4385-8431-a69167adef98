package buddies

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type Header struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}

type Buddy struct {
	ID            int32              `json:"id"`
	Image         *sushi.Image       `json:"image"`
	Title         *sushi.TextSnippet `json:"title"`
	Subtitle      *sushi.TextSnippet `json:"subtitle,omitempty"`
	IsSelected    bool               `json:"is_selected,omitempty"`
	IsSelectable  bool               `json:"is_selectable,omitempty"`
	PostbackParam string             `json:"postback_params,omitempty"`
	RightButton   *sushi.Button      `json:"right_button,omitempty"`
}
