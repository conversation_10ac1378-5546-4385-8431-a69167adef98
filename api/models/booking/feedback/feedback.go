package feedback

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type Header struct {
	Title   *sushi.TextSnippet `json:"title,omitempty"`
	BgColor *sushi.Color       `json:"bg_color,omitempty"`
}

type BookingInfo struct {
	LayoutConfig       *sushi.LayoutConfig              `json:"layout_config,omitempty"`
	SectionHeaderType1 *sushi.SectionHeaderType1Snippet `json:"section_header_type_1,omitempty"`
}

type RatingData struct {
	Rating       int32           `json:"rating,omitempty"`
	RatingConfig []*RatingObject `json:"rating_config,omitempty"`
	Alignment    string          `json:"alignment,omitempty"`
}

type RatingObject struct {
	Value             int32                  `json:"value,omitempty"`
	BgColor           *sushi.Color           `json:"bg_color,omitempty"`
	BorderColor       *sushi.Color           `json:"border_color,omitempty"`
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	TagData           *TagDataObject         `json:"tag_data,omitempty"`
	TextMandatory     bool                   `json:"text_mandatory",omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type TagDataObject struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
	Tags  []*Tags            `json:"tags,omitempty"`
}

type Tags struct {
	Id                string                 `json:"id,omitempty"`
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	IsSelected        bool                   `json:"is_selected,omitempty"`
	BgColor           *sushi.Color           `json:"bg_color,omitempty"`
	BorderColor       *sushi.Color           `json:"border_color,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type InputFieldData struct {
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	ReviewText        string                 `json:"review_text,omitempty"`
	AlertText         *sushi.TextSnippet     `json:"alert_text,omitempty"`
	PlaceholderText   string                 `json:"placeholder_text,omitempty"`
	MaxLength         int32                  `json:"max_length,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type TagConfig struct {
	Selected   *TagConfigs `json:"seleted,omitempty"`
	Unselected *TagConfigs `json:"Unselected,omitempty"`
}

type TagConfigs struct {
	BgColor     *sushi.Color `json:"bg_color,omitempty"`
	Color       *sushi.Color `json:"color,omitempty"`
	BorderColor *sushi.Color `json:"border_color,omitempty"`
}

type BackAction struct {
	Type        sushi.ActionType   `json:"type,omitempty"`
	CustomAlert *sushi.CustomAlert `json:"custom_alert,omitempty"`
}
