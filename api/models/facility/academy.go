package facility

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type AcademyPageHeader struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}

type AcademyTab struct {
	Id                int32                    `json:"id"`
	Title             *sushi.TextSnippet       `json:"title,omitempty"`
	IsSelected        bool                     `json:"is_selected,omitempty"`
	Type              string                   `json:"type,omitempty"`
	PostbackParam     string                   `json:"postback_param,omitempty"`
	QueryParam        string                   `json:"query_param,omitempty"`
	Sections          []*AcademyTabSectionItem `json:"sections,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem   `json:"clever_tap_tracking,omitempty"`
}

type AcademyTabSectionItem struct {
	SectionHeader *AcademyTabSectionHeader             `json:"section_header,omitempty"`
	Items         []*sushi.CustomTextSnippetTypeLayout `json:"items,omitempty"`
}

type AcademyTabSectionHeader struct {
	LayoutConfig       *sushi.LayoutConfig              `json:"layout_config,omitempty"`
	SectionHeaderType1 *sushi.SectionHeaderType1Snippet `json:"section_header_type_1,omitempty"`
}

type AcademyCourseResult struct {
	LayoutConfig                *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	MediaSnippetType2           *sushi.MediaType2Snippet                  `json:"media_snippet_type_2,omitempty"`
	FitsoImageTextSnippetType10 *sushi.FitsoImageTextSnippetType10Snippet `json:"fitso_image_text_snippet_type_10,omitempty"`
	TextSnippetType1            *sushi.TextSnippetType1Snippet            `json:"text_snippet_type_1,omitempty"`
	ImageSnippetType30          *sushi.ImageTextSnippetType30Snippet      `json:"image_text_snippet_type_30,omitempty"`
	V2ImageTextSnippetType49    *sushi.V2ImageTextSnippetType49Snippet    `json:"v2_image_text_snippet_type_49,omitempty"`
	AccordionSnippetType4       *sushi.AccordionSnippetType4Snippet       `json:"accordion_snippet_type_4,omitempty"`
	SnippetConfig               *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	SectionHeaderType1          *sushi.SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
	ResType3Snippet             *sushi.ResType3Snippet                    `json:"v2_res_snippet_type_3,omitempty"`
	FitsoFacilityCardType2      *sushi.FitsoFacilityCardType2Snippet      `json:"fitso_facility_card_type_2,omitempty"`
	FitsoPurchaseSnippetType2   *sushi.FitsoPurchaseSnippetType2Snippet   `json:"fitso_purchase_snippet_type_2,omitempty"`
}
