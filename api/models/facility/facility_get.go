package facility

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

// ResultSection represents structure of each result section
type ResultSection struct {
	LayoutConfig               *sushi.LayoutConfig                      `json:"layout_config,omitempty"`
	SnippetConfig              *sushi.SnippetConfig                     `json:"snippet_config,omitempty"`
	ImageTextSnippetType26     *sushi.ImageTextSnippetType26Snippet     `json:"image_text_snippet_type_26,omitempty"`
	ImageTextSnippetType28     *sushi.ImageTextSnippetType28Snippet     `json:"image_text_snippet_type_28,omitempty"`
	TextSnippetType8           *sushi.TextSnippetType8Snippet           `json:"text_snippet_type_8,omitempty"`
	ImageTextSnippetType30     *sushi.ImageTextSnippetType30Snippet     `json:"image_text_snippet_type_30,omitempty"`
	ImageTextSnippetType32     *sushi.ImageTextSnippetType32Snippet     `json:"image_text_snippet_type_32,omitempty"`
	ImageTextSnippetType33     *sushi.ImageTextSnippetType33Snippet     `json:"image_text_snippet_type_33,omitempty"`
	V2ImageTextSnippetType10   *sushi.V2ImageTextSnippetType10Snippet   `json:"v2_image_text_snippet_type_10,omitempty"`
	SectionHeaderType1         *sushi.SectionHeaderType1Snippet         `json:"section_header_type_1,omitempty"`
	ResType3Snippet            *sushi.ResType3Snippet                   `json:"v2_res_snippet_type_3,omitempty"`
	TickerSnippetType1         *sushi.TickerSnippetType1Snippet         `json:"ticker_snippet_type_1,omitempty"`
	Id                         string                                   `json:"id,omitempty"`
	FitsoImageTextSnippetType7 *sushi.FitsoImageTextSnippetType7Snippet `json:"fitso_image_text_snippet_type_7,omitempty"`
	V2ImageTextSnippetType49   *sushi.V2ImageTextSnippetType49Snippet   `json:"v2_image_text_snippet_type_49,omitempty"`
	FitsoFacilityCardType2     *sushi.FitsoFacilityCardType2Snippet     `json:"fitso_facility_card_type_2,omitempty"`
}

type GetFacilityRequest struct {
	SportID                    int32                  `json:"sport_id" form:"sport_id"`
	DistanceFilterID           string                 `json:"distance_filter_id" form:"distance_filter_id"`
	Count                      int32                  `json:"count" form:"count"`
	PostbackParams             string                 `json:"postback_params" form:"postback_params"`
	FacilityID                 int32                  `json:"-"`
	UnmarshalledPostbackParams *RequestPostbackParams `json:"-"`
	SportIDFlag                int32                  `json:"-"`
	CourseId                   int32                  `json:"course_id" form:"course_id"`
	ProductCategoryId          string                  `json:"product_category_id"`
}

type RequestPostbackParams struct {
	PreviousFacilityIds []int32
}

type GetNearbyFacilitiesRequest struct {
	SportID             int32   `json:"sport_id" form:"sport_id"`
	DistanceFilterID    string  `json:"distance_filter_id" form:"distance_filter_id"`
	Count               int32   `json:"count" form:"count"`
	PostbackParams      string  `json:"postback_params" form:"postback_params"`
	PreviousFacilityIds []int32 `json:"previous_facility_ids"`
}

type PageHeader struct {
	ScrolledUpTitle *sushi.TextSnippet    `json:"scrolled_up_title,omitempty"`
	Actions         []*sushi.CustomAction `json:"actions,omitempty"`
}
