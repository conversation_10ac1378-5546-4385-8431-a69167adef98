package purchase

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type MakePurchaseRequest struct {
	ReferralCode      string               `json:"referral_code"`
	ChildUsers        []*CalculateCartUser `json:"child_users"`
	ParentUser        *CalculateCartUser   `json:"parent_user"`
	PaymentMethodType string               `json:"payment_method_type"`
	PaymentMethodID   interface{}          `json:"payment_method_id"`
	PromoCode         string               `json:"promo_code"`
	ProductId         interface{}          `json:"product_id"`
	ProductCategoryId int32                `json:"product_category_id"`
}

type PurchaseStatusRequest struct {
	PurchaseId     string `json:"purchase_id"`
	PostbackParams string `json:"postback_params,omitempty"`
}

type CalculateCartUser struct {
	UserId              int32  `json:"user_id"`
	Phone               string `json:"phone"`
	PlanStartDate       int64  `json:"plan_start_date"`
	Name                string `json:"name"`
	PreferredFacilityId int32  `json:"preferred_facility_id"`
	PreferredSportId    int32  `json:"preferred_sport_id"`
	ProductId           int32  `json:"product_id"`
	Age                 int32  `json:"age"`
}

type PurchaseStatusResponse struct {
	Status         string            `json:"status,omitempty"`
	PaymentStatus  int32             `json:"payment_status,omitempty"`
	ActionList     []*ActionListItem `json:"action_list,omitempty"`
	PollInterval   int32             `json:"poll_interval,omitempty"`
	PostbackParams string            `json:"postback_params,omitempty"`
}

type ActionListItem struct {
	Type                 interface{}               `json:"type"`
	CustomAlert          *sushi.CustomAlert        `json:"custom_alert,omitempty"`
	BuyMembershipDismiss *ActionListItemSnippet    `json:"buy_membership_dismiss,omitempty"`
	CartPendingPayment   *sushi.CartPendingPayment `json:"cart_pending_payment_data,omitempty"`
	SaveKey              *sushi.SaveKey            `json:"save_key,omitempty"`
}

type ActionListItemSnippet struct {
	Type        sushi.ActionType   `json:"type"`
	CustomAlert *sushi.CustomAlert `json:"custom_alert,omitempty"`
}

type SendMailForPurchaseRequest struct {
	PurchaseId int32 `json:"purchase_id"`
}

type PurchasePaymentStatusResponse struct {
	Status         string            `json:"status,omitempty"`
	PaymentStatus  int32             `json:"payment_status,omitempty"`
	Title          string            `json:"title,omitempty"`
	Subtitle       string            `json:"sub_title,omitempty"`
	Message        string            `json:"message,omitempty"`
	PollInterval   int32             `json:"poll_interval,omitempty"`
	PostbackParams string            `json:"postback_params,omitempty"`
}
