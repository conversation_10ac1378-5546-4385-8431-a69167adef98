package purchase

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type PurchaseHistoryRequest struct {
	UserID         int32  `validate:"required,min=1" form:"user_id" json:"user_id,omitempty"`
	ProductID      int32  `validate:"required,min=1" form:"product_id" json:"product_id,omitempty"`
	PostbackParams string `validate:"omitempty" json:"postback_params" form:"postback_params"`
}

type ResultSection struct {
	LayoutConfig          *sushi.LayoutConfig   `json:"layout_config,omitempty"`
	FitsoTextSnippetType4 *sushi.FitsoTextType4 `json:"fitso_text_snippet_type_4,omitempty"`
	SnippetConfig         *sushi.SnippetConfig  `json:"snippet_config,omitempty"`
}

type Header struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}
