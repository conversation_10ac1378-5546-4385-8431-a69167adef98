package product

import (
	"bitbucket.org/jogocoin/go_api/api/models/jumbo"
	"bitbucket.org/jogocoin/go_api/api/models/sushi"
)

type CalculateCartRequest struct {
	ProductId         interface{}          `json:"product_id"`
	ReferralCode      string               `json:"referral_code"`
	PromoCode         string               `json:"promo_code"`
	ChildUsers        []*CalculateCartUser `json:"child_users"`
	ParentUser        *CalculateCartUser   `json:"parent_user"`
	ProductCategoryId int32                `json:"product_category_id"`
}

type CalculateCartUser struct {
	UserId              int32  `json:"user_id"`
	Phone               string `json:"phone"`
	PlanStartDate       int64  `json:"plan_start_date"`
	Name                string `json:"name"`
	PreferredFacilityId int32  `json:"preferred_facility_id"`
	PreferredSportId    int32  `json:"preferred_sport_id"`
	Age                 int32  `json:"age"`
	ProductId           int32  `json:"product_id"`
}

type CalculateCartResponse struct {
	Status            string                      `json:"status,omitempty"`
	ResultSections    *CalculateCartResultSection `json:"response,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem      `json:"clever_tap_tracking,omitempty"`
	JumboTracking     []*jumbo.Item               `json:"jumbo_tracking,omitempty"`
}

type CalculateCartResultSection struct {
	PaymentsData    *PaymentsData   `json:"payments_data,omitempty"`
	HeaderData      *HeaderData     `json:"header_data,omitempty"`
	TopContainer    *TopContainer   `json:"top_container,omitempty"`
	MembershipData  *MembershipData `json:"membership_data,omitempty"`
	OffersData      *OffersData     `json:"offers_data,omitempty"`
	BillInfoData    *BillInfoData   `json:"bill_info,omitempty"`
	CartButtonData  *CartButtonData `json:"cart_button_data,omitempty"`
	PaymentProvider int32           `json:"payment_provider,omitempty"`
}

type PaymentsData struct {
	CountryID          int32   `json:"country_id,omitempty"`
	ServiceType        string  `json:"service_type,omitempty"`
	Amount             float32 `json:"amount,omitempty"`
	OnlinePaymentsFlag int32   `json:"online_payments_flag"`
}

type HeaderData struct {
	Color    *sushi.Color       `json:"bg_color,omitempty"`
	Title    *sushi.TextSnippet `json:"title,omitempty"`
	Subtitle *sushi.TextSnippet `json:"subtitle,omitempty"`
	Button   *sushi.Button      `json:"button,omitempty"`
	Image    *sushi.Image       `json:"image,omitempty"`
	Tag      *sushi.Tag         `json:"tag,omitempty"`
	Gradient *sushi.Gradient    `json:"gradient,omitempty"`
}

type TopContainer struct {
	Disclaimer *TopContainerDisclaimer `json:"disclaimer,omitempty"`
}

type TopContainerDisclaimer struct {
	BgColor     *sushi.Color       `json:"bg_color,omitempty"`
	BorderColor *sushi.Color       `json:"border_color,omitempty"`
	Title       *sushi.TextSnippet `json:"title,omitempty"`
	Button      *sushi.Button      `json:"button,omitempty"`
}

type MembershipData struct {
	Items []*MembershipDataItem `json:"items,omitempty"`
}

type MembershipDataItem struct {
	Title          *sushi.TextSnippet `json:"title,omitempty"`
	Subtitle       *sushi.TextSnippet `json:"subtitle,omitempty"`
	RightTitle     *sushi.TextSnippet `json:"right_title,omitempty"`
	InfoTitle      *sushi.TextSnippet `json:"info_title,omitempty"`
	InfoRightTitle *sushi.TextSnippet `json:"info_right_title,omitempty"`
}

type OffersData struct {
	Items []*OffersDataItem `json:"items,items"`
}

type OffersDataItem struct {
	Button      *sushi.Button      `json:"title_button,omitempty"`
	RightButton *sushi.Button      `json:"right_button,omitempty"`
	LeftIcon    *sushi.Icon        `json:"left_icon,omitempty"`
	RightTitle  *sushi.TextSnippet `json:"right_title,omitempty"`
	Title       *sushi.TextSnippet `json:"title,omitempty"`
	Subtitle    *sushi.TextSnippet `json:"subtitle,omitempty"`
}

type BillInfoData struct {
	TotalCost float32             `json:"total_cost,omitempty"`
	Discount  int32               `json:"discount"`
	Items     []*BillInfoDataItem `json:"items,omitempty"`
}

type BillInfoDataItem struct {
	Title      *sushi.TextSnippet `json:"title,omitempty"`
	RightTitle *sushi.TextSnippet `json:"right_title,omitempty"`
}

type CartButtonData struct {
	Button           *CartButton       `json:"right_button,omitempty"`
	ConfirmationData *ConfirmationData `json:"confirmation_data,omitempty"`
}

type ConfirmationData struct {
	Title  *sushi.TextSnippet `json:"title,omitempty"`
	Button *sushi.Button      `json:"button,omitempty"`
	Time   string             `json:"time,omitempty"`
}

type CartButton struct {
	Title             *sushi.TextSnippet     `json:"title,omitempty"`
	SubTitle          *sushi.TextSnippet     `json:"subtitle,omitempty"`
	SubTitle2         *sushi.TextSnippet     `json:"subtitle2,omitempty"`
	IsDisabled        bool                   `json:"is_disabled"`
	ClevertapTracking []*sushi.ClevertapItem `json:"clever_tap_tracking,omitempty"`
}

type CalculateCartResponseV2 struct {
	Header            *CalculateCartHeaderSection `json:"header,omitempty"`
	Results           []*ProductResult            `json:"results,omitempty"`
	Footer            *CalculateCartFooterSection `json:"footer,omitempty"`
	ActionList        []*ActionListItem           `json:"action_list,omitempty"`
	ClevertapTracking []*sushi.ClevertapItem      `json:"clever_tap_tracking,omitempty"`
}

type CalculateCartHeaderSection struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}

type CalculateCartFooterSection struct {
	RightButton *CartButton `json:"right_button,omitempty"`
}

type ProductResult struct {
	LayoutConfig                *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoTextSnippetType3       *sushi.FitsoTextSnippetType3Snippet       `json:"fitso_text_snippet_type_3,omitempty"`
	FitsoTextSnippetType11      *sushi.FitsoTextSnippetType11Snippet      `json:"fitso_text_snippet_type_11,omitempty"`
	SnippetConfig               *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	FitsoTextSnippetType4       *sushi.FitsoTextType4                     `json:"fitso_text_snippet_type_4,omitempty"`
	FitsoImageTextSnippetType24 *sushi.FitsoImageTextSnippetType24Snippet `json:"fitso_image_text_snippet_type_24,omitempty"`
	FitsoTextSnippetType1       *sushi.FitsoTextSnippetType1Snippet       `json:"fitso_text_snippet_type_1,omitempty"`
}

type ActionListItem struct {
	Type        string             `json:"type"`
	CustomAlert *sushi.CustomAlert `json:"custom_alert,omitempty"`
}
