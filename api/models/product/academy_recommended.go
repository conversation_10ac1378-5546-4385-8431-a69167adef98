package product

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type AcademyRecommendedPlan struct {
	Header  *sushi.Header                       `json:"header,omitempty"`
	Snippet *sushi.FitsoTextSnippetType8Snippet `json:"snippet,omitempty"`
}

type AcademyRecommendedPlanItem struct {
	Type         string                           `json:"type"`
	PersonalInfo *AcademyRecommendedPlanInputItem `json:"personal_info,omitempty"`
	StartDate    *AcademyRecommendedPlanInputItem `json:"start_date,omitempty"`
	CenterTime   *AcademyRecommendedPlanInputItem `json:"center_time,omitempty"`
}

type AcademyRecommendedPlanInputItem struct {
	ClosedState   *AcademyRecommendedPlanInputClosedState   `json:"closed_state,omitempty"`
	ExpandedState *AcademyRecommendedPlanInputExpandedState `json:"expanded_state,omitempty"`
}

type AcademyRecommendedPlanInputClosedState struct {
	Title      *sushi.TextSnippet                         `json:"title,omitempty"`
	Subtitle   *sushi.TextSnippet                         `json:"subtitle,omitempty"`
	EditButton *sushi.Button                              `json:"edit_button,omitempty"`
	Snippet    *sushi.V2ImageTextSnippetType31SnippetItem `json:"snippet,omitempty"`
}

type AcademyRecommendedPlanInputExpandedState struct {
	Title                *sushi.TextSnippet                      `json:"title,omitempty"`
	Icon                 *sushi.Icon                             `json:"icon,omitempty"`
	PlaceholderContainer *AcademyRecommendedPlanPlaceholder      `json:"placeholder_container,omitempty"`
	EditInfoData         *AcademyRecommendedPlanPersonalInfoEdit `json:"edit_info_data,omitempty"`
	States               *sushi.InputFieldStates                 `json:"states,omitempty"`
	BottomButtonStates   *sushi.BottomButtonStates               `json:"bottom_button_states,omitempty"`
	StartDate            int64                                   `json:"start_date,omitempty"`
	DurationInDays       int32                                   `json:"duration_in_days,omitempty"`
	ProductStartDate     int64                            `json:"product_start_date,omitempty"`
}

type AcademyRecommendedPlanPlaceholder struct {
	Title       *sushi.TextSnippet `json:"title,omitempty"`
	Icon        *sushi.Icon        `json:"icon,omitempty"`
	EditButton  *sushi.Button      `json:"edit_button,omitempty"`
	ClickAction *sushi.ClickAction `json:"click_action,omitempty"`
}

type AcademyRecommendedPlanPersonalInfoEdit struct {
	NonEditableUserDetails *NonEditableUserDetails         `json:"non_editable_user_details,omitempty"`
	EditInfo               *AcademyRecommendedPlanEditInfo `json:"edit_info,omitempty"`
}

type NonEditableUserDetails struct {
	Name  string `json:"name,omitempty"`
	Phone string `json:"phone,omitempty"`
}

type AcademyRecommendedPlanEditInfo struct {
	BottomButtonStates *sushi.BottomButtonStates             `json:"bottom_button_states,omitempty"`
	Sections           []*AcademyRecommendedPlanInputSection `json:"sections,omitempty"`
}

type AcademyRecommendedPlanInputSection struct {
	Type string            `json:"type"`
	Age  *sushi.InputField `json:"age,omitempty"`
}

type AcademyRecommendedPlanItemsConfig struct {
	IncompleteIcon  *sushi.Icon  `json:"incomplete_icon,omitempty"`
	CompleteIcon    *sushi.Icon  `json:"complete_icon,omitempty"`
	DashedLineColor *sushi.Color `json:"dashed_line_color,omitempty"`
}
