package product

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type InfoData struct {
	Title       *sushi.TextSnippet `json:"title,omitempty"`
	Items       []*InfoDataItem    `json:"items,omitempty"`
	RightImage  *sushi.Image       `json:"right_image,omitempty"`
	BgColor     *sushi.Color       `json:"bg_color,omitempty"`
	BorderColor *sushi.Color       `json:"border_color,omitempty"`
}

type UserData struct {
	User             MembershipUser      `json:"user"`
	Title            *sushi.TextSnippet  `json:"title,omitempty"`
	Header           *UserDataHeader     `json:"header,omitempty"`
	EditInfo         *MembershipEditInfo `json:"edit_info,omitempty"`
	EditButton       *sushi.TextSnippet  `json:"edit_button,omitempty"`
	AddDetailsButton *sushi.TextSnippet  `json:"add_details_button,omitempty"`
	ProductDetails   *ProductDetails     `json:"product_details,omitempty"`
	PlanEditInfo     *PlanEditInfo       `json:"plan_edit_info,omitempty"`
	DisplayAge       string              `json:"display_age,omitempty"`
}

type ChildUsersData struct {
	Users            []*MembershipUser        `json:"users,omitempty"`
	Title            *sushi.TextSnippet       `json:"title,omitempty"`
	DefaultSection   MembershipDefaultSection `json:"default_section,omitempty"`
	StartDate        int64                    `json:"start_date"`
	EditButton       *sushi.TextSnippet       `json:"edit_button,omitempty"`
	AddDetailsButton *sushi.TextSnippet       `json:"add_details_button,omitempty"`
	EditInfo         MembershipEditInfo       `json:"edit_info"`
	ProductDetails   *ProductDetails          `json:"product_details,omitempty"`
	PlanEditInfo     *PlanEditInfo            `json:"plan_edit_info,omitempty"`
	DisplayAge       string                   `json:"display_age,omitempty"`
}

type UserDataHeader struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}

type ProductDetails struct {
	Duration  *sushi.TextSnippet `json:"duration,omitempty"`
	Price     *sushi.TextSnippet `json:"price,omitempty"`
	ProductId int32              `json:"product_id,omitempty"`
}

type PlanEditInfo struct {
	Title              *sushi.TextSnippet     `json:"title,omitempty"`
	RightButton        *sushi.Button          `json:"right_button,omitempty"`
	BottomButtonStates *BottomButtonStates    `json:"bottom_button_states,omitempty"`
	Sections           []*PlanEditInfoSection `json:"sections,omitempty"`
}

type PlanEditInfoSection struct {
	Type            string       `json:"type,omitempty"`
	StartDate       *SectionItem `json:"start_date,omitempty"`
	PreferredCenter *SectionItem `json:"preferred_center,omitempty"`
}

type SectionItem struct {
	Title                *sushi.TextSnippet                        `json:"title,omitempty"`
	RightIcon            *sushi.Icon                               `json:"icon,omitempty"`
	EditButton           *EditButtonCustom                         `json:"edit_button,omitempty"`
	PlaceholderContainer *EditInfoSectionFieldPlaceholderContainer `json:"placeholder_container,omitempty"`
	DurationInDays       int32                                     `json:"duration_in_days,omitempty"`
	Optional             bool                                      `json:"optional"`
}

type BottomButtonStates struct {
	Disabled  *BottomButtonState `json:"disabled,omitempty"`
	Edited    *BottomButtonState `json:"edited,omitempty"`
	Completed *BottomButtonState `json:"completed,omitempty"`
}

type TrackingAttributes struct {
	Type        string `json:"type,omitempty"`
	CityId      int32  `json:"city_id"`
	ProductId   int32  `json:"product_id"`
	UserId      int32  `json:"user_id,omitempty"`
	ContextType string `json:"context_type"`
}

type MembershipUser struct {
	UserId               int32                                      `json:"user_id"`
	Name                 string                                     `json:"name,omitempty"`
	DisplayAge           string                                     `json:"display_age,omitempty"`
	Age                  int32                                      `json:"age,omitempty"`
	Phone                string                                     `json:"phone,omitempty"`
	StartDate            int64                                      `json:"start_date,omitempty"`
	MembershipStatusText *sushi.TextSnippet                         `json:"membership_status_text,omitempty"`
	ProductId            int32                                      `json:"product_id,omitempty"`
	PreferredCenter      *sushi.V2ImageTextSnippetType31SnippetItem `json:"preferred_center,omitempty"`
	PreferredFacilityId  int32                                      `json:"preferred_facility_id,omitempty"`
	PreferredSportId     int32                                      `json:"preferred_sport_id,omitempty"`
}

type MembershipEditInfo struct {
	Title              *sushi.TextSnippet `json:"title,omitempty"`
	BottomButtonStates BottomButtonStates `json:"bottom_button_states,omitempty"`
	Sections           []*EditInfoSection `json:"sections,omitempty"`
}

type InfoDataItem struct {
	LeftIcon *sushi.Icon        `json:"left_icon"`
	Title    *sushi.TextSnippet `json:"title"`
}

type MembershipDefaultSection struct {
	Title     *sushi.TextSnippet `json:"title"`
	Subtitle  *sushi.TextSnippet `json:"subtitle"`
	RightIcon *sushi.Icon        `json:"right_icon"`
}

type BottomButtonState struct {
	Button     *sushi.Button `json:"button,omitempty"`
	PluralText string        `json:"plural_text,omitempty"`
}

type EditInfoSection struct {
	Type            string                `json:"type"`
	Name            *EditInfoSectionField `json:"name,omitempty"`
	Age             *EditInfoSectionField `json:"age,omitempty"`
	Mobile          *EditInfoSectionField `json:"mobile,omitempty"`
	StartDate       *EditInfoSectionField `json:"start_date,omitempty"`
	PreferredCenter *EditInfoSectionField `json:"preferred_center,omitempty"`
}

type EditInfoSectionField struct {
	Optional             bool                                      `json:"optional"`
	Placeholder          *sushi.TextSnippet                        `json:"placeholder,omitempty"`
	DurationInDays       int32                                     `json:"duration_in_days,omitempty"`
	Title                *sushi.TextSnippet                        `json:"title,omitempty"`
	EditButton           *EditButtonCustom                         `json:"edit_button,omitempty"`
	PlaceholderContainer *EditInfoSectionFieldPlaceholderContainer `json:"placeholder_container,omitempty"`
	States               EditInfoSectionFieldStates                `json:"states,omitempty"`
}

type EditButtonCustom struct {
	Text        string             `json:"text,omitempty"`
	ClickAction *sushi.ClickAction `json:"click_action,omitempty"`
}

type EditInfoSectionFieldStates struct {
	Always     *EditInfoSectionFieldStateTitle      `json:"always,omitempty"`
	Empty      *EditInfoSectionFieldStateTitle      `json:"empty,omitempty"`
	CharLength *EditInfoSectionFieldStateCharLength `json:"char_length,omitempty"`
	MinAge     *EditInfoSectionFieldStateMinAge     `json:"min_age,omitempty"`
}

type EditInfoSectionFieldPlaceholderContainer struct {
	Title       *sushi.TextSnippet `json:"title,omitempty"`
	Icon        *sushi.Icon        `json:"icon,omitempty"`
	ClickAction *sushi.ClickAction `json:"click_action,omitempty"`
}

type EditInfoSectionFieldStateTitle struct {
	Title *sushi.TextSnippet `json:"title,omitempty"`
}

type EditInfoSectionFieldStateCharLength struct {
	Length     int32              `json:"length,omitempty"`
	ErrorTitle *sushi.TextSnippet `json:"error_title,omitempty"`
}

type EditInfoSectionFieldStateMinAge struct {
	Age        int32              `json:"age,omitempty"`
	ErrorTitle *sushi.TextSnippet `json:"error_title,omitempty"`
}
