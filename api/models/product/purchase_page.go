package product

import "bitbucket.org/jogocoin/go_api/api/models/sushi"

type ResultSection struct {
	LayoutConfig                       *sushi.LayoutConfig                       `json:"layout_config,omitempty"`
	FitsoHeaderSnippetType1            *sushi.FitsoHeaderSnippetType1Snippet     `json:"fitso_header_snippet_type_1,omitempty"`
	FitsoTextSnippetType1              *sushi.FitsoTextSnippetType1Snippet       `json:"fitso_text_snippet_type_1,omitempty"`
	FitsoTextSnippetType2              *sushi.FitsoTextSnippetType2Snippet       `json:"fitso_text_snippet_type_2,omitempty"`
	FitsoTextSnippetType3              *sushi.FitsoTextSnippetType3Snippet       `json:"fitso_text_snippet_type_3,omitempty"`
	ImageTextSnippetType41             *sushi.ImageTextSnippetType41Snippet      `json:"image_text_snippet_type_41,omitempty"`
	V2ImageTextSnippetType43           *sushi.V2ImageTextSnippetType43Snippet    `json:"v2_image_text_snippet_type_43,omitempty"`
	FooterSnippetType2                 *sushi.FooterSnippetType2Snippet          `json:"footer_snippet_type_2,omitempty"`
	PurchaseWidgetSnippet3             *sushi.PurchaseWidgetSnippetType3         `json:"purchase_widget_snippet_3,omitempty"`
	SnippetConfig                      *sushi.SnippetConfig                      `json:"snippet_config,omitempty"`
	V2ImageSnippetType10               *sushi.V2ImageTextSnippetType10Snippet    `json:"v2_image_text_snippet_type_10,omitempty"`
	SectionHeaderType1                 *sushi.SectionHeaderType1Snippet          `json:"section_header_type_1,omitempty"`
	ImageTextSnippetType35             *sushi.ImageTextSnippetType35Snippet      `json:"image_text_snippet_type_35,omitempty"`
	AccordionSnippetType4Snippet       *sushi.AccordionSnippetType4Snippet       `json:"accordion_snippet_type_4,omitempty"`
	FitsoTextSnippetType4              *sushi.FitsoTextType4                     `json:"fitso_text_snippet_type_4,omitempty"`
	FitsoPurchaseSnippetType1          *sushi.FitsoPurchaseSnippetType1Snippet   `json:"fitso_purchase_snippet_type_1,omitempty"`
	FitsoPurchaseSnippetType2          *sushi.FitsoPurchaseSnippetType2Snippet   `json:"fitso_purchase_snippet_type_2,omitempty"`
	FitsoImageTextSnippetType19Snippet *sushi.FitsoImageTextSnippetType19Snippet `json:"fitso_image_text_snippet_type_19,omitempty"`
	ImageTextSnippetType19             *sushi.ImageTextSnippetType19Snippet      `json:"image_text_snippet_type_19,omitempty"`
}
