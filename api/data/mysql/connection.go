package mysql

import (
	"fmt"
	// "github.com/gin-gonic/gin"
	"bitbucket.org/jogocoin/go_api/api/data/mysql/models"
	"bitbucket.org/jogocoin/go_api/api/structs"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"github.com/micro/go-micro/config"
)

func Connect(conf *structs.Config) (db *gorm.DB, err error) {

	fmt.Println(config.Get("codeIndependent", "mysqlConnectURI").String(""))
	mysqlConnectURI := config.Get("codeIndependent", "mysqlConnectURI").String("go-app-user:jF9Crcut2yD5zomA@tcp(139.162.54.168:3306)/jogo_new?charset=utf8&parseTime=True&loc=Local")

	db, err = gorm.Open("mysql", mysqlConnectURI)

	if err != nil {
		panic(err)
	}

	db.LogMode(true)

	db.AutoMigrate(&models.User{})

	return db, err
}
