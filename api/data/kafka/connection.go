package kafka

import (
	//"fmt"
	"github.com/micro/go-micro/broker"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-plugins/broker/kafka"
	"log"
)

func KafkaConnect() (db broker.Broker) {
	var kafkaAddrs []string

	conf := config.Map()["codeIndependent"].(map[string]interface{})
	kafkaBrokerURL := conf["kafkaBrokerURL"].([]interface{})

	for _, url := range kafkaBrokerURL {
		kafkaAddrs = append(kafkaAddrs, url.(string))
	}

	kbroker := kafka.NewBroker(func(options *broker.Options) {
		options.Addrs = kafkaAddrs
	})

	if err := kbroker.Init(); err != nil {
		log.Fatalf("Kafka Broker Init error: %v", err)
	}

	if err := kbroker.Connect(); err != nil {
		log.Fatalf("Kafka Broker Connect error: %v", err)
	}

	return kbroker
}
