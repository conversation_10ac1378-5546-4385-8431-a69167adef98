package config

import (
	"github.com/Shopify/sarama"
)

type KafkaConfig struct {
	servers []string
	version string
}

func NewKafkaConfig(servers []string, version string) *KafkaConfig {
	return &KafkaConfig{servers: servers, version: version}
}

func (k *KafkaConfig) Servers() []string {
	return k.servers
}

func (k *KafkaConfig) Version() string {
	return k.version
}

func (k KafkaConfig) ToProducerConfig() *sarama.Config {
	// Set options as required
	config := sarama.NewConfig()
	config.Producer.Return.Successes = true
	config.Producer.Return.Errors = true
	return config
}
