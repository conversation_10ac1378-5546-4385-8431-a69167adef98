setup:
	go mod init "bitbucket.org/jogocoin/go_api/auth_service"

dep:
	dep ensure

build:
	protoc -I. \
	  -I$(GOPATH)/src/github.com/grpc-ecosystem/grpc-gateway/third_party/googleapis \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/auth_service \
		proto/auth/auth.proto
	echo "machine bitbucket.org login $(BITBUCKET_USER_NAME) password $(BITBUCKET_USER_PASS)" > $(GOPATH)/src/go_api/auth_service/.netrc

local:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50053 MICRO_REGISTRY=mdns GOPRIVATE="bitbucket.org" go run main.go

local2:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50053 MICRO_REGISTRY=mdns go run main.go
aws_dev:
	make build
	MICRO_MODE=qa MICRO_SERVER_ADDRESS=:50053 MICRO_REGISTRY=mdns go run main.go

aws_prod:
	make build
	MICRO_MODE=prod MICRO_SERVER_ADDRESS=:50053 MICRO_REGISTRY=mdns go run main.go

docker_build:
	make build
	docker build -t auth_service .

rund:
	docker run --rm -it -p 50053:50053 --network="host" -e MICRO_SERVER_ADDRESS=:50053 -e MICRO_REGISTRY=mdns -e DB_USER=root -e DB_NAME=newSchema -e DB_PASSWORD=newPassword auth_service

redis:
	docker run -it --network="host" --rm redis redis-cli -h auth-redis
