package main

import (
	"fmt"
	"log"
	"time"

	pb "go_api/auth_service/proto/auth"

	interceptor "bitbucket.org/jogocoin/go_api/pkg/interceptor"

	data "go_api/auth_service/data"
	authHandler "go_api/auth_service/handler"
	authSubscriber "go_api/auth_service/subscriber"

	newrelic "bitbucket.org/jogocoin/go_api/pkg/newrelic"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/server"

	"go_api/auth_service/setupFunc"

	"github.com/micro/go-micro"
	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	// initial setup
	setupFunc.SetUp()

	db, err := data.MysqlConnect()
	defer db.Close()

	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	db.AutoMigrate(&pb.Usertest{})

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	authData := &data.AuthData{Db: db, Client: redisClient}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.auth"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(authHandler.LogWrapper,
			nrmicro.HandlerWrapper(app),
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	// register a new subscriber function to the service
	micro.RegisterSubscriber("product.subscriber.auth.test", newService.Server(), authSubscriber.SubTestEvent, server.SubscriberQueue("queue.pubsub"))

	pb.RegisterAuthServiceHandler(newService.Server(), &authHandler.Service{authData})

	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}

}
