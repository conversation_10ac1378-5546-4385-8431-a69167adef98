module bitbucket.org/jogocoin/go_api/cron_service

go 1.13

replace github.com/micro/protoc-gen-micro v1.0.0 => /go/src/go_api/cron_service/protoc-gen-micro@v0.8.0

replace bitbucket.org/jogocoin/go_api/pkg => /go/src/go_api/cron_service/pkg

require (
	bitbucket.org/jogocoin/go_api/pkg v0.0.0-20220524135150-798cf1e2b24d
	github.com/golang/protobuf v1.4.1
	github.com/google/go-cmp v0.5.0 // indirect
	github.com/jasonlvhit/gocron v0.0.0-20191228163020-98b59b546dee
	github.com/micro/go-micro v1.18.0
	github.com/micro/go-plugins/broker/kafka v0.0.0-20200119172437-4fe21aa238fd
	github.com/newrelic/go-agent v3.10.0+incompatible
	github.com/nlopes/slack v0.6.0
	golang.org/x/net v0.0.0-20200324143707-d3edc9973b7e
	google.golang.org/grpc v1.26.0
	google.golang.org/protobuf v1.23.0
)
