package producer

import (
	"bitbucket.org/jogocoin/go_api/pkg/kafka/config"
	"encoding"
	"github.com/Shopify/sarama"
	"log"
)

type SyncProducer struct {
	producer sarama.SyncProducer
}

func NewSyncProducerWithConfig(config *config.KafkaConfig) (s *SyncProducer, er error) {
	producer, er := sarama.NewSyncProducer(config.Servers(), config.ToProducerConfig())
	if er != nil {
		log.Printf("Failed to start sarama sync producer : %s", er.Error())
		return
	}
	return &SyncProducer{
		producer: producer,
	}, er
}

func NewSyncProducer() (s *SyncProducer, er error) {
	kafkaConfigs := config.GetKafkaRepositoryInstance()
	onlineConfig := kafkaConfigs.GetConfig()
	producer, err := sarama.NewSyncProducer(onlineConfig.Servers(), onlineConfig.ToProducerConfig())
	if err != nil {
		log.Printf("Failed to start sarama sync producer : %s", err.Error())
		return
	}
	return &SyncProducer{
		producer: producer,
	}, er
}

// todo add support for partition key
func (p *SyncProducer) Send(topic string, message encoding.BinaryMarshaler) error {
	messageBytes, err := message.MarshalBinary()
	if err != nil {
		return err
	}

	_, _, err = p.producer.SendMessage(&sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(messageBytes),
	})

	return err
}

func (p *SyncProducer) Close() (err error) {
	err = p.producer.Close()
	if err != nil {
		log.Printf("unable to close sync producer : %s", err.Error())
	}

	return err
}
