package config

type TopicConfig struct {
	numPartitions     int32
	replicationFactor int
}

func NewTopicConfig(numPartitions int32, replicationFactor int) *TopicConfig {
	return &TopicConfig{numPartitions: numPartitions, replicationFactor: replicationFactor}
}

func (t TopicConfig) ReplicationFactor() int {
	return t.replicationFactor
}

func (t TopicConfig) NumPartitions() int32 {
	return t.numPartitions
}
