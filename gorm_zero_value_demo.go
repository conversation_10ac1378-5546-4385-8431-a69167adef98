package main

import (
	"fmt"
	"log"
	"time"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
)

// Purchase model - simplified version of your actual model
type Purchase struct {
	PurchaseId          int32     `gorm:"primary_key;column:purchase_id"`
	UserId              int32     `gorm:"column:user_id"`
	ProductId           int32     `gorm:"column:product_id"`
	PaymentStatus       int32     `gorm:"column:payment_status"`
	PaymentRequestToken string    `gorm:"column:payment_request_token"`
	Amount              float32   `gorm:"column:amount"`
	CreatedAt           time.Time `gorm:"column:created_at"`
}

// PurchaseRequest - matches your protobuf structure
type PurchaseRequest struct {
	PurchaseId          int32
	UserId              int32
	ProductId           int32
	PaymentStatus       int32
	PaymentRequestToken string
}

func main() {
	// Create in-memory SQLite database
	db, err := gorm.Open("sqlite3", ":memory:")
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Enable SQL logging to see actual queries
	db.LogMode(true)

	// Create table
	db.AutoMigrate(&Purchase{})

	// Insert sample data
	samplePurchases := []Purchase{
		{PurchaseId: 1, UserId: 100, ProductId: 1, PaymentStatus: 2, Amount: 1000.0, CreatedAt: time.Now()},
		{PurchaseId: 2, UserId: 101, ProductId: 2, PaymentStatus: 1, Amount: 2000.0, CreatedAt: time.Now()},
		{PurchaseId: 3, UserId: 102, ProductId: 1, PaymentStatus: 2, Amount: 1500.0, CreatedAt: time.Now()},
		{PurchaseId: 4, UserId: 100, ProductId: 3, PaymentStatus: 0, Amount: 500.0, CreatedAt: time.Now()},
		{PurchaseId: 5, UserId: 103, ProductId: 2, PaymentStatus: 2, Amount: 3000.0, CreatedAt: time.Now()},
	}

	for _, purchase := range samplePurchases {
		db.Create(&purchase)
	}

	fmt.Println("=== GORM Zero Value Demonstration ===\n")

	// Test Case 1: PurchaseId = 0 (DANGEROUS!)
	fmt.Println("🚨 TEST CASE 1: PurchaseId = 0 (ALL ZERO VALUES)")
	fmt.Println("This simulates what happens in your academy booking issue...")
	testDangerousQuery(db)

	// Test Case 2: PurchaseId = 0 but other fields have values
	fmt.Println("\n✅ TEST CASE 2: PurchaseId = 0 but UserId = 100")
	testMixedQuery(db)

	// Test Case 3: PurchaseId > 0 (SAFE)
	fmt.Println("\n✅ TEST CASE 3: PurchaseId = 1 (SAFE)")
	testSafeQuery(db)

	// Test Case 4: PaymentStatus = 0 (ZERO VALUE IGNORED!)
	fmt.Println("\n🚨 TEST CASE 4: PaymentStatus = 0 (ZERO VALUE IGNORED)")
	testPaymentStatusZero(db)

	fmt.Println("\n=== SOLUTION: Explicit WHERE conditions ===")
	testSafeApproach(db)

	// Test Case 5: Finding UserId = 0 (IMPOSSIBLE with GORM Where(&struct))
	fmt.Println("\n🚨 TEST CASE 5: Finding UserId = 0 (GORM limitation)")
	testFindUserIdZero(db)
}

func testDangerousQuery(db *gorm.DB) {
	fmt.Println("Request: {PurchaseId: 0, UserId: 0, ProductId: 0, PaymentStatus: 0}")

	// This is what your current code does - DANGEROUS!
	req := PurchaseRequest{
		PurchaseId:    0, // Zero value
		UserId:        0, // Zero value
		ProductId:     0, // Zero value
		PaymentStatus: 0, // Zero value
	}

	purchaseObj := &Purchase{
		PurchaseId:    req.PurchaseId,
		UserId:        req.UserId,
		ProductId:     req.ProductId,
		PaymentStatus: req.PaymentStatus,
	}

	var purchases []Purchase
	fmt.Println("Executing: db.Where(&purchaseObj).Find(&purchases)")
	result := db.Where(purchaseObj).Find(&purchases)

	fmt.Printf("❌ RESULT: Found %d purchases (ALL PURCHASES!)\n", len(purchases))
	fmt.Printf("❌ This would load ALL purchases into memory!\n")
	if result.Error != nil {
		fmt.Printf("Error: %v\n", result.Error)
	}
}

func testMixedQuery(db *gorm.DB) {
	req := PurchaseRequest{
		PurchaseId: 0,   // Zero value (ignored)
		UserId:     100, // Non-zero value (used)
	}

	purchaseObj := &Purchase{
		PurchaseId: req.PurchaseId,
		UserId:     req.UserId,
	}

	var purchases []Purchase
	fmt.Println("Executing: db.Where(&purchaseObj).Find(&purchases)")
	result := db.Where(purchaseObj).Find(&purchases)

	fmt.Printf("✅ RESULT: Found %d purchases for UserId=100\n", len(purchases))
	if result.Error != nil {
		fmt.Printf("Error: %v\n", result.Error)
	}
}

func testSafeQuery(db *gorm.DB) {
	req := PurchaseRequest{
		PurchaseId: 1, // Non-zero value
	}

	purchaseObj := &Purchase{
		PurchaseId: req.PurchaseId,
	}

	var purchases []Purchase
	fmt.Println("Executing: db.Where(&purchaseObj).Find(&purchases)")
	result := db.Where(purchaseObj).Find(&purchases)

	fmt.Printf("✅ RESULT: Found %d purchases for PurchaseId=1\n", len(purchases))
	if result.Error != nil {
		fmt.Printf("Error: %v\n", result.Error)
	}
}

func testPaymentStatusZero(db *gorm.DB) {
	fmt.Println("Request: {PurchaseId: 4, PaymentStatus: 0}")
	fmt.Println("Note: PaymentStatus=0 exists in database but GORM ignores it!")

	purchaseObj := &Purchase{
		PurchaseId:    4, // This exists
		PaymentStatus: 0, // This exists but GORM ignores zero values!
	}

	var purchases []Purchase
	fmt.Println("Executing: db.Where(&purchaseObj).Find(&purchases)")
	result := db.Where(purchaseObj).Find(&purchases)

	fmt.Printf("⚠️  RESULT: Found %d purchases (PaymentStatus=0 was IGNORED!)\n", len(purchases))
	if result.Error != nil {
		fmt.Printf("Error: %v\n", result.Error)
	}
}

func testSafeApproach(db *gorm.DB) {
	fmt.Println("\n🔧 SAFE APPROACH: Explicit WHERE conditions")

	req := PurchaseRequest{
		PurchaseId:    0, // Zero value
		UserId:        0, // Zero value
		PaymentStatus: 0, // Zero value
	}

	// Build query explicitly
	query := db.Select("purchase_id, user_id, product_id, payment_status, amount")

	hasConditions := false
	if req.PurchaseId > 0 {
		query = query.Where("purchase_id = ?", req.PurchaseId)
		hasConditions = true
	}
	if req.UserId > 0 {
		query = query.Where("user_id = ?", req.UserId)
		hasConditions = true
	}
	if req.PaymentStatus != 0 {
		query = query.Where("payment_status = ?", req.PaymentStatus)
		hasConditions = true
	}

	if !hasConditions {
		fmt.Println("🚨 BLOCKED: No valid WHERE conditions provided!")
		fmt.Println("✅ This prevents SELECT * FROM purchases!")
		return
	}

	var purchases []Purchase
	result := query.Limit(100).Find(&purchases)

	fmt.Printf("✅ RESULT: Found %d purchases with explicit conditions\n", len(purchases))
	if result.Error != nil {
		fmt.Printf("Error: %v\n", result.Error)
	}
}

func testFindUserIdZero(db *gorm.DB) {
	// First, let's add a purchase with UserId = 0 to test
	zeroPurchase := Purchase{
		PurchaseId:    999,
		UserId:        0, // Zero UserId
		ProductId:     1,
		PaymentStatus: 1,
		Amount:        100.0,
		CreatedAt:     time.Now(),
	}
	db.Create(&zeroPurchase)
	fmt.Println("✅ Added purchase with UserId = 0 for testing")

	fmt.Println("\n❌ WRONG WAY: Using GORM Where(&struct)")
	fmt.Println("Request: Find all purchases where UserId = 0")

	// This is WRONG - GORM ignores UserId = 0
	req := PurchaseRequest{UserId: 0}
	purchaseObj := &Purchase{UserId: req.UserId}

	var purchases1 []Purchase
	fmt.Println("Executing: db.Where(&purchaseObj).Find(&purchases)")
	result1 := db.Where(purchaseObj).Find(&purchases1)
	fmt.Printf("❌ RESULT: Found %d purchases (WRONG! Should find 1 but finds ALL)\n", len(purchases1))
	fmt.Println("❌ GORM ignored UserId = 0 and returned ALL purchases!")

	fmt.Println("\n✅ CORRECT WAY: Explicit WHERE clause")
	var purchases2 []Purchase
	fmt.Println("Executing: db.Where(\"user_id = ?\", 0).Find(&purchases)")
	result2 := db.Where("user_id = ?", 0).Find(&purchases2)
	fmt.Printf("✅ RESULT: Found %d purchases with UserId = 0 (CORRECT!)\n", len(purchases2))

	if len(purchases2) > 0 {
		fmt.Printf("✅ Found purchase: ID=%d, UserId=%d, Amount=%.2f\n",
			purchases2[0].PurchaseId, purchases2[0].UserId, purchases2[0].Amount)
	}

	if result1.Error != nil {
		fmt.Printf("Error 1: %v\n", result1.Error)
	}
	if result2.Error != nil {
		fmt.Printf("Error 2: %v\n", result2.Error)
	}
}
