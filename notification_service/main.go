package main

import (
	// "context"
	"fmt"
	"log"
	"time"

	pb "bitbucket.org/jogocoin/go_api/notification_service/proto/notification"
	"bitbucket.org/jogocoin/go_api/pkg/newrelic"

	interceptor "bitbucket.org/jogocoin/go_api/pkg/interceptor"

	data "bitbucket.org/jogocoin/go_api/notification_service/data"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/server"

	//model "bitbucket.org/jogocoin/go_api/notification_service/data/model"
	//handler "bitbucket.org/jogocoin/go_api/notification_service/handler"
	notificationHandler "bitbucket.org/jogocoin/go_api/notification_service/handler"
	notificationSubscriber "bitbucket.org/jogocoin/go_api/notification_service/subscriber"

	// "github.com/micro/cli"
	"github.com/micro/go-micro"
	//"github.com/micro/go-micro/broker"
	"bitbucket.org/jogocoin/go_api/notification_service/setupFunc"
	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	// initial setup
	setupFunc.SetUp()

	kbroker := setupFunc.KafkaConnect()

	db, errM := data.MysqlConnect()
	defer db.Close()

	if errM != nil {
		log.Fatalf("Could not connect to DB: %v", errM)
	}

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	notificationData := &data.NotificationData{Db: db, Client: redisClient}

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.notification"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(
			notificationHandler.LogWrapper,
			notificationHandler.SetContextWrapper,
			nrmicro.HandlerWrapper(app),
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	// register a new subscriber function to the service
	micro.RegisterSubscriber("product.subscriber.notification", newService.Server(), notificationSubscriber.SubTestEvent, server.SubscriberQueue("queue.pubsub"))

	serviceHandler := notificationHandler.Service{notificationData}
	pb.RegisterNotificationServiceHandler(newService.Server(), &serviceHandler)

	setupFunc.InitializeTopics(kbroker, &serviceHandler)

	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}

	// //remove later -- for testing notification log create
	// var uids []int32
	// uids = append(uids, 1)
	// uids = append(uids, 2)
	// uids = append(uids, 3)
	// notiData := pb.NotificationLogRequest{
	// 	UserId:     uids,
	// 	Title:      "Hello",
	// 	Text:       "Hello text body",
	// 	ActionId:   1,
	// 	CtaId:      1,
	// 	CtaText:    "Text CTA",
	// 	ExpiryDate: 1568073600,
	// }
	// var ackVal *pb.Ack

	// response := serviceHandler.CreateNotificationLogs(context.TODO(), &notiData, ackVal)
	// fmt.Println(" ------------------------------", response)

}
