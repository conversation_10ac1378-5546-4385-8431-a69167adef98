package messageHandler

import (
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/message"
	"context"
)

type PubSubMessageCommonHandler struct {
	queueName string
	groupId   string
	handler   IPubSubMessageHandler
}

func NewPubSubMessageCommonHandler(queueName string, groupId string, handler IPubSubMessageHandler) *PubSubMessageCommonHandler {
	return &PubSubMessageCommonHandler{queueName: queueName, groupId: groupId, handler: handler}
}

// all pubsub msgs will land here
func (p *PubSubMessageCommonHandler) Handle(ctx context.Context, msg *message.PubSubMessage) error {
	err := p.handler.Process(ctx, msg)
	if err != nil {
		return err
	}
	return nil
}
