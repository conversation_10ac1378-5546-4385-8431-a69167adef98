package clients

import (
	"context"
	"errors"
	"log"

	"bitbucket.org/jogocoin/go_api/pkg/kafka/config"
	"bitbucket.org/jogocoin/go_api/pkg/kafka/consumer"
	"bitbucket.org/jogocoin/go_api/pkg/kafka/producer"
	"bitbucket.org/jogocoin/go_api/pkg/kafka/subscriber"
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/message"
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/messageHandler"
)

type KafkaQueueClient struct {
	groupId           string
	topic             string
	offset            string
	brokers           string
	instanceType      string
	messageType       message.MessageType
	KafkaSyncProducer producer.IKafkaProducer
}

func NewKafkaQueueClient(topic string, messageType message.MessageType, instanceType string, offset string, groupId string, brokerUrls string) *KafkaQueueClient {
	return &KafkaQueueClient{
		groupId:      groupId,
		topic:        topic,
		offset:       offset,
		instanceType: instanceType,
		messageType:  messageType,
		brokers:      brokerUrls,
	}
}

func (k *KafkaQueueClient) Publish(ctx context.Context, message *message.PubSubMessage) (err error) {
	kafkConfig := config.ValidateAndExtractConfig(k.brokers, "2.0.0")

	if k.KafkaSyncProducer == nil {
		log.Printf("pkg pubsub: producer not found, creating a producer")
		k.KafkaSyncProducer, err = producer.NewSyncProducerWithConfig(kafkConfig)

		if err != nil {
			log.Println("ERROR in getting KAFKA producer", err)
			err = errors.New("ERROR in getting KAFKA producer")
			return
		}
	} else {
		log.Printf("pkg pubsub: producer found")
	}

	er := k.KafkaSyncProducer.Send(k.topic, message)
	log.Printf("Publish to kafka topic = %s", k.topic)
	if er != nil {
		err = er
		return
	}
	return
}

func (k *KafkaQueueClient) Subscribe(ctx context.Context, handler messageHandler.IPubSubMessageHandler) (customErr error) {
	//consumer1 := consumer.NewKafkaConsumer()
	kafkConfig := config.ValidateAndExtractConfig(k.brokers, "2.0.0")
	consumer1 := consumer.NewKafkaConsumerWithConfig(kafkConfig)
	subscriptionForConsumer := subscriber.NewKafkaSubscriptionWithOffSet(k.groupId, k.topic, k.offset, k.groupId)
	kafkaMessageHandler := messageHandler.NewKafkaMessageHandler(k.topic, k.groupId, handler, k.messageType)
	customErr = consumer1.Subscribe(ctx, kafkaMessageHandler, subscriptionForConsumer)
	if customErr != nil {
		log.Println("Failed to create subscriber: ", customErr)
		return
	}
	return
}
