package setupFunc

import (
	"log"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/micro/go-micro/config"
)

func AWSConnect() *session.Session {
	region := config.Get("codeIndependent", "sqsMembershipUpdateQueue", "queueRegion").String("")
	sess, err := session.NewSessionWithOptions(session.Options{
		Profile: "default",
		Config: aws.Config{
			Region: aws.String(region),
		},
	})
	if err != nil {
		log.Printf("Failed to initialize aws session: %v", err)
	} else {
		log.Printf("Successfully connected to Aws")
	}
	return sess
}
