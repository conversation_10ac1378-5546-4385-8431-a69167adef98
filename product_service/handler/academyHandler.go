package productHandler

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sort"
	"time"

	models "bitbucket.org/jogocoin/go_api/product_service/data/model"
	"bitbucket.org/jogocoin/go_api/product_service/internal/util"
	bookingPB "bitbucket.org/jogocoin/go_api/product_service/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/product_service/proto/facility_sport"
	pb "bitbucket.org/jogocoin/go_api/product_service/proto/product"
	structs "bitbucket.org/jogocoin/go_api/product_service/structs"
	ptypes "github.com/golang/protobuf/ptypes"
)
	
func (s *Service) GetSubscriptionSlotMappings(ctx context.Context, req *pb.SubscriptionSlotMappingsRequest, res *pb.SubscriptionSlotMappingsResponse) error {
	slotMappings, err := s.Data.GetSubscriptionSlotMappings(ctx, req.SubscriptionId)

	if err != nil {
		log.Printf("error in GetSubscriptionSlotMappings for getting mappings: %v", err)
		return fmt.Errorf("error in GetSubscriptionSlotMappings for getting mappings: %v", err)
	}

	facilityClient := util.GetFacilitySportClient()
	slotIdsMap := make(map[int32]int8)
	var fs_ids []int32
	for _, v := range slotMappings {
		if v.SlotId1 > 0 {
			slotIdsMap[v.SlotId1] = 1
		}
		if v.SlotId2 > 0 {
			slotIdsMap[v.SlotId2] = 1
		}
		fs_ids = append(fs_ids, v.FsId)
	}

	facilitySportDetails, err := facilityClient.BatchGetFacilitySportDetails(ctx, &facilitySportPB.BatchGetFacilitySportDetailsReq{
		FsIds: fs_ids,
	})
	if err != nil {
		log.Printf("Error in getting facility details for Ids %v, err %v", fs_ids, err)
		return fmt.Errorf("Error in getting facility details for Ids %v, err %v", fs_ids, err)
	}

	var slotIds []int32
	for slotId := range slotIdsMap {
		slotIds = append(slotIds, slotId)
	}

	slotDetailsResponse, err := facilityClient.BatchSlotGet(ctx, &facilitySportPB.BatchSlotGetRequest{
		SlotIds: slotIds,
	})
	if err != nil {
		return fmt.Errorf("Error in getting slot details for slot_ids: %v, error %v", slotIds, err)
	}
	slotDataMap := slotDetailsResponse.SlotsMap

	for _, v := range slotMappings {
		var timings []string
		if v.SlotId1 > 0 {
			if v.SlotId1 == 12 {
				timings = append(timings, "4 - 5 PM")
			} else if v.SlotId1 == 13 {
				timings = append(timings, "5 - 6 PM")
			} else if v.SlotId1 == 14 {
				timings = append(timings, "6 - 7 PM")
			} else {
				timings = append(timings, slotDataMap[v.SlotId1].Timing)
			}
		}
		if v.SlotId2 > 0 {
			if v.SlotId2 == 12 {
				timings = append(timings, "4 - 5 PM")
			} else if v.SlotId2 == 13 {
				timings = append(timings, "5 - 6 PM")
			} else if v.SlotId2 == 14 {
				timings = append(timings, "6 - 7 PM")
			} else {
				timings = append(timings, slotDataMap[v.SlotId2].Timing)
			}
		}
		facilityDetail := &pb.Facility{
			ShortName: facilitySportDetails.FacilitySportsMap[v.FsId].ShortName,
		}
		res.SubscriptionSlotMappings = append(res.SubscriptionSlotMappings, &pb.SubscriptionSlotMapping{
			FacilitySlot: &pb.FacilitySlot{
				AcademySlotId: v.AcademySlotId,
				FsId:          v.FsId,
				SlotId1:       v.SlotId1,
				SlotId2:       v.SlotId2,
				SlotTiming:    strings.Join(timings, ", "),
			},
			FacilityDetails:  facilityDetail,
			CourseCategoryId: v.CourseCategoryId,
		})
	}

	return nil
}

func (s *Service) UpdateAcademySlotsCapacityAfterSubscriptionExpires(ctx context.Context, req *pb.Empty, res *pb.Empty) error {
	academySlots, err := s.Data.GetAcademyExpiredSubscriptionsInLastDay(ctx)
	if err != nil {
		log.Println(err)
		return err
	}

	bookingClient := util.GetBookingServiceClient()
	for _, v := range academySlots {
		academySubscriptionReq := &pb.GetUserSubscriptionStatusRequest{
			UserId: v.UserId,
			CityId: v.CityId, 
		}
		var academySubscriptionRes pb.GetUserSubscriptionStatusResponse
		err = s.GetUserAcademySubscriptionStatus(ctx, academySubscriptionReq, &academySubscriptionRes)
		if err != nil {
			log.Printf("Error in getting subscription status for cron UserID %d, err %v", v.UserId, err)
			return err
		}
		var academySlot int32
		if academySubscriptionRes.SubscriptionStatus == pb.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {

			mappings, err := s.Data.GetSubscriptionSlotMappings(ctx, academySubscriptionRes.ActiveSubscription.SubscriptionId)
			if err != nil || len(mappings) == 0 {
				log.Printf("Error in getting slot mappings for subscription_id: %d, error: %v", academySubscriptionRes.ActiveSubscription.SubscriptionId, err)
				return fmt.Errorf("Error in getting slot mappings for subscription_id: %d, error: %v", academySubscriptionRes.ActiveSubscription.SubscriptionId, err)
			}
			academySlot = mappings[0].AcademySlotId
		} else if academySubscriptionRes.SubscriptionStatus == pb.GetUserSubscriptionStatusResponse_FUTURE_PREMIUM_SUBSCRIPTION {

			mappings, err := s.Data.GetSubscriptionSlotMappings(ctx, academySubscriptionRes.FutureSubscription.SubscriptionId)
			if err != nil || len(mappings) == 0 {
				log.Printf("Error in getting slot mappings for future subscription_id: %d, error: %v", academySubscriptionRes.FutureSubscription.SubscriptionId, err)
				return fmt.Errorf("Error in getting slot mappings for future subscription_id: %d, error: %v", academySubscriptionRes.FutureSubscription.SubscriptionId, err)
			}
			academySlot = mappings[0].AcademySlotId
		}

		if academySlot > 0 {
			var isRelatedAcademyProductPurchased bool
			academySlotReq := &bookingPB.AcademySlot{
				Id: academySlot,
			}
			academySlotResponse, err := bookingClient.GetRelatedAcademySlotDetails(ctx, academySlotReq)
			if err != nil {
				log.Printf("UpdateAcademySlotsCapacityAfterSubscriptionExpires, error in getting related academy slots data for academy slot id: %d, err: %v", academySlot, err)
				return err
			}
			if academySlotResponse.AcademySlots != nil && len(academySlotResponse.AcademySlots) > 0 {
				for _, academySlotData := range academySlotResponse.AcademySlots {
					if academySlotData.Id == v.AcademySlotId {
						isRelatedAcademyProductPurchased = true
						break
					}
				}
				if isRelatedAcademyProductPurchased {
					continue
				}
			}
		}

		request := &bookingPB.UpdateAcademySlotRequest{
			AcademySlotId: v.AcademySlotId,
		}
		response, err := bookingClient.IncreaseAcademySlotCapacity(ctx, request)
		if err != nil {
			log.Printf("Error in increasing capacity for AcademySlotId %d, err %v", v.AcademySlotId, err)
			return fmt.Errorf("Error in increasing capacity for AcademySlotId %d, err %v", v.AcademySlotId, err)
		}
		if response.Status != nil && response.Status.Status != success {
			log.Printf("Error in increasing capacity for AcademySlotId %d due to %s", v.AcademySlotId, response.Status.Message)
			return fmt.Errorf("Error in increasing capacity for AcademySlotId %d due to %s", v.AcademySlotId, response.Status.Message)
		}
		log.Printf("UpdateAcademySlotsCapacityAfterSubscriptionExpires: Capacity Increased for academy_subscription obj:%v\n", v)
	}
	return nil
}

func (s *Service) GetProductCourseMappingsDetails(ctx context.Context, req *pb.ProductCourseDetailsRequest, res *pb.ProductCourseDetailsResponse) error {

	productCourseMappings, err := s.Data.GetProductCourseMappingsForIds(ctx, req.ProductCourseMappingIds)
	if err != nil {
		log.Println(err)
		return err
	}
	facilityClient := util.GetFacilitySportClient()
	sportData := make(map[int32]*facilitySportPB.SportResponse)
	responseData := make(map[int32]*pb.ProductCourseMapping)
	for _, data := range productCourseMappings {
		if sportData[data.SportId] == nil {
			sportDetails, err := facilityClient.SportGet(ctx, &facilitySportPB.Sport{
				SportId: data.SportId,
			})
			if err != nil || len(sportDetails.Sport) == 0 {
				log.Printf("Error in getting sport details for ID %d, err %v", data.SportId, err)
				return fmt.Errorf("Error in getting sport details for ID %d, err %v", data.SportId, err)
			}
			sportData[data.SportId] = sportDetails
		}
		productDetails := &pb.Product{
			ProductId:          data.ProductId,
			ProductDescription: data.ProductDescription,
		}
		sportDetails := &pb.Sport{
			SportName: sportData[data.SportId].Sport[0].SportName,
			SportId:   data.SportId,
		}
		daysInt, daysStr := GetProductDaysOfWeek(data.DaysOfWeek)

		productCourseMappingDetails := &pb.ProductCourseMapping{
			ProductCourseMappingId: data.ProductCourseMappingId,
			CourseCategoryId:       data.CourseCategoryId,
			SportDetails:           sportDetails,
			ProductDetails:         productDetails,
			DaysText:               daysStr,
			Days:                   daysInt,
		}
		responseData[data.ProductCourseMappingId] = productCourseMappingDetails
	}
	res.ProductCourseMappingsData = responseData
	return nil
}

func (s *Service) GetDataToCreateAcademySessionBookings(
	ctx context.Context,
	req *pb.GetDataToCreateAcademySessionBookingsRequest,
	res *pb.GetDataToCreateAcademySessionBookingsResponse,
) error {
	log.Println("GetDataToCreateAcademySessionBookings: called with req: ", req)
	processTime := time.Unix(req.TimeToProcess, 0)
	loc, _ := time.LoadLocation(DEFAULT_LOCATION)
	bookingDate, _ := time.ParseInLocation(TIME_FORMAT_YMD, processTime.Format(TIME_FORMAT_YMD), loc)
	dayOfWeek := int32(processTime.Weekday())
	if dayOfWeek == 0 { // for sunday
		dayOfWeek = 7
	}

	reqV2 := &structs.AcademySessionBookingRequest{
		Weekday:       dayOfWeek,
		TimeToProcess: processTime,
		UserId:        req.UserId,
		ExcludeFs:     true,
	}
	data, err := s.Data.GetDataToCreateAcademyBookings(ctx, reqV2) 
	if err != nil {
		log.Printf("Error in fetching data to create academy bookings for req: %v, err: %v", reqV2, err)
		return err
	}

	var finalData []*pb.AcademyBookingCreatePayload
	for _, v := range data {
		payload := &pb.AcademyBookingCreatePayload{
			SubscriptionId: v.SubscriptionId,
			ProductId:      v.ProductId,
			UserId:         v.UserId,
			PacsId:         v.PacsId,
			FsId:           v.FsId,
			SlotId:         v.SlotId,
			PurchaseId:     v.PurchaseId,
			BookingDate:    bookingDate.Unix(),
			CultMembershipId:	v.CultMembershipId,
		}
		finalData = append(finalData, payload)
	}
	res.Data = finalData
	return nil
}

func (s *Service) GetAcademyCentersForPurchase(
	ctx context.Context,
	req *pb.GetAcademyCentersForPurchaseRequest,
	res *pb.GetAcademyCentersForPurchaseResponse,
) error {

	reqData := &pb.GetPreferredPlanRequest{
		StartDate:        req.StartDate,
		CourseCategoryId: req.CourseCategoryId,
	}
	var courseProducts pb.GetPreferredPlanResponse
	err := s.GetAcademyProductsForCourseCategory(ctx, reqData, &courseProducts)
	if err != nil {
		log.Println(err)
		return err
	}

	academySlotFilledDataMap := make(map[int32]*pb.AcademySlotFilledData)

	for _, element := range req.AcademySlotFilledData {
		academySlotFilledDataMap[element.AcademySlotId] = element
	}
	previousFacilityIdsMap := make(map[int32]bool)
	for _, facilityId := range req.PreviousFacilityIds {
		previousFacilityIdsMap[facilityId] = true
	}

	academySubscriptionReq := &pb.GetUserSubscriptionStatusRequest{
		UserId: req.UserId,
	}
	var academySubscriptionRes pb.GetUserSubscriptionStatusResponse
	err = s.GetUserAcademySubscriptionStatus(ctx, academySubscriptionReq, &academySubscriptionRes)
	if err != nil {
		log.Printf("Error in getting subscription status for UserID %d, err %v", req.UserId, err)
		return err
	}
	var academySlotToIncrease int32
	if academySubscriptionRes.SubscriptionStatus == pb.GetUserSubscriptionStatusResponse_PREMIUM_SUBSCRIPTION_ACTIVE {
		subEndDate := GetLocalDateTimeFromProtoTime(academySubscriptionRes.ActiveSubscription.SubscriptionEndDate)

		if time.Now().AddDate(0, 0, RENEWAL_ALLOWED_DAYS).After(subEndDate) && academySubscriptionRes.FutureSubscription == nil {

			mappings, err := s.Data.GetSubscriptionSlotMappings(ctx, academySubscriptionRes.ActiveSubscription.SubscriptionId)
			if err != nil || len(mappings) == 0 {
				log.Printf("Error in getting slot mappings for subscription_id: %d, error: %v", academySubscriptionRes.ActiveSubscription.SubscriptionId, err)
				return fmt.Errorf("Error in getting slot mappings for subscription_id: %d, error: %v", academySubscriptionRes.ActiveSubscription.SubscriptionId, err)
			}
			academySlotToIncrease = mappings[0].AcademySlotId

			data := &pb.CacheUserRenewSlotAcademy{
				SubscriptionId: academySubscriptionRes.ActiveSubscription.SubscriptionId,
				AcademySlotId:  academySlotToIncrease,
				UserId:         req.UserId,
			}
			err = s.SetUserRenewSlotForAcademyCache(ctx, data, &pb.Ack{})
			if err != nil {
				log.Printf("Error in caching slot user renew slot for Id %d, subscription_id: %d, error: %v", req.UserId, academySubscriptionRes.ActiveSubscription.SubscriptionId, err)
				return fmt.Errorf("Error in caching slot user renew slot for Id %d,  subscription_id: %d, error: %v", req.UserId, academySubscriptionRes.ActiveSubscription.SubscriptionId, err)
			}
		}
	} else {
		key := s.Data.GetUserRenewSlotForAcademyKey(req.UserId)
		err := s.Data.DeleteCacheKey(key)
		if err != nil {
			log.Printf("Error in deleting cache key %v for %v, err %v", key, academySlotToIncrease, err)
			return fmt.Errorf("Error in deleting cache key %v for %v, err %v", key, academySlotToIncrease, err)
		}
	}

	var fsIds []int32
	fsIdMap := make(map[int32]bool)
	productCourseMappingIdMap := make(map[int32]*pb.ProductCourseMapping)
	facilityProductSlotMap := make(map[string][]*pb.FacilitySlot)
	facilityProductCourseMap := make(map[int32][]int32)
	var courseSport int32
	for _, courseProduct := range courseProducts.Products {
		for _, productMapping := range courseProduct.ProductCourseMappings {
			if _, ok := productCourseMappingIdMap[productMapping.ProductCourseMappingId]; !ok {
				productCourseMappingIdMap[productMapping.ProductCourseMappingId] = productMapping
			}
			for _, allFacilitySlot := range productMapping.AcademySlots {
				fsIdPcmIdKey := fmt.Sprintf("%d-%d", allFacilitySlot.FsId, productMapping.ProductCourseMappingId)
				academySlotId := allFacilitySlot.AcademySlotId
				if _, ok := academySlotFilledDataMap[academySlotId]; ok {
					allFacilitySlot.Capacity -= 1
				}
				if academySlotToIncrease == academySlotId && academySlotToIncrease > 0 {
					allFacilitySlot.Capacity += 1
				}

				allFacilitySlot.Disabled = isDisabled(allFacilitySlot.Capacity)
				facilityProductSlotMap[fsIdPcmIdKey] = append(facilityProductSlotMap[fsIdPcmIdKey], allFacilitySlot)

				if _, ok := fsIdMap[allFacilitySlot.FsId]; !ok {
					fsIds = append(fsIds, allFacilitySlot.FsId)
					fsIdMap[allFacilitySlot.FsId] = true
				}
				if productCourseElement, ok := facilityProductCourseMap[allFacilitySlot.FsId]; ok {
					found := false
					for _, productCourseId := range productCourseElement {
						if productCourseId == productMapping.ProductCourseMappingId {
							found = true
							break
						}
					}
					if !found {
						facilityProductCourseMap[allFacilitySlot.FsId] = append(facilityProductCourseMap[allFacilitySlot.FsId], productMapping.ProductCourseMappingId)
					}
				} else {
					facilityProductCourseMap[allFacilitySlot.FsId] = append(facilityProductCourseMap[allFacilitySlot.FsId], productMapping.ProductCourseMappingId)
				}
			}
			if productMapping.Sport != nil {
				courseSport = productMapping.Sport.SportId
			}
		}
	}

	facilityClient := util.GetFacilitySportClient()
	fsReq := &facilitySportPB.FacilitySportsByFsIdRequest{
		FsIds: fsIds,
	}
	facilitiesData, err := facilityClient.GetAcademyFacilitiesForGivenProduct(ctx, fsReq)
	if err != nil {
		log.Println(err)
		return err
	}

	var facilityList []*pb.Facility
	var facilityIds []int32
	hasMore := false
	for _, elem := range facilitiesData.FacilitySports {
		if len(facilityList) == ACADEMY_PURCHASE_CENTERS_COUNT {
			hasMore = true
			break
		}
		facilityObj := elem.Facility[0]
		if _, ok := previousFacilityIdsMap[facilityObj.FacilityId]; ok {
			continue
		}
		facilityIds = append(facilityIds, facilityObj.FacilityId)
		var productCourseMappingsData []*pb.ProductCourseMapping
		if productCourseMappingIds, ok := facilityProductCourseMap[elem.FsId]; ok {

			for _, productCourseMappingId := range productCourseMappingIds {
				fsIdPcmIdKey := fmt.Sprintf("%d-%d", elem.FsId, productCourseMappingId)
				productCourseMapping := productCourseMappingIdMap[productCourseMappingId]
				productCourseMappingObj := &pb.ProductCourseMapping{
					AcademySlots:           facilityProductSlotMap[fsIdPcmIdKey],
					ProductCourseMappingId: productCourseMapping.ProductCourseMappingId,
					DaysText:               productCourseMapping.DaysText,
					Days:                   productCourseMapping.Days,
					ProductId:              productCourseMapping.ProductId,
				}
				productCourseMappingsData = append(productCourseMappingsData, productCourseMappingObj)
			}
		}
		seasonPoolInfo := &pb.FacilitySeasonalPoolInfo{}
		if facilityObj.SeasonalPoolInfo != nil {
			seasonPoolInfo.Flag = facilityObj.SeasonalPoolInfo.Flag
			seasonPoolInfo.StartDate = facilityObj.SeasonalPoolInfo.StartDate
			seasonPoolInfo.EndDate = facilityObj.SeasonalPoolInfo.EndDate
		}
		facilityData := &pb.Facility{
			FsId:                 elem.FsId,
			FacilityId:           facilityObj.FacilityId,
			DisplayName:          facilityObj.DisplayName,
			DisplayAddress:       facilityObj.DisplayAddress,
			ShortAddress:         facilityObj.ShortAddress,
			SubzoneName:          facilityObj.SubzoneName,
			Rating:               facilityObj.Rating,
			DisplayPicture:       facilityObj.DisplayPicture,
			Distance:             facilityObj.Distance,
			Tag:                  facilityObj.Tag,
			SportName:            elem.SportName,
			ProductCourseMapping: productCourseMappingsData,
			SeasonalPoolInfo:     seasonPoolInfo,
		}
		facilityList = append(facilityList, facilityData)
	}
	res.AcademyCenters = facilityList
	res.HasMore = hasMore
	res.FacilityIds = facilityIds
	res.CourseSport = courseSport
	return nil
}

func isDisabled(capacity int32) bool {
	if capacity == 0 {
		return true
	}
	return false
}

func (s *Service) SetUserRenewSlotForAcademyCache(ctx context.Context, req *pb.CacheUserRenewSlotAcademy, res *pb.Ack) error {
	data := structs.UserRenewSlotAcademy{
		SubscriptionId: req.SubscriptionId,
		AcademySlotId:  req.AcademySlotId,
	}
	if req.AcademySlotId > 0 {
		bcl := util.GetBookingServiceClient()
		var relatedAcademySlotIds []int32
		academySlotReq := &bookingPB.AcademySlot{
			Id: req.AcademySlotId,
		}
		academySlotResponse, err := bcl.GetRelatedAcademySlotDetails(ctx, academySlotReq)
		if err != nil {
			log.Printf("SetUserRenewSlotForAcademyCache, error in getting related academy slots data for academy slot id: %d, err: %v", req.AcademySlotId, err)
			return err
		}
		if academySlotResponse.AcademySlots != nil && len(academySlotResponse.AcademySlots) > 0 {
			for _, academySlotData := range academySlotResponse.AcademySlots {
				relatedAcademySlotIds = append(relatedAcademySlotIds, academySlotData.Id)
			}
		}
		if len(relatedAcademySlotIds) > 0 {
			data.RelatedAcademySlotIds = relatedAcademySlotIds
		}
	}
	err := s.Data.SetUserRenewSlotForAcademyCache(ctx, req.UserId, data)
	if err != nil {
		log.Printf("Error in caching slot user renew slot for Id %d, subscription_id: %d, error: %v", req.UserId, req.SubscriptionId, err)
		return fmt.Errorf("Error in caching slot user renew slot for Id %d,  subscription_id: %d, error: %v", req.UserId, req.SubscriptionId, err)
	}
	return nil
}

func (s *Service) GetAcademyActiveSubscriptionsForFsIds(ctx context.Context, req *pb.GetAcademySubscriptionsForFsIdsRequest, res *pb.GetAcademySubscriptionsForFsIdsResponse) error {
	activeSubscriptions, err := s.Data.GetAcademyActiveSubscriptionForFsIds(ctx, req.FsIdList)

	if err != nil {
		log.Printf("error in GetAcademyActiveSubscriptionsForFsIds: unable to get subscription data for fs ids: %d", req.FsIdList)
		return fmt.Errorf("error in GetAcademyActiveSubscriptionsForFsIds: unable to get subscription data for fs ids: %d", req.FsIdList)
	}

	for _, sub := range activeSubscriptions {
		includeSub := false
		if req.AssessmentFilterType == DUE_ASSESSMENT_FILTER {
			planDuration := fmt.Sprintf("%d %s", sub.Duration, sub.DurationUnit)
			assessmentTimelineArr := ACADEMY_PLAN_ASSESSMENT_TIMELINE_MAP[planDuration]
			for _, dayNumber := range assessmentTimelineArr {
				startDay := dayNumber
				endDay := startDay + ACADEMY_DUE_ASSESSMENT_PERIOD_DAY_COUNT
				subscriptionDay := sub.SubscriptionDay
				if subscriptionDay > startDay && subscriptionDay <= endDay {
					includeSub = true
				}
			}
		} else {
			includeSub = true
		}

		if !includeSub {
			continue
		}
		timeLayout := "15:04"
		var rawSlotTime string
		var slotTimeString string

		if sub.SlotStartMin <= 9 {
			rawSlotTime = fmt.Sprintf("%d:0%d", sub.SlotStartHour, sub.SlotStartMin)
		} else {
			rawSlotTime = fmt.Sprintf("%d:%d", sub.SlotStartHour, sub.SlotStartMin)
		}

		slotTime, _ := time.Parse(timeLayout, rawSlotTime)

		if sub.Slot2TimeString == "" {
			slotTimeString = sub.SlotTimeString
		} else {
			slotTimeString = sub.SlotTimeString + " & " + sub.Slot2TimeString
		}

		subscriptionStartDateTime, _ := ptypes.TimestampProto(sub.SubscriptionStartDateTime)
		subscriptionEndDateTime, _ := ptypes.TimestampProto(sub.SubscriptionEndDateTime)

		subscriptionData := &pb.AcademySubscription{
			FsId:                      sub.FsId,
			UserId:                    sub.UserId,
			SubscriptionSlotMappingId: sub.SubscriptionSlotMappingId,
			SubscriptionId:            sub.SubscriptionId,
			SlotTime:                  slotTime.Format("3:04 PM"),
			SlotString:                slotTimeString,
			SubscriptionStartDateTime: subscriptionStartDateTime,
			SubscriptionEndDateTime:   subscriptionEndDateTime,
			SubscriptionDay:           sub.SubscriptionDay,
			PlanDuration:              fmt.Sprintf("%d %s", sub.Duration, sub.DurationUnit),
		}

		res.Subscriptions = append(res.Subscriptions, subscriptionData)
	}

	return nil
}

func (s *Service) GetAcademySubscriptionDataBySubscriptionSlotMappingId(ctx context.Context, req *pb.SubscriptionDataRequest, res *pb.AcademySubscription) error {
	academySubscriptionData, err := s.Data.GetAcademySubscriptionDataBySubscriptionSlotMappingId(ctx, req.SubscriptionSlotMappingId)
	if err != nil {
		log.Printf("error in GetAcademySubscriptionDataBySubscriptionSlotMappingId: unable to get subscription data for SubscriptionSlotMappingId: %d", req.SubscriptionSlotMappingId)
		return fmt.Errorf("error in GetAcademySubscriptionDataBySubscriptionSlotMappingId: unable to get subscription data for SubscriptionSlotMappingId: %d", req.SubscriptionSlotMappingId)
	}

	res.UserId = academySubscriptionData.UserId
	res.SubscriptionId = academySubscriptionData.SubscriptionId
	res.FsId = academySubscriptionData.FsId
	var slotTimeString string

	if academySubscriptionData.Slot2TimeString == "" {
		slotTimeString = academySubscriptionData.SlotTimeString
	} else {
		slotTimeString = academySubscriptionData.SlotTimeString + " & " + academySubscriptionData.Slot2TimeString
	}

	res.SlotString = slotTimeString
	res.SubscriptionStartDateTime, _ = ptypes.TimestampProto(academySubscriptionData.SubscriptionStartDateTime)
	res.SubscriptionEndDateTime, _ = ptypes.TimestampProto(academySubscriptionData.SubscriptionEndDateTime)
	res.SubscriptionDay = academySubscriptionData.SubscriptionDay
	res.PlanDuration = fmt.Sprintf("%d %s", academySubscriptionData.Duration, academySubscriptionData.DurationUnit)
	return nil
}

func (s *Service) GetCourseProductDetailsMap(ctx context.Context, productIds []int32) (map[int32][]models.ProductCourseMapping, error) {
	courseProductDetailsMap := make(map[int32][]models.ProductCourseMapping)
	if len(productIds) == 0 {
		return courseProductDetailsMap, nil
	}
	productIds = util.DeduplicateSlice(productIds)
	mappings, err := s.Data.BatchGetCourseProductDetailsByProductIds(ctx, productIds)
	if err != nil {
		log.Printf("Error in getting course product details in BatchGetCourseProductDetailsByProductIds: %v", err)
		return courseProductDetailsMap, err
	}

	for _, mapping := range mappings {
		if val, ok := courseProductDetailsMap[mapping.ProductId]; ok {
			courseProductDetailsMap[mapping.ProductId] = append(val, mapping)
		} else {
			courseProductDetailsMap[mapping.ProductId] = []models.ProductCourseMapping{mapping}
		}
	}

	return courseProductDetailsMap, nil
}

func (s *Service) GetRelatedProductCourseDetails(ctx context.Context, req *pb.GetRelatedProductCourseDetailsReq, res *pb.ProductCourseDetailsResponse) error {
	bookingClient := util.GetBookingServiceClient()
	if req.FsId == 0 || req.ProductCourseMappingId == 0 {
		errMsg := fmt.Sprintf("Invalid Request, No Fs id or no slot id passed")
		log.Println("GetRelatedProductCourseDetails, bad request: ", errMsg)
		return fmt.Errorf(errMsg)
	}
	reqData := &bookingPB.AcademySlot{
		FsId:    req.FsId,
		SlotId1: req.SlotId1,
		SlotId2: req.SlotId2,
	}
	academySlotsResp, err := bookingClient.GetAcademySlots(ctx, reqData)
	if err != nil {
		log.Printf("GetRelatedProductCourseDetails, error in getting academy slot data for reqData: %v, err: %v", reqData, err)
		return err
	}
	productCourseMappingIds := []int32{req.ProductCourseMappingId}
	for _, element := range academySlotsResp.AcademySlots {
		productCourseMappingIds = append(productCourseMappingIds, element.ProductCourseMappingId)
	}
	productCourseMappingIds = util.DeduplicateSlice(productCourseMappingIds)
	var productCourseDetails pb.ProductCourseDetailsResponse
	err = s.GetProductCourseMappingsDetails(ctx, &pb.ProductCourseDetailsRequest{
		ProductCourseMappingIds: productCourseMappingIds,
	}, &productCourseDetails)
	if _, ok := productCourseDetails.ProductCourseMappingsData[req.ProductCourseMappingId]; !ok || err != nil {
		log.Printf("GetRelatedProductCourseDetails, error in finding product course details for product course mapping ids: %v, err: %v", productCourseMappingIds, err)
		return fmt.Errorf("Unable to fetch product course details for product course mapping ids: %v, Error: %v", productCourseMappingIds, err)
	}
	dayOfWeeks := productCourseDetails.ProductCourseMappingsData[req.ProductCourseMappingId].Days
	res.ProductCourseMappingsData = make(map[int32]*pb.ProductCourseMapping)
	for productCourseMappingId, productCourse := range productCourseDetails.ProductCourseMappingsData {
		if util.AreDuplicateSlices(dayOfWeeks, productCourse.Days) {
			res.ProductCourseMappingsData[productCourseMappingId] = productCourse
		}
	}
	return nil
}

func (s *Service) GetAcademyActiveSubscriptionsBySlotIds(ctx context.Context, req *pb.ActiveAcademySubscriptionsRequest, res *pb.SubscriptionResponse) error {

	var subscriptions []models.Subscription
	if err := s.Data.GetActiveAcademySubscriptions(ctx, req.AcademySlotIds, &subscriptions); err != nil {
		log.Printf("GetAcademyActiveSubscriptionsBySlotIds Error: %v", err)
		return err
	}

	sort.SliceStable(subscriptions, func(i, j int) bool {
		return subscriptions[i].StartDate.Unix() < subscriptions[j].StartDate.Unix()
	})
	for _, subData := range subscriptions {
		res.Subscriptions = append(res.Subscriptions, subData.Proto())
	}
	return nil
}