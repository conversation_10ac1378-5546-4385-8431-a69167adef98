package productHandler

import (
	pb "bitbucket.org/jogocoin/go_api/product_service/proto/product"

	"github.com/golang/protobuf/ptypes"
	"golang.org/x/net/context"
	"log"
	"time"
)

func (s *Service) GenerateVouchers(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	voucherCodes := GetRandStringSlice(8, 1000, "A", "E")
	localTime := time.Unix(1667154599, 0)
	expiryTime, _ := ptypes.TimestampProto(localTime)
	for _, voucherCode := range voucherCodes {
		voucherProto := &pb.Vouchers{
			Code:            voucherCode,
			Description:     "Accenture B2B One Time Use Coupon",
			ApplicantType:   2, //Any User
			CouponType:      3, // mapping type - product
			RewardType:      0,
			RewardValue:     0,
			DiscountType:    2,
			DiscountValue:   5000,
			MaxDiscount:     5000,
			MinPlanPrice:    0, // no min plan price, applicable on all products
			MaxPlanPrice:    0,
			DurationInDays:  0,
			UsageCount:      1, // one time use only
			MinUsers:        1,
			CouponTypeValue: []int32{1221, 1222, 1223, 1248, 1250, 1256, 1257, 1284, 1295, 1364, 1365, 1366}, //list of maping ids, in this case these are product ids
			UserId:          687802,
			DeletedFlag:     0,
			IsPromoted:      0,
			ExpiryDate:      expiryTime,
		}
		var response pb.CreateUpdateVoucherRes
		if err := s.CreateOrUpdateVouchers(context.TODO(), voucherProto, &response); err != nil {
			log.Printf("Error in creating one time B2B coupon: %v", err)
			return err
		}
		log.Printf("GenerateVouchers response: %v", response)
	}
	return nil
}
