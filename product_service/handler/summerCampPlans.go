package productHandler

import (
	"fmt"
	"log"
	"time"
	"sort"
	// "strconv"
	// "strings"

	models "bitbucket.org/jogocoin/go_api/product_service/data/model"
	"bitbucket.org/jogocoin/go_api/product_service/internal/util"
	// facilitySportPB "bitbucket.org/jogocoin/go_api/product_service/proto/facility_sport"
	facilitySportPB "bitbucket.org/jogocoin/go_api/product_service/proto/facility_sport"
	pb "bitbucket.org/jogocoin/go_api/product_service/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/product_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/product_service/structs"
	"golang.org/x/net/context"
)

func (s *Service) GetSummerCampPlansForUser(ctx context.Context, req *pb.GetPreferredPlanRequest, res *pb.GetSummerCampPreferredPlanResponse) error {
	loggedInUserId := util.GetUserIDFromContext(ctx)
	cityId := util.GetCityIDFromContext(ctx)
	if loggedInUserId == 0 {
		res.Status = &pb.Status{
			Message: "User not logged in",
			Status:  notAuthorized,
		}
		return nil
	}

	//check child User
	ucl := util.GetUserServiceClient()
	if req.UserId != loggedInUserId {
		userRequest := &userPB.UserChildMappingRequest{
			UserId:      loggedInUserId,
			ChildUserId: req.UserId,
		}
		userChildResponse, err := ucl.CheckUserIsChildUser(ctx, userRequest)
		if err != nil {
			log.Printf("Error in validating parent: %d and child: %d mapping", loggedInUserId, req.UserId)
			return fmt.Errorf("Error in validating parent: %d and child: %d mapping", loggedInUserId, req.UserId)
		}

		if userChildResponse.UserId != loggedInUserId || userChildResponse.ChildUserId != req.UserId {
			res.Status = &pb.Status{
				Message: notAuthorized,
				Status:  notAuthorized,
			}
			return nil
		}
	}

	productReq := &pb.GetSummerCampPlanRequest{
		CityId: cityId,
	}
	if req.SummercampProductMappingId > 0 {
		productReq.SummercampProductMappingId = req.SummercampProductMappingId
	}
	var productDetails pb.GetSummerCampPlanResponse
	if err := s.GetSummerCampProducts(ctx, productReq, &productDetails); err != nil {
		log.Printf("GetPreferredSummerCampPlanForUser: error in getting summer camp products for cityId: %d, err: %v", cityId, err)
		return err
	}

	sportProductMap := make(map[int32]*pb.SummerCampSportProduct)
	fcl := util.GetFacilitySportClient()
	for _, product := range productDetails.Products {
		if val, ok := sportProductMap[product.SportId]; ok {
			val.Products = append(val.Products, product)
		} else {
			sportProductProto := &pb.SummerCampSportProduct{
				SportId:   product.SportId,
				SportName: product.SportName,
				Products:  []*pb.SummerCampProduct{product},
			}
			log.Println("image path: val.ImageId", product.ImageId)
			if product.ImageId > 0 {
				imagePathReq := &facilitySportPB.GetImagePathByImageIdRequest{ImageId: product.ImageId}
				imageRes, err := fcl.GetImagePathByImageId(ctx, imagePathReq)
				if err != nil {
					log.Printf("GetSummerCampProductsForCity: Could not find image for image id: %d, Error:%v", product.ImageId, err)
					return err
				}
				if len(imageRes.ImagePath) > 0 {
					sportProductProto.SportIcon = util.GetCDNLink(imageRes.ImagePath)
				}
			}
			sportProductMap[product.SportId] = sportProductProto
		}
	}

	for _, sportProductsList := range sportProductMap {
		res.SportProducts = append(res.SportProducts, sportProductsList)
	}
	sort.SliceStable(res.SportProducts, func(i, j int) bool {
		return res.SportProducts[i].SportId < res.SportProducts[j].SportId
	})
	log.Println("res: test product resp", res)
	return nil
}

func (s *Service) GetSummerCampProducts(ctx context.Context, req *pb.GetSummerCampPlanRequest, res *pb.GetSummerCampPlanResponse) error {
	badRequestStatus := &pb.Status{
		Message: badRequest,
		Status:  badRequest,
	}
	cityId := util.GetCityIDFromContext(ctx)

	if cityId == 0 {
		res.Status = badRequestStatus
		return nil
	}
	reqData := &structs.SummerCampProductRequest{
		CityId:      cityId,
		FeatureType: FEATURE_TYPE_FEATURED,
	}
	if req.SummercampProductMappingId > 0 {
		reqData.SummerCampProductMappingId = req.SummercampProductMappingId
	}
	summerCampProductMappings, err := s.Data.GetSummerCampProducts(ctx, reqData)
	if err != nil {
		log.Println("GetSummerCampProductsForCity: error in getting summer camp products for cityId: %d, err: %v", cityId, err)
		return err
	}
	if len(summerCampProductMappings) == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: "Unable to find mapping details for given city",
		}
		return nil
	}
	for _, val := range summerCampProductMappings {
		summerCampProduct := val.Proto()
		productOfferings, err := s.Data.GetProductOfferingsFromRedis(ctx, val.ProductId)
		if err != nil {
			log.Printf("GetSummerCampProductsForCity: Unable to get product offerings for product id %d, Error: %v", val.ProductId, err)
			return fmt.Errorf("GetSummerCampProductsForCity: unable to get academy products for city id %d, Error: %v", val.ProductId, err)
		}
		summerCampProduct.ProductOfferings = productOfferings
		res.Products = append(res.Products, summerCampProduct)
	}
	return nil
}

func (s *Service) GetSummerCampRecommendedCourseForUser(ctx context.Context, req *pb.GetPreferredPlanRequest, res *pb.GetSummerCampPreferredPlanResponse) error {
	badRequestStatus := &pb.Status{
		Message: badRequest,
		Status:  badRequest,
	}
	notAuthorizedStatus := &pb.Status{
		Message: notAuthorized,
		Status:  notAuthorized,
	}

	cityId := util.GetCityIDFromContext(ctx)

	if cityId == 0 {
		res.Status = badRequestStatus
		return nil
	}

	loggedInUserId := util.GetUserIDFromContext(ctx)
	if loggedInUserId == 0 {
		res.Status = notAuthorizedStatus
		return nil
	}
	if req.UserId == 0 {
		res.Status = badRequestStatus
		return nil
	}

	userClient := util.GetUserServiceClient()

	if req.UserId != loggedInUserId {
		userRequest := &userPB.UserChildMappingRequest{
			UserId:      loggedInUserId,
			ChildUserId: req.UserId,
		}
		userChildResponse, err := userClient.CheckUserIsChildUser(ctx, userRequest)
		if err != nil {
			log.Printf("Error in validating parent: %d and child: %d mapping", loggedInUserId, req.UserId)
			return fmt.Errorf("Error in validating parent: %d and child: %d mapping", loggedInUserId, req.UserId)
		}

		if userChildResponse.UserId != loggedInUserId || userChildResponse.ChildUserId != req.UserId {
			res.Status = notAuthorizedStatus
			return nil
		}
	}

	userRequest := &userPB.UserRequest{
		UserId: req.UserId,
	}
	userResponse, err := userClient.UserGet(ctx, userRequest)
	if err != nil {
		return fmt.Errorf("Unable to get user details for userId: %d, Err: %v", req.UserId, err)
	}
	if len(userResponse.Users) == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: "Unable to find user details",
		}
		return nil
	}
	res.User = &pb.User{
		UserId: userResponse.Users[0].UserId,
		Name:   userResponse.Users[0].Name,
		Age:    userResponse.Users[0].Age,
		Phone:  userResponse.Users[0].Phone,
	}

	reqData := &pb.GetPreferredPlanRequest{
		UserId:                     req.UserId,
		ProductCategoryId:          req.ProductCategoryId,
		SummercampProductMappingId: req.SummercampProductMappingId,
	}
	var planRes pb.GetSummerCampPreferredPlanResponse
	if err := s.GetSummerCampPlansForUser(ctx, reqData, &planRes); err != nil {
		log.Printf("Error in getting summer camp plans for req:%v, err: %v", reqData, err)
		return fmt.Errorf("Error in getting summer camp plans for req:%v, err: %v", reqData, err)
	}

	res.SportProducts = planRes.SportProducts

	log.Println("res: ", res)

	res.Status = &pb.Status{
		Status:  success,
		Message: "product fetched successfully",
	}
	return nil
}

func (s *Service) GetSummercampMembershipforUser(ctx context.Context, req *pb.User, res *pb.SubscriptionResponse) error {
	var userSubscriptions []models.Subscription
	err := s.Data.GetSubscriptionsForUserId(ctx, structs.SubscriptionForUserId{
		UserId:            req.UserId,
		OrderBy:           "DESC",
		ProductCategoryId: CATEGORY_ID_SUMMER_CAMP,
	}, &userSubscriptions) 
	if err != nil {
		log.Printf("Error in handler GetSummercampMembershipforUser for user Id: %d, Error: %v", req.UserId, err)
		return err
	}
	loc, _ := time.LoadLocation("Asia/Kolkata")
	date := time.Date(2023, time.Month(3), 1, 0, 0, 0, 0, loc)
	for _, subscription := range userSubscriptions {
		if subscription.SubscriptionEndDate.After(date) {
			res.Subscriptions = append(res.Subscriptions, subscription.Proto())
		}
	}

	return nil
}