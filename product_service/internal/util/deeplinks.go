package util

import (
	"fmt"
)

const DEEPLINK_HOST string = "fitso://"
const CATEGORY_ID_ACADEMY int32 = 12
const CATEGORY_ID_SUMMER_CAMP int32 = 13

func GetPurchaseMembershipDeeplink(productId int32, isRenewal bool) string {
	if productId > 0 {
		if isRenewal {
			return fmt.Sprintf("%sbuy_membership?product_id=%d&is_renewal=%d", DEEPLINK_HOST, productId, 1)
		} else {
			return fmt.Sprintf("%sbuy_membership?product_id=%d", DEEPLINK_HOST, productId)
		}
	}
	if isRenewal {
		return fmt.Sprintf("%sbuy_membership?is_renewal=%d", DEEPLINK_HOST, 1)
	} else {
		return fmt.Sprintf("%sbuy_membership", DEEPLINK_HOST)
	}
}

func GetBuyingForMeDeeplink(productId int32, isRenewal bool) string {
	if isRenewal {
		return fmt.Sprintf("%sbuy_for_me?product_id=%d&is_renewal=%d", DEEPLINK_HOST, productId, 1)
	} else {
		return fmt.Sprintf("%sbuy_for_me?product_id=%d", DEEPLINK_HOST, productId)
	}
}

func GetBuyingForOthersDeeplink(productId int32, isRenewal bool) string {
	if isRenewal {
		return fmt.Sprintf("%sbuy_for_others?product_id=%d&is_renewal=%d", DEEPLINK_HOST, productId, 1)
	} else {
		return fmt.Sprintf("%sbuy_for_others?product_id=%d", DEEPLINK_HOST, productId)
	}
}

func GetFacilityPageDeeplink(facilityId int32, sportId int32) string {
	if sportId == 0 {
		return fmt.Sprintf("%sfacility/%d", DEEPLINK_HOST, facilityId)
	} else {
		return fmt.Sprintf("%sfacility/%d?sport_id=%d", DEEPLINK_HOST, facilityId, sportId)
	}
}

func GetAcademyFacilityPageDeeplink(facilityId int32, sportId int32) string {
	if sportId == 0 {
		return fmt.Sprintf("%sfacility/%d?product_category_id=%d", DEEPLINK_HOST, facilityId, CATEGORY_ID_ACADEMY)
	} else {
		return fmt.Sprintf("%sfacility/%d?sport_id=%d&product_category_id=%d", DEEPLINK_HOST, facilityId, sportId, CATEGORY_ID_ACADEMY)
	}
}

func GetMembershipDetailsDeeplink(productId int32, userId int32) string {
	return fmt.Sprintf("%smembership_details?product_id=%d&user_id=%d", DEEPLINK_HOST, productId, userId)
}

func GetSummerCampFacilityPageDeeplink(facilityId int32, sportId int32) string {
	if sportId == 0 {
		return fmt.Sprintf("%sfacility/%d?product_category_id=%d", DEEPLINK_HOST, facilityId, CATEGORY_ID_SUMMER_CAMP)
	} else {
		return fmt.Sprintf("%sfacility/%d?sport_id=%d&product_category_id=%d", DEEPLINK_HOST, facilityId, sportId, CATEGORY_ID_SUMMER_CAMP)
	}
}
