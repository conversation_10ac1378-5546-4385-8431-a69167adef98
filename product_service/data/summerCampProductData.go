package data

import (
	"context"
	"fmt"
	"log"

	gormNewrelicConfig "bitbucket.org/jogocoin/go_api/pkg/newrelic"
	"bitbucket.org/jogocoin/go_api/product_service/internal/util"
	"github.com/jinzhu/gorm"

	models "bitbucket.org/jogocoin/go_api/product_service/data/model"
	structs "bitbucket.org/jogocoin/go_api/product_service/structs"
)

type SummerCampDataInterface interface {
	GetSummerCampProducts(ctx context.Context, condition *structs.SummerCampProductRequest) ([]*models.SummercampProductMapping, error)
	GetSummerCampProductById(ctx context.Context, productId int32) ([]*models.SummercampProductMapping, error)
	GetSubscriptionSummercampSlotMappings(ctx context.Context, subscriptionId int32) ([]*models.SubscriptionSummercampSlotMapping, error)
	GetSummerCampProductMappings(ctx context.Context, productId int32, sportId int32) ([]*models.SummercampProductMapping, error)
	GetSummerCampProductMappingsForIds(ctx context.Context, summercampProductMappingIds []int32) ([]*models.SummercampProductMapping, error)
	GetSummercampProductsByCity(ctx context.Context) ([]*models.SummercampProductMapping, error)
	GetDataToCreateSummerCampBookings(ctx context.Context, condition *structs.AcademySessionBookingRequest) ([]*structs.AcademySessionBookingPayload, error)
	GetSummercampExpiredSubscriptionsInLastDay(ctx context.Context) ([]*models.SubscriptionSummercampSlotMapping, error)
	GetSummercampExpiredSubscriptionsOnNthDay(ctx context.Context, summercampSlotIds []int32, dayCount int32) ([]*models.SubscriptionSummercampSlotMapping, error)
}

func (a *ProductData) GetSummerCampProducts(ctx context.Context, condition *structs.SummerCampProductRequest) ([]*models.SummercampProductMapping, error) {
	var summerCampProducts []*models.SummercampProductMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("`summercamp_product_mappings` scpm").
		Select("scpm.*, p.product_id, p.price, p.retail_price, p.duration, p.duration_unit, p.duration_in_days, s.sport_name, s.icon_image_id").
		Joins("INNER JOIN `products` p ON p.`product_id` = scpm.`product_id` AND p.`is_active` = 1 AND scpm.`is_active` = 1 AND p.feature_type = ?", condition.FeatureType).
		Joins("INNER JOIN `sports` s ON s.`sport_id` = scpm.`sport_id`").
		Where("p.`location_id_v2` = ? OR p.`all_locations` = 1", condition.CityId)

	if condition.SummerCampProductMappingId > 0 {
		response = response.Where("scpm.summercamp_product_mapping_id = ?", condition.SummerCampProductMappingId)
	}
	response = response.Order("s.popularity desc").Find(&summerCampProducts)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Printf("GetSummerCampProductsForGivenCity: error in getting summer-camp product details for cityId: %d, err: %v", condition.CityId, err)
		return summerCampProducts, fmt.Errorf("error in getting summer-camp product details for cityId: %d, err: %v", condition.CityId, err)
	}
	return summerCampProducts, nil
}

func (a *ProductData) GetSummerCampProductById(ctx context.Context, productId int32) ([]*models.SummercampProductMapping, error) {
	var summerCampProducts []*models.SummercampProductMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("`summercamp_product_mappings` scpm").
		Select("scpm.*, p.product_id, p.price, p.retail_price, p.duration, p.duration_unit, s.sport_name, p.product_description").
		Joins("INNER JOIN `products` p ON p.`product_id` = scpm.`product_id` AND p.`is_active` = 1").
		Joins("INNER JOIN `sports` s ON s.`sport_id` = scpm.`sport_id`").
		Where("scpm.`product_id` = ? ", productId).
		Find(&summerCampProducts)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Printf("GetSummerCampProductById: error in getting summer-camp product details for Id: %d, err: %v", productId, err)
		return summerCampProducts, fmt.Errorf("error in getting summer-camp product details for Id: %d, err: %v", productId, err)
	}
	return summerCampProducts, nil
}

func (a *ProductData) GetSubscriptionSummercampSlotMappings(ctx context.Context, subscriptionId int32) ([]*models.SubscriptionSummercampSlotMapping, error) {
	var slotMappings []*models.SubscriptionSummercampSlotMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("subscription_summercamp_slot_mappings ssm").
		Select("ssm.summercamp_slot_id, sl.fs_id, sl.slot_id, sl.summercamp_product_mapping_id, spm.sport_id, s.name as sport_name, sl.active_slot_id, sl.active_slots_applicable").
		Joins("INNER JOIN summercamp_slots sl ON ssm.summercamp_slot_id = sl.id").
		Joins("INNER JOIN summercamp_product_mappings spm on spm.summercamp_product_mapping_id = sl.summercamp_product_mapping_id").
		Joins("INNER JOIN sports s on s.sport_id = spm.sport_id").
		Where("ssm.subscription_id = ?", subscriptionId).
		Find(&slotMappings)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return slotMappings, fmt.Errorf("error in getting subscription slot mappings for subId: %d, err: %v", subscriptionId, err)
	}
	return slotMappings, nil
}

func (a *ProductData) GetSummerCampProductMappings(ctx context.Context, productId int32, sportId int32) ([]*models.SummercampProductMapping, error) {
	var summerCampProductMappings []*models.SummercampProductMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Where("`product_id` = ?", productId)
	if sportId > 0 {
		response = response.Where("`sport_id` = ?", sportId)
	}
	response = response.Find(&summerCampProductMappings)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Printf("GetSummerCampProductById: error in getting summer-camp product details for product id: %d and sport id:%d, err: %v", productId, sportId, err)
		return summerCampProductMappings, fmt.Errorf("error in getting summer-camp product details for product id: %d and sport id:%d, err: %v", productId, sportId, err)
	}
	return summerCampProductMappings, nil
}

func (a *ProductData) GetSummerCampProductMappingsForIds(ctx context.Context, summercampProductMappingIds []int32) ([]*models.SummercampProductMapping, error) {
	var summerCampProductMappings []*models.SummercampProductMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("`summercamp_product_mappings` scpm").Select("scpm.*, p.product_description").Joins("INNER JOIN products p on scpm.product_id = p.product_id").Where("`summercamp_product_mapping_id` IN (?)", summercampProductMappingIds).
		Find(&summerCampProductMappings)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Printf("GetSummerCampProductById: error in getting summer-camp product details for summer-camp product mapping ids: %v, err: %v", summercampProductMappingIds, err)
		return summerCampProductMappings, fmt.Errorf("error in getting summer-camp product details for summer-camp product mapping ids: vd, err: %v", summercampProductMappingIds, err)
	}
	return summerCampProductMappings, nil
}

func (a *ProductData) GetSummercampProductsByCity(ctx context.Context) ([]*models.SummercampProductMapping, error) {
	var summerCampProducts []*models.SummercampProductMapping
	cityId := util.GetCityIDFromContext(ctx)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("`summercamp_product_mappings` scpm").
		Select("scpm.*, p.product_id, p.price, p.retail_price, p.duration, p.duration_unit, p.duration_in_days, s.sport_name, p.product_description, p.feature_type").
		Joins("INNER JOIN `products` p ON p.`product_id` = scpm.`product_id` AND p.`is_active` = 1 AND p.location_id_v2 = ? AND p.feature_type = 1 AND p.product_category_id = 13", cityId).
		Joins("INNER JOIN `sports` s ON s.`sport_id` = scpm.`sport_id`").
		Order("p.duration ASC").
		Find(&summerCampProducts)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Printf("GetSummercampProductsByCity: error in getting summer-camp product details for cityId: %d, err: %v", cityId, err)
		return summerCampProducts, fmt.Errorf("error in getting summer-camp product details for cityId: %d, err: %v", cityId, err)
	}
	return summerCampProducts, nil
}

func (a *ProductData) GetDataToCreateSummerCampBookings(ctx context.Context, condition *structs.AcademySessionBookingRequest) ([]*structs.AcademySessionBookingPayload, error) {
	var bookingsPayload []*structs.AcademySessionBookingPayload

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db) 
	response := DB.Table("subscription_summercamp_slot_mappings sssm").
		Select("s.subscription_id, s.product_id, s.user_id, s.purchase_id, pacs.pacs_id, pacs.fs_id, pacs.slot_id, spm.days_of_week").
		Joins("INNER JOIN subscriptions s ON s.subscription_id = sssm.subscription_id AND s.is_active = 1 AND s.subscription_end_date > ? AND s.start_date < ?", condition.TimeToProcess, condition.TimeToProcess).
		Joins("INNER JOIN summercamp_product_mappings spm ON s.product_id = spm.product_id").
		Joins("INNER JOIN summercamp_slots scs ON scs.id = sssm.summercamp_slot_id").
		Joins("INNER JOIN play_arena_capacity_slots pacs ON scs.fs_id = pacs.fs_id AND pacs.active_status = 1 AND pacs.day_of_week = ? AND pacs.product_arena_category_id = ? AND (pacs.slot_id = scs.slot_id)", condition.Weekday, PRODUCT_ARENA_TYPE_SUMMER_CAMP_SESSION).
		Joins("INNER JOIN facility_sport_mappings fsm ON scs.fs_id = fsm.fs_id and fsm.sport_id != 3")

	if condition.UserId > 0 {
		response = response.Where("s.user_id = ?", condition.UserId)
	}
	response = response.Find(&bookingsPayload)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Println("func:GetDataToCreateSummerCampBookings, error in getting summercamp purchase booking data for dow:%d, time: %v, error: %v", condition.Weekday, condition.TimeToProcess, err)
		return bookingsPayload, fmt.Errorf("Unable to get data to create summercamp booking, err: %v", err)
	}

	return bookingsPayload, nil
}

func (a *ProductData) GetSummercampExpiredSubscriptionsInLastDay(ctx context.Context) ([]*models.SubscriptionSummercampSlotMapping, error) {
	var slotMappings []*models.SubscriptionSummercampSlotMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("subscriptions s").
		Select("sssm.subscription_id, sssm.summercamp_slot_id, s.user_id, p.location_id_v2 as city_id").
		Joins("INNER JOIN subscription_summercamp_slot_mappings sssm ON s.subscription_id = sssm.subscription_id").
		Joins("INNER JOIN products p ON s.product_id = p.product_id").
		Where("s.is_active = 1 AND s.subscription_end_date < CURRENT_TIMESTAMP").
		Where("s.subscription_end_date > DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 1 DAY)").
		Find(&slotMappings)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return slotMappings, fmt.Errorf("error in getting expired subscriptions summercamp slot mappings for last day, err: %v", err)
	}
	return slotMappings, nil
}

func (a *ProductData) GetSummercampExpiredSubscriptionsOnNthDay(ctx context.Context, summercampSlotIds []int32, dayCount int32) ([]*models.SubscriptionSummercampSlotMapping, error) {
	var slotMappings []*models.SubscriptionSummercampSlotMapping

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("subscriptions s").
		Select("sssm.subscription_id, sssm.summercamp_slot_id, s.user_id, p.location_id_v2 as city_id").
		Joins("INNER JOIN subscription_summercamp_slot_mappings sssm ON s.subscription_id = sssm.subscription_id and sssm.summercamp_slot_id in (?)", summercampSlotIds).
		Joins("INNER JOIN products p ON s.product_id = p.product_id").
		Where("s.is_active = 1 AND s.subscription_end_date < DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY)", dayCount).
		Where("s.subscription_end_date > DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY)", dayCount - 1).
		Find(&slotMappings)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return slotMappings, fmt.Errorf("GetSummercampExpiredSubscriptionsOnNthDay: error in getting expired subscriptions summercamp slot mappings for nth day %d, err: %v",dayCount, err)
	}
	return slotMappings, nil
}