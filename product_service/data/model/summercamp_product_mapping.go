package models

import (
	pb "bitbucket.org/jogocoin/go_api/product_service/proto/product"
	"time"
)

type SummercampProductMapping struct {
	SummercampProductMappingId int32     `gorm:"not null;type:int(11) unsigned auto_increment;PRIMARY_KEY"`
	ProductId                  int32     `gorm:"not null;type:int(11) unsigned"`
	SportId                    int32     `gorm:"not null;type:int(11) unsigned"`
	SessionDuration            int32     `gorm:"not null;type:int(11) unsigned;comment:'in minutes'"`
	IsActive                   int32     `gorm:"not null;type:tinyint(1) unsigned;"`
	CreatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP"`
	UpdatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	DaysOfWeek                 string    `gorm:"not null;type:varchar(10);default:''"`
	SportName                  string    `gorm:"-"`
	IconImageId                int32     `gorm:"-"`
	Price                      float32   `gorm:"-"`
	Duration                   int32     `gorm:"-"`
	DurationUnit               string    `gorm:"-"`
	DurationInDays             int32     `gorm:"-"`
	RetailPrice                float32   `gorm:"-"`
	CityId                     int32     `gorm:"-"`
	ProductDescription         string    `gorm:"-"`
}

func (p *SummercampProductMapping) Proto() *pb.SummerCampProduct {
	return &pb.SummerCampProduct{
		SummercampProductMappingId: p.SummercampProductMappingId,
		ProductId:                  p.ProductId,
		SportId:                    p.SportId,
		SessionDuration:            p.SessionDuration,
		SportName:                  p.SportName,
		Price:                      p.Price,
		Duration:                   p.Duration,
		DurationUnit:               p.DurationUnit,
		DurationInDays:             p.DurationInDays,
		RetailPrice:                p.RetailPrice,
		CityId:                     p.CityId,
		ProductDescription:         p.ProductDescription,
		ImageId:                    p.IconImageId,
		DaysOfWeek:					p.DaysOfWeek,
	}
}
