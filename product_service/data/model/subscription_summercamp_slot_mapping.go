package models

type SubscriptionSummercampSlotMapping struct {
	Id               int32  `gorm:"not null;type:int(11) unsigned auto_increment;PRIMARY_KEY"`
	SubscriptionId   int32  `gorm:"not null;type:int(11) unsigned"`
	SummercampSlotId int32  `gorm:"not null;type:int(11) unsigned"`
	SlotId           int32  `gorm:"-"`
	FsId             int32  `gorm:"-"`
	SportName        string `gorm:"-"`
	UserId                 int32 `gorm:"-"`
	CityId                 int32 `gorm:"-"`
	ActiveSlotsApplicable  bool `gorm:"-"`
	ActiveSlotId           int32 `gorm:"-"`

}
