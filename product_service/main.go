package main

import (
	"context"
	"log"
	"time"

	interceptor "bitbucket.org/jogocoin/go_api/pkg/interceptor"
	"bitbucket.org/jogocoin/go_api/pkg/newrelic"
	data "bitbucket.org/jogocoin/go_api/product_service/data"
	productHandler "bitbucket.org/jogocoin/go_api/product_service/handler"
	pb "bitbucket.org/jogocoin/go_api/product_service/proto/product"
	"bitbucket.org/jogocoin/go_api/product_service/setupFunc"
	productSubscriber "bitbucket.org/jogocoin/go_api/product_service/subscriber"
	workerExecutor "bitbucket.org/jogocoin/go_api/product_service/worker"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"github.com/micro/go-micro/server"
	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	// initial setup
	setupFunc.SetUp()

	db, err := data.MysqlConnect()
	defer db.Close()

	kBroker := setupFunc.KafkaConnect()

	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	ROdb, ROerr := data.MysqlConnectRO()
	defer ROdb.Close()

	if ROerr != nil {
		log.Printf("Could not connect to Read only DB: %v", ROerr)
	}

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	awsSession := setupFunc.AWSConnect()

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	productData := &data.ProductData{Db: db, Client: redisClient, Broker: kBroker, ROdb: ROdb, AWSSession: awsSession}

	helperData := &productHandler.Helper{Client: redisClient}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.product"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(productHandler.LogWrapper,
			nrmicro.HandlerWrapper(app),
			productHandler.SetContextWrapper,
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	// register a new subscriber function to the service
	micro.RegisterSubscriber("product.subscriber.product", newService.Server(), productSubscriber.SubTestEvent, server.SubscriberQueue("queue.pubsub"))

	serviceHandler := productHandler.Service{Data: productData, Helper: helperData}
	pb.RegisterProductServiceHandler(newService.Server(), &serviceHandler)
	runWorkers(context.Background(), &serviceHandler)

	log.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}

}

func runWorkers(ctx context.Context, serviceHandler *productHandler.Service) {
	log.Println("Starting product_service workers...")
	workerExecutor.StartJobs(ctx, serviceHandler)
}
