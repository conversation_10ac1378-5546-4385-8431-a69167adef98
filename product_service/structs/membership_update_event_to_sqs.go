package structs

type MembershipUpdateMessageToSQS struct {
	Name                   string  `json:"name,omitempty"`
	Phone                  string  `json:"phone,omitempty"`
	CultPackId             int32   `json:"cultPackId,omitempty"`
	SubscriptionId         int32   `json:"subscriptionId,omitempty"`
	StartDate              string  `json:"startDate,omitempty"`
	EndDate                string  `json:"endDate,omitempty"`
	CenterId               int32   `json:"centerId,omitempty"`
	WorkoutId              int32   `json:"workoutId,omitempty"`
	Amount                 float32 `json:"amount,omitempty"`
	CourseCategory         string  `json:"course_category,omitempty"`
	CultUserId             int32   `json:"cult_user_id,omitempty"`
	ProductCourseMappingId int32   `json:"product_course_mapping_id,omitempty"`
	AcademySlotId          int32   `json:"academy_slot_id,omitempty"`
	ProductId              int32   `json:"product_id,omitempty"`
	ParentUserId           int32   `json:"parent_user_id,omitempty"`
	UserId                 int32   `json:"user_id,omitempty"`
	NewUser                bool    `json:"new_user,omitempty"`
	CultProductId          string  `json:"cultProductId,omitempty"`
}
