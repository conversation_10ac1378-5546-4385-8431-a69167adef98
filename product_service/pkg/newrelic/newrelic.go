package newrelic

import (
	"log"

	newrelic "github.com/newrelic/go-agent"
)

type newrelicConfig struct {
	Name            string
	License         string
	SkipStatusCodes bool
}

type option func(*newrelicConfig)

// Initialize newrelic
func Initialize(opts ...option) newrelic.Application {
	newrelicConfig := &newrelicConfig{}

	for _, opt := range opts {
		opt(newrelicConfig)
	}

	config := newrelic.NewConfig(newrelicConfig.Name, newrelicConfig.License)

	/**
	 * newrelic middleware logs 500 error for a transaction
	 * Since we are adding custom logs for errors, we get 2 sets of logs for same error
	 * skipping 500 will prevent nrmicro to skip 500 error log
	**/
	if newrelicConfig.SkipStatusCodes {
		config.ErrorCollector.IgnoreStatusCodes = []int{0, 5, 404, 500}
	}
	app, err := newrelic.NewApplication(config)

	if err != nil {
		log.Printf("Could not initialize newrelic: %s", err.<PERSON><PERSON><PERSON>())
	}

	return app
}

// WithAppName sets the newrelic app name
func WithAppName(name string) option {
	return func(config *newrelicConfig) {
		config.Name = name
	}
}

// WithLicense sets the newrelic license
func WithLicense(license string) option {
	return func(config *newrelicConfig) {
		config.License = license
	}
}

func EnableSkipStatusCodes() option {
	return func(config *newrelicConfig) {
		config.SkipStatusCodes = true
	}
}
