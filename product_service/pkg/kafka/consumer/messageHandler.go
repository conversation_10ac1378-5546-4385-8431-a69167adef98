package consumer

import (
	"context"
	"github.com/Shopify/sarama"
	"log"
)

type MessageHandler interface {
	Process(ctx context.Context, message *sarama.ConsumerMessage) error
}

type ConsumerMessageHandler struct {
	handler MessageHandler
}

func NewConsumerMessageHandler(handler MessageHandler) *ConsumerMessageHandler {
	return &ConsumerMessageHandler{handler: handler}
}

func (e *ConsumerMessageHandler) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

func (e *ConsumerMessageHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

func (e *ConsumerMessageHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	log.Printf("Consuming messages from Topic: %s, Partition: %d", claim.Topic(), claim.Partition())
	for msg := range claim.Messages() {
		log.Printf("New kafka request on topic - %s, partition- %d | Offset - %+d, member- %s", claim.Topic(), claim.Partition(), msg.Offset, session.MemberID())
		err := e.handler.Process(context.Background(), msg)
		if err != nil {
			log.Println("Error in handler processing, ", err)
			return err
		}
		log.Printf("Message topic:%s partition:%d offset:%d", msg.Topic, msg.Partition, msg.Offset)
		session.MarkMessage(msg, "")
	}
	log.Println("End of ConsumeClaim")
	return nil
}
