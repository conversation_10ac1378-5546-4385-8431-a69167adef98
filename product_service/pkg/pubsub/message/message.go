package message

import (
	"encoding/json"
	"log"
	"time"
)

type PubSubMessage struct {
	headers     map[string]string
	messageType MessageType
	rawPayload  []byte
	isReceived  bool
	timestamp   time.Time
}

func NewEmptyMessage() *PubSubMessage {
	return &PubSubMessage{}
}

func NewMessage(isReceived bool, headers map[string]string, rawBytes []byte, timestamp time.Time, messageType MessageType) *PubSubMessage {
	return &PubSubMessage{
		headers:     headers,
		messageType: messageType,
		rawPayload:  rawBytes,
		isReceived:  isReceived,
		timestamp:   timestamp,
	}
}

func NewMessageForJson(payload interface{}) (p *PubSubMessage, error error) {
	log.Println("Testloggs ", payload)
	bytes, err := json.Marshal(payload)
	if err != nil {
		error = err
		return
	}
	p = &PubSubMessage{rawPayload: bytes}
	log.Println("Testloggs ", p)
	return
}

func (m *PubSubMessage) Headers() map[string]string {
	return m.headers
}

func (m *PubSubMessage) Raw() []byte {
	return m.rawPayload
}

func (m *PubSubMessage) IsReceived() bool {
	return m.isReceived
}

func (m *PubSubMessage) Timestamp() time.Time {
	return m.timestamp
}

func (m *PubSubMessage) MarshalBinary() (data []byte, err error) {
	return m.rawPayload, nil
}

func (m *PubSubMessage) Log() {
	log.Println(string(m.rawPayload))
}

func (m *PubSubMessage) Payload(dest interface{}) error {
	log.Println("testLog ", m.messageType, "-Test" )
	switch m.messageType {
	case JSON:
		err := json.Unmarshal(m.rawPayload, dest)
		if err != nil {
			return err
		}
		break
	case Z_EVENT:
		/*e := pb.Event{}
		err := proto.Unmarshal(m.rawPayload, &e)
		if err != nil {
			return err
		}
		dest := dest.(proto.Message)
		if err := ptypes.UnmarshalAny(e.Payload, dest); err != nil {
			return err
		}*/
		log.Println("error in removing zomato dependancy", m.messageType, m.rawPayload)
		break
	}
	return nil
}
