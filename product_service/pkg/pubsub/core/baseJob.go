package core

import (
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/messageHandler"
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/queue"
	"context"
	"errors"
	"log"
)

type BaseJob struct {
	queue      queue.IQueue
	msgHandler messageHandler.IPubSubMessageHandler
}

func NewBaseJob(queue queue.IQueue, msgHandler messageHandler.IPubSubMessageHandler) *BaseJob {
	return &BaseJob{queue: queue, msgHandler: msgHandler}
}

func (b *BaseJob) SetQueue(q queue.IQueue) {
	b.queue = q
}

func (b *BaseJob) RegisterHandler(ctx context.Context, handler messageHandler.IPubSubMessageHandler) error {
	b.msgHandler = handler
	return nil
}

func (b *BaseJob) Start(ctx context.Context) (err error) {
	if b.queue == nil {
		log.Println("Queue is undefined")
		return errors.New("Queue is undefined")
	}
	err = b.queue.Subscribe(ctx, b.msgHandler)
	return nil
}

func (b *BaseJob) MsgHandler() messageHandler.IPubSubMessageHandler {
	return b.msgHandler
}

func (b *BaseJob) Queue() queue.IQueue {
	return b.queue
}
