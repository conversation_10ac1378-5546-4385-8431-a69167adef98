package messageHandler

import (
	message2 "bitbucket.org/jogocoin/go_api/pkg/pubsub/message"
	"context"
	"github.com/Shopify/sarama"
)

type KafkaMessageHandler struct {
	handler     *PubSubMessageCommonHandler
	messageType message2.MessageType
}

func NewKafkaMessageHandler(queueName, groupId string, handler IPubSubMessageHandler, messageType message2.MessageType) *KafkaMessageHandler {
	return &KafkaMessageHandler{handler: NewPubSubMessageCommonHandler(queueName, groupId, handler), messageType: messageType}
}

func (k *KafkaMessageHandler) Process(ctx context.Context, message *sarama.ConsumerMessage) (err error) {
	msg, e := NewMessageFromSaramaMessage(message, k.messageType)
	if e != nil {
		// todo handle me
		err = e
		return
	}
	err = k.handler.Handle(ctx, msg)
	return
}

func NewMessageFromSaramaMessage(message *sarama.ConsumerMessage, messageType message2.MessageType) (*message2.PubSubMessage, error) {
	headers := make(map[string]string, 0)
	for _, header := range message.Headers {
		headers[string(header.Key)] = string(header.Value)
	}
	msgObject := message2.NewMessage(true, headers, message.Value, message.Timestamp, messageType)
	return msgObject, nil
}
