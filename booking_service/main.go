package main

import (
	"fmt"
	"log"
	"time"

	"context"
	//_ "github.com/micro/go-plugins/broker/kafka"
	bookingSubscriber "bitbucket.org/jogocoin/go_api/booking_service/subscriber"

	data "bitbucket.org/jogocoin/go_api/booking_service/data"
	bookingHandler "bitbucket.org/jogocoin/go_api/booking_service/handler"
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	"bitbucket.org/jogocoin/go_api/booking_service/setupFunc"
	interceptor "bitbucket.org/jogocoin/go_api/pkg/interceptor"
	newrelic "bitbucket.org/jogocoin/go_api/pkg/newrelic"
	"github.com/micro/go-micro"
	"github.com/micro/go-micro/config"

	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	// initial setup
	setupFunc.SetUp()

	db, err := data.MysqlConnect()
	defer db.Close()

	kBroker := setupFunc.KafkaConnect()

	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	//db.AutoMigrate(&pb.GetUser{}) //Usertest

	ROdb, ROerr := data.MysqlConnectRO()
	defer ROdb.Close()

	if ROerr != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	awsSession := setupFunc.AWSConnect()

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	bookingData := &data.BookingData{
		Db:         db,
		Client:     redisClient,
		Broker:     kBroker,
		ROdb:       ROdb,
		AWSSession: awsSession,
	}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.booking"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(
			bookingHandler.LogWrapper,
			bookingHandler.SetContextWrapper,
			nrmicro.HandlerWrapper(app),
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()
	serviceHandler := bookingHandler.Service{bookingData}
	pb.RegisterBookingServiceHandler(newService.Server(), &serviceHandler)
	bookingSubscriber.InitializeTopics(context.Background(), kBroker, &serviceHandler, redisClient)
	//serviceHandler.ProcessSlotPopulationForFsId(context.Background(), int32(25))
	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}

}
