package data

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"

	"github.com/go-redis/redis"
	//"github.com/jinzhu/now"

	// "github.com/golang/protobuf/ptypes"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	// "github.com/micro/go-micro/config"
	"database/sql"

	models "bitbucket.org/jogocoin/go_api/booking_service/data/model"
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	structs "bitbucket.org/jogocoin/go_api/booking_service/structs"
	gormNewrelicConfig "bitbucket.org/jogocoin/go_api/pkg/newrelic"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	ptypes "github.com/golang/protobuf/ptypes"
	"github.com/micro/go-micro/broker"
	"github.com/micro/go-micro/config"
)

var (
	notificationTopic                = "notificationTopic"
	emailTopic                       = "emailTopic"
	smsTopic                         = "smsTopic"
	sheetPushTopic                   = "sheetPushTopic"
	noShowTopic                      = "noShowTopic"
	slotPopulationTopic              = "slotPopulationTopic"
	academyBookingTopic              = "academyBookingTopic"
	summerCampBookingTopic           = "summerCampBookingTopic"
	SwimmingCoachingType             = 9
	SwimmingCoachingText             = "Pro"
	is_swimming_considered_as_sports = true
	FacilityRatingComputeDaysRange   = 30
)

const (
	facilityRatingCountUpperLimit = 1500
	facilityRatingCountLowerLimit = 50
)

type DataInterface interface {
	PublishBookingDataInterface
	BookingSlotDataInterface
	MasterkeyBookingDataInterface
	AcademyBookingDataInterface
	BookingDetailsGet(ctx context.Context, bookingCondition *models.AllBooking, allBooking *[]models.AllBooking, timeline int32, start int32, count int32) error
	BookingDetailsGetV2(ctx context.Context, bookingCondition *models.AllBooking, allBooking *[]models.AllBooking, timeline int32, start int32, count int32) error
	PublishBooking(publishBookingRequest *pb.PublishBookingRequest, publishBookingResponse *pb.PublishBookingResponse) error
	PublishLiveSessionBooking(publishBookingRequest *pb.PublishBookingRequest, publishBookingResponse *pb.PublishBookingResponse) error
	GetBookingWindowForFacilitySport(fsid int32, prebookingWindow *int32) error
	FreezeLogsForSubscriptionGet(freezeModel *[]models.SportsUserFreezeLog, freezeCond *models.SportsUserFreezeLog) error
	GetIndividualCapacity(CapacityCheck structs.CheckCapacity, IndividualCapacity *int32) error
	GetRemainingCapacity(CapacityCheck structs.CheckCapacity, CapacityFlag *bool) error
	GetRemainingCapacityWithoutPAID(CapacityCheck structs.CheckCapacity, CapacityFlag *bool) error
	ActiveSlotsGet(slotCondition *models.FSActiveSlot, activeSlots *[]models.FSActiveSlot) error
	ActiveSlotsCreate(activeSlot models.FSActiveSlot) error
	ActiveSlotsGetLastDate(fsid int, dateMax *time.Time) error
	GetSlotCapacityByDate(conditions models.FacilitySportCapacitySlot, slotCapacity *[]models.FacilitySportCapacitySlot) error
	ArenaActiveSlotsGet(slotCondition *models.PAActiveSlot, activeSlots *[]models.PAActiveSlot) error
	ArenaActiveSlotsCreate(activeSlot models.PAActiveSlot) error
	ArenaActiveSlotsGetLastDate(fsid int, dateMax *time.Time) error
	GetArenaSlotCapacityByDate(conditions models.PlayArenaCapacitySlot, slotCapacity *[]models.PlayArenaCapacitySlot) error
	DeleteAllActiveSlots(date_del time.Time) error
	DeleteAllActiveArenaSlots(date_del time.Time) error
	GetFSIDForByPAId(paid int) int
	BookingSlotsGetFS(activeSlotFsCondition *models.FSActiveSlot, activeSlotFs *[]models.FSActiveSlot) error
	BookingSlotsGetPA(activeSlotPaCondition *models.PAActiveSlot, activeSlotPa *[]models.PAActiveSlot) error
	SendNotificationOnPublish(notiData *structs.FcmNotification) error
	RemainingSessionsForSubscriptionGet(subscriptionId int32, remainingSessions *float32) error
	GetBookingStartAndEndDate(req *pb.BookingDatesGet, bookingStartDate *time.Time, bookingEndDate *time.Time) error
	CancelBooking(req *pb.CancelBookingRequest, cancelBookingData *pb.CancelBooking) error
	CancelBookingV2(ctx context.Context, bookingDetails *structs.CancelBookingCondition, userID int32) error
	AvailedSessionsForSubscriptionGet(subscriptionId int32, availedSessions *float32) error
	SlotGet(ctx context.Context, slotCondition *models.Slot, slot *[]models.Slot) error
	SlotGetOptimized(ctx context.Context, slotCondition *models.Slot, slot *[]models.Slot, limit int, offset int) error
	GetFacilityIdAndSportIdByFsId(fsId *int32, fsIds *structs.FacilitySportIds) error
	FacilityGet(facilityCondition *models.Facility, facility *[]models.Facility) error
	SportGet(sportCondition *models.Sport, sport *[]models.Sport) error
	GetBookingForSlotAndDate(bookingCond *models.AllBooking, bookingEntries *[]models.AllBooking) error
	SendSealsBookingFailureEmail(emData *structs.EmailRequest) error
	SendSportsFeedbackAlertEmail(emData *structs.EmailRequest) error
	SendNotification(ndata *structs.FcmNotification) error
	GetFSIDForSportAndFacility(sportId int32, facilityId int32, fsid *int32) error
	GetProductIdForSubscriptionId(subscriptionId int32, pId *int32) error
	AllBookingSlotsGetFS(ctx context.Context, condition *structs.AllFsActiveSlots, activeSlotFs *[]models.FSActiveSlot) error
	GetAllFacilities(ctx context.Context, facility *[]models.Facility) error
	GetAllSports(ctx context.Context, sport *[]models.Sport) error
	GetAllFacilityIds(facilityIds *[]int32) error
	GetAllSportIds(sportIds *[]int32) error
	GetAllBookedSlotsForUser(ctx context.Context, bookingCond *models.AllBooking, bookingEntries *[]models.AllBooking) error
	GetFacilityIdsAndSportIdsForAllFsIds(ctx context.Context, fsids []int32, fsData *[]structs.FacilitySportIds) error
	GetLiveSessionBookingsForSubscription(bookingCond *models.AllBooking, booking *[]models.AllBooking) error
	GetMaxBookingLimitForUser(userId int32, maxLimit *int32) error
	GetMaxBookingLimitByProduct(productId int32, maxLimit *int32) error
	GetProductIdsForSubscriptionIds(subIdArr []int32, pdSubArr *[]structs.ProductSubscription) error
	GetFSIDForSportAndFacilityIds(fSpArr []structs.FacilitySportIds, fSpFsArr *[]structs.FacilitySportIds) error
	CoachingTypeGet(ctx context.Context, coachingTypeCond *models.CoachingType, coachingType *[]models.CoachingType) error
	GetFeedbackForBooking(feedbackCond *models.SportsFeedbackResponse, feedbackData *[]models.SportsFeedbackResponse) error
	GetAllFeedbackOptions(ctx context.Context, optData *[]models.SportsFeedbackOption, productCategoryId int32) error
	GetAllRatingOptions(ctx context.Context, ratingsData *[]models.SportsFeedbackRating) error
	SaveFeedbackResponse(feedbackData *models.SportsFeedbackResponse, categoryOptions []int32) error
	UpdateFeedbackResponse(FeedbackResponseId *int32, feedbackData *models.SportsFeedbackResponse, categoryOptions []int32) error
	InsertBlockedSlotsAndUpdateActiveSlots(blockedInventory *[]models.SportsBlockedInventory, updateActiveSlots int32) error
	GetDistinctPopulatableActiveFsids(ctx context.Context, fsids *[]int32) error
	GetDistinctPopulatableActivePaidsForFsid(fsid int32, paids *[]int32) error
	GetBlockedInventory(query *models.SportsBlockedInventory, res *[]models.SportsBlockedInventory) error
	GetSportsFeedbackForFilters(feedbackReq *pb.FeedbackListingRequest, feedback *[]models.SportsFeedbackResponse) error
	GetUserProductDetailsByBRN(bookingRefNumber string, userProduct *models.UserProduct) error
	GetCategoryOptionNamesByIds(categoryOptions []int32, feedOptions *[]models.SportsFeedbackOption) error
	GetParentDetailsForUser(parentData *models.UserProduct, userId int32) error
	GetUserDetails(userData *models.UserProduct, userId int32) error
	GetSealsFeedbackForFilters(ctx context.Context, feedbackReq *pb.FeedbackListingRequest, feedback *[]models.SportsFeedbackResponse) error
	CancelBookingsForFsIdSlotIdAndDate(fsPaSlotDate []structs.FsPaSlotDate, cancelledUids *[]int32) error
	GetRelatedFixedUsers(fsSlotDate *[]structs.FsPaSlotDate, fUsers *[]int32) error
	GetTrialUsersWithNoBookingsToday(trialUsersData *[]structs.UserSubscription) error
	GetPremiumUsersWithNoBookingsInLastTwoDays(premiumUsersData *[]structs.UserSubscription) error
	GetSubscribedUserWithNoBookingInLastNdays(premiumUsersData *[]structs.UserSubscription, ndays int32) error
	MessagePublish(phone []string, message string) error
	SportsBookingDataToSpreadsheetsAndSendTrialAlert(bookingRefNumber string, sportId int32, fmPhoneNumbers []string) error
	GetBlockInventoryForFilters(inventoryReq *pb.GetBlockedInventoryRequest, inventory *[]models.SportsBlockedInventory) error
	GetBookingSlotDetails(ctx context.Context, pointers *structs.InfoText, slotReq *pb.BookingSlotsDetailsRequest) error
	GetLiveSessionDetails(sessionDetails *[]models.SessionVideo, online_session_id int32) error
	GetCoachingDetails(pointers *structs.InfoText, csId int32) error
	AttendanceStatusMark(attData *pb.MarkAttendance, markSuccessful *bool) error
	GetCoachingType(ctx context.Context, fsId int32, slotId int32, dayOfWeek int32, coachingType *int32) error
	GetUnmarkedAttendanceUserIds(userData *[]structs.UserData, slotIds []int32) error
	GetDroppedOutBookingsForSlotIds(ctx context.Context, userData *[]structs.UserData, slotIds []int32) error
	GetMatchedBookings(ctx context.Context, slotIds []int32, matchedBookingIds *[]int32) error
	IsOptInSubscription(optInSubs *[]structs.OptIn, subscriptionId int32) error
	GetBlockedInventoryRowsForIds(ids *[]int32, rows *[]models.SportsBlockedInventory) error
	MarkDeletedBlockedInventoryRowsForIds(ids *[]int32, unblockerId int32, updateSuccessful *bool) error
	DeleteFsAndPaActiveSlotsForGiven(data *structs.UnblockCombo, deleteSuccess *bool) error
	GetUnmarkedAttendanceOfSealsUsersIds(userData *[]structs.UserData, slotId int32) error
	GetSlotDetails(SlotId int32, SlotData *[]models.Slot) error
	GetAllFavoriteFacilitiesForUser(ctx context.Context, userId int32, facility *[]models.Facility) error
	GetUpcomingInactiveDays(upcomingInactiveDays *[]models.SportsInactiveDay, inactiveDaysCondition *structs.InactiveDaysCondition) error
	GetLiveSessionBooking(condition *models.OnlineSessions, onlineSessions *[]models.OnlineSessions) error
	GetStreamUrl(online_session_id int32, seconds int64, streamUrl *string) error
	GetRecentSlots(bookingCond *models.AllBooking, recentSlots *[]structs.RecentSlotDetails) error
	GetRecommenedSession(onlineSessions *[]models.OnlineSessions, slots []structs.RecentSlotDetails) error
	SaveOnlineSessionAttendance(osAtt *models.OnlineSessionAttendance, success *bool) error
	GetTimeSpentInSession(os_id int32, user_id int32, time_spent *int32) error
	UpdateAttendanceFlagForOnlineSessions(bookingRefNo string, success *bool) error
	RecordBookingFailure(rec *models.BookingFailureRecord) error
	NoShowPenaltyOn(fsId int32, isOn *bool) error
	MarkNoShowApplicableInSports(bookingId int32) error
	NoShowPenaltyRecordGet(record *[]models.NoShowPenaltyAppliedRecord, cond models.NoShowPenaltyAppliedRecord) error
	NoShowRecordForRevert(record *[]structs.NoShowRevertTask, bookingIds []int32) error
	UpdateNSPARecord(bookingIds []int32, revertedBy int64, reason string, declineFlag bool) error
	UpdateNoShowRevertInBookings(bookingIds []int32) error
	BookingAndAttendanceDetails(details *structs.BookingAndAttendanceDetails, bookingId int32) error
	SendPartnerFacilityBlockingEmail(emData *structs.EmailRequest) error
	GetFacilitySportNameByFsId(fsId *int32, fsName *structs.FacilitySportName) error
	GetTextDetails(data *[]models.TextBodyMappings, cond models.TextBodyMappings) error
	GetTitleDetails(titleDetails *[]models.TextTitleMappings, titleMappingIds []int32) error
	TokenizeFacilityFmMap(facilityPhoneMap map[string]string) error
	GetCacheFacilityFmMap(facilityPhoneMap *map[string]string) error
	SlotBookingAttendanceCount(facilitySportSlotData *[]structs.FacilitySportSlotData, slotIds []int32) error
	SendEmail(emailData *structs.EmailRequest) error
	GetProductFsId(id int32, fsIds *[]int32) error
	GetFacilitySportForFsId(fsIds []int32, facilitySportDetails *[]models.FacilitySportDetails) error
	GetActiveSlotsForFsId(fsIds []int32, activeSlotFsCondition *structs.AllFsActiveSlots, activeSlotFs *[]models.FacilitySportDetails) error
	GetBookingIdInfo(ctx context.Context, slotInfo *models.FacilitySportDetails, bookingId int32) error
	UserNoShowRevertQueryInfo(ctx context.Context, userQueries *[]models.UserBookingRequestQueries, bookingIds []int32) error
	QueryRevertStatus(ctx context.Context, reverted *int32, bookingId int32) error
	GetReasonsForUserQueries(ctx context.Context, reasons *[]models.UserBookingRequestQueries, queryTypeId int32) error
	UserFeedbackForBooking(ctx context.Context, condition string, feedbackData *[]models.SportsFeedbackUserRating) error
	StoreUserQuery(ctx context.Context, queryData *models.UserBookingRequestQueries) error
	GetUserAssessmentLevel(ctx context.Context, userId int32, assessmentLevelId *int32) error
	GetSealsUpcomingBooking(userId int32, usbs *[]models.UserSealsBooking) error
	UserNoShowRevertedInMonth(ctx context.Context, conditions structs.NoShowRevertTask, autoRevertFlag int32, subscriptionStartDate time.Time, queryRevertedCount *int32) error
	IsCompetitiveSwimmingUser(userId int32, users *[]structs.SealsCompetitiveUser) error
	NoShowRevertQueue(ctx context.Context, data *pb.UserBookingRequestQueries) error
	GetReasonInfo(ctx context.Context, reasonId int32, reasonInfo *pb.UserBookingRequestQueries) error
	GetSwimmingslotInfo(ctx context.Context, slotInfo *models.FacilitySportDetails, bRN string) error
	PublishFsIdForSlotPopulation(ctx context.Context, data *pb.FacilitySportDetails) error
	UpdateQueryStatusNoShow(queryData *models.UserBookingRequestQueries) error
	SetInRedis(key string, value string, sessionTime time.Duration) error
	GetFromRedis(key string, value *string) error
	UserNoShowPenaltyPastBuffer(ctx context.Context, conditions structs.NoShowRevertTask, pastQueryData *[]models.UserBookingRequestQueries) error
	AllSwimmingSlotsGetFS(ctx context.Context, fsids []int32, activeSlotFsCondition *structs.AllFsActiveSlots, activeSlotFs *[]models.FSActiveSlot) error
	FacilityVisitedCountGet(ctx context.Context, visitedPeople *[]models.FacilityVisitedCounts, days int32) error
	AddFacilityVisitedCount(ctx context.Context, visitedPeople *models.FacilityVisitedCounts) error
	GetBrnFromBookingId(bookingId int32, brn *models.AllBooking) error
	FetchCountByFacilityId(facilityId int32, days int32, count *models.FacilityVisitedCounts) error
	GetFeedbacksForBRN(ctx context.Context, bookingReferenceNumber string) ([]*models.SportsFeedbackResponse, error)
	GetRatingsFacilityWise(ctx context.Context, rating *float64, facilityCondition *models.FacilityRatings) error
	BookingCountBySport(ctx context.Context, bookingCondition *models.AllBooking, bookingCountBySport *[]models.AllBooking) error
	BookingDetailsGetV4(ctx context.Context, bookingCondition *models.AllBooking, allBooking *[]models.AllBooking, timeline int32) error
	GetPacsIdFromPacsInfo(ctx context.Context, payload []structs.FsPaSlotDate) ([]structs.FsPaSlotDate, error)
	GetTrialAttendedBookingsOfLastNthDay(ctx context.Context, nthDay int32) ([]*models.AllBooking, error)
	AddEditPlayArenaCapacitySlots(ctx context.Context, req *pb.AddEditCapacitySlotRequest) error
	GetBookingSessionCount(ctx context.Context, userIds []int32, bookings *[]models.AllBooking) error
	CreateOrUpdateBookingCoachMapping(ctx context.Context, bookingCoachMapping *models.BookingAssignedCoaches) error
	BatchGetAssignedCoachesByBookingIds(ctx context.Context, bookingIds []int32, assignedCoaches *[]structs.BookingAssignedCoaches) error
	UpsertFMBookingRemark(ctx context.Context, bookingRemark *models.AllBooking) error
	MarkInActiveDayForNonOperational(ctx context.Context, fsId int32, date time.Time, reasonId int32) error
	MarkInactiveDayForFsIds(ctx context.Context, sportsInactiveDays []models.SportsInactiveDay) error
	GetBlockedInventoriesByDate(ctx context.Context, conditions models.SportsBlockedInventory, blockedInventories *[]models.SportsBlockedInventory) error
	GetTodayUpcomingPAActiveSlotsForFsId(ctx context.Context, fsId int32, activeSlotPa *[]models.PAActiveSlot) error
	MarkDeletedFlagSportsInActiveDay(ctx context.Context, fsId int32, date time.Time, updatedBy int32) error
	GetTextSubBody(ctx context.Context, data *[]models.TextSubBody, bodyId int32, sportId int32) error
	CheckIfNoShowRevertIsApplicable(ctx context.Context, bookingId int32, applicableForNoShowRevert *bool) error
	BatchGetConsecutiveBookingFlagMap(ctx context.Context, bookingIds []int32, consecutiveFlags *[]structs.ConsecutiveBooking) error
	AddEditPlayArenaCapacitySlot(ctx context.Context, playArenaCapacitySlot *models.PlayArenaCapacitySlot) error
	BulkInsertInactiveDays(ctx context.Context, sportsInactiveDays []models.SportsInactiveDay) error
	BulkMarkDeletedFlagForInactiveDays(ctx context.Context, sportsInactiveDays []models.SportsInactiveDay) error
	GetInactiveDays(ctx context.Context, startDate int64, endDate int64, inactiveDays *[]models.SportsInactiveDay) error
	SendOverbookingAlertMail(ctx context.Context, overbookingData *[]structs.OverbookingData) error
	PublishBookingUpdateMessageToSQS(data *structs.BookingEventMessageToSQS) error
	PublishSlotClosureEventToSQS(data *structs.SlotClosureSQSEvent) error
	GetSummercampSlots(ctx context.Context, condition *models.SummercampSlot) ([]*models.SummercampSlot, error)
	CheckMasterkeyGuidedSlotsPopulatedForFsIds(ctx context.Context, fsIds []int32, activeSlotPa *[]models.PAActiveSlot) error
	ReduceSummercampSlotCapacity(ctx context.Context, slotData *models.SummercampSlot) error
	GetBulkSummercampSlotsForProductMappingIds(ctx context.Context, summercamp_product_mapping_ids []int32) ([]*models.SummercampSlot, error)
	AddEditSummerCampSlot(ctx context.Context, slotData *models.SummercampSlot) error
	GetSummercampFacilitiesWithSlotsCapacity(ctx context.Context, data *models.SummercampSlot) ([]*models.SummercampFacilitySlotCapacity, error)
	BulkReduceSummerCampSlotCapacity(ctx context.Context, fs_id int32, slot_id int32, summercamp_product_mapping_ids []int32) error
	BulkIncreaseSummercampSlotCapacity(ctx context.Context, fs_id int32, slot_id int32, summercamp_product_mapping_ids []int32) error
	GetSummercampActiveSlots(ctx context.Context, condition *models.SummercampActiveSlot) ([]*models.SummercampActiveSlot, error)
	UpsertSummercampRemainingCapacity(ctx context.Context, ids []int32, remainingCapacity int32) error
	CreateSummercampActiveSlotData(ctx context.Context, data []*models.SummercampActiveSlot) error
	UpdateSummercampSlotActiveSlotId(ctx context.Context, ids []int32, activeSlotId int32) error
	ReduceSummercampActiveSlotCapacity(ctx context.Context, id int32, renewSlot bool) error
	IncreaseSummercampActiveSlotCapacity(ctx context.Context, id int32, renewSlot bool) error
	GetUserBookingDetailsByBookingId(ctx context.Context, bookingID int32) (*models.AllBooking, error)
}

type BookingData struct {
	Db         *gorm.DB
	Client     *redis.Client
	Broker     broker.Broker
	ROdb       *gorm.DB
	AWSSession *session.Session
}

const (
	formatYMD             = "2006-01-02"
	formatYMDHIS          = "2006-01-02 16:22:00"
	referenceNumberLength = 8
	DefaultLocation       = "Asia/Kolkata"
)

func (a *BookingData) CheckIfNoShowRevertIsApplicable(ctx context.Context, bookingId int32, applicableForNoShowRevert *bool) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	type NoShowPenaltyApplicationTime struct {
		CreatedAt time.Time `gorm:"column:created_at"`
	}
	var noShowPenaltyApplicationTime NoShowPenaltyApplicationTime
	res := DB.Table("no_show_penalty_applied_records").Select("created_at").Where("booking_id = ?", bookingId).Find(&noShowPenaltyApplicationTime)
	if res.Error != nil {
		return res.Error
	}
	if noShowPenaltyApplicationTime.CreatedAt.IsZero() {
		log.Println("No show penalty is not applied for this booking Id: ", bookingId)
		return nil
	}
	if time.Now().Sub(noShowPenaltyApplicationTime.CreatedAt).Hours() <= 72 {
		*applicableForNoShowRevert = true
		return nil
	}
	*applicableForNoShowRevert = false
	return nil
}

func (a *BookingData) BookingDetailsGet(ctx context.Context, bookingCondition *models.AllBooking, allBooking *[]models.AllBooking, timeline int32, start int32, count int32) error {
	log.Println("-----------", bookingCondition)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	tx := DB.Table("all_bookings")
	if bookingCondition.FetchSlotBookingsCount {
		tx = tx.Select("all_bookings.`booking_reference_number`, all_bookings.`booking_category`, all_bookings.app_type, all_bookings.`booking_id`, all_bookings.no_show_applicable, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`product_id`, all_bookings.`booking_type`, sum(all_bookings.`booking_capacity`) as `booking_capacity`, all_bookings.`booking_time`, all_bookings.`booking_date`,all_bookings.`created_at`, all_bookings.`created_by`, all_bookings.`fs_id`, all_bookings.`pa_id`, sum(all_bookings.`session_count`) as `session_count`, sum(all_bookings.`charge_levied`) as `charge_levied`, all_bookings.`booking_cancelled`, all_bookings.`attendance_flag`, all_bookings.`attendance_marked_by`, sppr.*, sp.`sport_id`, sp.`sport_name`, sp.`icon_sports_app` as `icon`, sp.`display_picture`, o.`online_session_id`, o.`session_video_id`, o.`title_a`, o.`title_b`, o.`date`, o.`session_time`, o.`description`, s.*, f.*, fsm.`map_link`, fsm.`latitude`, fsm.`longitude`, u.`name` as `user_name`, u.`phone`, u.`email`, sub.`start_date`, sub.`subscription_end_date`, prod.`is_trial`, sfr.`rating_id`, ct.`coaching_text`, ct.`coaching_type`, fscs.`coaching_flag`, fscs.`guided_environment_flag`, (SELECT COUNT(*) FROM `all_bookings` abb WHERE abb.`fs_id` = all_bookings.`fs_id` AND abb.`online_session_id` = all_bookings.`online_session_id` AND abb.`slot_id` = all_bookings.`slot_id` AND abb.`booking_date` = all_bookings.`booking_date` AND abb.`booking_cancelled` = 0) as `slot_bookings_count`, 	CASE WHEN SUM( osa.`duration` ) > 0 THEN SUM( osa.`duration` ) ELSE 0 END as total_duration").
			Where(&bookingCondition)
	} else {
		tx = tx.Select("all_bookings.`booking_reference_number`, all_bookings.`booking_category`, all_bookings.app_type, all_bookings.pacs_id, pacs.product_arena_category_id, all_bookings.`booking_id`, all_bookings.no_show_applicable, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`product_id`, all_bookings.`booking_type`, sum(all_bookings.`booking_capacity`) as `booking_capacity`, all_bookings.`booking_time`, all_bookings.`booking_date`,all_bookings.`created_at`, all_bookings.`created_by`, all_bookings.`fs_id`, all_bookings.`pa_id`, sum(all_bookings.`session_count`) as `session_count`, sum(all_bookings.`charge_levied`) as `charge_levied`, all_bookings.`booking_cancelled`, all_bookings.`attendance_flag`, all_bookings.`attendance_marked_by`, all_bookings.cult_membership_id, sppr.*, sp.`sport_id`, sp.`sport_name`, sp.`icon_sports_app` as `icon`, sp.`display_picture`, o.`online_session_id`, o.`session_video_id`, o.`title_a`, o.`title_b`, o.`date`, o.`session_time`, o.`description`, s.*, f.*, fsm.`map_link`, fsm.`latitude`, fsm.`longitude`, u.`name` as `user_name`, u.`phone`, u.`email`, sub.`start_date`, sub.`subscription_end_date`, prod.`is_trial`, sfr.`rating_id`, ct.`coaching_text`, ct.`coaching_type`, fscs.`coaching_flag`, fscs.`guided_environment_flag`,	CASE WHEN SUM( osa.`duration` ) > 0 THEN SUM( osa.`duration` ) ELSE 0 END as total_duration		").
			Where(&bookingCondition)
	}

	if bookingCondition.BookingStartDate > 0 && bookingCondition.BookingEndDate > 0 {
		bookingTimeStart := time.Unix(bookingCondition.BookingStartDate, 0)
		bookingTimeStartLocal, err := GetLocalDateTime(bookingTimeStart)
		if err != nil {
			log.Println("time converison error bsd-- ", err)
			return err
		}

		bookingTimeEnd := time.Unix(bookingCondition.BookingEndDate, 0)
		bookingTimeEndLocal, err := GetLocalDateTime(bookingTimeEnd)
		if err != nil {
			log.Println("time converison error bed-- ", err)
			return err
		}

		tx = tx.Where("all_bookings.`booking_time` >= ? AND all_bookings.`booking_time` <= ?", bookingTimeStartLocal, bookingTimeEndLocal)

		if timeline == 1 {
			tx = tx.Where("all_bookings.`booking_cancelled` = 0 AND all_bookings.`booking_rescheduled` = 0")
		} else if timeline == 2 {
			tx = tx.Where("all_bookings.`booking_cancelled` = 1 OR all_bookings.`booking_rescheduled` = 1")
		}

	} else {
		if timeline == 1 {
			tx = tx.Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 30 MINUTE) >= CURRENT_TIMESTAMP AND all_bookings.`booking_cancelled` = 0 AND all_bookings.`booking_rescheduled` = 0")
		} else if timeline == 2 {
			if bookingCondition.IgnoreCancelledBookings {
				tx = tx.Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 30 MINUTE) < CURRENT_TIMESTAMP")
				tx = tx.Where("all_bookings.`booking_cancelled` = 0")
			} else {
				tx = tx.Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 30 MINUTE) < CURRENT_TIMESTAMP OR all_bookings.`booking_cancelled` = 1 OR all_bookings.`booking_rescheduled` = 1")
			}
		}
	}
	if bookingCondition.TabId == 1 {
		tx = tx.Where("(all_bookings.`online_session_id` IS  NULL OR all_bookings.`online_session_id` = 0) AND (f.`society_id` IS  NULL OR f.`society_id` = 0) ")
	} else if bookingCondition.TabId == 2 {
		tx = tx.Where("f.`society_id` IS NOT NULL AND f.`society_id` > 0")
	} else if bookingCondition.IsOnlineSession || bookingCondition.TabId == 3 {
		tx = tx.Where("all_bookings.`online_session_id` IS NOT NULL AND all_bookings.`online_session_id` > 0")
	}

	//slot join conditions
	if len(bookingCondition.SlotIds) > 0 {
		slotStrArr := strings.Split(bookingCondition.SlotIds, ",")
		var slotIntArr []int32
		for _, ele := range slotStrArr {
			val, _ := strconv.Atoi(ele)
			slotIntArr = append(slotIntArr, int32(val))
		}
		tx = tx.Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id` AND s.`slot_id` IN (?)", slotIntArr)
	} else {
		tx = tx.Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id`")
	}

	//fs_id join conditions
	if len(bookingCondition.FsIds) > 0 {
		FsStrArr := strings.Split(bookingCondition.FsIds, ",")
		var fsIntArr []int32
		for _, ele := range FsStrArr {
			val, _ := strconv.Atoi(ele)
			fsIntArr = append(fsIntArr, int32(val))
		}
		tx = tx.Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id` AND fsm.`fs_id` IN (?)", fsIntArr)
	} else {
		if bookingCondition.FacilityID > 0 {
			tx = tx.Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id` AND fsm.`facility_id`= ?", bookingCondition.FacilityID)
		} else {
			tx = tx.Joins("LEFT JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`")
		}
	}

	if bookingCondition.IsTrial {
		tx = tx.Joins("INNER JOIN `products` prod ON prod.`product_id` = all_bookings.`product_id` AND prod.`is_trial` = 1")
	} else {
		tx = tx.Joins("LEFT JOIN `products` prod ON prod.`product_id` = all_bookings.`product_id`")
	}

	//mandatory events
	tx = tx.Joins("LEFT JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
		Joins("LEFT JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`").
		Joins("INNER JOIN `users` u ON u.`user_id` = all_bookings.`user_id`").
		Joins("LEFT JOIN `online_sessions` o on o.`online_session_id` = all_bookings.`online_session_id`").
		Joins("LEFT JOIN online_session_attendances osa ON osa.`booking_reference_number`= all_bookings.`booking_reference_number`").
		Joins("LEFT JOIN session_videos sv ON sv.`session_video_id` = o.`session_video_id`").
		Joins("LEFT JOIN service_providers sppr ON sppr.`provider_id` = sv.`provider_id`").
		Joins("LEFT JOIN `subscriptions` sub ON sub.`subscription_id` = all_bookings.`subscription_id`").
		// Joins("LEFT JOIN `products` prod ON prod.`product_id` = all_bookings.`product_id`").
		Joins("LEFT JOIN `sports_feedback_responses` sfr ON sfr.`booking_reference_number` = all_bookings.`booking_reference_number` AND sfr.`deleted_flag` = 0").
		Joins("LEFT JOIN `facility_sport_capacity_slots` fscs ON fscs.`fs_id` = all_bookings.`fs_id` AND fscs.`slot_id` = all_bookings.`slot_id` AND fscs.`day_of_week` = (case when (DAYOFWEEK(DATE(`all_bookings`.booking_time)) - 1) = 0 then 7 else (DAYOFWEEK(DATE(`all_bookings`.booking_time)) - 1) end)").
		Joins("LEFT JOIN `coaching_types` ct ON ct.`coaching_type` = fscs.`coaching_type`").
		Joins("LEFT JOIN `play_arena_capacity_slots` pacs on pacs.pacs_id = all_bookings.pacs_id").
		Group("all_bookings.`booking_id`")

	//order sorting
	if timeline == 1 {
		tx = tx.Order("all_bookings.`booking_time` asc")
	} else {
		tx = tx.Order("all_bookings.`booking_time` desc")
	}

	//other mandatory events
	if count > 0 {
		tx = tx.Offset(start - 1).
			Limit(count)
	}

	response := tx.Find(&allBooking)
	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}

	// if timeline == 1 { //upcoming
	// 	response := a.Db.Select("all_bookings.`booking_reference_number`, all_bookings.`booking_id`, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`product_id`, all_bookings.`booking_type`, sum(all_bookings.`booking_capacity`) as `booking_capacity`, all_bookings.`booking_time`, all_bookings.`booking_date`, all_bookings.`fs_id`, all_bookings.`pa_id`, sum(all_bookings.`session_count`) as `session_count`, sum(all_bookings.`charge_levied`) as `charge_levied`, all_bookings.`booking_cancelled`, sp.`sport_id`, sp.`sport_name`, sp.`icon_sports_app` as `icon`, sp.`display_picture`, s.*, f.*, fsm.`map_link`").
	// 		Where(&bookingCondition).
	// 		Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 1 HOUR) >= CURRENT_TIMESTAMP AND all_bookings.`booking_cancelled` = 0 AND all_bookings.`booking_rescheduled` = 0").
	// 		Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id`").
	// 		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
	// 		Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
	// 		Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`").
	// 		Group("all_bookings.`booking_reference_number`").
	// 		Order("all_bookings.`booking_time` asc").
	// 		Offset(start - 1).
	// 		Limit(count).
	// 		Find(&allBooking)

	// 	if err := response.Error; err != nil {
	// 		fmt.Println(err)
	// 		return err
	// 	}
	// } else if timeline == 2 { //past

	// 	if bookingCondition.IgnoreCancelledBookings { //ignoring cancelled bookings while fetching bookings for feedback
	// 		response := a.Db.Select("all_bookings.`booking_reference_number`, all_bookings.`booking_id`, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`product_id`, all_bookings.`booking_type`, sum(all_bookings.`booking_capacity`) as `booking_capacity`, all_bookings.`booking_time`, all_bookings.`booking_date`, all_bookings.`fs_id`, all_bookings.`pa_id`, sum(all_bookings.`session_count`) as `session_count`, sum(all_bookings.`charge_levied`) as `charge_levied`, all_bookings.`booking_cancelled`, sp.`sport_id`, sp.`sport_name`, sp.`icon_sports_app` as `icon`, sp.`display_picture`, s.*, f.*, fsm.`map_link`").
	// 			Where(&bookingCondition).
	// 			Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 1 HOUR) < CURRENT_TIMESTAMP").
	// 			Where("all_bookings.`booking_cancelled` = 0").
	// 			Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id`").
	// 			Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
	// 			Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
	// 			Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`").
	// 			Group("all_bookings.`booking_reference_number`").
	// 			Order("all_bookings.`booking_time` desc").
	// 			Offset(start - 1).
	// 			Limit(count).
	// 			Find(&allBooking)

	// 		if err := response.Error; err != nil {
	// 			fmt.Println(err)
	// 			return err
	// 		}

	// 	} else {
	// 		response := a.Db.Select("all_bookings.`booking_reference_number`,  all_bookings.`booking_id`, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`product_id`, all_bookings.`booking_type`, sum(all_bookings.`booking_capacity`) as `booking_capacity`, all_bookings.`booking_time`, all_bookings.`booking_date`, all_bookings.`fs_id`, all_bookings.`pa_id`, sum(all_bookings.`session_count`) as `session_count`, sum(all_bookings.`charge_levied`) as `charge_levied`, all_bookings.`booking_cancelled`, sp.`sport_id`, sp.`sport_name`, sp.`icon_sports_app` as `icon`, sp.`display_picture`, s.*, f.*, fsm.`map_link`").
	// 			Where(&bookingCondition).
	// 			Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 1 HOUR) < CURRENT_TIMESTAMP OR all_bookings.`booking_cancelled` = 1 OR all_bookings.`booking_rescheduled` = 1").
	// 			Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id`").
	// 			Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
	// 			Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
	// 			Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`").
	// 			Group("all_bookings.`booking_reference_number`").
	// 			Order("all_bookings.`booking_time` desc").
	// 			Offset(start - 1).
	// 			Limit(count).
	// 			Find(&allBooking)

	// 		if err := response.Error; err != nil {
	// 			fmt.Println(err)
	// 			return err
	// 		}
	// 	}
	// } else { //all
	// 	response := a.Db.Select("all_bookings.`booking_reference_number`,  all_bookings.`booking_id`, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`product_id`, all_bookings.`booking_type`, sum(all_bookings.`booking_capacity`) as `booking_capacity`, all_bookings.`booking_time`, all_bookings.`booking_date`, all_bookings.`fs_id`, all_bookings.`pa_id`, sum(all_bookings.`session_count`) as `session_count`, sum(all_bookings.`charge_levied`) as `charge_levied`, all_bookings.`booking_cancelled`, sp.`sport_id`, sp.`sport_name`, sp.`icon_sports_app` as `icon`, sp.`display_picture`, s.*, f.*, fsm.`map_link`").
	// 		Where(&bookingCondition).
	// 		Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id`").
	// 		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
	// 		Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
	// 		Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`").
	// 		Group("all_bookings.`booking_reference_number`").
	// 		Order("all_bookings.`booking_time` desc").
	// 		Offset(start - 1).
	// 		Limit(count).
	// 		Find(&allBooking)

	// 	if err := response.Error; err != nil {
	// 		fmt.Println(err)
	// 		return err
	// 	}
	// }

	//log.Println(allBooking)
	return nil
}

func (a *BookingData) BookingDetailsGetV2(ctx context.Context, bookingCondition *models.AllBooking, allBooking *[]models.AllBooking, timeline int32, start int32, count int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.ROdb)
	tx := DB.Table("all_bookings").Select("all_bookings.booking_id, all_bookings.`created_at`,all_bookings.`booking_time`, all_bookings.`booking_date`, all_bookings.`booking_reference_number`, all_bookings.`attendance_flag`, all_bookings.`subscription_id`, all_bookings.`user_id`, all_bookings.`cult_membership_id`, all_bookings.`booking_type`,all_bookings.`booking_capacity`, all_bookings.`session_count`,  all_bookings.`fs_id`, all_bookings.`booking_cancelled`,all_bookings.`attendance_marked_by`, all_bookings.`booking_category`, all_bookings.`created_by`, all_bookings.`slot_id`, all_bookings.`pa_id`, all_bookings.`pacs_id`, all_bookings.remark, all_bookings.app_type").
		Where(&bookingCondition)

	if bookingCondition.BookingStartDate > 0 && bookingCondition.BookingEndDate > 0 {
		bookingTimeStart := time.Unix(bookingCondition.BookingStartDate, 0)
		bookingTimeStartLocal, err := GetLocalDateTime(bookingTimeStart)
		if err != nil {
			log.Println("time converison error bsd-- ", err)
			return err
		}

		bookingTimeEnd := time.Unix(bookingCondition.BookingEndDate, 0)
		bookingTimeEndLocal, err := GetLocalDateTime(bookingTimeEnd)
		if err != nil {
			log.Println("time converison error bed-- ", err)
			return err
		}

		tx = tx.Where("all_bookings.`booking_time` >= ? AND all_bookings.`booking_time` <= ?", bookingTimeStartLocal, bookingTimeEndLocal)

		if timeline == 1 {
			tx = tx.Where("all_bookings.`booking_cancelled` = 0 AND all_bookings.`booking_rescheduled` = 0")
		} else if timeline == 2 {
			tx = tx.Where("all_bookings.`booking_cancelled` = 1 OR all_bookings.`booking_rescheduled` = 1")
		}

	} else {
		if timeline == 1 {
			tx = tx.Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 30 MINUTE) >= CURRENT_TIMESTAMP AND all_bookings.`booking_cancelled` = 0 AND all_bookings.`booking_rescheduled` = 0")
		} else if timeline == 2 {
			if bookingCondition.IgnoreCancelledBookings {
				tx = tx.Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 30 MINUTE) < CURRENT_TIMESTAMP")
				tx = tx.Where("all_bookings.`booking_cancelled` = 0")
			} else {
				tx = tx.Where("DATE_ADD(all_bookings.`booking_time`, INTERVAL 30 MINUTE) < CURRENT_TIMESTAMP OR all_bookings.`booking_cancelled` = 1 OR all_bookings.`booking_rescheduled` = 1")
			}
		}
	}

	if len(bookingCondition.SlotIds) > 0 {
		slotStrArr := strings.Split(bookingCondition.SlotIds, ",")
		var slotIntArr []int32
		for _, ele := range slotStrArr {
			val, _ := strconv.Atoi(ele)
			slotIntArr = append(slotIntArr, int32(val))
		}
		tx = tx.Where("all_bookings.`slot_id` IN (?)", slotIntArr)
	}

	if len(bookingCondition.FsIds) > 0 {
		FsStrArr := strings.Split(bookingCondition.FsIds, ",")
		var fsIntArr []int32
		for _, ele := range FsStrArr {
			val, _ := strconv.Atoi(ele)
			fsIntArr = append(fsIntArr, int32(val))
		}
		tx = tx.Where("all_bookings.`fs_id` IN (?)", fsIntArr)
	}

	if len(bookingCondition.ProductArenaCategoryIds) > 0 {
		productArenaCategoryIds := strings.Split(bookingCondition.ProductArenaCategoryIds, ",")
		var productArenaCategoryIdsIntArr []int32
		for _, ele := range productArenaCategoryIds {
			val, _ := strconv.Atoi(ele)
			productArenaCategoryIdsIntArr = append(productArenaCategoryIdsIntArr, int32(val))
		}
		tx = tx.Joins("INNER JOIN `play_arena_capacity_slots` pacs ON pacs.`pacs_id` = all_bookings.`pacs_id`").
			Where("pacs.`product_arena_category_id` IN (?)", productArenaCategoryIdsIntArr)
	}

	if bookingCondition.IsTrial {
		tx = tx.Joins("LEFT JOIN `products` prod ON prod.`product_id` = all_bookings.`product_id`")
		tx = tx.Where("prod.`is_trial` = 1 OR all_bookings.subscription_id = 0")
	}
	tx = tx.Group("all_bookings.`booking_id`")
	//order sorting
	if timeline == 1 {
		tx = tx.Order("all_bookings.`booking_time` asc")
	} else {
		tx = tx.Order("all_bookings.`booking_time` desc")
	}

	//other mandatory events
	if count > 0 {
		tx = tx.Offset(start - 1).
			Limit(count)
	}

	response := tx.Find(&allBooking)
	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) BookingDetailsGetV4(ctx context.Context, bookingCondition *models.AllBooking, allBooking *[]models.AllBooking, timeline int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	tx := DB.Table("`all_bookings` ab").
		Select("ab.booking_id, ab.user_id, ab.fs_id, ab.`booking_time`, ab.`booking_reference_number`, ab.`subscription_id`, ab.`attendance_flag`, s.sport_id, s.sport_name").
		Joins("INNER JOIN facility_sport_mappings fsm on fsm.fs_id = ab.fs_id").
		Joins("INNER JOIN sports s on s.sport_id = fsm.sport_id").
		Joins("INNER JOIN play_arena_capacity_slots pacs ON ab.pacs_id = pacs.pacs_id").
		Joins("INNER JOIN product_arena_category_mappings pacm ON pacm.id = pacs.product_arena_category_id AND pacm.product_category_id = ?", bookingCondition.ProductCategoryId)

	if bookingCondition.NoShowApplicable {
		tx = tx.Where("ab.no_show_applicable = 1")
	}
	if len(bookingCondition.BookingReferenceNumber) > 0 {
		tx = tx.Where("ab.booking_reference_number = ?", bookingCondition.BookingReferenceNumber)
	}
	if bookingCondition.UserID > 0 {
		tx = tx.Where("ab.user_id = ? OR ab.created_by = ?", bookingCondition.UserID, bookingCondition.UserID)
	}

	if bookingCondition.BookingID > 0 {
		tx = tx.Where("ab.booking_id = ?", bookingCondition.BookingID)
	}

	if bookingCondition.BookingStartDate > 0 && bookingCondition.BookingEndDate > 0 {
		bookingTimeStart := time.Unix(bookingCondition.BookingStartDate, 0)
		bookingTimeStartLocal, err := GetLocalDateTime(bookingTimeStart)
		if err != nil {
			log.Println("time converison error bsd-- ", err)
			return err
		}

		bookingTimeEnd := time.Unix(bookingCondition.BookingEndDate, 0)
		bookingTimeEndLocal, err := GetLocalDateTime(bookingTimeEnd)
		if err != nil {
			log.Println("time converison error bed-- ", err)
			return err
		}

		tx = tx.Where("ab.`booking_time` >= ? AND ab.`booking_time` <= ?", bookingTimeStartLocal, bookingTimeEndLocal)

		if timeline == 1 {
			tx = tx.Where("ab.`booking_cancelled` = 0 AND ab.`booking_rescheduled` = 0")
		} else if timeline == 2 {
			tx = tx.Where("ab.`booking_cancelled` = 1 OR ab.`booking_rescheduled` = 1")
		}
	} else {
		if timeline == 1 {
			tx = tx.Where("DATE_ADD(ab.`booking_time`, INTERVAL 30 MINUTE) >= CURRENT_TIMESTAMP AND ab.`booking_cancelled` = 0 AND ab.`booking_rescheduled` = 0")
		} else if timeline == 2 {
			tx = tx.Where("DATE_ADD(ab.`booking_time`, INTERVAL 30 MINUTE) < CURRENT_TIMESTAMP OR ab.`booking_cancelled` = 1 OR ab.`booking_rescheduled` = 1")
		}
	}
	tx = tx.Group("ab.`booking_id`")

	if timeline == 1 {
		tx = tx.Order("ab.`booking_time` asc")
	} else {
		tx = tx.Order("ab.`booking_time` desc")
	}

	response := tx.Find(&allBooking)
	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) FreezeLogsForSubscriptionGet(freezeModel *[]models.SportsUserFreezeLog, freezeCond *models.SportsUserFreezeLog) error {
	fmt.Println("freeze log get cond ----- ", freezeCond)

	response := a.Db.Table("sports_user_freeze_logs").
		Where(&freezeCond).
		Where("deleted_flag = ?", 0).
		Find(&freezeModel)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting freeze logs....", err)
		return err
	}

	return nil
}

func (a *BookingData) GetSlotDetails(SlotId int32, SlotData *[]models.Slot) error {

	response := a.Db.Table("slots").
		Where("slots.`slot_id` = ?", SlotId).
		Find(&SlotData)

	if response.Error != nil {
		fmt.Println("Error in fetching slot details", response.Error)
		return response.Error
	}

	fmt.Println(SlotData)
	return nil
}

func (a *BookingData) SportsBookingDataToSpreadsheetsAndSendTrialAlert(bookingRefNumber string, sportId int32, fmPhoneNumbers []string) error {

	bookingSheetId := "1rRBPDij3BXfsRnxkOfsOz2a3HoVuEDviO2UL8cvSjmc"
	var rows *sql.Rows
	var errR error

	if sportId != 3 {
		rows, errR = a.Db.Table("all_bookings ab").
			Select("ab.`booking_date`,u.`name` as user_name,ss.`user_id`, u.`phone`, u.`email`, sp.`name`, pr.`product_description` as description,s.`timing` as timing, ss.`start_date`, ss.`subscription_end_date`,ab.`booking_cancelled`,f.`facility_id`, ss.`subscription_id`, ab.`booking_reference_number`,pr.`is_trial`").
			Joins("INNER JOIN subscriptions ss ON ab.`subscription_id` = ss.`subscription_id`").
			Joins("INNER JOIN products pr ON ab.`product_id` = pr.`product_id`").
			Joins("INNER JOIN users u ON u.`user_id` = ab.`user_id`").
			Joins("INNER JOIN slots s ON s.`slot_id` = ab.`slot_id`").
			Joins("INNER JOIN facility_sport_mappings fsm ON ab.`fs_id` = fsm.`fs_id`").
			Joins("INNER JOIN facilities f ON f.`facility_id` = fsm.`facility_id`").
			Joins("INNER JOIN sports sp ON sp.`sport_id` = fsm.`sport_id`").
			Where("ab.`booking_reference_number` = ?", bookingRefNumber).
			Rows()
	} else {
		rows, errR = a.Db.Table("user_seals_bookings usb").
			Select("usb.`booking_date`,u.`name` as user_name,u.`user_id`, u.`phone`, u.`email`,'Swimming' as name, p.`product_description` as description, ss.`timings` as timing, sup.`actual_start_date` as start_date , sup.`next_billing_date` as subscription_end_date,usb.`cancelled_flag` as booking_cancelled,f.`facility_id`, sup.`all_sports_subscription_id` as `subscription_id`, usb.`booking_reference_number`,p.`is_trial`").
			Joins("INNER JOIN swimming_users_plans sup ON usb.`sup_id` = sup.`swimming_users_plan_id` AND sup.`all_sports_subscription_id` > ?", 0).
			Joins("INNER JOIN swimming_plan_types spt ON spt.`plan_type_id` = sup.`plan_type`").
			Joins("INNER JOIN users u ON u.`user_id` = usb.`user_id`").
			Joins("INNER JOIN swimming_slots ss ON ss.`slot_id` = usb.`slot_id`").
			Joins("INNER JOIN swimming_locations sl ON sl.`swimming_location_id` = usb.`swimming_location_id`").
			Joins("INNER JOIN facilities f ON f.`facility_id` = sl.`facility_id`").
			Joins("INNER JOIN subscriptions subs ON subs.`subscription_id` = sup.`all_sports_subscription_id`").
			Joins("INNER JOIN products p ON subs.`product_id` = p.`product_id`").
			Where("usb.`booking_reference_number` IS NOT NULL AND usb.`booking_reference_number` = ?", bookingRefNumber).
			Rows()
	}

	if errR != nil {
		log.Println("details for sheet get error", errR)
		return errR
	}
	columns, err := rows.Columns()
	if err != nil {
		log.Println("details for sheet column length error", err)
		return err
	}

	length := len(columns)

	result := make([]map[string]interface{}, 0)

	for rows.Next() {
		current := makeResultReceiver(length)
		if err := rows.Scan(current...); err != nil {
			log.Println(" [DEBUG] ", err)
			return err
		}
		value := make(map[string]interface{})
		for i := 0; i < length; i++ {
			k := columns[i]
			v := (*(current[i]).(*interface{}))

			switch v.(type) {
			case []byte:
				v = string(v.([]byte))
				break
			case time.Time:
				v = v.(time.Time).Format(formatYMD)
			}

			value[k] = v
		}
		result = append(result, value)
	}
	var data map[string]interface{}
	for _, value := range result {
		data = value
		break
	}

	log.Println("Data for sheets --- ", data)
	if len(data) == 0 {
		log.Println("No data from db -- skipping entry in sheets..")
		return nil
	}

	var bookingAction string
	if int(data["booking_cancelled"].(int64)) == 1 {
		bookingAction = "Booking Cancelled"
	} else {
		bookingAction = "Booking Created"
	}

	var subscriptionStatus string
	subscriptionTime, _ := time.Parse(formatYMD, string(data["subscription_end_date"].(string)))
	subscriptionEndDate, _ := GetLocalDateTime(subscriptionTime)
	subscriptionTime, _ = time.Parse(formatYMD, string(data["start_date"].(string)))
	subscriptionStartDate, _ := GetLocalDateTime(subscriptionTime)
	currentTimeLocal, err := GetLocalDateTime(time.Now())

	daysDiffStart := currentTimeLocal.Sub(subscriptionStartDate).Hours() / 24
	daysDiffEnd := subscriptionEndDate.Sub(currentTimeLocal).Hours() / 24

	if int(data["is_trial"].(int64)) == 1 {
		subscriptionStatus = "Trial User"
	} else if daysDiffStart <= 15 {
		subscriptionStatus = "New Premium User"
	} else if daysDiffEnd <= 15 {
		subscriptionStatus = "Expiring Soon Premium User"
	} else {
		subscriptionStatus = "Premium User"
	}

	body := [...]interface{}{data["booking_date"], data["booking_reference_number"], data["user_name"], data["user_id"], data["phone"], data["email"], data["name"], data["description"], data["timing"], subscriptionStatus, data["start_date"], data["subscription_end_date"], bookingAction, data["subscription_id"]}

	var bookingRange string
	switch int(data["facility_id"].(int64)) {
	case 33:
		bookingRange = "FF!A1:N1"
		break
	case 36:
		bookingRange = "Gallant!A1:N1"
		break
	case 9:
		bookingRange = "Swiss!A1:N1"
		break
	case 37:
		bookingRange = "Eva!A1:N1"
		break
	case 38:
		bookingRange = "Spuddy!A1:N1"
		break
	case 20:
		bookingRange = "AEX!A1:N1"
		break
	case 27:
		bookingRange = "JNS!A1:N1"
		break
	default:
		bookingRange = "Other!A1:N1"
		break

		//add more facility ids case here
	}

	shData := structs.SpreadsheetPushRequest{
		SpreadsheetId: bookingSheetId,
		Range:         bookingRange,
		Values:        body[:],
	}

	bodyBytes, _ := json.Marshal(shData)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "1",
		},
		Body: bodyBytes,
	}

	errSh := a.Broker.Publish(sheetPushTopic, msg)

	if errSh != nil {
		log.Println("[pub] failed: ", errSh)
	} else {
		log.Println("[pub] pubbed message:", string(msg.Body))
	}

	if len(fmPhoneNumbers) > 0 && int(data["is_trial"].(int64)) == 1 && int(data["booking_cancelled"].(int64)) != 1 {
		smsText := "TRIAL User just booked the FITSO session :- \n"
		smsText += "Name: " + data["user_name"].(string) + " (" + strconv.Itoa(int(data["user_id"].(int64))) + ") \n"
		smsText += "Sport: " + data["name"].(string) + "\n"
		smsText += "Date: " + data["booking_date"].(string) + "\n"
		smsText += "Timing: " + data["timing"].(string) + "\n"
		if len(data["phone"].(string)) > 0 {
			smsText += "Contact: " + data["phone"].(string) + "\n"
		}
		if errB := a.MessagePublish(fmPhoneNumbers, smsText); errB != nil {
			log.Println("send trial message error")
		}
	}

	return nil
}

func makeResultReceiver(length int) []interface{} {
	result := make([]interface{}, 0, length)
	for i := 0; i < length; i++ {
		var current interface{}
		//current = struct{}{}
		result = append(result, &current)
	}
	return result
}

func (a *BookingData) TokenizeFacilityFmMap(facilityPhoneMap map[string]string) error {

	sessionTime := time.Duration(1800 * 1000 * 1000 * 1000) // 30 min
	json, err := json.Marshal(facilityPhoneMap)
	if err != nil {
		return err
	}

	if err := a.Client.Set("FacilityFmPhoneMap", json, sessionTime).Err(); err != nil {
		log.Println("Error in tokenizing user categories -->", err)
		return err
	}

	return nil
}

func (a *BookingData) GetCacheFacilityFmMap(facilityPhoneMap *map[string]string) error {

	output, err := a.Client.Get("FacilityFmPhoneMap").Result()
	if err != nil {
		return err
	}

	log.Println("Cache data", output)

	err1 := json.Unmarshal([]byte(output), &facilityPhoneMap)
	if err1 != nil {
		return err
	}

	return nil
}

func (a *BookingData) PublishBooking(publishBookingRequest *pb.PublishBookingRequest, publishBookingResponse *pb.PublishBookingResponse) error {

	//convert booking time to date and time
	bookingTime := time.Unix(publishBookingRequest.BookingTime, 0)
	bookingTimeLocal, err := GetLocalDateTime(bookingTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	loc, _ := time.LoadLocation(DefaultLocation)
	BookingDate, parseErrDate := time.ParseInLocation(formatYMD, bookingTimeLocal.Format(formatYMD), loc)
	if parseErrDate != nil {
		fmt.Println("Error in parsing BookingTime! Rolling Back -- error: ", parseErrDate)
		return parseErrDate
	}

	//add slot start hour to get actual booking time
	var SlotData []models.Slot
	a.GetSlotDetails(publishBookingRequest.SlotID, &SlotData)
	for _, element := range SlotData {
		StartHour := element.StartHour
		StartMin := element.StartMin
		bookingTimeLocal = BookingDate.Add(time.Hour*time.Duration(StartHour) + time.Minute*time.Duration(StartMin))
		break
	}

	CapacityToCheck := structs.CheckCapacity{
		FSID:         publishBookingRequest.FSID,
		PAID:         publishBookingRequest.PAID,
		SlotID:       publishBookingRequest.SlotID,
		Capacity:     publishBookingRequest.BookingCapacity,
		CapacityType: publishBookingRequest.BookingType,
		BookingTime:  bookingTimeLocal,
	}

	var IndividualCapacity int32
	var paidCapacity []structs.PAIDCapacity

	a.GetIndividualCapacity(CapacityToCheck, &IndividualCapacity)

	if publishBookingRequest.PAID <= 0 {

		CapacityToCheck.IndividualCapacity = IndividualCapacity
		a.GetPAIDCapacity(CapacityToCheck, &paidCapacity)
	} else {
		paidCapacity = append(paidCapacity, structs.PAIDCapacity{
			RemainingCapacity: IndividualCapacity * CapacityToCheck.Capacity,
			PAID:              CapacityToCheck.PAID,
		})
	}

	var BookingReferenceNumber string

	if publishBookingRequest.ForBookingModification && len(publishBookingRequest.BookingReferenceNumber) > 0 {
		fmt.Println("Same brn now!")
		BookingReferenceNumber = publishBookingRequest.BookingReferenceNumber
	} else {
		if err := a.GenerateUniqueBookingReferenceNumber(&BookingReferenceNumber); err != nil {
			fmt.Println("Error in generating booking reference number -- Rolling Back!")
			return err
		}
	}

	//enter into all bookings
	for index, el := range paidCapacity {

		fmt.Println("Starting Publish Booking Transaction (", index+1, ") -----------------------------")

		tx := a.Db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		if err := tx.Error; err != nil {
			return err
		}

		//check if capacity remaining negative
		fsCapCheck := true
		paCapCheck := true
		//fs
		type CapacityRemainingFs struct {
			Capacity int32
		}
		var capRemainingFs CapacityRemainingFs

		responseCapFs := tx.Table("fs_active_slots").
			Set("gorm:query_option", "FOR UPDATE").
			Select("fs_active_slots.`remaining_capacity` as `capacity`").
			Where("fs_active_slots.`fs_id` = ? and fs_active_slots.`slot_id` = ?  and DATE(fs_active_slots.`date`) = ?", publishBookingRequest.FSID, publishBookingRequest.SlotID, bookingTimeLocal.Format(formatYMD)).
			Find(&capRemainingFs)

		if err := responseCapFs.Error; err != nil {
			fmt.Println(err)
			fmt.Println("get fs_active_slots remaining cap error -- Rolling Back")
			tx.Rollback()
			return err
		}
		fmt.Println("--------------", capRemainingFs)

		if capRemainingFs.Capacity-el.RemainingCapacity < 0 {
			fmt.Println("Unexpected capacity negative Fs --- Rolling Back")
			fsCapCheck = false
			tx.Rollback()
		}

		//pa
		type CapacityRemainingPa struct {
			Capacity int32
		}
		var capRemainingPa CapacityRemainingPa

		responseCapPa := tx.Table("pa_active_slots").
			Set("gorm:query_option", "FOR UPDATE").
			Select("pa_active_slots.`remaining_capacity` as `capacity`").
			Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`pa_id` = ? and pa_active_slots.`slot_id` = ?  and DATE(pa_active_slots.`date`) = ?", publishBookingRequest.FSID, el.PAID, publishBookingRequest.SlotID, bookingTimeLocal.Format(formatYMD)).
			Find(&capRemainingPa)

		if err := responseCapPa.Error; err != nil {
			fmt.Println(err)
			fmt.Println("get pa_active_slots remaining cap error -- Rolling Back")
			tx.Rollback()
			return err
		}
		fmt.Println("--------------", capRemainingPa)

		if capRemainingPa.Capacity-el.RemainingCapacity < 0 {
			fmt.Println("Unexpected capacity negative Pa --- Rolling Back")
			paCapCheck = false
			tx.Rollback()
		}

		if fsCapCheck && paCapCheck {
			//reduce slot capacity here -- FSC PAC
			responseFs := tx.Table("fs_active_slots").
				Where("fs_active_slots.`fs_id` = ? and fs_active_slots.`slot_id` = ? and DATE(fs_active_slots.`date`) = ?", publishBookingRequest.FSID, publishBookingRequest.SlotID, bookingTimeLocal.Format(formatYMD)).
				UpdateColumn("fs_active_slots.remaining_capacity", gorm.Expr("fs_active_slots.remaining_capacity - ?", el.RemainingCapacity))

			if err := responseFs.Error; err != nil {
				fmt.Println(err)
				fmt.Println("Update fs_active_slots error -- Rolling Back")
				tx.Rollback()
				return err
			}

			responsePa := tx.Table("pa_active_slots").
				Select("pa_active_slots.remaining_capacity").
				Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`pa_id` = ? and pa_active_slots.`slot_id` = ?  and DATE(pa_active_slots.`date`) = ?", publishBookingRequest.FSID, el.PAID, publishBookingRequest.SlotID, bookingTimeLocal.Format(formatYMD)).
				UpdateColumn("pa_active_slots.remaining_capacity", gorm.Expr("pa_active_slots.remaining_capacity - ?", el.RemainingCapacity))

			if err := responsePa.Error; err != nil {
				fmt.Println(err)
				fmt.Println("Update pa_active_slots error -- Rolling Back")
				tx.Rollback()
				return err
			}
		}

		allBookingData := &models.AllBooking{
			UserID:                 publishBookingRequest.UserID,
			SubscriptionID:         publishBookingRequest.SubscriptionID,
			ProductID:              publishBookingRequest.ProductID,
			SlotID:                 publishBookingRequest.SlotID,
			PAID:                   el.PAID,
			FSID:                   publishBookingRequest.FSID,
			BookingType:            publishBookingRequest.BookingType,
			BookingCapacity:        el.RemainingCapacity / IndividualCapacity,
			SessionCount:           el.RemainingCapacity, //newBookingSessionCount,
			BookingTime:            bookingTimeLocal,
			BookingDate:            BookingDate,
			AppType:                publishBookingRequest.AppType,
			AppVersion:             publishBookingRequest.AppVersion,
			BookingReferenceNumber: BookingReferenceNumber,
			ChargeLevied:           float32(el.RemainingCapacity), //same as session count for now
			CoachingType:           publishBookingRequest.CoachingType,
			CreatedBy:              publishBookingRequest.UserID,
		}

		if err := tx.Create(allBookingData).Error; err != nil {
			fmt.Println("Creating all booking data error! Rolling back")
			tx.Rollback()
			return err
		}

		fmt.Println("Committing Publish Booking Transaction (", index+1, ") -----------------------------")

		if err := tx.Commit().Error; err != nil {
			fmt.Println("Commiting txn error!")
			return err
		}

	}

	publishBookingResponse.BookingReferenceNumber = BookingReferenceNumber

	return nil
}

func (a *BookingData) PublishLiveSessionBooking(publishBookingRequest *pb.PublishBookingRequest, publishBookingResponse *pb.PublishBookingResponse) error {

	//convert booking time to date and time
	bookingTime := time.Unix(publishBookingRequest.BookingTime, 0)
	bookingTimeLocal, err := GetLocalDateTime(bookingTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	loc, _ := time.LoadLocation(DefaultLocation)
	BookingDate, parseErrDate := time.ParseInLocation(formatYMD, bookingTimeLocal.Format(formatYMD), loc)
	if parseErrDate != nil {
		fmt.Println("Error in parsing BookingTime! Rolling Back -- error: ", parseErrDate)
		return parseErrDate
	}

	//add slot start hour to get actual booking time
	var SlotData []models.Slot
	a.GetSlotDetails(publishBookingRequest.SlotID, &SlotData)
	for _, element := range SlotData {
		StartHour := element.StartHour
		StartMin := element.StartMin
		bookingTimeLocal = BookingDate.Add(time.Hour*time.Duration(StartHour) + time.Minute*time.Duration(StartMin))
		break
	}

	var IndividualOnlineSessions models.OnlineSessions

	response := a.Db.Table("online_sessions").
		Select("online_sessions.*").
		Where("online_sessions.`online_session_id` = ? AND online_sessions.`deleted_flag` = 0", publishBookingRequest.OnlineSessionId).
		Find(&IndividualOnlineSessions)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	var BookingReferenceNumber string

	if publishBookingRequest.ForBookingModification && len(publishBookingRequest.BookingReferenceNumber) > 0 {
		fmt.Println("Same brn now!")
		BookingReferenceNumber = publishBookingRequest.BookingReferenceNumber
	} else {
		if err := a.GenerateUniqueBookingReferenceNumber(&BookingReferenceNumber); err != nil {
			fmt.Println("Error in generating booking reference number -- Rolling Back!")
			return err
		}
	}
	// get booked capacity from all bookings
	type BookedCapacity struct {
		BookedCapacity int32
	}
	var bookedCapacity BookedCapacity

	responseBookingCapacity := a.Db.Table("all_bookings").
		Select("sum(all_bookings.`booking_capacity`) as booked_capacity ").
		Where("all_bookings.`online_session_id`= ?  AND all_bookings.`booking_cancelled` = ?", publishBookingRequest.OnlineSessionId, 0).
		Find(&bookedCapacity)
	if err := responseBookingCapacity.Error; err != nil {
		fmt.Println(err)
		return err
	}
	//enter into all bookings

	if IndividualOnlineSessions.Capacity >= publishBookingRequest.BookingCapacity+bookedCapacity.BookedCapacity {
		fmt.Println(IndividualOnlineSessions.Capacity, publishBookingRequest.BookingCapacity+bookedCapacity.BookedCapacity)
		tx := a.Db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		if err := tx.Error; err != nil {
			return err
		}

		allBookingData := &models.AllBooking{
			UserID:          publishBookingRequest.UserID,
			SubscriptionID:  publishBookingRequest.SubscriptionID,
			ProductID:       publishBookingRequest.ProductID,
			SlotID:          publishBookingRequest.SlotID,
			OnlineSessionID: publishBookingRequest.OnlineSessionId,
			BookingCapacity: publishBookingRequest.BookingCapacity,

			BookingType:            publishBookingRequest.BookingType,
			BookingTime:            bookingTimeLocal,
			BookingDate:            BookingDate,
			AppType:                publishBookingRequest.AppType,
			AppVersion:             publishBookingRequest.AppVersion,
			BookingReferenceNumber: BookingReferenceNumber,
		}

		if err := tx.Create(allBookingData).Error; err != nil {
			fmt.Println("Creating all booking data error! Rolling back")
			tx.Rollback()
			return err
		}

		//fmt.Println("Committing Publish Booking Transaction (", index+1, ") -----------------------------")

		if err := tx.Commit().Error; err != nil {
			fmt.Println("Commiting txn error!")
			return err
		}
		publishBookingResponse.BookingReferenceNumber = BookingReferenceNumber
		status := &pb.Status{
			Status:  "success",
			Message: "Operation successfull",
		}
		publishBookingResponse.Status = status
	} else {
		status := &pb.Status{
			Status:  "failed",
			Message: "Session sold out!",
		}
		publishBookingResponse.Status = status
	}

	return nil
}

func (a *BookingData) GenerateUniqueBookingReferenceNumber(bookingRefNumber *string) error {

	var generationSuccessful bool = false
	for generationSuccessful == false {

		newReferenceNumber := GenerateReferenceNumber(referenceNumberLength)

		type BookingIdData struct {
			BookingId int32
		}
		var bookingIdData BookingIdData

		response := a.Db.Table("all_bookings").
			Select("all_bookings.`booking_id`").
			Where("all_bookings.`booking_reference_number` = ?", newReferenceNumber).
			Scan(&bookingIdData)

		if err := response.Error; err != nil {
			if gorm.IsRecordNotFoundError(err) == false {
				fmt.Println("Error in getting booking data by reference number!")
				generationSuccessful = false
				return err
			} else {
				*bookingRefNumber = newReferenceNumber
				generationSuccessful = true
				return nil
			}
		}

		if bookingIdData.BookingId > 0 {
			generationSuccessful = false
		}
	}

	return nil
}

func (a *BookingData) GetBookingWindowForFacilitySport(fsid int32, prebookingWindow *int32) error {

	type PrebookingWindowData struct {
		Window int32
	}

	var windowSize PrebookingWindowData

	response := a.Db.Table("facility_sport_mappings").
		Select("facility_sport_mappings.`prebooking_window_size` as `window`").
		Where("facility_sport_mappings.`fs_id` = ?", fsid).
		Scan(&windowSize)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	*prebookingWindow = windowSize.Window

	return nil
}

func (a *BookingData) GetRemainingCapacity(CapacityCheck structs.CheckCapacity, CapacityFlag *bool) error {
	var PAActiveSlot models.PAActiveSlot

	a.Db.
		Table("pa_active_slots").
		Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`pa_id` = ? and pa_active_slots.`slot_id` = ? and DATE(pa_active_slots.`date`) = ?", CapacityCheck.FSID, CapacityCheck.PAID, CapacityCheck.SlotID, CapacityCheck.BookingTime.Format(formatYMD)).
		Scan(&PAActiveSlot)

	var IndividualCapacity int32
	a.GetIndividualCapacity(CapacityCheck, &IndividualCapacity)

	if PAActiveSlot.RemainingCapacity < IndividualCapacity*CapacityCheck.Capacity && PAActiveSlot.Active == 1 {
		*CapacityFlag = false
	} else {
		*CapacityFlag = true
	}
	return nil
}

func (a *BookingData) GetPAIDCapacity(CapacityCheck structs.CheckCapacity, paidCapacity *[]structs.PAIDCapacity) error {

	var result []structs.PAIDCapacity

	dayOfWeek := int(CapacityCheck.BookingTime.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}

	dateBookingStr := CapacityCheck.BookingTime.Format(formatYMD)

	if CapacityCheck.CapacityType == 1 { //full court
		a.Db.
			Table("pa_active_slots").
			Select("distinct pa_active_slots.`remaining_capacity`, pa_active_slots.`pa_id`").
			Joins("INNER JOIN play_arena_capacity_slots pacs ON pacs.`pa_id` = pa_active_slots.`pa_id` AND pacs.`fs_id` = pa_active_slots.`fs_id` AND pacs.`slot_id` = pa_active_slots.`slot_id` and pacs.`day_of_week` = ?", dayOfWeek).
			Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`slot_id` = ? and pa_active_slots.`active` = ? and DATE(pa_active_slots.`date`) = ?", CapacityCheck.FSID, CapacityCheck.SlotID, 1, dateBookingStr).
			Where("pa_active_slots.`remaining_capacity` >= (pacs.`capacity_individual`/pacs.`capacity_group`)").
			Scan(&result)
	} else { //shared court
		a.Db.
			Table("pa_active_slots").
			Select("distinct pa_active_slots.`remaining_capacity`, pa_active_slots.`pa_id`").
			Joins("INNER JOIN play_arena_capacity_slots pacs ON pacs.`pa_id` = pa_active_slots.`pa_id` AND pacs.`fs_id` = pa_active_slots.`fs_id` AND pacs.`slot_id` = pa_active_slots.`slot_id` and pacs.`day_of_week` = ?", dayOfWeek).
			Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`slot_id` = ? and pa_active_slots.`active` = ? and DATE(pa_active_slots.`date`) = ?", CapacityCheck.FSID, CapacityCheck.SlotID, 1, dateBookingStr).
			Where("pa_active_slots.`remaining_capacity` > 0").
			Scan(&result)
	}

	var BookingCapacity = CapacityCheck.Capacity * CapacityCheck.IndividualCapacity

	for _, el := range result {

		if BookingCapacity == 0 {
			break
		}

		if (BookingCapacity >= el.RemainingCapacity) && (BookingCapacity > 0) {
			*paidCapacity = append(*paidCapacity, el)
			BookingCapacity = BookingCapacity - el.RemainingCapacity
		} else if (BookingCapacity < el.RemainingCapacity) && (BookingCapacity > 0) {
			*paidCapacity = append(*paidCapacity, structs.PAIDCapacity{
				RemainingCapacity: BookingCapacity,
				PAID:              el.PAID,
			})
			BookingCapacity = 0
		}
	}

	fmt.Println("paid cap--------", paidCapacity)

	return nil
}

func (a *BookingData) GetRemainingCapacityWithoutPAID(CapacityCheck structs.CheckCapacity, CapacityFlag *bool) error {
	type RemainingCapacity struct {
		RemainingCapacity int32
	}

	var capacity RemainingCapacity
	dayOfWeek := int(CapacityCheck.BookingTime.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}

	dateBookingStr := CapacityCheck.BookingTime.Format(formatYMD)
	prevDateBookingStr := CapacityCheck.PrevBooking.BookingTime.Format(formatYMD) //only used in booking modification

	if CapacityCheck.CapacityType == 1 { //full court
		response := a.Db.Table("pa_active_slots").
			Select("sum(if(pa_active_slots.`remaining_capacity` >= (pacs.`capacity_individual`/pacs.`capacity_group`), FLOOR(pa_active_slots.`remaining_capacity`/(pacs.`capacity_individual`/pacs.`capacity_group`)), 0)) as `remaining_capacity`").
			Joins("INNER JOIN play_arena_capacity_slots pacs ON pacs.`fs_id` = pa_active_slots.`fs_id` AND pacs.`slot_id` = pa_active_slots.`slot_id` and pacs.`pa_id` = pa_active_slots.`pa_id` and pacs.`day_of_week` = ?", dayOfWeek).
			Where("DATE(pa_active_slots.`date`) = ?", dateBookingStr).
			Where("pa_active_slots.`fs_id` = ?", CapacityCheck.FSID).
			Where("pa_active_slots.`slot_id` = ?", CapacityCheck.SlotID).
			Where("pa_active_slots.`active` = ?", 1).
			Scan(&capacity)

		if err := response.Error; err != nil {
			fmt.Println(err)
			return err
		}

		fmt.Println("Court Capacity Remaining ------- ", capacity)
		fmt.Println("Cancel Cap -------------- ", CapacityCheck.PrevBooking.Capacity)

		var actualCapacity int32
		if CapacityCheck.FSID == CapacityCheck.PrevBooking.FSID && CapacityCheck.SlotID == CapacityCheck.PrevBooking.SlotID && dateBookingStr == prevDateBookingStr {
			actualCapacity = capacity.RemainingCapacity + CapacityCheck.PrevBooking.Capacity
			fmt.Println("actual Cap added with cancel cap as same fsid, slotid and date ======================================")
		} else {
			actualCapacity = capacity.RemainingCapacity
		}

		if actualCapacity < CapacityCheck.Capacity { //IndividualCapacity*CapacityCheck.Capacity
			*CapacityFlag = false
		} else {
			*CapacityFlag = true
		}
	} else {

		a.Db.
			Table("pa_active_slots").
			Select("sum(pa_active_slots.`remaining_capacity`) as `remaining_capacity`").
			Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`slot_id` = ? and pa_active_slots.`active` = ? and DATE(pa_active_slots.`date`) = ?", CapacityCheck.FSID, CapacityCheck.SlotID, 1, dateBookingStr).
			Scan(&capacity)

		fmt.Println("Players Capacity Remaining ------- ", capacity)
		fmt.Println("Cancel Cap -------------- ", CapacityCheck.PrevBooking.Capacity)

		var actualCapacity int32
		if CapacityCheck.FSID == CapacityCheck.PrevBooking.FSID && CapacityCheck.SlotID == CapacityCheck.PrevBooking.SlotID && dateBookingStr == prevDateBookingStr {
			actualCapacity = capacity.RemainingCapacity + CapacityCheck.PrevBooking.Capacity
			fmt.Println("actual Cap added with cancel cap as same fsid, slotid and date ======================================")
		} else {
			actualCapacity = capacity.RemainingCapacity
		}

		var IndividualCapacity int32
		a.GetIndividualCapacity(CapacityCheck, &IndividualCapacity)

		if actualCapacity < IndividualCapacity*CapacityCheck.Capacity {
			*CapacityFlag = false
		} else {
			*CapacityFlag = true
		}
	}

	return nil
}

func (a *BookingData) GetIndividualCapacity(CapacityCheck structs.CheckCapacity, IndividualCapacity *int32) error {

	*IndividualCapacity = CapacityCheck.CapacityType * 2

	dayOfWeek := int(CapacityCheck.BookingTime.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}

	type Capacity struct {
		CapacityIndividual int32
		CapacityGroup      int32
	}

	var CapacityDetails Capacity

	if CapacityCheck.PAID > 0 {
		response := a.Db.Table("play_arena_capacity_slots").
			Select("play_arena_capacity_slots.`capacity_individual`, play_arena_capacity_slots.`capacity_group`").
			Where("play_arena_capacity_slots.`fs_id` = ? and play_arena_capacity_slots.`pa_id` = ? and play_arena_capacity_slots.`slot_id` = ? and play_arena_capacity_slots.`day_of_week` = ?", CapacityCheck.FSID, CapacityCheck.PAID, CapacityCheck.SlotID, dayOfWeek).
			Scan(&CapacityDetails)
		if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
			fmt.Println(err)
			return err
		}
	} else {
		response := a.Db.Table("facility_sport_capacity_slots").
			Select("facility_sport_capacity_slots.`capacity_individual`, facility_sport_capacity_slots.`capacity_group`").
			Where("facility_sport_capacity_slots.`fs_id` = ? and facility_sport_capacity_slots.`slot_id` = ? and facility_sport_capacity_slots.`day_of_week` = ?", CapacityCheck.FSID, CapacityCheck.SlotID, dayOfWeek).
			Scan(&CapacityDetails)
		if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
			fmt.Println(err)
			return err
		}
	}

	if CapacityCheck.CapacityType == 2 {
		*IndividualCapacity = 1
	} else if CapacityCheck.CapacityType == 1 && CapacityDetails.CapacityGroup != 0 && CapacityDetails.CapacityIndividual != 0 {
		*IndividualCapacity = CapacityDetails.CapacityIndividual / CapacityDetails.CapacityGroup
	} else {
		*IndividualCapacity = 1
	}

	return nil
}

func (a *BookingData) ActiveSlotsGet(slotCondition *models.FSActiveSlot, activeSlots *[]models.FSActiveSlot) error {
	response := a.Db.Where(&slotCondition).Find(&activeSlots)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	fmt.Println(activeSlots)
	return nil
}

func (a *BookingData) ActiveSlotsCreate(activeSlot models.FSActiveSlot) error {

	var paSumVal structs.PaSumValues

	selectFs := a.Db.Table("pa_active_slots pas").
		Select("SUM(pas.`capacity`) as `capacity`, SUM(pas.`remaining_capacity`) as `remaining_capacity`, SUM(pas.`active`) as `active`").
		Where("pas.fs_id = ? and DATE(pas.date) = ? and pas.slot_id = ? AND pas.`active` = 1", activeSlot.FSID, activeSlot.Date.Format(formatYMD), activeSlot.SlotID).Scan(&paSumVal)

	if err := selectFs.Error; err != nil {
		fmt.Println("Select pa_Active sum values error!")
		return err
	}

	if paSumVal.Active > 1 {
		paSumVal.Active = 1
	}

	var dummyActiveSlot models.FSActiveSlot

	errIfExists := a.Db.Table("fs_active_slots").Where("fs_id = ? AND slot_id = ? AND DATE(date) = ?", activeSlot.FSID, activeSlot.SlotID, activeSlot.Date.Format(formatYMD)).Scan(&dummyActiveSlot).Error

	fmt.Println("FS already exist error -- ", errIfExists)

	if errIfExists != nil && gorm.IsRecordNotFoundError(errIfExists) {

		fmt.Println("creating new fs active slots")
		activeSlot.Capacity = paSumVal.Capacity
		activeSlot.RemainingCapacity = paSumVal.RemainingCapacity
		activeSlot.Active = paSumVal.Active

		if err := a.Db.Create(&activeSlot).Error; err != nil {
			fmt.Println(err)
			return err
		}
	} else {

		erir := a.Db.Table("fs_active_slots").
			Where("fs_id = ? AND slot_id = ? AND DATE(date) = ?", activeSlot.FSID, activeSlot.SlotID, activeSlot.Date.Format(formatYMD)).
			Updates(map[string]interface{}{"capacity": paSumVal.Capacity, "remaining_capacity": paSumVal.RemainingCapacity, "active": paSumVal.Active})

		if erir.Error != nil {
			fmt.Println(erir)
			return erir.Error
		}

		fmt.Println("Fs Active slot already existed so updated")
	}

	slot_datetime, _ := ptypes.TimestampProto(activeSlot.SlotDatetime)
	if slot_datetime.Seconds > 0 {
		if err := a.SetMaximumBookingCapacityByFsIdAndSlotDateTime(activeSlot.FSID, slot_datetime.Seconds, paSumVal.Capacity); err != nil {
			log.Printf("GetMaximumBookingCapacityByFsIdAndSlotDateTime, error in caching maximum booking capacity for fs_id: %d, slot_datetime: %d: %v", activeSlot.FSID, slot_datetime, err)
			return err
		}
	}

	return nil
}

func (a *BookingData) ActiveSlotsGetLastDate(fsid int, dateMax *time.Time) error {
	fmt.Println("getting current maxDate")
	var activeSlot models.FSActiveSlot
	//type MAXDate struct {
	//        Date time.Time
	//}
	//var currdate MAXDate
	response := a.Db.Where("fs_id =?", fsid).Order("Date desc").Take(&activeSlot) //TODO try to substitute defined fuxtions in place of query
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err //TODO
	}
	*dateMax = (activeSlot.Date)
	fmt.Println(dateMax)
	return nil
}

func (a *BookingData) GetSlotCapacityByDate(conditions models.FacilitySportCapacitySlot, slotCapacity *[]models.FacilitySportCapacitySlot) error {

	fmt.Println("getting slots by date")
	response := a.Db.Select("facility_sport_capacity_slots.* , slots.Timing as timing ").Where(conditions).
		Joins("Inner join slots on slots.slot_id=facility_sport_capacity_slots.slot_id").
		Find(&slotCapacity)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) ArenaActiveSlotsGet(slotCondition *models.PAActiveSlot, activeSlots *[]models.PAActiveSlot) error {
	response := a.Db.Where(&slotCondition).Find(&activeSlots)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	fmt.Println(activeSlots)
	return nil
}

func (a *BookingData) ArenaActiveSlotsCreate(activeSlot models.PAActiveSlot) error {

	var blockedSlots []models.SportsBlockedInventory
	blockedCond := &models.SportsBlockedInventory{
		FSID:   activeSlot.FSID,
		PAID:   activeSlot.PAID,
		Date:   activeSlot.Date,
		SlotID: activeSlot.SlotID,
		PacsID: activeSlot.PACSID,
	}
	errBlocked := a.GetBlockedInventory(blockedCond, &blockedSlots)

	if errBlocked != nil || len(blockedSlots) == 0 {

		var dummyActiveSlot models.PAActiveSlot

		errIfExists := a.Db.Table("pa_active_slots").Where("pa_id = ? AND slot_id = ? AND DATE(date) = ? AND active = 1", activeSlot.PAID, activeSlot.SlotID, activeSlot.Date.Format(formatYMD)).Scan(&dummyActiveSlot).Error

		if errIfExists != nil {

			if gorm.IsRecordNotFoundError(errIfExists) {

				log.Println("creating new pa active slots")

				if err := a.Db.Create(&activeSlot).Error; err != nil {
					log.Println(err)
					return err
				}
			}
		} else {
			log.Println("Pa Active slot already exists")
		}
	}

	return nil
}

func (a *BookingData) ArenaActiveSlotsGetLastDate(paid int, dateMax *time.Time) error {
	fmt.Println("getting current maxDate")
	var activeSlot models.PAActiveSlot
	response := a.Db.Where("pa_id =?", paid).Order("Date desc").Take(&activeSlot) //TODO use PLUCK and try to substitute defined fuxtions in place of query
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err //TODO
	}
	*dateMax = activeSlot.Date
	fmt.Println(dateMax)
	return nil
}

func (a *BookingData) BookingSlotsGetFS(activeSlotFsCondition *models.FSActiveSlot, activeSlotFs *[]models.FSActiveSlot) error {

	dayOfWeek := int(activeSlotFsCondition.Date.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}
	dateBookingStr := activeSlotFsCondition.Date.Format(formatYMD)

	if activeSlotFsCondition.FSID > 0 && activeSlotFsCondition.SlotID > 0 {
		response := a.Db.Select("fs_active_slots.*, s.*, FLOOR(fscs.`capacity_individual`/fscs.`capacity_group`) as `spots_in_play_arena`").
			Joins("INNER JOIN slots s ON s.`slot_id` = fs_active_slots.`slot_id`").
			Joins("INNER JOIN facility_sport_capacity_slots fscs ON fscs.`slot_id` = fs_active_slots.`slot_id` and fscs.`fs_id` = fs_active_slots.`fs_id`").
			Where("DATE(fs_active_slots.`date`) = ?", dateBookingStr).
			Where("fs_active_slots.`fs_id` = ?", activeSlotFsCondition.FSID).
			Where("fs_active_slots.`slot_id` = ?", activeSlotFsCondition.SlotID).
			Where("fscs.`day_of_week` = ?", dayOfWeek).
			Find(&activeSlotFs)

		if err := response.Error; err != nil {
			fmt.Println(err)
			return err
		}
	} else if activeSlotFsCondition.FSID > 0 {
		response := a.Db.Select("fs_active_slots.*, s.*, FLOOR(fscs.`capacity_individual`/fscs.`capacity_group`) as `spots_in_play_arena`").
			Joins("INNER JOIN slots s ON s.`slot_id` = fs_active_slots.`slot_id`").
			Joins("INNER JOIN facility_sport_capacity_slots fscs ON fscs.`slot_id` = fs_active_slots.`slot_id` and fscs.`fs_id` = fs_active_slots.`fs_id`").
			Where("DATE(fs_active_slots.`date`) = ?", dateBookingStr).
			Where("fs_active_slots.`fs_id` = ?", activeSlotFsCondition.FSID).
			Where("fscs.`day_of_week` = ?", dayOfWeek).
			Find(&activeSlotFs)

		if err := response.Error; err != nil {
			fmt.Println(err)
			return err
		}
	} else if activeSlotFsCondition.SlotID > 0 {
		// fmt.Println("from Slot id fetch")
		response := a.Db.Select("fs_active_slots.*, s.*, FLOOR(fscs.`capacity_individual`/fscs.`capacity_group`) as `spots_in_play_arena`").
			Joins("INNER JOIN slots s ON s.`slot_id` = fs_active_slots.`slot_id`").
			Joins("INNER JOIN facility_sport_capacity_slots fscs ON fscs.`slot_id` = fs_active_slots.`slot_id` and fscs.`fs_id` = fs_active_slots.`fs_id`").
			Where("DATE(fs_active_slots.`date`) = ?", dateBookingStr).
			Where("fs_active_slots.`slot_id` = ?", activeSlotFsCondition.SlotID).
			Where("fscs.`day_of_week` = ?", dayOfWeek).
			Find(&activeSlotFs)

		if err := response.Error; err != nil {
			fmt.Println(err)
			return err
		}
	}

	fmt.Println(activeSlotFs)
	return nil
}

func (a *BookingData) AllBookingSlotsGetFS(ctx context.Context, activeSlotFsCondition *structs.AllFsActiveSlots, activeSlotFs *[]models.FSActiveSlot) error {
	log.Println("reqqqq---- ", activeSlotFsCondition)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	dayOfWeek := int(activeSlotFsCondition.Date.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}
	dateBookingStr := activeSlotFsCondition.Date.Format(formatYMD)

	// fmt.Println("from Slot id fetch")
	response := DB.Select("fs_active_slots.*, s.*, FLOOR(fscs.`capacity_individual`/fscs.`capacity_group`) as `spots_in_play_arena`").
		Joins("INNER JOIN slots s ON s.`slot_id` = fs_active_slots.`slot_id`").
		Joins("INNER JOIN facility_sport_capacity_slots fscs ON fscs.`slot_id` = fs_active_slots.`slot_id` and fscs.`fs_id` = fs_active_slots.`fs_id`").
		Where("DATE(fs_active_slots.`date`) = ? AND fs_active_slots.`slot_datetime` > NOW() - INTERVAL 15 MINUTE", dateBookingStr).
		Where("fs_active_slots.`slot_id` IN (?)", activeSlotFsCondition.SlotIDArr).
		Where("fscs.`day_of_week` = ?", dayOfWeek).
		Where("fs_active_slots.active = 1").
		Find(&activeSlotFs)

	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}

	return nil
}
func (a *BookingData) AllSwimmingSlotsGetFS(ctx context.Context, fsids []int32, activeSlotFsCondition *structs.AllFsActiveSlots, activeSlotFs *[]models.FSActiveSlot) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	dayOfWeek := int32(activeSlotFsCondition.Date.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}
	dateBookingStr := activeSlotFsCondition.Date.Format(formatYMD)
	var tempActiveSlotsFs []models.FSActiveSlot
	response := DB.Table("swimming_location_slot_mappings slsm").
		Select("fsm.fs_id,ss.sports_slot_id as slot_id, s.timing, slsm.capacity_flexi ,slsdm.`capacity_flexi` as `slsdm_capacity_flexi`,sl.`schedule_type`  ").
		Joins("INNER JOIN `swimming_slots` ss ON slsm.`slot_id` = ss.`slot_id` AND ss.`deleted_flag` = 0").
		Joins("INNER JOIN `slots` s on ss.`sports_slot_id` = s.`slot_id`").
		Joins("INNER JOIN `swimming_locations` sl ON sl.`swimming_location_id` = slsm.`swimming_location_id` AND sl.`active_status` = 1").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`facility_id` = sl.`facility_id`	AND fsm.`operation_status` = 1 AND fsm.`fs_id` IN (?)", fsids).
		Joins("INNER JOIN `swimming_location_slot_day_mappings` slsdm ON slsdm.`swimming_location_slot_id` = slsm.`swimming_location_slot_id` AND slsdm.`day_number` = ? AND  slsdm.`visible`=?", dayOfWeek, 1).
		Where("  slsm.`slot_type` = 1 AND slsm.`is_active` = 1").
		Group("slsm.`swimming_location_slot_id`").
		Find(&tempActiveSlotsFs)

	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}
	type BookedSlotDetails struct {
		FsId         int32
		SlotId       int32
		BookingCount int32
	}
	var bookedSlots []BookedSlotDetails
	query := DB.Table("user_seals_bookings usb").
		Select("fsm.fs_id,ss.sports_slot_id as slot_id , count(*) as booking_count  ").
		Joins("INNER JOIN `swimming_slots` ss ON usb.`slot_id` = ss.`slot_id` ").
		Joins("INNER JOIN `swimming_locations` sl ON sl.`swimming_location_id` = usb.`swimming_location_id`	").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`facility_id` = sl.`facility_id` AND fsm.`sport_id`= 3	AND fsm.`operation_status` = 1").
		Where("DATE(usb.`booking_date`) = ? AND usb.`rescheduled_flag` = 0 AND usb.`cancelled_flag` = 0 AND usb.`deleted_flag` = 0", dateBookingStr).
		Group("usb.`swimming_location_id`, usb.`slot_id`").
		Find(&bookedSlots)
	if err := query.Error; err != nil {
		log.Println(err)
		return err
	}
	bookingCountMap := make(map[int32]map[int32]int)

	for _, ele := range bookedSlots {
		bookingMap := make(map[int32]int)
		if _, ok := bookingCountMap[ele.FsId]; ok == false {
			bookingMap = make(map[int32]int)
		} else {
			bookingMap = bookingCountMap[ele.FsId]
		}
		bookingMap[ele.SlotId] = int(ele.BookingCount)
		bookingCountMap[ele.FsId] = bookingMap
	}
	disableSlotsMap := make(map[int32]map[int32]bool)

	var disableSlots []BookedSlotDetails
	query = DB.Table("seals_disabled_slots sdb").
		Select("fsm.fs_id,ss.sports_slot_id as slot_id  ").
		Joins("INNER JOIN `swimming_slots` ss ON sdb.`slot_id` = ss.`slot_id` ").
		Joins("INNER JOIN `swimming_locations` sl ON sl.`swimming_location_id` = sdb.`swimming_location_id`	").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`facility_id` = sl.`facility_id` AND fsm.`sport_id`= 3	AND fsm.`operation_status` = 1").
		Where("DATE(sdb.`disable_date`) = ? AND sdb.`deleted_flag` = 0", dateBookingStr).
		Group("sdb.`swimming_location_id`, sdb.`slot_id`").
		Find(&disableSlots)

	for _, ele := range disableSlots {
		disableMap := make(map[int32]bool)
		if _, ok := disableSlotsMap[ele.FsId]; ok == false {
			disableMap = make(map[int32]bool)
		} else {
			disableMap = disableSlotsMap[ele.FsId]
		}
		disableMap[ele.SlotId] = true
		disableSlotsMap[ele.FsId] = disableMap
	}
	for _, ele := range tempActiveSlotsFs {
		if isOpen := checkIfSwimmingSoltsOpenForScheuleType(ctx, ele.ScheduleType, dayOfWeek); isOpen == false {
			continue
		}
		if _, ok := disableSlotsMap[ele.FSID]; ok == true && disableSlotsMap[ele.FSID][ele.SlotID] == true {
			continue
		}

		capacity := ele.CapacityFlexi
		bookingCount := 0

		if ele.SlsdmCapacityFlexi != 0 {
			capacity = ele.SlsdmCapacityFlexi
		}
		if _, ok := bookingCountMap[ele.FSID]; ok == true {
			bookingCount = bookingCountMap[ele.FSID][ele.SlotID]
		}
		ele.Capacity = capacity
		ele.CoachingFlag = true
		ele.CoachingType = int32(SwimmingCoachingType)
		ele.RemainingCapacity = capacity - int32(bookingCount)
		ele.SpotsInPlayArena = capacity
		ele.Active = 1
		*activeSlotFs = append(*activeSlotFs, ele)
	}

	return nil
}

func (a *BookingData) GetLiveSessionBooking(condition *models.OnlineSessions, onlineSessions *[]models.OnlineSessions) error {

	dateBookingStr := condition.Date.Format(formatYMD)
	response := a.Db.Table("online_sessions").
		Select("online_sessions.*, sv.*, s.*, sp.`provider_real_name`, sp.`dp_url`, sp.`title` as `trainer_title`, sp.`about_trainer`, online_sessions.`capacity` - CASE WHEN SUM( ab.`booking_capacity` ) > 0 THEN sum(ab.`booking_capacity`) ELSE 0 END as remaining_capacity").
		Joins("INNER JOIN slots s ON  s.slot_id = online_sessions.slot_id").
		Joins("INNER JOIN session_videos sv ON  sv.session_video_id = online_sessions.session_video_id").
		Joins("LEFT JOIN service_providers sp ON sp.`provider_id` = sv.`provider_id`").
		Joins("LEFT JOIN all_bookings ab on ab.`online_session_id` = online_sessions.`online_session_id` AND ab.`booking_cancelled` = ?", 0).
		Where("DATE(online_sessions.`date`) = ? AND online_sessions.`deleted_flag` = 0 ", dateBookingStr).
		Group("online_sessions.`online_session_id`").
		Find(&onlineSessions)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetArenaSlotCapacityByDate(conditions models.PlayArenaCapacitySlot, slotCapacity *[]models.PlayArenaCapacitySlot) error {

	response := a.Db.Select("play_arena_capacity_slots.* , slots.Timing as timing ").
		Where(conditions).
		Joins("Inner join slots on slots.slot_id=play_arena_capacity_slots.slot_id")
	if !conditions.IncludeAcademySession {
		response = response.Where("play_arena_capacity_slots.`product_arena_category_id` <> ?", PRODUCT_ARENA_CATEGORGY_ID_ACADEMY_SESSION)
	}
	response = response.Group("play_arena_capacity_slots.`pacs_id`").Find(&slotCapacity)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetBlockedInventoriesByDate(ctx context.Context, conditions models.SportsBlockedInventory, blockedInventories *[]models.SportsBlockedInventory) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	dateBookingStr := conditions.Date.Format(formatYMD)

	response := DB.Table("sports_blocked_inventories").
		Select("sports_blocked_inventories.*").
		Where("sports_blocked_inventories.`date` = ?", dateBookingStr).
		Where(conditions).
		Find(&blockedInventories)

	if err := response.Error; err != nil {
		log.Println("func:GetBlockedInventoriesByDate, error in fetching blocked inventories, err:%v", err)
		return err
	}

	return nil
}

func (a *BookingData) BookingSlotsGetPA(activeSlotPaCondition *models.PAActiveSlot, activeSlotPa *[]models.PAActiveSlot) error {

	dayOfWeek := int(activeSlotPaCondition.Date.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}

	dateBookingStr := activeSlotPaCondition.Date.Format(formatYMD)

	response := a.Db.Select("sum(if(pa_active_slots.`remaining_capacity` >= (pacs.`capacity_individual`/pacs.`capacity_group`), FLOOR(pa_active_slots.`remaining_capacity`/(pacs.`capacity_individual`/pacs.`capacity_group`)), 0)) as `remaining_capacity`, pa_active_slots.`active`, s.*, FLOOR(pacs.`capacity_individual`/pacs.`capacity_group`) as `spots_in_play_arena`").
		Joins("INNER JOIN slots s ON s.`slot_id` = pa_active_slots.`slot_id`").
		Joins("INNER JOIN play_arena_capacity_slots pacs ON pacs.`pa_id` = pa_active_slots.`pa_id` AND pacs.`fs_id` = pa_active_slots.`fs_id` AND pacs.`slot_id` = pa_active_slots.`slot_id`").
		Where("pacs.`product_arena_category_id` <> ?", PRODUCT_ARENA_CATEGORGY_ID_ACADEMY_SESSION).
		Where("DATE(pa_active_slots.`date`) = ?", dateBookingStr).
		Where("pa_active_slots.`fs_id` = ?", activeSlotPaCondition.FSID).
		Where("pacs.`day_of_week` = ?", dayOfWeek).
		Group("pa_active_slots.`slot_id`").
		Find(&activeSlotPa)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	fmt.Println(activeSlotPa)
	return nil
}

func (a *BookingData) DeleteAllActiveSlots(date_del time.Time) error {
	fmt.Println("deleting all inactive slots")
	response := a.Db.Delete(models.FSActiveSlot{}, "Date < ?", date_del.Format(formatYMD))
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) DeleteAllActiveArenaSlots(date_del time.Time) error {
	fmt.Println("deleting all inactive arena slots")
	response := a.Db.Delete(models.PAActiveSlot{}, "Date < ?", date_del.Format(formatYMD))
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetFSIDForByPAId(paid int) int {
	var play_arena models.PlayArena
	response := a.Db.Where("pa_id =?", paid).Take(&play_arena) //TODO try to substitute defined fuxtions in place of query
	if err := response.Error; err != nil {
		fmt.Println(err)
		return -1
	}
	fsid := play_arena.FSID
	return int(fsid)
}

func (a *BookingData) SendNotificationOnPublish(ndata *structs.FcmNotification) error {

	bodyBytes, _ := json.Marshal(ndata)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "0",
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(notificationTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) RemainingSessionsForSubscriptionGet(subscriptionId int32, remainingSessions *float32) error {

	type RemainingSessionsData struct {
		RemainingSessions float32 `json:"remaining_sessions"`
	}

	var remainingSessionsData RemainingSessionsData

	//do no remove this
	//sum(case when ab.booking_type = 1 then ((pacs.capacity_individual/pacs.capacity_group) * ab.booking_capacity) when ab.booking_type = 2 then ab.booking_capacity else 0 end))

	response := a.Db.Table("subscriptions").
		Select("subscriptions.session_count - (case when ab.`booking_type` > 0 then sum(ab.`charge_levied`) else 0 end) as `remaining_sessions`").
		Joins("LEFT JOIN all_bookings ab ON subscriptions.subscription_id = ab.subscription_id and ab.`booking_rescheduled` = 0").
		Joins("LEFT JOIN play_arena_capacity_slots pacs on pacs.fs_id = ab.fs_id and ab.slot_id = pacs.slot_id and pacs.pa_id = ab.pa_id and pacs.day_of_week = (case when (DAYOFWEEK(DATE(ab.booking_time)) - 1) = 0 then 7 else (DAYOFWEEK(DATE(ab.booking_time)) - 1) end)").
		Where("subscriptions.subscription_id = ?", subscriptionId).
		Scan(&remainingSessionsData)

	if err := response.Error; err != nil {
		fmt.Println("Could not calculate remaining sessions!")
		return err
	}

	*remainingSessions = remainingSessionsData.RemainingSessions
	fmt.Println("Thissss ---------", remainingSessionsData)

	fmt.Println("RemainingSessions Book--- ", remainingSessionsData.RemainingSessions)

	return nil
}

func (a *BookingData) AvailedSessionsForSubscriptionGet(subscriptionId int32, availedSessions *float32) error {

	type AvailedSessionsData struct {
		AvailedSessions float32 `json:"availed_sessions"`
	}
	var availedSessionsData AvailedSessionsData

	response := a.Db.Table("all_bookings ab").
		Select("count(booking_id) as `availed_sessions`").
		Where("ab.`booking_rescheduled` = 0 and ab.booking_cancelled=0 and ab.subscription_id = ?", subscriptionId).
		Scan(&availedSessionsData)

	if err := response.Error; err != nil {
		fmt.Println("Could not calculate availed sessions!")
		return err
	}

	*availedSessions = availedSessionsData.AvailedSessions
	log.Println("AvailedSessions Sub--- ", availedSessionsData.AvailedSessions)

	return nil
}

func (a *BookingData) GetBookingStartAndEndDate(req *pb.BookingDatesGet, bookingStartDate *time.Time, bookingEndDate *time.Time) error {

	type BookingDates struct {
		BookingStartDate time.Time
		BookingEndDate   time.Time
	}

	var bookingDatesData BookingDates

	var active_slots_table string
	if req.BookingType == 1 {
		active_slots_table = "pa_active_slots"
	} else {
		active_slots_table = "fs_active_slots"
	}

	response := a.Db.Table(active_slots_table+" as active_slots").
		Select("min(active_slots.`date`) as `booking_start_date`, max(active_slots.`date`) as `booking_end_date`").
		Where("active_slots.fs_id = ?", req.FsId).
		Find(&bookingDatesData)

	if err := response.Error; err != nil {
		fmt.Println("Error in fetching booking start and end date")
		return err
	}

	*bookingStartDate = bookingDatesData.BookingStartDate
	*bookingEndDate = bookingDatesData.BookingEndDate

	return nil
}

func (a *BookingData) CancelBookingV2(ctx context.Context, bookingData *structs.CancelBookingCondition, cancelledBy int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	tx := DB.Begin()

	defer func() {
		if r := recover(); r != nil {
			log.Println("CancelBookingV2: recover due to", r)
			tx.Rollback()
		}
	}()

	log.Println("CancelBookingV2: starting cancel booking transaction: for bookingData: ", bookingData)
	pacsIDsTrialCountUpdatedMap := make(map[int32]bool)

	updateResponse := tx.Table("all_bookings").
		Set("gorm:query_option", "FOR UPDATE").
		Where("booking_cancelled = ? AND booking_id IN (?)", 0, bookingData.BookingIds).
		Updates(map[string]interface{}{"booking_cancelled": 1, "cancel_by": cancelledBy, "cancel_time": time.Now()})
	if err := updateResponse.Error; err != nil {
		log.Printf("error in cancelling bookings for booking ids: %v, error: %v", bookingData.BookingIds, updateResponse.Error)
		tx.Rollback()
		return err
	}

	if updateResponse.RowsAffected == 0 {
		log.Printf("Booking already cancelled for booking ids: %v, hence breaking the capacity updation flow for PACSIDs: %v, fs id: %d, slot id: %d", bookingData.BookingIds, bookingData.PACSIDs, bookingData.FSID, bookingData.SlotID)
		if err := tx.Commit().Error; err != nil {
			log.Println("error in committing transaction", err)
			tx.Rollback()
			return err
		}
		return nil
	}

	responseFs := tx.Table("fs_active_slots").
		Where("fs_active_slots.`fs_id` = ? and fs_active_slots.`slot_id` = ? and DATE(fs_active_slots.`date`) = ?", bookingData.FSID, bookingData.SlotID, bookingData.BookingTime.Format(formatYMD)).
		UpdateColumn("fs_active_slots.remaining_capacity", gorm.Expr("fs_active_slots.remaining_capacity + ?", bookingData.BookingCapacity))

	if err := responseFs.Error; err != nil {
		log.Printf("error in updating fs active slots, error: %v", err)
		tx.Rollback()
		return err
	}
	for _, pacsID := range bookingData.PACSIDs {
		responsePa := tx.Table("pa_active_slots").
			Where("pa_active_slots.`pacs_id` = ? and DATE(pa_active_slots.`date`) = ?", pacsID, bookingData.BookingTime.Format(formatYMD))
		if _, ok := pacsIDsTrialCountUpdatedMap[pacsID]; !ok {
			if trialCount, exists := bookingData.PACSIDs_Trial_Count_Map[pacsID]; exists {
				responsePa = responsePa.Updates(map[string]interface{}{"pa_active_slots.remaining_capacity": gorm.Expr("pa_active_slots.remaining_capacity + ?", 1), "pa_active_slots.trial_count": gorm.Expr("pa_active_slots.trial_count - ?", trialCount)})
				pacsIDsTrialCountUpdatedMap[pacsID] = true
			} else {
				responsePa = responsePa.UpdateColumn("pa_active_slots.remaining_capacity", gorm.Expr("pa_active_slots.remaining_capacity + ?", 1))
				pacsIDsTrialCountUpdatedMap[pacsID] = true
			}
		} else {
			responsePa = responsePa.UpdateColumn("pa_active_slots.remaining_capacity", gorm.Expr("pa_active_slots.remaining_capacity + ?", 1))
			pacsIDsTrialCountUpdatedMap[pacsID] = true
		}

		if err := responsePa.Error; err != nil {
			log.Printf("error in updating pa active slots, error: %v", err)
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		log.Println("error in committing cancel transaction: rolling back: ", err)
		tx.Rollback()
		return err
	}

	return nil
}

func (a *BookingData) CancelBooking(req *pb.CancelBookingRequest, cancelBookingData *pb.CancelBooking) error {

	fmt.Println("Starting Cancel Booking Transaction ----------------")

	tx := a.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	bookingTime := time.Unix(cancelBookingData.BookingTime.Seconds, 0)
	bookingTimeLocal, err := GetLocalDateTime(bookingTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	bookingCancelledAb := false

	//select for update ab and cb
	fmt.Println("brn for cancel --------", req.BookingReferenceNumber)
	var allBookings []models.AllBooking
	responseCancelSelect := tx.Table("all_bookings").
		Set("gorm:query_option", "FOR UPDATE").
		Select("all_bookings.*").
		Where("all_bookings.`booking_reference_number` = ? AND all_bookings.`booking_cancelled` = ?", req.BookingReferenceNumber, 0).
		Find(&allBookings)

	if responseCancelSelect.Error != nil {
		fmt.Println("Error in selecting all booking data for update -- Rolling Back!")
		tx.Rollback()
		return responseCancelSelect.Error
	}

	for _, element := range allBookings {
		if element.BookingCancelled {
			bookingCancelledAb = true
		}

		break
	}

	if !bookingCancelledAb {

		//update all_bookings
		updateResponse := tx.Exec("UPDATE all_bookings SET all_bookings.`booking_cancelled` = ?, all_bookings.`cancel_by` = ?, all_bookings.`charge_levied` = all_bookings.`charge_levied` * ? WHERE all_bookings.`booking_reference_number` = ? AND all_bookings.`booking_cancelled` = ?", 1, req.ActionUserId, req.MultiplicationFactor, req.BookingReferenceNumber, 0)
		if updateResponse.Error != nil {
			fmt.Println("Error in cancelling booking -- all_bookings -- Rolling Back!")
			tx.Rollback()
			return updateResponse.Error
		}

		if cancelBookingData.FsId > 0 && cancelBookingData.PaId > 0 {
			//update fs_active and pa_active slots - booking cap
			responseFs := tx.Table("fs_active_slots").
				Where("fs_active_slots.`fs_id` = ? and fs_active_slots.`slot_id` = ? and DATE(fs_active_slots.`date`) = ?", cancelBookingData.FsId, cancelBookingData.SlotId, bookingTimeLocal.Format(formatYMD)).
				UpdateColumn("fs_active_slots.remaining_capacity", gorm.Expr("fs_active_slots.remaining_capacity + ?", cancelBookingData.BookedCapacity))

			if err := responseFs.Error; err != nil {
				fmt.Println(err)
				fmt.Println("Update fs_active_slots error -- Rolling Back")
				tx.Rollback()
				return err
			}

			fmt.Println("Updated fs_active_slots! added - ", cancelBookingData.BookedCapacity)

			responsePa := tx.Table("pa_active_slots").
				Where("pa_active_slots.`fs_id` = ? and pa_active_slots.`pa_id` = ? and pa_active_slots.`slot_id` = ?  and DATE(pa_active_slots.`date`) = ?", cancelBookingData.FsId, cancelBookingData.PaId, cancelBookingData.SlotId, bookingTimeLocal.Format(formatYMD)).
				UpdateColumn("pa_active_slots.remaining_capacity", gorm.Expr("pa_active_slots.remaining_capacity + ?", cancelBookingData.BookedCapacity))

			if err := responsePa.Error; err != nil {
				fmt.Println(err)
				fmt.Println("Update pa_active_slots error -- Rolling Back")
				tx.Rollback()
				return err
			}

			fmt.Println("Updated pa_active_slots! added - ", cancelBookingData.BookedCapacity)
		}
	} else {
		fmt.Println("Booking already cancelled for - ", req.BookingReferenceNumber)
	}

	fmt.Println("Committing Cancel Booking Transaction ----------------")
	if err := tx.Commit().Error; err != nil {
		fmt.Println("Error in committing txn")
		return err
	}

	return nil
}

func (a *BookingData) SlotGet(ctx context.Context, slotCondition *models.Slot, slot *[]models.Slot) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	response := DB.Where(&slotCondition).Find(&slot)

	if response.Error != nil {
		log.Println(response.Error)
		return response.Error
	}

	return nil
}

// OPTIMIZED: SlotGet with pagination and filtering to prevent memory issues
func (a *BookingData) SlotGetOptimized(ctx context.Context, slotCondition *models.Slot, slot *[]models.Slot, limit int, offset int) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Where(&slotCondition)

	// Add pagination to prevent loading all slots into memory
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	// Add ordering for consistent pagination
	query = query.Order("slot_id ASC")

	response := query.Find(&slot)

	if response.Error != nil {
		log.Printf("Error in SlotGetOptimized: %v", response.Error)
		return response.Error
	}

	log.Printf("SlotGetOptimized: Retrieved %d slots with limit=%d, offset=%d", len(*slot), limit, offset)
	return nil
}

func (a *BookingData) GetFacilityIdAndSportIdByFsId(fsId *int32, fsIds *structs.FacilitySportIds) error {
	response := a.Db.Table("facility_sport_mappings").
		Select("f.facility_id, s.sport_id,s.name as sport_name, f.`facility_type`, f.`city_id`, facility_sport_mappings.`non_operational_days`").
		Joins("INNER JOIN facilities f on f.`facility_id` = facility_sport_mappings.`facility_id`").
		Joins("INNER JOIN sports s on facility_sport_mappings.sport_id= s.sport_id").
		Where("facility_sport_mappings.`fs_id` = ?", fsId).
		Find(&fsIds)

	if err := response.Error; err != nil {
		fmt.Println("Could not fetch facility_id and sport_id")
		return err
	}
	return nil
}

func (a *BookingData) GetFacilitySportNameByFsId(fsId *int32, fsName *structs.FacilitySportName) error {
	response := a.Db.Table("facility_sport_mappings fsm").
		Select("fsm.description").
		Where("fsm.`fs_id` = ?", fsId).
		Find(&fsName)

	if err := response.Error; err != nil {
		fmt.Println("Could not fetch facility_sport name")
		return err
	}
	return nil
}

func (a *BookingData) FacilityGet(facilityCondition *models.Facility, facility *[]models.Facility) error {

	response := a.Db.Select("facilities.*").
		Where(&facilityCondition).
		Find(&facility)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) SportGet(sportCondition *models.Sport, sport *[]models.Sport) error {

	response := a.Db.Select("sports.*").
		Where(&sportCondition).
		Find(&sport)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) GetBookingForSlotAndDate(bookingCond *models.AllBooking, bookingEntries *[]models.AllBooking) error {

	dateBookingStr := bookingCond.BookingDate.Format(formatYMD)

	response := a.Db.Table("all_bookings").
		Select("all_bookings.`booking_id`, all_bookings.`fs_id`").
		Where("all_bookings.slot_id = ? and all_bookings.booking_date = ? and all_bookings.user_id = ?", bookingCond.SlotID, dateBookingStr, bookingCond.UserID).
		Where("all_bookings.booking_cancelled = 0 AND all_bookings.booking_rescheduled = 0").
		Find(&bookingEntries)

	if err := response.Error; err != nil {
		fmt.Println("Error in fetching booking details for date and slot", err)
		fmt.Println("Proceeding without it")
	}
	return nil
}

func (a *BookingData) GetAllBookedSlotsForUser(ctx context.Context, bookingCond *models.AllBooking, bookingEntries *[]models.AllBooking) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	dateBookingStr := bookingCond.BookingDate.Format(formatYMD)

	response := DB.Table("all_bookings").
		Select("all_bookings.`booking_id`, all_bookings.`fs_id`, all_bookings.`slot_id`,all_bookings.`online_session_id`").
		Where(" all_bookings.user_id = ?", bookingCond.UserID).
		Where("all_bookings.booking_cancelled = 0 AND all_bookings.booking_rescheduled = 0")

	if bookingCond.IsReturnAllFutureBookings {
		response = response.Where("DATE(all_bookings.`booking_time`) >=CURDATE()")
	} else {
		response = response.Where("all_bookings.booking_date = ? ", dateBookingStr)
	}
	if bookingCond.IsOnlineSession {
		response = response.Where("all_bookings.`online_session_id` IS NOT NULL OR all_bookings.`online_session_id`!=?", 0)
	} else {
		response = response.Where("all_bookings.`online_session_id` IS NULL OR all_bookings.`online_session_id`=?", 0)
	}

	response = response.Find(&bookingEntries)

	if err := response.Error; err != nil {
		log.Println("Error in fetching booking details for date and slot", err)
		log.Println("Proceeding without it")
	}

	if !is_swimming_considered_as_sports {
		var sealsBookingEntries []models.AllBooking
		response = a.Db.Table("user_seals_bookings usb").
			Select("usb.`booking_id`, fsm.`fs_id`, ss.`sports_slot_id` as slot_id ").
			Joins("INNER JOIN `swimming_slots` ss ON ss.`slot_id` = usb.`slot_id`").
			Joins("INNER JOIN `swimming_locations` sl ON sl.`swimming_location_id` = usb.`swimming_location_id`").
			Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`facility_id` = sl.`facility_id`").
			Where("usb.user_id = ?", bookingCond.UserID).
			Where("usb.cancelled_flag = 0 AND usb.rescheduled_flag = 0")

		if bookingCond.IsReturnAllFutureBookings {
			response = response.Where("DATE(usb.`booking_time`) >=CURDATE()")
		} else {
			response = response.Where("usb.`booking_date` = ? ", dateBookingStr)
		}
		response.Group("usb.`booking_id`").Find(&sealsBookingEntries)
		if err := response.Error; err != nil {
			log.Println("Error in fetching seals booking details for date and slot", err)
			log.Println("Proceeding without it")
		}
		*bookingEntries = append(*bookingEntries, sealsBookingEntries...)
	}
	return nil
}

func (a *BookingData) SendSealsBookingFailureEmail(emData *structs.EmailRequest) error {

	bodyBytes, _ := json.Marshal(emData)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "1",
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(emailTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) SendPartnerFacilityBlockingEmail(emData *structs.EmailRequest) error {
	fmt.Println("sending blocking email ....")
	bodyBytes, _ := json.Marshal(emData)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "1",
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(emailTopic, msg); err != nil {
		fmt.Printf("[pub] partner-facility  blocking email failed: %v", err)
	} else {
		fmt.Println("[pub] partner-facility pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) BookingAndAttendanceDetails(details *structs.BookingAndAttendanceDetails, bookingId int32) error {

	response := a.Db.Table("all_bookings").
		Select("all_bookings.`booking_id`,all_bookings.`booking_reference_number` , all_bookings.`booking_date`, u.`user_id`,u.`name`,u.`email`, psa.`checkin_time`, psa.`checkout_time`  ,fsm.`description` as facility_sport_description , s.`timing` ,s.`start_hour`,s.`start_min`,f.`display_name`, u.`phone`,psa.`medium`, all_bookings.subscription_id").
		Joins("INNER JOIN  `users` u ON u.`user_id` = all_bookings.`user_id`").
		Joins("INNER JOIN  `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
		Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id`").
		Joins("INNER JOIN `slots` s ON s.`slot_id` = all_bookings.`slot_id`").
		Joins("LEFT JOIN  `processed_sports_attendances` psa ON psa.`booking_id` = all_bookings.`booking_id`").
		Where("all_bookings.`booking_id` = ?", bookingId).
		Find(details)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting attendance and booking details of the user..")
		return err
	}
	return nil
}

func (a *BookingData) MessagePublish(phone []string, message string) error { //send SMS
	/*
		used in -
			1) Send booking reminder
	*/

	bodyBytes, _ := json.Marshal(map[string]interface{}{
		"numbers": phone,
		"message": message,
		"sender":  "CULTFT",
	})
	msg := &broker.Message{
		Header: map[string]string{
			"id": fmt.Sprintf("%s", phone),
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(smsTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) SendSportsFeedbackAlertEmail(emData *structs.EmailRequest) error {

	bodyBytes, _ := json.Marshal(emData)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "1",
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(emailTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) SendNotification(ndata *structs.FcmNotification) error {

	bodyBytes, _ := json.Marshal(ndata)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "0",
		},
		Body: bodyBytes,
	}

	fmt.Println(msg)

	if err := a.Broker.Publish(notificationTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) GetProductIdForSubscriptionId(subscriptionId int32, pId *int32) error {

	type PID struct {
		ProductId int32 `json:"product_id"`
	}
	var PIDdata PID

	query := a.Db.Table("subscriptions").
		Select("product_id").
		Where("subscription_id = ?", subscriptionId).
		Find(&PIDdata)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Error in getting product from subs")
		return err
	}

	*pId = PIDdata.ProductId

	return nil
}

func (a *BookingData) GetFSIDForSportAndFacility(sportId int32, facilityId int32, fsid *int32) error {

	type FSIDStruct struct {
		FSID int32 `json:"fs_id"`
	}
	var FSIDdata FSIDStruct

	query := a.Db.Table("facility_sport_mappings").
		Select("fs_id").
		Where("sport_id = ? AND facility_id = ?", sportId, facilityId).
		Find(&FSIDdata)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Error in getting fsid from fsm")
		return err
	}

	*fsid = FSIDdata.FSID

	return nil
}

func (a *BookingData) GetFSIDForSportAndFacilityIds(fSpArr []structs.FacilitySportIds, fSpFsArr *[]structs.FacilitySportIds) error {

	var FSIDdata []structs.FacilitySportIds

	var spArr []int32
	var fArr []int32
	for _, el := range fSpArr {
		spArr = append(spArr, el.SportID)
		fArr = append(fArr, el.FacilityID)
	}

	query := a.Db.Table("facility_sport_mappings").
		Select("facility_id, sport_id, fs_id").
		Where("sport_id IN (?) AND facility_id IN (?)", spArr, fArr).
		Find(&FSIDdata)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Error in getting fsid from fsm arr")
		return err
	}

	*fSpFsArr = FSIDdata

	return nil
}

func (a *BookingData) GetAllFacilities(ctx context.Context, facility *[]models.Facility) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("facilities").
		Select("facilities.*, fr.`region_name`, fsm.`latitude`, fsm.`longitude`").
		Joins("LEFT JOIN facility_regions fr ON fr.`facility_region_id` = facilities.`facility_region_id` AND fr.`deleted_flag` = 0").
		Joins("LEFT JOIN facility_sport_mappings fsm ON fsm.`facility_id` = facilities.`facility_id`").
		Group("facilities.facility_id").
		Find(&facility)

	if err := query.Error; err != nil {
		log.Println("Error in getting all facilities")
		return err
	}

	return nil
}

func (a *BookingData) GetAllSports(ctx context.Context, sport *[]models.Sport) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("sports").
		Select("sports.*").
		Find(&sport)

	if err := query.Error; err != nil {
		log.Println("Error in getting all facilities")
		return err
	}

	return nil
}

func (a *BookingData) GetAllFacilityIds(facilityIds *[]int32) error { //not being used as of now

	type FID struct {
		FacilityID int32 `json:"facility_id"`
	}

	var fidArr []FID

	query := a.Db.Table("facilities").
		Select("facilities.facility_id").
		Find(&fidArr)

	if err := query.Error; err != nil {
		fmt.Println("Error in getting all facilities")
		return err
	}

	var facilityIdArr []int32
	for _, element := range fidArr {
		facilityIdArr = append(facilityIdArr, element.FacilityID)
	}

	*facilityIds = facilityIdArr

	return nil
}

func (a *BookingData) GetAllSportIds(sportIds *[]int32) error { //not being used as of now

	type SID struct {
		SportID int32 `json:"sport_id"`
	}

	var sidArr []SID

	query := a.Db.Table("sports").
		Select("sports.sport_id").
		Where("sports.sport_id != 3").
		Find(&sidArr)

	if err := query.Error; err != nil {
		fmt.Println("Error in getting all sport")
		return err
	}

	var sportIdArr []int32
	for _, element := range sidArr {
		sportIdArr = append(sportIdArr, element.SportID)
	}

	*sportIds = sportIdArr

	return nil
}

func (a *BookingData) GetFacilityIdsAndSportIdsForAllFsIds(ctx context.Context, fsids []int32, fsData *[]structs.FacilitySportIds) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	var fsDataArr []structs.FacilitySportIds
	response := DB.Table("facility_sport_mappings").
		Select("facility_id, sport_id, fs_id").
		Where("facility_sport_mappings.`fs_id` IN (?)", fsids).
		Find(&fsDataArr)

	if err := response.Error; err != nil {
		log.Println("Could not fetch facility_id and sport_id arr")
		return err
	}

	var fsDataRetArr []structs.FacilitySportIds
	for _, ele := range fsDataArr {
		fs := structs.FacilitySportIds{
			FacilityID: ele.FacilityID,
			SportID:    ele.SportID,
			FSID:       ele.FSID,
		}
		fsDataRetArr = append(fsDataRetArr, fs)
	}

	*fsData = fsDataRetArr

	return nil
}

func (a *BookingData) GetLiveSessionBookingsForSubscription(bookingCond *models.AllBooking, booking *[]models.AllBooking) error {
	fmt.Println("Req -- ", bookingCond)
	dateBookingStr := bookingCond.BookingDate.Format(formatYMD)

	query := a.Db.Table("all_bookings").
		Select("all_bookings.*").
		Where("DATE(all_bookings.`booking_date`) = ? and all_bookings.`booking_cancelled` = 0 and all_bookings.`booking_rescheduled` = 0", dateBookingStr).
		Where("all_bookings.`online_session_id` = ? AND user_id = ?", bookingCond.OnlineSessionID, bookingCond.UserID).
		Find(&booking)

	if err := query.Error; err != nil {
		fmt.Println("Could not fetch active bookings")
		return err
	}

	fmt.Println(booking)
	return nil
}

func (a *BookingData) GetMaxBookingLimitForUser(userId int32, maxLimit *int32) error {
	fmt.Println("Req -- uid -- ", userId)

	type MaxLimitStruct struct {
		MaxSportsActiveBookingLimit int32 `json:"max_sports_active_booking_limit"`
	}

	var mObj MaxLimitStruct

	query := a.Db.Table("users").
		Select("users.`max_sports_active_booking_limit`").
		Where("users.`user_id` = ?", userId).
		Find(&mObj)

	if err := query.Error; err != nil {
		fmt.Println("Could not fetch max active booking limit for user")
		return err
	}
	*maxLimit = mObj.MaxSportsActiveBookingLimit

	return nil
}

func (a *BookingData) GetUserAssessmentLevel(ctx context.Context, userId int32, assessmentLevelId *int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	type AssessmentLevel struct {
		AfterLevel int32 `json:"after_level"`
	}

	var aslt AssessmentLevel

	query := DB.Table("seals_user_performance_record sup").
		Select("max(sup.`after_level`) as after_level").
		Where("sup.`user_id` = ?", userId).
		Find(&aslt)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch asessment level for user")
		return err
	}
	*assessmentLevelId = aslt.AfterLevel

	return nil
}

func (a *BookingData) GetMaxBookingLimitByProduct(productId int32, maxLimit *int32) error {
	fmt.Println("Req -- pid -- ", productId)

	type MaxLimitStruct struct {
		MaxSportsActiveBookingLimit int32 `json:"max_sports_active_booking_limit"`
	}

	var mObj MaxLimitStruct

	query := a.Db.Table("products").
		Select("products.`max_sports_active_booking_limit`").
		Where("products.`product_id` = ?", productId).
		Find(&mObj)

	if err := query.Error; err != nil {
		fmt.Println("Could not fetch max active booking limit for product", err)
		return err
	}
	*maxLimit = mObj.MaxSportsActiveBookingLimit

	return nil
}
func (a *BookingData) GetProductIdsForSubscriptionIds(subIdArr []int32, pdSubArr *[]structs.ProductSubscription) error {
	fmt.Println("req sub id --- ", subIdArr)

	var pdData []structs.ProductSubscription

	query := a.Db.Table("subscriptions").
		Select("subscriptions.`subscription_id`, subscriptions.`product_id`").
		Where("subscriptions.`subscription_id` IN (?)", subIdArr).
		Find(&pdData)

	if err := query.Error; err != nil {
		fmt.Println("Could not fetch product ids for subscription id - ", err)
		return err
	}

	fmt.Println(pdData)
	*pdSubArr = pdData

	return nil
}

func (a *BookingData) CoachingTypeGet(ctx context.Context, coachingTypeCond *models.CoachingType, coachingType *[]models.CoachingType) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("coaching_types").
		Select("coaching_types.*").
		Where(&coachingTypeCond).
		Find(&coachingType)

	if err := query.Error; err != nil {
		log.Println("Could not fetch coaching types - ", err)
		return err
	}

	return nil
}

func (a *BookingData) GetFeedbackForBooking(feedbackCond *models.SportsFeedbackResponse, feedbackData *[]models.SportsFeedbackResponse) error {
	//add join to options table when required

	query := a.Db.Table("sports_feedback_responses").
		Select("sports_feedback_responses.*").
		Where(&feedbackCond).
		Where("sports_feedback_responses.`deleted_flag` = 0").
		Find(&feedbackData)

	if err := query.Error; err != nil {
		fmt.Println("Could not fetch feedback response - ", err)
		return err
	}

	fmt.Println(feedbackData)

	return nil
}

func (a *BookingData) GetAllFeedbackOptions(ctx context.Context, optData *[]models.SportsFeedbackOption, productCategoryId int32) error {

	cacheKey := fmt.Sprintf("All_Feedback_options_%d", productCategoryId)
	cacheDuration := time.Duration(30 * 24 * time.Hour)
	fromCache, err := a.Client.Get(cacheKey).Result()

	if err == nil {
		err = json.Unmarshal([]byte(fromCache), &optData)

		if err != nil {
			log.Println("error unmarshalling the data: %v of all feedback options", err)
			return err
		}
		return nil
	}

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("sports_feedback_options").
		Select("sports_feedback_options.*").
		Where("sports_feedback_options.`deleted_flag` = 0 AND sports_feedback_options.`at_center_option` = 1").
		Where("sports_feedback_options.`product_category_id` = ? ", productCategoryId).
		Find(&optData)

	if err := query.Error; err != nil {
		log.Println("Could not fetch feedback options - ", err)
		return err
	}

	serializedData, err := json.Marshal(optData)
	if err != nil {
		log.Println("error marshalling the data: %v of all feedback options ", err)
		return err
	}

	if err := a.Client.Set(cacheKey, serializedData, cacheDuration).Err(); err != nil {
		log.Println("error in caching Feedback Options: %v", err)
		return err
	}

	return nil
}

func (a *BookingData) GetAllRatingOptions(ctx context.Context, ratingsData *[]models.SportsFeedbackRating) error {

	cacheKey := fmt.Sprintf("All_Rating_options")
	cacheDuration := time.Duration(30 * 24 * time.Hour)
	fromCache, err := a.Client.Get(cacheKey).Result()

	if err == nil {
		err = json.Unmarshal([]byte(fromCache), &ratingsData)

		if err != nil {
			log.Println("error unmarshalling the data: %v of all rating options", err)
			return err
		}
		return nil
	}
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("sports_feedback_ratings").
		Select("sports_feedback_ratings.*").
		Where("sports_feedback_ratings.`deleted_flag` = 0").
		Find(&ratingsData)

	if err := query.Error; err != nil {
		log.Println("Could not fetch feedback options - ", err)
		return err
	}

	serializedData, err := json.Marshal(ratingsData)
	if err != nil {
		log.Println("error marshalling the data: %v of all rating options ", err)
		return err
	}

	if err := a.Client.Set(cacheKey, serializedData, cacheDuration).Err(); err != nil {
		log.Println("error in caching rating Options: %v", err)
		return err
	}

	return nil
}

func (a *BookingData) SaveFeedbackResponse(feedbackData *models.SportsFeedbackResponse, categoryOptions []int32) error {
	fmt.Println("Starting save feedback Transaction -----------------------------")
	tx := a.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	if err := tx.Create(feedbackData).Error; err != nil {
		fmt.Println("Creating feedbackData entry error! Rolling back")
		tx.Rollback()
		return err
	}

	if feedbackData.FeedbackResponseId > 0 && len(categoryOptions) > 0 { //entry created
		//create option entries
		var valuesArr []string
		for _, element := range categoryOptions {
			unitString := "(" + strconv.Itoa(int(feedbackData.FeedbackResponseId)) + "," + strconv.Itoa(int(element)) + ")"
			valuesArr = append(valuesArr, unitString)
		}
		valuesStr := strings.Join(valuesArr, ",")
		stmt := "INSERT INTO `sports_feedback_response_options` (`feedback_response_id`, `feedback_option_id`) VALUES " + valuesStr

		if err := tx.Exec(stmt).Error; err != nil {
			fmt.Println("Creating feedback Options entry error! Rolling back")
			tx.Rollback()
			return err
		}
	}

	fmt.Println("Committing save feedback Transaction ----------------")
	if err := tx.Commit().Error; err != nil {
		fmt.Println("Error in committing txn")
		return err
	}

	return nil
}

func (a *BookingData) GetUserProductDetailsByBRN(bookingRefNumber string, userProduct *models.UserProduct) error {
	fmt.Println("Fetching details for brn -- ", bookingRefNumber)
	//booking ref. no. for swimming is of 9 digits; and for sports is of 8 digits. Using this for getting data currently

	if len(bookingRefNumber) == 9 { //seals feedback case
		query := a.Db.Table("user_seals_bookings").
			Select("ss.`sports_slot_id` as `slot_id`, ss.`timings` as `timing`, p.`product_description`, p.`product_id`, user_seals_bookings.`booking_date` as `seals_booking_date`, s.`subscription_id`, u.`user_id`, u.`name`, u.`phone`, u.`email`, u.`contact_email`, sl.`name` as `facility_name`").
			Joins("INNER JOIN swimming_users_plans sup ON sup.`swimming_users_plan_id` = user_seals_bookings.`sup_id`").
			Joins("INNER JOIN users u ON u.`user_id` = sup.`user_id`").
			Joins("INNER JOIN subscriptions s ON s.`subscription_id` = sup.`all_sports_subscription_id`").
			Joins("INNER JOIN products p ON p.`product_id` = s.`product_id`").
			Joins("INNER JOIN swimming_locations sl ON sl.`swimming_location_id` = user_seals_bookings.`swimming_location_id`").
			Joins("INNER JOIN swimming_slots ss ON ss.`slot_id` = user_seals_bookings.`slot_id`").
			Where("user_seals_bookings.`booking_reference_number` = ? AND user_seals_bookings.`booking_reference_number` IS NOT NULL ", bookingRefNumber).
			Find(&userProduct)

		fmt.Println(userProduct)
		if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
			fmt.Println("Error in getting user product data! ", err)
			return err
		}
	} else {
		query := a.Db.Table("all_bookings").
			Select("all_bookings.`fs_id`, all_bookings.`slot_id`, all_bookings.`product_id`, all_bookings.`booking_date`, all_bookings.`subscription_id`,all_bookings.`online_session_id`, u.`user_id`, u.`name`, u.`phone`, u.`email`, u.`contact_email`, p.`product_description`, ss.`timing`, s.`name` as `sport_name`, f.`display_name` as `facility_name`,ons.`title_a`, ons.`title_b`,  ons.`description` as `session_description`, sp.`provider_real_name` , c.`coaching_text`").
			Joins("INNER JOIN users u ON u.`user_id` = all_bookings.`user_id`").
			Joins("LEFT JOIN products p ON p.`product_id` = all_bookings.`product_id`").
			Joins("INNER JOIN slots ss ON ss.`slot_id` = all_bookings.`slot_id`").
			Joins("LEFT JOIN coaching_types c on c.coaching_type = all_bookings.coaching_type").
			Joins("LEFT JOIN facility_sport_mappings fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
			Joins("LEFT JOIN facilities f ON f.`facility_id` = fsm.`facility_id`").
			Joins("LEFT JOIN sports s ON s.`sport_id` = fsm.`sport_id`").
			Joins("LEFT JOIN online_sessions ons ON ons.`online_session_id` = all_bookings.`online_session_id`").
			Joins("LEFT JOIN session_videos sv ON sv.`session_video_id` = ons.`session_video_id`").
			Joins("LEFT JOIN service_providers sp ON sp.`provider_id` = sv.`provider_id`").
			Group("all_bookings.`booking_reference_number`").
			Where("all_bookings.`booking_reference_number` = ?", bookingRefNumber).
			Find(&userProduct)

		if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
			fmt.Println("Error in getting user product data! ", err)
			return err
		}
	}

	return nil
}

func (a *BookingData) GetUserDetails(userData *models.UserProduct, userId int32) error {
	fmt.Println("req ------- ", userId)

	query := a.Db.Table("users").
		Select("users.`user_id`, users.`name`, users.`email`, users.`phone`, users.`contact_email`, users.`birthday`").
		Where("users.`user_id` =?", userId).
		Find(&userData)

	if err := query.Error; err != nil {
		fmt.Println("Error in getting user details!")
		return err
	}

	return nil
}

func (a *BookingData) GetParentDetailsForUser(parentData *models.UserProduct, userId int32) error {
	fmt.Println("req ------- ", userId)

	query := a.Db.Table("users").
		Select("users.`user_id`, users.`name`, users.`email`, users.`phone`, users.`contact_email`").
		Joins("INNER JOIN `user_child_account_mappings` ucam ON ucam.`user_id` = users.`user_id` AND ucam.`child_user_id` = ?", userId).
		Find(&parentData)

	if err := query.Error; err != nil {
		fmt.Println("Error in getting parent details!")
		return err
	}

	return nil
}

func (a *BookingData) GetCategoryOptionNamesByIds(categoryOptions []int32, feedOptions *[]models.SportsFeedbackOption) error {
	fmt.Println("req ------- ", categoryOptions)

	query := a.Db.Table("sports_feedback_options").
		Select("sports_feedback_options.*").
		Where("sports_feedback_options.`feedback_option_id` IN (?)", categoryOptions).
		Find(&feedOptions)

	if err := query.Error; err != nil {
		fmt.Println("Error in getting sport feedback options data!")
		return err
	}

	return nil
}

func (a *BookingData) UpdateFeedbackResponse(FeedbackResponseId *int32, feedbackData *models.SportsFeedbackResponse, categoryOptions []int32) error {
	fmt.Println("Starting update feedback Transaction -----------------------------")
	tx := a.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	if *FeedbackResponseId > 0 {
		queryFr := tx.Table("sports_feedback_responses").
			Where("sports_feedback_responses.feedback_response_id = ?", FeedbackResponseId).
			UpdateColumn("sports_feedback_responses.deleted_flag", 1)
		if err := queryFr.Error; err != nil {
			fmt.Println("Updating feedback response error! Rolling back")
			tx.Rollback()
			return err
		}

		queryOr := tx.Table("sports_feedback_response_options").
			Where("sports_feedback_response_options.feedback_response_id = ?", FeedbackResponseId).
			UpdateColumn("sports_feedback_response_options.deleted_flag", 1)
		if err := queryOr.Error; err != nil {
			fmt.Println("Updating feedback response options error! Rolling back")
			tx.Rollback()
			return err
		}
	} else {
		fmt.Println("Invalid feedback response id.")
		tx.Rollback()
	}

	fmt.Println("Committing update feedback Transaction ----------------")
	if err := tx.Commit().Error; err != nil {
		fmt.Println("Error in committing txn")
		return err
	}

	return nil
}

func (a *BookingData) SetFsIdCondition(FsIds string) func(db *gorm.DB) *gorm.DB {
	return func(Db *gorm.DB) *gorm.DB {
		return a.Db.Where("all_bookings.fs_id IN (?)", FsIds)
	}
}

func (a *BookingData) SetSlotCondition(SlotIds string) func(db *gorm.DB) *gorm.DB {
	return func(Db *gorm.DB) *gorm.DB {
		return a.Db.Where("all_bookings.slot_id IN (?)", SlotIds)
	}
}

func (a *BookingData) SetRatingCondition(RatingIds string) func(db *gorm.DB) *gorm.DB {
	return func(Db *gorm.DB) *gorm.DB {
		return a.Db.Where("all_bookings.fs_id IN (?)", RatingIds)
	}
}

func (a *BookingData) GetSportsFeedbackForFilters(feedbackReq *pb.FeedbackListingRequest, feedback *[]models.SportsFeedbackResponse) error {

	//startDate
	feedTime := time.Unix(feedbackReq.StartDate, 0)
	feedTimeLocal, err := GetLocalDateTime(feedTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	//endDate
	feedTimeEnd := time.Unix(feedbackReq.EndDate, 0)
	feedTimeLocalEnd, err := GetLocalDateTime(feedTimeEnd)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}
	//bookingStartDate
	bookingStartTime := time.Unix(feedbackReq.BookingStartDate, 0)
	bookingStartTimeLocal, err := GetLocalDateTime(bookingStartTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}
	bookingEndTime := time.Unix(feedbackReq.BookingEndDate, 0)
	bookingEndTimeLocal, err := GetLocalDateTime(bookingEndTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	qStr := "SELECT sfr.*, sfro.*, sfo.`title`, sfo.`feedback_category_id`, s.*, u.`name`, u.`phone`, u.`user_id`, fac.`display_name`, fac.`facility_id`, spo.`name` as `sport_name`, spo.`sport_id`, ab.`booking_time` FROM sports_feedback_responses sfr "
	qStr += "INNER JOIN all_bookings ab ON ab.`booking_reference_number` = sfr.`booking_reference_number` "
	if feedbackReq.StartDate > 0 || feedbackReq.EndDate > 0 {
		qStr += "AND date(sfr.last_updated) >= '" + feedTimeLocal.Format(formatYMD) + "' AND date(sfr.last_updated) <= '" + feedTimeLocalEnd.Format(formatYMD) + "' "
	}

	if feedbackReq.BookingStartDate > 0 || feedbackReq.BookingEndDate > 0 {
		qStr += "AND date(ab.booking_time) >= '" + bookingStartTimeLocal.Format(formatYMD) + "' AND date(ab.booking_time) <= '" + bookingEndTimeLocal.Format(formatYMD) + "' "
	}
	if len(feedbackReq.FsIds) > 0 {
		qStr += "AND ab.`fs_id` IN (" + feedbackReq.FsIds + ") "
	}

	if len(feedbackReq.SlotIds) > 0 {
		if feedbackReq.ExcludeSlot {
			qStr += "AND ab.`slot_id` NOT IN (" + feedbackReq.SlotIds + ") "
		} else {
			qStr += "AND ab.`slot_id` IN (" + feedbackReq.SlotIds + ") "
		}
	}

	qStr += "INNER JOIN users u ON u.`user_id` = ab.`user_id` "
	qStr += "INNER JOIN slots s ON s.`slot_id` = ab.`slot_id` "
	qStr += "INNER JOIN facility_sport_mappings fsm ON fsm.`fs_id` = ab.`fs_id` "
	if len(feedbackReq.FIds) > 0 {
		if feedbackReq.ExcludeFacility {
			qStr += "AND fsm.`facility_id` NOT IN (" + feedbackReq.FIds + ") "
		} else {
			qStr += "AND fsm.`facility_id` IN (" + feedbackReq.FIds + ") "
		}
	}

	if len(feedbackReq.SIds) > 0 {
		if feedbackReq.ExcludeSport {
			qStr += "AND fsm.`sport_id` NOT IN (" + feedbackReq.SIds + ") "
		} else {
			qStr += "AND fsm.`sport_id` IN (" + feedbackReq.SIds + ") "
		}
	}

	qStr += "INNER JOIN facilities fac ON fac.`facility_id` = fsm.`facility_id` "
	qStr += "INNER JOIN sports spo ON spo.`sport_id` = fsm.`sport_id` "

	qStr += "LEFT JOIN sports_feedback_response_options sfro ON sfro.`feedback_response_id` = sfr.`feedback_response_id` AND sfro.deleted_flag = 0 "
	qStr += "LEFT JOIN sports_feedback_options sfo ON sfo.feedback_option_id = sfro.feedback_option_id "
	qStr += "WHERE sfr.deleted_flag = 0 "
	// qStr += "AND date(sfr.created_at) >= '" + feedTimeLocal.Format(formatYMD) + "' AND date(sfr.created_at) <= '" + feedTimeLocalEnd.Format(formatYMD) + "' "

	if len(feedbackReq.RatingIds) > 0 {
		qStr += "AND sfr.`rating_id` IN (" + feedbackReq.RatingIds + ") "
	}

	query := a.Db.Raw(qStr).
		Find(&feedback)

	// query := a.Db.Table("sports_feedback_responses").
	// 	Select("sports_feedback_responses.*").
	// 	Joins("INNER JOIN all_bookings ab ON ab.`booking_reference_number` = sports_feedback_responses.`booking_reference_number`").
	// 	Where("sports_feedback_responses.deleted_flag = 0").
	// 	Where("date(sports_feedback_responses.created_at) >= ? AND date(sports_feedback_responses.created_at) <= ?", feedTimeLocal.Format(formatYMD), feedTimeLocalEnd.Format(formatYMD)).
	// 	Scopes(a.SetFsIdCondition(feedbackReq.FsIds), a.SetSlotCondition(feedbackReq.SlotIds), a.SetRatingCondition(feedbackReq.RatingIds)).
	// 	Find(&feedback)

	if err := query.Error; err != nil {
		fmt.Println("Could not fetch feedback options - ", err)
		return err
	}

	//fmt.Println(feedback)

	return nil
}

func (a *BookingData) GetBlockInventoryForFilters(inventoryReq *pb.GetBlockedInventoryRequest, inventory *[]models.SportsBlockedInventory) error {

	log.Println("request Data block inventory ---- ", inventoryReq)

	tx := a.Db.Table("sports_blocked_inventories sbi")
	tx = tx.Select("sbi.*,fac.`display_name`,spo.`sport_name`,s.`timing`, CONCAT(pc.category_name, ' ', ac.`name`) as product_arena_category_name")

	//slot join conditions
	if len(inventoryReq.SlotIds) > 0 {
		slotStrArr := strings.Split(inventoryReq.SlotIds, ",")
		var slotIntArr []int32
		for _, ele := range slotStrArr {
			val, _ := strconv.Atoi(ele)
			slotIntArr = append(slotIntArr, int32(val))
		}
		tx = tx.Joins("INNER JOIN slots s ON s.`slot_id` = sbi.`slot_id` AND s.`slot_id` IN (?)", slotIntArr)
	} else {
		tx = tx.Joins("INNER JOIN slots s ON s.`slot_id` = sbi.`slot_id`")
	}

	//fs_id join conditions
	if len(inventoryReq.FsIds) > 0 {
		FsStrArr := strings.Split(inventoryReq.FsIds, ",")
		var fsIntArr []int32
		for _, ele := range FsStrArr {
			val, _ := strconv.Atoi(ele)
			fsIntArr = append(fsIntArr, int32(val))
		}
		tx = tx.Joins("INNER JOIN facility_sport_mappings fsm ON fsm.`fs_id` = sbi.`fs_id` AND fsm.`fs_id` IN (?)", fsIntArr)
	} else {
		tx = tx.Joins("INNER JOIN facility_sport_mappings fsm ON fsm.`fs_id` = sbi.`fs_id`")
	}

	//mandatory events
	tx = tx.Joins("INNER JOIN facilities fac ON fac.`facility_id` = fsm.`facility_id`").
		Joins("INNER JOIN sports spo ON spo.`sport_id` = fsm.`sport_id`").
		Joins("LEFT JOIN play_arena_capacity_slots pacs ON sbi.`pacs_id` = pacs.`pacs_id`").
		Joins("LEFT JOIN product_arena_category_mappings pacm ON pacm.id = pacs.product_arena_category_id").
		Joins("LEFT JOIN arena_categories ac ON ac.id = pacm.arena_category_id").
		Joins("LEFT JOIN product_categories pc ON pc.product_category_id = pacm.product_category_id")

	tx = tx.Where("sbi.deleted_flag = 0")

	if inventoryReq.FacilityId > 0 {
		tx = tx.Where("fac.facility_id = ?", inventoryReq.FacilityId)
	}

	if inventoryReq.StartDate > 0 && inventoryReq.EndDate > 0 {
		blockTime := time.Unix(inventoryReq.StartDate, 0)
		blockTimeLocal, err := GetLocalDateTime(blockTime)
		if err != nil {
			log.Println("time converison error -- ", err)
			return err
		}

		//endDate
		blockTimeEnd := time.Unix(inventoryReq.EndDate, 0)
		blockTimeLocalEnd, err := GetLocalDateTime(blockTimeEnd)
		if err != nil {
			log.Println("time converison error -- ", err)
			return err
		}
		tx = tx.Where("date(sbi.date) >= ? AND date(sbi.date) <= ?", blockTimeLocal.Format(formatYMD), blockTimeLocalEnd.Format(formatYMD))
	}

	query := tx.Find(&inventory)

	if err := query.Error; err != nil {
		log.Println("Could not fetch Block Inventory Data - ", err)
		return err
	}

	log.Println("query block inventory -------", inventory)

	return nil
}

func (a *BookingData) GetSealsFeedbackForFilters(ctx context.Context, feedbackReq *pb.FeedbackListingRequest, feedback *[]models.SportsFeedbackResponse) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	//startDate
	feedTime := time.Unix(feedbackReq.StartDate, 0)
	feedTimeLocal, err := GetLocalDateTime(feedTime)
	if err != nil {
		log.Println("time converison error -- ", err)
		return err
	}

	//endDate
	feedTimeEnd := time.Unix(feedbackReq.EndDate, 0)
	feedTimeLocalEnd, err := GetLocalDateTime(feedTimeEnd)
	if err != nil {
		log.Println("time converison error -- ", err)
		return err
	}

	//get facility_ids from fs_ids -- required for getting seals feedback
	var fids []string
	var fsData []structs.FacilitySportIds
	if len(feedbackReq.FsIds) > 0 {
		fsIds := strings.Split(feedbackReq.FsIds, ",") //string to array
		var fsIdsInt []int32
		for _, ele := range fsIds {
			val, _ := strconv.Atoi(ele)
			fsIdsInt = append(fsIdsInt, int32(val))
		}
		if err := a.GetFacilityIdsAndSportIdsForAllFsIds(ctx, fsIdsInt, &fsData); err != nil {
			log.Println("Error in getting facilityIds from fs_ids for getting seals feedback data")
			return err
		}
		for _, ele := range fsData {
			fids = append(fids, strconv.Itoa(int(ele.FacilityID)))
		}
	}

	qStr := "SELECT sfr.*, sfro.*, sfo.`title`, sfo.`feedback_category_id`, ss.`sports_slot_id` as `slot_id`, ss.`timings` as `timing`, sl.`facility_id`, sl.`name` as `display_name`, u.`user_id`, u.`name`, u.`phone`, \"Swimming\" as `sport_name`, \"3\" as `sport_id`, usb.`booking_time` FROM sports_feedback_responses sfr "
	qStr += "INNER JOIN user_seals_bookings usb ON usb.`booking_reference_number` = sfr.`booking_reference_number` AND usb.`booking_reference_number` IS NOT NULL "
	qStr += "AND date(sfr.last_updated) >= '" + feedTimeLocal.Format(formatYMD) + "' AND date(sfr.last_updated) <= '" + feedTimeLocalEnd.Format(formatYMD) + "' "
	qStr += "INNER JOIN users u ON u.`user_id` = usb.`user_id` "
	qStr += "INNER JOIN swimming_slots ss ON ss.`slot_id` = usb.`slot_id` "
	if len(feedbackReq.SlotIds) > 0 {
		if feedbackReq.ExcludeSlot {
			qStr += "AND ss.`sports_slot_id` NOT IN (" + feedbackReq.SlotIds + ") "
		} else {
			qStr += "AND ss.`sports_slot_id` IN (" + feedbackReq.SlotIds + ") "
		}
	}
	qStr += "INNER JOIN swimming_locations sl ON sl.`swimming_location_id` = usb.`swimming_location_id` "
	if len(fids) > 0 {
		qStr += "AND sl.`facility_id` IN (" + strings.Join(fids, ",") + ") "
	}
	if len(feedbackReq.FIds) > 0 {
		if feedbackReq.ExcludeFacility {
			qStr += "AND sl.`facility_id` NOT IN (" + feedbackReq.FIds + ") "
		} else {
			qStr += "AND sl.`facility_id` IN (" + feedbackReq.FIds + ") "
		}
	}
	qStr += "INNER JOIN sports_feedback_response_options sfro ON sfro.`feedback_response_id` = sfr.`feedback_response_id` AND sfro.deleted_flag = 0 "
	qStr += "INNER JOIN sports_feedback_options sfo ON sfo.feedback_option_id = sfro.feedback_option_id "
	qStr += "WHERE sfr.deleted_flag = 0 "
	// qStr += "AND date(sfr.created_at) >= '" + feedTimeLocal.Format(formatYMD) + "' AND date(sfr.created_at) <= '" + feedTimeLocalEnd.Format(formatYMD) + "' "

	if len(feedbackReq.RatingIds) > 0 {
		qStr += "AND sfr.`rating_id` IN (" + feedbackReq.RatingIds + ") "
	}

	query := DB.Raw(qStr).
		Find(&feedback)

	if err := query.Error; err != nil {
		log.Println("Could not fetch feedback options - ", err)
		return err
	}

	return nil
}

func (a *BookingData) InsertBlockedSlotsAndUpdateActiveSlots(blockedInventory *[]models.SportsBlockedInventory, updateActiveSlots int32) error {

	log.Println("Starting insert blocked slots Transaction -----------------------------", blockedInventory)
	tx := a.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	var valuesArr []string
	for _, element := range *blockedInventory {
		unitString := "(" + strconv.Itoa(int(element.FSID)) + "," + strconv.Itoa(int(element.PAID)) + "," + "\"" + element.Date.Format(formatYMD) + "\"" + "," + strconv.Itoa(int(element.SlotID)) + "," + strconv.Itoa(int(element.PacsID)) + "," + strconv.Itoa(int(element.BlockReasonId)) + "," + "\"" + element.BlockRelatedNote + "\"" + "," + strconv.Itoa(int(element.BlockedBy)) + ")"
		valuesArr = append(valuesArr, unitString)

		if updateActiveSlots == 1 {

			if element.FSID > 0 && element.PAID > 0 {

				queryPa := tx.Table("pa_active_slots pas").
					Where("DATE(pas.date) = ? and pas.pacs_id = ?", element.Date.Format(formatYMD), element.PacsID).
					UpdateColumn("pas.active", 0)

				if err := queryPa.Error; err != nil {
					log.Println("Updating pa_active slots error! Rolling back", err)
					tx.Rollback()
					return err
				}

				var paSumVal structs.PaSumValues
				selectFs := tx.Table("pa_active_slots pas").
					Select("SUM(pas.`capacity`) as `capacity`, SUM(pas.`remaining_capacity`) as `remaining_capacity`, SUM(pas.`active`) as `active`").
					Where("pas.fs_id = ? and DATE(pas.date) = ? and pas.slot_id = ? AND pas.`active` = 1", element.FSID, element.Date.Format(formatYMD), element.SlotID).
					Scan(&paSumVal)

				if err := selectFs.Error; err != nil {
					log.Println("Select pa_Active sum values error! Rolling back", err)
					tx.Rollback()
					return err
				}
				if paSumVal.Active > 1 {
					paSumVal.Active = 1
				}

				queryFs := tx.Table("fs_active_slots fas").
					Where("fas.fs_id = ? and DATE(fas.date) = ? and fas.slot_id = ?", element.FSID, element.Date.Format(formatYMD), element.SlotID).
					Updates(map[string]interface{}{"capacity": paSumVal.Capacity, "remaining_capacity": paSumVal.RemainingCapacity, "active": paSumVal.Active})

				if err := queryFs.Error; err != nil {
					log.Println("Updating fs_Active slots error! Rolling back", err)
					tx.Rollback()
					return err
				}
			}
		}
	}

	valuesStr := strings.Join(valuesArr, ",")

	stmt := "INSERT INTO `sports_blocked_inventories` (`fs_id`, `pa_id`, `date`, `slot_id`, `pacs_id`, `block_reason_id`, `block_related_note`, `blocked_by`) VALUES " + valuesStr

	if err := tx.Exec(stmt).Error; err != nil {
		log.Println("Creating blocked slots entry error! - - ", err)
		tx.Rollback()
		return err
	}

	log.Println("Committing insert blocked slots Transaction ----------------")
	if err := tx.Commit().Error; err != nil {
		log.Println("Error in committing txn", err)
		return err
	}

	return nil
}

func (a *BookingData) GetDistinctPopulatableActiveFsids(ctx context.Context, fsids *[]int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	type FsIds struct {
		FsId int32 `json:"fs_id"`
	}

	var fs_ids []FsIds

	response := DB.Table("play_arenas").
		Select("DISTINCT play_arenas.`fs_id`").
		Where("play_arenas.operation_status = ?", 1).
		Scan(&fs_ids)

	if err := response.Error; err != nil {
		log.Println("Could not fetch distinct fs_ids!")
		return err
	}

	for _, fsIdStruct := range fs_ids {
		*fsids = append(*fsids, fsIdStruct.FsId)
	}

	return nil
}

func (a *BookingData) GetDistinctPopulatableActivePaidsForFsid(fsid int32, paids *[]int32) error {

	type PaIds struct {
		PaId int32 `json:"pa_id",form:"pa_id"`
	}

	var pa_ids []PaIds

	response := a.Db.Table("play_arenas").
		Select("play_arenas.`pa_id`").
		Where("play_arenas.fs_id = ? AND play_arenas.operation_status = ?", fsid, 1).
		Scan(&pa_ids)

	if err := response.Error; err != nil {
		fmt.Println("Could not fetch distinct pa_ids!")
		return err
	}

	for _, paIdStruct := range pa_ids {
		*paids = append(*paids, paIdStruct.PaId)
	}

	fmt.Println("Distinct paids --- ", paids)

	return nil
}

func (a *BookingData) GetBlockedInventory(query *models.SportsBlockedInventory, res *[]models.SportsBlockedInventory) error {

	response := a.Db.Where("`pacs_id` = ? AND `date` = ? AND `deleted_flag` = 0", query.PacsID, query.Date.Format(formatYMD)).Find(res)

	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetRelatedFixedUsers(fsSlotDate *[]structs.FsPaSlotDate, fUsers *[]int32) error {
	fmt.Println("fsSlotDate -------------- ", fsSlotDate)

	type Uids struct {
		UserId int32 `json:"user_id"`
	}

	var user_ids []Uids

	var fsIds []int32
	for _, ele := range *fsSlotDate {
		fsIds = append(fsIds, ele.FSID)
	}

	response := a.Db.Table("subscriptions").
		Select("subscriptions.`user_id`").
		Joins("INNER JOIN product_facility_sport_mappings pfsm ON pfsm.`product_id` = subscriptions.`product_id` AND pfsm.`fs_id` IN (?)", fsIds).
		Where("subscriptions.`session_count` = 0 AND subscriptions.`is_active` = ? AND subscriptions.`subscription_end_date` > CURRENT_TIMESTAMP", 1).
		Group("subscriptions.`user_id`").
		Find(&user_ids)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	for _, ele := range user_ids {
		*fUsers = append(*fUsers, ele.UserId)
	}

	return nil
}

func (a *BookingData) CancelBookingsForFsIdSlotIdAndDate(fsPaSlotDate []structs.FsPaSlotDate, cancelledUids *[]int32) error {
	log.Println("fsidSlotDate -------- ", fsPaSlotDate)

	if len(fsPaSlotDate) == 0 {
		return errors.New("slots data can not be empty")
	}

	log.Println("Starting Cancel booking for block inventory Transaction -----------------------------")
	txn := a.Db.Begin()
	defer func() {
		if r := recover(); r != nil {
			txn.Rollback()
		}
	}()
	if err := txn.Error; err != nil {
		return err
	}

	cancelDate := time.Unix(fsPaSlotDate[0].Date, 0)
	cancelDateLocal, err := GetLocalDateTime(cancelDate)
	if err != nil {
		log.Println("time converison error -- ", err)
		txn.Rollback()
		return err
	}
	cancelBy := fsPaSlotDate[0].CancelBy

	var allB []models.AllBooking
	stx := txn.Table("all_bookings").
		Set("gorm:query_option", "FOR UPDATE").
		Select("booking_id, user_id, created_by").
		Where("booking_cancelled = ?", 0)

	for i, element := range fsPaSlotDate {
		if i == 0 {
			stx = stx.Where("`pacs_id` = ? AND date(`booking_time`) = ?", element.PacsId, cancelDateLocal.Format(formatYMD))
		} else {
			stx = stx.Or("`pacs_id` = ? AND date(`booking_time`) = ?", element.PacsId, cancelDateLocal.Format(formatYMD))
		}
	}
	stx = stx.Find(&allB)

	if errStx := stx.Error; errStx != nil {
		log.Println("Error in selecting bookings for update.......", errStx)
		txn.Rollback()
		return errStx
	}

	if len(allB) > 0 {
		bookingIds := make([]int32, 0)
		for _, val := range allB {
			bookingIds = append(bookingIds, val.BookingID)
		}

		tx := txn.Table("all_bookings").
			Where("booking_cancelled = ? AND booking_id IN (?)", 0, bookingIds).
			Updates(map[string]interface{}{"booking_cancelled": 1, "charge_levied": 0, "cancel_by": cancelBy, "cancel_time": time.Now()})
		if err := tx.Error; err != nil {
			log.Println("Could not cancel related bookings. Db error!", err)
			txn.Rollback()
			return err
		}

		if tx.RowsAffected > 0 {
			for _, ele := range allB {
				*cancelledUids = append(*cancelledUids, ele.UserID)
				*cancelledUids = append(*cancelledUids, ele.CreatedBy)
			}
		}
	}

	log.Println("Committing cancel for blocked slots Transaction ----------------")
	if err := txn.Commit().Error; err != nil {
		log.Println("Error in committing txn", err)
		txn.Rollback()
		return err
	}

	return nil
}

func (a *BookingData) GetTrialUsersWithNoBookingsToday(trialUsersData *[]structs.UserSubscription) error {

	currentTimeLocal, err := GetLocalDateTime(time.Now())
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}
	response := a.Db.Table("subscriptions").
		Select("subscriptions.`user_id`, subscriptions.`subscription_id`, u.`phone`").
		Joins("INNER JOIN products p ON p.`product_id` = subscriptions.`product_id` AND p.`is_trial` = 1").
		Joins("INNER JOIN users u ON u.`user_id` = subscriptions.`user_id`").
		Where("subscriptions.`is_active` = ? AND subscriptions.`subscription_end_date` > CURRENT_TIMESTAMP AND (SELECT COUNT(*) FROM all_bookings ab WHERE ab.`subscription_id` = subscriptions.`subscription_id` AND date(ab.`booking_time`) = ?) = ? AND (SELECT COUNT(*) FROM `subscriptions` ss WHERE ss.`user_id` = subscriptions.`user_id`) = ?", 1, currentTimeLocal.Format(formatYMD), 0, 1).
		Group("subscriptions.`user_id`").
		Find(&trialUsersData)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting trial users..", err)
		return err
	}

	return nil
}

func (a *BookingData) GetPremiumUsersWithNoBookingsInLastTwoDays(premiumUsersData *[]structs.UserSubscription) error {

	lastDateTime, err1 := GetLocalDateTime(time.Now().AddDate(0, 0, -1))
	if err1 != nil {
		fmt.Println("last datetime converison error -- ", err1)
		return err1
	}

	response := a.Db.Table("subscriptions").
		Select("subscriptions.`user_id`, subscriptions.`subscription_id`").
		Joins("INNER JOIN products p ON p.`product_id` = subscriptions.`product_id` AND p.`is_trial` = 0").
		Where("subscriptions.`is_active` = ? AND subscriptions.`subscription_end_date` > CURRENT_TIMESTAMP AND (SELECT COUNT(*) FROM all_bookings ab WHERE ab.`subscription_id` = subscriptions.`subscription_id` AND date(ab.`booking_time`) >= ?) = ?", 1, lastDateTime.Format(formatYMD), 0).
		Group("subscriptions.`user_id`").
		Find(&premiumUsersData)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting premium users..", err)
		return err
	}

	return nil
}

func (a *BookingData) GetSubscribedUserWithNoBookingInLastNdays(premiumUsersData *[]structs.UserSubscription, ndays int32) error {

	response := a.Db.Table("subscriptions").
		Select("subscriptions.`user_id`,subscriptions.`subscription_id`").
		Joins("INNER JOIN products p ON p.`product_id` = subscriptions.`product_id` ").
		Joins("INNER JOIN all_bookings ab ON  subscriptions.`user_id` = ab.`user_id` ").
		Joins("INNER JOIN users u ON u.`user_id` = ab.`user_id`").
		Where("subscriptions.`end_date` >= CURDATE( ) AND subscriptions.`start_date` < CURDATE( ) - ?  AND subscriptions.`product_id` <83 AND subscriptions.`product_id` not in (52,53,51) AND subscriptions.`is_active` =1 AND ab.`user_id` not in ( 	select user_id from `user_seals_bookings` where `booking_date` >=(curdate()-?)	)", ndays, ndays).
		Group("ab.`user_id`").
		Having("DATEDIFF( CURDATE( ) , MAX( booking_date ) ) >?", ndays).
		Order("subscriptions.`product_id` ASC").
		Find(&premiumUsersData)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting premium users..", err)
		return err
	}

	return nil
}

func (a *BookingData) GetBookingSlotDetails(ctx context.Context, pointers *structs.InfoText, slotReq *pb.BookingSlotsDetailsRequest) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	var fsIdArr []int32
	fsIdArr = append(fsIdArr, slotReq.FsId)
	var fsData []structs.FacilitySportIds
	if err := a.GetFacilityIdsAndSportIdsForAllFsIds(ctx, fsIdArr, &fsData); err != nil {
		log.Println("Error in getting fids and sids from fsids..", err)
		return err
	}
	var fsDataVal structs.FacilitySportIds
	for _, ele := range fsData {
		fsDataVal = ele
		break
	}

	log.Println("data-----------------------", fsDataVal)

	dayOfWeek := int(time.Unix(slotReq.Date, 0).Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}

	date := time.Unix(slotReq.Date, 0)

	if fsDataVal.SportID != 3 || is_swimming_considered_as_sports {
		response := DB.Table("fs_active_slots fsas").
			Select("fsas.`cs_id`, fsas.`info_text`,fsas.`coaching_type`").
			Where("fsas.`slot_id` = ? AND fsas.`fs_id` = ? AND date(fsas.date) = ?", slotReq.SlotId, slotReq.FsId, date.Format(formatYMD)).
			Scan(&pointers)

		if err := response.Error; err != nil {
			log.Println("Error in getting slots details..", err)
			return err
		}
	} else { //swimming
		response := DB.Table("swimming_location_slot_mappings slsm").
			Select("slsdm.`swimming_location_slot_day_id`, slsdm.`info_text`").
			Joins("INNER JOIN swimming_location_slot_day_mappings slsdm ON slsdm.`swimming_location_slot_id` = slsm.`swimming_location_slot_id` AND slsdm.`day_number` = ?", dayOfWeek).
			Joins("INNER JOIN swimming_slots ss ON ss.`slot_id` = slsm.`slot_id` AND ss.`sports_slot_id` = ?", slotReq.SlotId).
			Joins("INNER JOIN swimming_locations sl ON sl.`swimming_location_id` = slsm.`swimming_location_id` AND sl.`facility_id` = ?", fsDataVal.FacilityID).
			Where("slsm.`is_active` = ?", 1).
			Scan(&pointers)

		if err := response.Error; err != nil {
			log.Println("Error in getting slots details..", err)
			return err
		}

		log.Println("pointers ---- ", *pointers)
	}

	pointers.SportId = fsDataVal.SportID
	// response := a.Db.Table("fs_active_slots").
	// 	Select("fs_active_slots.`id`, fs_active_slots.`info_text`, fs_active_slots.`cs_id`").
	// 	Where("fs_active_slots.`cs_id` = ?", csId).
	// 	Scan(&pointers)

	// if err := response.Error; err != nil {
	// 	fmt.Println("Error in getting slots details..", err)
	// 	return err
	// }

	return nil
}

func (a *BookingData) GetLiveSessionDetails(sessionDetails *[]models.SessionVideo, online_session_id int32) error {

	response := a.Db.Table("online_sessions os").
		Select("sv.*, os.`title_a`, os.`title_b`, os.`session_time`, os.`description` as `session_description`, sp.`provider_real_name`, sp.`dp_url`, sp.`title` as `trainer_title`,sp.`about_trainer`, (SELECT COUNT(*) FROM `all_bookings` abb WHERE abb.`online_session_id` = os.`online_session_id` AND abb.`booking_cancelled` = 0) as `booking_count`").
		Joins("INNER JOIN session_videos sv ON sv.`session_video_id` = os.`session_video_id`").
		Joins("LEFT JOIN service_providers sp ON sp.`provider_id` = sv.`provider_id`").
		Where("os.`online_session_id` = ? AND os.`deleted_flag` = ?", online_session_id, 0).
		Scan(&sessionDetails)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting slots details..", err)
		return err
	}

	return nil
}

func (a *BookingData) GetCoachingDetails(coachingDetails *structs.InfoText, csId int32) error {
	response := a.Db.Table("fs_active_slots").
		Select("ct.`coaching_text`").
		Where("fs_active_slots.`cs_id` = ?", csId).
		Joins("INNER JOIN coaching_types  ct on ct.`coaching_type` = fs_active_slots.`coaching_type`").
		Find(&coachingDetails)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting coaching details..", err)
		return err
	}

	return nil
}

func (a *BookingData) AttendanceStatusMark(attData *pb.MarkAttendance, markSuccessful *bool) error {
	if len(attData.BookingReferenceNumber) == 9 {
		response := a.Db.Table("user_seals_bookings usb").
			Where("usb.`booking_reference_number` IS NOT NULL AND usb.`booking_reference_number` = ?", attData.BookingReferenceNumber).
			Updates(map[string]interface{}{"attendance_flag": attData.AttendanceFlag, "attendance_marked_by": attData.MarkedByUserId})

		if err := response.Error; err != nil {
			fmt.Println("Error in marking/unmarking seals attendance..", err)
			*markSuccessful = false
			return err
		}
	} else {
		response := a.Db.Table("all_bookings ab").
			Where("ab.`booking_reference_number` = ?", attData.BookingReferenceNumber).
			Updates(map[string]interface{}{"attendance_flag": attData.AttendanceFlag, "attendance_marked_by": attData.MarkedByUserId})

		if err := response.Error; err != nil {
			fmt.Println("Error in marking/unmarking sports attendance..", err)
			*markSuccessful = false
			return err
		}
	}
	*markSuccessful = true

	return nil
}

func (a *BookingData) MarkNoShowApplicableInSports(bookingId int32) error {
	log.Println("req bookingId-- ", bookingId)

	response := a.Db.Table("all_bookings").
		Where("`booking_id` = ?", bookingId).
		Updates(map[string]interface{}{"no_show_applicable": 1})

	if err := response.Error; err != nil {
		log.Println("Error marking no show applicable for ", bookingId, " -- DB err - ", err)
		return err
	}

	return nil
}

func (a *BookingData) GetCoachingType(ctx context.Context, fsId int32, slotId int32, dayOfWeek int32, coachingType *int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	type CoachingTypeObj struct {
		CoachingType int32 `json:"coaching_type"`
	}
	var ctypeVal CoachingTypeObj

	response := DB.Table("facility_sport_capacity_slots fscs").
		Select("fscs.coaching_type").
		Where("fscs.`fs_id` = ? AND fscs.`slot_id` = ? AND fscs.`day_of_week` = ?", fsId, slotId, dayOfWeek).
		Find(&ctypeVal)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch coaching type from fsid, dow and slotid")
		return err
	}

	*coachingType = ctypeVal.CoachingType
	return nil
}

func (a *BookingData) GetUnmarkedAttendanceUserIds(userData *[]structs.UserData, slotIds []int32) error {

	response := a.Db.Table("all_bookings").
		Select("all_bookings.`user_id` ,pacs.product_arena_category_id, all_bookings.`booking_reference_number`, all_bookings.`booking_id`, sp.`name` as sport_name , u.`name` as user_name , f.`display_name` as facility_name, fsm.`fs_id`, s.`slot_id`,s.`timing`,f.`no_show_penalty_on`,p.`is_trial`, all_bookings.`attendance_flag`").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
		Joins("INNER JOIN facilities f ON f.`facility_id` = fsm.`facility_id`").
		Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
		Joins("INNER JOIN `products` p ON p.`product_id`=all_bookings.`product_id`").
		Joins("INNER JOIN  `users` u ON u.`user_id` = all_bookings.`user_id` ").
		Joins("INNER JOIN  `slots` s ON s.`slot_id` = all_bookings.`slot_id` AND s.`slot_id` in (?)", slotIds).
		Joins("INNER JOIN play_arena_capacity_slots pacs on pacs.pacs_id = all_bookings.pacs_id").
		Where("all_bookings.`booking_date` = CURDATE() AND all_bookings.`slot_id` in (?) AND p.product_id not in (1681,1682,1683) AND (all_bookings.`attendance_flag`= ? OR (all_bookings.`attendance_flag` =1 AND all_bookings.`no_show_applicable` =1))  AND all_bookings.`booking_cancelled` = ? AND all_bookings.`booking_rescheduled` = ?", slotIds, 0, 0, 0).
		Where("all_bookings.`was_waitlisted` = 0 OR (all_bookings.`was_waitlisted` = 1 AND all_bookings.created_at < DATE_SUB(all_bookings.booking_time, INTERVAL 90 MINUTE))").
		Find(&userData)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch coaching type from fsid, dow and slotid")
		return err
	}

	return nil
}

func (a *BookingData) GetDroppedOutBookingsForSlotIds(ctx context.Context, userData *[]structs.UserData, slotIds []int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings").
		Select("all_bookings.`user_id` ,pacs.product_arena_category_id, all_bookings.`booking_reference_number`, all_bookings.`booking_id`, all_bookings.was_waitlisted, all_bookings.created_at, sp.`name` as sport_name , u.`name` as user_name , f.`display_name` as facility_name,s.`slot_id`,s.`timing`,f.`no_show_penalty_on`, fsm.`fs_id`, p.`is_trial`, all_bookings.`attendance_flag`").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
		Joins("INNER JOIN facilities f ON f.`facility_id` = fsm.`facility_id`").
		Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
		Joins("INNER JOIN `products` p ON p.`product_id`=all_bookings.`product_id`").
		Joins("INNER JOIN  `users` u ON u.`user_id` = all_bookings.`user_id` ").
		Joins("INNER JOIN  `slots` s ON s.`slot_id` = all_bookings.`slot_id` AND s.`slot_id` in (?)", slotIds).
		Joins("INNER JOIN play_arena_capacity_slots pacs on pacs.pacs_id = all_bookings.pacs_id").
		Where("all_bookings.`app_type` = ? AND all_bookings.`booking_date` = CURDATE() AND all_bookings.`slot_id` in (?) AND p.product_id not in (1681,1682,1683) AND all_bookings.`booking_cancelled` = ? AND all_bookings.`cancel_time` >= DATE_SUB(all_bookings.booking_time, INTERVAL 60 MINUTE)", CULT_APP_TYPE, slotIds, 1).
		Order("all_bookings.booking_id DESC").
		Find(&userData)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch coaching type from fsid, dow and slotid")
		return err
	}

	return nil
}

func (a *BookingData) GetMatchedBookings(ctx context.Context, slotIds []int32, matchedBookingIds *[]int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	type WaitlistCancelledBooking struct {
		CancelledBookingId int32 `json:"cancelled_booking_id"`
	}
	var cancelledBookings []WaitlistCancelledBooking
	response := DB.Table("waitlist_bookings").
		Select("cancelled_booking_id").
		Where("DATE(booking_time) = CURDATE() AND cancelled_booking_id > ? AND slot_id in (?) AND state = ?", 0, slotIds, "CONFIRMED").
		Find(&cancelledBookings)
	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("Could not fetch cancelled bookings from waitlist for slot ids: %v, %v", slotIds, err)
		return err
	}

	for _, item := range cancelledBookings {
		*matchedBookingIds = append(*matchedBookingIds, item.CancelledBookingId)
	}

	return nil
}

func (a *BookingData) SlotBookingAttendanceCount(facilitySportSlotData *[]structs.FacilitySportSlotData, slotIds []int32) error {
	response := a.Db.Table("all_bookings").
		Select("f.`display_name` as facility_name, sp.`name` as sport_name, s.`timing`, count(*) booking_count, sum(case when attendance_flag = 1 then 1 else 0 end) attendance_count, f.facility_type").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = all_bookings.`fs_id`").
		Joins("INNER JOIN facilities f ON f.`facility_id` = fsm.`facility_id`").
		Joins("INNER JOIN `sports` sp ON sp.`sport_id` = fsm.`sport_id`").
		Joins("INNER JOIN  `slots` s ON s.`slot_id` = all_bookings.`slot_id` AND s.`slot_id` in (?)", slotIds).
		Where("all_bookings.`booking_date` = CURDATE() AND all_bookings.`booking_cancelled` = ? AND all_bookings.`booking_rescheduled` = ?", 0, 0).
		Group("fsm.fs_id").
		Find(&facilitySportSlotData)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Could not fetch Attendance and booking count")
		return err
	}
	return nil
}

func (a *BookingData) SendEmail(emailData *structs.EmailRequest) error {

	bodyBytes, _ := json.Marshal(emailData)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "1",
		},
		Body: bodyBytes,
	}

	if err := a.Broker.Publish(emailTopic, msg); err != nil {
		fmt.Printf("[pub] failed: %v", err)
	} else {
		fmt.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) GetUnmarkedAttendanceOfSealsUsersIds(userData *[]structs.UserData, slotId int32) error {
	response := a.Db.Table("user_seals_bookings usb").
		Select("usb.`user_id`,usb.`booking_reference_number` , usb.`booking_id`  , u.name as user_name ,f.`display_name` as facility_name,sls.`slot_id`,sls.`timings`as `timing`").
		Joins("INNER JOIN users u ON u.`user_id`= usb.`user_id`").
		Joins("INNER JOIN swimming_slots sls ON sls.`slot_id` = usb.`slot_id` AND sls.`sports_slot_id`=? ", slotId).
		Joins("INNER JOIN swimming_locations sl ON sl.`swimming_location_id` = usb.`swimming_location_id`").
		Joins("INNER JOIN facilities f ON f.`facility_id` = sl.`facility_id`").
		Where("usb.`booking_date`= CURDATE()  AND usb.`attendance_flag`= ? AND usb.`cancelled_flag` = ? AND usb.`rescheduled_flag` = ? AND usb.`booking_reference_number` is not null", 0, 0, 0).
		Find(&userData)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Could not fetch coaching type from fsid, dow and slotid")
		return err
	}

	return nil
}

func (a *BookingData) IsOptInSubscription(optInSubs *[]structs.OptIn, subscriptionId int32) error {
	response := a.Db.Table("subscriptions as s").
		Select("opt_ins.*,p.`is_trial`").
		Joins("INNER JOIN products p ON p.`product_id`=s.`product_id`").
		Joins("LEFT JOIN opt_ins  ON opt_ins.`subscription_id`= s.`subscription_id` AND opt_ins.`subscription_id` = ? and opt_ins.`deleted_flag`= ?", subscriptionId, 0).
		Where("s.`subscription_id` = ? and s.`is_active`= ?", subscriptionId, 1).
		Find(&optInSubs)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting opt in details..", err)
		return err
	}

	return nil
}
func (a *BookingData) IsCompetitiveSwimmingUser(userId int32, users *[]structs.SealsCompetitiveUser) error {
	response := a.Db.Table("seals_competitive_users as scu").
		Select("scu.*").
		Where("scu.`deleted_flag`= 0 AND scu.`user_id` = ?", userId).
		Find(&users)

	if err := response.Error; err != nil {
		log.Println("Error in getting competitive swimmer details..", err)
		return err
	}
	return nil
}

func (a *BookingData) GetBlockedInventoryRowsForIds(ids *[]int32, rows *[]models.SportsBlockedInventory) error {

	response := a.Db.Table("sports_blocked_inventories sbi").
		Select("sbi.*").Where("sbi.`sports_blocked_inventory_id` IN (?) AND sbi.`deleted_flag` = 0 AND sbi.`date` >= CURDATE()", (*ids)[:]).
		Find(rows)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		fmt.Println("Could not fetch blcoked rows data")
		return err
	}

	return nil
}

func (a *BookingData) NoShowPenaltyOn(fsId int32, isOn *bool) error {
	type NoShowPenaltyOnObj struct {
		NoShowPenaltyOn bool `json:"no_show_penalty_on"`
	}
	var noShowPenaltyOnObj NoShowPenaltyOnObj

	response := a.Db.Table("facilities ").
		Select("facilities.`no_show_penalty_on`").
		Joins("INNER JOIN facility_sport_mappings fsm ON fsm.`facility_id` = facilities.`facility_id` AND fsm.`fs_id`=?", fsId).
		Find(&noShowPenaltyOnObj)

	if err := response.Error; err != nil {
		fmt.Println("Could not fetch no show penalty on from fsid, ")
		return err
	}

	*isOn = noShowPenaltyOnObj.NoShowPenaltyOn
	return nil
}
func (a *BookingData) MarkDeletedBlockedInventoryRowsForIds(ids *[]int32, unblockerId int32, updateSuccessful *bool) error {

	response := a.Db.Table("sports_blocked_inventories sbi").
		Where("sbi.`deleted_flag` = 0 AND sbi.`sports_blocked_inventory_id` IN (?) AND sbi.`date` >= CURDATE()", (*ids)[:])

	response = response.Updates(map[string]interface{}{"deleted_flag": 1, "unblocked_by": unblockerId})

	if err := response.Error; err != nil {
		fmt.Println("Error in unfreezing.. Db error -- ", err)
		*updateSuccessful = false
		return err
	}

	if response.RowsAffected > 0 {
		*updateSuccessful = true
	} else {
		*updateSuccessful = false
	}
	return nil
}

func (a *BookingData) DeleteFsAndPaActiveSlotsForGiven(data *structs.UnblockCombo, deleteSuccess *bool) error {

	*deleteSuccess = false

	response := a.Db.Delete(models.FSActiveSlot{}, "fs_id = ? and DATE(date) = ? and slot_id = ?", data.FSID, data.Date.Format(formatYMD), data.SlotID)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	responsePa := a.Db.Delete(models.PAActiveSlot{}, "DATE(date) = ? and pacs_id = ?", data.Date.Format(formatYMD), data.PacsID)
	if err := responsePa.Error; err != nil {
		fmt.Println(err)
		return err
	}

	*deleteSuccess = true

	return nil

}

func (a *BookingData) GetAllFavoriteFacilitiesForUser(ctx context.Context, userId int32, facility *[]models.Facility) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("favorite_facilities").
		Select("facilities.*").
		Joins("INNER JOIN facilities ON facilities.`facility_id` = favorite_facilities.`facility_id`").
		Where("favorite_facilities.`deleted_flag` = ? and favorite_facilities.`user_id` = ?", 0, userId).
		Find(&facility)

	if err := query.Error; err != nil {
		log.Println("Error in getting favorite facilities for this user")
		return err
	}

	return nil
}

func (a *BookingData) GetUpcomingInactiveDays(upcomingInactiveDays *[]models.SportsInactiveDay, idc *structs.InactiveDaysCondition) error {

	query := a.Db.Table("sports_inactive_days sid").
		Select("sid.*").
		Where("(sid.`fs_id` = ? OR sid.`facility_id` = ? OR (sid.`fs_id` = 0 AND sid.`facility_id` = 0)) AND sid.`date` >= CURDATE() AND sid.`deleted_flag` = ?", idc.FsId, idc.FacilityId, 0).
		Find(&upcomingInactiveDays)

	if err := query.Error; err != nil {
		log.Println("Error in getting upcoming inactive days!")
		return err
	}

	return nil
}

// checks if already entry exist or not, if exist don't override it.
func (a *BookingData) MarkInActiveDayForNonOperational(ctx context.Context, fsId int32, date time.Time, reasonId int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	var inactiveDays []models.SportsInactiveDay

	response := DB.Table("sports_inactive_days").
		Select("*").
		Where("fs_id = ? AND date = ? AND deleted_flag = ?", fsId, date, 0).
		Find(&inactiveDays)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("func:MarkInActiveDayForNonOperational, error in getting upcoming inactive days for fsId:%d, date:%s, err:%v", fsId, date, err)
		return err
	}

	if len(inactiveDays) == 0 {
		inactiveDate, _ := time.Parse(formatYMD, date.Format(formatYMD)) //currDate
		record := &models.SportsInactiveDay{
			Title:    "Non Operational Day",
			FsId:     fsId,
			Date:     inactiveDate,
			ReasonId: reasonId,
		}
		if err := DB.Create(record).Error; err != nil {
			log.Printf("func:MarkInActiveDayForNonOperational, error in creating sports inactive day record, err: %v", err)
			return err
		}
	}

	return nil
}

func (a *BookingData) MarkInactiveDayForFsIds(ctx context.Context, sportsInactiveDays []models.SportsInactiveDay) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	for _, record := range sportsInactiveDays {

		inactiveDate, err := time.Parse(formatYMD, record.Date.Format(formatYMD))
		if err != nil {
			log.Printf("func:MarkInactiveDayForFsIds, error in parsing time for date: %v and record: %v, err: %v", record.Date, record, err)
			return err
		}
		record.Date = inactiveDate

		if err := a.MarkDeletedFlagSportsInActiveDay(ctx, record.FsId, record.Date, record.UpdatedBy); err != nil {
			log.Printf("func:MarkInactiveDayForFsIds, error in deleting sports inactive day record for fs_id:%d and date: %v, err: %v", record.FsId, record.Date, err)
			return err
		}

		if err := DB.Create(&record).Error; err != nil {
			log.Printf("func:MarkInactiveDayForFsIds, error in creating sports inactive day record for fs_id:%d and date: %v, err: %v", record.FsId, record.Date, err)
			return err
		}
	}

	return nil
}

func (a *BookingData) BulkInsertInactiveDays(ctx context.Context, sportsInactiveDays []models.SportsInactiveDay) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	tx := DB.Begin()
	valueStrings := []string{}
	valueArgs := []interface{}{}
	for _, record := range sportsInactiveDays {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs, record.Title)
		valueArgs = append(valueArgs, record.FacilityId)
		valueArgs = append(valueArgs, record.FsId)
		valueArgs = append(valueArgs, record.Date)
		valueArgs = append(valueArgs, record.ReasonId)
		valueArgs = append(valueArgs, record.CreatedBy)
		valueArgs = append(valueArgs, record.UpdatedBy)
	}
	stmt := fmt.Sprintf("INSERT INTO sports_inactive_days (`title`,`facility_id`,`fs_id`,`date`, `reason_id`,`created_by`,`updated_by`) VALUES %s", strings.Join(valueStrings, ","))
	err := tx.Exec(stmt, valueArgs...).Error
	if err != nil {
		tx.Rollback()
		log.Println("Error in bulk inserting records into sports_inactive_days: %v", err)
		return err
	}
	err = tx.Commit().Error
	if err != nil {
		log.Println("func: BulkInsertInactiveDays, Error in committing Transaction for sports inactive days: ", err)
		return err
	}
	return nil
}

func (a *BookingData) BulkMarkDeletedFlagForInactiveDays(ctx context.Context, sportsInactiveDays []models.SportsInactiveDay) error {
	if len(sportsInactiveDays) == 0 {
		errMsg := "func: BulkMarkDeletedFlagForInactiveDays, Empty records"
		log.Printf("Invalid Request: %s", errMsg)
		return errors.New(errMsg)
	}
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	tx := DB.Begin()
	valueStrings := []string{}
	valueArgs := []interface{}{}
	for index, record := range sportsInactiveDays {
		inactiveDate, err := time.Parse(formatYMD, record.Date.Format(formatYMD))
		if err != nil {
			log.Printf("func: BulkMarkDeletedFlagForInactiveDays, error in parsing time for date: %v and record: %v, err: %v", record.Date, record, err)
			return err
		}
		record.Date = inactiveDate
		if index != len(sportsInactiveDays)-1 {
			valueStrings = append(valueStrings, "(fs_id = ? AND date = ?) OR ")
		} else {
			valueStrings = append(valueStrings, "(fs_id = ? AND date = ?)")
		}
		valueArgs = append(valueArgs, record.FsId)
		valueArgs = append(valueArgs, record.Date.Format(formatYMD))
	}
	stmt := fmt.Sprintf("UPDATE `sports_inactive_days` SET `deleted_flag` = 1, `updated_by` = %d  WHERE %s", sportsInactiveDays[0].UpdatedBy, strings.Join(valueStrings, ""))
	err := tx.Exec(stmt, valueArgs...).Error
	if err != nil {
		tx.Rollback()
		log.Println("func: BulkMarkDeletedFlagForInactiveDays, Error in marking deleting records from sports_inactive_days: %v", err)
		return err
	}
	err = tx.Commit().Error
	if err != nil {
		log.Println("func: BulkMarkDeletedFlagForInactiveDays, Error in committing Transaction for sports inactive days: ", err)
		return err
	}
	return nil
}

func (a *BookingData) MarkDeletedFlagSportsInActiveDay(ctx context.Context, fsId int32, date time.Time, updatedBy int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	inactiveDate := date.Format(formatYMD)

	response := DB.Model(&models.SportsInactiveDay{}).Where("fs_id = ? AND date = ? ", fsId, inactiveDate).Updates(map[string]interface{}{"deleted_flag": 1, "updated_by": updatedBy})
	if err := response.Error; err != nil {
		log.Printf("func:MarkDeletedFlagSportsInActiveDay, error in marking deleted flag on sports inactive day record for fs_id:%d, err: %v", fsId, err)
		return err
	}
	return nil
}

func (a *BookingData) GetInactiveDays(ctx context.Context, startDate int64, endDate int64, inactiveDays *[]models.SportsInactiveDay) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	var startDateStr, endDateStr string
	query := DB.Table("sports_inactive_days sid").
		Select("sid.date, sid.fs_id, fsm.description, fsm.sport_id, scr.reason_id, scr.reason").
		Joins("left join facility_sport_mappings fsm on fsm.fs_id = sid.fs_id").
		Joins("left join slots_closure_reasons scr on scr.reason_id = sid.reason_id").
		Where("sid.deleted_flag = 0")
	if startDate > 0 {
		startDate := time.Unix(startDate, 0)
		startDateStr = startDate.Format(formatYMD)
		query = query.Where("sid.date >= ?", startDateStr)
	}
	if endDate > 0 {
		endDate := time.Unix(endDate, 0)
		endDateStr = endDate.Format(formatYMD)
		query = query.Where("sid.date <= ?", endDateStr)
	}

	query = query.Find(&inactiveDays)
	if err := query.Error; err != nil {
		log.Printf("Error in fetching inactive days for dates: %s to %s, err: %v", startDateStr, endDateStr, err)
		return err
	}
	return nil
}

func (a *BookingData) GetStreamUrl(online_session_id int32, seconds int64, streamUrl *string) error {

	type UrlObj struct {
		FileUrl string `json:"file_url"`
	}
	var urlVal UrlObj

	query := a.Db.Table("session_video_m3u8_files svf").
		Select("svf.`file_url`").
		Joins("INNER JOIN online_sessions os ON svf.`session_video_id` = os.`session_video_id`").
		Where("os.`online_session_id` = ? AND svf.`seconds_to_serve_after` <= ? AND svf.`deleted_flag` = ?", online_session_id, seconds, 0).
		Order("svf.`seconds_to_serve_after` DESC").
		Limit(1).
		Find(&urlVal)

	if err := query.Error; err != nil {
		fmt.Println("Error in getting upcoming inactive days!")
		return err
	}

	*streamUrl = urlVal.FileUrl

	return nil
}
func (a *BookingData) GetRecentSlots(bookingCond *models.AllBooking, recentSlots *[]structs.RecentSlotDetails) error {

	response := a.Db.Table("all_bookings").
		Select("all_bookings.`slot_id`, online_sessions.`title_a`").
		Joins("inner join online_sessions on   online_sessions.`online_session_id` = all_bookings.`online_session_id`   ").
		Where(" all_bookings.`user_id` = ? AND  all_bookings.booking_date >= DATE_ADD(CURDATE(), INTERVAL -? DAY )", bookingCond.UserID, 7).
		Where("all_bookings.booking_cancelled = 0 AND all_bookings.booking_rescheduled = 0").
		Group("online_sessions.`title_a`").
		Order("count(online_sessions.`online_session_id`) desc").
		Find(&recentSlots)

	if err := response.Error; err != nil {
		fmt.Println("Error in getting recommended slots", err)
		return err
	}
	return nil
}

func (a *BookingData) GetRecommenedSession(onlineSessions *[]models.OnlineSessions, slots []structs.RecentSlotDetails) error {

	response := a.Db.Table("online_sessions").
		Select("online_sessions.*, s.*, sv.*, sp.`provider_real_name`, sp.`dp_url`, sp.`title` as `trainer_title`, sp.`about_trainer`,online_sessions.`capacity` - CASE WHEN SUM( ab.`booking_capacity` ) > 0 THEN sum(ab.`booking_capacity`) ELSE 0 END as remaining_capacity").
		Joins("INNER JOIN slots s ON  s.slot_id = online_sessions.slot_id").
		Joins("INNER JOIN session_videos sv ON  sv.session_video_id = online_sessions.session_video_id").
		Joins("LEFT JOIN service_providers sp ON sp.`provider_id` = sv.`provider_id`").
		Joins("LEFT JOIN all_bookings ab on ab.`online_session_id` = online_sessions.`online_session_id` AND ab.`booking_cancelled` = ?", 0)

	if len(slots) == 0 {
		response = response.Where("online_sessions.`is_recommended` = ? AND DATE_ADD(online_sessions.`session_time`, INTERVAL 30 MINUTE) > NOW() AND online_sessions.`deleted_flag` = 0 ", 1)
	} else {
		var slotIds []int32
		var groups []string
		for _, ele := range slots {
			slotIds = append(slotIds, ele.SlotID)
			groups = append(groups, ele.TitleA)
		}
		response = response.Where("online_sessions.`is_recommended` = 0 AND (online_sessions.`slot_id` in (?) OR online_sessions.`title_a` in (?)) AND DATE_ADD(online_sessions.`session_time`, INTERVAL 30 MINUTE) > NOW() AND online_sessions.`deleted_flag` = 0 ", slotIds, groups)
	}

	response = response.Group("online_sessions.`online_session_id`").Order("online_sessions.`session_time`, s.`start_hour` asc, s.`start_min` asc, online_sessions.`online_session_id`").Find(&onlineSessions)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) SaveOnlineSessionAttendance(osAtt *models.OnlineSessionAttendance, success *bool) error {

	if osAtt.OnlineSessionId > 0 && osAtt.StartTimestamp > 0 && osAtt.EndTimestamp > 0 {

		var dummyAtt models.OnlineSessionAttendance

		errIfExists := a.Db.Table("online_session_attendances").Where("online_session_id = ? AND booking_reference_number = ? AND start_timestamp = ? AND end_timestamp = ?", osAtt.OnlineSessionId, osAtt.BookingReferenceNumber, osAtt.StartTimestamp, osAtt.EndTimestamp).Scan(&dummyAtt).Error

		fmt.Println("Os Attendance already exist error -- ", errIfExists)

		if errIfExists != nil && gorm.IsRecordNotFoundError(errIfExists) {

			if err := a.Db.Create(&osAtt).Error; err != nil {
				fmt.Println(err)
				return err
			}
			*success = true
		} else {

			*success = false
		}
	} else {
		*success = false
	}

	return nil
}

func (a *BookingData) GetTimeSpentInSession(os_id int32, user_id int32, time_spent *int32) error {

	type TotalDuration struct {
		TotalDuration int32
	}
	var timeSpent TotalDuration

	response := a.Db.Table("online_session_attendances osa").
		Select("sum(osa.`duration`) as total_duration").
		Where("osa.`online_session_id`= ? AND osa.`user_id`= ? AND osa.`is_entry`= ?", os_id, user_id, 0).
		Find(&timeSpent)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	*time_spent = timeSpent.TotalDuration

	return nil
}

func (a *BookingData) UpdateAttendanceFlagForOnlineSessions(bookingRefNo string, success *bool) error {

	tx := a.Db.Table("all_bookings").
		Select("all_bookings.`attendance_flag`").
		Where("all_bookings.`booking_reference_number` = ?", bookingRefNo).
		Updates(map[string]interface{}{"attendance_flag": 1})

	if err := tx.Error; err != nil {
		fmt.Println(err)
		return err
	}

	fmt.Println("Attendace Flag updated for online sessions")

	if tx.RowsAffected > 0 {
		*success = true
	} else {
		*success = false
	}
	return nil
}

func (a *BookingData) RecordBookingFailure(rec *models.BookingFailureRecord) error {

	if rec.UserID > 0 && rec.SubscriptionID > 0 {

		if err := a.Db.Create(rec).Error; err != nil {
			fmt.Println(err)
			return err
		}
	}

	return nil
}

func (a *BookingData) NoShowRecordForRevert(record *[]structs.NoShowRevertTask, bookingIds []int32) error {
	response := a.Db.Table("no_show_penalty_applied_records nspa").
		Select("nspa.`subscription_id`, ab.product_id, nspa.`user_id`,count(*) as days_to_revert,GROUP_CONCAT(nspa.`booking_reference_number`) as booking_reference_numbers, ab.booking_time, ab.app_type, GROUP_CONCAT(nspa.booking_id) as booking_ids").
		Joins("INNER JOIN all_bookings ab ON ab.`booking_id` = nspa.`booking_id` AND ab.`no_show_applicable` = ?", 1).
		Where("nspa.`is_reverted`=? AND nspa.`booking_id` IN (?)", 0, bookingIds).
		Group("nspa.`subscription_id`").
		Find(&record)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}

	return nil

}
func (a *BookingData) UpdateNSPARecord(bookingIds []int32, revertedBy int64, reason string, declineFlag bool) error {
	currentTime := time.Now()
	response := a.Db.Table("no_show_penalty_applied_records").
		Where("no_show_penalty_applied_records.`booking_id` IN (?)", bookingIds).
		Updates(map[string]interface{}{"is_reverted": declineFlag != true, "reverted_by": revertedBy, "reason": reason, "last_updated": currentTime.Format("2006-01-02 15:04:05")})

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) UpdateNoShowRevertInBookings(bookingIds []int32) error {
	response := a.Db.Table("all_bookings").
		Where("all_bookings.`booking_id` IN (?)", bookingIds).
		Updates(map[string]interface{}{"no_show_applicable": 0})

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}
func (a *BookingData) NoShowPenaltyRecordGet(record *[]models.NoShowPenaltyAppliedRecord, cond models.NoShowPenaltyAppliedRecord) error {
	fmt.Println("-----cond-----", cond)
	response := a.Db.Table("no_show_penalty_applied_records ").
		Select("no_show_penalty_applied_records.*, ab.`booking_date`, u.`user_id`,u.`name`, psa.`checkin_time`, s.`slot_id`,s.`timing` ,f.`display_name`as facility_name, ubrq.`status` as query_status, ubrq.`other_reason`, rbrq.`description` as user_reason").
		Joins("INNER JOIN users u  ON u.`user_id`= no_show_penalty_applied_records.`user_id`").
		Joins("INNER JOIN all_bookings ab ON ab.`booking_id`= no_show_penalty_applied_records.`booking_id`").
		Joins("LEFT JOIN processed_sports_attendances psa ON psa.`booking_id`= ab.`booking_id`").
		Joins("LEFT JOIN user_booking_request_queries ubrq ON ubrq.`booking_id`=no_show_penalty_applied_records.`booking_id`")

	if (cond.IsReverted && !cond.DeclineFlag) || (!cond.IsReverted && cond.DeclineFlag) {
		response = response.Where("ubrq.`status`= ?", 1)
	}

	if cond.IsRaised {
		response = response.Where("rbrq.reason_id > 0 and ubrq.`status` = ?", 0)
	} else if !cond.IsRaised && !cond.IsReverted && !cond.DeclineFlag {
		response = response.Where("rbrq.reason_id IS NULL")
	}

	response = response.Joins("LEFT JOIN reasons_booking_request_queries rbrq ON rbrq.`reason_id`=ubrq.`reason_id`")

	if cond.StartDate > 0 && cond.EndDate > 0 {
		startDate := time.Unix(cond.StartDate, 0)
		startDateLocal, err := GetLocalDateTime(startDate)
		if err != nil {
			fmt.Println("time converison error bsd-- ", err)
			return err
		}

		endDate := time.Unix(cond.EndDate, 0)
		endDateLocal, err := GetLocalDateTime(endDate)
		if err != nil {
			fmt.Println("time converison error bed-- ", err)
			return err
		}
		response = response.Where("no_show_penalty_applied_records.`created_at` >= ? AND no_show_penalty_applied_records.`created_at` <= ?", startDateLocal, endDateLocal)

	}
	if len(cond.SlotIDs) > 0 {
		response = response.Joins("INNER JOIN slots s on s.`slot_id`= ab.`slot_id` AND s.`slot_id` IN (?)", cond.SlotIDs)
	} else {
		response = response.Joins("INNER JOIN slots s on s.`slot_id`= ab.`slot_id`")
	}

	if len(cond.FsIDs) > 0 {
		response = response.Joins("INNER JOIN facility_sport_mappings fsm ON fsm.`fs_id` = ab.`fs_id` AND fsm.`fs_id` IN (?)", cond.FsIDs)

	} else {
		response = response.Joins("INNER JOIN facility_sport_mappings fsm ON fsm.`fs_id` = ab.`fs_id`")
	}
	if len(cond.UserIDs) > 0 {
		response = response.Where("no_show_penalty_applied_records.`user_id` in (?)", cond.UserIDs)
	}
	if len(cond.BookingReferenceNumbers) > 0 && len(cond.BookingReferenceNumbers[0]) > 0 {
		response = response.Where("no_show_penalty_applied_records.`booking_reference_number` in (?)", cond.BookingReferenceNumbers)
	}
	if cond.Count > 0 {
		response = response.Offset(cond.Start - 1).
			Limit(cond.Count)
	}

	response = response.
		Joins("INNER JOIN facilities f ON f.`facility_id`=fsm.`facility_id`").
		Where("no_show_penalty_applied_records.`is_reverted`= ?", cond.IsReverted).
		Where(cond).Find(&record)

	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetTextDetails(data *[]models.TextBodyMappings, cond models.TextBodyMappings) error {

	response := a.Db.Table("text_body_mappings").
		Select("text_body_mappings.* ,text_bodies.text, text_bodies.image_id").
		Joins("INNER JOIN text_bodies on text_bodies.text_body_id = text_body_mappings.text_body_id").
		Where("text_body_mappings.`deleted_flag`=?", 0).
		Where("text_body_mappings.`slot_id`=? OR text_body_mappings.`all_slot_flag`=?", cond.SlotId, true).
		Where("text_body_mappings.`coaching_type`=? OR text_body_mappings.`all_coaching_flag`=?", cond.CoachingType, true).
		Where("text_body_mappings.`sport_id`=? OR text_body_mappings.`all_sport_flag`=?", cond.SportId, true)

	if cond.NoShowApplicable == false {
		response = response.Where("text_body_mappings.`no_show_applicable`=0")
	}
	if cond.FacilityType > 0 {
		response = response.Where("text_body_mappings.`facility_type` = ? OR text_body_mappings.`facility_type` = ?", cond.FacilityType, 4)
	}
	response = response.Where("text_body_mappings.`version_type`=? OR text_body_mappings.`version_type`=?", 3, cond.VersionType)

	response = response.Order("text_body_mappings.popularity DESC").Find(&data)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetTextSubBody(ctx context.Context, data *[]models.TextSubBody, bodyId int32, sportId int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("text_sub_bodies as tsb").
		Select("tsb.*").
		Joins("INNER JOIN text_sub_body_mappings as tsbm on tsbm.text_sub_body_id = tsb.text_sub_body_id").
		Where("text_body_id = ?", bodyId).
		Where("tsbm.`sport_id`=? OR tsbm.`all_sport_flag`=?", sportId, true).
		Order("tsbm.popularity DESC").Find(&data)
	if err := response.Error; err != nil {
		return err
	}
	return nil
}

func (a *BookingData) GetTitleDetails(titleDetails *[]models.TextTitleMappings, titleMappingIds []int32) error {
	response := a.Db.Table("text_title_mappings").
		Select("text_title_mappings.*, text_titles.title ").
		Joins(" INNER JOIN text_titles  on text_title_mappings.title_id = text_titles.title_id ").
		Where("text_title_mappings.`deleted_flag`=(?) AND text_title_mappings.text_title_mapping_id IN (?)", 0, titleMappingIds).
		Order("text_title_mappings.popularity DESC").Find(&titleDetails)
	if err := response.Error; err != nil {
		fmt.Println(err)
		return err
	}
	return nil

}

func (a *BookingData) GetProductFsId(id int32, fsIds *[]int32) error {
	type FsIds struct {
		FSId int32 `json:"fs_id"`
	}
	var fsId []FsIds
	query := a.Db.Table("products p").
		Select("fsm.fs_id").
		Joins("INNER JOIN product_facility_sport_mappings pfsm ON pfsm.`product_id` = p.`product_id` and pfsm.product_id = ?", id).
		Joins("INNER JOIN facility_sport_mappings fsm on fsm.fs_id = pfsm.fs_id AND fsm.`operation_status` IN (?)", 1).
		Group("fsm.`fs_id`").
		Find(&fsId)
	if err := query.Error; err != nil {
		log.Println("Could not fetch necessary Product data --- ", err)
		return err
	}
	for _, ele := range fsId {
		*fsIds = append(*fsIds, ele.FSId)
	}

	return nil
}

func (a *BookingData) GetFacilitySportForFsId(fsIds []int32, facilitySportDetails *[]models.FacilitySportDetails) error {
	query := a.Db.Table("facility_sport_mappings fsm").
		Select("f.facility_id, f.display_name, f.display_picture, f.short_name, f.timing_description, f.display_address, s.name, s.sport_name, s.sport_id, s.icon, s.icon_sports_app, s.popularity, fsm.latitude, fsm.longitude, fsm.fs_id").
		Joins("INNER JOIN facilities f on fsm.facility_id = f.facility_id").
		Joins("INNER JOIN sports s on s.sport_id = fsm.sport_id").
		Where("fsm.operation_status = 1 AND fsm.fs_id in (?)", fsIds).
		Find(&facilitySportDetails)
	if err := query.Error; err != nil {
		log.Println("Could not fetch necessary details --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) GetActiveSlotsForFsId(fsIds []int32, activeSlotFsCondition *structs.AllFsActiveSlots, activeSlotFs *[]models.FacilitySportDetails) error {

	dayOfWeek := int(activeSlotFsCondition.Date.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}
	dateBookingStr := activeSlotFsCondition.Date.Format(formatYMD)

	response := a.Db.Table("fs_active_slots").
		Select("fs_active_slots.date, fs_active_slots.slot_id, fs_active_slots.timing, fs_active_slots.fs_id, fs_active_slots.slot_datetime, fs_active_slots.remaining_capacity").
		Joins("INNER JOIN slots s ON s.`slot_id` = fs_active_slots.`slot_id`").
		Joins("INNER JOIN facility_sport_capacity_slots fscs ON fscs.`slot_id` = fs_active_slots.`slot_id` and fscs.`fs_id` = fs_active_slots.`fs_id`").
		Where("DATE(fs_active_slots.`date`) >= ? AND fs_active_slots.`slot_datetime` > NOW() - INTERVAL 15 MINUTE", dateBookingStr).
		Where("fs_active_slots.fs_id in (?)", fsIds).
		Where("fscs.`day_of_week` = ?", dayOfWeek).
		Where("fs_active_slots.active = 1").
		Find(&activeSlotFs)

	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) GetBookingIdInfo(ctx context.Context, slotInfo *models.FacilitySportDetails, bookingId int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("all_bookings ab").
		Select("f.facility_id, f.display_name, psa.checkin_time, f.short_name, f.display_address, s.name, s.sport_name, s.icon, s.icon_sports_app, ab.attendance_flag, ab.booking_reference_number, ab.booking_id, sl.timing, ab.no_show_applicable, ab.booking_time, ab.booking_cancelled, nspar.no_show_peanlty_id, nspar.is_reverted").
		Joins("LEFT JOIN no_show_penalty_applied_records nspar on nspar.booking_id = ab.booking_id").
		Joins("INNER JOIN facility_sport_mappings fsm on fsm.fs_id = ab.fs_id").
		Joins("INNER JOIN facilities f on fsm.facility_id = f.facility_id").
		Joins("INNER JOIN sports s on s.sport_id = fsm.sport_id").
		Joins("INNER JOIN slots sl on ab.slot_id = sl.slot_id").
		Joins("LEFT JOIN processed_sports_attendances psa on psa.booking_id = ab.booking_id").
		Where("ab.booking_id = ?", bookingId).
		Find(&slotInfo)

	if err := query.Error; err != nil {
		log.Println("Could not fetch necessary details --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) GetSwimmingslotInfo(ctx context.Context, slotInfo *models.FacilitySportDetails, bRN string) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("user_seals_bookings usb").
		Select("f.facility_id, f.display_name, psa.checkin_time, f.short_name, f.display_address, s.name, s.sport_name, s.icon, s.icon_sports_app, usb.attendance_flag, usb.booking_reference_number, ss.timings as timing, usb.booking_time, usb.cancelled_flag as booking_cancelled, nspar.no_show_peanlty_id, nspar.is_reverted").
		Joins("LEFT JOIN no_show_penalty_applied_records nspar on nspar.booking_reference_number = usb.booking_reference_number").
		Joins("INNER JOIN swimming_locations swl on swl.swimming_location_id = usb.swimming_location_id").
		Joins("INNER JOIN facilities f on swl.facility_id = f.facility_id").
		Joins("INNER JOIN facility_sport_mappings fsm on fsm.facility_id = f.facility_id").
		Joins("INNER JOIN sports s on s.sport_id = fsm.sport_id").
		Joins("INNER JOIN swimming_slots ss on usb.slot_id = ss.slot_id").
		Joins("LEFT JOIN processed_sports_attendances psa on psa.booking_reference_number = usb.booking_reference_number").
		Where("s.sport_id = 3 AND usb.booking_reference_number = ?", bRN).
		Find(&slotInfo)

	if err := query.Error; err != nil {
		log.Println("Could not fetch necessary details --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) UserNoShowRevertQueryInfo(ctx context.Context, userQueries *[]models.UserBookingRequestQueries, bookingIds []int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("user_booking_request_queries ubrq").
		Select("ubrq.*, rbrq.description").
		Joins("INNER JOIN reasons_booking_request_queries rbrq on rbrq.reason_id = ubrq.reason_id").
		Where("ubrq.query_type_id = 1 AND ubrq.deleted_flag = 0 AND ubrq.booking_id IN (?) ", bookingIds). //for no show 1 (future expansion)
		Find(&userQueries)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch necessary details --- ", err)
		return err
	}
	return nil
}

func (a *BookingData) QueryRevertStatus(ctx context.Context, reverted *int32, bookingId int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	type IsReverted struct {
		IsReverted int32 `json:"is_reverted"`
	}

	var status IsReverted
	query := DB.Table("no_show_penalty_applied_records nspar").
		Select("nspar.is_reverted").
		Where("nspar.booking_id = ?", bookingId).
		Find(&status)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch if reverted or not --- ", err)
		return err
	}

	*reverted = status.IsReverted
	return nil
}

func (a *BookingData) GetReasonsForUserQueries(ctx context.Context, reasons *[]models.UserBookingRequestQueries, queryTypeId int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("reasons_booking_request_queries rbrq").
		Select("rbrq.*").
		Where("rbrq.query_type_id = ? AND deleted_flag = 0", queryTypeId).
		Find(&reasons)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not reasons for noshow --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) UserFeedbackForBooking(ctx context.Context, condition string, feedbackData *[]models.SportsFeedbackUserRating) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	query := DB.Table("sports_feedback_responses sfr").
		Select("sfr.rating_id, sfo.title, sfro.feedback_option_id, sfo.feedback_category_id").
		Joins("LEFT JOIN sports_feedback_response_options sfro on sfro.feedback_response_id = sfr.feedback_response_id AND sfro.deleted_flag = 0").
		Joins("LEFT JOIN sports_feedback_options sfo on sfo.feedback_option_id = sfro.feedback_option_id").
		Where("sfr.deleted_flag = 0 AND sfr.booking_reference_number = ? ", condition).
		Find(&feedbackData)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not reasons for noshow --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) StoreUserQuery(ctx context.Context, queryData *models.UserBookingRequestQueries) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	storeUserQueryData := *queryData
	if err := DB.Where("user_booking_request_queries.`booking_id` = ? AND user_booking_request_queries.deleted_flag != 1", queryData.BookingId).Assign(queryData).FirstOrCreate(&storeUserQueryData).Error; err != nil {
		log.Println("Could not create or update User Query ---", err)
		return err
	}

	return nil
}

func (a *BookingData) GetSealsUpcomingBooking(userId int32, usbs *[]models.UserSealsBooking) error {
	response := a.Db.Table("user_seals_bookings usb").
		Select("usb.*").
		Where(" usb.`user_id` = ? AND usb.`booking_time`>=CURDATE() AND usb.`rescheduled_flag` = 0 AND usb.`cancelled_flag` = 0 ", userId).
		Find(usbs)
	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) UserNoShowRevertedInMonth(ctx context.Context, conditions structs.NoShowRevertTask, autoRevertFlag int32, subscriptionStartDate time.Time, queryRevertedCount *int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	type QueryRevertedCount struct {
		RevertedCount int32 `json:"reverted_count"`
	}

	var count QueryRevertedCount

	t := conditions.BookingTime

	totalDaysfromSubsStartDate := int(t.Sub(subscriptionStartDate).Hours() / 24)
	daysAfterCurrentPeriodFirstDay := totalDaysfromSubsStartDate % AutoReversalRenewalPeriod

	currentPeriodFirstDay := t.AddDate(0, 0, -daysAfterCurrentPeriodFirstDay)
	currentPeriodFirstDay = time.Date(currentPeriodFirstDay.Year(), currentPeriodFirstDay.Month(), currentPeriodFirstDay.Day(), subscriptionStartDate.Hour(), subscriptionStartDate.Minute(), subscriptionStartDate.Second(), subscriptionStartDate.Nanosecond(), subscriptionStartDate.Location())
	currentPeriodLastday := currentPeriodFirstDay.AddDate(0, 0, AutoReversalRenewalPeriod).Add(time.Nanosecond * -1)

	query := DB.Table("no_show_penalty_applied_records nspar").
		Joins("INNER JOIN user_booking_request_queries ubrq ON ubrq.booking_id = nspar.booking_id").
		Joins("INNER JOIN reasons_booking_request_queries rbrq ON rbrq.reason_id = ubrq.reason_id").
		Select("Count(*) reverted_count").
		Where("is_reverted = 1 AND user_id = ? AND subscription_id = ? AND rbrq.auto_revert_flag = ? AND nspar.deleted_flag = 0 AND nspar.created_at Between ? AND ?", conditions.UserId, conditions.SubscriptionId, autoRevertFlag, currentPeriodFirstDay, currentPeriodLastday).
		Group("user_id, subscription_id").
		Find(&count)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not reasons for noshow --- ", err)
		return err
	}

	*queryRevertedCount = count.RevertedCount
	return nil
}

func (a *BookingData) NoShowRevertQueue(ctx context.Context, data *pb.UserBookingRequestQueries) error {

	bodyBytes, _ := json.Marshal(data)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "0",
		},
		Body: bodyBytes,
	}

	if err := a.Broker.Publish(noShowTopic, msg); err != nil {
		log.Printf("[pub] failed: %v", err)
	} else {
		log.Println("[pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) GetReasonInfo(ctx context.Context, reasonId int32, reasonInfo *pb.UserBookingRequestQueries) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	query := DB.Table("reasons_booking_request_queries rbrq").
		Select("rbrq.description, rbrq.reason_id, rbrq.auto_revert_flag, rbrq.cx_ticket_flag, rbrq.query_type_id").
		Where("reason_id = ? AND deleted_flag = 0", reasonId).
		Find(&reasonInfo)
	if err := query.Error; err != nil {
		log.Println("Could not find reason information--- ", err)
		return err
	}
	return nil
}

func (a *BookingData) PublishFsIdForSlotPopulation(ctx context.Context, data *pb.FacilitySportDetails) error {

	bodyBytes, _ := json.Marshal(data)
	msg := &broker.Message{
		Header: map[string]string{
			"id": "0",
		},
		Body: bodyBytes,
	}

	if err := a.Broker.Publish(slotPopulationTopic, msg); err != nil {
		log.Printf("PublishFsIdForSlotPopulation: [pub] failed for data: %s to topic: %s with err: %v", string(msg.Body), slotPopulationTopic, err)
	} else {
		log.Println("PublishFsIdForSlotPopulation: [pub] pubbed message:", string(msg.Body))
	}

	return nil
}

func (a *BookingData) UpdateQueryStatusNoShow(queryData *models.UserBookingRequestQueries) error {

	storeUserQueryData := *queryData

	response := a.Db.Table("user_booking_request_queries").
		Where("user_booking_request_queries.`booking_id` = ? AND user_booking_request_queries.deleted_flag != 1", queryData.BookingId).Assign(queryData).FirstOrCreate(&storeUserQueryData)

	if err := response.Error; err != nil {
		log.Println(err)
		return err
	}

	return nil
}

func (a *BookingData) SetInRedis(key string, value string, sessionTime time.Duration) error {

	redisPrepend := config.Get("codeIndependent", "redisAPrepend").String("")
	redisKey := redisPrepend + "_" + key

	if err := a.Client.Set(redisKey, value, sessionTime).Err(); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func (a *BookingData) GetFromRedis(key string, value *string) error {

	redisPrepend := config.Get("codeIndependent", "redisAPrepend").String("")
	redisKey := redisPrepend + "_" + key

	output, err := a.Client.Get(redisKey).Result()
	if err != nil && err != redis.Nil {
		log.Println("Error in getting redis output", err)
	}

	*value = output
	return nil
}
func (a *BookingData) UserNoShowPenaltyPastBuffer(ctx context.Context, conditions structs.NoShowRevertTask, pastQueryData *[]models.UserBookingRequestQueries) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	t := conditions.BookingTime
	firstDay := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.Local)
	lastday := firstDay.AddDate(0, 1, 0).Add(time.Nanosecond * -1)

	query := DB.Table("no_show_penalty_applied_records nspar").
		Select("nspar.reason, nspar.created_at, ubrq.other_reason, rbrq.description").
		Joins("LEFT JOIN user_booking_request_queries ubrq on ubrq.booking_id = nspar.booking_id AND ubrq.deleted_flag = 0").
		Joins("LEFT JOIN reasons_booking_request_queries rbrq on rbrq.reason_id = ubrq.reason_id").
		Where("nspar.user_id = ? AND nspar.subscription_id = ? AND nspar.deleted_flag = 0 AND nspar.created_at Between ? AND ?", conditions.UserId, conditions.SubscriptionId, firstDay, lastday).
		Order("nspar.created_at desc").
		Find(&pastQueryData)
	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not reasons for noshow --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) FacilityVisitedCountGet(ctx context.Context, visitedPeople *[]models.FacilityVisitedCounts, days int32) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings").
		Select("count(*) as people_count, f.`facility_id`, ? as days", days).
		Joins("INNER JOIN `facility_sport_mappings` fsm ON all_bookings.`fs_id` = fsm.`fs_id`").
		Joins("INNER JOIN `facilities` f ON fsm.`facility_id` = f.`facility_id` and all_bookings.`attendance_flag`=1 and DATEDIFF(CURDATE(), all_bookings.`booking_date`) < ?", days).
		Group("f.`facility_id`").
		Find(&visitedPeople)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch count of people with successful bookings")
		return err
	}
	return nil
}

func (a *BookingData) AddFacilityVisitedCount(ctx context.Context, visitedPeople *models.FacilityVisitedCounts) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	visitedPeopleData := *visitedPeople
	if err := DB.Where("facility_id=?", visitedPeople.FacilityID).Assign(visitedPeople).FirstOrCreate(&visitedPeopleData).Error; err != nil {
		log.Println("Could not create or update Query for the facility id---", visitedPeopleData.FacilityID)
		return err
	}
	return nil
}

func (a *BookingData) GetBrnFromBookingId(bookingId int32, brn *models.AllBooking) error {

	query := a.Db.Table("all_bookings ab").
		Select("ab.booking_reference_number").
		Where("ab.booking_id = ?", bookingId).
		Find(&brn)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not get booking reference number --- ", err)
		return err
	}

	return nil
}

func (a *BookingData) FetchCountByFacilityId(facilityId int32, days int32, count *models.FacilityVisitedCounts) error {

	query := a.Db.Table("facility_visited_counts fvc").
		Select("fvc.people_count").
		Where("fvc.facility_id = ? AND fvc.days = ?", facilityId, days).
		Find(&count)

	if err := query.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not get count for the facility--- ", err)
		return err
	}
	return nil
}

func (a *BookingData) GetFeedbacksForBRN(ctx context.Context, bookingReferenceNumber string) ([]*models.SportsFeedbackResponse, error) {
	feedbacks := make([]*models.SportsFeedbackResponse, 0)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("sports_feedback_responses").
		Select("*").
		Where("deleted_flag = 0 and booking_reference_number = ?", bookingReferenceNumber).
		Find(&feedbacks)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("error in getting Bookings By ReferenceNumber, brn: %s, Error: %v", bookingReferenceNumber, err)
		return feedbacks, err
	}
	return feedbacks, nil
}

func (a *BookingData) GetRatingsFacilityWise(ctx context.Context, rating *float64, facilityCondition *models.FacilityRatings) error {

	// client call
	if facilityCondition.FacilityId != 0 {
		cacheKey := getCacheKeyForFacilityWiseRating(facilityCondition.FacilityId)
		fromCache, err := a.Client.Get(cacheKey).Result()
		if err != nil {
			log.Printf("error in getting rating from cache for facilityId: %d, ERROR: %v", facilityCondition.FacilityId, err)
			return err
		}
		gotRating, parseErr := strconv.ParseFloat(fromCache, 64)
		if parseErr != nil {
			log.Println("error in converting to float for fetched cache rating value, ERROR: %v", parseErr)
			return parseErr
		}
		*rating = gotRating
		return nil
	} else {
		// cron call
		var facilityRatings []models.FacilityRatings

		DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
		response := DB.Table("all_bookings").
			Select("sfr.`rating_id` as rating, f.`facility_id`").
			Joins("INNER JOIN `facility_sport_mappings` fsm ON all_bookings.`fs_id` = fsm.`fs_id`").
			Joins("INNER JOIN `facilities` f ON fsm.`facility_id` = f.`facility_id`").
			Joins("INNER JOIN `sports_feedback_responses` sfr ON sfr.`booking_id` = all_bookings.`booking_id`").
			Where("f.`operation_status` = 1").
			Order("all_bookings.`booking_date` desc").
			Find(&facilityRatings)

		if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
			log.Println("Could not fetch facility wise ratings")
			return err
		}

		facilityRatingsMap := make(map[int32][]int32)

		for _, ratingModel := range facilityRatings {
			if len(facilityRatingsMap[ratingModel.FacilityId]) < facilityRatingCountUpperLimit {
				facilityRatingsMap[ratingModel.FacilityId] = append(facilityRatingsMap[ratingModel.FacilityId], ratingModel.Rating)
			}
		}

		cacheDuration := time.Duration(7 * 24 * time.Hour)

		for facilityId, ratingArr := range facilityRatingsMap {

			cacheKey := getCacheKeyForFacilityWiseRating(facilityId)
			log.Println("setting cache key for : ", cacheKey)
			ratingArrSize := int32(len(ratingArr))

			if ratingArrSize < facilityRatingCountLowerLimit {
				log.Println("Too few ratings..., skipping rating computation for facility id : ", facilityId)
			} else {
				sumOfRating := int32(0)
				for _, ratingValue := range ratingArr {
					sumOfRating += ratingValue
				}
				avgCmoputedRating := float64(sumOfRating) / float64(ratingArrSize)
				avgCmoputedPreciseRating := float64(int(avgCmoputedRating*10)) / 10
				if err := a.Client.Set(cacheKey, avgCmoputedPreciseRating, cacheDuration).Err(); err != nil {
					log.Printf("error in caching facility rating data for facility id %d, ERROR : %v", facilityId, err)
					return err
				}
			}
		}
	}
	return nil
}

func (a *BookingData) BookingCountBySport(ctx context.Context, bookingCondition *models.AllBooking, bookingCountBySport *[]models.AllBooking) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings").
		Select("count(all_bookings.booking_id) as count, s.sport_id, s.name as sport_name").
		Joins("INNER JOIN facility_sport_mappings fsm on fsm.fs_id = all_bookings.fs_id").
		Joins("INNER JOIN sports s on s.sport_id = fsm.sport_id").
		Where("all_bookings.booking_cancelled = 0").
		Where(bookingCondition).
		Group("fsm.sport_id").
		Order("count(all_bookings.booking_id) desc").
		Find(&bookingCountBySport)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Println("Could not fetch booking count")
		return err
	}
	return nil
}

func (a *BookingData) GetPacsIdFromPacsInfo(ctx context.Context, payload []structs.FsPaSlotDate) ([]structs.FsPaSlotDate, error) {
	whereClauses := make([]string, 0)
	whereClauseParams := make([]interface{}, 0)
	pacsDetails := make([]structs.FsPaSlotDate, 0)

	for _, val := range payload {
		clause := "(fs_id = ? AND slot_id = ? AND day_of_week = ? AND pa_id = ? AND product_arena_category_id = ?)"
		whereClauses = append(whereClauses, clause)
		whereClauseParams = append(whereClauseParams, []interface{}{val.FSID, val.SlotID, val.DayOfWeek, val.PAID, val.ProductArenaCategoryId}...)
	}
	finalWhereClause := strings.Join(whereClauses, " OR ")

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("play_arena_capacity_slots pacs").
		Select("pacs_id, fs_id, slot_id, pa_id, product_arena_category_id").
		Where(finalWhereClause, whereClauseParams...).
		Find(&pacsDetails)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("unable to fetch pacs id from pacs info | Error: %v", err)
		return pacsDetails, err
	}
	return pacsDetails, nil
}

func getCacheKeyForFacilityWiseRating(facilityId int32) string {
	return fmt.Sprintf("rating_%d_v1", facilityId)
}

func (a *BookingData) GetTrialAttendedBookingsOfLastNthDay(ctx context.Context, nthDay int32) ([]*models.AllBooking, error) {
	bookings := make([]*models.AllBooking, 0)

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings ab").
		Select("ab.*").
		Where("DATE(ab.`booking_time`)=DATE_ADD(CURDATE(), INTERVAL -? DAY) AND ab.`subscription_id` = ? AND ab.`attendance_flag` = ? AND ab.`booking_cancelled` = ?", nthDay, 0, 1, 0).
		Group("ab.`user_id`").
		Find(&bookings)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("func:GetTrialAttendedBookingsOfLastNthDay, unable to fetch bookings of last %d th day, error: %v", nthDay, err)
		return bookings, err
	}
	return bookings, nil
}

func (a *BookingData) AddEditPlayArenaCapacitySlots(ctx context.Context, req *pb.AddEditCapacitySlotRequest) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	endDates, err1 := ptypes.Timestamp(req.EndDate)
	endDate, err2 := GetLocalDateTime(endDates)
	startDates, err3 := ptypes.Timestamp(req.StartDate)
	startDate, err4 := GetLocalDateTime(startDates)
	if err1 != nil || err2 != nil || err3 != nil || err4 != nil {
		log.Printf("Error in converting Timestamp to time, err1:%v, err2:%v, err3:%v, err4:%v", err1, err2, err3, err4)
		return fmt.Errorf("Error in function AddEditPlayArenaCapacitySlots when converting timestamp to time")
	}

	startDateStr, err := time.Parse(formatYMD, startDate.Format(formatYMD))
	if err != nil {
		log.Printf("func:AddEditPlayArenaCapacitySlots, error in parsing time")
		return err
	}
	capacitySlots := make([]*models.PlayArenaCapacitySlot, 0)
	for _, dayOfWeek := range req.DayOfWeeks {
		for _, slotId := range req.SlotIds {

			var SlotData []models.Slot
			if err := a.GetSlotDetails(slotId, &SlotData); err != nil {
				log.Printf("Error in getting slot details for slotID:%d, err:%v", slotId, err)
				tx.Rollback()
				return err
			}
			if len(SlotData) == 0 {
				tx.Rollback()
				return fmt.Errorf("No slot details for slotID:%d", slotId)
			}
			for _, paId := range req.PaIds {
				response := tx.Table("play_arena_capacity_slots").
					Select("*").
					Where("fs_id = ? AND slot_id = ? AND day_of_week = ? AND product_arena_category_id = ? AND pa_id = ?", req.FsId, slotId, dayOfWeek, req.ProductArenaCategoryId, paId).
					Find(&capacitySlots)

				if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
					log.Printf("Error in getting all PACS entries for FsID: %d,slotId: %d,DayOfWeek:%d,ProductArenaCategoryId:%d,PaId:%d, Error:%v", req.FsId, slotId, dayOfWeek, req.ProductArenaCategoryId, paId, err)
					tx.Rollback()
					return err
				}
				if len(capacitySlots) > 0 {
					//edit
					editQuery := tx.Table("play_arena_capacity_slots").
						Where("fs_id = ? AND slot_id = ? AND day_of_week = ? AND product_arena_category_id = ? AND pa_id = ?", req.FsId, slotId, dayOfWeek, req.ProductArenaCategoryId, paId).
						UpdateColumn("capacity_individual", req.CapacityIndividual).
						UpdateColumn("capacity_total", req.CapacityIndividual).
						UpdateColumn("active_status", req.ActiveStatus).
						UpdateColumn("start_date", startDateStr).
						UpdateColumn("waitlist_flag", req.WaitlistFlag).
						UpdateColumn("capacity_waitlist", req.CapacityWaitlist)

					if err := editQuery.Error; err != nil {
						log.Printf("Error in updating PACS entries for FsID: %d,slotId: %d,DayOfWeek:%d,ProductArenaCategoryId:%d,PaId:%d, Error:%v", req.FsId, slotId, dayOfWeek, req.ProductArenaCategoryId, paId, err)
						tx.Rollback()
						return err
					}
				} else {
					//add
					var data models.PlayArenaCapacitySlot
					data.FSID = req.FsId
					data.PAID = paId
					data.SlotID = slotId
					data.ProductArenaCategoryId = req.ProductArenaCategoryId
					data.DayOfWeek = dayOfWeek
					data.CapacityIndividual = req.CapacityIndividual
					data.CapacityGroup = 1
					data.ActiveStatus = req.ActiveStatus
					data.CapacityTotal = req.CapacityIndividual
					data.StartDate = startDate
					data.EndDate = endDate
					data.Timing = SlotData[0].Timing
					data.WaitlistFlag = req.WaitlistFlag
					data.CapacityWaitlist = req.CapacityWaitlist

					if err := tx.Create(&data).Error; err != nil {
						log.Printf("Error in storing PACS entry for FsID: %d,slotId: %d,DayOfWeek:%d,ProductArenaCategoryId:%d,PaId:%d, Error:%v", req.FsId, slotId, dayOfWeek, req.ProductArenaCategoryId, paId, err)
						tx.Rollback()
						return err
					}
				}
				if req.ActiveStatus == 1 {
					oldEntries := tx.Table("play_arena_capacity_slots").
						Select("*").
						Where("fs_id = ? AND active_status = 1 AND slot_id = ? AND day_of_week = ? AND pa_id = ?", req.FsId, slotId, dayOfWeek, paId).
						Find(&capacitySlots)

					if err := oldEntries.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
						log.Printf("Error in getting PACS entries for FsID: %d,slotId: %d,DayOfWeek:%d,PaId:%d, Error:%v", req.FsId, slotId, dayOfWeek, paId, err)
						tx.Rollback()
						return err
					}
					for _, data := range capacitySlots {
						if data.ProductArenaCategoryId == req.ProductArenaCategoryId {
							continue
						}
						editQuery := tx.Table("play_arena_capacity_slots").
							Where("fs_id = ? AND slot_id = ? AND day_of_week = ? AND product_arena_category_id = ? AND pa_id = ?", data.FSID, data.SlotID, data.DayOfWeek, data.ProductArenaCategoryId, data.PAID).
							UpdateColumn("active_status", 0)
						if err := editQuery.Error; err != nil {
							log.Printf("Error in Updating Status for FsId: %d,PaID:%d,SlotId:%d,DayOfWeek:%d,ProductArenaCategoryId:%d, Error:%v", data.FSID, data.PAID, data.SlotID, data.DayOfWeek, data.ProductArenaCategoryId, err)
							tx.Rollback()
							return err
						}
					}
				}
			}
			var slotCapacity models.FacilitySportCapacitySlot
			response := tx.Table("play_arena_capacity_slots").
				Select("sum(capacity_individual) as capacity_individual, sum(capacity_group) as capacity_group, sum(capacity_total) as capacity_total, fs_id, slot_id, day_of_week, active_status, timing").
				Where("fs_id = ? AND slot_id = ? AND day_of_week = ? AND active_status = 1", req.FsId, slotId, dayOfWeek).
				Group("fs_id, slot_id, day_of_week").
				Find(&slotCapacity)
			if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
				log.Printf("Error in getting PACS entry for FsID: %d,slotId: %d,DayOfWeek:%d, Error:%v", req.FsId, slotId, dayOfWeek, err)
				tx.Rollback()
				return err
			}
			slotCapacity.FSID = req.FsId
			slotCapacity.SlotID = slotId
			slotCapacity.DayOfWeek = dayOfWeek
			slotCapacity.StartDate = startDate
			slotCapacity.EndDate = endDate
			slotCapacity.Timing = SlotData[0].Timing

			if err := tx.Where("facility_sport_capacity_slots.`fs_id` = ? AND facility_sport_capacity_slots.slot_id = ? AND facility_sport_capacity_slots.day_of_week = ?", req.FsId, slotId, dayOfWeek).
				Assign(slotCapacity).FirstOrCreate(&slotCapacity).Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
				log.Printf("Could not create or update facility_sport_capacity_slots for fsid:%d, slotId:%d, dow:%d, err:%v", req.FsId, slotId, dayOfWeek, err)
				tx.Rollback()
				return err
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (a *BookingData) GetBookingSessionCount(ctx context.Context, userIds []int32, bookings *[]models.AllBooking) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.ROdb)
	response := DB.Table("all_bookings ab").
		Select("ab.booking_id, ab.user_id, ab.booking_time, fsm.sport_id").
		Joins("inner join facility_sport_mappings fsm on fsm.fs_id = ab.fs_id").
		Where("ab.attendance_flag = 1 or (ab.attendance_flag = 0 and ab.booking_cancelled = 0 and ab.booking_rescheduled = 0 and DATE_ADD(ab.`booking_time`, INTERVAL 30 MINUTE) >= CURRENT_TIMESTAMP)")
	if len(userIds) > 0 {
		var userIdsArr []string
		for _, element := range userIds {
			userIdsArr = append(userIdsArr, strconv.Itoa(int(element)))
		}
		response = response.Where("ab.user_id in (" + strings.Join(userIdsArr, ",") + ")")
	}
	response = response.Order("fsm.sport_id, ab.user_id, ab.booking_time asc").Find(&bookings)
	if err := response.Error; err != nil {
		log.Printf("Error in fetching booking session count: %v", err)
		return err
	}
	return nil
}

func (a *BookingData) CreateOrUpdateBookingCoachMapping(ctx context.Context, bookingCoachMapping *models.BookingAssignedCoaches) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	updateMap := *bookingCoachMapping
	if err := DB.Where("booking_assigned_coaches.`booking_id` = ? ", bookingCoachMapping.BookingId).Assign(&updateMap).FirstOrCreate(&bookingCoachMapping).Error; err != nil {
		log.Printf("Error in creating or updating booking coach mapping: %v", err)
		return err
	}
	return nil
}

func (a *BookingData) BatchGetAssignedCoachesByBookingIds(ctx context.Context, bookingIds []int32, assignedCoaches *[]structs.BookingAssignedCoaches) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.ROdb)
	response := DB.Table("booking_assigned_coaches bac").
		Select("bac.booking_id, bac.coach_user_id, bac.assigned_by, u1.name as coach_name, u2.name as assignee_name").
		Joins("inner join users u1 on u1.user_id = bac.coach_user_id").
		Joins("inner join users u2 on u2.user_id = bac.assigned_by")
	if len(bookingIds) > 0 {
		response = response.Where("bac.booking_id in (?)", bookingIds)
	}
	response = response.Find(&assignedCoaches)
	if err := response.Error; err != nil {
		log.Printf("Error in fetching assigned coaches details for booking ids: %q, err: %v", bookingIds, err)
		return err
	}
	return nil
}

func (a *BookingData) UpsertFMBookingRemark(ctx context.Context, bookingRemark *models.AllBooking) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings").Where("booking_id = (?)", bookingRemark.BookingID).UpdateColumn("remark", bookingRemark.Remark)
	if err := response.Error; err != nil {
		log.Printf("Error in updating booking remark details for booking id: %d, err: %v", bookingRemark.BookingID, err)
		return err
	}
	return nil
}

func (a *BookingData) GetTodayUpcomingPAActiveSlotsForFsId(ctx context.Context, fsId int32, activeSlotPa *[]models.PAActiveSlot) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	response := DB.Table("pa_active_slots pas").
		Select("pas.*").
		Where("DATE(pas.`slot_datetime`) = CURDATE() AND pas.`slot_datetime` >= CURDATE() AND pas.`active` = 1 AND pas.`fs_id` = ?", fsId).
		Group("pas.`pacs_id`").
		Find(&activeSlotPa)

	if err := response.Error; err != nil {
		log.Println("func:GetTodayUpcomingPAActiveSlotsForFsId, error in upcoming pa_active_slots", err)
		return err
	}

	return nil
}

func (a *BookingData) BatchGetConsecutiveBookingFlagMap(ctx context.Context, bookingIds []int32, consecutiveFlags *[]structs.ConsecutiveBooking) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.ROdb)
	response := DB.Table("all_bookings abs").
		Select("bookings.booking_id, count(abs.booking_id) as consecutive_flag").
		Joins("INNER JOIN (SELECT user_id, fs_id, slots.start_hour, booking_date, booking_id FROM `all_bookings` ab INNER JOIN slots ON ab.slot_id = slots.slot_id where ab.booking_id in (?)) as bookings on bookings.user_id = abs.user_id and bookings.fs_id = abs.fs_id and abs.booking_date = bookings.booking_date", bookingIds).
		Joins("INNER JOIN slots ON slots.slot_id = abs.slot_id").
		Where("slots.start_hour = bookings.start_hour + 1 and abs.booking_cancelled = 0").Group("abs.booking_id").Find(&consecutiveFlags)
	if err := response.Error; err != nil {
		log.Println("func:BatchGetConsecutiveBookingFlagMap, error in fetching consecutive booking flag", err)
		return err
	}
	return nil
}

func (a *BookingData) AddEditPlayArenaCapacitySlot(ctx context.Context, playArenaCapacitySlot *models.PlayArenaCapacitySlot) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	playArenaCapacitySlotData := *playArenaCapacitySlot
	if err := DB.Where("fs_id=? and slot_id = ? and pa_id = ? and product_arena_category_id = ? and day_of_week = ?", playArenaCapacitySlot.FSID, playArenaCapacitySlot.SlotID, playArenaCapacitySlot.PAID, playArenaCapacitySlot.ProductArenaCategoryId, playArenaCapacitySlot.DayOfWeek).Assign(playArenaCapacitySlotData).FirstOrCreate(&playArenaCapacitySlotData).Error; err != nil {
		log.Println("Could not create or update Query for the PACS Id---", playArenaCapacitySlot.PACSID)
		return err
	}
	return nil
}

func (a *BookingData) SendOverbookingAlertMail(ctx context.Context, overbookingData *[]structs.OverbookingData) error {
	log.Println("func:SendOverbookingAlertMail, start")
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings ab").
		Select("ab.fs_id, fsm.description as facility_sport, ab.slot_id, s.timing, ab.booking_date, count(*) as booked_count, fas.capacity").
		Joins("inner join `fs_active_slots` fas on fas.slot_id = ab.slot_id and fas.fs_id = ab.fs_id and ab.booking_cancelled = 0 and date(fas.date) = ab.booking_date and ab.booking_time > now()").
		Joins("inner join play_arena_capacity_slots pacs on ab.pacs_id = pacs.pacs_id").
		Joins("inner join facility_sport_mappings fsm on ab.fs_id = fsm.fs_id").
		Joins("inner join slots s on s.slot_id = ab.slot_id").
		Where("pacs.`pa_id` <> 0").
		Group("fas.slot_id, fas.fs_id, ab.booking_date").
		Having("booked_count > capacity").
		Order("ab.booking_date desc").
		Find(&overbookingData)
	if err := response.Error; err != nil {
		log.Println("func:SendOverbookingAlertMail, error in fetching overbooking data", err)
		return err
	}
	return nil
}

func SendSQSMessage(sess *session.Session, queueUrl string, messageBody string) error {
	sqsClient := sqs.New(sess)
	_, err := sqsClient.SendMessage(&sqs.SendMessageInput{
		QueueUrl:    &queueUrl,
		MessageBody: aws.String(messageBody),
	})
	return err
}

func (a *BookingData) PublishBookingUpdateMessageToSQS(data *structs.BookingEventMessageToSQS) error {
	log.Println("PublishBookingUpdateMessageToSQS: publishing to booking update sqs queue - ", data)
	message, _ := json.Marshal(data)
	queueUrl := config.Get("codeIndependent", "sqsBookingUpdateQueue", "queueUrl").String("")
	err := SendSQSMessage(a.AWSSession, queueUrl, string(message))
	log.Println("PublishBookingUpdateMessageToSQS: message: ", string(message))
	if err != nil {
		log.Printf("Got an error while trying to send message to queueUrl: %s, err: %v", queueUrl, err)
		return err
	}
	log.Println("PublishBookingUpdateMessageToSQS: message published successfully")
	return nil
}

func (a *BookingData) PublishSlotClosureEventToSQS(data *structs.SlotClosureSQSEvent) error {
	log.Println("PublishSlotClosureEventToSQS: publishing to slot closure sqs queue - ", data)
	message, _ := json.Marshal(data)
	queueUrl := config.Get("codeIndependent", "sqsPlaySlotClosure", "queueUrl").String("")
	err := SendSQSMessage(a.AWSSession, queueUrl, string(message))
	if err != nil {
		log.Printf("Got an error while trying to send message to queueUrl: %s, err: %v", queueUrl, err)
		return err
	}
	log.Println("PublishSlotClosureEventToSQS: message published successfully")
	return nil
}

func (a *BookingData) GetSummercampSlots(ctx context.Context, condition *models.SummercampSlot) ([]*models.SummercampSlot, error) {
	var summercampSlots []*models.SummercampSlot

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("summercamp_slots").Select("*")

	if condition.Id > 0 {
		response = response.Where("id = ?", condition.Id)
	}
	if condition.FsId > 0 {
		response = response.Where("fs_id = ?", condition.FsId)
	}
	if condition.SlotId > 0 {
		response = response.Where("slot_id = ?", condition.SlotId)
	}
	if condition.SummercampProductMappingId > 0 {
		response = response.Where("summercamp_product_mapping_id = ?", condition.SummercampProductMappingId)
	}
	if condition.IsActive {
		response = response.Where("is_active = 1")
	}
	if condition.ActiveSlotsApplicable {
		response = response.Where("active_slots_applicable = 1")
	}
	if condition.StartDate.Unix() > 0 {
		response = response.Where("start_date <= ?", condition.StartDate)
	}

	response = response.Find(&summercampSlots)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return summercampSlots, fmt.Errorf("unable to get summer camp slots details %v, Error: %v", condition, err)
	}
	return summercampSlots, nil
}

func (a *BookingData) GetSummercampFacilitiesWithSlotsCapacity(ctx context.Context, data *models.SummercampSlot) ([]*models.SummercampFacilitySlotCapacity, error) {
	var facilities []*models.SummercampFacilitySlotCapacity

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("`summercamp_slots` scs").
		Select("scs.`id` as `summercamp_slot_id`, scs.`fs_id`, scs.`slot_id`, scs.`remaining_capacity`, s.`sport_id`, f.`display_name`, s.`sport_name`, scs.active_slots_applicable, scs.active_slot_id").
		Joins("INNER JOIN `facility_sport_mappings` fsm ON fsm.`fs_id` = scs.`fs_id` AND scs.is_active = 1 AND fsm.`operation_status` = 1 AND fsm.`has_summer_camp` = 1").
		Joins("INNER JOIN `facilities` f ON f.`facility_id` = fsm.`facility_id` AND f.`city_id_v2` = ? AND f.operation_status = 1", data.CityId).
		Joins("INNER JOIN `sports` s ON s.`sport_id` = fsm.`sport_id`").
		Where("scs.`summercamp_product_mapping_id` IN (?) AND scs.`start_date` <= ?", data.SummercampProductMappingId, data.StartDate).
		Find(&facilities)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return facilities, fmt.Errorf("unable to get summer-camp centers with slots capacity for product mapping id: %d", data.SummercampProductMappingId)
	}
	return facilities, nil
}

func (a *BookingData) CheckMasterkeyGuidedSlotsPopulatedForFsIds(ctx context.Context, fsIds []int32, activeSlotPa *[]models.PAActiveSlot) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("pa_active_slots pas").
		Select("pas.*").
		Joins("inner join `play_arena_capacity_slots` pacs on pacs.pacs_id = pas.pacs_id and pas.fs_id in (?) and pas.remaining_capacity > 0 and pas.date >= now() and pas.active = 1 and pacs.product_arena_category_id = ? and pacs.active_status = 1 and pacs.`pa_id` <> 0", fsIds, PRODUCT_ARENA_CATEGORGY_ID_MASTERKEY_GUIDED).
		Group("pas.date").
		Find(&activeSlotPa)
	if err := response.Error; err != nil {
		log.Printf("func:CheckSlotsPopulatedForFsIds, error in checking populated slots data for fsIds: %v, err: %v", fsIds, err)
		return err
	}
	return nil
}

func (a *BookingData) ReduceSummercampSlotCapacity(ctx context.Context, slotData *models.SummercampSlot) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("summercamp_slots").
		Where("id = ?", slotData.Id).
		UpdateColumn("remaining_capacity", gorm.Expr("remaining_capacity - ?", 1))

	if err := response.Error; err != nil {
		return err
	}
	return nil
}

func (a *BookingData) GetBulkSummercampSlotsForProductMappingIds(ctx context.Context, summercamp_product_mapping_ids []int32) ([]*models.SummercampSlot, error) {
	var summercampSlots []*models.SummercampSlot

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("summercamp_slots").Select("*").
		Where("is_active = 1 and summercamp_product_mapping_id in (?)", summercamp_product_mapping_ids).
		Find(&summercampSlots)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return summercampSlots, fmt.Errorf("unable to get summer-camp slots details for summercamp_product_mapping_ids: %v, Error: %v", summercamp_product_mapping_ids, err)
	}
	return summercampSlots, nil
}

func (a *BookingData) AddEditSummerCampSlot(ctx context.Context, slotData *models.SummercampSlot) error {

	updateSlotData := *slotData
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	if err := DB.Where("summercamp_slots.summercamp_product_mapping_id = ? AND summercamp_slots.fs_id = ? AND summercamp_slots.slot_id = ?", slotData.SummercampProductMappingId, slotData.FsId, slotData.SlotId).
		Assign(&updateSlotData).FirstOrCreate(&slotData).
		Updates(map[string]interface{}{"is_active": updateSlotData.IsActive, "remaining_capacity": updateSlotData.RemainingCapacity}).Error; err != nil {

		log.Printf("Error in Add/Edit Summer Camp slot %v", err)
		return err
	}
	return nil
}

func (a *BookingData) BulkReduceSummerCampSlotCapacity(ctx context.Context, fs_id int32, slot_id int32, summercamp_product_mapping_ids []int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	tx := DB.Begin()

	defer func() {
		if r := recover(); r != nil {
			log.Println("BulkReduceSummerCampSlotCapacity: recover due to", r)
			tx.Rollback()
		}
	}()

	updateResponse := tx.Table("summercamp_slots").
		Set("gorm:query_option", "FOR UPDATE").
		Where("fs_id = ? and slot_id = ?  and summercamp_product_mapping_id in (?)", fs_id, slot_id, summercamp_product_mapping_ids).
		UpdateColumn("remaining_capacity", gorm.Expr("remaining_capacity - ?", 1))

	if err := updateResponse.Error; err != nil {
		log.Printf("BulkReduceSummerCampSlotCapacity error in increasing capacity for fsId %v, slot %v, pmids %v, error: %v", fs_id, slot_id, summercamp_product_mapping_ids, updateResponse.Error)
		tx.Rollback()
		return err
	}
	if updateResponse.RowsAffected == 0 {
		log.Printf("BulkReduceSummerCampSlotCapacity: Rows affected zero for academy slot capacity fsId %v, slot %v, pmids %v,", fs_id, slot_id, summercamp_product_mapping_ids)
		if err := tx.Commit().Error; err != nil {
			log.Println("BulkReduceSummerCampSlotCapacity: error in committing transaction", err)
			tx.Rollback()
			return err
		}
		return nil
	}
	if err := tx.Commit().Error; err != nil {
		log.Println("BulkReduceSummerCampSlotCapacity error in committing increase capacity transaction: rolling back: ", err)
		tx.Rollback()
		return err
	}
	return nil
}

func (a *BookingData) BulkIncreaseSummercampSlotCapacity(ctx context.Context, fs_id int32, slot_id int32, summercamp_product_mapping_ids []int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)

	tx := DB.Begin()

	defer func() {
		if r := recover(); r != nil {
			log.Println("BulkIncreaseSummercampSlotCapacity: recover due to", r)
			tx.Rollback()
		}
	}()

	updateResponse := tx.Table("summercamp_slots").
		Set("gorm:query_option", "FOR UPDATE").
		Where("fs_id = ? and slot_id = ?  and summercamp_product_mapping_id in (?)", fs_id, slot_id, summercamp_product_mapping_ids).
		UpdateColumn("remaining_capacity", gorm.Expr("remaining_capacity + ?", 1))

	if err := updateResponse.Error; err != nil {
		log.Printf("BulkIncreaseSummercampSlotCapacity error in increasing capacity for fsId %v, slot %v, pmids %v, error: %v", fs_id, slot_id, summercamp_product_mapping_ids, updateResponse.Error)
		tx.Rollback()
		return err
	}

	if updateResponse.RowsAffected == 0 {
		log.Printf("BulkIncreaseSummercampSlotCapacity: Rows affected zero for academy slot capacity fsId %v, slot %v, pmids %v,", fs_id, slot_id, summercamp_product_mapping_ids)
		if err := tx.Commit().Error; err != nil {
			log.Println("BulkIncreaseSummercampSlotCapacity: error in committing transaction", err)
			tx.Rollback()
			return err
		}
		return nil
	}
	if err := tx.Commit().Error; err != nil {
		log.Println("BulkIncreaseSummercampSlotCapacity error in committing increase capacity transaction: rolling back: ", err)
		tx.Rollback()
		return err
	}

	return nil
}

func (a *BookingData) GetSummercampActiveSlots(ctx context.Context, condition *models.SummercampActiveSlot) ([]*models.SummercampActiveSlot, error) {
	var summercampActiveSlots []*models.SummercampActiveSlot

	lastDateTime, err1 := GetLocalDateTime(time.Now().AddDate(0, 0, -1))
	if err1 != nil {
		log.Println("last datetime converison error -- ", err1)
		return summercampActiveSlots, err1
	}

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("summercamp_active_slots").Select("*")

	if condition.Id > 0 {
		response = response.Where("id = ?", condition.Id)
	}
	if condition.ActiveSlotId > 0 {
		response = response.Where("active_slot_id = ?", condition.ActiveSlotId)
	}

	/*if condition.Date.Unix() > 0 {
		response = response.Where("date >= ?", condition.Date)
	}*/
	log.Println(lastDateTime.Format(formatYMD), lastDateTime)
	response = response.Where("date >= ?", lastDateTime.Format(formatYMD)).
		Order("date ASC")

	response = response.Find(&summercampActiveSlots)

	if err := response.Error; err != nil && !gorm.IsRecordNotFoundError(err) {
		return summercampActiveSlots, fmt.Errorf("unable to get summer camp active slots details %v, Error: %v", condition, err)
	}
	return summercampActiveSlots, nil
}

func (a *BookingData) UpsertSummercampRemainingCapacity(ctx context.Context, ids []int32, remainingCapacity int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("summercamp_slots").Where("id in (?)", ids).UpdateColumn("remaining_capacity", remainingCapacity)
	if err := response.Error; err != nil {
		log.Printf("UpsertSummercampRemainingCapacity: Error in updating remaining cap for ids: %v, err: %v", ids, err)
		return err
	}
	return nil
}

func (a *BookingData) UpdateSummercampSlotActiveSlotId(ctx context.Context, ids []int32, activeSlotId int32) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("summercamp_slots").Where("id in (?)", ids).UpdateColumn("active_slot_id", activeSlotId)
	if err := response.Error; err != nil {
		log.Printf("UpdateSummercampSlotActiveSlotId: Error in updating active slot id for ids: %v, %v, err: %v", ids, activeSlotId, err)
		return err
	}
	return nil
}

func (a *BookingData) CreateSummercampActiveSlotData(ctx context.Context, data []*models.SummercampActiveSlot) error {

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	log.Println(&data)
	tx := DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	for _, createSlot := range data {
		if err1 := tx.Create(&createSlot).Error; err1 != nil {
			log.Printf("CreateSummercampActiveSlotData: data %v, err %v", &createSlot, err1)
			tx.Rollback()
			return err1
		}
	}
	if err := tx.Commit().Error; err != nil {
		log.Println("ERROR ")
		tx.Rollback()
		return err
	}
	return nil
}

func (a *BookingData) ReduceSummercampActiveSlotCapacity(ctx context.Context, id int32, renewSlot bool) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	tx := DB.Begin()

	defer func() {
		if r := recover(); r != nil {
			log.Println("ReduceSummercampActiveSlotCapacity: recover due to", r)
			tx.Rollback()
		}
	}()
	updateResponse := tx.Table("summercamp_active_slots").
		Set("gorm:query_option", "FOR UPDATE").
		Where("id = ?", id)

	if renewSlot {
		updateResponse = updateResponse.UpdateColumn("renew_capacity", gorm.Expr("renew_capacity - ?", 1))
	} else {
		updateResponse = updateResponse.UpdateColumn("new_capacity", gorm.Expr("new_capacity - ?", 1))
	}
	if err := updateResponse.Error; err != nil {
		log.Printf("ReduceSummercampActiveSlotCapacity error in reducing capacity for id %d error: %v", id, updateResponse.Error)
		tx.Rollback()
		return err
	}

	if updateResponse.RowsAffected == 0 {
		log.Printf("ReduceSummercampActiveSlotCapacity: Rows affected zero for summercamp active slot capacity %v,", id)
		if err := tx.Commit().Error; err != nil {
			log.Println("ReduceSummercampActiveSlotCapacity: error in committing transaction", err)
			tx.Rollback()
			return err
		}
		return nil
	}
	if err := tx.Commit().Error; err != nil {
		log.Println("ReduceSummercampActiveSlotCapacity error in committing increase capacity transaction: rolling back: ", err)
		tx.Rollback()
		return err
	}
	return nil
}

func (a *BookingData) IncreaseSummercampActiveSlotCapacity(ctx context.Context, id int32, renewSlot bool) error {
	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	tx := DB.Begin()

	defer func() {
		if r := recover(); r != nil {
			log.Println("IncreaseSummercampActiveSlotCapacity: recover due to", r)
			tx.Rollback()
		}
	}()
	updateResponse := tx.Table("summercamp_active_slots").
		Set("gorm:query_option", "FOR UPDATE").
		Where("id = ?", id)

	if renewSlot {
		updateResponse = updateResponse.UpdateColumn("renew_capacity", gorm.Expr("renew_capacity + ?", 1))
	} else {
		updateResponse = updateResponse.UpdateColumn("new_capacity", gorm.Expr("new_capacity + ?", 1))
	}
	if err := updateResponse.Error; err != nil {
		log.Printf("IncreaseSummercampActiveSlotCapacity error in increasing capacity for id %d error: %v", id, updateResponse.Error)
		tx.Rollback()
		return err
	}

	if updateResponse.RowsAffected == 0 {
		log.Printf("IncreaseSummercampActiveSlotCapacity: Rows affected zero for summercamp active slot capacity %v,", id)
		if err := tx.Commit().Error; err != nil {
			log.Println("IncreaseSummercampActiveSlotCapacity: error in committing transaction", err)
			tx.Rollback()
			return err
		}
		return nil
	}
	if err := tx.Commit().Error; err != nil {
		log.Println("IncreaseSummercampActiveSlotCapacity error in committing increase capacity transaction: rolling back: ", err)
		tx.Rollback()
		return err
	}
	return nil
}

func (a *BookingData) GetUserBookingDetailsByBookingId(ctx context.Context, bookingID int32) (*models.AllBooking, error) {
	var booking models.AllBooking

	DB := gormNewrelicConfig.SetTransactionToGorm(ctx, a.Db)
	response := DB.Table("all_bookings ab").
		Select("ab.booking_time, u.user_id, u.name as user_name, u.phone").
		Joins("inner join `users` u on u.user_id = ab.user_id and ab.booking_id = ?", bookingID).
		Find(&booking)

	if err := response.Error; err != nil && gorm.IsRecordNotFoundError(err) == false {
		log.Printf("Func: GetBookingDetailsByBookingID, Booking id: %d, Error: %v", bookingID, err)
		return &booking, err
	}
	booking.BookingID = bookingID

	return &booking, nil
}
