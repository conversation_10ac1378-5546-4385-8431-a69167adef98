package models

import (
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	"github.com/golang/protobuf/ptypes"
	"time"
)

type SummercampActiveSlot struct {
	Id            int32     `gorm:"not null;type:int(11) unsigned auto_increment;PRIMARY_KEY"`
	ActiveSlotId  int32     `gorm:"not null;type:int(11) unsigned"`
	Date          time.Time `gorm:"not null;type:date"`
	RenewCapacity int32     `gorm:"not null;type:int(11) unsigned"`
	NewCapacity   int32     `gorm:"not null;type:int(11) unsigned"`
	CreatedAt     time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

func (asl *SummercampActiveSlot) Proto() *pb.SummercampActiveSlot {
	date, _ := ptypes.TimestampProto(asl.Date)
	return &pb.SummercampActiveSlot{
		Id:            asl.Id,
		ActiveSlotId:  asl.ActiveSlotId,
		RenewCapacity: asl.RenewCapacity,
		NewCapacity:   asl.NewCapacity,
		Date:          date,
	}
}
