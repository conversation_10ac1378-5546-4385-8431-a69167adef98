package models

import (
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	"github.com/golang/protobuf/ptypes"
	"time"
)

type SummercampSlot struct {
	Id                         int32     `gorm:"not null;type:int(11) unsigned auto_increment;PRIMARY_KEY"`
	SlotId                     int32     `gorm:"not null;type:int(11) unsigned"`
	FsId                       int32     `gorm:"not null;type:int(11) unsigned"`
	SummercampProductMappingId int32     `gorm:"not null;type:int(11) unsigned"`
	RemainingCapacity          int32     `gorm:"not null;type:int(11) unsigned"`
	MaxCapacity                int32     `gorm:"type:int(11) unsigned"`
	IsActive                   bool      `gorm:"not null;type:tinyint unsigned;default:1"`
	StartDate                  time.Time `gorm:"not null;type:datetime"`
	CreatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP"`
	UpdatedAt                  time.Time `gorm:"not null;type:datetime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	ActiveSlotsApplicable      bool      `gorm:"not null;type:tinyint unsigned;default:0"`
	ActiveSlotId               int32     `gorm:"not null;type:int(11) unsigned"`
	CityId                     int32     `gorm:"-"`
}

func (asl *SummercampSlot) Proto() *pb.SummercampSlot {
	startDate, _ := ptypes.TimestampProto(asl.StartDate)
	return &pb.SummercampSlot{
		Id:                         asl.Id,
		FsId:                       asl.FsId,
		SlotId:                     asl.SlotId,
		SummercampProductMappingId: asl.SummercampProductMappingId,
		RemainingCapacity:          asl.RemainingCapacity,
		MaxCapacity:                asl.MaxCapacity,
		IsActive:                   asl.IsActive,
		StartDate:                  startDate,
		ActiveSlotsApplicable:      asl.ActiveSlotsApplicable,
		ActiveSlotId:               asl.ActiveSlotId,
	}
}
