package models

import (
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	"github.com/golang/protobuf/ptypes"
	"time"
)

type WaitlistBooking struct {
	Id                 int32     `gorm:"-"`
	UserId             int32     `gorm:"-"`
	SlotId             int32     `gorm:"-"`
	FsId               int32     `gorm:"-"`
	CityId             int32     `gorm:"-"`
	State              string    `gorm:"-"`
	PaId               int32     `gorm:"-"`
	BookingTime        time.Time `gorm:"-"`
	CreatedAt          time.Time `gorm:"-"`
	PacsId             int32     `gorm:"-"`
	BookingCategory    int32     `gorm:"-"`
	CancelledBookingId int32     `gorm:"-"`
	WlBookingNumber    string    `gorm:"-"`
}

func (wb *WaitlistBooking) Proto() *pb.WaitlistBooking {
	bookingTime, _ := ptypes.TimestampProto(wb.BookingTime)
	return &pb.WaitlistBooking{
		Id:                 wb.Id,
		FsId:               wb.FsId,
		UserId:             wb.UserId,
		SlotId:             wb.SlotId,
		CityId:             wb.CityId,
		PaId:               wb.PaId,
		PacsId:             wb.PacsId,
		BookingCategory:    wb.BookingCategory,
		CancelledBookingId: wb.CancelledBookingId,
		BookingTime:        bookingTime,
	}
}
