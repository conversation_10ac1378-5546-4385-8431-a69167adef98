package structs

type BookingEventMessageToSQS struct {
	BookingId              int32  `json:"booking_id,omitempty"`
	UserId                 int32  `json:"user_id,omitempty"`
	CultMembershipId       int32  `json:"cult_membership_id,omitempty"`
	DurationInSeconds      int32  `json:"duration_in_seconds,omitempty"`
	Reason                 string `json:"reason,omitempty"`
	EventName              string `json:"event_name,omitempty"`
	FsId                   int32  `json:"fs_id,omitempty"`
	BookingTime            string `json:"booking_time,omitempty"`
	IsPenaltyApplied       bool   `json:"is_penalty_applied,omitempty"`
	IsDroppedOut           bool   `json:"is_dropped_out,omitempty"`
	FitsoBooking           bool   `json:"fitso_booking,omitempty"`
	BookingReferenceNumber string `json:"booking_reference_number,omitempty"`
	RevertedBy             int32  `json:"reverted_by,omitempty"`
}
