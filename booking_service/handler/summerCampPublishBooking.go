package bookingHandler

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"

	util "bitbucket.org/jogocoin/go_api/booking_service/internal/util"
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	productPB "bitbucket.org/jogocoin/go_api/booking_service/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/booking_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/booking_service/structs"
)

func (s *Service) SummerCampPrebookingConditions(ctx context.Context, req *pb.PublishBookingRequestV2, res *pb.Ack) error {
	if len(req.BookingUsers) == 0 {
		log.Println("Booking Users List cannot be empty")
		res.Status = &pb.Status{
			Status:  failed,
			Message: badRequest,
		}
		return nil
	}
	req.BookingUsers = UniqueBookingUsers(req.BookingUsers)

	errors := make(chan error, 5)
	var wgMain sync.WaitGroup

	wgMain.Add(1)
	go s.AreValidChildUsers(ctx, req.BookingUsers, &wgMain, errors)

	wgMain.Add(1)
	go s.IsAgeValidForSummerCamp(ctx, req.BookingUsers, &wgMain, errors)

	wgMain.Add(1)
	go s.IsSummerCampTrialAllowedForGivenSport(ctx, req, &wgMain, errors)

	wgMain.Add(1)
	go s.IsBookingTimeValidBasedOnActiveBookings(ctx, req, &wgMain, errors)

	wgMain.Add(1)
	var statusObjSlotArenaAvailability pb.Ack
	go s.SlotArenaAvailabilityV2(ctx, req, &statusObjSlotArenaAvailability, &wgMain, errors)

	wgMain.Wait()

	select {
	case err := <-errors:
		status := &pb.Status{
			Status:  failed,
			Message: err.Error(),
		}
		res.Status = status

	default:
		status := &pb.Status{
			Status:  success,
			Message: "Eligible for booking",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) IsAgeValidForSummerCamp(ctx context.Context, bookingUsers []*pb.BookingUser, wg *sync.WaitGroup, errorsChan chan<- error) {
	defer wg.Done()

	for _, v := range bookingUsers {
		ucl := util.GetUserClient()
		userAgeRecord, err := ucl.GetUserAgeRecord(ctx, &userPB.GetUserAgeRecordReq{
			UserId: v.GetUserID(),
		})
		if err != nil {
			log.Printf("Error in getting user age: user_id: %d, err: %v", v.GetUserID(), err)
			errorsChan <- errors.New("Something went wrong")
			return
		}

		if userAgeRecord.Age < SUMMER_CAMP_MIN_AGE || userAgeRecord.Age > SUMMER_CAMP_MAX_AGE {
			err := fmt.Errorf("Age is not between %d and %d", SUMMER_CAMP_MIN_AGE, SUMMER_CAMP_MAX_AGE)
			log.Printf("Invalid Age: user_id: %d, err: %v", v.GetUserID(), err)
			errorsChan <- err
			return
		}
	}
}

func (s *Service) IsSummerCampTrialAllowedForGivenSport(ctx context.Context, req *pb.PublishBookingRequestV2, wg *sync.WaitGroup, errorsChan chan<- error) {
	defer wg.Done()

	userIds := make([]int32, 0)
	for _, v := range req.BookingUsers {
		userIds = append(userIds, v.GetUserID())
	}

	trialDetailsReq := &pb.TrialsStatusRequest{
		UserIds:           userIds,
		ProductCategoryId: SummerCampCategoryID,
	}
	trialDetailsRes := &pb.TrialsStatusResponse{}
	err := s.GetUserTrialsWithinCityByProductCategory(ctx, trialDetailsReq, trialDetailsRes)
	if err != nil {
		log.Printf("Unable to get academy trial details | Error: %v", err)
		errorsChan <- errors.New("Something went wrong")
		return
	}
	if len(trialDetailsRes.UserTrials) == 0 {
		log.Println("no academy trial details found")
		errorsChan <- errors.New("No trial details found for users")
		return
	}

	var fsData structs.FacilitySportIds
	if err = s.Data.GetFacilityIdAndSportIdByFsId(&req.FSID, &fsData); err != nil {
		log.Println("Get facilityId and SportId from fsid error -->", err)
		errorsChan <- errors.New("Something went wrong")
		return
	}

	for _, userId := range userIds {
		trialDetails, ok := trialDetailsRes.UserTrials[userId]
		if !ok {
			log.Printf("No trial details found for userId: %d", userId)
			errorsChan <- errors.New("No trial details found for user")
			return
		}
		if hasTakenTrial, ok := trialDetails.SportTrial[fsData.SportID]; !ok || hasTakenTrial {
			log.Printf("Trial not allowed for given sportId: %d, userId: %d", fsData.SportID, userId)
			errorsChan <- errors.New("User has already taken trial for requested sport")
			return
		}
	}
}

/** TODO:
 * 1. Push data to leadsquare
 * 2. Delete user suggestions category cache for academy
 **/
func (s *Service) SummerCampPublishBooking(ctx context.Context, req *pb.PublishBookingRequestV2, res *pb.PublishBookingResponseV2) error {
	req.BookingUsers = UniqueBookingUsers(req.BookingUsers)
	userSubMap := make(map[int32]*productPB.Subscription)

	userBookingCategoryMap := make(map[int32]int32)

	for _, val := range req.BookingUsers {
		userBookingCategoryMap[val.UserID] = TRIAL_BOOKING
	}

	if err := s.Data.PublishBookingV2(ctx, req, res, userSubMap, userBookingCategoryMap); err != nil {
		log.Println("unable to publish booking", err)
		BookingErrorStatus(res)
		return nil
	}

	loggedInUser := util.GetUserIDFromContext(ctx)
	s.Data.DeleteUserLinkedBookingsReferenceNumberCache(loggedInUser, SummerCampCategoryID)

	for _, val := range req.BookingUsers {
		s.Data.DeleteUserLinkedBookingsReferenceNumberCache(val.UserID, SummerCampCategoryID)
		if err := deleteUserCategoryCache(ctx, val.UserID, util.GetCityIDFromContext(ctx), SummerCampCategoryID); err != nil {
			log.Println("Error in deleting user suggestion categories cache for user_id: %d, err: %v", val.UserID, err)
			return err
		}
	}

	go s.sendPublishBookingNotification(ctx, res.BookingReferenceNumber)

	status := &pb.Status{
		Status:  success,
		Message: "Booking created successfully!",
	}
	res.Status = status
	return nil
}
