package bookingHandler

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"net/http"
	"strings"
	"sync"

	data "bitbucket.org/jogocoin/go_api/booking_service/data"
	model "bitbucket.org/jogocoin/go_api/booking_service/data/model"

	featuresupport "bitbucket.org/jogocoin/go_api/booking_service/internal/featuresupport"
	util "bitbucket.org/jogocoin/go_api/booking_service/internal/util"
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/booking_service/proto/facility_sport"
	notificationPB "bitbucket.org/jogocoin/go_api/booking_service/proto/notification"
	productPB "bitbucket.org/jogocoin/go_api/booking_service/proto/product"
	userPB "bitbucket.org/jogocoin/go_api/booking_service/proto/user"
	structs "bitbucket.org/jogocoin/go_api/booking_service/structs"
	ptypes "github.com/golang/protobuf/ptypes"
	tspb "github.com/golang/protobuf/ptypes/timestamp"
	micro "github.com/micro/go-micro"
	"github.com/micro/go-micro/config"
	"golang.org/x/net/context"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"

	// "net/url"
	"sort"
	"strconv"
	"time"

	"github.com/jinzhu/now"
)

type Service struct {
	Data data.DataInterface
}

var (
	pcl productPB.ProductService
	ucl userPB.UserService
)

const (
	formatYMDHIS          = "2006-01-02 15:04:05" //Go only understands this date as ymdhis format.
	sampleDate            = "1970-01-01"
	DefaultLocation       = "Asia/Kolkata"
	referenceNumberLength = 8

	//booking cancellation charge levied factors
	multiplicationFactor0min         = 1.0
	multiplicationFactor30min        = 0.75
	multiplicationFactor1hrs         = 0.25
	multiplicationFactor2hrs         = 0.0
	client_secret_path               = "./credentials/client_secret.json"
	SwimmingCoachingType             = 9
	SwimmingCoachingText             = "Pro"
	is_swimming_considered_as_sports = true
	OneDirectNoShowTicketTag         = "30786"
	OneDirectFeedbackTicketTag       = "30469"
)

func (s *Service) Ping(ctx context.Context, req *pb.Empty, res *pb.Pong) error {
	res.Response = "Pong"
	return nil
}

func (s *Service) GetBookingDetails(ctx context.Context, req *pb.Booking, res *pb.BookingResponse) error {
	bookingGetData := &model.AllBooking{
		BookingID:               req.BookingId,
		UserID:                  req.UserId,
		SubscriptionID:          req.SubscriptionId,
		ProductID:               req.ProductId,
		AttendanceFlag:          req.AttendanceFlag,
		BookingCancelled:        req.BookingCancelled,
		RescheduledFrom:         req.RescheduledFrom,
		SessionCount:            req.SessionCount,
		SlotID:                  req.SlotId,
		PAID:                    req.PaId,
		ChargeLevied:            req.ChargeLevied,
		FSID:                    req.FsId,
		BookingType:             req.BookingType,
		BookingCapacity:         req.BookingCapacity,
		BookingStatus:           req.BookingStatus,
		BookingReferenceNumber:  req.BookingReferenceNumber,
		IgnoreCancelledBookings: req.IgnoreCancelledBookings,
		FsIds:                   req.FsIds,
		SlotIds:                 req.SlotIds,
		BookingStartDate:        req.BookingStartDate,
		BookingEndDate:          req.BookingEndDate,
		FetchSlotBookingsCount:  req.FetchSlotBookingsCount,
		IsOnlineSession:         req.IsOnline,
		TabId:                   req.TabId,
		NoShowApplicable:        req.NoShowApplicable,
		FacilityID:              req.FacilityId,
		IsTrial:                 req.IsTrial,
	}

	//get Bookings from seals

	var failText string
	var allSealsBookings []model.AllBooking
	var fetchOnlyCount int32 = 0
	var bookingsCount int32 = 0

	//go to seals only if brn is not present or brn is of length 9
	//TODO remove once swimming is on

	if (len(bookingGetData.BookingReferenceNumber) <= 0 || len(bookingGetData.BookingReferenceNumber) == 9) && (req.TabId == 0 || req.TabId == 1) && !req.NoShowApplicable && (req.Timeline == 2 || !is_swimming_considered_as_sports) {
		if err := s.GetBookingFromSeals(bookingGetData, &allSealsBookings, req.Timeline, 0, req.Start+req.Count, &failText, fetchOnlyCount, &bookingsCount); err != nil || len(failText) > 0 {
			fmt.Println("Error in fetching bookings from seals ->", err)
			error := &pb.Error{
				Code:        305,
				Description: "Error in fetching Booking Details!",
			}
			res.Error = append(res.Error, error)

			failMsg := "Error in fetching Booking Details!"
			if len(failText) > 0 {
				failMsg = failText
			}
			status := &pb.Status{
				Status:  "failure",
				Message: failMsg,
			}
			res.Status = status

			return nil
		}
	}

	var allBooking []model.AllBooking
	if err := s.Data.BookingDetailsGet(ctx, bookingGetData, &allBooking, req.Timeline, 1, req.Start+req.Count); err != nil {

		error := &pb.Error{
			Code:        305,
			Description: "Error in fetching Booking Details!",
		}
		res.Error = append(res.Error, error)

		status := &pb.Status{
			Status:  "failure",
			Message: "Error in fetching Booking Details!",
		}
		res.Status = status

		return nil
	}

	var finalBookingArr []model.AllBooking
	for _, sealsAppendEle := range allSealsBookings {
		finalBookingArr = append(finalBookingArr, sealsAppendEle)
	}

	for _, sportsAppendEle := range allBooking {
		finalBookingArr = append(finalBookingArr, sportsAppendEle)
	}

	//sort based on time
	sort.SliceStable(finalBookingArr,
		func(i, j int) bool {
			if req.Timeline == 1 { //upcoming -- ascending
				return finalBookingArr[i].BookingTime.Before(finalBookingArr[j].BookingTime)
			} else {
				return finalBookingArr[i].BookingTime.After(finalBookingArr[j].BookingTime)
			}
		})

	var startVal int32 = req.Start
	//check for start to be 0 in older apps
	//for android
	if req.AppType == "sports-android" && (req.AppVersion == "1.0.0" || req.AppVersion == "1.0.1" || req.AppVersion == "1.0.2") {
		startVal = req.Start - 1
		if startVal < 0 {
			startVal = 0
		}
	}

	//for ios
	if req.AppType == "sports-ios" && (req.AppVersion == "1.0.0" || req.AppVersion == "1.0.1") {
		startVal = req.Start - 1
		if startVal < 0 {
			startVal = 0
		}
	}

	if req.Count > 0 {

		if req.Start >= int32(len(finalBookingArr)) {
			startVal = int32(len(finalBookingArr))
		}

		var endVal int32 = (req.Count + req.Start)
		if (req.Count + req.Start) >= int32(len(finalBookingArr)) {
			endVal = int32(len(finalBookingArr))
		}

		finalBookingArr = finalBookingArr[startVal:endVal]
	}

	if len(finalBookingArr) > 0 {
		for _, element := range finalBookingArr {

			slotData := &pb.Slot{
				SlotId:    element.SlotID,
				Timing:    element.Timing,
				StartHour: element.StartHour,
				StartMin:  element.StartMin,
				EndHour:   element.EndHour,
				EndMin:    element.EndMin,
			}
			element.Slot = slotData
			if element.FacilityID > 0 {
				facilityData := &pb.Facility{
					FacilityId:        element.FacilityID,
					DisplayName:       element.DisplayName,
					DisplayAddress:    element.DisplayAddress,
					DisplayPicture:    element.DisplayPicture,
					TimingDescription: element.TimingDescription,
					OperationStatus:   element.OperationStatus,
					ShortName:         element.ShortName,
					Latitude:          element.Latitude,
					Longitude:         element.Longitude,
					MapLink:           element.MapLink,
					GooglePlaceId:     element.GooglePlaceId,
					FacilityType:      element.FacilityType,
				}

				if len(element.GooglePlaceId) > 0 {
					googleReviewLink := config.Get("static", "google_review_link").String("https://search.google.com/local/writereview?placeid=") + element.GooglePlaceId
					facilityData.GoogleReviewLink = googleReviewLink
				}
				element.Facility = facilityData

				sportData := &pb.Sport{
					SportId:   element.SportID,
					SportName: element.SportName,
					Icon:      element.Icon,
				}
				element.Sport = sportData

				if element.SportID == 26 && req.Timeline == 1 {
					element.SpecialNotice = s.getSpecialNoticeForUpcomingBookings(element.SlotID, element.FacilityID)
				}

				if element.GuidedEnvironmentFlag == 0 && element.CoachingFlag == 0 && element.Sport.SportId != 3 && req.Timeline == 1 {
					element.SpecialNotice = "NO COACH will be present in the duration of this slot."
				}

				if element.GuidedEnvironmentFlag == 0 && element.CoachingType == 5 { //buddy hour
					element.SpecialNotice = "Please bring ONE friend with you as there won't be any coach available in this slot. Coming alone? Do check the player count in My Bookings before leaving for the facility."
				}
			}
			userData := &pb.User{
				Name:  element.UserName,
				Phone: element.Phone,
				Email: element.Email,
			}
			element.User = userData

			StartDate, _ := ptypes.TimestampProto(element.StartDate)
			SubscriptionEndDate, _ := ptypes.TimestampProto(element.SubscriptionEndDate)
			subData := &pb.Subscription{
				StartDate:           StartDate,
				SubscriptionEndDate: SubscriptionEndDate,
				IsTrial:             element.IsTrial,
			}
			element.Subscription = subData
			if element.OnlineSessionID > 0 {

				element.Slot.Timing = getStartTimingFromSlotsTimingsDetails(element.StartHour, element.StartMin)
				SessionTime, _ := ptypes.TimestampProto(element.SessionTime)
				onlineSession := &pb.OnlineSessions{
					OnlineSessionId: element.OnlineSessionID,
					SessionVideoId:  element.SessionVideoID,
					TitleB:          element.TitleB,
					Description:     element.Description,
					SessionTime:     SessionTime,
					TrainerName:     element.ProviderRealName,
				}
				element.OnlineSession = onlineSession
				group := &pb.SessionGroup{
					TitleA: element.TitleA,
				}
				element.Group = group
			}
			// fmt.Println(element.FSID, " === ", element.SlotID, "--------------------------------", element.SlotBookingsCount, " --------------- ", element.BookingTime)
			res.Booking = append(res.Booking, element.Proto())
		}

		status := &pb.Status{
			Status:  "success",
			Message: "Bookings Fetched successfully!",
		}
		res.Status = status
	} else {

		status := &pb.Status{
			Status:  "success",
			Message: "No bookings exist!",
		}
		res.Status = status

		error := &pb.Error{
			Code:        309,
			Description: "No bookings exist with for the given fields!",
		}
		res.Error = append(res.Error, error)

		//emptyAllBooking := &pb.Booking{}
		//res.Booking = append(res.Booking, emptyAllBooking)
	}

	return nil
}

func (s *Service) GetBookingDetailsV4(ctx context.Context, req *pb.Booking, res *pb.BookingResponse) error {
	bookingGetData := &model.AllBooking{
		UserID:                 req.UserId,
		BookingStartDate:       req.BookingStartDate,
		BookingEndDate:         req.BookingEndDate,
		NoShowApplicable:       req.NoShowApplicable,
		BookingReferenceNumber: req.BookingReferenceNumber,
		ProductCategoryId:      req.ProductCategoryId,
	}
	var allBooking []model.AllBooking
	if err := s.Data.BookingDetailsGetV4(ctx, bookingGetData, &allBooking, req.Timeline); err != nil {
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in fetching Booking Details!",
		}
		res.Status = status
		return nil
	}
	if len(allBooking) == 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "No bookings exist!",
		}
		res.Status = status
		return nil
	}
	for _, element := range allBooking {
		sportData := &pb.Sport{
			SportId:   element.SportID,
			SportName: element.SportName,
		}
		element.Sport = sportData
		if element.SubscriptionID == 0 {
			element.IsTrial = true
		}
		res.Booking = append(res.Booking, element.Proto())
	}
	status := &pb.Status{
		Status:  "success",
		Message: "Bookings Fetched successfully!",
	}
	res.Status = status
	return nil
}

func (s *Service) GetBookingFromSeals(bookingGetData *model.AllBooking, allSealsBookings *[]model.AllBooking, timeline int32, start int32, count int32, failText *string, fetchOnlyCount int32, bookingsCount *int32) error {
	// log.Println("Seals booking fetch req-- ", bookingGetData)

	extraParam := ""
	if len(bookingGetData.BookingReferenceNumber) > 0 {
		extraParam += "&booking_reference_number=" + bookingGetData.BookingReferenceNumber
	}
	if bookingGetData.IgnoreCancelledBookings {
		extraParam += "&for_feedback=1"
	}
	if len(bookingGetData.FsIds) > 0 {
		extraParam += "&fs_ids=" + bookingGetData.FsIds
	}
	if len(bookingGetData.SlotIds) > 0 {
		extraParam += "&slot_ids=" + bookingGetData.SlotIds
	}
	if bookingGetData.BookingStartDate > 0 && bookingGetData.BookingEndDate > 0 {
		extraParam += "&booking_start_date=" + strconv.Itoa(int(bookingGetData.BookingStartDate)) + "&booking_end_date=" + strconv.Itoa(int(bookingGetData.BookingEndDate))
	}

	url := "https://www.getfitso.com/api/v1/getSealsBookingsForSports.json?user_id=" + strconv.Itoa(int(bookingGetData.UserID)) + "&timeline=" + strconv.Itoa(int(timeline)) + "&start=" + strconv.Itoa(int(start)) + "&count=" + strconv.Itoa(int(count)) + extraParam
	fmt.Println(url)
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Content-Type", "application/json")
	if bookingGetData.UserID > 0 {
		req.Header.Set("X-J-User-Id", strconv.Itoa(int(bookingGetData.UserID)))
	} else {
		req.Header.Set("X-J-User-Id", "496126") //req from sports dashboard -- <EMAIL>
	}
	req.Close = true
	res, errResp := http.DefaultClient.Do(req)
	if errResp != nil {
		fmt.Println("Error in getting response from seals -- ", errResp)
	}
	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	sealsBookingResponse := make(map[string]interface{})
	b := []byte(string(body))

	err := json.Unmarshal(b, &sealsBookingResponse)
	if err != nil {
		fmt.Println("Swim slot cap Response unmarshal error", err)
		return err
	}

	// fmt.Println(string(body))

	if sealsBookingResponse["status"].(string) == "success" {
		var sealsBookings []model.AllBooking
		if value, ok := sealsBookingResponse["bookings"].([]interface{}); ok && len(value) > 0 {

			if fetchOnlyCount == 1 { // only count of bookings to be fetched, ignore the extra computation
				*bookingsCount = int32(len(value))
				return nil
			}

			//create product_id map with subscription id as key
			subProdMap := make(map[int32]int32) //key -> subscription_id, val -> product_id
			var subIdArr []int32
			for _, sBEle := range value {
				unitBooking := sBEle.(map[string]interface{})
				subIdArr = append(subIdArr, int32(unitBooking["subscription_id"].(float64)))
			}
			var pdSubArr []structs.ProductSubscription
			if err := s.Data.GetProductIdsForSubscriptionIds(subIdArr, &pdSubArr); err != nil {
				fmt.Println("Error in getting product ids for subscription ids - ", err)
				return err
			}
			for _, subProdEle := range pdSubArr {
				subProdMap[subProdEle.SubscriptionID] = subProdEle.ProductID
			}
			// fmt.Println("Created sub prod map -- ", subProdMap)

			//create FSID map with f_id and sp_id joined string key
			fIdSpIdFsIdMap := make(map[string]int32) //key -> string combination of f_id and sp_id, val -> fs_id
			var fIdSpIdArr []structs.FacilitySportIds
			for _, sBEle := range value {
				unitBooking := sBEle.(map[string]interface{})
				fsp := structs.FacilitySportIds{
					FacilityID: int32(unitBooking["facility_id"].(float64)),
					SportID:    int32(unitBooking["sport_id"].(float64)),
				}
				fIdSpIdArr = append(fIdSpIdArr, fsp)
			}
			var fsIdArr []structs.FacilitySportIds
			if err := s.Data.GetFSIDForSportAndFacilityIds(fIdSpIdArr, &fsIdArr); err != nil {
				fmt.Println("Error in getting fsids arr - ", err)
				return err
			}
			for _, el := range fsIdArr {
				keyStr := strconv.Itoa(int(el.FacilityID)) + "_" + strconv.Itoa(int(el.SportID))
				fIdSpIdFsIdMap[keyStr] = el.FSID
			}
			fmt.Println("Created fidspid fs_id map -- ", fIdSpIdFsIdMap)

			for _, ubElement := range value {

				unitBooking := ubElement.(map[string]interface{})

				//getProductId from subscription id
				pId := subProdMap[int32(unitBooking["subscription_id"].(float64))]
				// if err := s.Data.GetProductIdForSubscriptionId(int32(unitBooking["subscription_id"].(float64)), &pId); err != nil {
				// 	fmt.Println("Error in getting product id")
				// 	return err
				// }

				//get fs_id from facility_id and sport_id
				keyStr := strconv.Itoa(int(int32(unitBooking["facility_id"].(float64)))) + "_" + strconv.Itoa(int(int32(unitBooking["sport_id"].(float64))))
				fsid := fIdSpIdFsIdMap[keyStr]

				// if err := s.Data.GetFSIDForSportAndFacility(int32(unitBooking["sport_id"].(float64)), int32(unitBooking["facility_id"].(float64)), &fsid); err != nil {
				// 	fmt.Println("Error in getting fsid")
				// 	return err
				// }

				//time handle
				createdAt := time.Unix(int64(unitBooking["created_at"].(float64)), 0)
				createdAtLocal, err := GetLocalDateTime(createdAt)
				if err != nil {
					fmt.Println("time converison error ca-- ", err)
					return err
				}

				bookingDate := time.Unix(int64(unitBooking["booking_date"].(float64)), 0)
				bookingDateLocal, err := GetLocalDateTime(bookingDate)
				if err != nil {
					fmt.Println("time converison error bd-- ", err)
					return err
				}

				bookingTime := time.Unix(int64(unitBooking["booking_time"].(float64)), 0)
				bookingTimeLocal, err := GetLocalDateTime(bookingTime)
				if err != nil {
					fmt.Println("time converison error bt-- ", err)
					return err
				}

				cancelTime := time.Unix(int64(unitBooking["cancel_time"].(float64)), 0)
				cancelTimeLocal, err := GetLocalDateTime(cancelTime)
				if err != nil {
					fmt.Println("time converison error ct-- ", err)
					return err
				}

				unitBookingObj := model.AllBooking{
					UserID:                 int32(unitBooking["user_id"].(float64)),
					SubscriptionID:         int32(unitBooking["subscription_id"].(float64)),
					SessionCount:           int32(unitBooking["session_count"].(float64)),
					ChargeLevied:           float32(unitBooking["charge_levied"].(float64)),
					BookingReferenceNumber: unitBooking["booking_reference_number"].(string),
					BookingCancelled:       unitBooking["booking_cancelled"].(bool),
					PAID:                   0,
					SlotID:                 int32(unitBooking["sports_slot_id"].(float64)),
					ProductID:              pId,
					FacilityID:             int32(unitBooking["facility_id"].(float64)),
					SportID:                int32(unitBooking["sport_id"].(float64)),
					FSID:                   fsid,
					BookingType:            2,
					BookingCapacity:        int32(unitBooking["session_count"].(float64)),
					CreatedAt:              createdAtLocal,
					BookingDate:            bookingDateLocal,
					BookingTime:            bookingTimeLocal,
					CancelTime:             cancelTimeLocal,

					//slot
					Timing:    unitBooking["timings"].(string),
					StartHour: int32(unitBooking["start_hrs"].(float64)),
					EndHour:   int32(unitBooking["end_hrs"].(float64)),
					StartMin:  int32(unitBooking["start_mins"].(float64)),
					EndMin:    int32(unitBooking["end_mins"].(float64)),

					//sport
					SportName: unitBooking["sport_name"].(string),
					Icon:      unitBooking["icon"].(string),

					//facility
					DisplayName:    unitBooking["display_name"].(string),
					ShortName:      unitBooking["short_name"].(string),
					DisplayAddress: unitBooking["display_address"].(string),
					MapLink:        unitBooking["map_link"].(string),
				}
				if KeyExists(unitBooking, "attendance_flag") {
					unitBookingObj.AttendanceFlag = unitBooking["attendance_flag"].(bool)
				}
				if KeyExists(unitBooking, "attendance_marked_by") {
					unitBookingObj.AttendanceMarkedBy = int32(unitBooking["attendance_marked_by"].(float64))
				}

				//user
				if KeyExists(unitBooking, "user_name") {
					unitBookingObj.UserName = unitBooking["user_name"].(string)
				}
				if KeyExists(unitBooking, "phone") {
					unitBookingObj.Phone = unitBooking["phone"].(string)
				}
				if KeyExists(unitBooking, "email") {
					unitBookingObj.Email = unitBooking["email"].(string)
				}

				//subscription
				if KeyExists(unitBooking, "start_date") {
					startDateSub := time.Unix(int64(unitBooking["start_date"].(float64)), 0)
					startDateSubLocal, err := GetLocalDateTime(startDateSub)
					if err != nil {
						fmt.Println("time converison error sub start_date-- ", err)
						return err
					}
					unitBookingObj.StartDate = startDateSubLocal
				}
				if KeyExists(unitBooking, "subscription_end_date") {
					endDateSub := time.Unix(int64(unitBooking["subscription_end_date"].(float64)), 0)
					endDateSubLocal, err := GetLocalDateTime(endDateSub)
					if err != nil {
						fmt.Println("time converison error sub start_date-- ", err)
						return err
					}
					unitBookingObj.SubscriptionEndDate = endDateSubLocal
				}
				if KeyExists(unitBooking, "is_trial") {
					unitBookingObj.IsTrial = unitBooking["is_trial"].(bool)
				}
				if KeyExists(unitBooking, "rating_id") {
					unitBookingObj.RatingId = int32(unitBooking["rating_id"].(float64))
				}

				sealsBookings = append(sealsBookings, unitBookingObj)
			}
		} else {
			if fetchOnlyCount == 1 { // only count of bookings to be fetched, ignore the extra computation
				*bookingsCount = int32(len(value))
				return nil
			}
		}

		*allSealsBookings = sealsBookings
	} else {
		*failText = "Error in getting seals bookings"
	}

	//swimBookingResponse["status"].(string) == "success"

	return nil
}

func (s *Service) getSpecialNoticeForUpcomingBookings(slotId int32, facilityId int32) string {

	//slotId - 21 (morning) ,23 (evening)
	//location id - 9 (swiss), 34 (tdp), 35 ()tds
	var envStr string
	envStr = config.Get("config").String("production")

	weekday := int(time.Now().Weekday()) //Sunday is 0 and saturday is 6

	if envStr == "development" {
		fmt.Println("Here dev booking get ------- special notice")
		fmt.Println(slotId, " ---- ", facilityId, " slot fid")
		if facilityId == 9 {
			//Swiss Cottage
			if slotId == 18 {
				//Morning
				return "Contact your coach (Chandan) on - 8750037049 to locate him/her upon reaching the venue"
			} else if slotId == 19 {
				//Evening
				//Days of week
				if weekday == 2 || weekday == 3 || weekday == 4 || weekday == 5 {
					return "Contact your coach (Vinay) on - 9818259920 to locate him/her upon reaching the venue"
				} else {
					return "Contact your coach (Chandan) on - 8750037049 to locate him/her upon reaching the venue"
				}
			}
		} else if facilityId == 34 {
			//Tau Devilal Park TDP
			if slotId == 2 || slotId == 3 {
				//Morning
				return "Contact your coach (Sumit) on - 9868936854 to locate him/her upon reaching the venue"
			} else if slotId == 13 || slotId == 14 {
				//Evening
				return "Contact your coach (Ashish) on - 7838242258 to locate him/her upon reaching the venue"

			}
		} else if facilityId == 35 {
			//Tau Devilal Stadium TDS
			if slotId == 2 || slotId == 3 {
				//Morning
				return "Contact your coach (Ashish) on - 7838242258 to locate him/her upon reaching the venue"
			}
		}
	} else {
		fmt.Println("Here prod booking get ------- special notice")
		fmt.Println(slotId, " ---- ", facilityId, " slot fid")
		if facilityId == 9 {
			//Swiss Cottage
			if slotId == 21 {
				//Morning
				return "Contact your coach (Chandan) on - 8750037049 to locate him/her upon reaching the venue"
			} else if slotId == 23 {
				//Evening
				//Days of week
				if weekday == 2 || weekday == 3 || weekday == 4 || weekday == 5 {
					return "Contact your coach (Vinay) on - 9818259920 to locate him/her upon reaching the venue"
				} else {
					return "Contact your coach (Chandan) on - 8750037049 to locate him/her upon reaching the venue"
				}
			}
		} else if facilityId == 34 {
			//Tau Devilal Park TDP
			if slotId == 2 || slotId == 3 {
				return "Contact your coach (Sumit) on - 9868936854 to locate him/her upon reaching the venue"
			} else if slotId == 21 {
				//Morning
				return "Contact your coach (Sumit) on - 9868936854 to locate him/her upon reaching the venue"
			} else if slotId == 23 {
				//Evening
				//Days of week
				if weekday == 4 || weekday == 5 {
					return "Contact your coach (Sumit) on - 9868936854 to locate him/her upon reaching the venue"
				} else {
					return "Contact your coach (Ashish) on - 7838242258 to locate him/her upon reaching the venue"
				}
			} else if slotId == 13 || slotId == 14 {
				return "Contact your coach (Ashish) on - 7838242258 to locate him/her upon reaching the venue"
			}
		} else if facilityId == 35 {
			//Tau Devilal Stadium TDS
			if slotId == 21 || slotId == 2 || slotId == 3 {
				//Morning
				return "Contact your coach (Ashish) on - 7838242258 to locate him/her upon reaching the venue"
			}
		}
	}

	return ""
}
func (s *Service) ApplyPrebookingConditions(ctx context.Context, req *pb.PublishBookingRequest, res *pb.Ack) error {
	res.Status = &pb.Status{
		Status:  "failure",
		Message: "This version is no longer supported. Please download the latest version of fitso.",
	}
	return nil

	log.Println("apply pre booking req ---", req)
	errors := make(chan error, 5)
	var wgMain sync.WaitGroup

	maxActiveBookingParams := &pb.MaxActiveBookingCheck{
		UserId:         req.UserID,
		SubscriptionId: req.SubscriptionID,
		ProductId:      req.ProductID,
		BookingTime:    req.BookingTime,
		SlotId:         req.SlotID,
		FsId:           req.FSID,
	}

	wgMain.Add(1)
	var statusIsUserAuthentic bool
	go s.CheckIfUserAuthentic(ctx, maxActiveBookingParams, &statusIsUserAuthentic, &wgMain, errors)

	wgMain.Add(1)
	var statusObjApplyPrebookingUserCond pb.Ack

	go s.ApplyUserSpecificPrebookingConditions(maxActiveBookingParams, &statusObjApplyPrebookingUserCond, &wgMain, errors)
	wgMain.Add(1)
	var statusObjFreezeSubscription pb.Ack
	go s.FreezeSubscriptionCheck(maxActiveBookingParams, &statusObjFreezeSubscription, &wgMain, errors)

	wgMain.Add(1)
	var statusObjApplyPrebooking pb.Ack
	go s.ApplyUserSubscriptionSpecificPrebookingConditions(maxActiveBookingParams, &statusObjApplyPrebooking, &wgMain, errors)

	wgMain.Add(1)
	var statusObjSlotArenaAvailability pb.SlotAvailResponse
	arenaSlot := &pb.ArenaSlot{
		SlotID:           req.SlotID,
		PAID:             req.PAID,
		FSID:             req.FSID,
		BookingTime:      req.BookingTime,
		SubscriptionId:   req.SubscriptionID,
		SubscriptionType: req.SubscriptionType,
	}

	slotAvail := &pb.SlotAvailRequest{
		ArenaSlot:     arenaSlot,
		CapacityCount: req.BookingCapacity,
		CapacityType:  req.BookingType,
	}

	go s.SlotArenaAvailability(slotAvail, &statusObjSlotArenaAvailability, &wgMain, errors)

	wgMain.Add(1)
	var statusObjApplySealsUserAgeCond pb.Ack
	go s.ApplySealsUserSpecificAgeConditions(maxActiveBookingParams, &statusObjApplySealsUserAgeCond, &wgMain, errors)

	wgMain.Wait()

	select {
	case err := <-errors:
		status := &pb.Status{
			Status:  "failure",
			Message: err.Error(),
		}
		res.Status = status
		return nil
	default:
	}

	if !statusIsUserAuthentic {
		res.Status = &pb.Status{
			Status:  notAuthorized,
			Message: notAuthorized,
		}
		return nil
	} else if statusObjFreezeSubscription.Status != nil && statusObjFreezeSubscription.Status.Status == "failure" {
		res.Status = statusObjFreezeSubscription.Status
		return nil
	} else if statusObjApplyPrebooking.Status != nil && statusObjApplyPrebooking.Status.Status == "failure" {
		res.Status = statusObjApplyPrebooking.Status
		return nil
	} else if statusObjSlotArenaAvailability.Status != nil && statusObjSlotArenaAvailability.Status.Status == "failure" {
		res.Status = statusObjSlotArenaAvailability.Status
		return nil
	} else if statusObjApplyPrebookingUserCond.Status != nil && statusObjApplyPrebookingUserCond.Status.Status == "failure" {
		res.Status = statusObjApplyPrebookingUserCond.Status
		return nil
	} else if statusObjApplySealsUserAgeCond.Status != nil && statusObjApplySealsUserAgeCond.Status.Status == "failure" {
		res.Status = statusObjApplySealsUserAgeCond.Status
		return nil
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Eligible for booking.",
	}
	res.Status = status

	return nil
}
func (s *Service) ApplyUserSubscriptionSpecificPrebookingConditions(req *pb.MaxActiveBookingCheck, res *pb.Ack, wg *sync.WaitGroup, errors chan<- error) error {
	defer wg.Done()
	// slot start check , abort the booking if half of slots passes.
	bookingTime := time.Unix(req.BookingTime, 0)
	bookingTimeLocal, err := GetLocalDateTime(bookingTime)
	if err != nil {
		log.Println("time converison error -- ", err)
		return err
	}

	loc, _ := time.LoadLocation(DefaultLocation)
	BookingDate, parseErrDate := time.ParseInLocation(formatYMD, bookingTimeLocal.Format(formatYMD), loc)
	if parseErrDate != nil {
		log.Println("Error in parsing BookingTime! Rolling Back -- error: ", parseErrDate)
		return parseErrDate
	}

	curTime, err := GetLocalDateTime(time.Now())
	currentTimeStamp, _ := ptypes.TimestampProto(curTime)
	//currentTimeStampLocal,err := GetLocalDateTime()

	var SlotData []model.Slot
	s.Data.GetSlotDetails(req.SlotId, &SlotData)
	for _, element := range SlotData {
		StartHour := element.StartHour
		StartMin := element.StartMin
		bookingTime = BookingDate.Add(time.Hour*time.Duration(StartHour) + time.Minute*time.Duration(StartMin))
		break
	}

	bookingTimeStamp, _ := ptypes.TimestampProto(bookingTime)

	timeDiff := int32(currentTimeStamp.Seconds - bookingTimeStamp.Seconds)

	maxBookingTimeLimit := int32(15 * 60)
	log.Println("time diffenrece ", timeDiff, curTime, bookingTime)
	if timeDiff >= maxBookingTimeLimit {

		status := &pb.Status{
			Status:  "failure",
			Message: "Can't book your session, The booking time limit is 15-minute post-session.",
		}
		res.Status = status

		return nil
	}

	limitReached := true

	//sports active bookings
	var booking []model.AllBooking
	bookingCond := &model.AllBooking{
		SubscriptionID: req.SubscriptionId,
	}
	if err := s.Data.GetActiveBookingsForSubscriptionV2(bookingCond, &booking, bookingTime); err != nil {
		log.Println("Error in getting current active bookings")
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch active bookings for subscription!",
		}
		res.Status = status
		return err
	}
	var facilitySportIds structs.FacilitySportIds
	if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.FsId, &facilitySportIds); err != nil {
		log.Println("Get facilityId and SportId from fsid error -->", err)
		return err
	}
	if facilitySportIds.FacilityType == 3 {

		bookingData := &pb.Booking{
			BookingStartDate: BookingDate.Unix(),
			BookingEndDate:   BookingDate.Unix() + (24 * 60 * 60),
			Start:            0,
			Count:            10,
			Timeline:         3,
			UserId:           req.UserId,
		}
		bookingResponseData := &pb.BookingResponse{}
		if err := s.GetBookingDetails(context.TODO(), bookingData, bookingResponseData); err != nil {
			log.Println("Error in fetching existing booking for user_id - ", "!")
			return err
		}
		bookings := bookingResponseData.Booking

		alreadyBookedSlotInCurrentFsId := 0
		for _, ele := range bookings {
			if ele.BookingCancelled != true {
				if ele.FsId == req.FsId {
					alreadyBookedSlotInCurrentFsId = alreadyBookedSlotInCurrentFsId + 1
				}
			}
		}
		if alreadyBookedSlotInCurrentFsId >= 3 {
			status := &pb.Status{
				Status:  "failure",
				Message: "Max 3 " + facilitySportIds.SportName + " slots can be booked in a day at this facility. ",
			}
			res.Status = status
			return nil
		}

	}

	// current slots check
	for _, ele := range booking {
		diff := ele.BookingTime.Sub(bookingTime)
		//fmt.Println("diff--------", ele.BookingTime, bookingTime)
		if math.Abs(diff.Seconds()) == 0 {
			status := &pb.Status{
				Status:  "failure",
				Message: "Already booked.",
			}
			res.Status = status
			return nil
		}
	}
	//seals active bookings
	subToCount := make(map[int32]int32)
	var subscriptionIdArr []int32
	subscriptionIdArr = append(subscriptionIdArr, req.SubscriptionId)
	if err := s.GetSealsActiveBookingCountForSubscriptions(subscriptionIdArr, &subToCount); err != nil {
		log.Println("Error in getting seals active booking")
		return err
	}
	totalActiveBookings := int32(len(booking)) + subToCount[req.SubscriptionId]

	log.Println("Total active bookings (seals + sports) ----------------- ", totalActiveBookings)

	if totalActiveBookings > 0 { //active bookings exist
		//check for max limit, then compare
		var maxLimitOfUser, maxLimitOfProduct int32

		if errM := s.Data.GetMaxBookingLimitForUser(req.UserId, &maxLimitOfUser); errM != nil {
			fmt.Println("Error in getting max booking limit for user", errM)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch max active bookings limit!",
			}
			res.Status = status
			return errM
		}

		if errM := s.Data.GetMaxBookingLimitByProduct(req.ProductId, &maxLimitOfProduct); errM != nil {
			fmt.Println("Error in getting max booking limit for user", errM)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch max active bookings limit!",
			}
			res.Status = status

			return errM
		}

		if totalActiveBookings < maxLimitOfUser || totalActiveBookings < maxLimitOfProduct {
			limitReached = false
		}

	} else {
		limitReached = false
	}
	if !limitReached {
		status := &pb.Status{
			Status:  "success",
			Message: "Maximum active bookings limit not reached yet.",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "You have already maxed out your active booking limit.",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) CheckIfUserAuthentic(ctx context.Context, req *pb.MaxActiveBookingCheck, res *bool, wg *sync.WaitGroup, errorsChan chan<- error) error {
	defer wg.Done()

	pcl = util.GetProductClient()

	subscriptionData := &productPB.Subscription{
		SubscriptionId: req.SubscriptionId,
		UserId:         req.UserId,
	}

	responseSubscription, err := pcl.SubscriptionGet(ctx, subscriptionData)

	if err != nil {
		log.Println("Error in getting subscription details of user")
		errorsChan <- err
		return err
	}

	if responseSubscription.Status.Status == "failure" && len(responseSubscription.Errors) > 0 && responseSubscription.Errors[0].Code == 206 {
		*res = false
		return nil
	}

	*res = true
	return nil
}

func (s *Service) ApplyUserSpecificPrebookingConditions(req *pb.MaxActiveBookingCheck, res *pb.Ack, wg *sync.WaitGroup, errorsChan chan<- error) error {
	defer wg.Done()
	isMinAgeCheck := false
	if isMinAgeCheck == true {
		isAllowed := true
		ucl := util.GetUserClient()
		userAgeRecord, err := ucl.GetUserAgeRecord(context.TODO(), &userPB.GetUserAgeRecordReq{
			UserId: req.UserId,
		})
		if err != nil {
			log.Println("Error in getting seals active booking")
			errorsChan <- err
			return err
		}
		if userAgeRecord.Age > 0 {
			var facilitySportIds structs.FacilitySportIds
			if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.FsId, &facilitySportIds); err != nil {
				log.Println("Get facilityId and SportId from fsid error -->", err)
				errorsChan <- err
				return err
			}
			if facilitySportIds.SportID != 3 && userAgeRecord.Age < 10 {
				isAllowed = false
			}
		}
		if isAllowed == false {
			failureRecord := model.BookingFailureRecord{
				UserID:         req.UserId,
				SubscriptionID: req.SubscriptionId,
				ProductID:      req.ProductId,
				BookingTime:    time.Unix(req.BookingTime, 0),
				SlotId:         req.SlotId,
				FsId:           req.FsId,
			}
			go s.Data.RecordBookingFailure(&failureRecord)
			status := &pb.Status{
				Status:  "failure",
				Message: "can't book your session, do not meet the minimum age requirement.",
			}
			res.Status = status
			return nil
		}
	}
	status := &pb.Status{
		Status:  "success",
		Message: "meeting user requirement.",
	}
	res.Status = status
	return nil
}

func (s *Service) ApplySealsUserSpecificAgeConditions(req *pb.MaxActiveBookingCheck, res *pb.Ack, wg *sync.WaitGroup, errorsChan chan<- error) error {
	defer wg.Done()

	var userData model.UserProduct
	if isSwimmingAgeCheckEnabled == true {
		isAllowed := true
		if err := s.Data.GetUserDetails(&userData, req.UserId); err != nil {
			log.Println("Could not get user details..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch user details!",
			}
			res.Status = status
			return nil
		}

		birthday := userData.Birthday
		user_age := math.Floor(time.Now().Sub(birthday).Hours() / 24 / 365)
		if user_age > 0 {
			var facilitySportIds structs.FacilitySportIds
			if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.FsId, &facilitySportIds); err != nil {
				log.Println("Get facilityId and SportId from fsid error -->", err)
				errorsChan <- err
				return err
			}
			if facilitySportIds.SportID == 3 && (user_age < 10 || user_age > 65) {
				ucl := util.GetUserClient()
				userAgeRecord, err := ucl.GetUserAgeRecord(context.TODO(), &userPB.GetUserAgeRecordReq{
					UserId: req.UserId,
				})
				if err != nil {
					log.Println("Error in getting seals active booking")
					errorsChan <- err
					return err
				}

				if userAgeRecord.Age > 9 && userAgeRecord.Age < 66 {
					isAllowed = true
				} else {
					isAllowed = false
					status := &pb.Status{
						Status:  "failure",
						Message: "Please update your birthday before booking.",
					}
					res.Status = status
					return nil
				}
			}

		}

		if isAllowed == false {
			failureRecord := model.BookingFailureRecord{
				UserID:         req.UserId,
				SubscriptionID: req.SubscriptionId,
				ProductID:      req.ProductId,
				BookingTime:    time.Unix(req.BookingTime, 0),
				SlotId:         req.SlotId,
				FsId:           req.FsId,
			}
			go s.Data.RecordBookingFailure(&failureRecord)
			status := &pb.Status{
				Status:  "failure",
				Message: "can not book a session, you do not meet the age requirement for swimming.",
			}
			res.Status = status
			return nil
		}
	}
	status := &pb.Status{
		Status:  "success",
		Message: "meeting user age requirement.",
	}
	res.Status = status
	return nil
}

func (s *Service) OptinSubscriptionCheck(ctx context.Context, req *pb.MaxActiveBookingCheck, res *pb.Ack) error {

	curdate := time.Now()

	if curdate.Before(now.EndOfMonth()) && req.SubscriptionId > 0 {
		var optIn []structs.OptIn
		if err := s.Data.IsOptInSubscription(&optIn, req.SubscriptionId); err != nil {
			fmt.Println("Error in getting  opt in details..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "error in getting opt-in the subscription",
			}
			res.Status = status
			return nil
		}

		if len(optIn) == 0 || (optIn[0].IsTrial == false && optIn[0].OptInId == 0) {

			failureRecord := model.BookingFailureRecord{
				UserID:         req.UserId,
				SubscriptionID: req.SubscriptionId,
				ProductID:      req.ProductId,
				BookingTime:    time.Unix(req.BookingTime, 0),
				SlotId:         req.SlotId,
				FsId:           req.FsId,
			}
			go s.Data.RecordBookingFailure(&failureRecord)

			status := &pb.Status{
				Status:  "failure",
				Message: "Your Membership is currently on pause due to Extensions provided during lockdown. Please re-activate from Home Screen.",
			}
			res.Status = status
			return nil

		}
	}
	status := &pb.Status{
		Status:  "success",
		Message: "Your Membership is active.",
	}
	res.Status = status
	return nil
}

func (s *Service) FreezeSubscriptionCheck(req *pb.MaxActiveBookingCheck, res *pb.Ack, wg *sync.WaitGroup, errorsChan chan<- error) error {
	defer wg.Done()

	reqData := &pb.SportsUserFreezeLogRequest{
		SubscriptionId: req.SubscriptionId,
	}
	var respData pb.SportsUserFreezeLogResponse
	if err := s.GetFreezeLogsForSubscription(context.TODO(), reqData, &respData); err != nil {
		log.Println("Error in getting freeze logs..", err)
		errorsChan <- err
		return err
	}

	logData := respData.FreezeLog

	var freezeDates []int64

	for _, element := range logData {
		// append works on nil slices.
		freezeDates = append(freezeDates, element.FreezeDate.Seconds)
	}

	// if ContainElement(freezeDates, req.BookingTime) {

	// 	status := &pb.Status{
	// 		Status:  "failure",
	// 		Message: "Booking Date is Already Frozen",
	// 	}
	// 	res.Status = status
	// 	return nil
	// }

	//check if frozen
	if CheckForFrozenDate(freezeDates, req.BookingTime) {
		status := &pb.Status{
			Status:  "failure",
			Message: "Booking date is already frozen",
		}
		err := errors.New("Booking date is already frozen")
		errorsChan <- err
		res.Status = status
		return nil
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Your Membership is active.",
	}
	res.Status = status
	return nil

}

func (s *Service) SealsSpecificCheck(req *pb.MaxActiveBookingCheck, res *pb.Ack, wg *sync.WaitGroup, errorsChan chan<- error) error {
	defer wg.Done()

	var facilitySportIds structs.FacilitySportIds
	if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.FsId, &facilitySportIds); err != nil {
		log.Println("Get facilityId and SportId from fsid error -->", err)
		errorsChan <- err
		return err
	}

	if facilitySportIds.SportID == 3 && facilitySportIds.CityId != 8 {
		// slot start check , abort the booking if half of slots passes.
		bookingTime := time.Unix(req.BookingTime, 0)
		bookingTimeLocal, err := GetLocalDateTime(bookingTime)
		if err != nil {
			errorsChan <- err
			log.Println("time converison error -- ", err)
			return err
		}

		loc, _ := time.LoadLocation(DefaultLocation)
		BookingDate, parseErrDate := time.ParseInLocation(formatYMD, bookingTimeLocal.Format(formatYMD), loc)
		if parseErrDate != nil {
			errorsChan <- err
			log.Println("Error in parsing BookingTime! Rolling Back -- error: ", parseErrDate)
			return parseErrDate
		}

		var SlotData []model.Slot
		s.Data.GetSlotDetails(req.SlotId, &SlotData)
		for _, element := range SlotData {
			StartHour := element.StartHour
			StartMin := element.StartMin
			bookingTime = BookingDate.Add(time.Hour*time.Duration(StartHour) + time.Minute*time.Duration(StartMin))
			break
		}

		var bookings []model.UserSealsBooking
		if err := s.Data.GetSealsUpcomingBooking(req.UserId, &bookings); err != nil {
			log.Println("Get facilityId and SportId from fsid error -->", err)
			errorsChan <- err
			return err
		}
		for _, ele := range bookings {

			diff := ele.BookingTime.Sub(bookingTime)

			if math.Abs(diff.Seconds()) <= 60*60 {
				status := &pb.Status{
					Status:  "failure",
					Message: "Consecutive slot booking is not allowed for swimming.",
				}
				res.Status = status
				return nil
			}
		}
	}

	status := &pb.Status{
		Status:  "success",
		Message: "swimming booking check passed successfuly.",
	}
	res.Status = status
	return nil

}

func (s *Service) GetSealsActiveBookingCountForSubscriptions(subscriptionIdArr []int32, subToCount *map[int32]int32) error {
	fmt.Println("req --------", subscriptionIdArr)

	var subIdsStrArr []string
	for _, sEle := range subscriptionIdArr {
		k := strconv.Itoa(int(sEle))
		subIdsStrArr = append(subIdsStrArr, k)
	}

	url := "https://www.getfitso.com/api/v1/getSealsActiveBookingCountForSportSubscriptions.json?subscription_ids=" + strings.Join(subIdsStrArr, ",")
	fmt.Println(url)
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-J-User-Id", "496126") //req from sports dashboard -- <EMAIL>
	req.Close = true
	res, errResp := http.DefaultClient.Do(req)
	if errResp != nil {
		fmt.Println("Error in getting response from seals -- ", errResp)
	}
	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	sealsBookingResponse := make(map[string]interface{})
	b := []byte(string(body))

	err := json.Unmarshal(b, &sealsBookingResponse)
	if err != nil {
		fmt.Println("Swim slot cap Response unmarshal error", err)
		return err
	}

	fmt.Println(string(body))
	subIdsToActiveBookingCount := make(map[int32]int32)
	if sealsBookingResponse["status"].(string) == "success" {
		if value, ok := sealsBookingResponse["data"].([]interface{}); ok {
			for _, vEle := range value {
				ele := vEle.(map[string]interface{})
				subIdsToActiveBookingCount[int32(ele["subscription_id"].(float64))] = int32(ele["seals_active_bookings_count"].(float64))
			}
		}

		*subToCount = subIdsToActiveBookingCount
	}

	return nil
}

func (s *Service) SlotArenaAvailability(req *pb.SlotAvailRequest, res *pb.SlotAvailResponse, wg *sync.WaitGroup, errorsChan chan<- error) error {
	defer wg.Done()
	log.Println("SlotArenaAvailability", req)

	if req.CapacityCount > 0 && req.CapacityType > 0 && req.ArenaSlot.FSID > 0 && req.ArenaSlot.SlotID > 0 && req.ArenaSlot.BookingTime > 0 {

		//has value only in case of modify booking
		var bookedCap int32 = 0
		var bookedCapType int32 = 0
		var slotId int32 = 0
		var fsid int32 = 0
		var bookingTimePrev time.Time

		//get facility_id and sport_id -- needed to skip cap check for swimming
		var facilitySportIds structs.FacilitySportIds
		if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.ArenaSlot.FSID, &facilitySportIds); err != nil {
			log.Println("Get facilityId and SportId from fsid error -->", err)
			errorsChan <- err
			return err
		}

		//check for ModifyBookingFlag here - check availability according to it
		//assuming booking type remains same for prev and current booking while modifying
		if req.ModifyBookingFlag {

			bookingData := &pb.Booking{
				BookingReferenceNumber: req.BookingReferenceNumber,
				Timeline:               1,
				Start:                  1,
				Count:                  10,
			}

			bookingResponseData := &pb.BookingResponse{}
			if err := s.GetBookingDetails(context.TODO(), bookingData, bookingResponseData); err != nil {
				log.Println("Error in fetching existing booking!")
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not find existing booking with reference number - " + req.BookingReferenceNumber,
				}
				errorsChan <- err
				res.Status = status
			}

			if len(bookingResponseData.Booking) > 0 {
				for _, element := range bookingResponseData.Booking {
					bookedCap = element.BookingCapacity
					bookedCapType = element.BookingType
					slotId = element.SlotId
					fsid = element.FsId

					bookingTime := time.Unix(element.BookingTime.Seconds, 0)
					bookingTimeLocal, err := GetLocalDateTime(bookingTime)
					if err != nil {
						log.Println("time converison error -- ", err)
						errorsChan <- err
						return err
					}
					bookingTimePrev = bookingTimeLocal
					break
				}
			} else {
				status := &pb.Status{
					Status:  "failure",
					Message: "Sorry! No booking exists for this reference number.",
				}
				res.Status = status

				return nil
			}

			log.Println("Cap BOoking ------------- ", bookedCap)
			log.Println("Type BOoking ------------- ", bookedCapType)
		}

		var RemainingSessions float32
		if err := s.Data.RemainingSessionsForSubscriptionGet(req.ArenaSlot.SubscriptionId, &RemainingSessions); err != nil {
			fmt.Println("Could not get remaining sessions for subscription!")

			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch Remaining Sessions!",
			}
			err = errors.New(status.Message)
			errorsChan <- err
			res.Status = status

			return err
		}

		//write availed sessions logic here for per day limit comparison

		fmt.Println("RemainingSessions ---------------- ", RemainingSessions)

		bookingTime := time.Unix(req.ArenaSlot.BookingTime, 0)
		bookingTimeLocal, err := GetLocalDateTime(bookingTime)
		if err != nil {
			log.Println("time converison error -- ", err)
			errorsChan <- err
			return err
		}

		PrevBooking := structs.PrevBooking{
			Capacity:     bookedCap,
			CapacityType: bookedCapType,
			SlotID:       slotId,
			FSID:         fsid,
			BookingTime:  bookingTimePrev,
		}

		CapacityToCheck := structs.CheckCapacity{
			FSID:         req.ArenaSlot.FSID,
			PAID:         req.ArenaSlot.PAID,
			SlotID:       req.ArenaSlot.SlotID,
			BookingTime:  bookingTimeLocal,
			Capacity:     req.CapacityCount,
			CapacityType: req.CapacityType,
			PrevBooking:  PrevBooking,
		}

		log.Println("SlotArenaAvailability", req.CapacityType, req.ArenaSlot.FSID, CapacityToCheck)

		// return nil
		var CapacityFlag bool

		if facilitySportIds.SportID != 3 { //not swimming - normal pre cap check
			if CapacityToCheck.PAID <= 0 {
				if err := s.Data.GetRemainingCapacityWithoutPAID(CapacityToCheck, &CapacityFlag); err != nil {
					log.Println("err in getting remaining capacity without paid ", err)
				}
			} else {
				if err := s.Data.GetRemainingCapacity(CapacityToCheck, &CapacityFlag); err != nil {
					log.Println("err in getting remaining capacity with paid ", err)
				}
			}
		} else {
			CapacityFlag = true
		}

		//individual Capacity
		var IndividualCapacity int32
		if err := s.Data.GetIndividualCapacity(CapacityToCheck, &IndividualCapacity); err != nil {
			log.Println("err in getting individual capacity ", err)
		}

		res.ArenaSlot = req.ArenaSlot
		res.CapacityCount = req.CapacityCount
		res.CapacityType = req.CapacityType
		res.AvailabilityFlag = CapacityFlag
		res.IndividualCapacity = IndividualCapacity

		if !res.AvailabilityFlag {
			fmt.Println("Here 1st!")
			status := &pb.Status{
				Status:  "failure",
				Message: "This slot is no longer available!",
			}
			errorsChan <- errors.New(status.Message)
			res.Status = status
		} else if (req.ArenaSlot.SubscriptionType != 2) && (RemainingSessions+float32(PrevBooking.Capacity) < float32(IndividualCapacity*req.CapacityCount)) { //prevBooking.Capacity = 0 for fresh booking
			fmt.Println("Here 2nd!")
			status := &pb.Status{
				Status:  "failure",
				Message: "Oops! You do not have enough sessions left!",
			}
			errorsChan <- errors.New(status.Message)
			res.Status = status
		} else {
			fmt.Println("Here 3rd!")
			status := &pb.Status{
				Status:  "success",
				Message: "Slot is available!",
			}
			res.Status = status
		}

	} else {
		fmt.Println("Here 4nd!")
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid Request!",
		}
		errorsChan <- errors.New(status.Message)
		res.Status = status
	}

	return nil
}

func (s *Service) SlotArenaPrice(ctx context.Context, req *pb.SlotPriceRequest, res *pb.SlotPriceResponse) error {
	return nil
}

func (s *Service) PublishBooking(ctx context.Context, req *pb.PublishBookingRequest, res *pb.PublishBookingResponse) error {
	fmt.Println("PublishBooking handler....", req)

	var facilitySportIds structs.FacilitySportIds
	if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.FSID, &facilitySportIds); err != nil {
		fmt.Println("Get facilityId and SportId from fsid error -->", err)
		return err
	}
	t := time.Unix(req.BookingTime, 0)
	dayOfWeek := int(t.Weekday())
	if dayOfWeek == 0 {
		dayOfWeek = 7
	}

	var coachingType int32
	if err := s.Data.GetCoachingType(ctx, req.FSID, req.SlotID, int32(dayOfWeek), &coachingType); err != nil {
		fmt.Println("This slot is dedicated for Kids Coaching. Kindly reach out to our Facility manager for further details or write to <NAME_EMAIL> error -->", err)
		fmt.Println("Proceed Anyway!!")
		return nil
	}
	if coachingType == 2 { //kids classes
		status := &pb.Status{
			Status:  "failure",
			Message: "This slot is dedicated for Kids Coaching. Kindly reach out to our Facility manager for further details or write to <NAME_EMAIL>",
		}
		res.Status = status

		return nil
	}
	req.CoachingType = coachingType

	if facilitySportIds.SportID != 3 || is_swimming_considered_as_sports { //not swimming -- publish booking in sports db

		if err := s.Data.PublishBooking(req, res); err != nil {
			errors.New(fmt.Sprintf("error creating booking: %v", err))
			fmt.Println("FAILED...!!!")

			error := &pb.Error{
				Code:        306,
				Description: "Error in creating Booking!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Oops! Booking could not be processed. Please try again!",
			}
			res.Status = status

			return nil
		} else {

			if nErr := s.SendNotificationForBookingReferenceNumber(ctx, res.BookingReferenceNumber); nErr != nil {
				fmt.Println("Couldn't send notification on booking creation")
			}

			fmt.Println("Booking created!")
			status := &pb.Status{
				Status:  "success",
				Message: "Booking created successfully!",
			}
			res.Status = status
		}

	} else { //for swimming
		req.FacilityId = facilitySportIds.FacilityID
		req.SportId = facilitySportIds.SportID

		var brn string
		if err, errStr := s.PublishBookingInSeals(req, &brn); err != nil || len(errStr) > 0 { //post booking in seals db -- notification handled there
			error := &pb.Error{
				Code:        306,
				Description: "Error in creating Booking!",
			}
			res.Error = append(res.Error, error)

			errTxt := "Oops! Booking could not be processed. Please try again!"
			if len(errStr) > 0 {
				errTxt = errStr
			}
			status := &pb.Status{
				Status:  "failure",
				Message: errTxt,
			}
			res.Status = status

			return nil
		}

		fmt.Println("Booking created!")
		status := &pb.Status{
			Status:  "success",
			Message: "Booking created successfully!",
		}
		res.Status = status

		if len(brn) > 0 {
			res.BookingReferenceNumber = brn
		}
	}

	go s.ManualProcessForBookingData(res.BookingReferenceNumber, facilitySportIds.SportID, facilitySportIds.FacilityID)

	return nil
}

func (s *Service) ManualProcessForBookingData(bRNumber string, sportId int32, facilityId int32) error {
	facilityPhoneMap := make(map[string]string)

	if errA := s.retrieveFMsPhoneNumberFromSheet(&facilityPhoneMap); errA != nil {
		log.Println("FM phone retrieval error")
	}

	fmPhoneNumberStr := facilityPhoneMap[strconv.Itoa(int(facilityId))]
	fmPhoneNumbers := GetStringArrayFromCommaSeparatedString(fmPhoneNumberStr)

	if err := s.Data.SportsBookingDataToSpreadsheetsAndSendTrialAlert(bRNumber, sportId, fmPhoneNumbers); err != nil {
		log.Println("error in inserting booking data to spreadsheet")
	}

	return nil
}

func (s *Service) retrieveFMsPhoneNumberFromSheet(facilityPhoneMap *map[string]string) error {

	facility_phone_map := make(map[string]string)
	if errA := s.Data.GetCacheFacilityFmMap(&facility_phone_map); errA != nil {
		log.Println("FM phone retrieval error", errA)
	}

	if len(facility_phone_map) < 1 {
		ctx := context.Background()
		srv, err := sheets.NewService(ctx, option.WithCredentialsFile(client_secret_path), option.WithScopes(sheets.SpreadsheetsScope))

		resp, err := srv.Spreadsheets.Values.Get("1a8SWgir0GxF2_G4VK4Go66B3DxoslTOUv2etLYmWDN4", "Sheet1").Do()
		if err != nil {
			log.Printf("retrieveFMsPhoneNumberFromSheet: Unable to retrieve data from sheet: %v", err)
			return err
		}

		if len(resp.Values) == 0 {
			log.Println("No data found.")
			return nil
		} else {
			for _, row := range resp.Values {
				if len(row) == 4 && len(row[3].(string)) >= 10 {
					facility_phone_map[row[0].(string)] = row[3].(string)
				}
			}
		}

		if errB := s.Data.TokenizeFacilityFmMap(facility_phone_map); errB != nil {
			log.Println("FM phone mapping error", errB)
		}
	}

	*facilityPhoneMap = facility_phone_map
	return nil
}

func (s *Service) PublishLiveSessionBooking(ctx context.Context, req *pb.PublishBookingRequest, res *pb.PublishBookingResponse) error {
	if err := IsUserAuthentic(ctx, req.UserID, req.Token); err != nil {
		res.Status = &pb.Status{
			Status:  failed,
			Message: notAuthorized,
		}
		return nil
	}

	if req.UserID > 0 {

		// pre booking checks

		// check if booking time less than current time
		/*
			localTime := time.Now().Add(-1 * time.Hour)
			if localTime.Unix() > req.BookingTime {
				status := &pb.Status{
					Status:  "failure",
					Message: "Booking time less than current time!",
				}
				res.Status = status
				return nil
			}
		*/
		// check if booking already exist
		DateBookingTime := time.Unix(req.BookingTime, 0)
		LocalDateBookingTime, _ := GetLocalDateTime(DateBookingTime)
		var booking []model.AllBooking
		bookingCond := &model.AllBooking{
			OnlineSessionID: req.OnlineSessionId,
			BookingDate:     LocalDateBookingTime,
			UserID:          req.UserID,
		}
		if err := s.Data.GetLiveSessionBookingsForSubscription(bookingCond, &booking); err != nil {
			fmt.Println("Error in getting current active bookings")
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch active bookings for subscription!",
			}
			res.Status = status
			return err
		}
		if len(booking) > 0 {
			status := &pb.Status{
				Status:  "failure",
				Message: "Booking already exist!",
			}
			res.Status = status
			return nil
		}

		// start publish booking
		if err := s.Data.PublishLiveSessionBooking(req, res); err != nil {
			errors.New(fmt.Sprintf("error creating booking: %v", err))
			fmt.Println("FAILED...!!!")

			error := &pb.Error{
				Code:        306,
				Description: "Error in creating Booking!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Oops! Booking could not be processed. Please try again!",
			}
			res.Status = status
		}

	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Oops! Invalid user. Please try again!",
		}
		res.Status = status

	}

	return nil
}

func (s *Service) PublishBookingInSeals(bookingReq *pb.PublishBookingRequest, booking_reference_number *string) (error, string) {

	SealsBookingPublishRequest := &pb.SealsBookingPublishRequest{
		FacilityId:    bookingReq.FacilityId,
		SportsSlotId:  bookingReq.SlotID,
		DateTimestamp: bookingReq.BookingTime,
		UserId:        bookingReq.UserID,
	}

	buf := new(bytes.Buffer)
	json.NewEncoder(buf).Encode(SealsBookingPublishRequest)

	xJUserid := strconv.Itoa(int(SealsBookingPublishRequest.UserId))
	req, errReq := http.NewRequest("POST", "https://www.getfitso.com/api/v1/createSealsBookingsForSports.json", buf)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-J-User-Id", xJUserid)
	req.Header.Set("app_type", "sports-android")
	req.Header.Set("app-version", bookingReq.AppVersion)
	//add app-type and app-version

	if errReq != nil {
		log.Println("Error in initiating requset to seals", errReq)
		return errReq, ""
	}
	req.Close = true
	resp, errResp := http.DefaultClient.Do(req)
	if errResp != nil {
		log.Println("Error in getting response from seals -- ", errResp)
		return errResp, ""
	}

	defer resp.Body.Close()

	body, errBody := ioutil.ReadAll(resp.Body)
	if errBody != nil {
		log.Println("Error in reading resp body", errBody)
		return errBody, ""
	}

	//parse swim response
	swimBookingResponse := make(map[string]interface{})
	b := []byte(string(body))

	err := json.Unmarshal(b, &swimBookingResponse)
	if err != nil {
		log.Println("Swim slot cap Response unmarshal error", err)
		return err, ""
	}

	if swimBookingResponse["status"].(string) == "success" {
		//get all other values and send notificaiton
		bookingData := &pb.SealsBookingPublishResponse{
			BookingReferenceNumber: swimBookingResponse["booking_reference_number"].(string),
			Timing:                 swimBookingResponse["timing"].(string),
			DisplayName:            swimBookingResponse["display_name"].(string),
			SportName:              swimBookingResponse["sport_name"].(string),
			UserId:                 int32(swimBookingResponse["user_id"].(float64)),
		}

		if err := s.SendNotificationForBookingData(bookingData); err != nil {
			log.Println("Error in sending success notificaiton for booking", err)
			return err, ""
		}

		*booking_reference_number = swimBookingResponse["booking_reference_number"].(string)

	} else {
		//trigger booking failure alert mail to tech
		var failReason string
		if len(swimBookingResponse["message"].(string)) > 0 {
			failReason = swimBookingResponse["message"].(string)
		} else {
			failReason = "---"
		}

		if err := s.SendBookingFailureAlertMail(bookingReq, failReason); err != nil {
			log.Println("Could not initiate booking failure alert mail")
		}

		return nil, swimBookingResponse["message"].(string)
	}

	return nil, ""
}

func (s *Service) SendBookingFailureAlertMail(bookingReq *pb.PublishBookingRequest, failReason string) error {
	fmt.Println("-------------------booking", bookingReq)

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	var toEm []*structs.Email

	toEmVal := &structs.Email{
		Name:         "Tech - Fitso",
		EmailAddress: TECH_MAIL,
	}
	toEm = append(toEm, toEmVal)

	bookingTime := time.Unix(bookingReq.BookingTime, 0)
	bookingTimeLocal, err := GetLocalDateTime(bookingTime)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	subjectStr := "Seals booking failure | All Sports Pass | User ID - " + strconv.Itoa(int(bookingReq.UserID))

	messageStr := "The following booking has failed - <br><br>"
	messageStr += "UserID - " + strconv.Itoa(int(bookingReq.UserID)) + "<br>"
	messageStr += "SubscriptionID - " + strconv.Itoa(int(bookingReq.SubscriptionID)) + "<br>"
	messageStr += "SubscriptionType - " + strconv.Itoa(int(bookingReq.SubscriptionType)) + "<br>"
	messageStr += "ProductID - " + strconv.Itoa(int(bookingReq.ProductID)) + "<br>"
	messageStr += "FSID - " + strconv.Itoa(int(bookingReq.FSID)) + "<br>"
	messageStr += "BookindDate - " + bookingTimeLocal.Format(formatYMD) + "<br>"
	messageStr += "SlotID - " + strconv.Itoa(int(bookingReq.SlotID)) + "<br>"
	messageStr += "AppType - " + bookingReq.AppType + "<br>"
	messageStr += "AppVersion - " + bookingReq.AppVersion + "<br>"
	messageStr += "Failure Reason (if any) - " + failReason + "<br>"

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: subjectStr,
		Message: messageStr,
	}

	if err := s.Data.SendSealsBookingFailureEmail(emailReq); err != nil {
		fmt.Println("Error in sending booking failure alert email")
		return err
	}

	return nil
}

func (s *Service) ModifyBooking(ctx context.Context, req *pb.ModifyBookingRequest, res *pb.Ack) error {
	fmt.Println(req)

	publishSuccessful := false
	cancelSuccessful := false

	//cancel
	cancelBookingData := &pb.CancelBookingRequest{
		BookingReferenceNumber: req.BookingReferenceNumber,
		ActionUserId:           int32(req.CancelUserID),
		ForBookingModification: true,
		MultiplicationFactor:   0, //can set desired multiplication factor here
	}

	cancelBookingResponse := &pb.Ack{}
	if err := s.CancelBooking(context.TODO(), cancelBookingData, cancelBookingResponse); err != nil {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not modify booking -- cancel booking error!",
		}

		res.Status = status

		error := &pb.Error{
			Code:        411,
			Description: "Error in modifying booking -- cancel booking error!",
		}
		res.Error = append(res.Error, error)

		return err
	}

	if cancelBookingResponse.Status.Status == "success" {
		cancelSuccessful = true
	}

	if cancelSuccessful {
		//publish
		bookingData := &pb.PublishBookingRequest{
			UserID:                 req.UserID,
			SubscriptionID:         req.SubscriptionID,
			ProductID:              req.ProductID,
			SessionCount:           req.SessionCount,
			SlotID:                 req.SlotID,
			BookingTime:            req.BookingTime,
			PAID:                   req.PAID,
			FSID:                   req.FSID,
			BookingType:            req.BookingType,
			BookingCapacity:        req.BookingCapacity,
			AppType:                req.AppType,
			AppVersion:             req.AppVersion,
			ForBookingModification: true,
			BookingReferenceNumber: req.BookingReferenceNumber,
		}
		pbResponse := &pb.PublishBookingResponse{}

		if err := s.PublishBooking(context.TODO(), bookingData, pbResponse); err != nil {
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not modify booking -- create booking error!",
			}
			res.Status = status

			error := &pb.Error{
				Code:        410,
				Description: "Error in modifying booking -- create booking error!",
			}
			res.Error = append(res.Error, error)

			return nil
		}

		if pbResponse.Status.Status == "success" {
			publishSuccessful = true
		}
		//publish over
	}

	//update booking ref number here
	// updateBrnStatus := &pb.Ack{}
	// if err := s.Data.UpdateBookingReferenceNumber(req.BookingReferenceNumber, pbResponse.BookingReferenceNumber, updateBrnStatus); err != nil {
	// 	status := &pb.Status{
	// 		Status:  "failure",
	// 		Message: "Could not modify booking -- update booking reference number error!",
	// 	}
	// 	res.Status = status

	// 	error := &pb.Error{
	// 		Code:        413,
	// 		Description: "Could not modify booking -- update booking reference number error!",
	// 	}
	// 	res.Error = append(res.Error, error)

	// 	return nil
	// }

	if publishSuccessful && cancelSuccessful {
		status := &pb.Status{
			Status:  "success",
			Message: "Booking modified successfully!",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not modify booking!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) SendNotificationForBookingReferenceNumber(ctx context.Context, bookingRef string) error {

	bookingGetData := &model.AllBooking{
		BookingReferenceNumber: bookingRef,
	}

	var allBooking []model.AllBooking

	app_condition := make(map[string]string)
	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"

	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	if err := s.Data.BookingDetailsGet(ctx, bookingGetData, &allBooking, 1, 1, 1); err != nil {
		return err
	}

	curTime := time.Now()

	//notification exp time
	defExpTime := config.Get("static", "default_notification_expiry_time_in_hours").Int(4)
	expTime := curTime.AddDate(0, 0, defExpTime)
	expTimestamp := expTime.Unix()

	for _, aBooking := range allBooking {

		extra_data := make(map[string]interface{})
		var custom_action_arr []interface{}

		//action 1
		custom_action_obj := make(map[string]string)
		custom_action_obj["action"] = "upcoming_bookings"
		custom_action_obj["url"] = ""
		custom_action_obj["action_title"] = "View Booking"
		custom_action_arr = append(custom_action_arr, custom_action_obj)

		custom_action_obj_2 := make(map[string]string)
		custom_action_obj_2["action"] = "booking"
		custom_action_obj_2["url"] = ""
		custom_action_obj_2["id"] = strconv.Itoa(int(aBooking.SubscriptionID))
		custom_action_obj_2["action_title"] = "New Booking"
		custom_action_arr = append(custom_action_arr, custom_action_obj_2)

		extra_data["custom_action"] = custom_action_arr

		usersIds := make([]int32, 0)
		usersIds = append(usersIds, aBooking.UserID)

		nData := structs.FcmNotification{
			Title:         "Booking Successful!",
			Text:          "Hi, Your " + aBooking.SportName + " session for " + aBooking.Timing + " at " + aBooking.DisplayName + " is confirmed.",
			UserIds:       usersIds,
			Id:            0,
			Action:        "upcoming_bookings",
			ConditionData: condition_data,
			CtaText:       "bookingRef",
			ExpiryDate:    expTimestamp,
			SubCategory:   int32(notificationPB.NotificationSubCategories_BOOKING_SUCCESSFUL),
			//ExtraData:     extra_data,
		}

		if aBooking.UserID == 564178 {
			nData.ExtraData = extra_data
		}

		s.Data.SendNotificationOnPublish(&nData)
	}

	return nil
}

func (s *Service) SendNotificationForBookingData(bookingData *pb.SealsBookingPublishResponse) error {

	app_condition := make(map[string]string)

	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"

	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	usersIds := make([]int32, 0)
	usersIds = append(usersIds, bookingData.UserId)

	curTime := time.Now()

	//notification exp time
	defExpTime := config.Get("static", "default_notification_expiry_time_in_hours").Int(4)
	expTime := curTime.AddDate(0, 0, defExpTime)
	expTimestamp := expTime.Unix()

	nData := structs.FcmNotification{
		Title:         "Booking Successful!",
		Text:          "Hi, Your " + bookingData.SportName + " session for " + bookingData.Timing + " at " + bookingData.DisplayName + " is confirmed.",
		UserIds:       usersIds,
		Id:            0,
		Action:        "upcoming_bookings",
		ConditionData: condition_data,
		CtaText:       bookingData.BookingReferenceNumber,
		ExpiryDate:    expTimestamp,
		SubCategory:   int32(notificationPB.NotificationSubCategories_BOOKING_SUCCESSFUL),
	}

	if err := s.Data.SendNotificationOnPublish(&nData); err != nil {
		fmt.Println("Error in sending notification ", err)
		return err
	}

	return nil
}

func (s *Service) UpdateBooking(ctx context.Context, req *pb.Booking, res *pb.BookingResponse) error {
	return nil
}

func (s *Service) GetPreBookingWindow(fsid int32, prebookingWindow *int32) error {

	if err := s.Data.GetBookingWindowForFacilitySport(fsid, prebookingWindow); err != nil {
		errors.New(fmt.Sprintf("error getting window size: %v", err))
		fmt.Println("error getting window size...!!!")
		return err
	}
	//TODO  caching and first check cache
	return nil
}

func (s *Service) GetDataForBookingCreation(ctx context.Context, req *pb.BookingDatesGet, res *pb.BookingDatesGetResponse) error {
	fmt.Println("GetDataForBookingCreation handler... req. - ", req)

	var bookingStartDate time.Time
	var bookingEndDate time.Time
	usePrebooking := false

	if err := s.Data.GetBookingStartAndEndDate(req, &bookingStartDate, &bookingEndDate); err != nil {
		fmt.Println("Error in getting start and end date from active slots!")
		usePrebooking = true
	}

	StartDateBooking, _ := ptypes.TimestampProto(bookingStartDate)
	EndDateBooking, _ := ptypes.TimestampProto(bookingEndDate)

	if StartDateBooking.Seconds < 0 || EndDateBooking.Seconds < 0 || usePrebooking { //above query failed or invalid date

		var prebookingWindow int32 = 0
		if err := s.GetPreBookingWindow(req.FsId, &prebookingWindow); err != nil {

			error := &pb.Error{
				Code:        307,
				Description: "Error in getting booking dates!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch applicable booking dates!",
			}
			res.Status = status

			return err
		}

		//start date - today; end date - today + prebooking window days
		timeNow := time.Now()
		startDate, _ := ptypes.TimestampProto(timeNow)
		endDate, _ := ptypes.TimestampProto(timeNow.AddDate(0, 0, int(prebookingWindow-1))) //end date not inclusive

		StartDateBooking = startDate
		EndDateBooking = endDate
	}

	//response format -- currently not dependent on BookingType, only on FsId
	res.BookingStartDate = StartDateBooking
	res.BookingEndDate = EndDateBooking
	res.FsId = req.FsId
	res.BookingType = req.BookingType

	status := &pb.Status{
		Status:  "success",
		Message: "Details fetched successfully!",
	}
	res.Status = status
	return nil
}

func GetFSIdForPAId(paid int, dao data.DataInterface) int {
	var fsid int
	fsid = dao.GetFSIDForByPAId(paid)
	return fsid //TODO
}
func (s *Service) PopulateArenaSlots(ctx context.Context, req *pb.PopulateSlotRequest, res *pb.Ack) error {
	fmt.Println("Populating arena slots started for req object: ", req)
	var endDate time.Time
	ts := req.EndDate

	pa_ids := req.PAids //TODO use facility sports api to getListOfActive Facilities instead of request.
	var dates []time.Time
	for _, el := range pa_ids {
		pa_id := int(el)

		fsid := GetFSIdForPAId(pa_id, s.Data)
		var prebookingWindow int32 = 0
		if err := s.GetPreBookingWindow(int32(fsid), &prebookingWindow); err != nil {
			error := &pb.Error{
				Code:        355,
				Description: "Error in getting populating active slots!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch applicable booking dates!",
			}
			res.Status = status

			return err
		}
		if ts != nil {
			endDate = time.Unix(ts.Seconds, int64(ts.Nanos)).UTC()
		} else {
			endDate = time.Now().AddDate(0, 0, int(prebookingWindow-1))
		}
		//requestDate := time.Unix(ts.Seconds, int64(ts.Nanos)).UTC()
		//if requestDate.Equal(endDate) {
		//	endDate = time.Now().AddDate(0, 0, int(prebookingWindow))
		//}

		dates = getDatesToBeAddedForArena(endDate, pa_id, s.Data)
		for _, element := range dates {
			slotCapacityConditions := populateConditionsForArenaSlotCapacity(element, pa_id)
			capacitySlots := s.GetArenaCapacitySlotsForDate(slotCapacityConditions)
			activeSlots := s.generateArenaActiveSlotsUsingCapacityslots(capacitySlots, element)
			for _, a_slot := range activeSlots {
				s.Data.ArenaActiveSlotsCreate(a_slot)
			}
		}
	}
	status := &pb.Status{
		Status:  "status",
		Message: "Slots populated successfully!",
	}
	res.Status = status

	return nil
}

func (s *Service) PopulateSlots(ctx context.Context, req *pb.PopulateSlotRequest, res *pb.Ack) error {
	fmt.Println("Populating slots started for req object: ", req)
	var endDate time.Time
	ts := req.EndDate
	fsids := req.Fsids //TODO use facility sports api to getListOfActive Facilities instead of request.
	fmt.Println(fsids)
	var dates []time.Time
	for _, el := range fsids {
		fsid := int(el)

		var prebookingWindow int32 = 0
		if err := s.GetPreBookingWindow(int32(fsid), &prebookingWindow); err != nil {
			error := &pb.Error{
				Code:        355,
				Description: "Error in getting populating active slots!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch applicable booking dates!",
			}
			res.Status = status

			return err
		}

		fmt.Println("pbooking window : ", prebookingWindow)
		if ts != nil {
			endDate = time.Unix(ts.Seconds, int64(ts.Nanos)).UTC()
		} else {
			endDate = time.Now().AddDate(0, 0, int(prebookingWindow-1))
		}

		dates = getDatesToBeAdded(endDate, fsid, s.Data)
		fmt.Println("FS slots will be added for dates ", dates)
		for _, element := range dates {
			slotCapacityConditions := populateConditionsForSlotCapacity(element, fsid)
			capacitySlots := s.GetCapacitySlotsForDate(slotCapacityConditions)
			activeSlots := s.generateActiveSlotsUsingCapacityslots(capacitySlots, element)
			for _, a_slot := range activeSlots {
				s.Data.ActiveSlotsCreate(a_slot)
			}
		}
	}
	status := &pb.Status{
		Status:  "status",
		Message: "Slots populated successfully!",
	}
	fmt.Println(status.Message)
	res.Status = status
	return nil
}

func getDatesToBeAdded(endDate time.Time, fsid int, dao data.DataInterface) []time.Time {
	fmt.Println("getting dates to be added using end date as  ", endDate)
	var res []time.Time
	var currDate time.Time
	currDate = getMaxDateFromActiveSlots(fsid, dao)
	fmt.Println("curr date", currDate)
	for currDate.Before(endDate) && currDate.Day() != endDate.Day() {
		currDate = currDate.AddDate(0, 0, 1)
		res = append(res, currDate)
	}
	return res
}

func getMaxDateFromActiveSlots(fsid int, dao data.DataInterface) time.Time {

	var maxDate time.Time
	err := dao.ActiveSlotsGetLastDate(fsid, &maxDate)
	fmt.Println("maxdate", maxDate)
	if err != nil {
		fmt.Println("no prev active slots found for fsid ", fsid)
		maxDate = time.Now().AddDate(0, 0, -1) //TODO .Format("02-01-2006")
	}

	return maxDate
}

func populateConditionsForSlotCapacity(date time.Time, fsid int) model.FacilitySportCapacitySlot {
	var conditions model.FacilitySportCapacitySlot
	conditions.FSID = int32(fsid)
	conditions.DayOfWeek = getSystemDayOfWeekFromDate(date)
	return conditions
}

func (s *Service) GetCapacitySlotsForDate(conditions model.FacilitySportCapacitySlot) []model.FacilitySportCapacitySlot {
	var slotCapacity []model.FacilitySportCapacitySlot
	err := s.Data.GetSlotCapacityByDate(conditions, &slotCapacity)
	if err == nil {
		return slotCapacity
	} else {
		fmt.Println("error in retriebving capacity slots.")
		return slotCapacity
		//TODO throw error
	}
}

func (s *Service) generateActiveSlotsUsingCapacityslots(capacitySlots []model.FacilitySportCapacitySlot, currDate time.Time) []model.FSActiveSlot {
	var activeSlots []model.FSActiveSlot
	var err error
	for _, element := range capacitySlots {
		if getSystemDayOfWeekFromDate(currDate) == element.DayOfWeek && element.StartDate.Before(currDate.AddDate(0, 0, 1)) && (element.ActiveStatus > 0) {
			fmt.Println("FSCS Populate --- ", element)

			//add slot start hour to get actual booking time
			var SlotData []model.Slot
			s.Data.GetSlotDetails(element.SlotID, &SlotData)

			var slotDatetimeLocal time.Time
			for _, element := range SlotData {

				dateToBeUsed, err2 := GetLocalDateTime(currDate)

				if err2 != nil {
				}
				StartHour := element.StartHour
				StartMin := element.StartMin
				slotDatetimeLocal = dateToBeUsed.Add(time.Hour*time.Duration(StartHour) + time.Minute*time.Duration(StartMin))
				break
			}

			var activeSlot model.FSActiveSlot
			activeSlot.FSID = element.FSID
			activeSlot.CSID = element.CSID
			activeSlot.SlotID = element.SlotID
			activeSlot.Timing = element.Timing
			activeSlot.Capacity = element.CapacityTotal
			activeSlot.RemainingCapacity = element.CapacityTotal
			activeSlot.SlotDatetime = slotDatetimeLocal
			activeSlot.Date, err = time.Parse(formatYMD, currDate.Format(formatYMD)) //currDate
			activeSlot.Active = element.ActiveStatus
			activeSlot.GuidedEnvironmentFlag = element.GuidedEnvironmentFlag
			activeSlot.CoachingType = element.CoachingType
			activeSlot.InfoText = element.InfoText
			if element.CoachingFlag == 1 {
				activeSlot.CoachingFlag = true
			} else {
				activeSlot.CoachingFlag = false
			}
			activeSlots = append(activeSlots, activeSlot)
			if err != nil {
				fmt.Println(err) //TODO throw
			}
		}
	}
	return activeSlots
}

func convertToTime(ts tspb.Timestamp) time.Time {
	tm := time.Unix(ts.Seconds, int64(ts.Nanos)).UTC()
	return tm
}

func getDatesToBeAddedForArena(endDate time.Time, pa_id int, dao data.DataInterface) []time.Time {
	fmt.Println("getting last active arena date ")
	var res []time.Time
	var currDate time.Time
	currDate = getMaxDateFromArenaActiveSlots(pa_id, dao)
	for currDate.Before(endDate) && currDate.Day() != endDate.Day() {
		currDate = currDate.AddDate(0, 0, 1)
		res = append(res, currDate)
	}
	fmt.Println("dates to be added ", res)
	return res
}

func getMaxDateFromArenaActiveSlots(pa_id int, dao data.DataInterface) time.Time {

	var maxDate time.Time
	err := dao.ArenaActiveSlotsGetLastDate(pa_id, &maxDate)
	if err != nil {
		maxDate = time.Now().AddDate(0, 0, -1) //TODO .Format("02-01-2006")
	}
	return maxDate
}

func populateConditionsForArenaSlotCapacity(date time.Time, pa_id int) model.PlayArenaCapacitySlot {
	var conditions model.PlayArenaCapacitySlot
	conditions.PAID = int32(pa_id)
	conditions.DayOfWeek = getSystemDayOfWeekFromDate(date)
	return conditions
}

func (s *Service) GetArenaCapacitySlotsForDate(conditions model.PlayArenaCapacitySlot) []model.PlayArenaCapacitySlot {
	var slotCapacity []model.PlayArenaCapacitySlot
	err := s.Data.GetArenaSlotCapacityByDate(conditions, &slotCapacity)
	if err == nil {
		return slotCapacity
	} else {
		fmt.Println("error in retriebving capacity slots.")
		return slotCapacity
		//TODO throw error
	}
}

func (s *Service) generateArenaActiveSlotsUsingCapacityslots(capacitySlots []model.PlayArenaCapacitySlot, currDate time.Time) []model.PAActiveSlot {
	var activeSlots []model.PAActiveSlot
	var err error
	for _, element := range capacitySlots {
		if getSystemDayOfWeekFromDate(currDate) == element.DayOfWeek && element.StartDate.Before(currDate.AddDate(0, 0, 1)) && (element.ActiveStatus > 0) && element.ProductArenaCategoryId != PRODUCT_ARENA_CATEGORGY_ID_ACADEMY_SESSION {
			log.Println("PACS Populate --- ", element)
			//add slot start hour to get actual booking time
			var SlotData []model.Slot
			s.Data.GetSlotDetails(element.SlotID, &SlotData)

			var slotDatetimeLocal time.Time
			for _, element := range SlotData {

				dateToBeUsed, err2 := GetLocalDateTime(currDate)

				if err2 != nil {
				}
				StartHour := element.StartHour
				StartMin := element.StartMin
				slotDatetimeLocal = dateToBeUsed.Add(time.Hour*time.Duration(StartHour) + time.Minute*time.Duration(StartMin))
				break
			}

			var activeSlot model.PAActiveSlot
			activeSlot.PAID = element.PAID
			activeSlot.FSID = element.FSID
			activeSlot.PACSID = element.PACSID
			activeSlot.Timing = element.Timing
			activeSlot.Capacity = element.CapacityTotal
			activeSlot.RemainingCapacity = element.CapacityTotal
			activeSlot.SlotDatetime = slotDatetimeLocal
			activeSlot.Date, err = time.Parse(formatYMD, currDate.Format(formatYMD)) //currDate
			activeSlot.Active = element.ActiveStatus
			activeSlot.SlotID = element.SlotID
			activeSlot.ProductArenaCategoryId = element.ProductArenaCategoryId
			activeSlot.GuidedEnvironmentFlag = element.GuidedEnvironmentFlag
			activeSlot.CoachingType = element.CoachingType
			activeSlot.InfoText = element.InfoText
			if element.CoachingFlag == 1 {
				activeSlot.CoachingFlag = true
			} else {
				activeSlot.CoachingFlag = false
			}
			if element.WaitlistFlag == 1 && element.CapacityWaitlist > 0 {
				activeSlot.WaitlistCapacity = element.CapacityWaitlist
				activeSlot.WaitlistRemainingCapacity = element.CapacityWaitlist
				activeSlot.WaitlistConfirmThreshold = element.WaitlistConfirmThreshold
			}
			activeSlots = append(activeSlots, activeSlot)
			if err != nil {
				log.Println("date parse error", err)
			}
		}
	}
	return activeSlots
}

func (s *Service) CleanupSlots(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	date_del := time.Now()
	err := s.Data.DeleteAllActiveSlots(date_del)
	if err != nil {
		return err
	} else {
		err = s.Data.DeleteAllActiveArenaSlots(date_del)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) GetBookingSlots(ctx context.Context, req *pb.BookingSlotsRequest, res *pb.BookingSlotsResponse) error {
	fmt.Println("Get booking slots handler.... req - ", req)

	var activeSlotsFS []model.FSActiveSlot
	var activeSlotsPA []model.PAActiveSlot
	var bookingSlots = []pb.ActiveSlot{}

	if req.BookingType == 1 { //court booking
		err, bookingSlotsData := GetPAActiveBookingSlots(req, activeSlotsPA, s.Data)
		if err != nil {
			error := &pb.Error{
				Code:        405,
				Description: "Error in fetching Play Arena Active Slots!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Error in fetching Play Arena Active Slots Details!",
			}
			res.Status = status

			return err
		}

		bookingSlots = bookingSlotsData
	} else { //individual booking
		err, bookingSlotsData := GetFSActiveBookingSlots(req, activeSlotsFS, s.Data)
		if err != nil {
			error := &pb.Error{
				Code:        406,
				Description: "Error in fetching Facility Sport Active Slots!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Error in fetching Facility Sport Active Slots Details!",
			}
			res.Status = status

			return err
		}

		bookingSlots = bookingSlotsData
	}

	res.BookingType = req.BookingType
	res.FsId = req.FsId

	if len(bookingSlots) > 0 {

		status := &pb.Status{
			Status:  "success",
			Message: "Slots fetched successfully!",
		}
		res.Status = status

		for _, element := range bookingSlots {
			activeSlotData := &pb.ActiveSlot{
				SlotId:            element.SlotId,
				Timing:            element.Timing,
				StartHour:         element.StartHour,
				EndHour:           element.EndHour,
				StartMin:          element.StartMin,
				EndMin:            element.EndMin,
				CapacityRemaining: element.CapacityRemaining,
				AvailabilityType:  element.AvailabilityType,
				AvailabilityText:  element.AvailabilityText,
				InfoText:          element.InfoText,
			}

			res.Slots = append(res.Slots, activeSlotData)
		}
	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "No Slots Found!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetAllSportFacilitySlots(ctx context.Context, req *pb.ActiveSlotsRequest, res *pb.ActiveSlotsResponse) error {

	product_id := req.ProductId
	var fsIds []int32
	if err := s.Data.GetProductFsId(product_id, &fsIds); err != nil {
		log.Println("Error getting FSIDs")
		return err
	}

	DateBookingTime := time.Unix(req.DateBooking, 0)
	LocalDateBookingTime, err := GetLocalDateTime(DateBookingTime)
	if err != nil {
		log.Println("Error in converting date into local time", err)
		return err
	}
	allActiveSlotCondition := &structs.AllFsActiveSlots{
		Date: LocalDateBookingTime,
	}

	var wgMain sync.WaitGroup
	fs_detail := make(chan []model.FacilitySportDetails)
	activeSlot := make(chan []model.FacilitySportDetails)
	wgMain.Add(2)

	go s.GetFacilitySportForFsId(fsIds, &fs_detail, &wgMain)
	go s.GetActiveSlotsForFsId(fsIds, allActiveSlotCondition, &activeSlot, &wgMain)
	fs_details := <-fs_detail
	activeSlotFs := <-activeSlot

	wgMain.Wait()

	if req.UserLatitude > 0 && req.UserLongitude > 0 {
		sort.SliceStable(fs_details, func(i, j int) bool {
			return euclideanDistanceSquare1(fs_details[i], req.UserLatitude, req.UserLongitude) < euclideanDistanceSquare1(fs_details[j], req.UserLatitude, req.UserLongitude)
		})
	} //Sorting based on lattitude and longitude

	fsIdSlotMap := make(map[int32][]*model.FacilitySportDetails)
	for _, ele := range activeSlotFs {
		slots := &model.FacilitySportDetails{
			Date:              ele.Date,
			SlotId:            ele.SlotId,
			Timing:            ele.Timing,
			SlotDatetime:      ele.SlotDatetime,
			RemainingCapacity: ele.RemainingCapacity,
		}
		fsIdSlotMap[ele.FsId] = append(fsIdSlotMap[ele.FsId], slots)
	} //slots mapped by fsId

	sports := make(map[int32][]string)
	for _, element := range fs_details {
		if len(fsIdSlotMap[element.FsId]) > 0 {
			sports[element.FacilityId] = append(sports[element.FacilityId], element.SportName)
		}
	}

	sportFacilityMap := make(map[int32][]*model.FacilitySportDetails)
	for _, element := range fs_details {
		var slotspb []*pb.FacilitySportDetails
		for _, ele := range fsIdSlotMap[element.FsId] {
			slotspb = append(slotspb, ele.Proto())
		}
		facility := &model.FacilitySportDetails{
			FsId:              element.FsId,
			FacilityId:        element.FacilityId,
			DisplayName:       element.DisplayName,
			ShortName:         element.ShortName,
			DisplayAddress:    element.DisplayAddress,
			DisplayPicture:    element.DisplayPicture,
			TimingDescription: element.TimingDescription,
			Latitude:          element.Latitude,
			Longitude:         element.Longitude,
			Sports:            sports[element.FacilityId],
			Slots:             slotspb,
		}
		if len(slotspb) > 0 {
			sportFacilityMap[element.SportId] = append(sportFacilityMap[element.SportId], facility)
		}
	} //Slots inside facility

	check := make(map[int32]bool)
	for _, element := range fs_details {
		var facilitiespb []*pb.FacilitySportDetails
		for _, ele := range sportFacilityMap[element.SportId] {
			facilitiespb = append(facilitiespb, ele.Proto())
		}
		sport := model.FacilitySportDetails{
			SportId:       element.SportId,
			SportName:     element.SportName,
			Icon:          element.Icon,
			IconSportsApp: element.IconSportsApp,
			Popularity:    element.Popularity,
			Facilities:    facilitiespb,
		}
		if check[element.SportId] == false && len(facilitiespb) > 0 {
			check[element.SportId] = true
			res.Sports = append(res.Sports, sport.Proto())
		} //Facilities inside Sport
	}
	if len(res.Sports) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "Available slots fetched successfully!",
		}
		res.Status = status
		sort.SliceStable(res.Sports, func(i, j int) bool {
			return res.Sports[i].Popularity > res.Sports[j].Popularity
		})
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch available slots!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetFacilitySportForFsId(fsIds []int32, facilitySportDetails *chan []model.FacilitySportDetails, wg *sync.WaitGroup) error {

	defer wg.Done()

	var fs_details []model.FacilitySportDetails
	if err := s.Data.GetFacilitySportForFsId(fsIds, &fs_details); err != nil {
		log.Println("Error getting Facility sport details")
		return err
	}
	*facilitySportDetails <- fs_details
	return nil
}

func (s *Service) GetActiveSlotsForFsId(fsIds []int32, allActiveSlotCondition *structs.AllFsActiveSlots, activeSlots *chan []model.FacilitySportDetails, wg *sync.WaitGroup) error {

	defer wg.Done()

	var activeSlotFs []model.FacilitySportDetails
	if err := s.Data.GetActiveSlotsForFsId(fsIds, allActiveSlotCondition, &activeSlotFs); err != nil {
		log.Println("Error getting Facility sport details")
		return err
	}
	*activeSlots <- activeSlotFs
	return nil
}

//critical
func (s *Service) GetBookingSlotsV2(ctx context.Context, req *pb.BookingSlotsRequestV2, res *pb.BookingSlotsResponseV2) error {
	fmt.Println("Get booking slots handler.... req - ", req)
	//get subscription first -- then get slots for all applicable fs_ids -- take out unique Fs - fs_id and Ss - fs_id
	//iterate and format as required

	//get Subscription
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()

	pcl = productPB.NewProductService(serviceList.Product, service.Client())

	subscriptionData := &productPB.Subscription{
		SubscriptionId:  req.SubscriptionId,
		ForBookingSlots: true,
		TabId:           req.TabId,
	}

	// session affinity based call
	response, err := pcl.SubscriptionGetV2(context.TODO(), subscriptionData)

	if err != nil {
		fmt.Println(" [DEBUG] ", err)
		return err
	}
	// fmt.Println("Sub response ---", response.Status.Status)
	//get Subscription over
	var slot []model.Slot
	var fsIdsInPlan []int32
	var userId int32 //used in fetching swimming slots
	var wgMain sync.WaitGroup

	if response.Status.Status == "success" {
		//fetch all slots
		slotConditionObj := &model.Slot{
			DeletedFlag: 0,
		}

		if err := s.Data.SlotGet(ctx, slotConditionObj, &slot); err != nil {
			status := &pb.Status{
				Status:  "failure",
				Message: "No Slots Found!",
			}
			res.Status = status

			error := &pb.Error{
				Code:        407,
				Description: "Error in fetching slots!",
			}
			res.Error = append(res.Error, error)
			fmt.Println("Error in getting all slots.")
			return err
		}

		if len(req.FsIds) > 0 {

			fsIdsInPlan = req.FsIds
			userId = response.Subscriptions[0].UserId
		} else {

			//get available fs_ids in plan
			//there will be only one subscription element as we have fetched subscriptions based on subsctiption_id(PK) --hence, break
			for _, subElement := range response.Subscriptions {
				userId = subElement.UserId
				for _, unitSubElement := range subElement.Product.FacilitySports {
					fsIdsInPlan = append(fsIdsInPlan, unitSubElement.FsId)
				}

				break
			}

			//get available fs_ids in plan over
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "No Slots Found!",
		}
		res.Status = status

		error := &pb.Error{
			Code:        407,
			Description: "Error in fetching subscription!",
		}
		res.Error = append(res.Error, error)
	}

	//slot request date
	DateBookingTime := time.Unix(req.DateBooking, 0)
	LocalDateBookingTime, err := GetLocalDateTime(DateBookingTime)
	if err != nil {
		fmt.Println("Error in converting date into local time", err)
		return err
	}

	//fetch all f_ids and s_ids for all fs_ids in plan
	fidsInPlanMap := make(map[int32]structs.FacilitySportIds)
	s.GetFacilityIdsAndSportIdsForAllFsIds(ctx, fsIdsInPlan, &fidsInPlanMap)

	//get BookingSlots for all slots -- store in map
	var allSlotIdsArr []int32
	slotFsActiveMap := make(map[int32][]model.FSActiveSlot) // store facility_ids(key) -- sport_ids(values)
	for _, element := range slot {
		allSlotIdsArr = append(allSlotIdsArr, element.SlotID)
	}

	allActiveSlotCondition := &structs.AllFsActiveSlots{
		SlotIDArr: allSlotIdsArr,
		Date:      LocalDateBookingTime,
	}
	isSwimmingAvaiableInPlan := false
	for _, fsIdElement := range fsIdsInPlan {

		fsIdMapValue := fidsInPlanMap[fsIdElement]

		//check if fsIdElement is for swimming - if yes - create map of fs_id = f_id - else go to the next loop
		if fsIdMapValue.SportID == 3 {
			// isSwimmingAvaiableInPlan = true
			isSwimmingAvaiableInPlan = false
		}
	}
	wgMain.Add(1)
	go s.AllBookingSlotsGetFS(ctx, req.UserId, fsIdsInPlan, isSwimmingAvaiableInPlan, allActiveSlotCondition, &slotFsActiveMap, &wgMain)
	//favorite facility fecth on the basis of userId

	favMapFac := make(map[int32]model.Facility) // store facility_ids(key) -- Facilities(values) only for fav facilities
	wgMain.Add(1)
	go s.GetAllFavoriteFacilitiesForUser(ctx, userId, &favMapFac, &wgMain)

	// fac fetch -- also mark favorite in the process

	mapFac := make(map[int32]model.Facility) // store facility_ids(key) -- Facilities(values)
	mapFacOrder := make(map[int32]int32)     //facility_id to display_order
	var facData []model.Facility

	wgMain.Add(1)
	go s.GetAllFacilities(ctx, &mapFacOrder, &facData, req, &wgMain)

	// mapFac := make(map[int32]model.Facility) // store facility_ids(key) -- Facilities(values)

	mapSport := make(map[int32]model.Sport) // store facility_ids(key) -- Facilities(values)
	wgMain.Add(1)
	go s.GetAllSports(ctx, &mapSport, &wgMain)

	//store all bookings for user
	bookingCond := model.AllBooking{
		BookingDate: LocalDateBookingTime,
		UserID:      userId,
	}
	mapAllBookedForDate := make(map[int32][]model.AllBooking) // store slot_id(key) -- AllBooking(values)
	wgMain.Add(1)
	go s.GetAllBookedSlotsForUser(ctx, bookingCond, &mapAllBookedForDate, &wgMain)

	//create coaching type map

	mapCoachingType := make(map[int32]model.CoachingType) // store slot_id(key) -- AllBooking(values)
	wgMain.Add(1)
	go s.GetCoachingType(ctx, &mapCoachingType, &wgMain)
	//create coaching type map over

	//***********

	//*********

	wgMain.Wait()
	for _, fEle := range facData {
		if _, ok := favMapFac[fEle.FacilityID]; ok {
			fEle.IsFavorite = true
		}
		fEle.DisplayOrder = mapFacOrder[fEle.FacilityID]
		mapFac[fEle.FacilityID] = fEle
	}

	for _, element := range slot {

		var activeSlotsFS []model.FSActiveSlot //stores all available fs_ids from fs_active_slots
		activeSlotsFS = slotFsActiveMap[element.SlotID]

		scMap := make(map[int32][]structs.SportCapacity) // store facility_ids(key) -- sport_ids(values)

		// check if booking exists by user for this slot and date and set availability type and text
		var slotAvailabilityInfo model.SlotAvailabilityInfo
		var bookingFSID int32
		bookingCond := model.AllBooking{
			SlotID:      element.SlotID,
			BookingDate: LocalDateBookingTime,
			UserID:      userId,
		}
		if err := s.GetSlotAvailabilityInfo(ctx, &bookingCond, &slotAvailabilityInfo, &bookingFSID, mapAllBookedForDate); err != nil {
			log.Println("Getting slot availability info error")
			log.Println("Moving forward without throwing error")
		}

		element.AvailabilityType = slotAvailabilityInfo.AvailabilityType
		element.AvailabilityText = slotAvailabilityInfo.AvailabilityText
		element.BookingFsId = bookingFSID //used to check if sport is already booked
		// check over
		for _, fsIdElement := range fsIdsInPlan {

			fsIdMapValue := fidsInPlanMap[fsIdElement]

			//check if fsIdElement is for swimming - if yes - create map of fs_id = f_id - else go to the next loop

			//other sports flow
			for _, activeSlotElement := range activeSlotsFS {

				if fsIdElement == activeSlotElement.FSID {

					sportCapObj := structs.SportCapacity{
						SportID:               fsIdMapValue.SportID,
						Capacity:              activeSlotElement.Capacity,
						RemainingCapacity:     activeSlotElement.RemainingCapacity,
						SpotsInPlayArena:      activeSlotElement.SpotsInPlayArena,
						Active:                activeSlotElement.Active,
						FSID:                  activeSlotElement.FSID,
						CoachingFlag:          activeSlotElement.CoachingFlag,
						CoachingType:          activeSlotElement.CoachingType,
						CoachingText:          mapCoachingType[activeSlotElement.CoachingType].CoachingText,
						GuidedEnvironmentFlag: activeSlotElement.GuidedEnvironmentFlag,
						InfoText:              activeSlotElement.InfoText,
						CSID:                  activeSlotElement.CSID,
					}

					if len(mapCoachingType[activeSlotElement.CoachingType].CoachingText) <= 0 && sportCapObj.CoachingType > 0 {
						sportCapObj.CoachingText = "Coaching"
					}

					scMap[fsIdMapValue.FacilityID] = append(scMap[fsIdMapValue.FacilityID], sportCapObj)
				}
			}

		}

		// sortedFacMap := make(map[int32]*pb.Facility)
		finalFacPb := make([]*pb.Facility, len(mapFac))
		for k, v := range scMap {

			var facilityModel []model.Facility
			facilityModel = append(facilityModel, mapFac[k])

			var facilityPb *pb.Facility
			for _, facEle := range facilityModel {
				facilityPb = facEle.Proto()
			}

			for _, sportEle := range v {

				var sportModel []model.Sport

				sportModel = append(sportModel, mapSport[sportEle.SportID])

				for _, sEle := range sportModel {

					slotCapacityObj := model.SlotCapacity{
						Capacity:              sportEle.Capacity,
						RemainingCapacity:     sportEle.RemainingCapacity,
						Active:                sportEle.Active,
						SpotsInPlayArena:      sportEle.SpotsInPlayArena,
						FSID:                  sportEle.FSID,
						BookingFSID:           bookingFSID,
						CoachingFlag:          sportEle.CoachingFlag,
						GuidedEnvironmentFlag: sportEle.GuidedEnvironmentFlag,
					}

					var SlotAvailability model.SlotAvailability
					if err := s.GetSlotAvailabilityStatsByCapacity(ctx, slotCapacityObj, &SlotAvailability); err != nil {
						log.Println("Capacity Stats fetch err")
						return err
					}

					sEle.RemainingCapacity = sportEle.RemainingCapacity
					sEle.AvailabilityType = SlotAvailability.AvailabilityType
					sEle.AvailabilityText = SlotAvailability.AvailabilityText
					sEle.InfoText = sportEle.InfoText //SlotAvailability.InfoText
					sEle.FSID = sportEle.FSID
					sEle.GuidedEnvironmentFlag = sportEle.GuidedEnvironmentFlag
					sEle.CoachingType = sportEle.CoachingType
					sEle.CoachingText = sportEle.CoachingText
					sEle.CSID = sportEle.CSID
					if len(sEle.InfoText) <= 0 {
						sEle.InfoText = SlotAvailability.InfoText
					}

					if sEle.GuidedEnvironmentFlag == 1 {
						if len(sEle.InfoText) <= 0 {
							sEle.InfoText = "Please reach the venue 10 minutes before your booking time."
						}
					} else {
						if len(sEle.InfoText) <= 0 {
							sEle.InfoText = "NO COACH will be present in the duration of this slot.\n\nYou can play with others who have made a reservation in this slot."
						}
					}

					//form info text arr from string
					var infArr []string
					infArr = strings.Split(sEle.InfoText, "^^") //string to array
					sEle.InfoTextArr = infArr

					//furnish info text, remove all "^^"

					sEle.InfoText = strings.Join(sEle.InfoTextArr, " ")
					facilityPb.SportsForBooking = append(facilityPb.SportsForBooking, sEle.Proto())
				}
			}

			finalFacPb[facilityPb.DisplayOrder] = facilityPb
		}
		for _, sortedFacEle := range finalFacPb {
			if sortedFacEle != nil {

				if sortedFacEle.IsFavorite {
					element.Facility = append([]*pb.Facility{sortedFacEle}, element.Facility...)
				} else {
					element.Facility = append(element.Facility, sortedFacEle)
				}
			}
		}
		if len(element.Facility) > 0 {
			res.Slots = append(res.Slots, element.Proto())
		}

	}

	if len(res.Slots) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "Available slots fetched successfully!",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch available slots!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetOnlineSessionBookingSlotsV2(ctx context.Context, req *pb.BookingSlotsRequestV2, res *pb.OnlineSessionResponse) error {
	//get Subscription

	//fetch all slots
	DateBookingTime := time.Unix(req.DateBooking, 0)
	LocalDateBookingTime, err := GetLocalDateTime(DateBookingTime)

	if err != nil {
		fmt.Println("Error in converting date into local time", err)
		return err
	}

	condition := &model.OnlineSessions{
		Date: LocalDateBookingTime,
	}
	var OnlineSessions []model.OnlineSessions

	if err := s.Data.GetLiveSessionBooking(condition, &OnlineSessions); err != nil {
		errors.New(fmt.Sprintf("error gettin live session details: %v", err))
		return err
	}
	bookingCond := model.AllBooking{
		BookingDate:     LocalDateBookingTime,
		UserID:          req.UserId,
		IsOnlineSession: true,
	}
	var allBookingsForDate []model.AllBooking
	onlineSessionsToBookingMap := make(map[int32]int32)
	if err := s.Data.GetAllBookedSlotsForUser(ctx, &bookingCond, &allBookingsForDate); err != nil {
		fmt.Println("Error in getting all booked slots for date  slot and user", err)
		return err
	}
	for _, ele := range allBookingsForDate {
		onlineSessionsToBookingMap[ele.OnlineSessionID] = ele.BookingID
	}

	slotsMap := make(map[int32]*pb.LiveSessionSlots)
	slotsGroupMap := make(map[int32]map[string][]*pb.OnlineSessions)
	slotsOnlineSessionsMap := make(map[int32][]*pb.OnlineSessions)
	var slots []*pb.LiveSessionSlots
	for _, ele := range OnlineSessions {
		slot := &pb.LiveSessionSlots{
			SlotId:    ele.SlotId,
			Timing:    getStartTimingFromSlotsTimingsDetails(ele.StartHour, ele.StartMin),
			StartHour: ele.StartHour,
			StartMin:  ele.StartMin,
			EndHour:   ele.EndHour,
			EndMin:    ele.EndMin,
		}
		slotsMap[ele.SlotId] = slot
		slotCapacityObj := model.SlotCapacity{
			Capacity:          ele.Capacity,
			RemainingCapacity: ele.RemainingCapacity,
			Active:            1 - ele.DeletedFlag,
			BookingID:         onlineSessionsToBookingMap[ele.OnlineSessionId],
		}
		var SlotAvailability model.SlotAvailability
		if err := s.GetSlotAvailabilityStatsByCapacity(ctx, slotCapacityObj, &SlotAvailability); err != nil {
			fmt.Println("Capacity Stats fetch err")
			return err
		}

		ele.AvailabilityType = SlotAvailability.AvailabilityType
		ele.AvailabilityText = SlotAvailability.AvailabilityText

		if slotsGroupMap[ele.SlotId] == nil {
			slotsGroupMap[ele.SlotId] = make(map[string][]*pb.OnlineSessions)
		}
		if slotsGroupMap[ele.SlotId][ele.TitleA] == nil {
			var slotGroupInit []*pb.OnlineSessions
			slotGroupInit = append(slotGroupInit, ele.Proto())
			slotsGroupMap[ele.SlotId][ele.TitleA] = slotGroupInit
		} else {
			slotsGroupMap[ele.SlotId][ele.TitleA] = append(slotsGroupMap[ele.SlotId][ele.TitleA], ele.Proto())
		}
		slotsOnlineSessionsMap[ele.SlotId] = append(slotsOnlineSessionsMap[ele.SlotId], ele.Proto())

	}

	for _, slot := range slotsMap {
		var sessionGroup []*pb.SessionGroup
		for title, onlineSession := range slotsGroupMap[slot.SlotId] {
			group := &pb.SessionGroup{
				TitleA:         title,
				OnlineSessions: onlineSession,
			}
			sessionGroup = append(sessionGroup, group)
		}
		slot.Group = sessionGroup
		slots = append(slots, slot)
	}
	sort.Slice(slots, func(i, j int) bool {
		return slots[i].StartHour*60+slots[i].StartMin < slots[j].StartHour*60+slots[j].StartMin
	})
	for _, slot := range slots {
		res.Slots = append(res.Slots, slot)
	}
	if len(slots) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "Available slots fetched successfully!",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch available slots!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetFacilityIdsAndSportIdsForAllFsIds(ctx context.Context, fsids []int32, fidsInPlanMap *map[int32]structs.FacilitySportIds) error {
	var facilitySportIds []structs.FacilitySportIds
	if err := s.Data.GetFacilityIdsAndSportIdsForAllFsIds(ctx, fsids, &facilitySportIds); err != nil {
		log.Println("Get all f_ids and s_ids from all fs_ids error", err)
		return err
	}

	tempFidsInPlanMap := make(map[int32]structs.FacilitySportIds)
	for _, fsEle := range facilitySportIds {
		tempFidsInPlanMap[fsEle.FSID] = fsEle
	}
	*fidsInPlanMap = tempFidsInPlanMap
	return nil
}

func (s *Service) AllBookingSlotsGetFS(ctx context.Context, userId int32, fsids []int32, isFetchSealsSlots bool, condition *structs.AllFsActiveSlots, slotFsActiveMap *map[int32][]model.FSActiveSlot, wg *sync.WaitGroup) error {

	defer wg.Done()

	var activeSlotFs []model.FSActiveSlot
	if err := s.Data.AllBookingSlotsGetFS(ctx, condition, &activeSlotFs); err != nil {
		errors.New(fmt.Sprintf("error getting active slots details: %v", err))
		return err
	}
	var swimmingActiveSlotFs []model.FSActiveSlot
	if isFetchSealsSlots == true {
		var assessmentLevelId int32 = 0
		if err := s.Data.GetUserAssessmentLevel(ctx, userId, &assessmentLevelId); err != nil {
			log.Println("Error in fetching assessment level..", err)
		}

		if !Contains([]int32{1, 2, 3, 4, 5, 9, 10}, assessmentLevelId) {
			if err := s.Data.AllSwimmingSlotsGetFS(ctx, fsids, condition, &swimmingActiveSlotFs); err != nil {
				errors.New(fmt.Sprintf("error getting active slots details: %v", err))
				return err
			}
		}
	}
	activeSlotFs = append(activeSlotFs, swimmingActiveSlotFs...)
	tempSlotFsActiveMap := make(map[int32][]model.FSActiveSlot)
	for _, ele := range activeSlotFs {
		tempSlotFsActiveMap[ele.SlotID] = append(tempSlotFsActiveMap[ele.SlotID], ele)
	}
	*slotFsActiveMap = tempSlotFsActiveMap

	return nil
}

func (s *Service) GetAllFavoriteFacilitiesForUser(ctx context.Context, userId int32, favMapFac *map[int32]model.Facility, wg *sync.WaitGroup) error {

	defer wg.Done()

	var favFacData []model.Facility
	if err := s.Data.GetAllFavoriteFacilitiesForUser(ctx, userId, &favFacData); err != nil {
		fmt.Println("Err in getting favorites facilities for user")
		return err
	}
	fmt.Println("Fav fac data ---->", favFacData)
	tempfavMapFac := make(map[int32]model.Facility) // store facility_ids(key) -- Facilities(values) only for fav facilities
	for _, favEle := range favFacData {
		tempfavMapFac[favEle.FacilityID] = favEle
	}
	*favMapFac = tempfavMapFac

	return nil
}

func (s *Service) GetAllFacilities(ctx context.Context, mapFacOrder *map[int32]int32, faciltyData *[]model.Facility, req *pb.BookingSlotsRequestV2, wg *sync.WaitGroup) error {

	defer wg.Done()

	var sortBasedOnLatLong bool
	if req.UserLatitude <= 0 && req.UserLongitude <= 0 {
		sortBasedOnLatLong = false
	} else {
		sortBasedOnLatLong = true
	}
	var facData []model.Facility

	if err := s.Data.GetAllFacilities(ctx, &facData); err != nil {
		log.Println("Err in getting facilities")
		return err
	}

	tempmapFacOrder := make(map[int32]int32) //facility_id to display_order
	if sortBasedOnLatLong == true {
		sort.SliceStable(facData, func(i, j int) bool {
			return euclideanDistanceSquare(facData[i], req.UserLatitude, req.UserLongitude) < euclideanDistanceSquare(facData[j], req.UserLatitude, req.UserLongitude)
		})
	}

	for i, fEle := range facData {
		//	fEle.DisplayOrder = int32(i)
		tempmapFacOrder[fEle.FacilityID] = int32(i)
	}
	*mapFacOrder = tempmapFacOrder
	*faciltyData = facData

	return nil
}

func (s *Service) GetAllSports(ctx context.Context, mapSport *map[int32]model.Sport, wg *sync.WaitGroup) error {

	defer wg.Done()

	var spData []model.Sport
	if err := s.Data.GetAllSports(ctx, &spData); err != nil {
		log.Println("Err in getting sports")
		return err
	}

	tempmapSport := make(map[int32]model.Sport) // store facility_ids(key) -- Facilities(values)
	for _, spEle := range spData {
		tempmapSport[spEle.SportID] = spEle
	}

	*mapSport = tempmapSport

	return nil
}

func (s *Service) GetAllBookedSlotsForUser(ctx context.Context, bookingCond model.AllBooking, mapAllBookedForDate *map[int32][]model.AllBooking, wg *sync.WaitGroup) error {

	defer wg.Done()

	var allBookingsForDate []model.AllBooking
	if err := s.Data.GetAllBookedSlotsForUser(ctx, &bookingCond, &allBookingsForDate); err != nil {
		fmt.Println("Error in getting all booked slots for date  slot and user", err)
		return err
	}
	tempmapAllBookedForDate := make(map[int32][]model.AllBooking) // store slot_id(key) -- AllBooking(values)
	for _, el := range allBookingsForDate {
		tempmapAllBookedForDate[el.SlotID] = append(tempmapAllBookedForDate[el.SlotID], el)
	}
	*mapAllBookedForDate = tempmapAllBookedForDate

	return nil
}

func (s *Service) GetCoachingType(ctx context.Context, mapCoachingType *map[int32]model.CoachingType, wg *sync.WaitGroup) error {

	defer wg.Done()

	tempmapCoachingType := make(map[int32]model.CoachingType) // store slot_id(key) -- AllBooking(values)
	var coachingType []model.CoachingType
	coachingTypeCond := &model.CoachingType{}
	if err := s.Data.CoachingTypeGet(ctx, coachingTypeCond, &coachingType); err != nil {
		fmt.Println("Error in getting all coaching types: ", err)
		return err
	}
	for _, el := range coachingType {
		tempmapCoachingType[el.CoachingType] = el
	}
	*mapCoachingType = tempmapCoachingType
	return nil
}

//used in v2 -- new booking flow
func (s *Service) GetSlotAvailabilityStatsByCapacity(ctx context.Context, slotCapacityObj model.SlotCapacity, slotAvailability *model.SlotAvailability) error {
	// fmt.Println("Slot availability stats req ----- ", slotCapacityObj)

	var availabilityType int32
	var availabilityText string
	var infoText string

	if slotCapacityObj.Active == 0 {
		availabilityType = 5
		availabilityText = "Unavailable"
		infoText = "This slot is currently unavailable."
	} else if (slotCapacityObj.FSID == slotCapacityObj.BookingFSID && slotCapacityObj.FSID > 0) || slotCapacityObj.BookingID > 0 {
		availabilityType = 4
		availabilityText = "Booked"
		infoText = "You already have an existing booking for this slot."
	} else if slotCapacityObj.RemainingCapacity <= 0 {
		availabilityType = 3
		availabilityText = "Fully booked"
		infoText = "This slot is currently sold out."

	} else if float32(slotCapacityObj.Capacity)*0.2 > float32(slotCapacityObj.RemainingCapacity) { //20% thresold
		availabilityType = 2
		availabilityText = "Filling Fast"
		infoText = "Hurry up! Slots are filling fast."

	} else {
		if slotCapacityObj.CoachingFlag {
			availabilityType = 1
			availabilityText = "Available with coaching"
			infoText = "You will be sharing the arena with upto " + strconv.Itoa(int(slotCapacityObj.SpotsInPlayArena-1)) + " other players. Coach will be available at the facility for guidance."
		} else {
			availabilityType = 1
			availabilityText = "Available without coaching"
			infoText = "You will be sharing the arena with upto " + strconv.Itoa(int(slotCapacityObj.SpotsInPlayArena-1)) + " other players."
		}
	}

	slotAvailabilityObj := model.SlotAvailability{
		AvailabilityType: availabilityType,
		AvailabilityText: availabilityText,
		InfoText:         infoText,
	}

	*slotAvailability = slotAvailabilityObj

	return nil
}

func (s *Service) GetSlotAvailabilityInfo(ctx context.Context, bookingCond *model.AllBooking, slotAvailabilityInfo *model.SlotAvailabilityInfo, bookingFSID *int32, mapAllBookedForDate map[int32][]model.AllBooking) error {

	var slotAvailabilityType int32
	var slotAvailabilityText string
	var bookingFsId int32

	var bookingEntries []model.AllBooking
	// if err := s.Data.GetBookingForSlotAndDate(bookingCond, &bookingEntries); err != nil {
	// 	fmt.Println("Getting booking details for date and slot error", err)
	// 	fmt.Println("Moving forward without throwing error")
	// }

	bookingEntries = mapAllBookedForDate[bookingCond.SlotID]

	if len(bookingEntries) > 0 { //booking exists for date and slot
		slotAvailabilityType = 2
		slotAvailabilityText = "You already have an existing booking for this slot."
		bookingFsId = bookingEntries[0].FSID
	} else {
		slotAvailabilityType = 1
		slotAvailabilityText = "Available"
		bookingFsId = 0
	}
	//add more cases of slot unavailability here -- eg: event in facility, disabled slot etc

	slotAvailabilityInfoElement := model.SlotAvailabilityInfo{
		AvailabilityType: slotAvailabilityType,
		AvailabilityText: slotAvailabilityText,
	}

	*slotAvailabilityInfo = slotAvailabilityInfoElement
	*bookingFSID = bookingFsId

	return nil
}

func GetFSActiveBookingSlots(req *pb.BookingSlotsRequest, activeSlotsFS []model.FSActiveSlot, dao data.DataInterface) (error, []pb.ActiveSlot) {

	DateBookingTime := time.Unix(req.DateBooking, 0)
	LocalDateBookingTime, err := GetLocalDateTime(DateBookingTime)
	if err != nil {
		fmt.Println("Error in converting date into local time", err)
		return err, nil
	}

	activeSlotCondition := &model.FSActiveSlot{
		FSID: req.FsId,
		Date: LocalDateBookingTime,
	}

	if err := dao.BookingSlotsGetFS(activeSlotCondition, &activeSlotsFS); err != nil {
		errors.New(fmt.Sprintf("error getting active slots details: %v", err))
		return err, nil
	}

	activeSlotsArr := []pb.ActiveSlot{}
	for _, element := range activeSlotsFS {

		var availabilityType int32
		var availabilityText string
		var infoText string

		if element.Active == 0 {
			availabilityType = 4
			availabilityText = "Unavailable"
			infoText = "This slot is currently unavailable."

		} else if element.RemainingCapacity <= 0 {
			availabilityType = 3
			availabilityText = "Sold Out"
			infoText = "This slot is currently sold out."

		} else if float32(element.Capacity)*0.2 > float32(element.RemainingCapacity) { //20% thresold
			availabilityType = 2
			availabilityText = "Filling Fast"
			infoText = "Hurry up! Slots are filling fast."

		} else {
			availabilityType = 1
			availabilityText = "Available"
			infoText = "A maximum of " + strconv.Itoa(int(element.SpotsInPlayArena)) + " players can play in a court."
		}

		if len(element.InfoText) > 0 {
			infoText = element.InfoText
		}

		activeSlotObj := pb.ActiveSlot{
			SlotId:            element.SlotID,
			Timing:            element.Timing,
			StartHour:         element.StartHour,
			StartMin:          element.StartMin,
			EndHour:           element.EndHour,
			EndMin:            element.EndMin,
			CapacityRemaining: element.RemainingCapacity,
			AvailabilityType:  availabilityType,
			AvailabilityText:  availabilityText,
			InfoText:          infoText,
		}

		activeSlotsArr = append(activeSlotsArr, activeSlotObj)
	}

	return nil, activeSlotsArr
}

func GetPAActiveBookingSlots(req *pb.BookingSlotsRequest, activeSlotsPA []model.PAActiveSlot, dao data.DataInterface) (error, []pb.ActiveSlot) {

	DateBookingTime := time.Unix(req.DateBooking, 0)
	LocalDateBookingTime, err := GetLocalDateTime(DateBookingTime)
	if err != nil {
		fmt.Println("Error in converting date into local time", err)
		return err, nil
	}
	activeSlotCondition := &model.PAActiveSlot{
		FSID: req.FsId,
		Date: LocalDateBookingTime,
	}

	if err := dao.BookingSlotsGetPA(activeSlotCondition, &activeSlotsPA); err != nil {
		errors.New(fmt.Sprintf("error getting pa active slots details: %v", err))
		return err, nil
	}

	activeSlotsArr := []pb.ActiveSlot{}
	for _, element := range activeSlotsPA {

		var availabilityType int32
		var availabilityText string
		var infoText string

		if element.Active == 0 {
			availabilityType = 4
			availabilityText = "Unavailable"
			infoText = "This slot is currently unavailable."

		} else if element.RemainingCapacity <= 0 {
			availabilityType = 3
			availabilityText = "Sold Out"
			infoText = "This slot is currently sold out."

		} else if float32(element.Capacity)*0.2 > float32(element.RemainingCapacity) { //20% thresold
			availabilityType = 2
			availabilityText = "Filling Fast"
			infoText = "Hurry up! Slots are filling fast."

		} else {
			availabilityType = 1
			availabilityText = "Available"
			infoText = "You can bring a maximum of " + strconv.Itoa(int(element.SpotsInPlayArena+2)) + " people on one court."
		}

		if len(element.InfoText) > 0 {
			infoText = element.InfoText
		}

		activeSlotObj := pb.ActiveSlot{
			SlotId:            element.SlotID,
			Timing:            element.Timing,
			StartHour:         element.StartHour,
			StartMin:          element.StartMin,
			EndHour:           element.EndHour,
			EndMin:            element.EndMin,
			CapacityRemaining: element.RemainingCapacity,
			AvailabilityType:  availabilityType,
			AvailabilityText:  availabilityText,
			InfoText:          infoText,
		}

		activeSlotsArr = append(activeSlotsArr, activeSlotObj)
	}

	return nil, activeSlotsArr
}

func getSystemDayOfWeekFromDate(date time.Time) int32 {
	day := int32(date.Weekday())
	if day == 0 {
		return 7
	}
	return day
}

func (s *Service) CancelAllUpcomingBookings(ctx context.Context, req *pb.CancelAllUpcomingBookingReq, res *pb.Ack) error {
	userID := util.GetUserIDFromContext(ctx)
	var bookings []model.AllBooking
	bookingCond := &model.AllBooking{
		SubscriptionID: req.SubscriptionId,
	}
	now := time.Now()
	if err := s.Data.GetActiveBookingsForSubscriptionV2(bookingCond, &bookings, now); err != nil {
		log.Println("Func:CancelAllUpcomingBookings")
		log.Println("Error in getting current active bookings for SubscriptionId:%d, err:%v", req.SubscriptionId, err)
		return err
	}

	for _, ele := range bookings {
		pacsIDs := []int32{ele.PACSID}
		pacsIdsTrialCountMap := make(map[int32]int32)
		if ele.BookingTime.Year() >= now.Year() && ele.BookingTime.Month() >= now.Month() && ele.BookingTime.Day() > now.Day() {
			bookingCondition := &structs.CancelBookingCondition{
				BookingIds:      []int32{ele.BookingID},
				BookingCapacity: 1,
				SlotID:          ele.SlotID,
				FSID:            ele.FSID,
				PACSIDs:         pacsIDs,
				BookingTime:     ele.BookingTime,
			}

			if ele.BookingCategory == GUEST_BOOKING || ele.BookingCategory == TRIAL_BOOKING {
				pacsIdsTrialCountMap[ele.PACSID] = 1
				bookingCondition.PACSIDs_Trial_Count_Map = pacsIdsTrialCountMap
			}

			err := s.Data.CancelBookingV2(ctx, bookingCondition, userID)
			if err != nil {
				log.Printf("error in cancelling booking for user id: %d, error %v", userID, err)
				return err
			}
		}
	}

	res.Status = &pb.Status{
		Status:  success,
		Message: "All upcoming bookings are cancelled successfully!",
	}

	return nil
}

func (s *Service) CancelBookingV2(ctx context.Context, req *pb.CancelBookingRequestV2, res *pb.CancelBookingResponseV2) error {
	userID := util.GetUserIDFromContext(ctx)

	notAuthStatus := &pb.Status{
		Status:  notAuthorized,
		Message: "Not authorized",
	}

	badRequestStatus := &pb.Status{
		Status:  badRequest,
		Message: "Invalid request",
	}

	if userID == 0 {
		res.Status = notAuthStatus
		return nil
	}
	if len(req.BookingIds) == 0 {
		res.Status = badRequestStatus
		return nil
	}

	bookingCapacity := int32(0)
	bookings := make([]*model.AllBooking, 0)
	bookingsCancelled := make(map[int32]bool)
	var paIDs []int32
	var pacsIDs []int32
	var cancelledUids []int32

	var brn string
	var slot_id int32
	var fs_id int32
	var created_by_user int32
	var booking_time *tspb.Timestamp
	creationUserBeingCancelled := false
	var creationUserBookingId int32
	var cleverTapPayload *pb.CleverTapPayload

	pacsIdsTrialCountMap := make(map[int32]int32)

	for _, bookingId := range req.BookingIds {
		bookingDetails, err := s.Data.GetBookingDetailsByBookingID(ctx, bookingId)
		if err != nil {
			log.Printf("error in getting booking details for booking id: %d, error %v", bookingId, err)
			return err
		}

		brn = bookingDetails.BookingReferenceNumber
		slot_id = bookingDetails.SlotID
		fs_id = bookingDetails.FSID
		created_by_user = bookingDetails.CreatedBy
		bookingTimeStam, _ := ptypes.TimestampProto(bookingDetails.BookingTime)
		booking_time = bookingTimeStam

		if created_by_user == bookingDetails.UserID {
			creationUserBeingCancelled = true
			creationUserBookingId = bookingDetails.BookingID
		}

		cleverTapPayload = &pb.CleverTapPayload{
			BookingReferenceNumber: brn,
			SlotId:                 slot_id,
			FsId:                   fs_id,
			BookingTime:            booking_time,
		}
		res.CleverTapPayload = cleverTapPayload

		if bookingDetails.BookingCancelled { // if its already cancelled, ignore it
			continue
		}

		cancelledUids = append(cancelledUids, bookingDetails.UserID)

		bookingTimeStamp := bookingDetails.BookingTime.Unix()
		timeDiff := bookingTimeStamp - time.Now().Unix()

		if timeDiff < 0 {
			status := &pb.Status{
				Status:  failed,
				Message: "Can't cancel your booking. Your booking is in the past.",
			}
			res.Status = status

			return nil
		}
		limit := config.Get("codeIndependent", "maxCancelationTimeDifference").Int(30)
		maxCancelationTimeDifference := int64(limit * 60)

		if timeDiff < maxCancelationTimeDifference {
			status := &pb.Status{
				Status:  failed,
				Message: "Can't cancel your booking. Please, cancel slots " + strconv.Itoa(limit) + " minutes before the session starts.",
			}
			res.Status = status

			return nil
		}

		if !(bookingDetails.UserID == userID || bookingDetails.CreatedBy == userID) {
			res.Status = notAuthStatus
			return nil
		}
		bookings = append(bookings, bookingDetails)
		bookingsCancelled[bookingDetails.BookingID] = true
		bookingCapacity += 1
		paIDs = append(paIDs, bookingDetails.PAID)
		pacsIDs = append(pacsIDs, bookingDetails.PACSID)

		if bookingDetails.BookingCategory == GUEST_BOOKING || bookingDetails.BookingCategory == TRIAL_BOOKING {
			if val, ok := pacsIdsTrialCountMap[bookingDetails.PACSID]; ok {
				pacsIdsTrialCountMap[bookingDetails.PACSID] = val + 1
			} else {
				pacsIdsTrialCountMap[bookingDetails.PACSID] = 1
			}
		}
	}

	if featuresupport.SupportsBringAGuest(ctx) && creationUserBeingCancelled {
		guestBookings, err := s.Data.GetGuestBookingDetailsByBRN(ctx, brn)
		if err != nil {
			log.Printf("func:CancelBookingV2, error in getting guest bookings for bookingId:%d", creationUserBookingId)
		}
		for _, guestBooking := range *guestBookings {
			if bookingsCancelled[guestBooking.BookingID] {
				continue
			}
			req.BookingIds = append(req.BookingIds, guestBooking.BookingID)
			bookings = append(bookings, &guestBooking)
			bookingsCancelled[guestBooking.BookingID] = true
			bookingCapacity += 1
			paIDs = append(paIDs, guestBooking.PAID)
			pacsIDs = append(pacsIDs, guestBooking.PACSID)
			if val, ok := pacsIdsTrialCountMap[guestBooking.PACSID]; ok {
				pacsIdsTrialCountMap[guestBooking.PACSID] = val + 1
			} else {
				pacsIdsTrialCountMap[guestBooking.PACSID] = 1
			}
		}

	}

	// This means all bookings ids in request are already cancelled
	if len(bookings) == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: "Booking already cancelled!",
		}
		return nil
	}

	trial, trialErr := isTrial(ctx)
	if trialErr != nil {
		log.Printf("error in getting trial details before cancelling booking for user id: %d, error: %v", userID, trialErr)
		return trialErr
	}

	bookingCondition := &structs.CancelBookingCondition{
		BookingIds:              req.BookingIds,
		BookingCapacity:         bookingCapacity,
		SlotID:                  bookings[0].SlotID,
		FSID:                    bookings[0].FSID,
		PAIDs:                   paIDs,
		PACSIDs:                 pacsIDs,
		BookingTime:             bookings[0].BookingTime,
		PACSIDs_Trial_Count_Map: pacsIdsTrialCountMap,
	}
	log.Printf("func:CancelBookingV2, Booking Ids: %v, Booking Capacity: %d, PACS Ids:%v", bookingCondition.BookingIds, bookingCondition.BookingCapacity, bookingCondition.PACSIDs)
	err := s.Data.CancelBookingV2(ctx, bookingCondition, userID)
	if err != nil {
		log.Printf("error in cancelling booking for user id: %d, error %v", userID, err)
		return err
	}

	trialAfterCancel, err := isTrial(ctx)
	if err != nil {
		log.Printf("error in getting trial details after cancelling booking for user id: %d, error: %v", userID, err)
		return err
	}

	if !trial && trialAfterCancel {
		if err := deleteUserCategoryCache(ctx, util.GetUserIDFromContext(ctx), util.GetCityIDFromContext(ctx), MasterkeyCategoryID); err != nil {
			log.Println("Error in deleting user suugestion categories cache data after cancelling 2nd trial booking: %v", err)
			return err
		}
	}

	go s.sendCancelBookingNotification(ctx, bookings[0].BookingReferenceNumber, cancelledUids)

	res.Status = &pb.Status{
		Status:  success,
		Message: "Booking cancelled successfully!",
	}
	return nil
}

func (s *Service) CancelBooking(ctx context.Context, req *pb.CancelBookingRequest, res *pb.Ack) error {

	fmt.Println("Request -----", req)
	cancelBookingData := &pb.CancelBooking{} //stores all data other than request

	currentTimeStamp, _ := ptypes.TimestampProto(time.Now())

	sendCancelNotificationSports := false
	sendCancelNotificationSeals := false

	if len(req.BookingReferenceNumber) == 8 { // sports booking cancellation

		bookingGetData := &model.AllBooking{
			BookingReferenceNumber: req.BookingReferenceNumber,
			BookingCancelled:       false,
		}

		var allBooking []model.AllBooking
		if err := s.Data.BookingDetailsGet(ctx, bookingGetData, &allBooking, 1, 1, 10); err != nil {

			error := &pb.Error{
				Code:        305,
				Description: "Error in fetching Booking Details!",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Error in fetching Booking Details!",
			}
			res.Status = status

			return nil
		}

		var timeDiff int32
		bookingCancelled := false
		var bookingUserId int32
		var createdBy int32
		for _, element := range allBooking {
			bookingTimeStamp, _ := ptypes.TimestampProto(element.BookingTime)
			timeDiff = int32(bookingTimeStamp.Seconds - currentTimeStamp.Seconds)
			bookingCancelled = element.BookingCancelled
			bookingUserId = element.UserID
			createdBy = element.CreatedBy

			cancelBookingData.BookedCapacity = element.BookingCapacity
			cancelBookingData.PaId = element.PAID
			cancelBookingData.SlotId = element.SlotID
			cancelBookingData.FsId = element.FSID
			cancelBookingData.BookingTime = bookingTimeStamp
			cancelBookingData.OnlineSessionId = element.OnlineSessionID

			break
		}

		if !bookingCancelled && len(allBooking) > 0 { //not cancelled

			if bookingUserId != req.ActionUserId && createdBy != req.ActionUserId {
				status := &pb.Status{
					Status:  "failure",
					Message: "You cannot cancel booking of another user!",
				}
				res.Status = status
				return nil
			}

			maxCancelationTimeDifference := int32(30 * 60)
			if timeDiff < maxCancelationTimeDifference {
				status := &pb.Status{
					Status:  "failure",
					Message: "Can't cancel your booking Please, cancel slots 30 minutes before the session starts.",
				}
				res.Status = status

				return nil
			}

			if !req.ForBookingModification { //skip charge levied for booking modification
				if req.MultiplicationFactor == 0 {
					fmt.Println("Calculating multiplication factor!")
					multiplicationFactor := GetMultiplicationFactorForBookingCancellation(timeDiff)
					req.MultiplicationFactor = multiplicationFactor
				}
			}

			if err := s.Data.CancelBooking(req, cancelBookingData); err != nil {
				fmt.Println("Could not cancel boooking.")

				error := &pb.Error{
					Code:        308,
					Description: "Error in cancelling Booking!",
				}
				res.Error = append(res.Error, error)

				status := &pb.Status{
					Status:  "failure",
					Message: "Error in cancelling Booking!",
				}
				res.Status = status

				return nil
			}

			status := &pb.Status{
				Status:  "success",
				Message: "Booking cancelled successfully!",
			}
			res.Status = status

			sendCancelNotificationSports = true
		} else {

			if len(allBooking) <= 0 {
				fmt.Println("Invalid Reference Number!")
				error := &pb.Error{
					Code:        310,
					Description: "Invalid booking reference number!",
				}
				res.Error = append(res.Error, error)

				status := &pb.Status{
					Status:  "failure",
					Message: "Invalid booking reference number!",
				}
				res.Status = status

			} else {
				fmt.Println("Already cancelled!")
				error := &pb.Error{
					Code:        309,
					Description: "Booking already cancelled!",
				}
				res.Error = append(res.Error, error)

				status := &pb.Status{
					Status:  "failure",
					Message: "Booking already cancelled!",
				}
				res.Status = status
			}

			return nil
		}

		var empty_str []string
		go s.Data.SportsBookingDataToSpreadsheetsAndSendTrialAlert(req.BookingReferenceNumber, 0, empty_str)

	} else if len(req.BookingReferenceNumber) == 9 { // seals booking cancellation
		if err, errTxt := s.CancelSealsBooking(req); err != nil || len(errTxt) > 0 {
			fmt.Println("Could not cancel seals booking")
			error := &pb.Error{
				Code:        311,
				Description: "Invalid reference number or booking already cancelled",
			}
			res.Error = append(res.Error, error)

			status := &pb.Status{
				Status:  "failure",
				Message: "Invalid reference number or booking already cancelled",
			}
			res.Status = status
			return err
		}

		status := &pb.Status{
			Status:  "success",
			Message: "Booking cancelled successfully!",
		}
		res.Status = status

		var empty_str []string
		go s.Data.SportsBookingDataToSpreadsheetsAndSendTrialAlert(req.BookingReferenceNumber, 3, empty_str)
		sendCancelNotificationSeals = true
	}

	if sendCancelNotificationSports { //send cancel notification to seals app
		if err := s.sendCancelNotificationForBooking(ctx, req.BookingReferenceNumber); err != nil {
			fmt.Println("Error in sending booking cancelled notification --> ", err)
		}
	}

	if sendCancelNotificationSeals {
		fmt.Println("Cancel notification handled in seals dashboard.....")
	}

	return nil
}

func (s *Service) sendCancelNotificationForBooking(ctx context.Context, bookingRef string) error {

	bookingGetData := &model.AllBooking{
		BookingReferenceNumber: bookingRef,
	}

	var allBooking []model.AllBooking

	app_condition := make(map[string]string)

	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"

	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	if err := s.Data.BookingDetailsGet(ctx, bookingGetData, &allBooking, 2, 1, 1); err != nil {
		fmt.Println("Could not get booking details while sending cancel notificaiton..")
		return err
	}

	curTime := time.Now()

	//notification exp time
	defExpTime := config.Get("static", "default_notification_expiry_time_in_hours").Int(4)
	expTime := curTime.AddDate(0, 0, defExpTime)
	expTimestamp := expTime.Unix()

	for _, aBooking := range allBooking {

		if aBooking.BookingCancelled {
			usersIds := make([]int32, 0)
			usersIds = append(usersIds, aBooking.UserID)
			var nText string

			if aBooking.OnlineSessionID != 0 {
				nText = "Hi, Your " + aBooking.TitleB + " session for " + aBooking.Timing + " has been cancelled."
			} else {
				nText = "Hi, Your " + aBooking.SportName + " session for " + aBooking.Timing + " at " + aBooking.DisplayName + " has been cancelled."
			}
			nData := structs.FcmNotification{
				Title:         "Booking Cancelled!",
				Text:          nText,
				UserIds:       usersIds,
				Id:            0,
				Action:        "past_bookings",
				ConditionData: condition_data,
				CtaText:       bookingRef,
				ExpiryDate:    expTimestamp,
				SubCategory:   int32(notificationPB.NotificationSubCategories_BOOKING_CANCELLED),
			}

			s.Data.SendNotification(&nData)
		}
	}

	return nil
}

func (s *Service) CancelSealsBooking(cancelReq *pb.CancelBookingRequest) (error, string) {
	fmt.Println("Seals Cancel --- ", cancelReq)

	SealsCancelBookingRequest := &pb.SealsCancelBookingRequest{
		BookingReferenceNumber: cancelReq.BookingReferenceNumber,
		ActionUserId:           cancelReq.ActionUserId,
		CancelReason:           "",
	}

	buf := new(bytes.Buffer)
	json.NewEncoder(buf).Encode(SealsCancelBookingRequest)

	xJUserid := strconv.Itoa(int(cancelReq.ActionUserId))
	req, errReq := http.NewRequest("POST", "https://www.getfitso.com/api/v1/cancelSealsBookingsForSports.json", buf)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-J-User-Id", xJUserid)

	if errReq != nil {
		fmt.Println("Error in initiating requset to seals")
	}
	req.Close = true
	resp, errResp := http.DefaultClient.Do(req)
	if errResp != nil || resp == nil {
		fmt.Println("Error in getting response from seals --", errResp)
		return errResp, ""
	}

	defer resp.Body.Close()

	body, errBody := ioutil.ReadAll(resp.Body)
	if errBody != nil {
		fmt.Println("Error in reading resp body")
	}

	//parse swim response
	fmt.Println("Response from seals -- ", string(body))
	swimResponse := make(map[string]interface{})
	b := []byte(string(body))

	err := json.Unmarshal(b, &swimResponse)
	if err != nil {
		fmt.Println("Swim slot cap Response unmarshal error", err)
		return err, ""
	}

	if swimResponse["status"].(string) == "success" {
		fmt.Println("Booking cancelled successfully!")
		return nil, ""
	} else {
		return nil, swimResponse["status"].(string)
	}

	return nil, ""
}

func GetMultiplicationFactorForBookingCancellation(timeDiff int32) float32 {
	mFactor := 0.0

	if timeDiff >= 60*60*2 { //2 hrs
		mFactor = multiplicationFactor2hrs
	} else if timeDiff >= 60*60 { //1 hrs
		mFactor = multiplicationFactor1hrs
	} else if timeDiff >= 60*30 { //30 min
		mFactor = multiplicationFactor30min
	} else {
		mFactor = multiplicationFactor0min
	}

	return float32(mFactor)
}

func (s *Service) GetBookingFeedbackOptions(ctx context.Context, req *pb.BookingFeedbackRequest, res *pb.BookingFeedbackResponse) error {
	fmt.Println("Req ----- ", req)
	//get latest past booking for user without any feedback, if it has feedback -- return empty with feedback_applicable = false, else proceed with true

	var feedbackApplicable bool
	var bookingData *pb.Booking
	if len(req.BookingReferenceNumber) > 0 {
		bookingDataBRN := &pb.Booking{
			BookingReferenceNumber:  req.BookingReferenceNumber,
			Timeline:                2,
			Start:                   0,
			Count:                   1,
			BookingCancelled:        false,
			IgnoreCancelledBookings: true,
			TabId:                   req.TabId,
		}

		bookingData = bookingDataBRN
	} else {
		bookingDataUID := &pb.Booking{
			UserId:                  req.UserId,
			Timeline:                2,
			Start:                   0,
			Count:                   1,
			BookingCancelled:        false,
			IgnoreCancelledBookings: true,
			TabId:                   req.TabId,
		}

		bookingData = bookingDataUID
	}
	bookingResponseData := &pb.BookingResponse{}
	if err := s.GetBookingDetails(context.TODO(), bookingData, bookingResponseData); err != nil {
		fmt.Println("Error in fetching existing booking for user_id - ", req.UserId, "!")
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not fetch last booking!",
		}
		res.Status = status
		return err
	}

	var lastBookingArr []*pb.Booking
	lastBookingArr = bookingResponseData.Booking

	if len(lastBookingArr) > 0 {

		//working only on the first booking
		var lastBooking *pb.Booking
		for _, element := range lastBookingArr {
			lastBooking = element
			break
		}

		isPartnerFacility := false
		if lastBooking.Facility != nil && lastBooking.Facility.FacilityType == 3 {
			isPartnerFacility = true
		}
		if lastBooking.AttendanceFlag == false && lastBooking.IsTrial == false && isPartnerFacility == false {
			status := &pb.Status{
				Status:  "success",
				Message: "user didn't attend the session",
			}
			res.Status = status
			return nil
		}

		//check if feedback exists for last booking
		var feedbackData []model.SportsFeedbackResponse
		feedbackCond := &model.SportsFeedbackResponse{
			BookingReferenceNumber: lastBooking.BookingReferenceNumber,
		}
		var isOnlineSession bool
		if lastBooking.OnlineSession != nil {
			isOnlineSession = true
		} else {
			isOnlineSession = false
		}
		if err := s.Data.GetFeedbackForBooking(feedbackCond, &feedbackData); err != nil {
			fmt.Println("Could not fetch feedback for booking reference number.")
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch last booking!",
			}
			res.Status = status
			return err
		}

		if len(feedbackData) > 0 {
			fmt.Println("Feedback exists.")
			feedbackApplicable = false
			res.FeedbackApplicable = feedbackApplicable
			status := &pb.Status{
				Status:  "success",
				Message: "Feedback exists for bookings!",
			}
			res.Status = status
		} else { //no feedback exists
			fmt.Println("No feedback case.")
			res.Booking = lastBooking

			feedbackApplicable = true
			res.FeedbackApplicable = feedbackApplicable

			//get feedback options here
			var ratingsForFeedback []*pb.SportsFeedbackRating
			if err := s.GetSportsFeedbackRatingOptions(ctx, &ratingsForFeedback, isOnlineSession, false); err != nil {
				fmt.Println("Could not fetch feedback options!")
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not fetch feedback options!",
				}
				res.Status = status
				return err
			}
			res.Ratings = ratingsForFeedback

			status := &pb.Status{
				Status:  "success",
				Message: "Feedback does not exists for bookings!",
			}
			res.Status = status
		}

	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "No bookings exist",
		}
		res.Status = status
		feedbackApplicable = false
		res.FeedbackApplicable = feedbackApplicable
	}

	return nil
}

func (s *Service) GetSportsFeedbackRatingOptions(ctx context.Context, ratings *[]*pb.SportsFeedbackRating, isOnlineSession bool, isAcademyFlow bool) error {
	var productCategoryId int32
	if isAcademyFlow {
		productCategoryId = AcademyCategoryID
	}
	//get all category_options and create map with feedback_category_id
	categoryIdOptionsMap := make(map[int32][]*pb.SportsFeedbackOption)
	var categoryOptions []model.SportsFeedbackOption
	if err := s.Data.GetAllFeedbackOptions(ctx, &categoryOptions, productCategoryId); err != nil {
		log.Println("Could not fetch feedback options!")
		return err
	}
	for _, element := range categoryOptions {
		categoryIdOptionsMap[element.FeedbackCategoryId] = append(categoryIdOptionsMap[element.FeedbackCategoryId], element.Proto())
	}

	//fetch ratings and add all options there
	var ratingsArr []*pb.SportsFeedbackRating
	var ratingsData []model.SportsFeedbackRating
	if err := s.Data.GetAllRatingOptions(ctx, &ratingsData); err != nil {
		log.Println("Could not fetch ratings data!")
		return err
	}
	for _, element := range ratingsData {
		element.CategoryOptions = categoryIdOptionsMap[element.FeedbackCategoryId]
		ratingsArr = append(ratingsArr, element.Proto())
	}

	*ratings = ratingsArr

	return nil
}

//call this function for both insert and update
func (s *Service) SaveBookingFeedback(ctx context.Context, req *pb.SaveBookingFeedbackRequest, res *pb.SaveBookingFeedbackResponse) error {
	fmt.Println("req -- ", req)
	if req.RatingId > 0 && len(req.BookingReferenceNumber) > 0 {
		//save feedback in feedback_responses table and create options entry in response_options
		var feedbackCreateData model.SportsFeedbackResponse
		//check if feedback for this brn already exists
		_, bookingReferenceNumberArray := s.GetConsecutiveBRNForFeedback(ctx, req.UserId, req.BookingReferenceNumber)
		bookingReferenceNumberArray = append(bookingReferenceNumberArray, req.BookingReferenceNumber)

		for _, bookingReferenceNumber := range bookingReferenceNumberArray {
			var lastFeedbackData []model.SportsFeedbackResponse
			feedbackCond := &model.SportsFeedbackResponse{
				BookingReferenceNumber: bookingReferenceNumber,
			}

			if err := s.Data.GetFeedbackForBooking(feedbackCond, &lastFeedbackData); err != nil {
				fmt.Println("Could not fetch last feedback for booking reference number.")
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not fetch last booking!",
				}
				res.Status = status
				return err
			}

			feedbackCreateData = model.SportsFeedbackResponse{
				BookingReferenceNumber: bookingReferenceNumber,
				Note:                   req.Note,
				RatingId:               req.RatingId,
				AppType:                req.AppType,
				AppVersion:             req.AppVersion,
			}

			if len(lastFeedbackData) > 0 { //feedback exists -- update
				fmt.Println("Feedback already exists for brn - ", bookingReferenceNumber)
				var feedbackResponseId int32
				for _, ele := range lastFeedbackData {
					feedbackResponseId = ele.FeedbackResponseId
				}
				//sets deleted flag to 1
				if err := s.Data.UpdateFeedbackResponse(&feedbackResponseId, &feedbackCreateData, req.CategoryOptions); err != nil {
					fmt.Println("Could not update feedback response")
					status := &pb.Status{
						Status:  "failure",
						Message: "Could not update feedback for - " + bookingReferenceNumber,
					}
					res.Status = status
					return err
				}
			}

			if err := s.Data.SaveFeedbackResponse(&feedbackCreateData, req.CategoryOptions); err != nil {
				fmt.Println("Could not save feedback response")
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not save feedback for - " + bookingReferenceNumber,
				}
				res.Status = status
				return err
			}
		}

		status := &pb.Status{
			Status:  "success",
			Message: "Feedback saved successfully!",
		}
		res.Status = status

		if feedbackCreateData.RatingId < 5 { //now sending mail for all-ratings(<=5)
			go s.SendFeedbackAlertMail(ctx, &feedbackCreateData, req.CategoryOptions)
		}

	} else {
		//form failure msg
		status := &pb.Status{
			Status:  "failure",
			Message: "Insufficient Data",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) SaveBookingFeedbackV2(ctx context.Context, req *pb.SaveBookingFeedbackRequest, res *pb.SaveBookingFeedbackResponse) error {

	userID := util.GetUserIDFromContext(ctx)
	appVersion := util.GetAppVersionFromContext(ctx)
	appType := util.GetClientIDFromContext(ctx)

	cleverTapPayload := &pb.CleverTapPayload{
		BookingReferenceNumber: req.BookingReferenceNumber,
		RatingId:               req.RatingId,
		CreatedBy:              userID,
		Note:                   req.Note,
	}
	res.CleverTapPayload = cleverTapPayload
	if userID == 0 {
		status := &pb.Status{
			Status:  notAuthorized,
			Message: "USER NOT AUTHORIZED",
		}
		res.Status = status
		return nil
	}

	if req.BookingId != 0 && len(req.BookingReferenceNumber) >= 0 {

		userBookings, err := s.Data.GetBookingsByReferenceNumbers(ctx, []string{req.BookingReferenceNumber})
		if err != nil {
			log.Printf("Unable to get user bookings for brn : %s, Error: %v", req.BookingReferenceNumber, err)
			return err
		}
		if len(userBookings) == 0 {
			errStr := fmt.Sprintf("No bookings available for reference no %s", req.BookingReferenceNumber)
			log.Println(errStr)
			return errors.New(errStr)
		}

		userFeedbacks, err := s.Data.GetFeedbacksForBRN(ctx, req.BookingReferenceNumber)
		if err != nil {
			log.Printf("Unable to get user feedbacks for booking id : %d, Error: %v", req.BookingId, err)
			return err
		}

		status := &pb.Status{
			Status:  failed,
			Message: "Could not save feedback",
		}
		res.Status = status

		feedbackToStore := model.SportsFeedbackResponse{
			BookingReferenceNumber: req.BookingReferenceNumber,
			Note:                   req.Note,
			RatingId:               req.RatingId,
			AppType:                appType,
			AppVersion:             appVersion,
			CreatedBy:              userID,
		}
		feedbackCreateData := feedbackToStore
		parentIndex := -1
		childIndex := -1
		bookingIdCancelledMap := make(map[int32]bool)
		for i, userBooking := range userBookings {

			if userBooking.BookingTime.Unix()-time.Now().Unix() > 0 {
				status := &pb.Status{
					Status:  badRequest,
					Message: "Could not save feedback",
				}
				res.Status = status
				return nil
			}
			if userBooking.UserID == userID && userBooking.BookingCancelled == true {
				bookingIdCancelledMap[userBooking.BookingID] = userBooking.BookingCancelled
				continue
			}
			if userBooking.CreatedBy == userID {
				parentIndex = i
			} else if userBooking.UserID == userID {
				childIndex = i
			}
			bookingIdCancelledMap[userBooking.BookingID] = userBooking.BookingCancelled
		}
		if parentIndex == -1 && childIndex == -1 {
			status := &pb.Status{
				Status:  badRequest,
				Message: "Could not save the feedback",
			}
			res.Status = status
			return nil
		}
		if len(userFeedbacks) == 0 {
			//if userID - create 3/ create 1
			if parentIndex > -1 {
				for _, userBooking := range userBookings {
					if userBooking.BookingCancelled == true {
						continue
					} else {
						//save feedback
						feedbackCreateData = feedbackToStore
						feedbackCreateData.BookingId = userBooking.BookingID
						if err := s.Data.SaveFeedbackResponse(&feedbackCreateData, req.CategoryOptions); err != nil {
							log.Println("Could not save feedback response")
							return err
						}
					}
				}
			} else {
				//save feedback with childIndex
				feedbackCreateData.BookingId = userBookings[childIndex].BookingID
				if err := s.Data.SaveFeedbackResponse(&feedbackCreateData, req.CategoryOptions); err != nil {
					log.Println("Could not save feedback response")
					return err
				}
			}
		} else {

			if parentIndex > -1 {
				// if feed createdby - same then delete, create
				bookingIdCreatedByMap := make(map[int32]int32)
				for _, userFeedback := range userFeedbacks {
					if userFeedback.CreatedBy == userID && !bookingIdCancelledMap[userFeedback.BookingId] {
						// delete
						if err := s.Data.UpdateFeedbackResponse(&userFeedback.FeedbackResponseId, &feedbackCreateData, req.CategoryOptions); err != nil {
							log.Println("Could not delete feedback response")
							return err
						}
					}
					bookingIdCreatedByMap[userFeedback.BookingId] = userFeedback.CreatedBy
				}
				for _, userBooking := range userBookings {

					if userBooking.BookingCancelled == true {
						continue
					} else if bookingIdCreatedByMap[userBooking.BookingID] == 0 || bookingIdCreatedByMap[userBooking.BookingID] == userID {
						//save feedback
						feedbackCreateData = feedbackToStore
						feedbackCreateData.BookingId = userBooking.BookingID
						if err := s.Data.SaveFeedbackResponse(&feedbackCreateData, req.CategoryOptions); err != nil {
							log.Println("Could not save feedback response")
							return err
						}
					}
				}
			} else {
				// check child feedback exist -yes delete then create
				for _, userFeedback := range userFeedbacks {
					if userFeedback.BookingId == userBookings[childIndex].BookingID {
						// delete
						if err := s.Data.UpdateFeedbackResponse(&userFeedback.FeedbackResponseId, &feedbackCreateData, req.CategoryOptions); err != nil {
							log.Println("Could not delete feedback response")
							return err
						}
						break
					}
				}
				// save feedback with childindex
				feedbackCreateData.BookingId = userBookings[childIndex].BookingID
				if err := s.Data.SaveFeedbackResponse(&feedbackCreateData, req.CategoryOptions); err != nil {
					log.Println("Could not save feedback response")
					return err
				}
			}
		}
		status = &pb.Status{
			Status:  success,
			Message: "Feedback Saved successfully",
		}
		res.Status = status

		if feedbackCreateData.RatingId < 4 && len(req.Note) > 0 {
			go s.SendFeedbackAlertMail(ctx, &feedbackCreateData, req.CategoryOptions)
		}

	} else {
		status := &pb.Status{
			Status:  badRequest,
			Message: "Pass BRN and BookingId",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetConsecutiveBRNForFeedback(ctx context.Context, userId int32, bookingReferenceNumber string) (error, []string) {

	bookingData := &pb.Booking{
		UserId:                  userId,
		Timeline:                2,
		Start:                   0,
		Count:                   3,
		BookingCancelled:        false,
		IgnoreCancelledBookings: true,
	}

	var validBookings []string

	bookingResponseData := &pb.BookingResponse{}
	if err := s.GetBookingDetails(ctx, bookingData, bookingResponseData); err != nil {
		fmt.Println("Error in fetching existing booking for user_id - ", userId, "!")
		return err, validBookings
	}
	bookings := bookingResponseData.Booking

	var lastBooking pb.Booking
	for _, ele := range bookings {
		fmt.Println(ele.BookingReferenceNumber)
		if ele.BookingReferenceNumber == bookingReferenceNumber {
			lastBooking = *ele
			break
		}

	}
	for _, validBooking := range bookings { //check for booking

		//attendace check
		if validBooking.AttendanceFlag == false {
			continue
		}
		// onlinesession check

		if validBooking.OnlineSession != nil || lastBooking.OnlineSession != nil {
			continue
		}
		// feedback exist check
		var feedbackData []model.SportsFeedbackResponse
		feedbackCond := &model.SportsFeedbackResponse{
			BookingReferenceNumber: validBooking.BookingReferenceNumber,
		}

		if err := s.Data.GetFeedbackForBooking(feedbackCond, &feedbackData); err != nil {
			fmt.Println("Could not fetch feedback for booking reference number.")
			return err, validBookings
		}

		if len(feedbackData) > 0 {
			continue
		}
		// time frame check
		diff := lastBooking.BookingTime.GetSeconds() - validBooking.BookingTime.GetSeconds()
		if diff <= 0 || (diff >= 2*60*60) || validBooking.Facility.FacilityId != lastBooking.Facility.FacilityId {
			continue
		}

		validBookings = append(validBookings, validBooking.BookingReferenceNumber)

	}

	return nil, validBookings
}

func (s *Service) SendFeedbackAlertMail(ctx context.Context, feedbackCreateData *model.SportsFeedbackResponse, categoryOptions []int32) error {
	fmt.Println("-------------------feedback alert mail", feedbackCreateData)

	//fetch all the required data here ; user; product; facilitySport;
	var userProduct model.UserProduct
	if err := s.Data.GetUserProductDetailsByBRN(feedbackCreateData.BookingReferenceNumber, &userProduct); err != nil {
		fmt.Println("Could not get user product details by brn..")
		return err
	}
	if len(feedbackCreateData.BookingReferenceNumber) == 9 { //seals
		userProduct.SportName = "Swimming"
	}

	//fetch category option names
	var feedOptions []model.SportsFeedbackOption
	if err := s.Data.GetCategoryOptionNamesByIds(categoryOptions, &feedOptions); err != nil {
		fmt.Println("Could not fetch category option names")
		return err
	}

	//create comma separated option title array
	var optTitleArr []string
	for _, opEle := range feedOptions {
		optTitleArr = append(optTitleArr, opEle.Title)
	}
	var subjectStr string
	if userProduct.OnlineSessionID > 0 {
		subjectStr = "Feedback for Live Sessions |" + strconv.Itoa(int(userProduct.OnlineSessionID)) + "|" + feedbackCreateData.BookingReferenceNumber + " (" + strconv.Itoa(int(userProduct.UserID)) + ")"
	} else {
		subjectStr = "Feedback for " + userProduct.SportName + " at " + userProduct.FacilityName + " | " + userProduct.Name + " | " + feedbackCreateData.BookingReferenceNumber + " (" + strconv.Itoa(int(userProduct.UserID)) + ")"
	}
	messageStr := "<b>Feedback</b><br>"
	messageStr += "Rating - " + strconv.Itoa(int(feedbackCreateData.RatingId)) + "<br>"
	messageStr += "Options Selected - " + strings.Join(optTitleArr, ", ") + "<br>"
	messageStr += "Note - " + feedbackCreateData.Note + "<br><br>"

	messageStr += "<b>Session Details</b><br>"
	messageStr += "Booking Reference Number - " + feedbackCreateData.BookingReferenceNumber + "<br>"
	messageStr += "Slot Timing - " + userProduct.Timing + "<br>"

	if len(feedbackCreateData.BookingReferenceNumber) == 9 { //seals
		//handle booking date format
		BookingDate, _ := ptypes.TimestampProto(userProduct.SealsBookingDate)
		bookingTime := time.Unix(BookingDate.Seconds, 0)
		bookingTimeLocal, err := GetLocalDateTime(bookingTime)
		if err != nil {
			fmt.Println("time converison error -- ", err)
			return err
		}
		messageStr += "Booking Date - " + bookingTimeLocal.Format(formatYMD) + "<br>"
	} else {
		//handle booking date format
		BookingDate, _ := ptypes.TimestampProto(userProduct.BookingDate)
		bookingTime := time.Unix(BookingDate.Seconds, 0)
		bookingTimeLocal, err := GetLocalDateTime(bookingTime)
		if err != nil {
			fmt.Println("time converison error -- ", err)
			return err
		}
		messageStr += "Booking Date - " + bookingTimeLocal.Format(formatYMD) + "<br>"
	}
	messageStr += "AppType - " + feedbackCreateData.AppType + "<br>"
	messageStr += "AppVersion - " + feedbackCreateData.AppVersion + "<br>"
	if len(userProduct.CoachingText) > 0 {
		messageStr += "Coaching Type - " + userProduct.CoachingText + "<br>"

	} else {
		messageStr += "Coaching Type - Guided Environment <br>"
	}

	if len(userProduct.FacilityName) > 0 {
		messageStr += "Facility Sport - " + userProduct.FacilityName + " (" + userProduct.SportName + ")<br>"
	}

	messageStr += "<br>"
	messageStr += "<b>Plan Details</b><br>"
	messageStr += "User ID - " + strconv.Itoa(int(userProduct.UserID)) + "<br>"
	messageStr += "User Email - " + userProduct.Email + "<br>"
	messageStr += "User Phone - " + userProduct.Phone + "<br>"
	if userProduct.OnlineSessionID == 0 {
		messageStr += "Subscription ID - " + strconv.Itoa(int(userProduct.SubscriptionID)) + "<br>"
		messageStr += "Product ID - " + strconv.Itoa(int(userProduct.ProductID)) + "<br>"
		messageStr += "Product Description - " + userProduct.ProductDescription + "<br>"
	}
	messageStr += "<br>"
	if userProduct.OnlineSessionID > 0 {
		messageStr += "<b>Live Session Details</b><br>"
		messageStr += "OnlineSession ID - " + strconv.Itoa(int(userProduct.OnlineSessionID)) + "<br>"
		messageStr += "Session Description - " + userProduct.SessionDescription + "<br>"
		messageStr += "Title A - " + userProduct.TitleA + "<br>"
		messageStr += "Title B  - " + userProduct.TitleB + "<br>"
		messageStr += "Trainer Name - " + userProduct.ProviderRealName + "<br><br>"
	}

	freshdeskTicketPayload := &userPB.FreshdeskTicket{
		UserId:      userProduct.UserID,
		Subject:     subjectStr,
		Description: messageStr,
		Email:       userProduct.Email,
		Name:        userProduct.Name,
		Phone:       userProduct.Phone,
	}

	ucl := util.GetUserClient()

	_, err := ucl.CreateFreshdeskTicket(ctx, freshdeskTicketPayload)
	if err != nil {
		log.Printf("Error in generating freshdesk ticket for feedback, UserId:%d, err: %v", userProduct.UserID, err)
		return err
	}

	return nil
}

func (s *Service) MarkInactiveDayForFsIds(ctx context.Context, req *pb.MarkInactiveDayRequest, res *pb.Ack) error {
	if len(req.SportsInactiveDays) == 0 || req.ReasonId == 0 {
		res.Status = &pb.Status{
			Status:  failed,
			Message: badRequest,
		}
		return nil
	}
	var sportsInactiveDays []model.SportsInactiveDay
	for _, inactiveDayReq := range req.SportsInactiveDays {
		inactiveDateTimeObj := time.Unix(inactiveDayReq.Date, 0)
		record := model.SportsInactiveDay{
			Title:     req.Title,
			FsId:      inactiveDayReq.FsId,
			Date:      inactiveDateTimeObj,
			ReasonId:  req.ReasonId,
			CreatedBy: req.CreatedBy,
			UpdatedBy: req.CreatedBy,
		}
		sportsInactiveDays = append(sportsInactiveDays, record)
	}
	if err := s.Data.BulkMarkDeletedFlagForInactiveDays(ctx, sportsInactiveDays); err != nil {
		log.Printf("func: MarkInactiveDayForFsIds, could not mark deleted flag for inactive days, Error: %v", err)
		return err
	}
	if err := s.Data.BulkInsertInactiveDays(ctx, sportsInactiveDays); err != nil {
		log.Printf("func: MarkInactiveDayForFsIds, could not insert inactive days, Error: %v", err)
		return err
	}
	res.Status = &pb.Status{
		Status:  success,
		Message: "Successfully updated inactive days.",
	}
	return nil
}

func (s *Service) BlockInventory(ctx context.Context, req *pb.BlockInventoryRequest, res *pb.Ack) error {
	if req.Date <= 0 || len(req.FsSlotsData) == 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "Incorrect input Data",
		}
		res.Status = status
		return nil
	}

	blockDateTimeObj := time.Unix(req.Date, 0)
	dayOfWeek := int32(blockDateTimeObj.Weekday())
	if dayOfWeek == 0 { // for sunday
		dayOfWeek = 7
	}

	var pacsInfoData []structs.FsPaSlotDate
	var fsIds []int32
	for _, fsSlotData := range req.FsSlotsData {
		item := structs.FsPaSlotDate{
			FSID:                   fsSlotData.FsId,
			SlotID:                 fsSlotData.SlotId,
			PAID:                   fsSlotData.PaId,
			ProductArenaCategoryId: fsSlotData.ProductArenaCategoryId,
			DayOfWeek:              dayOfWeek,
		}
		pacsInfoData = append(pacsInfoData, item)

		if !Contains(fsIds, item.FSID) {
			fsIds = append(fsIds, item.FSID)
		}
	}
	pacsDetails, err := s.Data.GetPacsIdFromPacsInfo(ctx, pacsInfoData)
	if err != nil {
		log.Printf("Func:BlockInventory | Data:GetPacsIdFromPacsInfo | Error:%v", err)
		return err
	}

	var blockedInventory []model.SportsBlockedInventory
	var paCapacityDetails []structs.FsPaSlotDate

	for _, val := range pacsDetails {
		blockedInventoryRow := model.SportsBlockedInventory{
			Date:             blockDateTimeObj,
			FSID:             val.FSID,
			SlotID:           val.SlotID,
			PAID:             val.PAID,
			PacsID:           val.PacsId,
			BlockReasonId:    req.SlotsClosureReasonId,
			BlockRelatedNote: req.BlockRelatedNote,
			BlockedBy:        req.BlockedBy,
		}
		blockedInventory = append(blockedInventory, blockedInventoryRow)

		pacsItem := structs.FsPaSlotDate{
			Date:                   req.Date,
			FSID:                   val.FSID,
			PAID:                   val.PAID,
			SlotID:                 val.SlotID,
			ProductArenaCategoryId: val.ProductArenaCategoryId,
			PacsId:                 val.PacsId,
			CancelBy:               req.BlockedBy,
		}
		paCapacityDetails = append(paCapacityDetails, pacsItem)
	}

	// cancel bookings based on paCapacityDetails []struct
	log.Println("cancel bookings based on block inventory", paCapacityDetails)
	var cancelledUids []int32
	if err := s.Data.CancelBookingsForFsIdSlotIdAndDate(paCapacityDetails, &cancelledUids); err != nil {
		log.Printf("Error in cancelling bookings while blocking inventories, Error: %v", err)
		return err
	}

	if err := s.Data.InsertBlockedSlotsAndUpdateActiveSlots(&blockedInventory, 1); err != nil {
		log.Printf("Could not insert blocked slots, Error: %v", err)
		return err
	}

	if req.SlotsClosureReasonId > 0 && len(fsIds) > 0 {
		var wg sync.WaitGroup
		wg.Add(1)
		go s.MarkInactiveDayForFullDayClosure(ctx, req, fsIds, blockDateTimeObj, req.BlockedBy, &wg)
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Inventory blocked successfully!",
	}
	res.Status = status

	// get fixed users for the slot
	if req.FixedUserFlag {
		var fUsers []int32
		if err := s.Data.GetRelatedFixedUsers(&paCapacityDetails, &fUsers); err != nil {
			log.Printf("Could not fetch fixed users, error: %v", err)
			return err
		}
		for _, ele := range fUsers {
			cancelledUids = append(cancelledUids, ele)
		}
	}

	cancelledUids = DeduplicateSlice(cancelledUids)

	//send broadcast to cancelled uids and fixed uids
	broadData := &userPB.Broadcast{
		SenderUserId:      req.BlockedBy,
		ReceivingUserIds:  cancelledUids,
		EmailSubject:      req.Title,
		EmailBody:         req.EmailBody,
		SmsBody:           req.SmsBody,
		NotificationTitle: req.Title,
		NotificationBody:  req.NotificationBody,
		EmailFlag:         req.EmailFlag,
		SmsFlag:           req.SmsFlag,
		NotificationFlag:  req.NotificationFlag,
	}

	if len(broadData.ReceivingUserIds) > 0 {
		go s.SendBroadcastToCanceledAndFixedUsers(broadData)
	}

	if req.PartnerFacility == true && len(blockedInventory) > 0 {
		go s.SendPartnerFacilityBlockingEmailToFitsoTeam(blockedInventory)
	}

	if len(paCapacityDetails) > 0 {
		go s.publishSlotClosureEventToSQS(ctx, paCapacityDetails)
	}

	return nil
}

func (s *Service) MarkInactiveDayForFullDayClosure(ctx context.Context, req *pb.BlockInventoryRequest, fsIds []int32, date time.Time, blockedBy int32, wg *sync.WaitGroup) error {

	defer wg.Done()

	defer func() {
		if r := recover(); r != nil {
			log.Println("Recovered from MarkInactiveDayForFullDayClosure panic- ", r)
		}
	}()

	var sportsInactiveDays []model.SportsInactiveDay
	for _, fsId := range fsIds {
		fullDayClosure, err := s.CheckIfFullDayClosure(ctx, fsId, date)
		if err != nil {
			log.Printf("func:MarkInactiveDayForFullDayClosure, error in checking fullDayClosure, error: %v", err)
			return err
		}

		if fullDayClosure {
			log.Printf("IT'S A FULL DAY CLOSURE for fs_id:%d on date: %v", fsId, date)
			record := model.SportsInactiveDay{
				Title:     req.BlockRelatedNote,
				FsId:      fsId,
				Date:      date,
				ReasonId:  req.SlotsClosureReasonId,
				CreatedBy: blockedBy,
				UpdatedBy: blockedBy,
			}
			sportsInactiveDays = append(sportsInactiveDays, record)
		}
	}

	if len(sportsInactiveDays) > 0 {
		if err := s.Data.MarkInactiveDayForFsIds(ctx, sportsInactiveDays); err != nil {
			log.Printf("func:MarkInactiveDayForFullDayClosure, could not mark inactive days, Error: %v", err)
			return err
		}
	}

	return nil
}

func (s *Service) CheckIfFullDayClosure(ctx context.Context, fsId int32, date time.Time) (fulldayClosure bool, err error) {

	// handling for TODAY
	today := time.Now()
	ty, tm, td := today.Date()
	by, bm, bd := date.Date()

	var a []int32
	if ty == by && tm == bm && td == bd {
		var paData []model.PAActiveSlot
		if err := s.Data.GetTodayUpcomingPAActiveSlotsForFsId(ctx, fsId, &paData); err != nil {
			log.Println("func:CheckIfFullDayClosureOrNot, error in fetching blocked inventories, err:%v", err)
			return false, err
		}

		if len(paData) == 0 {
			return true, nil
		} else {
			return false, nil
		}
	}

	// play arena capacities
	pacsCond := model.PlayArenaCapacitySlot{
		FSID:                  fsId,
		DayOfWeek:             getSystemDayOfWeekFromDate(date),
		ActiveStatus:          1,
		IncludeAcademySession: true,
	}
	pacsData := s.GetArenaCapacitySlotsForDate(pacsCond)

	for _, val := range pacsData {
		a = append(a, val.PACSID)
	}

	sort.SliceStable(a, func(i, j int) bool {
		return a[i] < a[j]
	})

	blockedInventoryCond := model.SportsBlockedInventory{
		FSID:        fsId,
		Date:        date,
		DeletedFlag: 0,
	}

	var blockedData []model.SportsBlockedInventory
	if err := s.Data.GetBlockedInventoriesByDate(ctx, blockedInventoryCond, &blockedData); err != nil {
		log.Println("func:CheckIfFullDayClosureOrNot, error in fetching blocked inventories, err:%v", err)
		return false, err
	}

	var b []int32
	for _, val := range blockedData {
		b = append(b, val.PacsID)
	}

	sort.SliceStable(b, func(i, j int) bool {
		return b[i] < b[j]
	})

	return IntArrayEquals(a, b), nil
}

func (s *Service) SendBroadcastToCanceledAndFixedUsers(broadData *userPB.Broadcast) error {

	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	ucl = userPB.NewUserService(serviceList.User, service.Client())

	response, err := ucl.SendBroadcast(context.TODO(), broadData)
	if err != nil {
		fmt.Println("Error in sending broadcast...", err)
		return err
	}
	fmt.Println("broad after cancel resposne -------------- ", response)
	return nil
}

func (s *Service) SendPartnerFacilityBlockingEmailToFitsoTeam(blockedInventoryData []model.SportsBlockedInventory) error {
	fmt.Println("initiating sending mail for partner-facility blocking", blockedInventoryData)

	var userData model.UserProduct
	if blockedInventoryData[0].BlockedBy > 0 {
		if err := s.Data.GetUserDetails(&userData, blockedInventoryData[0].BlockedBy); err != nil {
			fmt.Println("Could not get partner-facility admin details..", err)
			return err
		}
	}
	firstName := strings.Split(userData.Name, " ")[0]

	bookingDateLocal, err := GetLocalDateTime(blockedInventoryData[0].Date)
	if err != nil {
		fmt.Println("time converison error -- ", err)
		return err
	}

	var subject string
	subject = "Blocking Inventory by Partner Facility Admin"

	var message string
	message = "Dear Fitso Member, <br><br>"
	message += "<b>" + firstName + "</b>" + " just blocked the following slots in his facility:- <br>"
	message += "<b>Reason:</b>" + blockedInventoryData[0].BlockRelatedNote + "<br>"
	message += "<b>When:</b>" + bookingDateLocal.Format(formatYMD) + "<br><br>"

	fs_slots := make(map[int32][]pb.Slot)

	for _, element := range blockedInventoryData {
		fmt.Println("element ------------", element)

		var SlotData []model.Slot
		s.Data.GetSlotDetails(element.SlotID, &SlotData)

		var Timing string
		for _, element := range SlotData {
			Timing = element.Timing
			break
		}

		slotVal := pb.Slot{
			Timing: Timing,
		}

		fs_slots[element.FSID] = append(fs_slots[element.FSID], slotVal)
	}

	index := 1
	for fs_id, slot_data := range fs_slots {

		var fsName structs.FacilitySportName
		s.Data.GetFacilitySportNameByFsId(&fs_id, &fsName)
		message += "<b>" + strconv.Itoa(index) + ".) " + fsName.Description + "</b><br>"

		slots_detail := make(map[string]int)

		for _, slot := range slot_data {
			if _, ok := slots_detail[slot.Timing]; ok == false {
				slots_detail[slot.Timing] = 1
			} else {
				slots_detail[slot.Timing] = slots_detail[slot.Timing] + 1
			}
		}

		for timing, count := range slots_detail {
			message += "<b>*</b> " + timing + "  <b>(No of blocked arenas: " + strconv.Itoa(count) + ")</b><br>"
		}
		index++
		message += "<br>"
	}
	message += "<br>"
	message += "Please check the dashboard for further details :- https://www.getfitso.com/react-dashboard/sports-experts/list-blocked-inventory <br><br>"
	message += "Regards <br> Fitso Bot"

	fmt.Println(message)

	toEmails := [1]string{"<EMAIL>"}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	fromEm := &structs.Email{
		Name:         "Fitso Contact",
		EmailAddress: "<EMAIL>",
	}

	bccEmails := [1]string{TECH_MAIL}

	var bccEm []*structs.Email

	for _, emailId := range bccEmails {
		bccEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		bccEm = append(bccEm, bccEmVal)
	}

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		BCC:     bccEm,
		Subject: subject,
		Message: message,
	}
	if err := s.Data.SendPartnerFacilityBlockingEmail(emailReq); err != nil {
		fmt.Println("Error in sending partner-facility blocked inventory email")
		return err
	}

	return nil
}

func (s *Service) FeedbackListingGet(ctx context.Context, req *pb.FeedbackListingRequest, res *pb.FeedbackListingResponse) error {
	fmt.Println("req -- ", req)
	//fetch all feedbacks using start_date and end_date --> mandatory fields, Then apply scopes to dynamically fetch data for conditions
	if (req.StartDate == 0 && req.EndDate == 0) && (req.BookingStartDate == 0 && req.BookingEndDate == 0) {
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid Dates passed!",
		}
		res.Status = status
		return nil
	}
	//fetch all sports feedback responses
	var feedbackData []model.SportsFeedbackResponse
	if err := s.Data.GetSportsFeedbackForFilters(req, &feedbackData); err != nil {
		fmt.Println("Error in getting feedback data for filters!")
		return err
	}

	//if req.sport ids contain swimming(3); then check here for feedback
	var fIdsInt []int32
	if len(req.SIds) > 0 {
		fIds := strings.Split(req.SIds, ",") //string to array
		for _, ele := range fIds {
			val, _ := strconv.Atoi(ele)
			fIdsInt = append(fIdsInt, int32(val))
		}
	}

	if Contains(fIdsInt, int32(3)) || len(req.SIds) <= 0 {
		//fetch all seals feedback responses
		var sealsFeedbackData []model.SportsFeedbackResponse
		if err := s.Data.GetSealsFeedbackForFilters(ctx, req, &sealsFeedbackData); err != nil {
			fmt.Println("Error in getting seals feedback data for filters!")
			return err
		}

		//merge both feedback results
		for _, sFeed := range sealsFeedbackData {
			feedbackData = append(feedbackData, sFeed)
		}
	}

	//Sorting the feedbackdata on the basis of Booking Time in Descending Order

	sort.Slice(feedbackData, func(i, j int) bool {
		return feedbackData[j].BookingTime.Before(feedbackData[i].BookingTime)
	})

	if len(feedbackData) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "Feedback fetched successfully!",
		}
		res.Status = status

		respOptMap := make(map[int32][]*pb.SportsFeedbackResponseOption) //key -> feedback_response_id, val -> options array
		respMap := make(map[int32]model.SportsFeedbackResponse)          //key -> feedback_response_id, val -> responses array
		for _, element := range feedbackData {
			optionsData := model.SportsFeedbackResponseOption{
				FeedbackResponseOptionId: element.FeedbackResponseOptionId,
				FeedbackResponseId:       element.FeedbackResponseId,
				FeedbackOptionId:         element.FeedbackOptionId,
				Title:                    element.Title,
				FeedbackCategoryId:       element.FeedbackCategoryId,
			}

			respOptMap[element.FeedbackResponseId] = append(respOptMap[element.FeedbackResponseId], optionsData.Proto())
			respMap[element.FeedbackResponseId] = element
		}

		for _, element := range respMap {
			element.OptionsSelected = respOptMap[element.FeedbackResponseId]

			BookingTimeVal, _ := ptypes.TimestampProto(element.BookingTime)
			bookingData := &pb.Booking{
				BookingReferenceNumber: element.BookingReferenceNumber,
				BookingTime:            BookingTimeVal,
			}
			element.BookingData = bookingData

			userData := &pb.User{
				UserId: element.UserId,
				Name:   element.Name,
				Phone:  element.Phone,
			}
			element.User = userData

			slotData := &pb.Slot{
				SlotId: element.SlotId,
				Timing: element.Timing,
			}
			element.Slot = slotData

			facilityData := &pb.Facility{
				DisplayName: element.DisplayName,
				FacilityId:  element.FacilityId,
			}
			element.Facility = facilityData

			sportData := &pb.Sport{
				SportName: element.SportName,
				SportId:   element.SportId,
			}
			element.Sport = sportData

			res.FeedbackData = append(res.FeedbackData, element.Proto())
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "No feedbacks available!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) GetBlockedInventory(ctx context.Context, req *pb.GetBlockedInventoryRequest, res *pb.GetBlockedInventoryResponse) error {

	var inventoryData []model.SportsBlockedInventory
	if err := s.Data.GetBlockInventoryForFilters(req, &inventoryData); err != nil {
		log.Println("Error in getting block inventory data for filters!", err)
		return err
	}

	if len(inventoryData) > 0 {
		status := &pb.Status{
			Status:  "success",
			Message: "block inventory data fetched successfully!",
		}
		res.Status = status

		for _, element := range inventoryData {
			res.BlockedSlotsData = append(res.BlockedSlotsData, element.Proto())
		}

	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "No block inventory available!",
		}
		res.Status = status
	}

	return nil

}

func (s *Service) GetSlotPopulationStatus(ctx context.Context, req *pb.Empty, res *pb.SlotPopulationStatus) error {

	var populatableFsIds []int32
	if err := s.Data.GetDistinctPopulatableActiveFsids(ctx, &populatableFsIds); err != nil {
		log.Println("Could not get distinct fsids")
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get distinct fsids",
		}
		res.Status = status
		return err
	}

	var slotPopulationInProgress bool = false
	var redisKey string

	for _, givenFsId := range populatableFsIds {
		redisKey = strconv.Itoa(int(givenFsId))

		var output string
		if err := s.Data.GetFromRedis(redisKey, &output); err != nil {
			log.Println("Error in getting redis output", err)
		}

		if len(output) == 0 || output == "0" {
			continue
		} else {
			slotPopulationInProgress = true

		}
	}

	if slotPopulationInProgress {
		status := &pb.Status{
			Status:  "success",
			Message: "slot population is in progress",
		}
		res.Status = status
		res.SlotPopulationStatus = true
	} else {
		status := &pb.Status{
			Status:  "success",
			Message: "slots already populated",
		}
		res.Status = status
		res.SlotPopulationStatus = false
	}

	return nil
}

func (s *Service) PopulateActiveSlots(ctx context.Context, req *pb.PopulateSlotRequest, res *pb.Ack) error {

	var populatableFsIds []int32

	errFs := s.Data.GetDistinctPopulatableActiveFsids(ctx, &populatableFsIds)

	if errFs != nil {
		fmt.Println("Could not get distinct fsids")
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get distinct fsids",
		}
		res.Status = status
		return errFs
	}

	for _, givenFsId := range populatableFsIds {
		// go s.ProcessSlotPopulationForFsId(ctx, givenFsId)
		if _, ok := DISABLED_FS_SLOT_POPULATION[givenFsId]; ok {
			continue
		}

		fsInfo := &pb.FacilitySportDetails{
			FsId:       givenFsId,
			CronDriven: req.CronDriven,
		}

		log.Printf("PopulateActiveSlots: publishing fs data for slot population with fsInfo: %v", fsInfo)
		go s.Data.PublishFsIdForSlotPopulation(ctx, fsInfo)
	}
	//sleeping thread for 15 seconds and checking if slot_population processed, if not trigger a alert mail
	time.Sleep(15 * time.Second)
	if err := s.checkSlotPopulationAndAlertOnFailure(ctx, populatableFsIds); err != nil {
		log.Printf("PopulateActiveSlots: error in checking slot population: %v", err)
		return err
	}

	return nil
}

func (s *Service) checkSlotPopulationAndAlertOnFailure(ctx context.Context, fsIds []int32) error {
	var paActiveSlotsData []model.PAActiveSlot
	if err := s.Data.CheckMasterkeyGuidedSlotsPopulatedForFsIds(ctx, fsIds, &paActiveSlotsData); err != nil {
		log.Printf("checkSlotPopulationAndAlertOnFailure: error in checking slot population: %v", err)
		return err
	}
	isSlotPopulatedForTomorrow := false
	isSlotPopulatedForDayAfterTomorrow := false
	tomorrowDate := time.Now().AddDate(0, 0, 1).Format(formatYMD)
	dayAfterTomorrowDate := time.Now().AddDate(0, 0, 2).Format(formatYMD)
	for _, slotData := range paActiveSlotsData {
		slotDate := slotData.Date.Format(formatYMD)
		if slotDate == tomorrowDate {
			isSlotPopulatedForTomorrow = true
		} else if slotDate == dayAfterTomorrowDate {
			isSlotPopulatedForDayAfterTomorrow = true
		}
	}
	if !isSlotPopulatedForTomorrow || !isSlotPopulatedForDayAfterTomorrow {
		if err := s.sendMailForSlotPopulationFailure(ctx, isSlotPopulatedForTomorrow, isSlotPopulatedForDayAfterTomorrow); err != nil {
			log.Println("checkSlotPopulationAndAlertOnFailure: error in sending mail for slot population failure: %v", err)
			return err
		}
	}
	return nil
}

func (s *Service) ProcessSlotPopulationForFsId(ctx context.Context, fsId int32, cronDriven int32) error {
	log.Printf("ProcessSlotPopulationForFsId : called for fsId : %d, cronDriven: %d", fsId, cronDriven)
	var prebookingWindowSize int32 = 0
	if err := s.GetPreBookingWindow(fsId, &prebookingWindowSize); err != nil {
		return err
	}

	currDate := time.Now()
	isOneTimeCheckDate := currDate.Year() == 2023 && currDate.Month() == 06 && currDate.Day() == 26

	if (prebookingWindowSize > 0 && cronDriven == 1) || isOneTimeCheckDate {
		prebookingWindowSize += 1
	}

	//get facility_id for fsId
	var facIdStruct structs.FacilitySportIds
	if err := s.Data.GetFacilityIdAndSportIdByFsId(&fsId, &facIdStruct); err != nil {
		fmt.Println("Error in getting facility_id and sport id from facility_id: ", err)
		return err
	}

	non_operational_dow := GetIntegerArrayFromCommaSeparatedString(facIdStruct.NonOperationalDays)
	currentTs := time.Now().Unix()
	if len(non_operational_dow) > 0 {
		var reasonId int32 = 1
		if len(non_operational_dow) > 1 {
			// for gx sports
			reasonId = 5
		}
		for dateIterator := 0; dateIterator < int(prebookingWindowSize); dateIterator++ {
			additionFactor := dateIterator * 86400
			iteratorTs := currentTs + int64(additionFactor)
			iteratorTime := time.Unix(iteratorTs, 0)
			locationTime, _ := GetLocalDateFromDateTime(iteratorTime)
			dayOfWeek := getSystemDayOfWeekFromDate(locationTime)

			for _, ele := range non_operational_dow {
				if ele == 0 {
					ele = 7
				}
				if ele == dayOfWeek {
					if err := s.Data.MarkInActiveDayForNonOperational(ctx, fsId, locationTime, reasonId); err != nil {
						log.Println("func:ProcessSlotPopulationForFsId, error in marking inactive day for fs_id:%d, date:%s, err:- %v", fsId, locationTime, err)
						return err
					}
				}
			}
		}
	}

	inactiveDaysCondition := structs.InactiveDaysCondition{
		FsId:       fsId,
		FacilityId: facIdStruct.FacilityID,
	}
	//get upcoming inactive days
	var inactiveDays []model.SportsInactiveDay
	if err := s.Data.GetUpcomingInactiveDays(&inactiveDays, &inactiveDaysCondition); err != nil {
		fmt.Println("Error in getting upcoming inactive days: ", err)
		return err
	}

	mapInactiveDate := make(map[string]model.SportsInactiveDay)
	for _, ele := range inactiveDays {
		mapInactiveDate[ele.Date.Format(formatYMD)] = ele
	}

	//start date - today; end date - today + prebooking window days

	for dateIterator := 0; dateIterator < int(prebookingWindowSize); dateIterator++ {

		additionFactor := dateIterator * 86400

		iteratorTs := currentTs + int64(additionFactor)
		iteratorTime := time.Unix(iteratorTs, 0)

		locationTime, _ := GetLocalDateFromDateTime(iteratorTime)
		//LocationTimeFormatted := locationTime.Format(formatYMD)

		if val, ok := mapInactiveDate[locationTime.Format(formatYMD)]; ok {
			if val.FacilityId == 0 || val.FacilityId == facIdStruct.FacilityID {
				log.Println("Cannot Populate ------> Facility inactive on " + locationTime.Format(formatYMD))
				continue
			}
		}

		var populatablePaIds []int32
		errPa := s.Data.GetDistinctPopulatableActivePaidsForFsid(fsId, &populatablePaIds)

		if errPa != nil {
			log.Println("Could not get distinct paids", errPa)
			return errPa
		}

		for _, givenPaId := range populatablePaIds {

			s.ProcessSlotPopulationForPaIdForDate(ctx, givenPaId, locationTime)
		}

		conditionFs := model.FacilitySportCapacitySlot{
			FSID:      fsId,
			DayOfWeek: getSystemDayOfWeekFromDate(locationTime),
		}
		capacitySlots := s.GetCapacitySlotsForDate(conditionFs)

		activeSlots := s.generateActiveSlotsUsingCapacityslots(capacitySlots, locationTime)
		for _, a_slot := range activeSlots {
			s.Data.ActiveSlotsCreate(a_slot)
		}
	}

	if err := s.Data.SetInRedis(strconv.Itoa(int(fsId)), "0", 3*time.Minute); err != nil {
		log.Println("error in updating cache value of redis key")
	}

	return nil

}

func (s *Service) ProcessSlotPopulationForPaIdForDate(ctx context.Context, paId int32, date time.Time) error {

	// Play Arena slot population logic
	conditionPa := model.PlayArenaCapacitySlot{
		PAID:         paId,
		DayOfWeek:    getSystemDayOfWeekFromDate(date),
		ActiveStatus: 1,
	}

	capacitySlotsPa := s.GetArenaCapacitySlotsForDate(conditionPa)
	activeSlotsPa := s.generateArenaActiveSlotsUsingCapacityslots(capacitySlotsPa, date)

	for _, a_slot := range activeSlotsPa {
		s.Data.ArenaActiveSlotsCreate(a_slot)
	}

	return nil
}

func (s *Service) MakeABookingReminderNotification(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	fmt.Println("Booking reminder notification for 1) trial for every booking days, 2) premium plan for 2 no booking days")
	//get trial users with no booking for today
	var notificationMessageArray []structs.FcmNotificationMessage
	var trialUsersData []structs.UserSubscription
	if err := s.Data.GetTrialUsersWithNoBookingsToday(&trialUsersData); err != nil {
		fmt.Println("Error in getting trial users details.. Db error..", err)
		return err
	}
	fmt.Println("TUD-----", trialUsersData)

	var nDetails structs.TitleTextBooknigReminder
	GetRandomBookingReminderNotificationContent(&nDetails)

	fmt.Println("nData val --- ", nDetails)
	curTime := time.Now()
	//if hour of day == 9; send notificaiton
	//else if hour of day == 18; send sms
	if curTime.Hour() == 18 { //send sms
		fmt.Println("Here sending sms.......")
		var phoneArr []string
		for _, ele := range trialUsersData {
			phoneArr = append(phoneArr, ele.Phone)
		}
		smsText := nDetails.Text

		go s.SendBroadcastSmsAsync(phoneArr, smsText)
	} else {
		//notification exp time
		defExpTime := config.Get("static", "default_notification_expiry_time_in_hours").Int(4)
		expTime := curTime.AddDate(0, 0, defExpTime)
		expTimestamp := expTime.Unix()

		for _, element := range trialUsersData {
			notificationMessage := structs.FcmNotificationMessage{
				UserId:     element.UserId,
				Text:       nDetails.Text,
				Action:     "booking",
				ActionId:   4,
				Title:      nDetails.Title,
				Id:         element.SubscriptionId,
				ExpiryDate: expTimestamp,
			}
			notificationMessageArray = append(notificationMessageArray, notificationMessage)
		}
		go s.sendNotificationForBookingsUsers(notificationMessageArray) //&wg, errGo
	}

	//get booking users with no booking for last 2 days
	// var premiumUsersData []structs.UserSubscription
	// if err := s.Data.GetPremiumUsersWithNoBookingsInLastTwoDays(&premiumUsersData); err != nil {
	// 	fmt.Println("Error in getting premium users details.. Db error..", err)
	// 	return err
	// }
	// fmt.Println("PUD-----", premiumUsersData)

	// for _, element := range premiumUsersData {
	// 	notificationMessage := structs.FcmNotificationMessage{
	// 		UserId:   element.UserId,
	// 		Text:     "You have a " + element.Sport.SportName + " session scheduled for " + element.Slot.Timing + " at " + element.Facility.DisplayName,
	// 		Action:   "booking",
	// 		ActionId: 4,
	// 		Title:    "Couch surfing?? Try Court surfing instead!",
	// 		Id:       element.SubscriptionId,
	// 	}
	// 	notificationMessageArray = append(notificationMessageArray, notificationMessage)
	// }

	return nil
}

func (s *Service) SendAlertForLessThan50Attendance(ctx context.Context, req *pb.Empty, res *pb.Ack) error {

	var facilitySportSlotData []structs.FacilitySportSlotData
	var slotIds []int32
	flag := 0
	s.DetermineSlotIdByCurrentTime(ctx, &slotIds, time.Now())
	if len(slotIds) > 0 {
		if err := s.Data.SlotBookingAttendanceCount(&facilitySportSlotData, slotIds); err != nil {
			fmt.Println("Error in getting Facility sport Slot details details.. Db error..", err)
			return err
		}
		var facilitySportSlot []structs.FacilitySportSlotData
		for _, element := range facilitySportSlotData {
			if (element.BookingCount+1)/2 > element.AttendanceCount && element.BookingCount > 5 {
				facilitySportSlot = append(facilitySportSlot, element)
			}
		}

		if len(facilitySportSlot) != 0 {
			var subject string
			subject = "Low Attendance at "
			message := "Dear Fitso Group,<br><br>"
			message += "Facility Slots which have Attendance less than 50 percent of total Bookings are as follows :- <br>"
			message += "<br><b>Facility Details </b><br>"
			for _, element := range facilitySportSlot {
				subject += element.FacilityName + ", "
				message += "<b>Facility Name : </b>" + element.FacilityName + "<br>"
				message += "<b>Sport Name : </b>" + element.SportName + "<br>"
				message += "<b>Slot : </b>" + element.Timing + "<br>"
				message += "<b>Booking Count : </b>" + strconv.Itoa(int(element.BookingCount)) + "<br>"
				message += "<b>Attendance Count : </b>" + strconv.Itoa(int(element.AttendanceCount)) + "<br>"
				message += "<br><br>"
				if element.FacilityType == 3 {
					flag = 1
				}
			}
			subject += " during " + facilitySportSlot[0].Timing
			message += "<br>Regards,<br>"
			message += "Fitso Bot<br>"
			fromEm := &structs.Email{
				Name:         "Fitso Bot",
				EmailAddress: "<EMAIL>",
			}
			var names []string
			var mailIds []string
			if flag == 1 {
				names = []string{"Tech-Fitso", "Vardhan Singh", "Akshit Chaudhary", "Fitso-CX", "Rahul Gaur", "Rohit Rathi", "Coaching-ops", "Rishabh"}
				mailIds = []string{TECH_MAIL, "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
			} else {
				names = []string{"Tech-Fitso", "Vardhan Singh", "Akshit Chaudhary", "Fitso-CX", "Coaching-ops", "Rishabh"}
				mailIds = []string{TECH_MAIL, "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
			}
			var toEmails []*structs.Email
			for i, id := range mailIds {
				toEmail := &structs.Email{
					Name:         names[i],
					EmailAddress: id,
				}
				toEmails = append(toEmails, toEmail)
			}
			emailReq := &structs.EmailRequest{
				From:    fromEm,
				To:      toEmails,
				Subject: subject,
				Message: message,
			}
			if err := s.Data.SendEmail(emailReq); err != nil {
				fmt.Println("Error in sending email")
				return err
			}
		}
	}
	return nil
}

func (s *Service) NoShowApplyForBookingId(bookingId int32, isPenaltyApplied bool) error {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	pcl = productPB.NewProductService(serviceList.Product, service.Client())

	noShowApplyRequest := &productPB.NoShowApplyRequest{
		BookingId:        bookingId,
		IsPenaltyApplied: isPenaltyApplied,
	}

	// session affinity based call
	_, err := pcl.NoShowApplyWithBookingId(context.TODO(), noShowApplyRequest)

	if err != nil {
		log.Println(err)
	}
	return nil
}

func (s *Service) SendBroadcastSmsAsync(phoneNumbers []string, smsText string) error {

	fmt.Println("-------------------------", phoneNumbers)
	if errB := s.Data.MessagePublish(phoneNumbers, smsText); errB != nil {
		fmt.Println("send message error")
		return errB
	}

	return nil
}

func (s *Service) BookingReminderNotificationForInactiveSubscribedUser(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	fmt.Println("Subscribed user not making booking since 7 days ")
	//get trial users with no booking for today
	var days int32 = 7

	var notificationMessageArray []structs.FcmNotificationMessage
	var usersData []structs.UserSubscription

	if err := s.Data.GetSubscribedUserWithNoBookingInLastNdays(&usersData, days); err != nil {
		fmt.Println("Error in getting subsribed users details.. Db error..", err)
		return err
	}
	fmt.Println("user data-----", usersData)

	var nDetails structs.TitleTextBooknigReminder
	GetRandomBookingReminderNotificationForInactiveUser(&nDetails)

	fmt.Println("nData val --- ", nDetails)
	//notification exp time

	expTimeInHours := 12
	expTimestamp := time.Now().Local().Add(time.Hour * time.Duration(expTimeInHours)).Unix()

	for _, element := range usersData {
		notificationMessage := structs.FcmNotificationMessage{
			UserId:     element.UserId,
			Text:       nDetails.Text,
			Action:     "booking",
			ActionId:   4,
			Title:      nDetails.Title,
			Id:         element.SubscriptionId,
			ExpiryDate: expTimestamp,
		}
		notificationMessageArray = append(notificationMessageArray, notificationMessage)
	}
	go s.sendNotificationForBookingsUsers(notificationMessageArray) //&wg, errGo

	return nil
}

func (s *Service) BookingSlotsDetailsGetV2(ctx context.Context, req *pb.BookingSlotsDetailsRequest, res *pb.BookingSlotsDetailsResponseV2) error {

	if len(req.BookingReferenceNumber) > 0 {
		var wgMain sync.WaitGroup
		slotInformation := make(chan model.FacilitySportDetails)
		userQueryRaised := make(chan model.UserBookingRequestQueries)
		reasonsForNoshow := make(chan []model.UserBookingRequestQueries)
		ratingsForFeedback := make(chan []*pb.SportsFeedbackRating)
		feedback := make(chan []model.SportsFeedbackUserRating)

		wgMain.Add(5)
		go s.GetBookingIdInfo(ctx, &slotInformation, req.BookingId, req.BookingReferenceNumber, &wgMain)

		go s.UserNoShowRevertQueryInfo(ctx, &userQueryRaised, req.BookingId, &wgMain)

		go s.GetReasonsForUserQueries(ctx, &reasonsForNoshow, 1, &wgMain)

		go s.GetFeedbackOptions(ctx, &ratingsForFeedback, false, &wgMain)

		go s.GetUserFeedbackInformation(ctx, &feedback, req.BookingReferenceNumber, &wgMain)
		slotInfo := <-slotInformation
		userQuery := <-userQueryRaised
		reasons := <-reasonsForNoshow
		ratingFeedback := <-ratingsForFeedback
		userFeedbackData := <-feedback

		wgMain.Wait()

		feedbackApplicable := true
		if len(userFeedbackData) > 0 {
			feedbackApplicable = false
		}
		var checkNoShow model.FacilitySportDetails
		var titles []string
		var ratingId int32
		for _, ele := range userFeedbackData {
			ratingId = ele.RatingId
			if len(ele.Title) > 0 {
				titles = append(titles, ele.Title)
			}
		}
		userFeedbackDetails := &pb.SportsFeedbackUserRating{
			RatingId: ratingId,
			Titles:   titles,
		}

		CheckinTime, _ := ptypes.TimestampProto(slotInfo.CheckinTime)
		BookingTime, _ := ptypes.TimestampProto(slotInfo.BookingTime)
		bookingInfo := &pb.FacilitySportDetails{
			DisplayName:            slotInfo.DisplayName,
			DisplayAddress:         slotInfo.DisplayAddress,
			SportName:              slotInfo.SportName,
			Icon:                   slotInfo.Icon,
			Timing:                 slotInfo.Timing,
			IconSportsApp:          slotInfo.IconSportsApp,
			BookingReferenceNumber: slotInfo.BookingReferenceNumber,
			AttendanceFlag:         slotInfo.AttendanceFlag,
			NoShowApplicable:       slotInfo.NoShowApplicable,
			NoShowPeanltyId:        slotInfo.NoShowPeanltyId,
			CheckinTime:            CheckinTime,
			BookingTime:            BookingTime,
			IsReverted:             slotInfo.IsReverted,
		}
		attendanceInfo := &pb.UserBookingRequestQueries{
			BookingReferenceNumber: userQuery.BookingReferenceNumber,
			ReasonId:               userQuery.ReasonId,
			QueryTypeId:            userQuery.QueryTypeId,
			Status:                 userQuery.Status,
			OtherReason:            userQuery.OtherReason,
			Description:            userQuery.Description,
		}
		var reasonForNoshow []*pb.UserBookingRequestQueries
		for _, ele := range reasons {
			if slotInfo.AttendanceFlag == 1 && slotInfo.NoShowPeanltyId > 0 && slotInfo.NoShowPeanltyId != checkNoShow.NoShowPeanltyId {
				if ele.ReasonId == 6 || ele.ReasonId == 7 || ele.ReasonId == 13 || ele.ReasonId == 9 {
					reason := &pb.UserBookingRequestQueries{
						ReasonId:    ele.ReasonId,
						Description: ele.Description,
					}
					reasonForNoshow = append(reasonForNoshow, reason)
				}
			} else if ele.ReasonId != 6 && ele.ReasonId != 7 {
				reason := &pb.UserBookingRequestQueries{
					ReasonId:    ele.ReasonId,
					Description: ele.Description,
				}
				reasonForNoshow = append(reasonForNoshow, reason)
			}
		}
		var ratings []*pb.SportsFeedbackRating
		for _, ele := range ratingFeedback {
			ratingFeedbacks := &pb.SportsFeedbackRating{
				RatingId:           ele.RatingId,
				FeedbackCategoryId: ele.FeedbackCategoryId,
				Question:           ele.Question,
				CategoryOptions:    ele.CategoryOptions,
			}
			ratings = append(ratings, ratingFeedbacks)
		}

		if slotInfo.AttendanceFlag == 1 && slotInfo.NoShowPeanltyId == checkNoShow.NoShowPeanltyId { // feedback write
			if bookingInfo.NoShowApplicable == 1 {
				data := &pb.UserBookingRequestQueries{
					BookingInfo: bookingInfo,
				}
				res.Data = data
			} else {
				data := &pb.UserBookingRequestQueries{
					BookingInfo:        bookingInfo,
					Ratings:            ratings,
					FeedbackApplicable: feedbackApplicable,
					UserRating:         userFeedbackDetails,
				}
				res.Data = data
			}

		} else if slotInfo.AttendanceFlag == 1 && slotInfo.NoShowPeanltyId > 0 { //late entry

			if len(userQuery.BookingReferenceNumber) > 0 && userQuery.Status == 0 { // pending
				attendanceInfo.Penalty = "01 Day deducted"
				attendanceInfo.PenaltyHeading = "Late entry penalty has been applied"
				attendanceInfo.Information = "Attendance can only be marked during first 10 mins of the slots"
				data := &pb.UserBookingRequestQueries{
					BookingInfo:        bookingInfo,
					AttendanceInfo:     attendanceInfo,
					Ratings:            ratings,
					FeedbackApplicable: feedbackApplicable,
					UserRating:         userFeedbackDetails,
				}
				res.Data = data

			} else if len(userQuery.BookingReferenceNumber) > 0 && userQuery.Status == 1 { // resolved
				var reverted int32
				if err := s.Data.QueryRevertStatus(ctx, &reverted, slotInfo.BookingId); err != nil {
					log.Println("Error in getting resolved result.. Db error..", err)
					return err
				}
				if reverted == 1 { //feedback
					attendanceInfo.PenaltyHeading = "Your 1 day penalty has been reversed."
					attendanceInfo.Information = "Please mark your attendance within 15 mins or cancel your booking if not attending not later than 30 mins of your session start time in future to avoid a penalty."
					data := &pb.UserBookingRequestQueries{
						BookingInfo:        bookingInfo,
						AttendanceInfo:     attendanceInfo,
						Ratings:            ratings,
						FeedbackApplicable: feedbackApplicable,
						UserRating:         userFeedbackDetails,
					}
					res.Data = data

				} else { // Not reversed
					attendanceInfo.PenaltyHeading = "Late entry penalty has been applied"
					attendanceInfo.Penalty = "01 Day deducted"
					attendanceInfo.Information = "Reversal cannot be made for the reason"
					data := &pb.UserBookingRequestQueries{
						BookingInfo:        bookingInfo,
						AttendanceInfo:     attendanceInfo,
						Ratings:            ratings,
						FeedbackApplicable: feedbackApplicable,
						UserRating:         userFeedbackDetails,
					}
					res.Data = data
				}
			} else if bookingInfo.IsReverted == 1 || bookingInfo.BookingTime.Seconds < 1609506060 { // Past bookings is reverted
				data := &pb.UserBookingRequestQueries{
					BookingInfo:        bookingInfo,
					Ratings:            ratings,
					FeedbackApplicable: feedbackApplicable,
					UserRating:         userFeedbackDetails,
				}
				res.Data = data
			} else { //no raised
				attendanceInfo.PenaltyHeading = "Late entry penalty has been applied"
				attendanceInfo.Penalty = "01 Day deducted"
				attendanceInfo.Information = "Attendance was not marked at facility within 15 mins of the session start time."
				data := &pb.UserBookingRequestQueries{
					BookingInfo:        bookingInfo,
					AttendanceInfo:     attendanceInfo,
					Reasons:            reasonForNoshow,
					Ratings:            ratings,
					FeedbackApplicable: feedbackApplicable,
					UserRating:         userFeedbackDetails,
				}
				res.Data = data

			}
		} else if slotInfo.AttendanceFlag == 0 && slotInfo.NoShowPeanltyId > 0 { //no show

			if len(userQuery.BookingReferenceNumber) > 0 && userQuery.Status == 0 { // pending
				attendanceInfo.Penalty = "01 Day deducted"
				attendanceInfo.PenaltyHeading = "No show penalty has been applied"
				attendanceInfo.Information = "You must cancel the session atleast 30 mins prior to the slot to avoid no-show penalty."
				data := &pb.UserBookingRequestQueries{
					BookingInfo:    bookingInfo,
					AttendanceInfo: attendanceInfo,
				}
				res.Data = data

			} else if len(userQuery.BookingReferenceNumber) > 0 && userQuery.Status == 1 { // resolved
				var reverted int32
				if err := s.Data.QueryRevertStatus(ctx, &reverted, slotInfo.BookingId); err != nil {
					log.Println("Error in getting resolved result.. Db error..", err)
					return err
				}

				if reverted == 1 { //feedback
					if userQuery.ReasonId == 6 || userQuery.ReasonId == 7 || userQuery.ReasonId == 10 || userQuery.ReasonId == 11 || userQuery.ReasonId == 12 {
						attendanceInfo.PenaltyHeading = "Your 1 day penalty has been reversed."
						attendanceInfo.Information = "Please mark your attendance within 15 mins or cancel your booking if not attending not later than 30 mins of your session start time in future to avoid a penalty."
						data := &pb.UserBookingRequestQueries{
							BookingInfo:        bookingInfo,
							AttendanceInfo:     attendanceInfo,
							Ratings:            ratings,
							FeedbackApplicable: feedbackApplicable,
							UserRating:         userFeedbackDetails,
						}
						res.Data = data
					} else {
						attendanceInfo.PenaltyHeading = "Your 1 day penalty has been reversed."
						attendanceInfo.Information = "Please mark your attendance within 15 mins or cancel your booking if not attending not later than 30 mins of your session start time in future to avoid a penalty."
						data := &pb.UserBookingRequestQueries{
							BookingInfo:    bookingInfo,
							AttendanceInfo: attendanceInfo,
						}
						res.Data = data
					}

				} else { // Not reversed
					attendanceInfo.PenaltyHeading = "No show penalty has been applied"
					attendanceInfo.Penalty = "01 Day deducted"
					attendanceInfo.Information = "Reversal cannot be made for the reason"
					data := &pb.UserBookingRequestQueries{
						BookingInfo:    bookingInfo,
						AttendanceInfo: attendanceInfo,
					}
					res.Data = data
				}
			} else if bookingInfo.IsReverted == 1 || bookingInfo.BookingTime.Seconds < 1609506060 { // Past booking records noshow
				data := &pb.UserBookingRequestQueries{
					BookingInfo:        bookingInfo,
					Ratings:            ratings,
					FeedbackApplicable: feedbackApplicable,
					UserRating:         userFeedbackDetails,
				}
				res.Data = data

			} else { // no raised
				attendanceInfo.PenaltyHeading = "No show penalty has been applied"
				attendanceInfo.Penalty = "01 Day deducted"
				attendanceInfo.Information = "Attendance not marked at facility. Please cancel the booking not later than 30 mins before your session start time to avoid the No Show penalty."
				data := &pb.UserBookingRequestQueries{
					BookingInfo:    bookingInfo,
					AttendanceInfo: attendanceInfo,
					Reasons:        reasonForNoshow,
				}
				res.Data = data
			}
		} else if slotInfo.AttendanceFlag == 0 && slotInfo.BookingCancelled == 1 {
			attendanceInfo.PenaltyHeading = "Booking status"
			attendanceInfo.Penalty = "Session was cancelled"
			data := &pb.UserBookingRequestQueries{
				BookingInfo:    bookingInfo,
				AttendanceInfo: attendanceInfo,
			}
			res.Data = data
		} else {
			data := &pb.UserBookingRequestQueries{
				BookingInfo: bookingInfo,
			}
			res.Data = data
		}
		status := &pb.Status{
			Status:  "success",
			Message: "Data fetched successfully!",
		}
		res.Status = status

		return nil
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid request! Cannot proceed!",
		}
		res.Status = status

		return nil
	}
}

func (s *Service) GetBookingIdInfo(ctx context.Context, slotInfo *chan model.FacilitySportDetails, bookingId int32, bRN string, wg *sync.WaitGroup) error {

	defer wg.Done()
	var bookingSlot model.FacilitySportDetails
	if len(bRN) == 9 {
		if err := s.Data.GetSwimmingslotInfo(ctx, &bookingSlot, bRN); err != nil {
			log.Println("Error getting Booking Slot details")
			return err
		}
	} else {
		if err := s.Data.GetBookingIdInfo(ctx, &bookingSlot, bookingId); err != nil {
			log.Println("Error getting Booking Slot details")
			return err
		}
	}
	*slotInfo <- bookingSlot

	return nil
}

func (s *Service) GetFeedbackOptions(ctx context.Context, ratingFeedback *chan []*pb.SportsFeedbackRating, onlineSession bool, wg *sync.WaitGroup) error {

	defer wg.Done()
	var ratingsForFeedback []*pb.SportsFeedbackRating
	if err := s.GetSportsFeedbackRatingOptions(ctx, &ratingsForFeedback, onlineSession, false); err != nil {
		log.Println("Could not fetch feedback options!")
		return err
	}

	*ratingFeedback <- ratingsForFeedback
	return nil
}

func (s *Service) GetReasonsForUserQueries(ctx context.Context, reasons *chan []model.UserBookingRequestQueries, queryTypeId int32, wg *sync.WaitGroup) error {

	defer wg.Done()
	var reason []model.UserBookingRequestQueries
	if err := s.Data.GetReasonsForUserQueries(ctx, &reason, queryTypeId); err != nil {
		log.Println("Error getting Reasons for No Show Penalty")
		return err
	}
	*reasons <- reason

	return nil
}

func (s *Service) UserNoShowRevertQueryInfo(ctx context.Context, userQuery *chan model.UserBookingRequestQueries, bookingId int32, wg *sync.WaitGroup) error {

	defer wg.Done()
	var userQueriesRaised []model.UserBookingRequestQueries
	bookingIds := []int32{bookingId}
	if err := s.Data.UserNoShowRevertQueryInfo(ctx, &userQueriesRaised, bookingIds); err != nil {
		log.Printf("Error getting User Query Information for bookingIds: %v, err: %v", bookingIds, err)
		return err
	}
	if len(userQueriesRaised) > 0 {
		*userQuery <- userQueriesRaised[0]
	}

	return nil
}

func (s *Service) GetUserFeedbackInformation(ctx context.Context, userFeedback *chan []model.SportsFeedbackUserRating, condition string, wg *sync.WaitGroup) error {

	defer wg.Done()
	var feedbackData []model.SportsFeedbackUserRating
	if err := s.Data.UserFeedbackForBooking(ctx, condition, &feedbackData); err != nil {
		fmt.Println("Could not fetch feedback for booking reference number.")
		return err
	}

	*userFeedback <- feedbackData

	return nil
}

func (s *Service) BookingSlotsDetailsGet(ctx context.Context, req *pb.BookingSlotsDetailsRequest, res *pb.BookingSlotsDetailsResponse) error {
	log.Println("Booking slots details :", req)

	// --- fetching slots details
	if req.FsId > 0 && req.SlotId > 0 && req.Date > 0 {
		var str structs.InfoText
		var slotDetails []structs.SlotDetail
		var pointerDataArray []structs.PointersData

		if err := s.Data.GetBookingSlotDetails(ctx, &str, req); err != nil {
			log.Println("Could not get slots details..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch slot details!",
			}
			res.Status = status
			return nil
		}

		var infArr []string
		infArr = strings.Split(str.InfoText, "^^") //string to array
		var slotDetail structs.SlotDetail

		slotDetail.Title = "Important information"

		for _, element := range infArr {
			pointerData := structs.PointersData{
				Text: element,
			}
			pointerDataArray = append(pointerDataArray, pointerData)
		}
		slotDetail.Pointers = pointerDataArray
		slotDetails = append(slotDetails, slotDetail)
		var isNoShowPenaltyOn bool

		if err := s.Data.NoShowPenaltyOn(req.FsId, &isNoShowPenaltyOn); err != nil {
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not No show Penalty Applicable details!",
			}
			res.Status = status
			return nil
		}

		var facilitySportIds structs.FacilitySportIds
		if err := s.Data.GetFacilityIdAndSportIdByFsId(&req.FsId, &facilitySportIds); err != nil {
			log.Println("Get facilityId and SportId from fsid error -->", err)
			return err
		}

		var versionType int32
		if req.AppType == "sports-android" || req.AppType == "sports-ios" {
			oldVersion, err := checkOldAppVersion(req.AppType, req.AppVersion)
			if err != nil {
				log.Println("error in getting version", err)
			}
			if oldVersion == true {
				versionType = 2
			} else {
				versionType = 1
			}
		}
		if str.SportId == 3 {
			str.CoachingType = SwimmingCoachingType
		}
		var data []model.TextBodyMappings
		cond := model.TextBodyMappings{
			SlotId:           req.SlotId,
			SportId:          str.SportId,
			CoachingType:     str.CoachingType,
			FacilityType:     facilitySportIds.FacilityType,
			NoShowApplicable: isNoShowPenaltyOn,
			AllSlotFlag:      true,
			AllCoachingFlag:  true,
			AllSportFlag:     true,
			AllCityFlag:      true,
			VersionType:      versionType,
		}

		if err := s.Data.GetTextDetails(&data, cond); err != nil {
			fmt.Println(err)
		}
		titleTestMapping := make(map[int32][]int32)
		var titleMappingIds []int32
		for idx, ele := range data {
			titleTestMapping[ele.TextTitleMappingId] = append(titleTestMapping[ele.TextTitleMappingId], int32(idx))
			titleMappingIds = append(titleMappingIds, ele.TextTitleMappingId)
		}
		var titleDetails []model.TextTitleMappings

		if err := s.Data.GetTitleDetails(&titleDetails, titleMappingIds); err != nil {
			fmt.Println(err)
		}
		var slotDetailsNew []structs.SlotDetail
		for _, ele := range titleDetails {
			bodyIndexs := titleTestMapping[ele.TextTitleMappingId]
			var pointerDataArrayNew []structs.PointersData
			for _, idx := range bodyIndexs {

				pointerData := structs.PointersData{
					Text: data[idx].Text,
				}
				pointerDataArrayNew = append(pointerDataArrayNew, pointerData)

			}
			slotDetail := structs.SlotDetail{
				Title:    ele.Title,
				Pointers: pointerDataArrayNew,
			}
			slotDetailsNew = append(slotDetailsNew, slotDetail)

		}
		slotDetails = append(slotDetails, slotDetailsNew...)
		for _, slotDetail := range slotDetails {

			var pointersProto []*pb.Pointer
			for _, pointer := range slotDetail.Pointers {

				pointerProto := &pb.Pointer{
					Text: pointer.Text,
				}
				pointersProto = append(pointersProto, pointerProto)
			}

			bookingSlotsProto := &pb.BookingSlots{
				Title:    slotDetail.Title,
				Pointers: pointersProto,
			}

			res.RelevantInfo = append(res.RelevantInfo, bookingSlotsProto)

		}
		status := &pb.Status{
			Status:  "success",
			Message: "Data fetched successfully!",
		}
		res.Status = status

		return nil
	} else if req.OnlineSessionId > 0 && req.SlotId > 0 && req.Date > 0 {

		var sessionDetails []model.SessionVideo

		if err := s.Data.GetLiveSessionDetails(&sessionDetails, req.OnlineSessionId); err != nil {
			log.Println("Could not get session details..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch session details!",
			}
			res.Status = status
			return nil
		}
		// get user details
		var userData model.UserProduct
		if req.UserId > 0 {
			if err := s.Data.GetUserDetails(&userData, req.UserId); err != nil {
				log.Println("Could not get user details..", err)
				status := &pb.Status{
					Status:  "failure",
					Message: "Could not fetch user details!",
				}
				res.Status = status
				return nil
			}
		}
		firstName := strings.Split(userData.Name, " ")[0]

		for _, sessDetail := range sessionDetails {
			sessDetail.FirstName = firstName
			res.SessionData = sessDetail.Proto()

			if len(sessDetail.ProviderRealName) > 0 {
				var pointersProto []*pb.Pointer

				pointerProto := &pb.Pointer{
					Text: sessDetail.ProviderRealName,
				}
				pointersProto = append(pointersProto, pointerProto)

				if len(sessDetail.AboutTrainer) > 0 {
					pointerProto := &pb.Pointer{
						Text: sessDetail.AboutTrainer,
					}
					pointersProto = append(pointersProto, pointerProto)
				}

				bookingSlotsProto := &pb.BookingSlots{
					Title:    "Trainer",
					Pointers: pointersProto,
				}

				res.RelevantInfo = append(res.RelevantInfo, bookingSlotsProto)

			}
			if sessDetail.CaloriesBurned > 0 {

				var pointersProto []*pb.Pointer

				pointerProto := &pb.Pointer{
					Text: strconv.FormatFloat(sessDetail.CaloriesBurned, 'f', 2, 64) + " Cal",
				}
				pointersProto = append(pointersProto, pointerProto)

				bookingSlotsProto := &pb.BookingSlots{
					Title:    "Calories Burn",
					Pointers: pointersProto,
				}

				res.RelevantInfo = append(res.RelevantInfo, bookingSlotsProto)
			}

			if len(sessDetail.SessionDescription) > 0 {

				var pointersProto []*pb.Pointer

				pointerProto := &pb.Pointer{
					Text: sessDetail.SessionDescription,
				}
				pointersProto = append(pointersProto, pointerProto)

				bookingSlotsProto := &pb.BookingSlots{
					Title:    "About this Session",
					Pointers: pointersProto,
				}

				res.RelevantInfo = append(res.RelevantInfo, bookingSlotsProto)
			}

			if len(sessDetail.EquipmentRequired) > 0 {

				var pointersProto []*pb.Pointer

				eqps := strings.Split(sessDetail.EquipmentRequired, ",")

				for _, eqp := range eqps {
					pointerProto := &pb.Pointer{
						Text: eqp,
					}
					pointersProto = append(pointersProto, pointerProto)
				}

				bookingSlotsProto := &pb.BookingSlots{
					Title:    "Equipments Required",
					Pointers: pointersProto,
				}

				res.RelevantInfo = append(res.RelevantInfo, bookingSlotsProto)
			}
		}

		status := &pb.Status{
			Status:  "success",
			Message: "Data fetched successfully!",
		}
		res.Status = status

		return nil
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Invalid request! Cannot proceed!",
		}
		res.Status = status

		return nil
	}
}

func (s *Service) MarkAttendanceStatus(ctx context.Context, req *pb.MarkAttendance, res *pb.Ack) error {
	fmt.Println("req ----- ", req)
	var markSuccessful bool
	if err := s.Data.AttendanceStatusMark(req, &markSuccessful); err != nil {
		fmt.Println("Error in marking/unmarking attendance for brn -- ", req.BookingReferenceNumber)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not mark/unmark attendance for brn -- " + req.BookingReferenceNumber + "!",
		}
		res.Status = status
		return nil
	}

	if markSuccessful {
		status := &pb.Status{
			Status:  "success",
			Message: "Attendance status updated successfully!",
		}
		res.Status = status

		if req.AttendanceFlag {
			go s.SendNotificationForAttendanceMarked(ctx, req.BookingReferenceNumber)
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Error in marking/unmarking attendance for brn -- " + req.BookingReferenceNumber + "!",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) DetermineSlotIdByCurrentTime(ctx context.Context, slotIds *[]int32, currentTime time.Time) error {
	var slotData []structs.Slot
	if err := s.Data.GetAllSlots(ctx, &slotData); err != nil {
		fmt.Println("Error in getting all slots..", err)
		return err
	}
	// fmt.Println(slotData)
	currentTimeInMinutes := int32(currentTime.Hour())*60 + int32(currentTime.Minute())

	entrySlot := int32(0)
	for _, ele := range slotData {

		slotStartHour := ele.StartHour
		slotEndHour := ele.EndHour
		if slotStartHour > slotEndHour {
			// handling the slots for the time change from 24 P.M. to 1 A.M.
			slotEndHour = slotEndHour + 24

		}
		slotStartTimeInMiuntes := slotStartHour*60 + ele.StartMin
		slotEndTimeInMiutes := slotEndHour*60 + ele.EndMin
		//check if the slots stated int last 30 minutes;
		if slotStartTimeInMiuntes >= (currentTimeInMinutes-45) && slotStartTimeInMiuntes <= (currentTimeInMinutes-30) && currentTimeInMinutes < slotEndTimeInMiutes {
			entrySlot = ele.SlotID
			*slotIds = append(*slotIds, entrySlot)
		}
	}

	return nil
}
func (s *Service) SendNotificationForAttendanceMarked(ctx context.Context, bookingRef string) error {
	fmt.Println("sending notification on mark attendance -- ")

	bookingGetData := &model.AllBooking{
		BookingReferenceNumber: bookingRef,
	}
	var allBooking []model.AllBooking

	app_condition := make(map[string]string)
	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"
	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	if err := s.Data.BookingDetailsGet(ctx, bookingGetData, &allBooking, 1, 1, 1); err != nil {
		return err
	}

	for _, aBooking := range allBooking {

		usersIds := make([]int32, 0)
		usersIds = append(usersIds, aBooking.UserID)

		nData := structs.FcmNotification{
			Title:         "Marked Safe and NO SHOW penalty averted 😎",
			Text:          "Hope you had a great " + aBooking.SportName + " session!!",
			UserIds:       usersIds,
			Id:            aBooking.BookingID,
			Action:        "upcoming_bookings",
			ConditionData: condition_data,
			CtaText:       bookingRef,
			ExpiryDate:    time.Now().AddDate(0, 0, 3).Unix(),
			SubCategory:   int32(notificationPB.NotificationSubCategories_ATTENDANCE_MARKED),
			//ExtraData:     extra_data,
		}
		s.Data.SendNotificationOnPublish(&nData)
	}

	return nil
}

func (s *Service) GetFreezeLogsForSubscription(ctx context.Context, req *pb.SportsUserFreezeLogRequest, res *pb.SportsUserFreezeLogResponse) error {
	fmt.Println("req ---- ", req)

	freezeLogCond := &model.SportsUserFreezeLog{
		SubscriptionId:     req.SubscriptionId,
		UserId:             req.UserId,
		UniqueFreezeString: req.UniqueFreezeString,
		FreezeReasonId:     req.FreezeReasonId,
	}
	var freezeData []model.SportsUserFreezeLog

	if err := s.Data.FreezeLogsForSubscriptionGet(&freezeData, freezeLogCond); err != nil {
		fmt.Println("Error in getting freeze logs... ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get freeze logs..",
		}
		res.Status = status
		return err
	}

	status := &pb.Status{
		Status:  "success",
		Message: "Freeze logs fetched successfully.",
	}
	res.Status = status

	for _, ele := range freezeData {
		res.FreezeLog = append(res.FreezeLog, ele.Proto())
	}
	return nil
}

func (s *Service) UnblockBlockedSlots(ctx context.Context, req *pb.UnblockInventoryRequest, res *pb.Ack) error {
	fmt.Println("unblock inv req ---- ", req)

	if len(req.BlockedInventoryIdsArray) > 0 {

		var blockedRows []model.SportsBlockedInventory

		if err := s.Data.GetBlockedInventoryRowsForIds(&req.BlockedInventoryIdsArray, &blockedRows); err != nil {
			fmt.Println("Error in getting blocked rows... ", err)
			return err
		}

		var updateSuccess bool
		if err := s.Data.MarkDeletedBlockedInventoryRowsForIds(&req.BlockedInventoryIdsArray, req.UserId, &updateSuccess); err != nil {
			fmt.Println("Error in getting blocked rows... ", err)
			return err
		}

		fsIdCountMap := make(map[int32]int32)

		if updateSuccess {
			for _, blocked_row := range blockedRows {

				if blocked_row.PAID > 0 {
					// Court Blocking case

					combo := structs.UnblockCombo{
						FSID:   blocked_row.FSID,
						PAID:   blocked_row.PAID,
						Date:   blocked_row.Date,
						SlotID: blocked_row.SlotID,
						PacsID: blocked_row.PacsID,
					}
					var deleteSuccess bool

					if err := s.Data.DeleteFsAndPaActiveSlotsForGiven(&combo, &deleteSuccess); err != nil {
						log.Println("Error in getting blocked rows... ", err)
						return err
					}

					if deleteSuccess {
						if _, ok := fsIdCountMap[blocked_row.FSID]; ok == false {
							fsIdCountMap[blocked_row.FSID] = 1
						}

						if err := s.Data.MarkDeletedFlagSportsInActiveDay(ctx, blocked_row.FSID, blocked_row.Date, req.UserId); err != nil {
							log.Println("Error in updating deleted flag for  inactive day ", err)
							return err
						}
						//go s.ProcessSlotPopulationForFsId(ctx, blocked_row.FSID)
					}
				}
			}

			for fs_id, _ := range fsIdCountMap {

				//go s.ProcessSlotPopulationForFsId(ctx, fs_id)

				fsInfo := &pb.FacilitySportDetails{
					FsId: fs_id,
				}

				go s.Data.PublishFsIdForSlotPopulation(ctx, fsInfo)
			}

			status := &pb.Status{
				Status:  "success",
				Message: "Unblock successfully.",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not mark deleted for given rows",
			}
			res.Status = status
		}
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not unblock/ received no ids to unblock ..",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) StreamDetailsForOnlineSession(ctx context.Context, req *pb.StreamDetailsRequest, res *pb.StreamDetailsResponse) error {
	fmt.Println("req ---- ", req)

	if req.OnlineSessionId > 0 {
		var sessionDetails []model.SessionVideo

		if err := s.Data.GetLiveSessionDetails(&sessionDetails, req.OnlineSessionId); err != nil {
			fmt.Println("Could not get session details..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not fetch session details!",
			}
			res.Status = status
			return nil
		}
		if len(sessionDetails) > 0 {

			var userData model.UserProduct
			if req.UserId > 0 {
				if err := s.Data.GetUserDetails(&userData, req.UserId); err != nil {
					fmt.Println("Could not get user details..", err)
					status := &pb.Status{
						Status:  "failure",
						Message: "Could not fetch user details!",
					}
					res.Status = status
					return nil
				}
			}
			firstName := strings.Split(userData.Name, " ")[0]

			for _, sessDetail := range sessionDetails {
				if req.AppType == "sports-ios" && req.AppVersion == "1.2.7" {
					additionalPoints := []string{"Please make sure your phone is not on silemt mode while viewing the session for the best experience"}
					sessDetail.AdditionalPoints = additionalPoints
				}
				currentTs := time.Now().Unix()
				sessionStartTs := sessDetail.SessionTime.Unix()
				fmt.Println(currentTs, sessionStartTs)
				diffTs := currentTs - sessionStartTs
				if diffTs > int64(sessDetail.Duration) {

					var timeSpent int32
					if err := s.Data.GetTimeSpentInSession(req.OnlineSessionId, req.UserId, &timeSpent); err != nil {
						fmt.Println("Could not get fetch time spent details..", err)
						status := &pb.Status{
							Status:  "failure",
							Message: "Could not fetch time spent!",
						}
						res.Status = status
						return nil
					}

					sessDetail.TimeSpent = timeSpent

					res.State = 3
					res.DisplayImageUrl = "https://www.getfitso.com/images/sports_app_images/session-finished.jpg"
					res.DisplayMessage = "Session finished"
				} else if diffTs >= 0 {
					res.State = 1

					var streamUrl string
					if err := s.Data.GetStreamUrl(req.OnlineSessionId, diffTs, &streamUrl); err != nil {
						fmt.Println("Could not get session details..", err)
						status := &pb.Status{
							Status:  "failure",
							Message: "Could not fetch stream url!",
						}
						res.Status = status
						return nil
					}

					res.StreamUrl = streamUrl

					newReq := &pb.MarkOnlineSessionAttendanceRequest{
						UserId:          req.UserId,
						OnlineSessionId: req.OnlineSessionId,
						IsEntry:         1,
						AppType:         req.AppType,
						AppVersion:      req.AppVersion,
					}
					newResp := &pb.Ack{}
					go s.MarkOnlineSessionAttendance(ctx, newReq, newResp)

				} else {
					res.State = 2
					res.DisplayImageUrl = "https://www.getfitso.com/images/sports_app_images/yet-to-start.jpg"
					res.DisplayMessage = "Session yet to start"
				}
				sessDetail.FirstName = firstName
				res.SessionData = sessDetail.Proto()

				break
			}

			status := &pb.Status{
				Status:  "success",
				Message: "Fetched successfully",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "failed",
				Message: "Invalid session",
			}
			res.Status = status
		}

		return nil
	} else {
		status := &pb.Status{
			Status:  "failed",
			Message: "Invalid online session id",
		}
		res.Status = status
	}
	return nil
}
func (s *Service) NoShowPenaltyRecordGet(ctx context.Context, req *pb.NoShowPenaltyRecordRequest, res *pb.NoShowPenaltyRecordResponse) error {
	fmt.Println("--------req-----", req)
	var record []model.NoShowPenaltyAppliedRecord
	//var cond model.NoShowPenaltyAppliedRecord
	cond := model.NoShowPenaltyAppliedRecord{
		StartDate:               req.StartDate,
		EndDate:                 req.EndDate,
		SlotIDs:                 req.SlotIds,
		IsReverted:              req.IsReverted,
		DeclineFlag:             req.DeclineFlag,
		UserIDs:                 req.UserIds,
		FsIDs:                   req.FsIds,
		BookingReferenceNumbers: req.BookingReferenceNumbers,
		Start:                   req.Start,
		Count:                   req.Count,
		IsRaised:                req.IsRaised,
	}
	if err := s.Data.NoShowPenaltyRecordGet(&record, cond); err != nil {
		fmt.Println("error in getting No Show Penalty Record ---- ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "error in getting No Show Penalty Record!",
		}
		res.Status = status
		return nil
	}
	fmt.Println("Record--------", record)
	for _, ele := range record {
		res.NoShowPenaltyRecord = append(res.NoShowPenaltyRecord, ele.Proto())
	}
	status := &pb.Status{
		Status:  "success",
		Message: "Fetched successfully",
	}
	res.Status = status
	return nil
}

func (s *Service) NoShowRevert(ctx context.Context, req *pb.NoShowRevertRequest, res *pb.NoShowPenaltyRecordResponse) error {
	fmt.Println("--------req-----", req)

	jobs := make(chan structs.NoShowRevertTask, 100)
	errors := make(chan error, 100)
	var records []structs.NoShowRevertTask

	bookingIds := GetIntegerArrayFromCommaSeparatedString(req.BookingIds)

	if err := s.Data.NoShowRecordForRevert(&records, bookingIds); err != nil {
		fmt.Println("error in getting record.")
		status := &pb.Status{
			Status:  "failure",
			Message: " Getting error in update : " + err.Error(),
		}
		res.Status = status
		return nil
	}
	var wg sync.WaitGroup
	for w := 1; w <= 10; w++ {
		go s.worker(w, &wg, jobs, errors)
	}

	for _, record := range records {
		record.RevertedBy = req.RevertedBy
		record.Reason = req.Reason
		record.EmailNotify = req.EmailNotify
		record.NotificationNotify = req.NotificationNotify
		record.DeclineFlag = req.DeclineFlag
		record.NotRaised = req.NotRaised
		jobs <- record
		wg.Add(1)
	}
	close(jobs)

	wg.Wait()
	var status pb.Status
	select {
	case err := <-errors:
		fmt.Println("finished with error:", err.Error())
		status = pb.Status{
			Status:  "failure",
			Message: " Getting error in update : " + err.Error(),
		}
	default:
		status = pb.Status{
			Status:  "success",
			Message: "updated successfully",
		}
	}

	res.Status = &status

	return nil
}

func (s *Service) worker(id int, wg *sync.WaitGroup, jobs chan structs.NoShowRevertTask, errorsChan chan<- error) {
	pcl := util.GetProductClient()
	for j := range jobs {
		fmt.Println("worker", id, "processing job", j)
		bookingIds := GetIntegerArrayFromCommaSeparatedString(j.BookingIds)

		// not handling no-show revert updates for hyderabad cult-app users since sports-api is handling it now
		skipHyderabadUsers := false
		if j.ProductId > 0 {
			productDetails, _ := pcl.GetProductDetails(context.TODO(), &productPB.Product{ProductId: j.ProductId})
			if j.AppType == "cult-app" && productDetails.LocationIdV2 == HYDERABAD_CITY_ID {
				skipHyderabadUsers = true
			}
		}

		if !skipHyderabadUsers {
			if j.DeclineFlag != true {
				if err1 := s.updateSubscription(j); err1 != nil {
					fmt.Println("error in updating subscriptions.")
					errorsChan <- errors.New("Error in updating subscriptions  with BookingReferernce Number:" + j.BookingReferenceNumbers + " " + err1.Error())
				}
			}
			if err2 := s.Data.UpdateNSPARecord(bookingIds, j.RevertedBy, j.Reason, j.DeclineFlag); err2 != nil {
				fmt.Println("error in updating No Show Penalty Applied Record.")
				errorsChan <- errors.New("Error in updating No Show Penalty Record with Booking Ids:" + j.BookingIds + " " + err2.Error())
			}

			if j.DeclineFlag != true {
				if err3 := s.Data.UpdateNoShowRevertInBookings(bookingIds); err3 != nil {
					fmt.Println("error in updating Bookings.")
					errorsChan <- errors.New("Error in updating no show revert in all bookings  with Booking Ids:" + j.BookingIds + " " + err3.Error())
				}
			}
			for _, bookingId := range bookingIds {
				storeUserQueryData := &model.UserBookingRequestQueries{}
				if j.NotRaised == true {
					var brn model.AllBooking
					if err := s.Data.GetBrnFromBookingId(bookingId, &brn); err != nil {
						log.Println("error in getting Booking reference number.")
						errorsChan <- errors.New("Error in BRN " + err.Error())
					}
					storeUserQueryData = &model.UserBookingRequestQueries{
						BookingId:              bookingId,
						BookingReferenceNumber: brn.BookingReferenceNumber,
						Status:                 1,
						QueryTypeId:            1,
						ReasonId:               13,
						OtherReason:            j.Reason,
					}
				} else {
					storeUserQueryData = &model.UserBookingRequestQueries{
						BookingId:   bookingId,
						Status:      1,
						QueryTypeId: 1,
					}
				}

				if err4 := s.Data.UpdateQueryStatusNoShow(storeUserQueryData); err4 != nil {
					log.Println("error in updating Query Status.")
					errorsChan <- errors.New("Error in updating no show user query status with Booking Id:" + strconv.Itoa(int(bookingId)) + " " + err4.Error())
				}
			}
		}

		for _, bookingId := range bookingIds {
			if j.DeclineFlag != true {
				go s.sendNoShowRevertEventToSqs(context.TODO(), bookingId, j.Reason, int32(int(j.RevertedBy)))
			}
			go s.sendNoShowRevertEmailAndNotification(bookingId, j.EmailNotify, j.NotificationNotify, j.DeclineFlag)
		}
		wg.Done()
	}
}
func (s *Service) sendNoShowRevertEmailAndNotification(bookingId int32, emailNotify bool, notificationNotify bool, declineFlag bool) error {

	var bookingAndAttendanceDetails structs.BookingAndAttendanceDetails
	if err := s.Data.BookingAndAttendanceDetails(&bookingAndAttendanceDetails, bookingId); err != nil {
		fmt.Println("Error in getting booking details with booking reference number ----", bookingId)
		return nil
	}
	userEmail := bookingAndAttendanceDetails.Email
	if len(userEmail) == 0 {
		var parentDetails model.UserProduct
		if err := s.Data.GetParentDetailsForUser(&parentDetails, bookingAndAttendanceDetails.UserId); err != nil {
			fmt.Println("Error in getting parent details! Going Ahead!")
		}

		if len(parentDetails.Email) > 0 {
			userEmail = parentDetails.Email
		} else if len(parentDetails.ContactEmail) > 0 {
			userEmail = parentDetails.ContactEmail
		}

	}

	if len(userEmail) == 0 {
		fmt.Println("No show mail not sent for user_id: ", bookingAndAttendanceDetails.UserId, " with Booking Reference Number: ", bookingAndAttendanceDetails.BookingReferenceNumber)
		return nil
	}

	if emailNotify {
		go s.sendNoShowRevertMail(bookingAndAttendanceDetails, userEmail, declineFlag)
	}
	if notificationNotify {
		go s.sendNoShowRevertNotification(bookingAndAttendanceDetails, declineFlag)
	}
	return nil
}

func (s *Service) sendNoShowRevertNotification(bookingAndAttendanceDetails structs.BookingAndAttendanceDetails, declineFlag bool) error {

	isLateComer := false
	if strings.Compare("0001-01-01 00:00:00", bookingAndAttendanceDetails.CheckinTime.Format(formatYMDHIS)) != 0 && bookingAndAttendanceDetails.Medium == 1 {
		isLateComer = true
	}

	var title string

	if declineFlag != true {
		if isLateComer {
			title = "1-day Penalty reversed for LATE ENTRY for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		} else {
			title = "1-day Penalty reversed for NO SHOW for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		}
	} else {
		if isLateComer {
			title = "No show penalty cannot be reversed for LATE ENTRY for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		} else {
			title = "No show penalty cannot be reversed for NO SHOW for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		}
	}

	text := "Dear cultpass PLAY Member,\n\n"

	if declineFlag != true {
		text += "After investigation/internal confirmation, we have reversed the 1-day penalty against your booking at " + bookingAndAttendanceDetails.DisplayName + " on Date: " + bookingAndAttendanceDetails.BookingDate.Format(formatYMD) + " at Time: " + bookingAndAttendanceDetails.Timing + " which was earlier marked under "
	} else {
		text += "After evaluating your request for No Show reversal, we have found that the penalty application is correct. Hence, the No Show penalty cannot be reversed against your booking at " + bookingAndAttendanceDetails.DisplayName + " on Date: " + bookingAndAttendanceDetails.BookingDate.Format(formatYMD) + " at Time: " + bookingAndAttendanceDetails.Timing + " which was earlier marked under "
	}

	if declineFlag != true {
		if isLateComer {
			text += " LATE ENTRY and added 1 day back to your subscription."
		} else {
			text += " NO SHOW and added 1 day back to your subscription."
		}
	} else {
		if isLateComer {
			text += " LATE ENTRY."
		} else {
			text += " NO SHOW."
		}
	}

	text += "\n\nRegards,\n"
	text += "Fitso Support\n"
	/*
		fmt.Println("---------noti-")
		fmt.Println("------title------", title)
		fmt.Println("------text------", text)
	*/
	app_condition := make(map[string]string)

	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"

	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	curTime := time.Now()

	//notification exp time
	defExpTime := config.Get("static", "default_notification_expiry_time_in_hours").Int(4)
	expTime := curTime.AddDate(0, 0, defExpTime)
	expTimestamp := expTime.Unix()

	usersIds := make([]int32, 0)
	usersIds = append(usersIds, bookingAndAttendanceDetails.UserId)
	var subCategory int32
	if !declineFlag {
		subCategory = int32(notificationPB.NotificationSubCategories_NOSHOW_REVERTED)
	} else {
		subCategory = int32(notificationPB.NotificationSubCategories_NOSHOW_NOT_REVERTED)
	}

	nData := structs.FcmNotification{
		Title:         title,
		Text:          text,
		UserIds:       usersIds,
		Id:            bookingAndAttendanceDetails.BookingId,
		CtaText:       bookingAndAttendanceDetails.BookingReferenceNumber,
		Action:        "past_bookings",
		ConditionData: condition_data,
		ExpiryDate:    expTimestamp,
		SubCategory:   subCategory,
	}

	s.Data.SendNotification(&nData)

	return nil
}

func (s *Service) sendNoShowRevertMail(bookingAndAttendanceDetails structs.BookingAndAttendanceDetails, userEmail string, declineFlag bool) error {

	isLateComer := false
	if strings.Compare("0001-01-01 00:00:00", bookingAndAttendanceDetails.CheckinTime.Format(formatYMDHIS)) != 0 && bookingAndAttendanceDetails.Medium == 1 {
		isLateComer = true
	}

	var subject string

	if declineFlag != true {
		if isLateComer {
			subject = "1-day Penalty reversed for LATE ENTRY for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		} else {
			subject = "1-day Penalty reversed for NO SHOW for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		}
	} else {
		if isLateComer {
			subject = "No show penalty cannot be reversed for LATE ENTRY for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		} else {
			subject = "No show penalty cannot be reversed for NO SHOW for your session Booking Ref: [" + bookingAndAttendanceDetails.BookingReferenceNumber + "]"
		}
	}

	message := "Dear cultpass PLAY Member,<br><br>"

	if declineFlag != true {
		message += "After investigation/internal confirmation, we have reversed the 1-day penalty against your booking at " + bookingAndAttendanceDetails.DisplayName + " on Date: " + bookingAndAttendanceDetails.BookingDate.Format(formatYMD) + " at Time: " + bookingAndAttendanceDetails.Timing + " which was earlier marked under "
	} else {
		message += "After evaluating your request for No Show reversal, we have found that the penalty application is correct. Hence, the No Show penalty cannot be reversed against your booking at " + bookingAndAttendanceDetails.DisplayName + " on Date: " + bookingAndAttendanceDetails.BookingDate.Format(formatYMD) + " at Time: " + bookingAndAttendanceDetails.Timing + " which was earlier marked under "
	}

	if declineFlag != true {
		if isLateComer {
			message += " LATE ENTRY and added 1 day back to your subscription."
		} else {
			message += " NO SHOW and added 1 day back to your subscription."
		}
	} else {
		if isLateComer {
			message += " LATE ENTRY."
		} else {
			message += " NO SHOW."
		}
	}

	message += "<br><br>Regards,<br>"
	message += "Fitso Support<br>"
	/*
		fmt.Println("----------")
		fmt.Println("------subject------", subject)
		fmt.Println("------message------", message)
	*/
	bccEmails := [1]string{"<EMAIL> "}

	var bccEm []*structs.Email

	for _, emailId := range bccEmails {
		bccEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		bccEm = append(bccEm, bccEmVal)
	}

	fromEm := &structs.Email{
		Name:         "Fitso Contact",
		EmailAddress: "<EMAIL>",
	}
	toEmail := &structs.Email{
		EmailAddress: userEmail,
	}
	var toEmails []*structs.Email
	toEmails = append(toEmails, toEmail)

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEmails,
		BCC:     bccEm,
		Subject: subject,
		Message: message,
	}
	if err := s.Data.SendSealsBookingFailureEmail(emailReq); err != nil {
		fmt.Println("Error in sending broadcast email")
		return err
	}
	return nil
}

func (s *Service) updateSubscription(req structs.NoShowRevertTask) error {
	var serviceList structs.ServiceConfig
	var asycnServiceList structs.AsyncServiceConfig

	config.Get("codeIndependent", "services").Scan(&serviceList)
	config.Get("codeIndependent", "asyncServices").Scan(&asycnServiceList)

	service := micro.NewService()
	pcl = productPB.NewProductService(serviceList.Product, service.Client())

	subscriptionData := &productPB.Subscription{
		SubscriptionId: req.SubscriptionId,
		UserId:         req.UserId,
	}

	// session affinity based call
	ctx := context.TODO()
	response, err := pcl.SubscriptionGet(ctx, subscriptionData)

	if err != nil {
		log.Println("updateSubscription: Error in getting subscription for req: %v, err: %v", subscriptionData, err)
		return err
	}
	if response.Status.Status == "success" {
		requiredSubscriptionpb := response.Subscriptions[0]
		fmt.Println("-----------required ---------")

		currSubEndDate := time.Unix(requiredSubscriptionpb.SubscriptionEndDate.Seconds, 0)
		effectiveSubEndData := currSubEndDate.AddDate(0, 0, int(req.DaysToRevert))
		subEndDate, _ := ptypes.TimestampProto(effectiveSubEndData)

		updateSubData := &productPB.UpdateSubscriptionRequest{
			SubscriptionId:      requiredSubscriptionpb.SubscriptionId,
			EndDate:             subEndDate,
			SubscriptionEndDate: subEndDate,
			SessionCount:        requiredSubscriptionpb.SessionCount,
			FreezesProvided:     requiredSubscriptionpb.FreezesProvided,
			UpdatedBy:           req.UserId,
			IsActive:            true,
			DeactivationDate:    nil,
		}

		if _, err := pcl.UpdateSubscription(context.TODO(), updateSubData); err != nil {
			fmt.Println("Error in updating subscription for new extended subscription end date... ", err)
			return err
		}
	}

	return nil
}

func (s *Service) GetRecommendedSlots(ctx context.Context, req *pb.GetRecommendedSlotsRequest, res *pb.GetRecommendedSlotsResponse) error {

	bookingCond := &model.AllBooking{
		UserID:                    req.UserId,
		IsOnlineSession:           true,
		IsReturnAllFutureBookings: true,
	}
	/*var recentslots []structs.RecentSlotDetails
	if err := s.Data.GetRecentSlots(bookingCond, &recentslots); err != nil {
		fmt.Println("Could not get recent slots..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get recent slots!",
		}
		res.Status = status
		return nil
	}


	var recentSessions []model.OnlineSessions

	if len(recentslots) > 0 {
		if err := s.Data.GetRecommenedSession(&recentSessions, recentslots); err != nil {
			fmt.Println("Could not get recent slots..", err)
			status := &pb.Status{
				Status:  "failure",
				Message: "Could not get recent slots!",
			}
			res.Status = status
			return nil
		}
	}
	*/
	var recommendedSessions []model.OnlineSessions
	if err := s.Data.GetRecommenedSession(&recommendedSessions, nil); err != nil {
		fmt.Println("Could not get recent slots..", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get recent slots!",
		}
		res.Status = status
		return nil
	}

	var allBookingsForDate []model.AllBooking
	onlineSessionsToBookingMap := make(map[int32]int32)
	if err := s.Data.GetAllBookedSlotsForUser(ctx, bookingCond, &allBookingsForDate); err != nil {
		fmt.Println("Error in getting all booked slots for date  slot and user", err)
		return err
	}
	for _, ele := range allBookingsForDate {
		onlineSessionsToBookingMap[ele.OnlineSessionID] = ele.BookingID
	}
	var allSessions []model.OnlineSessions

	/*for _, ele := range recentSessions {
		if ele.OnlineSessionId > 0 {
			allSessions = append(allSessions, ele)
		}
	}*/
	for _, ele := range recommendedSessions {
		if ele.OnlineSessionId > 0 {
			allSessions = append(allSessions, ele)
		}
	}
	//resize to 2 as present requirentmeints of only 2 slot
	sessionVideoIdToSessionMap := make(map[int32][]model.OnlineSessions)
	var sessionVideoIds []int32
	for _, ele := range allSessions {
		if _, ok := sessionVideoIdToSessionMap[ele.SessionVideoId]; !ok {
			sessionVideoIds = append(sessionVideoIds, ele.SessionVideoId)
		}
		sessionVideoIdToSessionMap[ele.SessionVideoId] = append(sessionVideoIdToSessionMap[ele.SessionVideoId], ele)

	}

	for _, sessionVideoID := range sessionVideoIds {
		sessions := sessionVideoIdToSessionMap[sessionVideoID]
		sessionTimeToSessionMap := make(map[time.Time][]model.OnlineSessions)

		for _, session := range sessions {
			sessionTimeToSessionMap[session.Date] = append(sessionTimeToSessionMap[session.Date], session)
		}
		//  sort the map by key
		var sessionTimeToSessionMapKey []time.Time

		for key := range sessionTimeToSessionMap {
			sessionTimeToSessionMapKey = append(sessionTimeToSessionMapKey, key)
		}

		sort.Slice(sessionTimeToSessionMapKey, func(i, j int) bool {
			return sessionTimeToSessionMapKey[i].Before(sessionTimeToSessionMapKey[j])
		})

		var availableDates []*pb.AvailableDates
		for _, sTMapKey := range sessionTimeToSessionMapKey {
			sessionsByDate := sessionTimeToSessionMap[sTMapKey]
			var events []*pb.Event

			//  sort the session by  starting time

			sort.Slice(sessionsByDate, func(i, j int) bool {
				return sessionsByDate[i].StartHour*60+sessionsByDate[i].StartMin < sessionsByDate[j].StartHour*60+sessionsByDate[j].StartMin
			})

			for _, ele := range sessionsByDate {

				slot := &pb.Slot{
					SlotId:    ele.SlotId,
					Timing:    getStartTimingFromSlotsTimingsDetails(ele.StartHour, ele.StartMin),
					StartHour: ele.StartHour,
					StartMin:  ele.StartMin,
					EndHour:   ele.EndHour,
					EndMin:    ele.EndMin,
				}
				slotCapacityObj := model.SlotCapacity{
					Capacity:          ele.Capacity,
					RemainingCapacity: ele.RemainingCapacity,
					Active:            1 - ele.DeletedFlag,
					BookingID:         onlineSessionsToBookingMap[ele.OnlineSessionId],
				}
				var SlotAvailability model.SlotAvailability
				if err := s.GetSlotAvailabilityStatsByCapacity(ctx, slotCapacityObj, &SlotAvailability); err != nil {
					fmt.Println("Capacity Stats fetch err")
					return err
				}

				SessionTime, _ := ptypes.TimestampProto(ele.SessionTime)

				onlineSession := &pb.OnlineSessions{
					OnlineSessionId:  ele.OnlineSessionId,
					SessionVideoId:   ele.SessionVideoId,
					TitleB:           ele.TitleB,
					Description:      ele.Description,
					SessionTime:      SessionTime,
					AvailabilityType: SlotAvailability.AvailabilityType,
					AvailabilityText: SlotAvailability.AvailabilityText,
				}

				group := &pb.SessionGroup{
					TitleA: ele.TitleA,
				}

				event := &pb.Event{
					Slot:          slot,
					OnlineSession: onlineSession,
					Group:         group,
				}
				events = append(events, event)

			}
			SessionTime, _ := ptypes.TimestampProto(sessionsByDate[0].Date)

			availableDate := &pb.AvailableDates{
				Timestamp:      SessionTime,
				SessionDetails: events,
			}
			availableDates = append(availableDates, availableDate)

		}
		recommendedSessions := &pb.RecommendedSessions{
			GroupName:          sessions[0].Title,
			EventName:          sessions[0].Subtitle,
			SessionVideoId:     sessions[0].SessionVideoId,
			ThumbnailImage:     sessions[0].ThumbnailUrl,
			AvailableDates:     availableDates,
			SessionDuration:    sessions[0].Duration,
			CalorieBurn:        sessions[0].CaloriesBurned,
			TrainerName:        sessions[0].ProviderRealName,
			TrainerTitle:       sessions[0].TrainerTitle,
			AboutTrainer:       sessions[0].AboutTrainer,
			TrainerImageUrl:    sessions[0].DpUrl,
			SessionDescription: sessions[0].SessionDescription,
			PreSessionImageUrl: sessions[0].BgImageUrl,
		}

		res.RecommendedSessions = append(res.RecommendedSessions, recommendedSessions)

	}
	status := &pb.Status{
		Status:  "success",
		Message: "Fetched successfully",
	}
	res.Status = status
	return nil
}

func (s *Service) MarkOnlineSessionAttendance(ctx context.Context, req *pb.MarkOnlineSessionAttendanceRequest, res *pb.Ack) error {

	if req.UserId > 0 && req.OnlineSessionId > 0 && req.VideoExitSecond >= 0 {
		var OnlineSessionAtt model.OnlineSessionAttendance

		OnlineSessionAtt.UserId = req.UserId
		OnlineSessionAtt.OnlineSessionId = req.OnlineSessionId

		if req.StartTimestamp <= 0 || req.EndTimestamp <= 0 {
			nowTs := time.Now().Unix()
			OnlineSessionAtt.StartTimestamp = nowTs
			OnlineSessionAtt.EndTimestamp = nowTs
		} else {
			OnlineSessionAtt.StartTimestamp = req.StartTimestamp
			OnlineSessionAtt.EndTimestamp = req.EndTimestamp
		}

		if len(req.BookingReferenceNumber) > 0 {
			OnlineSessionAtt.BookingReferenceNumber = req.BookingReferenceNumber
		} else {

			var bRefNo string
			bookingGetData := &model.AllBooking{
				UserID:          req.UserId,
				OnlineSessionID: req.OnlineSessionId,
			}

			var allBooking []model.AllBooking

			if err := s.Data.BookingDetailsGet(ctx, bookingGetData, &allBooking, 0, 1, 1); err != nil {
				return err
			}

			for _, aBooking := range allBooking {
				bRefNo = aBooking.BookingReferenceNumber
				break
			}

			OnlineSessionAtt.BookingReferenceNumber = bRefNo
		}

		if req.Duration <= 0 {
			OnlineSessionAtt.Duration = int32(req.EndTimestamp - req.StartTimestamp)
		} else {
			OnlineSessionAtt.Duration = req.Duration
		}

		OnlineSessionAtt.VideoEntrySecond = req.VideoEntrySecond
		OnlineSessionAtt.VideoExitSecond = req.VideoExitSecond
		OnlineSessionAtt.BufferingDuration = req.BufferingDuration
		OnlineSessionAtt.IsEntry = req.IsEntry
		OnlineSessionAtt.AppType = req.AppType
		OnlineSessionAtt.AppVersion = req.AppVersion

		var success bool
		if err2 := s.Data.SaveOnlineSessionAttendance(&OnlineSessionAtt, &success); err2 != nil {
			return err2
		}

		if len(OnlineSessionAtt.BookingReferenceNumber) > 0 {
			var success_status bool
			bRefNo := OnlineSessionAtt.BookingReferenceNumber
			go s.Data.UpdateAttendanceFlagForOnlineSessions(bRefNo, &success_status)
		}

		if success == true {
			status := &pb.Status{
				Status:  "success",
				Message: "Created",
			}
			res.Status = status
		} else {
			status := &pb.Status{
				Status:  "failure",
				Message: "row creation failed",
			}
			res.Status = status
		}
	} else if req.VideoExitSecond < 0 {
		status := &pb.Status{
			Status:  "failure",
			Message: "video not started yet.",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "UserId/SessionId not available",
		}
		res.Status = status
	}

	return nil
}

func (s *Service) UserQueryNoShowRevert(ctx context.Context, req *pb.UserBookingRequestQueries, res *pb.Ack) error {
	loggedInUser := util.GetUserIDFromContext(ctx)
	storeUserQueryData := &model.UserBookingRequestQueries{
		BookingReferenceNumber: req.BookingReferenceNumber,
		BookingId:              req.BookingId,
		ReasonId:               req.ReasonId,
		QueryTypeId:            req.QueryTypeId,
		OtherReason:            req.OtherReason,
	}
	if req.BookingId > 0 && req.ReasonId > 0 {
		bookingInfo, err := s.Data.GetBookingDetailsByBookingID(ctx, req.BookingId)
		if err != nil {
			log.Println("error in validating no show user query ---- ", err)
			return err
		}
		if loggedInUser > 0 && bookingInfo.UserID != loggedInUser && bookingInfo.CreatedBy != loggedInUser {
			status := &pb.Status{
				Status:  notAuthorized,
				Message: "Not authorized",
			}
			res.Status = status
			return nil
		}
		if err := s.Data.StoreUserQuery(ctx, storeUserQueryData); err != nil {
			log.Println("error in storing User Query ---- ", err)
			return nil
		}
		go s.Data.NoShowRevertQueue(ctx, req)

		status := &pb.Status{
			Status:  "success",
			Message: "Query raised successfully",
		}
		res.Status = status
	} else {
		status := &pb.Status{
			Status:  "failure",
			Message: "Query not raised successfully",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) NoShowUserQueryCheck(ctx context.Context, req *pb.UserBookingRequestQueries) error {

	var record []structs.NoShowRevertTask

	var bookingId []int32
	bookingId = append(bookingId, req.BookingId)

	if err := s.Data.NoShowRecordForRevert(&record, bookingId); err != nil {
		log.Println("error in getting NoShow record ---- ", err)
		return nil
	}

	if len(record) > 0 && (record[0].UserId > 0 && record[0].SubscriptionId > 0) {

		updateQueryStaus := model.UserBookingRequestQueries{
			BookingReferenceNumber: req.BookingReferenceNumber,
			BookingId:              req.BookingId,
			Status:                 1,
		}
		var userReasonInfo pb.UserBookingRequestQueries
		if err := s.Data.GetReasonInfo(ctx, req.ReasonId, &userReasonInfo); err != nil {
			log.Println("error in getting reason info", err)
			return nil
		}

		var applicableForNoShowRevert bool
		if err := s.Data.CheckIfNoShowRevertIsApplicable(ctx, req.BookingId, &applicableForNoShowRevert); err != nil {
			log.Println("func:NoShowUserQueryCheck Error in getting reason info err:", err)
			return nil
		}
		if applicableForNoShowRevert == false {
			log.Println("func:NoShowUserQueryCheck, no show revert is not applicable for booking_id:%d", req.BookingId)
			return nil
		}

		pcl = util.GetProductClient()

		subDataRequest := &productPB.GetSubscriptionDetailsRequest{
			SubscriptionId: record[0].SubscriptionId,
		}

		subData, err := pcl.GetSubscriptionDetails(ctx, subDataRequest)
		if err != nil {
			log.Println("Error in fetching subscription details", err)
			return fmt.Errorf("Error in fetching subscription details for subsId:%d, Error: %v", record[0].SubscriptionId, err)
		}

		subscriptionStartDate := time.Unix(subData.Subscription[0].StartDate.Seconds, 0)

		var userRevertCount int32

		if userReasonInfo.ReasonId == 13 { // 13 - Others
			// if 'others' is selected in the reason dropdown, then we need to send CX Team mail for revert
			s.SendCxTeamMailForRevert(ctx, req, record[0])

		} else if userReasonInfo.CxTicketFlag == 0 {
			if err := s.Data.UserNoShowRevertedInMonth(ctx, record[0], userReasonInfo.AutoRevertFlag, subscriptionStartDate, &userRevertCount); err != nil {
				log.Println("error in getting NoShow revert count of User---- ", err)
				return nil
			}

			if userRevertCount >= 2 {
				if err := s.Data.StoreUserQuery(ctx, &updateQueryStaus); err != nil {
					log.Println("error in closing User Query", err)
					return nil
				}
				title := "1-day Penalty cannot be reversed"
				text := "Your 1 day penalty cannot be reversed. Please mark your attendance within 15 mins or cancel your booking if not attending not later than 30 mins of your session start time in future."
				s.NoShowAutoRevertNotification(req, record, title, text, false)
			} else {
				s.SendCxTeamMailForRevert(ctx, req, record[0])
			}
		} else {
			s.SendCxTeamMailForRevert(ctx, req, record[0])
		}
	}
	return nil
}

func (s *Service) NoShowAutoRevertNotification(req *pb.UserBookingRequestQueries, record []structs.NoShowRevertTask, title string, text string, isReverted bool) error {

	app_condition := make(map[string]string)

	app_condition["sports-ios"] = "1.0.0"
	app_condition["sports-android"] = "1.0.0"

	condition_data := make(map[string]interface{})
	condition_data["platform"] = app_condition

	curTime := time.Now()

	//notification exp time
	defExpTime := config.Get("static", "default_notification_expiry_time_in_hours").Int(4)
	expTime := curTime.AddDate(0, 0, defExpTime)
	expTimestamp := expTime.Unix()

	usersIds := make([]int32, 0)
	usersIds = append(usersIds, record[0].UserId)
	subCategory := int32(notificationPB.NotificationSubCategories_NOSHOW_NOT_REVERTED)
	if isReverted {
		subCategory = int32(notificationPB.NotificationSubCategories_NOSHOW_REVERTED)
	}
	nData := structs.FcmNotification{
		Title:         title,
		Text:          text,
		UserIds:       usersIds,
		Id:            req.BookingId,
		Action:        "past_bookings",
		ConditionData: condition_data,
		CtaText:       req.BookingReferenceNumber,
		ExpiryDate:    expTimestamp,
		SubCategory:   subCategory,
	}
	s.Data.SendNotification(&nData)
	return nil
}

func (s *Service) NoShowRevertUserQuery(req *pb.UserBookingRequestQueries, record []structs.NoShowRevertTask, updateQueryStaus *model.UserBookingRequestQueries) error {

	jobs := make(chan structs.NoShowRevertTask, 1)
	errors := make(chan error, 1)
	var wg sync.WaitGroup
	go s.worker(1, &wg, jobs, errors)

	for _, ele := range record {
		ele.Reason = "Auto Revert"
		jobs <- ele
		wg.Add(1)
	}
	close(jobs)
	wg.Wait()
	select {
	case err := <-errors:
		log.Println("finished with error:", err.Error())

	}
	title := "1-day Penalty has been reversed"
	text := "Your 1 day penalty has been reversed. Please mark your attendance within 15 mins or cancel your booking if not attending not later than 30 mins of your session start time in future."
	s.NoShowAutoRevertNotification(req, record, title, text, true)
	return nil

}

func (s *Service) SendCxTeamMailForRevert(ctx context.Context, req *pb.UserBookingRequestQueries, record structs.NoShowRevertTask) error {

	var bookingAndAttendanceDetails structs.BookingAndAttendanceDetails
	if err := s.Data.BookingAndAttendanceDetails(&bookingAndAttendanceDetails, req.BookingId); err != nil {
		fmt.Println("Error in getting booking details with booking reference number ----", err)
		return nil
	}

	var userReasonInfo pb.UserBookingRequestQueries
	if err := s.Data.GetReasonInfo(ctx, req.ReasonId, &userReasonInfo); err != nil {
		log.Println("error in getting reason info", err)
		return nil
	}

	var userPastQueries []model.UserBookingRequestQueries
	if err := s.Data.UserNoShowPenaltyPastBuffer(ctx, record, &userPastQueries); err != nil {
		log.Println("error in getting reason info", err)
		return nil
	}

	isLateComer := false
	if strings.Compare("0001-01-01 00:00:00", bookingAndAttendanceDetails.CheckinTime.Format(formatYMDHIS)) != 0 && bookingAndAttendanceDetails.Medium == 1 {
		isLateComer = true
	}
	var subject string
	if isLateComer == true {
		subject = "Reversal dispute request | Late Entry by " + bookingAndAttendanceDetails.Name + " at " + bookingAndAttendanceDetails.DisplayName + " for BRN : " + req.BookingReferenceNumber
	} else {
		subject = "Reversal dispute request | No Show by " + bookingAndAttendanceDetails.Name + " at " + bookingAndAttendanceDetails.DisplayName + " for BRN : " + req.BookingReferenceNumber
	}
	message := "Dear Team,<br><br>"
	if isLateComer {
		message += "Penalty reversal request raised for Late Entry under reason: <b>" + userReasonInfo.Description + "</b>"
	} else {
		message += "Penalty reversal request raised for No Show under reason: <b>" + userReasonInfo.Description + "</b>"
	}
	message += "<br><br><u>Member Details:</u><br>"
	message += "<br>Registered User ID: <b>" + strconv.Itoa(int(bookingAndAttendanceDetails.UserId)) + "</b>"
	message += "<br>Registered Email : <b>" + bookingAndAttendanceDetails.Email + "</b>"
	message += "<br>Registered Phone : <b>" + bookingAndAttendanceDetails.Phone + "</b>"
	if len(req.OtherReason) > 0 {
		message += "<br>Other reason : <b>" + req.OtherReason + "</b>"
	}

	message += "<br><br><u>Session Details: </u><br>"
	message += "<br>Booking Id: <b>" + strconv.Itoa(int(req.BookingId)) + "</b>"
	message += "<br>Slot Timing: <b>" + bookingAndAttendanceDetails.Timing + "</b>"
	message += "<br>Booking Date: <b>" + bookingAndAttendanceDetails.BookingDate.Format(formatYMDHIS) + "</b>"
	message += "<br> Facility Sport: <b>" + bookingAndAttendanceDetails.FacilitySportDescription + "</b>"
	if isLateComer {
		message += "<br>Entry time as per Attendance System : <b>" + bookingAndAttendanceDetails.CheckinTime.Format(formatYMDHIS) + "</b><br><br>"
	}
	message += "<br><b> Past Penalties </b><br>"
	if len(userPastQueries) > 0 {
		for i, ele := range userPastQueries {
			message += strconv.Itoa(int(i+1)) + ". <b>No Show date -</b> " + ele.CreatedAt.Format(formatYMDHIS) + "<br><b> Reverted Reason - </b>" + ele.Reason + "<br><b>User Reason - </b> " + ele.Description
			if len(ele.OtherReason) > 0 {
				message += " - " + ele.OtherReason
			}
			message += "<br><br>"
			if i == 9 {
				break
			}
		}
	}
	message += "<br><br>Regards,<br>"
	message += "Fitso Tech Team<br>"

	ucl := util.GetUserClient()

	freshdeskTicketPayload := &userPB.FreshdeskTicket{
		UserId:      bookingAndAttendanceDetails.UserId,
		Subject:     subject,
		Description: message,
		Name:        bookingAndAttendanceDetails.Name,
		Email:       bookingAndAttendanceDetails.Email,
		Phone:       bookingAndAttendanceDetails.Phone,
	}

	_, err := ucl.CreateFreshdeskTicket(ctx, freshdeskTicketPayload)
	if err != nil {
		log.Printf("Error in generating freshdesk ticket for SupportQuery, UserId:%d, err: %v", bookingAndAttendanceDetails.UserId, err)
		return err
	}

	return nil
}

func (s *Service) AddFacilityVisitedCount(ctx context.Context, req *pb.Booking, res *pb.Ack) error {

	var visitedPeopleData []model.FacilityVisitedCounts

	if err := s.Data.FacilityVisitedCountGet(ctx, &visitedPeopleData, req.NumDays); err != nil {
		log.Println("Error in getting people count who have visited recently... ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get count of people visited recently..",
		}
		res.Status = status
		return err
	}
	if err := s.createOrUpdateData(ctx, visitedPeopleData); err != nil {
		log.Println("Error... ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not update data..",
		}
		res.Status = status
		return err
	}
	return nil
}

func (s *Service) createOrUpdateData(ctx context.Context, visitedPeopleData []model.FacilityVisitedCounts) error {

	for i, _ := range visitedPeopleData {
		newEntry := &model.FacilityVisitedCounts{
			PeopleCount: visitedPeopleData[i].PeopleCount,
			FacilityID:  visitedPeopleData[i].FacilityID,
			Days:        visitedPeopleData[i].Days,
		}
		if err := s.Data.AddFacilityVisitedCount(ctx, newEntry); err != nil {
			log.Println("Error in inserting... ", err)
			return err
		}
	}
	return nil
}

func (s *Service) FetchFacilityVisitCount(ctx context.Context, req *pb.Booking, res *pb.Booking) error {

	var countByFacilityId model.FacilityVisitedCounts
	if err := s.Data.FetchCountByFacilityId(req.FacilityId, req.NumDays, &countByFacilityId); err != nil {
		log.Println("Error in getting count of facility id:....", req.FacilityId, "Error: ", err)
		return err
	}
	res.PeopleCount = countByFacilityId.PeopleCount
	return nil
}

func (s *Service) ComputeFacilityRatings(ctx context.Context, req *pb.Facility, res *pb.FacilityRatingsResponse) error {

	facilityObj := &model.FacilityRatings{
		FacilityId: req.FacilityId,
	}
	var rating float64
	if err := s.Data.GetRatingsFacilityWise(ctx, &rating, facilityObj); err != nil {
		log.Println("Error in getting facility wise ratings... ", err)
		status := &pb.Status{
			Status:  "failure",
			Message: "Could not get ratings facility wise..",
		}
		res.Status = status
		return nil
	}
	res.Rating = rating
	status := &pb.Status{
		Status:  "success",
		Message: "Fetched facility wise ratings successfully !..",
	}
	res.Status = status
	return nil

}

func (s *Service) GetFeedbackOptionsV2(ctx context.Context, req *pb.BookingFeedbackRequest, res *pb.BookingDetailsResponse) error {

	userID := util.GetUserIDFromContext(ctx)
	if userID == 0 {
		res.Status = &pb.Status{
			Message: "Invalid user id",
			Status:  badRequest,
		}
		return nil
	}

	booking, err := s.Data.GetBookingDetailsByBookingID(ctx, req.BookingId)
	if err != nil {
		log.Printf("Unable to get booking details for bookingId: %d, Error : %v", req.BookingId, err)
		return err
	}
	if booking.BookingID == 0 {
		res.Status = &pb.Status{
			Message: "No bookings found",
			Status:  badRequest,
		}
		return nil
	}
	if userID != booking.UserID && userID != booking.CreatedBy {
		res.Status = &pb.Status{
			Message: "Invalid user id",
			Status:  badRequest,
		}
		return nil
	}

	bookingGetData := &model.AllBooking{
		ProductCategoryId: AcademyCategoryID,
		BookingID:         req.BookingId,
	}
	var allBooking []model.AllBooking
	var academyTags bool
	if err := s.Data.BookingDetailsGetV4(ctx, bookingGetData, &allBooking, 0); err != nil {
		log.Println("Error in getting booking data for Id %d, err %v", req.BookingId, err)
		return fmt.Errorf("Error in getting booking data for Id %d, err %v", req.BookingId, err)
	}
	if len(allBooking) > 0 {
		academyTags = true
	}
	log.Printf("GetFeedbackOptionsV2: Error Provider: City id - %d", util.GetCityIDFromContext(ctx))
	fsDetailsRequest := &facilitySportPB.FacilitySportsByFsIdRequest{
		FsIds: []int32{booking.FSID},
	}
	facilityClient := util.GetFacilitySportClient()
	fsDetailsResponse, err := facilityClient.GetFacilitySportsDetailsByFsID(ctx, fsDetailsRequest)
	if err != nil {
		log.Printf("Unable to fetch fs details for fs id %d, error %v", booking.FSID, err)
		return err
	}
	if len(fsDetailsResponse.FacilitySports) == 0 {
		errStr := fmt.Sprintf("No facility sport found for given fs id %d", booking.FSID)
		log.Println(errStr)
		res.Status = &pb.Status{
			Message: errStr,
			Status:  badRequest,
		}
		return nil
	}

	userFeedback, err := s.Data.GetUserFeedbackDetails(ctx, []int32{req.BookingId})
	if err != nil {
		log.Printf("Could not fetch feedback for booking id %d, Error: %v", req.BookingId, err)
		return err
	}

	var ratingsForFeedback []*pb.SportsFeedbackRating
	if err := s.GetSportsFeedbackRatingOptions(ctx, &ratingsForFeedback, false, academyTags); err != nil {
		log.Println("Could not fetch feedback options!")
		return err
	}
	res.Facility = &pb.Facility{
		FacilityId:     fsDetailsResponse.FacilitySports[0].Facility[0].FacilityId,
		DisplayName:    fsDetailsResponse.FacilitySports[0].Facility[0].DisplayName,
		DisplayAddress: fsDetailsResponse.FacilitySports[0].Facility[0].DisplayAddress,
	}
	res.Sport = &pb.Sport{
		SportId:   fsDetailsResponse.FacilitySports[0].Sport[0].SportId,
		SportName: fsDetailsResponse.FacilitySports[0].Sport[0].SportName,
		Icon:      fsDetailsResponse.FacilitySports[0].Sport[0].Icon,
	}
	res.Bookings = make(map[int32]*pb.Booking)
	bookingTime, _ := ptypes.TimestampProto(booking.BookingTime)
	res.Bookings[0] = &pb.Booking{
		BookingId:              booking.BookingID,
		UserId:                 booking.UserID,
		BookingReferenceNumber: booking.BookingReferenceNumber,
		BookingTime:            bookingTime,
		CreatedBy:              booking.CreatedBy,
		CancelBy:               booking.CancelBy,
	}

	feedbackSelectdOptionsMap := make(map[int32]bool)
	for _, ele := range userFeedback {
		if len(ele.Title) > 0 {
			feedbackSelectdOptionsMap[ele.FeedbackOptionId] = true
		}
	}
	if len(userFeedback) > 0 {
		res.UserFeedback = &pb.SportsFeedbackUserRating{
			RatingId:               userFeedback[0].RatingId,
			Note:                   userFeedback[0].Note,
			FeedbackOptionSelected: feedbackSelectdOptionsMap,
		}
	} else {
		res.UserFeedback = &pb.SportsFeedbackUserRating{
			RatingId:               0,
			Note:                   "",
			FeedbackOptionSelected: feedbackSelectdOptionsMap,
		}
	}
	var ratings []*pb.SportsFeedbackRating
	for _, ele := range ratingsForFeedback {
		ratingFeedbacks := &pb.SportsFeedbackRating{
			RatingId:        ele.RatingId,
			Question:        ele.Question,
			HighlightText:   ele.HighlightText,
			CategoryOptions: ele.CategoryOptions,
		}
		ratings = append(ratings, ratingFeedbacks)
	}
	res.Ratings = ratings

	status := &pb.Status{
		Status:  success,
		Message: "Fetched facility wise ratings successfully !..",
	}
	res.Status = status
	return nil

}

func (s *Service) GetUserBookingsCount(ctx context.Context, req *pb.GetUserBookingsCountForProductIdRequest, res *pb.GetUserBookingsCountForProductIdResponse) error {
	condition := model.AllBooking{
		UserID:    req.UserId,
		ProductID: req.ProductId,
	}

	if req.AttendanceFlag == true {
		condition.AttendanceFlag = true
	}
	count, err := s.Data.GetUserBookingsCount(ctx, condition)
	if err != nil {
		log.Println("Error in GetUserBookingsCount, Error : %v", err)
		return err
	}
	res.NumBookings = count
	return nil
}

func (s *Service) GetSportWiseUserBookingCount(ctx context.Context, req *pb.Booking, res *pb.UsersAllBookingByProduct) error {
	condition := &model.AllBooking{
		UserID:    req.UserId,
		ProductID: req.ProductId,
	}
	var bookingCountBySport []model.AllBooking
	if err := s.Data.BookingCountBySport(ctx, condition, &bookingCountBySport); err != nil {
		log.Printf("Error in handler GetSportWiseUserBookingCount: booking count for userID %d, Error: %v", req.UserId, err)
		return err
	}
	for _, data := range bookingCountBySport {
		bookingCount := &pb.BookingCountBySport{
			SportId:   data.SportID,
			Count:     data.Count,
			SportName: data.SportName,
		}
		res.Data = append(res.Data, bookingCount)
	}
	return nil
}

func (s *Service) GetTrialAttendedUsersOfLastNthDay(ctx context.Context, req *pb.GetTrialAttendedUsersOfLastNthDayReq, res *pb.GetTrialAttendedUsersOfLastNthDayRes) error {

	bookings, err := s.Data.GetTrialAttendedBookingsOfLastNthDay(ctx, req.NthDay)
	if err != nil {
		log.Printf("func:GetTrialAttendedUsersOfLastNthDay, unable to get last %d nd trial attended bookings, error: %v", req.NthDay, err)
		return err
	}

	for _, val := range bookings {
		res.UserIds = append(res.UserIds, val.UserID)
	}

	return nil
}

func (s *Service) AddEditCapacitySlots(ctx context.Context, req *pb.AddEditCapacitySlotRequest, res *pb.Ack) error {
	if len(req.SlotIds) == 0 || len(req.PaIds) == 0 || req.FsId == 0 || req.ProductArenaCategoryId == 0 || len(req.DayOfWeeks) == 0 || (req.WaitlistFlag <= 0 && req.CapacityWaitlist > 0) || (req.WaitlistFlag >= 1 && req.CapacityWaitlist <= 0) {
		status := &pb.Status{
			Status:  failed,
			Message: "Fill all fields",
		}
		res.Status = status
		return nil
	}
	if err := s.Data.AddEditPlayArenaCapacitySlots(ctx, req); err != nil {
		log.Printf("Error in handler AddEditCapacitySlots: for FsId: %d, ProductArenaCategoryId: %d, Error: %v", req.FsId, req.ProductArenaCategoryId, err)
		return err
	}
	status := &pb.Status{
		Status:  success,
		Message: "Updated successfully",
	}
	res.Status = status
	return nil
}

func (s *Service) GetBookingDetailsByID(ctx context.Context, req *pb.Booking, res *pb.Booking) error {
	allBooking, err := s.Data.GetBookingDetailsByBookingID(ctx, req.BookingId)

	if err != nil {
		log.Printf("error in GetBookingDetailsByID: unable to get booking data for id: %d", req.BookingId)
		return fmt.Errorf("error in GetBookingDetailsByID: unable to get booking data for id: %d", req.BookingId)
	}

	res.BookingId = allBooking.BookingID
	res.UserId = allBooking.UserID
	res.FsId = allBooking.FSID
	res.BookingCancelled = allBooking.BookingCancelled
	bookingTime, _ := ptypes.TimestampProto(allBooking.BookingTime)
	res.BookingTime = bookingTime
	res.SubscriptionId = allBooking.SubscriptionID

	return nil
}

func (s *Service) GetSlotsMapInfo(ctx context.Context) map[int32]structs.Slot {
	slotsMapInfo := make(map[int32]structs.Slot)

	var slotData []structs.Slot
	if err := s.Data.GetAllSlots(ctx, &slotData); err != nil {
		log.Println("func:GetSlotsMapInfo, error in getting all slots..", err)
		return slotsMapInfo
	}

	for _, val := range slotData {
		slotsMapInfo[val.SlotID] = val
	}

	return slotsMapInfo
}

func (s *Service) GetTrialsOfUserByProductCategory(ctx context.Context, req *pb.TrialsOfUserByCityRequest, res *pb.BookingResponse) error {
	conditionAcademy := &model.AllBooking{
		UserIds:            []int32{req.UserId},
		ProductCategoryIds: req.ProductCategoryIds,
	}
	trialBookings, err := s.Data.GetTrialBookingsForGivenCity(ctx, conditionAcademy)
	if err != nil {
		return fmt.Errorf("Unable to get trial bookings for userId %d, %v", req.UserId, err)
	}

	for _, booking := range trialBookings {
		res.Booking = append(res.Booking, booking.Proto())
	}
	return nil
}

func (s *Service) SendOverbookingAlertMail(ctx context.Context, req *pb.Empty, res *pb.Ack) error {
	var overbookingData []structs.OverbookingData
	if err := s.Data.SendOverbookingAlertMail(ctx, &overbookingData); err != nil {
		log.Println("Error in SendOverbookingAlertMail, Error : %v", err)
		return err
	}
	if len(overbookingData) > 0 {
		subject := "Overbooking alert" + " " + time.Now().Format("2006-01-02 15:04:05")
		message := "Please find the details of overbooking centers below:<br><br>"
		for _, val := range overbookingData {
			message += "Fs Id: " + strconv.Itoa(int(val.FsId)) + "<br>"
			message += "Facility Sport: " + val.FacilitySport + "<br>"
			message += "SlotId: " + strconv.Itoa(int(val.SlotId)) + "<br>"
			message += "Slot Timing: " + val.Timing + "<br>"
			message += "Booking Date: " + val.BookingDate.Format("2006-01-02") + "<br>"
			message += "BookedCount: " + strconv.Itoa(int(val.BookedCount)) + "<br>"
			message += "Capacity: " + strconv.Itoa(int(val.Capacity)) + "<br>"

			message += "<br><br>"
		}

		fromEm := &structs.Email{
			Name:         "Fitso Bot",
			EmailAddress: "<EMAIL>",
		}
		var names []string
		var mailIds []string

		names = []string{"Sushmita", "Rishabh Jain", "Rahul Garg", "Ujjwal Mishra", "Akshit Chaudhary"}
		mailIds = []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}

		var toEmails []*structs.Email
		for i, id := range mailIds {
			toEmail := &structs.Email{
				Name:         names[i],
				EmailAddress: id,
			}
			toEmails = append(toEmails, toEmail)
		}
		emailReq := &structs.EmailRequest{
			From:    fromEm,
			To:      toEmails,
			Subject: subject,
			Message: message,
		}
		if err := s.Data.SendEmail(emailReq); err != nil {
			log.Println("Error in SendOverbookingAlertMail, Error : %v", err)
			return err
		}
	}
	return nil
}

func (s *Service) sendNoShowRevertEventToSqs(ctx context.Context, bookingId int32, reason string, revertedBy int32) error {
	bookingDetails, err := s.Data.GetBookingDetailsByBookingID(ctx, bookingId)
	if err != nil {
		log.Printf("sendNoShowRevertEventToSqs: error in getting booking details for booking id: %d, error %v", bookingId, err)
		return err
	}
	if bookingDetails.SubscriptionID > 0 {
		pcl = util.GetProductClient()

		subDataRequest := &productPB.GetSubscriptionDetailsRequest{
			SubscriptionId: bookingDetails.SubscriptionID,
		}

		subData, err := pcl.GetSubscriptionDetails(ctx, subDataRequest)
		if err != nil {
			log.Printf("sendNoShowRevertEventToSqs: Error in fetching subscription details for subsId:%d, err: %v", bookingDetails.SubscriptionID, err)
			return fmt.Errorf("Error in fetching subscription details for subsId:%d, Error: %v", bookingDetails.SubscriptionID, err)
		}

		if len(subData.Subscription) <= 0 {
			log.Printf("sendNoShowRevertEventToSqs: No subscription details found for subsId:%d, err: %v", bookingDetails.SubscriptionID, err)
			return fmt.Errorf("No subscription details for subsId:%d, Error: %v", bookingDetails.SubscriptionID, err)
		}
		bookingDetails.CultMembershipId = subData.Subscription[0].CultMembershipId
	}
	if bookingDetails.CultMembershipId <= 0 {
		log.Printf("sendNoShowRevertEventToSqs: not publishing no show revert since no cult membership found for booking id: %d, error %v", bookingId)
		return nil
	}
	noShowRevertEventRequest := &pb.PublishBookingUpdateMessageToSQSReq{
		BookingId:         bookingId,
		UserId:            bookingDetails.UserID,
		CultMembershipId:  bookingDetails.CultMembershipId,
		DurationInSeconds: 24 * 60 * 60,
		Reason:            fmt.Sprintf("NO_SHOW Penalty Revert|%s:%s", bookingDetails.BookingReferenceNumber, reason),
		EventName:         NO_SHOW_PENALTY_REVERT_EVENT,
		FsId:              bookingDetails.FSID,
		RevertedBy:        revertedBy,
	}
	if !bookingDetails.BookingTime.IsZero() {
		// sending date-time in the string format 2009-11-10 23:00:00 +0000 UTC
		noShowRevertEventRequest.BookingTime = bookingDetails.BookingTime.String()[:29]
	}
	if err := s.PublishBookingUpdateMessageToSQS(ctx, noShowRevertEventRequest, &pb.Ack{}); err != nil {
		log.Printf("sendNoShowRevertEventToSqs: error in publishing no show revert event to sqs for booking id: %d, error %v", bookingId, err)
		return err
	}
	return nil
}

func (s *Service) PublishBookingUpdateMessageToSQS(ctx context.Context, req *pb.PublishBookingUpdateMessageToSQSReq, res *pb.Ack) error {
	if req.CultMembershipId <= 0 {
		errMsg := fmt.Sprintf("No Cult membership found for the user id: %d", req.UserId)
		log.Println("PublishBookingUpdateMessageToSQS, err: ", errMsg)
		return fmt.Errorf(errMsg)
	}
	messageData := &structs.BookingEventMessageToSQS{
		BookingId:         req.BookingId,
		UserId:            req.UserId,
		CultMembershipId:  req.CultMembershipId,
		DurationInSeconds: req.DurationInSeconds,
		Reason:            req.Reason,
		EventName:         req.EventName,
		FsId:              req.FsId,
		BookingTime:       req.BookingTime,
		IsPenaltyApplied:  req.IsPenaltyApplied,
		IsDroppedOut:      req.IsDroppedOut,
	}
	if req.RevertedBy > 0 {
		messageData.RevertedBy = req.RevertedBy
	}
	if err := s.Data.PublishBookingUpdateMessageToSQS(messageData); err != nil {
		errMsg := fmt.Sprintf("Error in publishing booking update event for data: %v with err: %v", messageData, err)
		log.Printf("PublishBookingUpdateMessageToSQS, err: %s", errMsg)
		return fmt.Errorf(errMsg)
	}
	return nil
}

func (s *Service) publishSlotClosureEventToSQS(ctx context.Context, slotClosureData []structs.FsPaSlotDate) error {
	log.Printf("publishSlotClosureEventToSQS: publishing slot closure event to sqs for slotClosureData: %v", slotClosureData)
	for _, element := range slotClosureData {
		if element.Date > 0 {
			bookingDate := time.Unix(element.Date, 0)
			messageData := &structs.SlotClosureSQSEvent{
				PACSID: element.PacsId,
			}
			messageData.BookingDate = bookingDate.Format(formatYMD)
			if err := s.Data.PublishSlotClosureEventToSQS(messageData); err != nil {
				s.sendMailForPublishSlotClosureEventToSQSFailure(ctx, messageData, err)
				return err
			}
		}
	}
	log.Println("publishSlotClosureEventToSQS: published successfully")
	return nil
}

func (s *Service) sendMailForPublishSlotClosureEventToSQSFailure(ctx context.Context, element *structs.SlotClosureSQSEvent, procErr error) error {
	toEmails := [1]string{TECH_MAIL}

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	messageStr := "For following slot Closure details, publishing slot closure event to the queue failed:<br><br>"
	messageStr += "Pacs Id - " + strconv.Itoa(int(element.PACSID)) + "<br>"
	messageStr += "Date - " + element.BookingDate + "<br>"

	if procErr != nil {
		messageStr += "Error - " + fmt.Sprintf("%v", procErr) + "<br><br>"
	}

	emailRequest := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: "Publish Slot Closure Event Failure",
		Message: messageStr,
	}

	if err := s.Data.SendEmail(emailRequest); err != nil {
		log.Println("Error in sending mail for publish membership update failure for data: %v, err: %v", element, err)
		return err
	}

	return nil
}

func (s *Service) sendMailForSlotPopulationFailure(ctx context.Context, isSlotPopulatedForTomorrow bool, isSlotPopulatedForDayAfterTomorrow bool) error {
	toEmails := [1]string{TECH_MAIL}

	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	var toEm []*structs.Email

	for _, emailId := range toEmails {
		toEmVal := &structs.Email{
			EmailAddress: emailId,
		}
		toEm = append(toEm, toEmVal)
	}

	messageStr := "For following Dates, slot population failed:<br>"
	if !isSlotPopulatedForTomorrow {
		messageStr += "Date - " + time.Now().AddDate(0, 0, 1).Format(formatYMD) + "<br>"
	}
	if !isSlotPopulatedForDayAfterTomorrow {
		messageStr += "Date - " + time.Now().AddDate(0, 0, 2).Format(formatYMD) + "<br><br>"
	}
	messageStr += "Please resolve this on priority!<br><br>"

	emailRequest := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: "ALERT! Slot Population Failure",
		Message: messageStr,
	}

	if err := s.Data.SendEmail(emailRequest); err != nil {
		log.Println("sendMailForSlotPopulationFailure: Error in sending mail for slot population failure: %v", err)
		return err
	}

	return nil
}

func (s *Service) ReduceSummercampSlotCapacity(ctx context.Context, req *pb.UpdateSummercampSlotRequest, res *pb.Ack) error {

	if req.SummercampSlotId <= 0 {
		status := &pb.Status{
			Status:  badRequest,
			Message: "invalid request",
		}
		res.Status = status
		return nil
	}

	condition := &model.SummercampSlot{
		Id: req.SummercampSlotId,
	}
	summercampSlots, err := s.Data.GetSummercampSlots(ctx, condition)
	if err != nil || len(summercampSlots) <= 0 {
		errMsg := fmt.Sprintf("ReduceSummercampSlotCapacity: error in getting data for summer camp slots for request: %v, error: %v", condition, err)
		log.Println("ReduceSummercampSlotCapacity: ", errMsg)
		return fmt.Errorf(errMsg)
	}
	summercampSlotData := summercampSlots[0]
	if summercampSlotData.ActiveSlotsApplicable && req.CheckActiveSlotsApplicable {
		return nil
	}

	pcl := util.GetProductClient()
	reqData := &productPB.RelatedSummerCampProductsReq{
		FsId:                       summercampSlotData.FsId,
		SummercampProductMappingId: summercampSlotData.SummercampProductMappingId,
		SlotId:                     summercampSlotData.SlotId,
	}
	relatedSummercampProducts, err := pcl.GetRelatedSummerCampProducts(ctx, reqData)
	if err != nil {
		log.Printf("ReduceSummercampSlotCapacity, error in getting related summer-camp product mappings for reqData: %v, err: %v", reqData, err)
		return fmt.Errorf("Error in getting related summer-camp product mappings data for Fs Id: %d, Slot Id: %d, summer camp product mapping id: %d", reqData.FsId, reqData.SlotId, reqData.SummercampProductMappingId)
	}

	var summercampProductMappingIds []int32
	for summercampProductMappingId, _ := range relatedSummercampProducts.SummercampProductMappingsData {
		summercampProductMappingIds = append(summercampProductMappingIds, summercampProductMappingId)
	}

	if len(summercampProductMappingIds) == 0 {
		errMsg := fmt.Sprintf("ReduceSummercampSlotCapacity: error in getting related slots data for summer camp slot id: %d, error: %v", req.SummercampSlotId, err)
		log.Println("ReduceSummercampSlotCapacity: ", errMsg)
		return fmt.Errorf(errMsg)
	}

	if err := s.Data.BulkReduceSummerCampSlotCapacity(ctx, summercampSlotData.FsId, summercampSlotData.SlotId, summercampProductMappingIds); err != nil {
		log.Printf("ReduceSummercampSlotCapacity: Error in BulkReduceSummerCampSlotCapacity for fs id: %d, slot id: %d, summercampProductMappingIds: %v, error: %v", summercampSlotData.FsId, summercampSlotData.SlotId, summercampProductMappingIds, err)
		return err
	}

	status := &pb.Status{
		Status:  success,
		Message: "capacity reduced successfully",
	}
	res.Status = status

	return nil
}

func (s *Service) GetUserBookingDetailsByBookingId(ctx context.Context, req *pb.Booking, res *pb.BookingResponse) error {
	bookingDetails, err := s.Data.GetUserBookingDetailsByBookingId(ctx, req.BookingId)
	if err != nil {
		log.Printf("GetUserBookingDetailsByBookingId: error in getting booking details for booking id: %d, error %v", req.BookingId, err)
		return err
	}
	userData := &pb.User{
		UserId: bookingDetails.UserID,
		Name:   bookingDetails.UserName,
		Phone:  bookingDetails.Phone,
	}
	bookingDetails.User = userData

	res.Booking = append(res.Booking, bookingDetails.Proto())

	status := &pb.Status{
		Status:  "success",
		Message: "Bookings Fetched successfully!",
	}
	res.Status = status
	return nil
}
