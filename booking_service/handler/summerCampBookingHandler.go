package bookingHandler

import (
	"context"
	"fmt"
	"log"
	"time"

	"bitbucket.org/jogocoin/go_api/booking_service/structs"

	models "bitbucket.org/jogocoin/go_api/booking_service/data/model"
	util "bitbucket.org/jogocoin/go_api/booking_service/internal/util"
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	productPB "bitbucket.org/jogocoin/go_api/booking_service/proto/product"
)

func (s *Service) CreateSummerCampSubscriptionBookings(ctx context.Context, req *pb.CreateAcademySessionBookingsRequest, res *pb.Empty) error {
	log.Println("CreateSummerCampSubscriptionBookings: called with req: ", req)
	if len(req.TimeToProcess) == 0 {
		log.Printf("No weekdays available to populate academy session bookings")
		return nil
	}

	var slots []models.Slot
	slotCondition := &models.Slot{
		DeletedFlag: 0,
	}

	err := s.Data.SlotGet(ctx, slotCondition, &slots)
	if err != nil {
		log.Printf("Unable to get slot details, Err: %v", err)
		return fmt.Errorf("Unable to get slot details, Err: %v", err)
	}

	slotMap := make(map[int32]models.Slot)
	for _, v := range slots {
		slotMap[v.SlotID] = v
	}

	productClient := util.GetProductClient()
	fsDataMap := make(map[int32][]*structs.AcademyMemberBookingPayload)

	for _, t := range req.TimeToProcess {
		reqData := &productPB.GetDataToCreateAcademySessionBookingsRequest{
			TimeToProcess: t,
			UserId:        req.UserId,
		}
		res, err := productClient.GetDataToCreateSummerCampSessionBookings(ctx, reqData)
		if err != nil {
			log.Printf("CreateSummerCampSubscriptionBookings: error in fetching academy bookings data for req: %v, err: %v", reqData, err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, &structs.AcademyMemberBookingPayload{UserId: req.UserId})
			return err
		}

		for _, v := range res.Data {
			bookingDate := time.Unix(v.BookingDate, 0)
			slotElem := slotMap[v.SlotId]

			payload := &structs.AcademyMemberBookingPayload{
				SubscriptionId: v.SubscriptionId,
				ProductId:      v.ProductId,
				UserId:         v.UserId,
				PacsId:         v.PacsId,
				FsId:           v.FsId,
				SlotId:         v.SlotId,
				PurchaseId:     v.PurchaseId,
				BookingDate:    bookingDate,
				BookingTime:    bookingDate.Add(time.Hour*time.Duration(slotElem.StartHour) + time.Minute*time.Duration(slotElem.StartMin)),
			}
			fsDataMap[v.FsId] = append(fsDataMap[v.FsId], payload)
		}
	}

	for fsId, bookings := range fsDataMap {
		log.Printf("CreateSummerCampSubscriptionBookings: pushing to academy booking topic for fs id: %d with bookings: %v", fsId, bookings)
		payload := &structs.AcademyMemberBookingQueuePayload{
			FsId:     fsId,
			Bookings: bookings,
		}
		err := s.Data.EnqueueSummerCampMemberBookingCreationPayload(ctx, payload)
		if err != nil {
			log.Printf("Unable to enqueue academy session booking payload: %v, Err: %v", payload, err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), true, &structs.AcademyMemberBookingPayload{FsId: fsId})
			return fmt.Errorf("Unable to enqueue academy session booking payload, Err: %v", err)
		}
	}
	return nil
}

func (s *Service) PopulateSummerCampBookings(ctx context.Context, req *structs.AcademyMemberBookingQueuePayload) error {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[PANIC] func: PopulateSummerCampBookings, Error: %v", r)
		}
	}()

	fsId := req.FsId
	bookings := req.Bookings

	log.Printf("Calling PopulateSummerCampBookings  for fsId: %d with booking count %d", fsId, len(bookings))

	var facIdStruct structs.FacilitySportIds
	if err := s.Data.GetFacilityIdAndSportIdByFsId(&fsId, &facIdStruct); err != nil {
		log.Printf("Error in getting facility_id and sport id for fsId: %d, Err: %v", fsId, err)
		go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), true, &structs.AcademyMemberBookingPayload{FsId: fsId})
		return err
	}

	inactiveDaysCondition := structs.InactiveDaysCondition{
		FsId:       fsId,
		FacilityId: facIdStruct.FacilityID,
	}

	var inactiveDays []models.SportsInactiveDay
	if err := s.Data.GetUpcomingInactiveDays(&inactiveDays, &inactiveDaysCondition); err != nil {
		log.Printf("Error in getting upcoming inactive days for fsId: %d, Err: %v", fsId, err)
		go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), true, &structs.AcademyMemberBookingPayload{FsId: fsId})
		return err
	}

	mapInactiveDate := make(map[string]models.SportsInactiveDay)
	for _, ele := range inactiveDays {
		mapInactiveDate[ele.Date.Format(formatYMD)] = ele
	}

	for _, v := range bookings {
		dayOfWeek := int32(v.BookingDate.Weekday())
		/*if dayOfWeek == 1 && facIdStruct.SportID == SWIMMING_SPORTS_ID { // Swimming Off on Monday
			continue
		}*/

		log.Printf("Start creating summercamp session for given data: %v", v, dayOfWeek)
		if _, ok := mapInactiveDate[v.BookingTime.Format(formatYMD)]; ok {
			v.BookingCancelled = true
			v.CancelTime = time.Now()
		}

		blockedInventoryCond := models.SportsBlockedInventory{
			FSID:        fsId,
			Date:        v.BookingDate,
			PacsID:      v.PacsId,
			DeletedFlag: 0,
		}
		isBookingBlocked := false
		var blockedData []models.SportsBlockedInventory
		if err := s.Data.GetBlockedInventoriesByDate(ctx, blockedInventoryCond, &blockedData); err != nil {
			log.Printf("func:PopulateSummerCampBookings, error in fetching blocked inventories, err:%v\n", err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), true, v)
			return err
		}
		if len(blockedData) > 0 {
			v.BookingCancelled = true
			v.CancelTime = time.Now()
			isBookingBlocked = true
		}

		masterUserId, err := getMasterUserForGivenPurchaseId(ctx, v.PurchaseId)
		if err != nil {
			log.Printf("func:PopulateSummerCampBookings | getMasterUserForGivenPurchaseId | purchaseId:%d | err: %v", v.PurchaseId, err)
			continue
		}
		v.CreatedBy = masterUserId

		bookingReferenceNumber, err := s.Data.CreateSummerCampSubscriptionBooking(ctx, v)
		if err != nil {
			log.Println("PopulateSummerCampBookings, error in creation of summercamp booking for req: %v, err: %v", v, err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), true, v)
			return err
		}
		if v.BookingCancelled && !isBookingBlocked {
			go s.sendFitsoCancelledBookingNotification(ctx, &models.AllBooking{
				BookingReferenceNumber: bookingReferenceNumber,
			})
		} else if isBookingBlocked {
			go s.sendFitsoCancelledBookingNotificationForBlockedInventory(ctx, &models.AllBooking{
				BookingReferenceNumber: bookingReferenceNumber,
			}, blockedData[0].BlockRelatedNote)
		} else {
			go s.sendSummerCampBookingNotification(ctx, bookingReferenceNumber)
		}
		s.Data.DeleteUserLinkedBookingsReferenceNumberCache(v.UserId, SummerCampCategoryID)
		s.Data.DeleteUserLinkedBookingsReferenceNumberCache(v.CreatedBy, SummerCampCategoryID)
	}

	return nil
}
