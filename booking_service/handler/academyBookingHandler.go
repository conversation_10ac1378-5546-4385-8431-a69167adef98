package bookingHandler

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"bitbucket.org/jogocoin/go_api/booking_service/structs"

	models "bitbucket.org/jogocoin/go_api/booking_service/data/model"
	util "bitbucket.org/jogocoin/go_api/booking_service/internal/util"
	bookingPB "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	pb "bitbucket.org/jogocoin/go_api/booking_service/proto/booking"
	facilitySportPB "bitbucket.org/jogocoin/go_api/booking_service/proto/facility_sport"
	productPB "bitbucket.org/jogocoin/go_api/booking_service/proto/product"
	purchasePB "bitbucket.org/jogocoin/go_api/booking_service/proto/purchase"
	userPB "bitbucket.org/jogocoin/go_api/booking_service/proto/user"
	ptypes "github.com/golang/protobuf/ptypes"
)

// If user has taken trial for given sport, then, status = true
func (s *Service) GetUserTrialsWithinCityByProductCategory(ctx context.Context, req *pb.TrialsStatusRequest, res *pb.TrialsStatusResponse) error {
	cityId := util.GetCityIDFromContext(ctx)
	if cityId == 0 {
		cityId = req.CityId
	}

	if req.ProductCategoryId <= 0 {
		errMsg := fmt.Sprintf("Invalid request to fetch trials by product category for req: %v", req)
		log.Println("GetUserTrialsWithinCityByProductCategory: ", errMsg)
		return errors.New(errMsg)
	}

	facilitySportClient := util.GetFacilitySportClient()
	userSportTrialStatusMap := make(map[int32]map[int32]bool)
	log.Printf("GetUserTrialsWithinCityByProductCategory: Error Provider: City id - %d", cityId)
	cityFeatures, err := facilitySportClient.GetCityBasedFeatureDetails(ctx, &facilitySportPB.Empty{})
	if err != nil {
		return fmt.Errorf("Unable to get city based fitso features, cityId: %d", cityId)
	}

	for _, userId := range req.UserIds {
		sportTrialStatus := make(map[int32]bool)
		if req.ProductCategoryId == SummerCampCategoryID {
			for _, sportId := range cityFeatures.SummerCampSportIds {
				sportTrialStatus[sportId] = false
			}
		} else {
			for _, sportId := range cityFeatures.AcademySportIds {
				sportTrialStatus[sportId] = false
			}
		}
		userSportTrialStatusMap[userId] = sportTrialStatus
	}

	trialBookingsReq := &models.AllBooking{
		UserIds:            req.UserIds,
		CityID:             cityId,
		ProductCategoryIds: []int32{req.ProductCategoryId},
	}
	bookings, err := s.Data.GetTrialBookingsForGivenCity(ctx, trialBookingsReq)
	if err != nil {
		return fmt.Errorf("Unable to get academy trial bookings for users in cityId: %d", cityId)
	}

	currentTime := time.Now()
	fsIds := make([]int32, 0)
	for _, val := range bookings {
		if val.BookingCancelled || (val.BookingTime.Before(currentTime) && !val.AttendanceFlag) {
			continue
		}
		fsIds = append(fsIds, val.FSID)
	}
	fsIds = DeduplicateSlice(fsIds)

	if len(fsIds) == 0 {
		convertTrialStatusIntoResponse(ctx, userSportTrialStatusMap, res)
		return nil
	}

	facilitySportReq := &facilitySportPB.FacilitySportsByFsIdRequest{
		FsIds: fsIds,
	}
	facilitySportRes, err := facilitySportClient.GetFacilitySportsDetailsByFsID(ctx, facilitySportReq)
	if err != nil || len(facilitySportRes.FacilitySports) == 0 {
		return fmt.Errorf("Unable to get fs details for academy trials of cityId:%d", cityId)
	}

	fsSportMap := make(map[int32]int32)
	for _, val := range facilitySportRes.FacilitySports {
		fsSportMap[val.FsId] = val.Sport[0].SportId
	}

	for _, val := range bookings {
		if val.BookingCancelled || (val.BookingTime.Before(currentTime) && !val.AttendanceFlag) {
			continue
		}
		sportId := fsSportMap[val.FSID]
		userSportTrialStatusMap[val.UserID][sportId] = true
	}
	convertTrialStatusIntoResponse(ctx, userSportTrialStatusMap, res)
	return nil
}

func convertTrialStatusIntoResponse(ctx context.Context, trialStatus map[int32]map[int32]bool, res *pb.TrialsStatusResponse) {
	if res.UserTrials == nil {
		res.UserTrials = make(map[int32]*pb.TrialStatus)
	}
	for userId, sportMap := range trialStatus {
		statusWrapper := &pb.TrialStatus{
			SportTrial: sportMap,
		}
		res.UserTrials[userId] = statusWrapper
	}
}

func (s *Service) GetAcademyTrialBookingAllowedStatus(ctx context.Context, req *pb.Empty, res *pb.GetTrialBookingAllowedStatusResponse) error {
	userId := util.GetUserIDFromContext(ctx)

	userClient := util.GetUserClient()
	userAgeRecordReq := &userPB.GetUserAgeRecordReq{
		UserId: userId,
	}
	userAgeRecordRes, err := userClient.GetUserAgeRecord(ctx, userAgeRecordReq)
	if err != nil {
		log.Printf("error in GetAcademyTrialBookingAllowedStatus in getting user age record, user: %d, error: %v", userId, err)
		return fmt.Errorf("error in GetAcademyTrialBookingAllowedStatus in getting user age record, user: %d, error: %v", userId, err)
	}

	academyBookingDataRequest := &bookingPB.TrialsStatusRequest{
		UserIds:           []int32{userId},
		ProductCategoryId: AcademyCategoryID,
	}
	academyTrialsStatusResponse := &pb.TrialsStatusResponse{}

	err = s.GetUserTrialsWithinCityByProductCategory(ctx, academyBookingDataRequest, academyTrialsStatusResponse)
	if err != nil {
		log.Printf("error in GetAcademyTrialBookingAllowedStatus in fetching academy booking data for user %d, err: %v", userId, err)
		return fmt.Errorf("error in GetAcademyTrialBookingAllowedStatus in fetching academy booking data for user %d, err: %v", userId, err)
	}

	var trialsAllowed, trialsTaken int
	var trialSports []int32

	if userTrials, ok := academyTrialsStatusResponse.UserTrials[userId]; ok {
		for sportID, trialTaken := range userTrials.SportTrial {
			trialsAllowed += 1
			if trialTaken {
				trialsTaken += 1
			}
			trialSports = append(trialSports, sportID)
		}
	} else {
		return fmt.Errorf("error in GetAcademyTrialBookingAllowedStatus - no data found for user with user id %d", userId)
	}

	res.TrialSports = trialSports

	if userAgeRecordRes.Age > ACADEMY_MAX_AGE {
		res.IsTrialBookingAllowed = true
	} else {
		res.IsTrialBookingAllowed = trialsTaken < trialsAllowed
	}
	return nil
}

func (s *Service) ReduceAcademySlotCapacity(ctx context.Context, req *pb.UpdateAcademySlotRequest, res *pb.Ack) error {

	if req.AcademySlotId <= 0 {
		status := &pb.Status{
			Status:  badRequest,
			Message: "invalid request",
		}
		res.Status = status
		return nil
	}
	academySlotReq := &bookingPB.AcademySlot{
		Id: req.AcademySlotId,
	}
	var academySlotResponse pb.GetAcademySlotsResponse
	err := s.GetAcademySlots(ctx, academySlotReq, &academySlotResponse)
	if err != nil {
		log.Printf("ReduceAcademySlotCapacity, error in getting academy slot data for academy slot id: %d, err: %v", req.AcademySlotId, err)
		return err
	}
	if academySlotResponse.AcademySlots != nil && len(academySlotResponse.AcademySlots) > 0 {
		pcl := util.GetProductClient()
		academySlotDetails := academySlotResponse.AcademySlots[0]
		reqData := &productPB.GetRelatedProductCourseDetailsReq{
			FsId:                   academySlotDetails.FsId,
			ProductCourseMappingId: academySlotDetails.ProductCourseMappingId,
			SlotId1:                academySlotDetails.SlotId1,
			SlotId2:                academySlotDetails.SlotId2,
		}
		productCourseDetail, err := pcl.GetRelatedProductCourseDetails(ctx, reqData)
		if err != nil {
			log.Printf("ReduceAcademySlotCapacity, error in getting related product course mappings for reqData: %v, err: %v", reqData, err)
			return fmt.Errorf("Error in getting related product course mappings data for Fs Id: %d, Slot Id1: %d, Slot Id2: %d, Product Course Mapping Id: %d", reqData.FsId, reqData.SlotId1, reqData.SlotId2, reqData.ProductCourseMappingId)
		}
		var productCourseMappingIds []int32
		for productCourseMappingId, _ := range productCourseDetail.ProductCourseMappingsData {
			productCourseMappingIds = append(productCourseMappingIds, productCourseMappingId)
		}
		if err := s.Data.BulkReduceAcademySlotCapacity(ctx, academySlotDetails.FsId, academySlotDetails.SlotId1, academySlotDetails.SlotId2, productCourseMappingIds); err != nil {
			log.Printf("Error in BulkReduceAcademySlotCapacity for fs id: %d, slot id 1: %d, slot id 2: %d, productCourseMappingIds: %v, error: %v", academySlotDetails.FsId, academySlotDetails.SlotId1, academySlotDetails.SlotId2, productCourseMappingIds, err)
			return err
		}
		status := &pb.Status{
			Status:  success,
			Message: "capacity reduced successfully",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) IncreaseAcademySlotCapacity(ctx context.Context, req *pb.UpdateAcademySlotRequest, res *pb.Ack) error {

	if req.AcademySlotId <= 0 {
		status := &pb.Status{
			Status:  badRequest,
			Message: "invalid request",
		}
		res.Status = status
		return nil
	}
	academySlotReq := &bookingPB.AcademySlot{
		Id: req.AcademySlotId,
	}
	var academySlotResponse pb.GetAcademySlotsResponse
	err := s.GetAcademySlots(ctx, academySlotReq, &academySlotResponse)
	if err != nil {
		log.Printf("IncreaseAcademySlotCapacity, error in getting academy slot data for academy slot id: %d, err: %v", req.AcademySlotId, err)
		return err
	}
	if academySlotResponse.AcademySlots != nil && len(academySlotResponse.AcademySlots) > 0 {
		pcl := util.GetProductClient()
		academySlotDetails := academySlotResponse.AcademySlots[0]
		reqData := &productPB.GetRelatedProductCourseDetailsReq{
			FsId:                   academySlotDetails.FsId,
			ProductCourseMappingId: academySlotDetails.ProductCourseMappingId,
			SlotId1:                academySlotDetails.SlotId1,
			SlotId2:                academySlotDetails.SlotId2,
		}
		productCourseDetail, err := pcl.GetRelatedProductCourseDetails(ctx, reqData)
		if err != nil {
			log.Printf("IncreaseAcademySlotCapacity, error in getting related product course mappings for reqData: %v, err: %v", reqData, err)
			return fmt.Errorf("Error in getting related product course mappings data for Fs Id: %d, Slot Id1: %d, Slot Id2: %d, Product Course Mapping Id: %d", reqData.FsId, reqData.SlotId1, reqData.SlotId2, reqData.ProductCourseMappingId)
		}
		var productCourseMappingIds []int32
		for productCourseMappingId, _ := range productCourseDetail.ProductCourseMappingsData {
			productCourseMappingIds = append(productCourseMappingIds, productCourseMappingId)
		}
		if err := s.Data.BulkIncreaseAcademySlotCapacity(ctx, academySlotDetails.FsId, academySlotDetails.SlotId1, academySlotDetails.SlotId2, productCourseMappingIds); err != nil {
			log.Printf("IncreaseAcademySlotCapacity, Error in BulkReduceAcademySlotCapacity for fs id: %d, slot id 1: %d, slot id 2: %d, productCourseMappingIds: %v, error: %v", academySlotDetails.FsId, academySlotDetails.SlotId1, academySlotDetails.SlotId2, productCourseMappingIds, err)
			return err
		}
		status := &pb.Status{
			Status:  success,
			Message: "capacity increased successfully",
		}
		res.Status = status
	}
	return nil
}

func (s *Service) GetAcademyTrialBookingsOfLastNDaysForFsIds(ctx context.Context, req *pb.GetAcademyTrialBookingsOfLastNDaysForFsIdsRequest, res *pb.BookingResponse) error {
	trialBookings, err := s.Data.GetAcademyTrialBookingsOfLastNDaysForFsIds(ctx, req.FsIdList, req.NumDays)

	if err != nil {
		log.Printf("error in GetAcademyTrialBookingsOfLastNDaysForFsIds: unable to get booking data for fs ids: %d", req.FsIdList)
		return fmt.Errorf("error in GetAcademyTrialBookingsOfLastNDaysForFsIds: unable to get booking data for fs ids: %d", req.FsIdList)
	}

	for _, trial := range trialBookings {
		bookingTime, _ := ptypes.TimestampProto(trial.BookingTime)
		trialBookingData := &pb.Booking{
			FsId:           trial.FSID,
			UserId:         trial.UserID,
			BookingId:      trial.BookingID,
			BookingTime:    bookingTime,
			AttendanceFlag: trial.AttendanceFlag,
			Slot: &pb.Slot{
				Timing: trial.Timing,
			},
		}

		res.Booking = append(res.Booking, trialBookingData)
	}

	return nil
}

func (s *Service) CreateAcademySubscriptionBookings(ctx context.Context, req *pb.CreateAcademySessionBookingsRequest, res *pb.Empty) error {
	log.Printf("CreateAcademySubscriptionBookings: called with req: %v", req)
	if len(req.TimeToProcess) == 0 {
		log.Printf("No weekdays available to populate academy session bookings")
		return nil
	}

	var slots []models.Slot
	slotCondition := &models.Slot{
		DeletedFlag: 0,
	}

	err := s.Data.SlotGet(ctx, slotCondition, &slots)
	if err != nil {
		log.Printf("Unable to get slot details, Err: %v", err)
		return fmt.Errorf("Unable to get slot details, Err: %v", err)
	}

	slotMap := make(map[int32]models.Slot)
	for _, v := range slots {
		slotMap[v.SlotID] = v
	}

	productClient := util.GetProductClient()
	fsDataMap := make(map[int32][]*structs.AcademyMemberBookingPayload)

	for _, t := range req.TimeToProcess {
		reqData := &productPB.GetDataToCreateAcademySessionBookingsRequest{
			TimeToProcess: t,
			UserId:        req.UserId,
		}
		log.Println("CreateAcademySubscriptionBookings: fetching data to get academy bookings data for reqData")
		res, err := productClient.GetDataToCreateAcademySessionBookings(ctx, reqData)
		if err != nil {
			log.Println("CreateAcademySubscriptionBookings: error in fetching bookings data to populate academy bookings: %v", err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, &structs.AcademyMemberBookingPayload{UserId: req.UserId})
			return err
		}
		log.Println("CreateAcademySubscriptionBookings: fetched academy bookings data: %v", res)

		for _, v := range res.Data {
			bookingDate := time.Unix(v.BookingDate, 0)
			slotElem := slotMap[v.SlotId]

			payload := &structs.AcademyMemberBookingPayload{
				SubscriptionId: v.SubscriptionId,
				ProductId:      v.ProductId,
				UserId:         v.UserId,
				PacsId:         v.PacsId,
				FsId:           v.FsId,
				SlotId:         v.SlotId,
				PurchaseId:     v.PurchaseId,
				BookingDate:    bookingDate,
				BookingTime:    bookingDate.Add(time.Hour*time.Duration(slotElem.StartHour) + time.Minute*time.Duration(slotElem.StartMin)),
				CultMembershipId: v.CultMembershipId,
			}
			fsDataMap[v.FsId] = append(fsDataMap[v.FsId], payload)
		}
	}

	log.Printf("CreateAcademySubscriptionBookings: before publishing fsDataMap: %v", fsDataMap)

	for fsId, bookings := range fsDataMap {
		log.Printf("CreateAcademySubscriptionBookings: publishing to academy booking topic for fsId: %d, bookings: %v", fsId, bookings)
		payload := &structs.AcademyMemberBookingQueuePayload{
			FsId:     fsId,
			Bookings: bookings,
		}
		err := s.Data.EnqueueAcademyMemberBookingCreationPayload(ctx, payload)
		if err != nil {
			log.Printf("Unable to enqueue academy session booking payload: %v, Err: %v", payload, err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, &structs.AcademyMemberBookingPayload{FsId: fsId})
			return fmt.Errorf("Unable to enqueue academy session booking payload, Err: %v", err)
		}
	}
	return nil
}

func (s *Service) PopulateAcademyBookings(ctx context.Context, req *structs.AcademyMemberBookingQueuePayload) error {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[PANIC] func: PopulateAcademyBookings, Error: %v", r)
		}
	}()

	fsId := req.FsId
	bookings := req.Bookings

	log.Printf("Calling PopulateAcademyBookings for fsId: %d with booking count %d", fsId, len(bookings))

	var facIdStruct structs.FacilitySportIds
	if err := s.Data.GetFacilityIdAndSportIdByFsId(&fsId, &facIdStruct); err != nil {
		log.Printf("Error in getting facility_id and sport id for fsId: %d, Err: %v", fsId, err)
		go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, &structs.AcademyMemberBookingPayload{FsId: fsId})
		return err
	}

	inactiveDaysCondition := structs.InactiveDaysCondition{
		FsId:       fsId,
		FacilityId: facIdStruct.FacilityID,
	}

	var inactiveDays []models.SportsInactiveDay
	if err := s.Data.GetUpcomingInactiveDays(&inactiveDays, &inactiveDaysCondition); err != nil {
		log.Printf("Error in getting upcoming inactive days for fsId: %d, Err: %v", fsId, err)
		go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, &structs.AcademyMemberBookingPayload{FsId: fsId})
		return err
	}

	mapInactiveDate := make(map[string]models.SportsInactiveDay)
	for _, ele := range inactiveDays {
		mapInactiveDate[ele.Date.Format(formatYMD)] = ele
	}

	for _, v := range bookings {
		log.Printf("Start creating academy session for given data: %v", v)
		if _, ok := mapInactiveDate[v.BookingTime.Format(formatYMD)]; ok {
			v.BookingCancelled = true
			v.CancelTime = time.Now()
		}

		blockedInventoryCond := models.SportsBlockedInventory{
			FSID:        fsId,
			Date:        v.BookingDate,
			PacsID:      v.PacsId,
			DeletedFlag: 0,
		}
		isBookingBlocked := false
		var blockedData []models.SportsBlockedInventory
		if err := s.Data.GetBlockedInventoriesByDate(ctx, blockedInventoryCond, &blockedData); err != nil {
			log.Printf("func:PopulateAcademyBookings, error in fetching blocked inventories, err:%v\n", err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, v)
			return err
		}
		if len(blockedData) > 0 {
			v.BookingCancelled = true
			v.CancelTime = time.Now()
			isBookingBlocked = true
		}

		masterUserId, err := getMasterUserForGivenPurchaseId(ctx, v.PurchaseId)
		if err != nil {
			log.Printf("func:PopulateAcademyBookings | getMasterUserForGivenPurchaseId | purchaseId:%d | err: %v", v.PurchaseId, err)
			continue
		}
		v.CreatedBy = masterUserId

		bookingReferenceNumber, err := s.Data.CreateAcademySubscriptionBooking(ctx, v)
		if err != nil {
			log.Println("PopulateAcademyBookings, error in creation of academy booking for req: %v, err: %v", v, err)
			go s.sendBookingCreationFailureAlertMail(ctx, err.Error(), false, v)
			return err
		}
		if v.BookingCancelled && !isBookingBlocked {
			go s.sendFitsoCancelledBookingNotification(ctx, &models.AllBooking{
				BookingReferenceNumber: bookingReferenceNumber,
			})
		} else if isBookingBlocked {
			go s.sendFitsoCancelledBookingNotificationForBlockedInventory(ctx, &models.AllBooking{
				BookingReferenceNumber: bookingReferenceNumber,
			}, blockedData[0].BlockRelatedNote)
		} else {
			go s.sendAcademyBookingNotification(ctx, bookingReferenceNumber)
		}
		s.Data.DeleteUserLinkedBookingsReferenceNumberCache(v.UserId, AcademyCategoryID)
		s.Data.DeleteUserLinkedBookingsReferenceNumberCache(v.CreatedBy, AcademyCategoryID)
	}

	return nil
}

func (s *Service) AcademyBookingCreationCult(ctx context.Context, req *pb.CreateAcademySessionBookingsRequest, res *pb.Ack) error {
	isBookingBlocked := req.IsBookingBlocked
	log.Println("AcademyBookingCreationCult ", req)
	if req.BookingCancelled && !isBookingBlocked {
		go s.sendFitsoCancelledBookingNotification(ctx, &models.AllBooking{
			BookingReferenceNumber: req.BookingReferenceNumber,
		})
	} else if isBookingBlocked {
		go s.sendFitsoCancelledBookingNotificationForBlockedInventory(ctx, &models.AllBooking{
			BookingReferenceNumber: req.BookingReferenceNumber,
		}, req.BlockRelatedNote)
	} else {
		go s.sendAcademyBookingNotification(ctx, req.BookingReferenceNumber)
	}
	s.Data.DeleteUserLinkedBookingsReferenceNumberCache(req.UserId, AcademyCategoryID)
	s.Data.DeleteUserLinkedBookingsReferenceNumberCache(req.CreatedBy, AcademyCategoryID)
	return nil
}

func (s *Service) sendBookingCreationFailureAlertMail(ctx context.Context, failureReason string, isSummerCamp bool, req *structs.AcademyMemberBookingPayload) error {
	reqData := &pb.BookingCreationFailureAlertReq{
		FailureReason: failureReason,
		IsSummerCamp:  false,
	}
	if req != nil && (req.UserId > 0 || req.FsId > 0) {
		bookingProto := &pb.Booking{
			UserId:         req.UserId,
			SubscriptionId: req.SubscriptionId,
			ProductId:      req.ProductId,
			FsId:           req.FsId,
			SlotId:         req.SlotId,
		}
		if !req.BookingTime.IsZero() {
			bookingTimeProto, _ := ptypes.TimestampProto(req.BookingTime)
			bookingProto.BookingTime = bookingTimeProto
		}
		reqData.BookingReq = bookingProto
	}
	if err := s.SendBookingCreationFailureAlertMail(ctx, reqData, &pb.Ack{}); err != nil {
		log.Println("sendBookingCreationFailureAlertMail: error in sending booking creation failure: ", err)
		return err
	}
	return nil
}

func (s *Service) getBookingFailureAlertMailContent(req *pb.BookingCreationFailureAlertReq) (string, string) {
	subjectStr := "Academy booking creation failure"
	if req.IsSummerCamp {
		subjectStr = "Summer Camp booking creation failure"
	}
	bookingReq := req.BookingReq
	if bookingReq != nil && bookingReq.UserId > 0 {
		subjectStr += fmt.Sprintf(" | UserId: %d", bookingReq.UserId)
	}
	messageStr := "Booking creation has failed - <br><br>"
	if bookingReq != nil && bookingReq.UserId > 0 {
		messageStr += "UserID - " + strconv.Itoa(int(bookingReq.UserId)) + "<br>"
		messageStr += "SubscriptionID - " + strconv.Itoa(int(bookingReq.SubscriptionId)) + "<br>"
		messageStr += "ProductID - " + strconv.Itoa(int(bookingReq.ProductId)) + "<br>"
		messageStr += "SlotID - " + strconv.Itoa(int(bookingReq.SlotId)) + "<br>"
	}
	if bookingReq != nil && bookingReq.BookingTime != nil && bookingReq.BookingTime.Seconds > 0 {
		bookingTimeLocal := GetLocalDateTimeFromProtoTime(bookingReq.BookingTime)
		messageStr += "Booking Time - " + bookingTimeLocal.Format(formatYMDHIS) + "<br>"
	}
	if bookingReq != nil && bookingReq.FsId > 0 {
		messageStr += "FSID - " + strconv.Itoa(int(bookingReq.FsId)) + "<br>"
	}
	messageStr += "Failure Reason (if any) - " + req.FailureReason + "<br>"
	return subjectStr, messageStr
}

func (s *Service) SendBookingCreationFailureAlertMail(ctx context.Context, req *pb.BookingCreationFailureAlertReq, res *pb.Ack) error {
	fromEm := &structs.Email{
		Name:         "Fitso",
		EmailAddress: "<EMAIL>",
	}

	var toEm []*structs.Email

	toEmVal := &structs.Email{
		Name:         "Tech - Fitso",
		EmailAddress: TECH_MAIL,
	}
	toEm = append(toEm, toEmVal)

	subjectStr, messageStr := s.getBookingFailureAlertMailContent(req)

	emailReq := &structs.EmailRequest{
		From:    fromEm,
		To:      toEm,
		ReplyTo: fromEm,
		Subject: subjectStr,
		Message: messageStr,
	}

	if err := s.Data.SendEmail(emailReq); err != nil {
		log.Println("SendBookingCreationFailureAlertMail: Error in sending booking creation failure alert email: %v", err)
		return err
	}

	return nil
}

func getMasterUserForGivenPurchaseId(ctx context.Context, purchaseId int32) (int32, error) {
	purchaseClient := util.GetPurchaseClient()
	purchaseReq := &purchasePB.PurchaseRequest{
		PurchaseId: purchaseId,
	}
	purchaseRes, err := purchaseClient.PurchaseGet(ctx, purchaseReq)
	if err != nil {
		return 0, err
	}
	if len(purchaseRes.Purchases) == 0 {
		return 0, fmt.Errorf("Unable to find purchase details for id: %d", purchaseId)
	}
	purchase := purchaseRes.Purchases[0]
	if purchase.LinkedPurchaseId == 0 { // master user
		if purchase.PpId >= 0 {
            parentUserId,_ := getMasterUserForGivenPurchaseId(ctx, purchase.PpId)
            return parentUserId, nil
        }
		return purchase.UserId, nil
	}

	purchaseReqV2 := &purchasePB.PurchaseRequest{
		PurchaseId: purchase.LinkedPurchaseId,
	}
	purchaseResV2, err := purchaseClient.PurchaseGet(ctx, purchaseReqV2)
	if err != nil {
		return 0, err
	}
	if len(purchaseResV2.Purchases) == 0 {
		return 0, fmt.Errorf("Unable to find purchase details for id: %d", purchase.LinkedPurchaseId)
	}
	return purchaseResV2.Purchases[0].UserId, nil
}

func (s *Service) CorrectAcademySlotCapacity(ctx context.Context, req *pb.AcademySlot, res *pb.Ack) error {
	if req.FsId <= 0 {
		errMsg := fmt.Sprintf("Invalid request CorrectAcademySlotCapacity req: %v", req)
		log.Println("CorrectAcademySlotCapacity: ", errMsg)
		return errors.New(errMsg)
	}

	req.IsActive = true
	pcl := util.GetProductClient()
	var academySlotResponse pb.GetAcademySlotsResponse
	err := s.GetAcademySlots(ctx, req, &academySlotResponse)
	if err != nil {
		log.Printf("CorrectAcademySlotCapacity, error in getting academy slot data for fsId: %d, err: %v", req.FsId, err)
		return err
	}
	if academySlotResponse.AcademySlots == nil || len(academySlotResponse.AcademySlots) == 0 {
		errMsg := fmt.Sprintf("CorrectAcademySlotCapacity: Invalid request no slots for req: %v", req)
		log.Println("CorrectAcademySlotCapacity: ", errMsg)
		return errors.New(errMsg)
	}
	productCourseMappingSlotMap := make(map[string]bool)
	for _, academySlotDetails := range academySlotResponse.AcademySlots {

		key := fmt.Sprintf("%d_%d", academySlotDetails.ProductCourseMappingId, academySlotDetails.SlotId1)
		if productCourseMappingSlotMap[key] {
			log.Println("already handled - ", key, academySlotDetails.Id)
			continue
		}
		var academyRelatedSlots pb.GetAcademySlotsResponse
		reqData := &pb.GetRelatedAcademySlotsReq{
			FsId:                   academySlotDetails.FsId,
			ProductCourseMappingId: academySlotDetails.ProductCourseMappingId,
			SlotId1:                academySlotDetails.SlotId1,
			SlotId2:                academySlotDetails.SlotId2,
		}
		if err := s.getRelatedAcademySlots(ctx, reqData, &academyRelatedSlots); err != nil {
			log.Printf("CorrectAcademySlotCapacity, error in getting related academy slots for req: %v, err: %v", reqData, err)
			return err
		}
		productCourseMappingSlotMap[key] = true

		if academyRelatedSlots.AcademySlots == nil || len(academyRelatedSlots.AcademySlots) == 0 {
			errMsg := fmt.Sprintf("CorrectAcademySlotCapacity: no related slots req: %v", reqData)
			log.Println("CorrectAcademySlotCapacity: no related slot", errMsg)
			return errors.New(errMsg)
		}
		var academySlotIds []int32
		var remainingCapcity, soldSlots int32
		for i, academySlot := range academyRelatedSlots.AcademySlots {
			productCourseMappingSlotMap[fmt.Sprintf("%d_%d", academySlot.ProductCourseMappingId, academySlot.SlotId1)] = true
			academySlotIds = append(academySlotIds, academySlot.Id)

			if i == 0 {
				remainingCapcity = academySlot.RemainingCapacity
			} else if remainingCapcity != academySlot.RemainingCapacity {
				//err cap not equal for related slots
				errMsg := fmt.Sprintf("CorrectAcademySlotCapacity: capacity mismatch slot %d, %d", academySlot.Id, remainingCapcity)
				log.Println("CorrectAcademySlotCapacity: ", errMsg)
				return errors.New(errMsg)
			}
		}
		reqSubData := &productPB.ActiveAcademySubscriptionsRequest{
			AcademySlotIds: academySlotIds,
		}
		activeSubscriptions, err := pcl.GetAcademyActiveSubscriptionsBySlotIds(ctx, reqSubData)
		if err != nil {
			log.Printf("CorrectAcademySlotCapacity, error in getting active sub for reqData: %v, err: %v", reqSubData, err)
			return fmt.Errorf("CorrectAcademySlotCapacity Error in getting active sub for reqData %v, %v", reqSubData, err)
		}
		log.Println("test====", academySlotIds, remainingCapcity)
		if activeSubscriptions == nil || len(activeSubscriptions.Subscriptions) == 0 {
			//error no active sub
			log.Println("No sub for slot ID", academySlotDetails.Id)
		}

		userSubMap := make(map[int32]bool) //start date
		for _, subData := range activeSubscriptions.Subscriptions {
			if userSubMap[subData.UserId] {
				log.Println("User have more than 2 academy active membership maybe renew", subData.UserId, key, academySlotDetails.Id)
				continue
			}
			userSubMap[subData.UserId] = true
			soldSlots += 1
		}
		for _, academySlot := range academyRelatedSlots.AcademySlots {
			if academySlot.MaxCapacity-soldSlots == academySlot.RemainingCapacity {
				log.Println("capacity correct for id ", academySlot.Id)
				continue
			}
			remainingCap := academySlot.MaxCapacity - soldSlots
			if remainingCap < 0 {
				//email
				log.Println("Slot sold in exccess capacity ", soldSlots, academySlot)
				remainingCap = 0
			}
			slotData := &models.AcademySlot{
				SlotId1:                academySlot.SlotId1,
				SlotId2:                academySlot.SlotId2,
				IsActive:               academySlot.IsActive,
				FsId:                   academySlot.FsId,
				ProductCourseMappingId: academySlot.ProductCourseMappingId,
				RemainingCapacity:      remainingCap,
				MaxCapacity:            academySlot.MaxCapacity,
			}
			log.Printf("CorrectAcademySlotCapacity: Academyslot FsID %d,old %d, new %d", academySlot.FsId, academySlot.RemainingCapacity, remainingCap)
			err := s.Data.AddEditAcademySlot(ctx, slotData)
			if err != nil {
				log.Printf("Error in CorrectAcademySlotCapacity Academy slot for fsId %d, pcmId %d, slotId1 %d, err %v", slotData.FsId, slotData.ProductCourseMappingId, slotData.SlotId1, err)
				return fmt.Errorf("Error in CorrectAcademySlotCapacity Academy slot for fsId %d, pcmId %d, slotId1 %d, err %v", slotData.FsId, slotData.ProductCourseMappingId, slotData.SlotId1, err)
			}
		}
	}
	return nil
}
