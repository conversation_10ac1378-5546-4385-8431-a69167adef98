package queue

import (
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/clients"
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/message"
	"bitbucket.org/jogocoin/go_api/pkg/pubsub/messageHandler"
	"context"
	"errors"
	"log"
)

type IQueue interface {
	Backend() BackendType
	BrokerUrls() string
	Name() string         //topic
	InstanceType() string //port for redis or offline online for kafka
	GroupId() string      //consumer group
	SetGroupId(string)    //consumer group
	OffsetStrategy() OffsetStrategy
	MessageType() message.MessageType
	Publish(ctx context.Context, message *message.PubSubMessage) error
	Subscribe(ctx context.Context, handler messageHandler.IPubSubMessageHandler) error
}

type Queue struct {
	backend        BackendType
	messageType    message.MessageType
	name           string
	instance       string
	groupId        string
	brokers        string
	offsetStrategy OffsetStrategy
}

func NewQueue(backend BackendType, messageType message.MessageType, name string) *Queue {
	return &Queue{backend: backend, messageType: messageType, name: name}
}

func (q *Queue) MessageType() message.MessageType {
	return q.messageType
}

func (q *Queue) OffsetStrategy() OffsetStrategy {
	return q.offsetStrategy
}

func (q *Queue) SetOffsetStrategy(offset OffsetStrategy) {
	q.offsetStrategy = offset
}

func (q *Queue) GroupId() string {
	return q.groupId
}

func (q *Queue) SetGroupId(groupId string) {
	q.groupId = groupId
}

func (q *Queue) InstanceType() string {
	return q.instance
}

func (q *Queue) SetInstance(instance string) {
	q.instance = instance
}

func (q *Queue) Name() string {
	return q.name
}

func (q *Queue) SetName(name string) {
	q.name = name
}

func (q *Queue) Backend() BackendType {
	return q.backend
}

func (q *Queue) SetBackend(backend BackendType) {
	q.backend = backend
}

func (q *Queue) BrokerUrls() string {
	return q.brokers
}

func (q *Queue) SetBrokerUrls(brokers string) {
	q.brokers = brokers
}

func (q *Queue) Publish(ctx context.Context, message *message.PubSubMessage) (err error) {
	client, err := GetQueueClient(q)
	if err != nil {
		log.Println("error in getting queue client", err)
		return
	}
	return client.Publish(ctx, message)
}

func (q *Queue) Subscribe(ctx context.Context, handler messageHandler.IPubSubMessageHandler) (err error) {
	if q.groupId == "" {
		log.Println("Group Id not defined")
		err = errors.New("Group Id not defined")
		return
	}
	client, err := GetQueueClient(q)
	if err != nil {
		log.Println("error in getting queue client", err)
		return
	}
	return client.Subscribe(ctx, handler)
}

func GetQueueClient(q IQueue) (client clients.IQueueClient, customError error) {
	switch q.Backend() {
	case KAFKA:
		client = clients.NewKafkaQueueClient(q.Name(), q.MessageType(), q.InstanceType(), q.OffsetStrategy().ToString(), q.GroupId(), q.BrokerUrls())
	default:
		customError = errors.New("queue backend not defined")
	}
	return
}
