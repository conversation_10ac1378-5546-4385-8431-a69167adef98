setup:
	go mod init "bitbucket.org/jogocoin/go_api/seo_service"

build:
	protoc -I. \
	  -I$(GOPATH)/src/github.com/grpc-ecosystem/grpc-gateway/third_party/googleapis \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/seo_service \
		proto/seo/seo.proto
	protoc --proto_path=$(GOPATH)/src/go_api/notification_service/ \
	  --micro_out=. \
	  --go_out=plugins=grpc:$(GOPATH)/src/go_api/seo_service \
		proto/notification/notification.proto
	echo "machine bitbucket.org login $(BITBUCKET_USER_NAME) password $(BITBUCKET_USER_PASS)" > $(GOPATH)/src/go_api/seo_service/.netrc

local:
	make build
	MICRO_MODE=dev MICRO_SERVER_ADDRESS=:50158 MICRO_REGISTRY=mdns GOPRIVATE="bitbucket.org" go run main.go