package main

import (
	"fmt"
	"log"
	"time"

	"bitbucket.org/jogocoin/go_api/pkg/interceptor"

	"bitbucket.org/jogocoin/go_api/pkg/newrelic"
	data "bitbucket.org/jogocoin/go_api/seo_service/data"
	seoHandler "bitbucket.org/jogocoin/go_api/seo_service/handler"
	"github.com/micro/go-micro/config"

	pb "bitbucket.org/jogocoin/go_api/seo_service/proto/seo"
	"bitbucket.org/jogocoin/go_api/seo_service/setupFunc"

	"github.com/micro/go-micro"
	"github.com/newrelic/go-agent/_integrations/nrmicro"
)

func main() {
	setupFunc.SetUp()
	kBroker := setupFunc.KafkaConnect()

	db, err := data.MysqlConnect()
	defer db.Close()
	if err != nil {
		log.Fatalf("Could not connect to DB: %v", err)
	}

	redisClient, errR := data.RedisConnect()
	defer redisClient.Close()

	if errR != nil {
		log.Fatalf("Could not connect to redis: %v", errR)
	}

	var (
		appName     string
		newrelicKey string
	)

	config.Get("env").Scan(&appName)
	config.Get("codeIndependent", "newrelicKey").Scan(&newrelicKey)

	app := newrelic.Initialize(
		newrelic.WithAppName(appName),
		newrelic.WithLicense(newrelicKey),
		newrelic.EnableSkipStatusCodes(),
	)

	seoData := &data.SeoData{
		Db:     db,
		Client: redisClient,
		Broker: kBroker,
	}

	newService := micro.NewService(
		// This name must match the package name given in your protobuf definition
		micro.Name("product.service.seo"),
		micro.Version("latest"),
		micro.RegisterTTL(time.Second*30),
		micro.RegisterInterval(time.Second*15),
		micro.WrapHandler(seoHandler.LogWrapper,
			nrmicro.HandlerWrapper(app),
			seoHandler.SetContextWrapper,
			interceptor.PanicRecoveryInterceptor,
			interceptor.ErrorInterceptor))

	newService.Init()

	// register a new subscriber function to the service
	serviceHandler := seoHandler.Service{seoData}
	pb.RegisterSeoServiceHandler(newService.Server(), &serviceHandler)

	// Run the server
	fmt.Println("------initiating server--------")
	if err := newService.Run(); err != nil {
		log.Fatal(err)
	}
}
