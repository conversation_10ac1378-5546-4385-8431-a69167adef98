#!/bin/bash

echo "🔧 Setting up GORM Zero Value Demonstration..."

# Initialize Go module
go mod init gorm-demo

# Add required dependencies
go get github.com/jinzhu/gorm
go get github.com/jinzhu/gorm/dialects/sqlite

echo "🚀 Running GORM Zero Value Demo..."
echo "This will show you exactly what happens when PurchaseId = 0"
echo ""

# Run the demo
go run gorm_zero_value_demo.go

echo ""
echo "🎯 Key Takeaways:"
echo "1. When PurchaseId = 0, GORM ignores it and queries ALL purchases"
echo "2. This causes SELECT * FROM purchases with no WHERE clause"
echo "3. In your production database, this loads ALL purchases into memory"
echo "4. This is what's causing your OOM errors in academy booking"
echo "5. The solution is explicit WHERE conditions that block zero-value queries"
